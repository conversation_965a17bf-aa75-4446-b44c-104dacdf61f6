({
    // 获取order数据
    getPODetailInfo : function(component) {
        console.log('获取order数据-----------1');
        let self = this;
        let isDropAddressInFirst = component.get('v.isDropAddressInFirst');
        var action = component.get('c.QueryPirchaseAndItemInfo');
        action.setParams({
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            IsProtal: false,
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS') {
                // 判断draft状态是否有保存当前步骤
                if ((component.get('v.actionType') === 'draft' && Number(result.MaxStep) >= 2) || component.get('v.actionType') === 'edit') {
                    if(result.BillToAddressId) {
                        component.set('v.billToAddress', {
                            'Name': result.BillToAddress,
                            'Id': result.BillToAddressId,
                            'address': result.BillToAddressInfo
                        });
                    }
                    
                    if(result.ShipToAddressId) {
                        component.set('v.shipToAddress', {
                            'Name': result.ShipToAddress,
                            'Id': result.ShipToAddressId,
                            'address': result.ShipToAddressInfo
                        });
                    }
                    
                    if (result.IsDropship) {
                        component.set('v.dropshipType', result.DropshipType);
                        if (result.DropshipType === 'End Consumer') {
                            component.set('v.DropShipName', result.DropShipName);
                            component.set('v.DropShipAddress1', result.DropShipAddress1);
                            component.set('v.DropShipAddress2', result.DropShipAddress2);
                            component.set('v.DropShipPhone', result.DropShipPhone);
                            component.set('v.DropShipCountry', {Id: result.DropShipCountry, Name: result.DropShipCountry});
                            component.set('v.DropShipCity', result.DropShipCity);
                            component.set('v.DropShipZip', result.DropShipZip);
                            component.set('v.DropShipState', result.DropShipState);
                            component.set('v.showShip', true);
                        } else if (result.DropshipType && result.DropshipType != 'End Consumer') {
                            if(result.DropshipAddressId) {
                                component.set('v.dropshipAddress', {
                                    'Name': result.DropshipAddress,
                                    'Id': result.DropshipAddressId,
                                    'address': result.DropshipAddressInfo
                                });
                            }
                            
                            component.set('v.DropShipName', null);
                            component.set('v.DropShipAddress1', null);
                            component.set('v.DropShipAddress2', null);
                            component.set('v.DropShipPhone', null);
                            component.set('v.DropShipCountry', null);
                            component.set('v.DropShipCity', null);
                            component.set('v.DropShipZip', null);
                            component.set('v.DropShipState', null);
                            component.set('v.showShip', false);
                            component.set('v.shipToAddress', {Id: null, Name: null});
                        } else if (!result.DropshipType) {
                            component.set('v.showShip', true);
                        }
                    } else {
                        component.set('v.dropshipType', null);
                        component.set('v.dropshipAddress', null);
                        component.set('v.DropShipName', null);
                        component.set('v.DropShipAddress1', null);
                        component.set('v.DropShipAddress2', null);
                        component.set('v.DropShipPhone', null);
                        component.set('v.DropShipCountry', null);
                        component.set('v.DropShipCity', null);
                        component.set('v.DropShipZip', null);
                        component.set('v.DropShipState', null);
                    }
                    component.set('v.freightCost', result.FreightCost || 0);
                    component.set('v.insuranceFee', result.InsuranceFee || 0);
                    component.set('v.otherFee', result.OtherFee || 0);
                    component.set('v.comments', result.Comments);
                    component.set('v.instructionToDSV', result.instructionToDsv);
                }
                // 获取自动填入address list
                self.getBillAddressList(component);
                self.getShipAddressList(component);
                self.getDropAddressList(component);
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 下一步
    handleNextStep: function (component, isNext) {
        var action = component.get("c.UpdatePoInfo");
        let billToAddress = component.get("v.billToAddress");
        let shipToAddress = component.get("v.shipToAddress");
        let dropshipAddress = component.get("v.dropshipAddress");
        let DropShipCountry = component.get("v.DropShipCountry");
        let paramsObj = {
            CustomerId: component.get('v.customerId'),
            BillAddressId: billToAddress ? billToAddress.Id : '',
            ShipAddressId: shipToAddress ? shipToAddress.Id : '',
            DropShipType: component.get('v.dropshipType'),
            DropShipAddressId: dropshipAddress ? dropshipAddress.Id : '',
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            IsProtal: false,
            DropShipName: component.get('v.DropShipName'),
            DropShipAddress1: component.get('v.DropShipAddress1'),
            DropShipAddress2: component.get('v.DropShipAddress2'),
            DropShipPhone: component.get('v.DropShipPhone'),
            DropShipCountry: DropShipCountry ? DropShipCountry.Id : '',
            DropShipCity: component.get('v.DropShipCity'),
            DropShipZip: component.get('v.DropShipZip'),
            DropShipState: component.get('v.DropShipState'),
            FreightCost: component.get('v.freightCost'),
            InsuranceFee: component.get('v.insuranceFee'),
            OtherFee: component.get('v.otherFee'),
            isFirst: false,
            Comments: component.get('v.comments'),
            InstructionToDSV: component.get('v.instructionToDSV')
        }
        action.setParams({
            StringRequestBody: JSON.stringify(paramsObj),
            'pricingDate': component.get('v.pricingDate')
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                let result = JSON.parse(response.getReturnValue());
                if(!result.isSuccess) {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Error",
                        "message": result.errorMsg,
                        "type": "error",
                        "duration": 60000
                    }).fire();
                } 
                else {
                    if(isNext) {
                        let userType = component.get('v.userType');
                        let actionType = component.get('v.actionType');
                        let recordId = component.get('v.purchaseOrderId');
                        if (userType === 'InsideSales' && actionType === 'edit') {
                            let url = window.location.origin + '/lightning/n/PO_Detail?0.recordId=' + recordId;
                            window.open(url, '_self');
                        } else {
                            component.set("v.currentStep", component.get("v.currentStep") + 1);
                        }
                    }
                    else {
                        var currentStep = component.get("v.currentStep");
                        var actionType = component.get("v.actionType");
                        if (actionType !== 'edit') {
                            component.set('v.actionType', 'draft');
                        }
                        component.set("v.currentStep", currentStep - 1);
                    }
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action); 
    },
    // 获取picklistget
    getDropshipList: function (component) {
        // TODO: 需要后端返回picklist
        let list = [
            {
                label: 'Affiliate', 
                value: 'Affiliate',
            },
            {
                label: 'Warehouse', 
                value: 'Warehouse',
            },
            {
                label: 'End Consumer', 
                value: 'End Consumer',
            },
        ];
        component.set('v.dropshipTypeOptions', list);
    },

    // 必填校验
    getValidation : function (component, type) {
        let self = this;
        if (type == 'detail') {
            let valid1 = self.getElementRequiredError(component, 'dropShipName');
            let valid2 = self.getElementRequiredError(component, 'dropshipStreetAndStreetNo');
            let valid3 = self.getElementRequiredError(component, 'dropShipZip');
            let valid4 = self.getElementRequiredError(component, 'dropShipCity');
            return (valid1 && valid2 && valid3 && valid4);
        } else {
            let valid1 = self.getElementRequiredError(component, 'dropshipType');
            return valid1;
        }
    },

    // 校验错误提示信息
    getElementRequiredError : function (component, ele) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = element.get('v.value');
        var valid = !!val;
        console.log(valid, 'dropshipType valid-------------');
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }

        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
    // 获取自动填入address
    getBillAddressList: function (component) {
        var action = component.get("c.QueryAddressByName");
        action.setParams({
            AddressName: '',
            customerId: component.get('v.customerId'),
            recordTypeName: 'Billing Address'
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let billList = JSON.parse(result);
                if (billList && billList.length) {
                    let primaryList = [];
                    billList.forEach((item)=>{
                        if (item.address.Primary) {
                            primaryList.push(item)
                        }
                    })
                    if (billList.length == 1) {
                         // 1.当仅有一条数据时，直接自动带入，不用考虑primary
                        component.set('v.defaultBillAddressInfo', {
                            Id: billList[0].Id,
                            Name: billList[0].Name,
                            address: billList[0].address,
                        })
                    } else {
                        // 2.当有多条数据，但仅有一条数据primary为true时，自动带入
                        if (primaryList.length == 1) {
                            component.set('v.defaultBillAddressInfo', {
                                Id: primaryList[0].Id,
                                Name: primaryList[0].Name,
                                address: primaryList[0].address,
                            })
                        } else {
                            component.set('v.defaultBillAddressInfo', {
                                Id: '',
                                Name: ''
                            });

                            var toastEvt = $A.get("e.force:showToast");
                            toastEvt.setParams({
                                "title": "Info",
                                "message": 'Multiple Primary Billing Address Exist, Please Select Manually.',
                                "type": "success"
                            }).fire();
                        }
                    }
                } else {
                    component.set('v.defaultBillAddressInfo', {
                        Id: '',
                        Name: ''
                    })
                }
                // 判断是否立即带入
                let billToAddress = component.get('v.billToAddress');
                if (!billToAddress.Id) {
                    component.set('v.billToAddress', component.get('v.defaultBillAddressInfo'));
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getShipAddressList: function (component) {
        var action = component.get("c.QueryAddressByName");
        action.setParams({
            AddressName: '',
            customerId: component.get('v.customerId'),
            recordTypeName: 'Shipping Address'
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let shipList = JSON.parse(result);
                console.log(JSON.stringify(shipList), 'shipList===============');
                if (shipList && shipList.length) {
                    let primaryList = [];
                    shipList.forEach((item)=>{
                        if (item.address.Primary) {
                            primaryList.push(item)
                        }
                    })
                    if (shipList.length == 1) {
                         // 1.当仅有一条数据时，直接自动带入，不用考虑primary
                        component.set('v.defaultShipAddressInfo', {
                            Id: shipList[0].Id,
                            Name: shipList[0].Name,
                            address: shipList[0].address,
                        })
                    } else {
                        // 2.当有多条数据，但仅有一条数据primary为true时，自动带入
                        if (primaryList.length == 1) {
                            component.set('v.defaultShipAddressInfo', {
                                Id: primaryList[0].Id,
                                Name: primaryList[0].Name,
                                address: primaryList[0].address,
                            })
                        } else {
                            component.set('v.defaultShipAddressInfo', {
                                Id: '',
                                Name: ''
                            });

                            var toastEvt = $A.get("e.force:showToast");
                            toastEvt.setParams({
                                "title": "Info",
                                "message": 'Multiple Primary Shipping Address Exist, Please Select Manually.',
                                "type": "success"
                            }).fire();
                        }
                    }
                } else {
                    component.set('v.defaultShipAddressInfo', {
                        Id: '',
                        Name: ''
                    })
                }
                // 判断是否立即带入
                let shipToAddress = component.get('v.shipToAddress');
                if (!shipToAddress.Id && (component.get('v.isDropShip') === 'N' || (component.get('v.isDropShip') === 'Y' && (component.get('v.dropshipType') == 'End Consumer' || !component.get('v.dropshipType'))))) {
                    component.set('v.shipToAddress', component.get('v.defaultShipAddressInfo'));
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getDropAddressList: function (component) {
        var action = component.get("c.QueryAddressByName");
        action.setParams({
            AddressName: '',
            customerId: component.get('v.customerId'),
            recordTypeName: 'Dropship Shipping Address'
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let dropList = JSON.parse(result);
                console.log(JSON.stringify(dropList), 'dropList===============');
                if (dropList && dropList.length) {
                    let primaryList = [];
                    dropList.forEach((item)=>{
                        if (item.address.Primary) {
                            primaryList.push(item)
                        }
                    })
                    if (dropList.length == 1) {
                         // 1.当仅有一条数据时，直接自动带入，不用考虑primary
                        component.set('v.defaultDropAddressInfo', {
                            Id: dropList[0].Id,
                            Name: dropList[0].Name,
                            address: dropList[0].address,
                        })
                    } else {
                        // 2.当有多条数据，但仅有一条数据primary为true时，自动带入
                        if (primaryList.length == 1) {
                            component.set('v.defaultDropAddressInfo', {
                                Id: primaryList[0].Id,
                                Name: primaryList[0].Name,
                                address: primaryList[0].address,
                            })
                        } else {
                            component.set('v.defaultDropAddressInfo', {
                                Id: '',
                                Name: ''
                            });

                            var toastEvt = $A.get("e.force:showToast");
                            toastEvt.setParams({
                                "title": "Info",
                                "message": 'Multiple Primary Dropship Address Exist, Please Select Manually.',
                                "type": "success"
                            }).fire();
                        }
                    }
                } else {
                    component.set('v.defaultDropAddressInfo', {
                        Id: '',
                        Name: ''
                    })
                }
                // 判断是否立即带入
                let dropshipAddress = component.get('v.dropshipAddress');
                if (!dropshipAddress.Id && component.get('v.isDropShip') === 'Y' && !component.get('v.isDropAddressInFirst')) {
                    component.set('v.dropshipAddress', component.get('v.defaultDropAddressInfo'));
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },

    getInstructionToDSVOptions: function(component) {
        let locale = $A.get("$Locale.language");
        let options = [];
        if(locale === 'de') {
            options = [
                {
                    label: 'Versand per Paketdienst',
                    value: 'Versand per Paketdienst'
                }
            ];
        }
        else {
            options = [
                {
                    label: 'Dispatch by parcel',
                    value: 'Dispatch by parcel'
                }
            ];
        }
        component.set('v.instructionToDSVOptions', options);
    }
})