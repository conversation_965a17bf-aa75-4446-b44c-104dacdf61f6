({
    init:function(component, event, helper){
        const params = new URLSearchParams(window.location.search);
        const userType =  params.get('userType');
        const recordId =  params.get('recordId');
        const actionType =  params.get('actionType');
        component.set('v.userType', userType);
        component.set('v.recordId', recordId);
        component.set('v.actionType', actionType);
        if (userType == 'Commercial' && actionType == 'view') {
            component.set('v.showType', 'commercialView');
            const commercialViewColumns= [
                {label: $A.get('$Label.c.CCM_PurchaseDate'), fieldName: 'purchaseDate'},
                {label: $A.get('$Label.c.CCM_Brand'), fieldName:'brand'},
                {label: $A.get('$Label.c.CCM_PurchasePlace'), fieldName:'purchasePlace'},
                {label: $A.get('$Label.c.CCM_KitModel'), fieldName: 'masterProduct'},
                {label: $A.get('$Label.c.CCM_ModelNumber'), fieldName: 'modelNumber'},
                {label: $A.get('$Label.c.CCM_SerialNumber'), fieldName: 'serialNumber'},
                {
                    label: $A.get('$Label.c.CCM_Receipt'),
                    tdStyle: 'text-align: center',
                    children:[
                        {
                            type: "lightning:button",
                            attributes:{
                                label: "${receiptName}",
                                value: "${receiptUrl}",
                                variant:"bare",
                                class: "company-btn",
                                onclick: component.getReference("c.toReceiptUrl")
                            }
                        }
                    ]
                },
                {label: $A.get('$Label.c.CCM_WarrantyStatus'), fieldName: 'warrantyStatus'},
                {label: $A.get('$Label.c.CCM_ExpirationDate'), fieldName: 'expirationDate'},
            ];
            component.set('v.commercialViewColumns',commercialViewColumns);
            // 获取commercialView数据
            helper.getCommercialViewData(component, recordId);
        } else if (userType == 'Commercial' && actionType == 'edit') {
            component.set('v.showType', 'commercialEdit');
            // 获取commercialEdit数据
            helper.getCommercialEditData(component, recordId);
        } else if (userType == 'Residential' && actionType == 'view') {
            component.set('v.showType', 'residentialView');
            const residentialViewColumns= [
                {label: $A.get('$Label.c.CCM_PurchaseDate'), fieldName: 'purchaseDate'},
                {label: $A.get('$Label.c.CCM_Brand'), fieldName: 'brand'},
                {label: $A.get('$Label.c.CCM_PurchasePlace'), fieldName:'purchasePlace'},
                {label: $A.get('$Label.c.CCM_KitModel'), fieldName: 'masterProduct'},
                {label: $A.get('$Label.c.CCM_ModelNumber'), fieldName: 'modelNumber'},
                {label: $A.get('$Label.c.CCM_SerialNumber'), fieldName: 'serialNumber'},
                {
                    label: $A.get('$Label.c.CCM_Receipt'),
                    tdStyle: 'text-align: center',
                    children:[
                        {
                            type: "lightning:button",
                            attributes:{
                                label: "${receiptName}",
                                value: "${receiptUrl}",
                                variant:"bare",
                                class: "company-btn",
                                onclick: component.getReference("c.toReceiptUrl")
                            }
                        }
                    ]
                },
                {label: $A.get('$Label.c.CCM_WarrantyStatus'), fieldName: 'warrantyStatus'},
                {label: $A.get('$Label.c.CCM_ExpirationDate'), fieldName: 'expirationDate'},
            ];
            component.set('v.residentialViewColumns', residentialViewColumns);
            // 获取ResidentialView数据
            helper.getResidentialViewData(component, recordId);
        } else if (userType == 'Residential' && actionType == 'edit') {
            component.set('v.showType', 'residentialEdit');
            const residentialEditColumns= [
                {label: $A.get('$Label.c.CCM_PurchaseDate'), fieldName: 'purchaseDate'},
                {label: $A.get('$Label.c.CCM_Brand'), fieldName:'brand'},
                {label: $A.get('$Label.c.CCM_PurchasePlace'), fieldName:'purchasePlace'},
                {label: $A.get('$Label.c.CCM_KitModel'), fieldName: 'masterProduct'},
                {label: $A.get('$Label.c.CCM_ModelNumber'), fieldName: 'modelNumber'},
                {label: $A.get('$Label.c.CCM_SerialNumber'), fieldName: 'serialNumber'},
                {
                    label: $A.get('$Label.c.CCM_Receipt'),
                    tdStyle: 'text-align: center',
                    children:[
                        {
                            type: "lightning:button",
                            attributes:{
                                label: "${receiptName}",
                                value: "${receiptUrl}",
                                variant:"bare",
                                class: "company-btn",
                                onclick: component.getReference("c.toReceiptUrl")
                            }
                        }
                    ]
                },
                {label: $A.get('$Label.c.CCM_WarrantyStatus'), fieldName: 'warrantyStatus'},
                {label: $A.get('$Label.c.CCM_ExpirationDate'), fieldName: 'expirationDate'},
            ];
            component.set('v.residentialEditColumns', residentialEditColumns);
            // 获取ResidentialEdit数据
            helper.getResidentialEditData(component, recordId);
        }
    },
    handlerCancel : function(component, event, helper){
        window.history.back();
    },
    handlerSubmit : function(component, event, helper){
        const params = new URLSearchParams(window.location.search);
        const recordId =  params.get('recordId');
        helper.updateUserHistory(component, recordId);
    },
    // 修改email
    changeEmail : function(component, event, helper){
        helper.checkDuplicateEmail(component);
    },
    // 预览发票
    toReceiptUrl : function(component, event, helper){
        const receiptUrl = event.getSource().get('v.value');
        console.log(receiptUrl, 'receiptUrl-------------');
        if (receiptUrl) {
            helper.showReceiptUrl(component, receiptUrl);
        }
    },
})