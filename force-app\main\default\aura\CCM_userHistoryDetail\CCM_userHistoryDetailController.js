({
    init:function(component, event, helper){
        const params = new URLSearchParams(window.location.search);
        const userType =  params.get('userType');
        const recordId =  params.get('recordId');
        const actionType =  params.get('actionType');
        component.set('v.userType', userType);
        component.set('v.recordId', recordId);
        console.log('recordId === ',recordId);
        console.log('userType === ',userType);
        console.log('actionType === ',actionType);
        component.set('v.actionType', actionType);
        if (userType == 'Commercial' && actionType == 'view') {
            component.set('v.showType', 'commercialView');
            const commercialViewColumns= [
                {label: 'Purchase Date', fieldName: 'purchaseDate'},
                {label: 'Brand', fieldName:'brand'},
                {label: 'Purchase Place', fieldName:'purchasePlace'},
                {label: 'Kit/Model', fieldName: 'masterProduct'},
                {label: 'Model Number', fieldName: 'modelNumber'},
                {label: 'Serial Number', fieldName: 'serialNumber'},
                {
                    label: 'Receipt',
                    tdStyle: 'text-align: center',
                    children:[
                        {
                            type: "lightning:button",
                            attributes:{
                                label: "${receiptName}",
                                value: "${receiptUrl}",
                                variant:"bare",
                                class: "company-btn",
                                onclick: component.getReference("c.toReceiptUrl")
                            }
                        }
                    ]
                },
                {label: 'Warranty Status', fieldName: 'warrantyStatus'},
                {label: 'Expiration Date', fieldName: 'expirationDate'},
            ];
            component.set('v.commercialViewColumns',commercialViewColumns);
            // 获取commercialView数据
            helper.getCommercialViewData(component, recordId);
        } else if (userType == 'Commercial' && actionType == 'edit') {
            component.set('v.showType', 'commercialEdit');
            // 获取commercialEdit数据
            helper.getCommercialEditData(component, recordId);
        } else if (userType == 'Residential' && actionType == 'view') {
            component.set('v.showType', 'residentialView');
            const residentialViewColumns= [
                {label: 'Purchase Date', fieldName: 'purchaseDate'},
                {label: 'Brand', fieldName: 'brand'},
                {label: 'Purchase Place', fieldName:'purchasePlace'},
                {label: 'Master Product', fieldName: 'masterProduct'},
                {label: 'Model Number', fieldName: 'modelNumber'},
                {label: 'Serial Number', fieldName: 'serialNumber'},
                {
                    label: 'Receipt',
                    tdStyle: 'text-align: center',
                    children:[
                        {
                            type: "lightning:button",
                            attributes:{
                                label: "${receiptName}",
                                value: "${receiptUrl}",
                                variant:"bare",
                                class: "company-btn",
                                onclick: component.getReference("c.toReceiptUrl")
                            }
                        }
                    ]
                },
                {label: 'Warranty Status', fieldName: 'warrantyStatus'},
                {label: 'Expiration Date', fieldName: 'expirationDate'},
            ];
            component.set('v.residentialViewColumns', residentialViewColumns);
            // 获取ResidentialView数据
            helper.getResidentialViewData(component, recordId);
        } else if (userType == 'Residential' && actionType == 'edit') {
            component.set('v.showType', 'residentialEdit');
            const residentialEditColumns= [
                {label: 'Purchase Date', fieldName: 'purchaseDate'},
                {label: 'Brand', fieldName:'brand'},
                {label: 'Purchase Place', fieldName:'purchasePlace'},
                {label: 'Master Product', fieldName: 'masterProduct'},
                {label: 'Model Number', fieldName: 'modelNumber'},
                {label: 'Serial Number', fieldName: 'serialNumber'},
                {
                    label: 'Receipt',
                    tdStyle: 'text-align: center',
                    children:[
                        {
                            type: "lightning:button",
                            attributes:{
                                label: "${receiptName}",
                                value: "${receiptUrl}",
                                variant:"bare",
                                class: "company-btn",
                                onclick: component.getReference("c.toReceiptUrl")
                            }
                        }
                    ]
                },
                {label: 'Warranty Status', fieldName: 'warrantyStatus'},
                {label: 'Expiration Date', fieldName: 'expirationDate'},
            ];
            component.set('v.residentialEditColumns', residentialEditColumns);
            // 获取ResidentialEdit数据
            helper.getResidentialEditData(component, recordId);
        }
    },
    handlerCancel : function(component, event, helper){
        window.history.back();
    },
    handlerSubmit : function(component, event, helper){
        const params = new URLSearchParams(window.location.search);
        const recordId =  params.get('recordId');
        helper.updateUserHistory(component, recordId);
    },
    // 修改email
    changeEmail : function(component, event, helper){
        helper.checkDuplicateEmail(component);
    },
    // 预览发票
    toReceiptUrl : function(component, event, helper){
        const receiptUrl = event.getSource().get('v.value');
        console.log(receiptUrl, 'receiptUrl-------------');
        if (receiptUrl) {
            helper.showReceiptUrl(component, receiptUrl);
        }
    },
})