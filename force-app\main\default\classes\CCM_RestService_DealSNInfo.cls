/**********************************************************
 * @url: /services/apexrest/CCM_RestService_DealSNInfo
 * <AUTHOR>
 * @date 2023-03-14
 * @description This is the interface that Sync SN From ERP To SFDC.
 * 根据当前批次号，判断请求类型并处理：UNION_BATCH = 'SN',则把SN信息更新至对应Warranty Item；若UNION_BATCH  != 'SN',则将SN信息写入SN_Temp表
 * @data:
 * [
    {
        "SHIPPING_ORDER": "914612",
        "CUSTOMER": "E02320",
        "ORDER": "1033531",
        "ORDER_LINE": "1",
        "SN": "ESN02222115397X",
        "BATCH_NO": "201801W1",
        "MODEL_NO": "SNT2400E",
        "UNION_BATCH": "SN",
        "TOTAL_NUM": "1",
        "CONTAINER_NO":"22222",
        "INVOICE_NO":"53738"
    }
]

 *
 ***********************************************************/
@RestResource(urlMapping='/CCM_RestService_DealSNInfo')
global with sharing class CCM_RestService_DealSNInfo {

    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        String type = ''; 
        resObj.Process_Result = new List<ReturnItem>();
        String resStr = req.requestBody.toString();
        //String logId1 = Util.logIntegration('SN Sync call In','CCM_RestService_DealSNInfo','POST','',resStr, '');
        try{
            
            if (!resStr.startsWith('[')) {
                resStr = '[' + resStr + ']';
            }
            resStr = resStr.replace('\'','\"');
            reqObjList = parse(resStr);
            
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObjList);
            List<SN_Temp__c> lstSNToAdd = new List<SN_Temp__c>();
            if (reqObjList != null && reqObjList.size() > 0)    {
                //获取批次号，判断请求类型
                String unionBatch = reqObjList.get(0).UNION_BATCH;
                System.debug('unionBatch===>'+unionBatch);
                //请求类型不为SN，将SN写入临时表SN_Temp__c
                if(!unionBatch.contains(CCM_Constants.SN_QUERY_SN) && !unionBatch.contains('SEC') && !unionBatch.contains('PJALL')){
                    for(ReqestObj reqObj : reqObjList){
                        SN_Temp__c objSN = new SN_Temp__c();
                        objSN.Shipping_Order_No__c = reqObj.SHIPPING_ORDER;
                        objSN.Customer_Code__c = reqObj.CUSTOMER;
                        objSN.Order_No__c = reqObj.ORDER;
                        objSN.Order_Line_No__c = reqObj.ORDER_LINE;
                        objSN.SN__c = reqObj.SN;
                        objSN.Batch_No__c = reqObj.BATCH_NO;
                        objSN.Model_No__c = reqObj.MODEL_NO;
                        objSN.Bare_Tool_Model__c = reqObj.Attribute2;
                        objSN.Union_Batch__c = reqObj.UNION_BATCH;
                        objSN.Total_Num__c = reqObj.TOTAL_NUM;
                        objSN.Container_No__c = reqObj.CONTAINER_NO;
                        objSN.Invoice_No__c = reqObj.INVOICE_NO;
                        objSN.Order_Line_OracleId__c = reqObj.ORDER_LINE_ORACLEID;
                        objSN.Order_OracleId__c = reqObj.ORDER_ORACLEID;
                        objSN.Process_Message__c = reqObj.PROCESS_MESSAGE;
                        //add start vince 2023-10-25
                        objSN.Order_Date__c = String.isEmpty(reqObj.ORDER_DATE) ? null : Date.valueOf(reqObj.ORDER_DATE);
                        objSN.Sub_Inventory_Code__c = reqObj.SUBINVENTORY;
                        objSN.QTY__c = String.isEmpty(reqObj.QTY) ? null : Decimal.valueOf(reqObj.QTY);
                        objSN.Shipment_Date__c = String.isEmpty(reqObj.SHIPMENT_DATE) ? null : Date.valueOf(reqObj.SHIPMENT_DATE);
                        objSN.Bill_To_Name__c = reqObj.BILL_TO_NAME;
                        objSN.Ship_To_Name__c = reqObj.SHIP_TO_NAME;
                        objSN.Ship_To_Country__c = reqObj.SHIP_TO_COUNTRY;
                        objSN.Bom_SN__c = reqObj.Attribute1;
                        //add end vince 2023-10-25
                        lstSNToAdd.add(objSN);
                    }
                    System.debug(LoggingLevel.INFO,'***SNList : ' + lstSNToAdd);
                    Database.SaveResult[] resList = Database.insert(lstSNToAdd,false);
                    //请求记录，写入日志
                    //返回报错信息
                    for (Integer i = 0 ; i < resList.size() ; i++) {
                        if (!resList.get(i).isSuccess()) {
                            Database.Error[] err = resList.get(i).getErrors();
                            ReturnItem request = new ReturnItem();
                            request.External_Id = reqObjList.get(i).SN;
                            request.Error_Message = 'This SN was failed saving in Salesforce';
                            request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                            resObj.Process_Result.add(request);
                        }
                    }
                    if (resObj.Process_Result.size() == 0) {
                        resObj.Process_Status = 'Success';
                        String logId = Util.logIntegration('SN Sync Log','CCM_RestService_DealSNInfo','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(reqObjList), JSON.serialize(resObj));
                    }else {
                        resObj.Process_Status = 'Fail';
                        String logId = Util.logIntegration('SN Sync Exception','CCM_RestService_DealSNInfo','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(reqObjList), JSON.serialize(resObj));
                        Util.pushExceptionEmail('Accept SN Info',logId,getMailErrorMessage(resObj));
                    }
                    System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));

                    List<SN_Export__c> snExportRequestList = [SELECT Request_Id__c, Usage__c FROM SN_Export__c WHERE Request_Id__c = :unionBatch];
                    if(!snExportRequestList.isEmpty()) {
                        if('WarrantyClaimQuery' == snExportRequestList[0].Usage__c){
                            Boolean findPriceInLogistic = CCM_WarrantyClaim_VATGAPCalculateCtl.processWarrantyClaimQuery(reqObjList);
                            if(!findPriceInLogistic) {
                                CCM_WarrantyClaim_VATGAPCalculateCtl.findPirceInSellableProductList(unionBatch);
                            }
                        }
                    }
                }else if(unionBatch.contains('SEC') || unionBatch.contains('PJALL')){
                    //Project recall 场景   requestId：SEC20230909001
                    updateSNByProject(resObj,resStr,reqObjList);
                }else {
                    //TODO  请求类型为SN时，更新SN信息到对应的Warranty Item
                    //获取请求体中的SN、Order、Order Line、Shipping Order、Customer、Invoice存入Set
                    Set<String> setSn = new Set<String>();
                    Set<String> setShippingOrderNo = new Set<String>();
                    Set<String> setOrderExternalId = new Set<String>();
                    Set<String> setOrderLineExternalId = new Set<String>();
                    Set<String> setCustomerCode = new Set<String>();
                    Set<String> setInvoiceNo = new Set<String>();
                    //将Sn与warranty item、order、shipping 、order customer、入参记录 建立映射关系
                    Map<String,ReqestObj> mapSnReq = new Map<String,ReqestObj>();
                    Map<String,Warranty_Item__c> mapSnWi = new Map<String,Warranty_Item__c>();
                    //获取原请求批次号
                    String requestId = reqObjList[0].UNION_BATCH;
                    //请求中SN与MSG的映射
                    Map<String,String> mapSNToMsg = new Map<String,String>();
                    //获取原批次号的SN请求关联的warranty
                    List<Warranty_Item__c> lstWI = new List<Warranty_Item__c>();
                    System.debug(LoggingLevel.INFO, '*** requestId: ' + requestId);
                    lstWI = [SELECT 
                                    Id,Batch_Number__c,Serial_Number__c,Related_Sales_Order_Line_No__c,Order_No__c,Related_Customer__c,
                                    Related_Shipping_Order_No__c,Related_Customer_Code__c,Order_Line_OracleId__c,Order_OracleId__c,Union_Batch__c,Logistics_Database__c  
                                FROM Warranty_Item__c 
                                WHERE Union_Batch__c  = :requestId limit 49999];    
                    System.debug(LoggingLevel.INFO, '*** lstWI: ' + lstWI);
                    List<Warranty_Item__c> lstWIToUpdate = new List<Warranty_Item__c>();
                    for(ReqestObj reqObj : reqObjList){
                        System.debug(LoggingLevel.INFO, '*** reqObj.SN: ' + reqObj.SN);
                        if(String.isNotBlank(reqObj.SN)){
                            setSn.add(reqobj.SN);
                            System.debug(LoggingLevel.INFO, '*** reqobj.ORDER_ORACLEID: ' + reqobj.ORDER_ORACLEID);
                            if(!String.isBlank(reqobj.ORDER_ORACLEID)){
                                setOrderExternalId.add(reqobj.ORDER_ORACLEID);
                                setShippingOrderNo.add(reqobj.SHIPPING_ORDER);
                                setOrderLineExternalId.add(reqobj.ORDER_LINE_ORACLEID);
                                setCustomerCode.add(reqobj.CUSTOMER);
                                setInvoiceNo.add(reqobj.INVOICE_NO);
                            }
                            mapSnReq.put(reqObj.SN,reqObj);
                        }
                    }
                    //获取相关order、Order Line、shipping order、customer的Salesforce Id
                    System.debug(LoggingLevel.INFO,'***请求体SN存入setOrderExternalId : ' + setOrderExternalId);
                    System.debug(LoggingLevel.INFO,'***请求体SN存入setShippingOrderNo : ' + setShippingOrderNo);
                    System.debug(LoggingLevel.INFO,'***请求体SN存入setCustomerCode : ' + setCustomerCode);



                    List<Order> lstOrder = [SELECT Id,Order_OracleID__c FROM Order WHERE Order_OracleID__c in :setOrderExternalId limit 10000];
                    List<Order_Item__c> lstOrderLine = [SELECT Id,OrderLine_OracleID__c,Order__c FROM Order_Item__c WHERE  OrderLine_OracleID__c in :setOrderLineExternalId limit 10000];
                    List<Shipment__c> lstShippingOrder = [SELECT Id,Ship_OracleID__c FROM Shipment__c WHERE Ship_OracleID__c in :setShippingOrderNo limit 10000];
                    List<Account> lstCustomer = [SELECT Id,AccountNumber FROM Account WHERE AccountNumber in :setCustomerCode limit 10000];
                    List<Invoice__c> lstInvoice = [SELECT Id,Invoice_Number__c FROM Invoice__c WHERE Invoice_Number__c in :setInvoiceNo limit 10000];
                    //将order、Order Line、shipping order、customer、invoice的Oracle Id与Salesforce Id建立映射
                    Map<String,String> mapOrderNo = new Map<String,String>(); 
                    Map<String,String> mapOrderLineExternalId = new Map<String,String>(); 
                    Map<String,String> mapShippingOrderNo = new Map<String,String>(); 
                    Map<String,String> mapCustomerNo = new Map<String,String>();
                    Map<String,String> mapInvoiceNo = new Map<String,String>();
                    for(Order o : lstOrder){
                        mapOrderNo.put(o.Order_OracleID__c,o.Id);
                    }
                    for(Order_Item__c oi : lstOrderLine){
                        mapOrderLineExternalId.put(oi.OrderLine_OracleID__c,oi.id);
                    }
                    for(Shipment__c spt : lstShippingOrder){
                        mapShippingOrderNo.put(spt.Ship_OracleID__c,spt.Id);
                    }
                    for(Account a : lstCustomer){
                        mapCustomerNo.put(a.AccountNumber,a.Id);
                    }
                    for(Invoice__c i : lstInvoice){
                        mapInvoiceNo.put(i.Invoice_Number__c,i.Id);
                    } 
                    
                    //遍历相关Warranty item,将订单、运单、客户信息更新
                    System.debug(LoggingLevel.INFO,'***根据SN查到的WI : ' + lstWI);
                    for(Warranty_Item__c wi : lstWI){
                        String sn = wi.serial_number__c;
                        if(mapSnReq.containsKey(sn)){
                            //判断MSG信息
                            if(String.isNotBlank(mapSnReq.get(sn).PROCESS_MESSAGE)){
                                //SN 不为空，且MSG 为 no result 时，表示未查到订单信息
                                if(mapSnReq.get(sn).PROCESS_MESSAGE.equals(CCM_Constants.SN_EXPORT_REP_MSG_NO_RESULT)){
                                    wi.Logistics_Database__c = Label.CCM_Warranty_Logistics_Database_No_Result;
                                }
                            }else{
                                //SN 不为空，但MSG为空时，表示查到订单信息
                                wi.Logistics_Database__c = Label.CCM_Warranty_Logistics_Database_Valid_SN;
                                wi.Batch_Number__c = mapSnReq.get(sn).BATCH_NO;
                                wi.Order_No__c = mapSnReq.get(sn).ORDER;
                                wi.Order_Line_OracleId__c = mapSnReq.get(sn).ORDER_LINE_ORACLEID;
                                wi.Order_OracleId__c = mapSnReq.get(sn).ORDER_ORACLEID;
                                wi.Related_Sales_Order_Line_No__c = mapSnReq.get(sn).ORDER_LINE;
                                wi.Related_Shipping_Order_No__c = mapSnReq.get(sn).SHIPPING_ORDER;
                                wi.Related_Customer_Code__c = mapSnReq.get(sn).CUSTOMER;
                                wi.Invoice_No__c =  mapSnReq.get(sn).INVOICE_NO;
                                wi.Container_No__c =  mapSnReq.get(sn).CONTAINER_NO;
                                if(mapOrderNo.containsKey(wi.Order_OracleId__c)) {
                                    wi.Order__c = mapOrderNo.get(wi.Order_OracleId__c);
                                }
                                if(mapOrderLineExternalId.containsKey(wi.Order_Line_OracleId__c)) {
                                    wi.Order_Item__c = mapOrderLineExternalId.get(wi.Order_Line_OracleId__c);
                                }
                                if(mapShippingOrderNo.containsKey(wi.Related_Shipping_Order_No__c)) {
                                    wi.Related_Shipping_Order__c = mapShippingOrderNo.get(wi.Related_Shipping_Order_No__c);
                                }
                                if(mapCustomerNo.containsKey(wi.Related_Customer_Code__c)) {
                                    wi.Related_Customer__c = mapCustomerNo.get(wi.Related_Customer_Code__c);
                                }
                                if(mapInvoiceNo.containsKey(wi.Invoice_No__c)) {
                                    wi.Related_Invoice__c = mapInvoiceNo.get(wi.Invoice_No__c);     
                                }
                            }   
                        }else{
                            //不包含在返回体中的SN，表示SN参数有误
                            wi.Logistics_Database__c = Label.CCM_Warranty_Logistics_Database_Invalid_SN;
                        }                     
                        lstWIToUpdate.add(wi);
                    }
                    System.debug(LoggingLevel.INFO,'***lstWIToUpdate待更新的WI : ' + lstWIToUpdate);
                    Database.SaveResult[] resList = Database.update(lstWIToUpdate,false);
                    //请求记录，写入日志
                    //返回报错信息
                    for (Integer i = 0 ; i < resList.size() ; i++) {
                        if (!resList.get(i).isSuccess()) {
                            Database.Error[] err = resList.get(i).getErrors();
                            ReturnItem request = new ReturnItem();
                            request.External_Id = reqObjList.get(i).SN;
                            request.Error_Message = 'This SN information was failed updating to warranty items in Salesforce';
                            request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                            resObj.Process_Result.add(request);
                        }
                    }
                    if (resObj.Process_Result.size() == 0) {
                        resObj.Process_Status = 'Success';
                        String logId = Util.logIntegration('SN Sync Log','CCM_RestService_DealSNInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                    }else {
                        resObj.Process_Status = 'Fail';
                        String logId = Util.logIntegration('SN Sync Exception','CCM_RestService_DealSNInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                        Util.pushExceptionEmail('Accept SN Info',logId,getMailErrorMessage(resObj));
                    }
                    System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));


                }              
            }else {
                //请求体为空，返回报错信息，写入日志
                resObj.Process_Status = 'Fail';
                ReturnItem empty = new ReturnItem();
                empty.Error_Message = 'Empty JSON';
                empty.Error_Detail = 'Empty JSON';
                resObj.Process_Result.add(empty);

                String logId = Util.logIntegration('SN Sync Exception','CCM_RestService_DealSNInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept SN Info',logId,getMailErrorMessage(resObj));
            }


        }catch (Exception e) {
            resObj.Process_Status = 'Fail';
            ReturnItem request = new ReturnItem();
            request.Error_Message = 'This SN was failed saving in Salesforce';
            request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
            resObj.Process_Result.add(request);
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration('SN Sync Exception','CCM_RestService_DealSNInfo','POST',
                                               JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept SN Sync Info',logId,getMailErrorMessage(resObj));
            return resObj;
        }
       
        System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
        return resObj;

    }

    public static void updateSNByProject(ResultObj resObj,String resStr,List<ReqestObj> reqObjList){
        String type;
        List<Target_Customer__c> lstTargetCustomer = new List<Target_Customer__c>();
        List<Order> lstOrder = new List<Order>();
        List<Account> lstCustomer = new List<Account>();
        List<Warranty_Claim__c> warrantyClaimList = new List<Warranty_Claim__c>();
        Map<String,String> mapOrderNo = new Map<String,String>();
        Map<String,String> mapCustomerNo = new Map<String,String>();
        Map<String,String> warrantyClaimMap = new Map<String,String>();
        Set<String> setOrderOracleId = new Set<String>();
        Set<String> setCustomerNo = new Set<String>();
        Set<String> snSet = new Set<String>();
        String unionBatch;
        Integer totalDataNum = 0;
        Map<String,Integer> mapBatchToTotalNum = new Map<String,Integer>();
        Map<String,Integer> mapBatchToReceivedNum = new Map<String,Integer>();
        Integer currentReceived = 0;
        //获取SN 区间信息
        List<Project_SN__c> lstSNSection = new List<Project_SN__c>();
        //获取project信息
        Project__c project = new Project__c();
        String projectId;
        String userId;
        String actionType;
        //Project__c + '-' + userId + '-' + actionType
        String projectString;
        //依次存放projectId，userId，actionType
        List<String> strList = new List<String>();
        
        for(ReqestObj reqObj : reqObjList){
            System.debug(LoggingLevel.INFO, '*** reqobj.ORDER_ORACLEID: ' + reqobj.ORDER_ORACLEID);
            setOrderOracleId.add(reqobj.ORDER_ORACLEID);
            setCustomerNo.add(reqobj.CUSTOMER);
            snSet.add(reqobj.SN);
            projectString = reqObj.PROJECT_ID;
            unionBatch = reqObj.UNION_BATCH;
            totalDataNum = Integer.valueOf(reqObj.TOTAL_NUM);
            currentReceived += 1;
            if(unionBatch.contains('SEC')){
                type = 'SN Section';
            }else if(unionBatch.contains('PJALL')){
                type = 'PJALL';
            }
            
        }
        //截取出projectId，userId，actionType
        strList = splitProjectStr(strList,projectString);
        projectId = strList[0];
        userId = strList[1];
        actionType = strList[2];
        System.debug('projectId=====>'+projectId);
        System.debug('userId=====>'+userId);
        System.debug('actionType=====>'+actionType);

        lstOrder = [SELECT Id,Order_OracleID__c FROM Order WHERE Order_OracleID__c in :setOrderOracleId limit 10000];
        lstCustomer = [SELECT Id,AccountNumber FROM Account WHERE AccountNumber in :setCustomerNo limit 10000];
        warrantyClaimList = [SELECT Id,Serial_Number__c,Service_Option__c,Project__c FROM Warranty_Claim__c WHERE Serial_Number__c in :snSet And Claim_Status__c = 'Approved' Order By Createddate];

        for(Order objOrder : lstOrder){
            mapOrderNo.put(objOrder.Order_OracleID__c, objOrder.Id);
        }

        for(Account objCustomer : lstCustomer){
            mapCustomerNo.put(objCustomer.AccountNumber, objCustomer.Id);
        }
        for(Warranty_Claim__c objClaim : warrantyClaimList){
            if(objClaim.Project__c != null || (objClaim.Project__c == null && objClaim.Service_Option__c =='Replacement')){
                warrantyClaimMap.put(objClaim.Serial_Number__c, objClaim.Id);
            }
        }

        for(ReqestObj reqObj : reqObjList){
            if(totalDataNum > 0){
                Target_Customer__c data = new Target_Customer__c();
                if(mapCustomerNo.containsKey(reqObj.CUSTOMER)){
                    data.Customer__c = mapCustomerNo.get(reqObj.CUSTOMER);
                }
                if(mapOrderNo.containsKey(reqObj.ORDER_ORACLEID)){
                    data.Order__c = mapOrderNo.get(reqObj.ORDER_ORACLEID);
                }
                if(warrantyClaimMap.containsKey(reqObj.SN)){
                    data.Warranty_Claim__c = warrantyClaimMap.get(reqObj.SN);
                }
                data.SN__c = reqObj.SN;
                data.Project__c = projectId;
                data.Union_Batch__c = reqObj.UNION_BATCH;
                data.Unique_Id__c = projectId + '-' + reqObj.SN;
                lstTargetCustomer.add(data);
                //应收
                mapBatchToTotalNum.put(reqObj.UNION_BATCH,Integer.valueOf(reqObj.TOTAL_NUM));
                //实收
                mapBatchToReceivedNum.put(reqObj.UNION_BATCH,currentReceived);
            }
        }
            

        if(type.equalsIgnoreCase('SN Section')){
            //更新区间段应收、实收数量
            lstSNSection = [SELECT End_SN__c, Project__c, Start_SN__c ,Union_Batch__c,Total_SN_Oracle_Num__c ,Callout_Status__c,Total_Outbound__c 
            FROM Project_SN__c 
            WHERE Union_Batch__c =:reqObjList[0].UNION_BATCH];
            for(Project_SN__c snSection: lstSNSection){
                if(mapBatchToTotalNum.containsKey(snSection.Union_Batch__c)){
                snSection.Total_SN_Oracle_Num__c = mapBatchToTotalNum.get(snSection.Union_Batch__c);
                }
                if(mapBatchToReceivedNum.containsKey(snSection.Union_Batch__c)){
                //对应实收数量判空，将当前数量与已有数量相加求和
                    if(snSection.Total_Outbound__c == null ){
                        snSection.Total_Outbound__c = 0;
                    }else{
                        snSection.Total_Outbound__c += mapBatchToReceivedNum.get(snSection.Union_Batch__c);
                    }
                }
                if(snSection.Total_Outbound__c < snSection.Total_SN_Oracle_Num__c){
                snSection.Callout_Status__c = 'In Progress';
                }else{
                snSection.Callout_Status__c = 'Complete';
                }
            }
            update lstSNSection;
            setTotalData(projectId);
        }else if(type.equalsIgnoreCase('PJALL')){
            project = [Select id, BatchExecuteStatus__c,All_SN_Callout_Status__c ,Total_SN_Outbound_Record__c  ,All_SN_Callout_Total_Num__c,All_SN__c,Product__c,Product__r.Order_Model__c From Project__c Where id =: projectId];
            if(mapBatchToTotalNum.containsKey(reqObjList[0].UNION_BATCH)){
                //应收总数
                project.All_SN_Callout_Total_Num__c = mapBatchToTotalNum.get(reqObjList[0].UNION_BATCH);
                //实收数量
                project.Total_SN_Outbound_Record__c += mapBatchToReceivedNum.get(reqObjList[0].UNION_BATCH);
                //状态
                if(project.All_SN_Callout_Total_Num__c > project.Total_SN_Outbound_Record__c){
                    project.All_SN_Callout_Status__c = 'In Progress';
                }else {
                    project.All_SN_Callout_Status__c = 'Complete';
                }
            }
            update project;
        }
        
        
        System.debug('需要插入的lstTargetCustomer共计条数：'+lstTargetCustomer.size());
            //插入lstTargetCustomer记录
        if(lstTargetCustomer.size() > 0){
            Database.UpsertResult[] resList = Database.upsert(lstTargetCustomer,Target_Customer__c.Unique_Id__c.getDescribe().getSObjectField(),false);
            
            //请求记录，写入日志
            //返回报错信息
            for (Integer i = 0 ; i < resList.size() ; i++) {
                if (!resList.get(i).isSuccess()) {
                    Database.Error[] err = resList.get(i).getErrors();
                    ReturnItem request = new ReturnItem();
                    request.External_Id = reqObjList.get(i).SN;
                    request.Error_Message = 'This SN information was failed updating to project recall in Salesforce';
                    request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                    resObj.Process_Result.add(request);
                }
            }
            if (resObj.Process_Result.size() == 0) {
                resObj.Process_Status = 'Success';
                String logId = Util.logIntegration('SN Sync Log','CCM_RestService_DealSNInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            }else {
                resObj.Process_Status = 'Fail';
                String logId = Util.logIntegration('SN Sync Exception','CCM_RestService_DealSNInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept SN Info',logId,getMailErrorMessage(resObj));
            }
            System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));
        }
        if(checkIsComplete(projectId, type)){
            sendCompleteMail(projectId, actionType, userId);
        }
    }

    /**
     * 根据'-'截取字符串
     **/
    public static List<String> splitProjectStr(List<String> strList,String projectStr){
        strList = projectStr.split('-');
        return strList;
    }

    public static Boolean checkIsComplete(String projectId, String CalloutType){
        Boolean checkResult = false;
        if(CalloutType.equalsIgnoreCase('SN Section')){
            checkResult = true;
            List<Project_SN__c> projectSN = new List<Project_SN__c>();
            projectSN = [Select Callout_Status__c From Project_SN__c Where Project__c =:projectId];
            if(projectSN.size() != 0){
                for(Project_SN__c snItem: projectSN){
                    if(snItem.Callout_Status__c != 'complete'){
                        checkResult = false;
                    }
                }
            }else{
                checkResult = false;
            }
        }else if(CalloutType.equalsIgnoreCase('PJALL')){
            Project__c projectObj = New Project__c();
            projectObj = [Select All_SN_Callout_Status__c From Project__c Where id =: projectId];
            if(projectObj.All_SN_Callout_Status__c == 'Complete'){
                checkResult = true;
            }else{
                checkResult = false;
            }
        }
        
        return checkResult;
    }

    public static void sendCompleteMail(String projectId, String actionType, String userId){
        // refresh Customer后发送邮件的template取得
        List<EmailTemplate> recLst = [Select 
                                            Subject, 
                                            Body
                                        FROM EmailTemplate
                                        WHERE DeveloperName = 'Project_Recall_Refresh_Complete'];
        List<OrgWideEmailAddress> sendMailOrg = New List<OrgWideEmailAddress>();
        sendMailOrg = [Select Id FROM OrgWideEmailAddress];
        User userInfo = [select id,Name,Email from User where id=: userId];
        Project__c projectFinshObj = New Project__c();
        
        projectFinshObj = [Select id,BatchExecuteStatus__c,Project_Code__c,Product__r.Name,RefreshTargetMailFlag__c From Project__c Where id =: projectId];

        if(projectFinshObj.RefreshTargetMailFlag__c || actionType =='customer'){
            if(actionType != 'batchSchedule'){
                // 邮件内容设定
                String strBody = recLst[0].Body;
                String strSubject = recLst[0].Subject;
                strBody = strBody.replaceAll('ACTIONNAME', 'Refresh Action');
                strBody = strBody.replaceAll('USERNAME', userInfo.Name); 
                strBody = strBody.replaceAll('PROJECTCODE', projectFinshObj.Project_Code__c);
                strBody = strBody.replaceAll('PRODUCTINFO', projectFinshObj.Product__r.Name);                     
                strBody = strBody.replaceAll('TARGETLINK', string.valueof(URL.getSalesforceBaseUrl().toExternalForm() +'/'+projectId ));
                strSubject = strSubject.replaceAll('ACTIONNAME', 'Refresh Action');
                
                List<Messaging.SingleEmailMessage> mailList = new List<Messaging.SingleEmailMessage>();
                Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                mail.setOrgWideEmailAddressId(sendMailOrg[0].Id);
                mail.setToAddresses(new String[]{userInfo.Email});
                mail.setSaveAsActivity(false);                                              
                mail.setSubject(strSubject);                                          
                mail.setPlainTextBody(strBody);                                             
                mailList.add(mail);

                try{
                    Messaging.sendEmail(mailList);
                }catch(exception e){
                    system.debug (e);
                }
            }else{
                CCM_ConsumerGroupCreateBatch consumerGroupBatch = new CCM_ConsumerGroupCreateBatch(projectId, true);
                Database.executeBatch(consumerGroupBatch, 200);

                CCM_CustomerGroupCreateBatch customerGroupBatch = new CCM_CustomerGroupCreateBatch(projectId, true);
                Database.executeBatch(customerGroupBatch, 200);
            }
            
            projectFinshObj.BatchExecuteStatus__c = false;
            projectFinshObj.RefreshTargetMailFlag__c = false;
            UPDATE projectFinshObj;
        }else{
            projectFinshObj.RefreshTargetMailFlag__c = true;
            UPDATE projectFinshObj;
        }
    }

    public static void setTotalData(String projectId){
        Project__c projectObj = New Project__c();
        List<Project_SN__c> projectSN = new List<Project_SN__c>();
        Integer totalClaimNum = 0;
        Integer totalOutbound = 0;

        
        projectSN = [Select Total_Outbound__c From Project_SN__c Where Project__c =:projectId];
        projectObj = [Select id, Total_Claim__c, Total_SN_Outbound_Record__c From Project__c Where id =: projectId];
        totalClaimNum = [Select Count() From Target_Customer__c Where Project__c =: projectId And Has_Claim__c = true];
    
        for(Project_SN__c snItem: projectSN){
            if(snItem.Total_Outbound__c != null){
                totalOutbound += Integer.valueOf(snItem.Total_Outbound__c);
            }
        }

        projectObj.Total_Claim__c = totalClaimNum;
        projectObj.Total_SN_Outbound_Record__c = totalOutbound;
        UPDATE projectObj;

    }

    global class ReqestObj {
        //0414 EBS callback
        global String SHIPPING_ORDER;
        global String SHIPPING_ORDER_LINE;
        global String CUSTOMER;
        global String ORDER;
        global String ORDER_LINE;
        global String SN;
        global String BATCH_NO;
        global String MODEL_NO;
        global String UNION_BATCH;
        global String TOTAL_NUM;
        global String CONTAINER_NO;
        global String INVOICE_NO;
        global String ORDER_ORACLEID;
        global String ORDER_LINE_ORACLEID;
        global String PROCESS_MESSAGE;
        global String PROJECT_ID;

        //add start vince 2023-10-25
        global String ORDER_DATE;
        global String SUBINVENTORY;
        global String QTY;
        global String SHIPMENT_DATE;
        global String BILL_TO_NAME;
        global String SHIP_TO_NAME;
        global String SHIP_TO_COUNTRY;
        //add end vince 2023-10-25
        global String Attribute1;
        global String Attribute2;
        global String Attribute3;
        global String Attribute4;
        global String Attribute5;
        global String Attribute6;
        global String Attribute7;
        global String Attribute8;
        global String Attribute9;
        global String Attribute10;
    }

    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
        errContent += 'Process Status : Fail<br/><br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
                errContent += 'External ID : ' + Item.External_Id + '<br/>';
                errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
                errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>';
            }
        }
        return errContent;
    }

    global static List<ReqestObj> parse(String jsonStr) {
        return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }

    global class ResultObj {
        global String Process_Status;
        global List<ReturnItem> Process_Result;
    }

    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
}