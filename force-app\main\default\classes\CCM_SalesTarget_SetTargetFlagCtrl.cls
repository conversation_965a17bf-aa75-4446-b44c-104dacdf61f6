/**
 * <AUTHOR>
 * @date 2025-07-28
 * @description Set sales person target flag
 */
public without sharing class CCM_SalesTarget_SetTargetFlagCtrl {
    public static void setTargetFlag(String salesTargetId) {
        List<Quota_Allocation_Detail__c> salesTargetDetails = [SELECT Parent_Quota_Detail__c, Id, Sales_Person_Name__c, Quota_Allocation__c, Assigned_Actual__c FROM Quota_Allocation_Detail__c WHERE Quota_Allocation__c = :salesTargetId Order By Parent_Quota_Detail__c];
        Map<String, List<Quota_Allocation_Detail__c>> parentChildMap = new Map<String, List<Quota_Allocation_Detail__c>>();
        for(Quota_Allocation_Detail__c salesTargetDetail : salesTargetDetails) {
            String key = '';
            if(salesTargetDetail.Parent_Quota_Detail__c != null) {
                key = salesTargetDetail.Parent_Quota_Detail__c;
            }
            if(!parentChildMap.containsKey(key)) {
                parentChildMap.put(key, new List<Quota_Allocation_Detail__c>());
            }
            parentChildMap.get(key).add(salesTargetDetail);
        }

        List<SalesTargetWrapper> topTargetWrappers = new List<SalesTargetWrapper>();
        for(Quota_Allocation_Detail__c salesTargetDetail : salesTargetDetails) {
            if(salesTargetDetail.Parent_Quota_Detail__c == null) {
                SalesTargetWrapper topTargetWrapper = new SalesTargetWrapper();
                topTargetWrapper.currentTarget = salesTargetDetail;
                topTargetWrappers.add(topTargetWrapper);
            }
        }

        for(SalesTargetWrapper topTargetWrapper : topTargetWrappers) {
            constructSalesTargetTree(parentChildMap, topTargetWrapper);
        }

        Set<String> parentSalesPersonNames = new Set<String>();
        List<Quota_Allocation_Detail__c> updateSalesTargets = new List<Quota_Allocation_Detail__c>();

        for(SalesTargetWrapper topTargetWrapper : topTargetWrappers) { 
            assignFlag(topTargetWrapper, parentSalesPersonNames, updateSalesTargets);
        }
        // update updateSalesTargets;
        List<Sales_Target_Detail_Report__c> targetReports = new List<Sales_Target_Detail_Report__c>();
        for(Quota_Allocation_Detail__c target : updateSalesTargets) {
            Sales_Target_Detail_Report__c targetReport = new Sales_Target_Detail_Report__c();
            targetReport.Quota_Allocation__c = target.Quota_Allocation__c;
            targetReport.Assigned_Quota__c = target.Assigned_Actual__c;
            targetReport.Sales_Person_Name__c = target.Sales_Person_Name__c;
            targetReports.add(targetReport);
        }
        insert targetReports;
    }

    private static void constructSalesTargetTree(Map<String, List<Quota_Allocation_Detail__c>> parentChildMap, SalesTargetWrapper parentSalesTarget) {
        if(parentChildMap.keySet().size() == 0) {
            return;
        }

        String parentTargetId = parentSalesTarget.currentTarget.Id;
        if(parentChildMap.containsKey(parentTargetId)) {
            // System.debug('*** parentTargetId: ' + parentTargetId);
            // System.debug('*** parentChildMap.get(parentTargetId): ' + JSON.serialize(parentChildMap.get(parentTargetId)));
            List<SalesTargetWrapper> childTargetWrappers = new List<SalesTargetWrapper>();
            for(Quota_Allocation_Detail__c childTarget : parentChildMap.get(parentTargetId)) {
                SalesTargetWrapper childTargetWrapper = new SalesTargetWrapper();
                childTargetWrapper.currentTarget = childTarget;
                childTargetWrappers.add(childTargetWrapper);
            }
            parentSalesTarget.childTargets = childTargetWrappers;
            for(SalesTargetWrapper childTargetWrapper : childTargetWrappers) {
                constructSalesTargetTree(parentChildMap, childTargetWrapper);
            }
            parentChildMap.remove(parentTargetId);
        }
    }


    private static void assignFlag(SalesTargetWrapper parentTargetWrapper, Set<String> parentSalesPersonNames, List<Quota_Allocation_Detail__c> updateSalesTargets) {
        // System.debug(parentTargetWrapper.currentTarget.Parent_Quota_Detail__c + ',' + parentTargetWrapper.currentTarget.Id + ',' + parentTargetWrapper.currentTarget.Sales_Person_Name__c);
        if(!parentSalesPersonNames.contains(parentTargetWrapper.currentTarget.Sales_Person_Name__c)) {
            // parentTargetWrapper.currentTarget.Sales_Person_Target_Flag__c = true;
            updateSalesTargets.add(parentTargetWrapper.currentTarget);
            parentSalesPersonNames.add(parentTargetWrapper.currentTarget.Sales_Person_Name__c);
        }
        if(parentTargetWrapper.childTargets == null) {
            return;
        }
        for(SalesTargetWrapper childTargetWrapper : parentTargetWrapper.childTargets) {
            assignFlag(childTargetWrapper, parentSalesPersonNames, updateSalesTargets);
        }
    }

    private class SalesTargetWrapper {
        public Quota_Allocation_Detail__c currentTarget;
        public List<SalesTargetWrapper> childTargets;
    }
}