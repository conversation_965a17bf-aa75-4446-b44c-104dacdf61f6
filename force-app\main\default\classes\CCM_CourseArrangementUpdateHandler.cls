/**
* Honey
* 在状态变为receive的时候同步数据到Oracle
*/
public without sharing class CCM_CourseArrangementUpdateHandler implements Triggers.Handler{
    public static Boolean isRun = true;
    
    public void handle(){
        if(isRun){
            system.debug('进入修改');
            List<String> lstRegisterId = new List<String>();
            List<String> lstSendEmailId = new List<String>();
            Map<Id,Object> mapId2ResalesOld =  (Map<Id, Object>)Trigger.oldMap;
            List<String> lstAutoConfirmId = new List<string>();
            List<String> lstApprovalId = new List<String>();
            List<String> lstRejectId = new List<String>();

            for(Course_Register__c objCourse : (List<Course_Register__c>)Trigger.new){
                Course_Register__c objCourseOld = (Course_Register__c)mapId2ResalesOld.get(objCourse.Id);
                //校验状态是否变为Reserved-->需要同步到Oralce
                if(objCourse.Status__c == 'Confirmed' && objCourseOld.Status__c != 'Confirmed'){
                    lstRegisterId.add(objCourse.Id);
                }
                //校验数据变更--->发邮件通知到Owner
                if(objCourse.Customer__c != objCourseOld.Customer__c || objCourse.Billing_Address__c != objCourseOld.Billing_Address__c ||
                   objCourse.Training_Course__c != objCourseOld.Training_Course__c || objCourse.Course_Arrangement__c != objCourseOld.Course_Arrangement__c
                   || objCourse.Trainee__c != objCourseOld.Trainee__c){
                       lstSendEmailId.add(objCourse.Id);
                   }
                //开始日期前四天可以有取消按钮
                Datetime now =  Datetime.now();
                //获取开始日期的前四天
                Datetime startDate = objCourse.Training_Start_Time__c.addDays(-4);
                system.debug('startDate--->'+startDate);
                system.debug('now--->'+now);
                if(objCourse.Status__c == 'Reserved' && objCourseOld.Status__c != 'Reserved' && now > startDate){
                    //现在时间大于开始的前四天。自动同步
                    lstAutoConfirmId.add(objCourse.Id);
                }
                if(objCourse.Status__c == 'Reserved' && objCourseOld.Status__c != 'Reserved' ){
                    //审批通过发送邮件
                    lstApprovalId.add(objCourse.Id);
                }

            }

            if(lstRegisterId != null && lstRegisterId.size() > 0){
                Course_Register__c course = [
                    SELECT Id, Name, Billing_Address__c, Billing_Address__r.Country__c,
                           Course_Arrangement__c, Course_Arrangement__r.Free_Training__c
                    FROM Course_Register__c
                    WHERE Id = :lstRegisterId[0]
                    ];
                // Only sync to ERP if it's not a free training and country is DE
                if(course.Billing_Address__r.Country__c == 'DE' && course.Course_Arrangement__r.Free_Training__c != true){
                    pushRegisterToEBS(lstRegisterId[0]);
                }

                //状态变为confirm需要发邮件到dealer
                SendConfirmEmail(lstRegisterId);
                 system.debug('状态变为confirm需要发邮件到dealer');
            }
            if(lstSendEmailId != null && lstSendEmailId.size() > 0){
                system.debug('变更发邮件--->'+lstSendEmailId);
                sendUpdateEmail(lstSendEmailId,'update');
            }
            if(lstApprovalId != null && lstApprovalId.size() > 0){
                system.debug('变更发邮件--->'+lstApprovalId);
                sendUpdateEmail(lstApprovalId,'approval');
            }
            if(lstAutoConfirmId != null && lstAutoConfirmId.size()>0){
                autoConfirm(lstAutoConfirmId);
            }
        }
    }

    public CCM_CourseArrangementUpdateHandler() {
    }

    public static void autoConfirm(List<String> lstRecordId){
        List<Course_Register__c> lstRegister = [
            SELECT Status__c FROM Course_Register__c WHERE Id IN : lstRecordId
        ];
        For(Course_Register__c objRegister : lstRegister){
            objRegister.Status__c = 'Confirmed';
        }
        update lstRegister;
        //SendConfirmEmail(lstRecordId);

    }

    public static void sendUpdateEmail(List<String> lstRecordId,String type){
        try {
            //审核通过发送邮件到创建人
            List<EmailTemplate> lstEmailTemplate = new List<EmailTemplate>();
            //不同语言不同邮件内容
            //判断当前语言
            String userLanguage = UserInfo.getLanguage();
            String EmailName = '';
            if(type == 'update') {
                if(userLanguage == CCM_Constants.DE){
                    EmailName = 'Training Update Email DE';
                }else{
                    EmailName = 'Training Update Email';
                }
            }
            if(type == 'approval'){
                if(userLanguage == CCM_Constants.DE){
                    EmailName = 'Training Approval Email DE';
                }else{
                    EmailName = 'Training Approval Email';
                }

            }


            lstEmailTemplate = [
                Select e.Id, e.Name,Subject,Body,HtmlValue from EmailTemplate e where Name  = :EmailName
            ];

            if(lstEmailTemplate != null && lstEmailTemplate.size()>0){
                 String templateId = lstEmailTemplate[0].Id;
                 //通过recordId查询Register的创建人
                 List<Course_Register__c> lstCourse = new List<Course_Register__c>();
                 lstCourse = [
                    SELECT Id,CreatedById,CreatedBy.Email,CreatedBy.IsActive,Status__c,Customer__c,
                    Customer__r.Name,Customer__r.AccountNumber,Billing_Address__c,Billing_Address__r.Final_Address__c,
                    Course_Arrangement__c,Training_Course__r.Course_Name__c,Training_Course_Name__c,
                    Training_Course__c,Course_Arrangement__r.Course_Date__c,Course_Arrangement__r.End_Time__c,
                    Course_Arrangement__r.Start_Time__c,Course_Arrangement__r.Training_Location__r.Final_Address__c,
                    Shiping_Address__c,
                    Billing_Address__r.Acknowledgement_Customer__c,
                    Billing_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Billing_Address__r.Acknowledgement_Customer__r.Salutation),
                    Shiping_Address__r.Acknowledgement_Customer__c,
                    Shiping_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Shiping_Address__r.Acknowledgement_Customer__r.Salutation),

                    Trainee__c
                      FROM Course_Register__c
                    WHERE CreatedBy.IsActive = TRUE AND Id IN :lstRecordId
                ];
                List<String> lstReceiptEmail = new List<String>();

                List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
                List<Messaging.SingleEmailMessage> lstemails = new List<Messaging.SingleEmailMessage>();
                for(Course_Register__c objCourse : lstCourse){
                    if(String.isNotBlank(objCourse.Billing_Address__r.Acknowledgement_Customer__c)){
                        //不为空。表示取billAddress上的email
                        lstReceiptEmail.add(objCourse.Billing_Address__r.Acknowledgement_Customer__r.Email);
                    }else if(String.isNotBlank(objCourse.Shiping_Address__r.Acknowledgement_Customer__c)){
                         //不为空。表示取ShipAddress上的email
                         lstReceiptEmail.add(objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Email);

                    }
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    //邮件内容设定
                    email.setSubject(lstEmailTemplate[0].Subject);
                    String strBody = lstEmailTemplate[0].HtmlValue;
                    strBody = strBody.replace('{Customer_Name__c}', objCourse.Customer__r.Name);
                    strBody = strBody.replace('{Customer_Number__c}', objCourse.Customer__r.AccountNumber);
                    strBody = strBody.replace('{Bill_Address_Info__c}', objCourse.Billing_Address__r.Final_Address__c);
                    strBody = strBody.replace('{Training_Course_Name__c}', objCourse.Training_Course_Name__c);
                    strBody = strBody.replace('{Course_Location_Info__c}', objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c == null ? '' : objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c);
                    String courseTime = String.valueOf(objCourse.Course_Arrangement__r.Course_Date__c) +' '
                    + String.valueOf( objCourse.Course_Arrangement__r.Start_Time__c).subString(0,8) +'-'+
                     String.valueOf(objCourse.Course_Arrangement__r.End_Time__c).subString(0,8);
                    strBody = strBody.replace('{Course_Time__c}', courseTime);

                    strBody = strBody.replace('{Trainee__c}', objCourse.Trainee__c);
                    String gender = objCourse.Billing_Address__r.Acknowledgement_Customer__c == null ?
                    objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Salutation :
                    objCourse.Billing_Address__r.Acknowledgement_Customer__r.Salutation;

                    strBody = strBody.replace('{Family Name}', gender == null ? '': gender);

                    email.setTargetObjectId(UserInfo.getUserId());
                    email.setHtmlBody(strBody);

                    if(lstReceiptEmail == null || lstReceiptEmail.size() == 0){
                        break ;
                    }
                    system.debug('send Email--->'+lstReceiptEmail);
                    email.setToAddresses(lstReceiptEmail);
                    // email.setCcAddresses(new List<String>{'<EMAIL>'});
                    //设置发件人

                    if(listAddresses != null  && listAddresses.size() > 0){
                        email.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }
                    email.setSaveAsActivity(false);
                    lstemails.add(email);

                }
                //发送邮件
                if (!System.Test.isRunningTest()){
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(lstemails);
                }
             }
        } catch (Exception e) {
            system.debug('报错信息－－－＞'+e.getMessage()+'报错行数－－－－'+e.getLineNumber());
        }
    }

    public static void SendConfirmEmail(List<String> lstRecordId){
        try {
            //审核通过发送邮件到创建人
            List<EmailTemplate> lstEmailTemplate = new List<EmailTemplate>();
            //不同语言不同邮件内容
            //判断当前语言
            String userLanguage = UserInfo.getLanguage();
            String EmailName = '';
            if(userLanguage == CCM_Constants.DE){
                EmailName = 'Training Reminder within 7 days (Participants) DE';
            }else{
                EmailName = 'Training Confrim Email';
            }
            lstEmailTemplate = [
                Select e.Id, e.Name,Subject,Body,HtmlValue from EmailTemplate e where Name  = :EmailName
            ];


            if(lstEmailTemplate != null && lstEmailTemplate.size()>0){
                String templateId = lstEmailTemplate[0].Id;
                //通过recordId查询Register的创建人
                List<Course_Register__c> lstCourse = new List<Course_Register__c>();
                lstCourse = [
                    SELECT Id,CreatedById,CreatedBy.Email,CreatedBy.IsActive,Status__c,Customer__c,
                    Customer__r.Name,Customer__r.AccountNumber,Billing_Address__c,Billing_Address__r.Final_Address__c,
                    Course_Arrangement__c,Training_Course__r.Course_Name__c,Training_Course_Name__c,
                    Course_Arrangement__r.Training_Location__r.Final_Address__c,
                    Course_Arrangement__r.Start_Time__c,
                    Training_Course__c,Course_Arrangement__r.Course_Date__c,Course_Arrangement__r.End_Time__c,
                    Course_Arrangement__r.Survery__c,Course_Arrangement__r.Training_Location__r.name,
                    Course_Arrangement__r.Training_Location__c,
                    Shiping_Address__c,
                    Billing_Address__r.Acknowledgement_Customer__c,
                    Billing_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Billing_Address__r.Acknowledgement_Customer__r.Salutation),
                    Shiping_Address__r.Acknowledgement_Customer__c,
                    Shiping_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Shiping_Address__r.Acknowledgement_Customer__r.Salutation),
                    Trainee__c
                    FROM Course_Register__c
                    WHERE CreatedBy.IsActive = TRUE AND Id IN :lstRecordId
                ];
                List<String> lstLocationIds = new List<String>();
                for(Course_Register__c objCourse : lstCourse){
                    lstLocationIds.add(objCourse.Course_Arrangement__r.Training_Location__c);
                }
                //查询附件信息
                List<ContentDocumentLink> lstContentDocumentLink = [SELECT ContentDocumentId ,
                Id,LinkedEntityId FROM ContentDocumentLink WHERE LinkedEntityId IN :lstLocationIds];
                List<Id> lstLinkId = new List<Id>();
                Map<String,Set<String>> mapLicationId2DocumentIds = new Map<String,Set<String>>();
                for(ContentDocumentLink objContentDocumentLink :lstContentDocumentLink){
                    lstLinkId.add(objContentDocumentLink.ContentDocumentId);
                    Set<String> setDocumentLinkIds = mapLicationId2DocumentIds.containsKey(objContentDocumentLink.LinkedEntityId) ?
                    mapLicationId2DocumentIds.get(objContentDocumentLink.LinkedEntityId) :  new Set<String>();
                    setDocumentLinkIds.add(objContentDocumentLink.ContentDocumentId);
                    mapLicationId2DocumentIds.put(objContentDocumentLink.LinkedEntityId, setDocumentLinkIds);

                }

                system.debug('查询到文件信息----'+lstContentDocumentLink);

                List<ContentVersion> documents = [
                    SELECT Id, Title, FileExtension, VersionData, isLatest, ContentDocumentId,ContentSize
                    FROM ContentVersion
                    WHERE  ContentDocumentId IN :lstLinkId
                ];
                Map<String,ContentVersion> mapDocumentId2Content = new Map<String,ContentVersion>();
                for(ContentVersion objVersion : documents){
                    mapDocumentId2Content.put(objVersion.ContentDocumentId , objVersion);
                }


                //通过recordId查询register Item
                List<Course_Register_Item__c>  lstRegister = [
                SELECT c.Course_Register__c, c.email__c, c.Id, c.Name,Trainee__c FROM Course_Register_Item__c c
                WHERE  Course_Register__c IN : lstRecordId
                ];
                Map<String,List<Course_Register_Item__c>> mapRegisterId2Item = new Map<String,List<Course_Register_Item__c>>();
                for(Course_Register_Item__c objItem : lstRegister){
                    List<Course_Register_Item__c> lstRegisterItem = mapRegisterId2Item.containsKey(objItem.Course_Register__c) ?
                    mapRegisterId2Item.get(objItem.Course_Register__c) : new List<Course_Register_Item__c>();
                    lstRegisterItem.add(objItem);
                    mapRegisterId2Item.put(objItem.Course_Register__c,lstRegisterItem);
                }
                List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
                List<Messaging.SingleEmailMessage> lstemails = new List<Messaging.SingleEmailMessage>();
                for(Course_Register__c objCourse : lstCourse){
                List<String> lstReceiptEmail = new List<String>();
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    //邮件内容设定
                    email.setSubject(lstEmailTemplate[0].Subject);
                    String strBody = lstEmailTemplate[0].HtmlValue;
                    strBody = strBody.replace('{Customer_Name__c}', objCourse.Training_Course_Name__c);
                    strBody = strBody.replace('{Course_Location_Info__c}', objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c == null ? '' : objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c);
                    String courseTime = String.valueOf(objCourse.Course_Arrangement__r.Course_Date__c) +' '
                    + String.valueOf( objCourse.Course_Arrangement__r.Start_Time__c).subString(0,8) +'-'+
                     String.valueOf(objCourse.Course_Arrangement__r.End_Time__c).subString(0,8);
                    strBody = strBody.replace('{Course_Time__c}', courseTime);
                    strBody = strBody.replace('{Trainee__c}', objCourse.Trainee__c);
                    strBody = strBody.replace('{days}', String.valueOf( (Integer)(Date.today().daysBetween(objCourse.Course_Arrangement__r.Course_Date__c) ) ) );
                    Set<String> setDontentDocumentId = mapLicationId2DocumentIds.get(objCourse.Course_Arrangement__r.Training_Location__c);
                    if(setDontentDocumentId != null && setDontentDocumentId.size()>0){
                        Messaging.EmailFileAttachment[] eAttachments = new Messaging.EmailFileAttachment[]{};
                        for (String attId: setDontentDocumentId) {
                            ContentVersion att = mapDocumentId2Content.get(attId);
                            if(att != null){
                                Messaging.EmailFileAttachment eAttachment = new Messaging.EmailFileAttachment();
                                eAttachment.setFileName(att.Title + '.' + att.FileExtension);
                                eAttachment.setBody(att.VersionData);
                                eAttachments.add(eAttachment);
                            }

                        }
                        email.setFileAttachments(eAttachments);
                    }

                    List<Course_Register_Item__c> lstRegisterItem = mapRegisterId2Item.get(objCourse.Id);
                    if(lstRegisterItem != null && lstRegisterItem.size()>0){
                        for(Course_Register_Item__c objItem : lstRegisterItem){
                            lstReceiptEmail.add(objItem.Email__c);
                            strBody = strBody.replace('{Family Name}', objItem.Trainee__c);
                        }

                        if(lstReceiptEmail == null || lstReceiptEmail.size() == 0){
                            break ;
                        }
                        email.setHtmlBody(strBody);
                        email.setToAddresses(lstReceiptEmail);
                        // email.setCcAddresses(new List<String>{'<EMAIL>'});
                        //设置发件人

                        if(listAddresses != null  && listAddresses.size() > 0){
                            email.setOrgWideEmailAddressId(listAddresses[0].Id);
                        }
                        email.setSaveAsActivity(false);

                        lstemails.add(email);
                    }
                }
                //发送邮件
                if (!System.Test.isRunningTest()){
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(lstemails);
                }


            }
        } catch (Exception e) {
            system.debug('报错信息－－－＞'+e.getMessage()+'报错行数－－－－'+e.getLineNumber());
        }
    }

    @future (callout=true)
    public static void pushRegisterToEBS(String recordId){
        //toDo 调用同步
        CCM_TraningOrderCallout.pushRequestInfo(recordId);
    }
}