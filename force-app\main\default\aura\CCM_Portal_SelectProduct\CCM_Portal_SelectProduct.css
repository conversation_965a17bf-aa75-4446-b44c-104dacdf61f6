.THIS .slds-form-element__label:empty{
    display: none;
}
.THIS .slds-icon_container{
    cursor: pointer;
}
.THIS .buttonWidth{
    width: 80px;
}
.THIS p.slds-p-horizontal_small{
    position: relative;
    z-index: 1;
}
.THIS .ccm_paddingLeft {
    padding-left: 6%;
}
.THIS .explosive{
    color: rgb(10, 50, 198);
    text-decoration: underline;
}
.THIS .BtnDisabl{
    background-color: #cecece !important;
    border: 0px solid #cecece !important;
}
.THIS .h27{
    height: 27px
}
.THIS .ml17{
    margin-left: 17px;
}
.THIS .slds-modal__container{
    max-width: initial;
    width: 80%;
    position:relative;
}
/* .THIS .slds-modal__header{
    padding: 0;
    border: 0;
} */
.THIS .slds-modal__content{
    position:relative;
}
.THIS .slds-spinner_container{
    margin: 3rem 0;
}
.THIS .slds-form-element__label:empty{
    display: none;
}
.THIS .slds-card__footer{
    border: 0;
}
.THIS .forceContentFileCard{
    width: 100%!important;
}
.THIS tr.highLight{
    background-color: #90c41f;
}
.THIS .backToTop{
    display: inline-block;
    line-height: 2.5;
    text-decoration: underline;
}
.THIS .total-wrap {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}
.THIS .total-value-wrap {
    min-width: 60px;
    padding-left: 15px;
}
.THIS .smallWidth {
    width: 80px !important;
}
.THIS .mediumWidth {
    width: 120px !important;
}
.THIS .largeWidth {
    width: 160px !important;
}
.THIS .maxLargeWidth {
    width: 180px !important;
}
.THIS .qty-wrap {
    display: flex;
    align-items: center;
}
.THIS .qty-wrap .slds-form-element {
    width: 88px;
}
.THIS .qty-wrap .sub, .THIS .qty-wrap .add {
    margin-top: 15px;
    width: 42px;
}
.THIS .cCCM_Community_LookUp .slds-form-element__label {
    display: none;
}
.THIS .content-wrap {
    padding: 10px;
    max-height: 500px;
}
.THIS .promotion-item-wrap {
    width: 100%;
    display: flex;
    justify-content: space-between;
}
.THIS .w100i{
    width: 100px !important;
}
.THIS .w220i {
    width: 220px !important;
}
.THIS .ml3{
    margin-left: 3px;
}
.THIS .apply-btn-wrap {
    width: 36%;
    height: auto;
    padding: 10px;
    display: flex;
    align-items: center;
    background-color: #DEDEDE;
}
.THIS .h32{
    height: 32px;
}
.THIS .apply-btn-wrap .apply-btn {
    width: 60px;
    height: 40px;
    margin-right: 10px;
}
.THIS .mrl20{
    margin-left: 20px;
}
.THIS .apply-btn-wrap .promotion-type {
    width: calc(100% - 70px);
    font-size: 16px;
    font-weight: 500;
}
.THIS .detail-wrap {
    width: 42%;
    height: auto;
    padding: 10px;
    background-color: #ECEEF1;
}
.THIS .detail-wrap .promotion-label {
    font-size: 14px;
    font-weight: 500;
}
.THIS .detail-wrap .promotion-value {
    font-size: 14px;
    font-weight: 400 !important;

}
.THIS .code-wrap {
    width: 20%;
    height: auto;
    padding: 10px;
    background-color: #ECEEF1;
}
.THIS .code-wrap .promotion-label {
    font-size: 14px;
    font-weight: 500;
}
.THIS .content-wrap {
    max-height: 350px;
    overflow-x: hidden;
    overflow-y: auto;
}
.THIS .content-wrap:last-child {
    margin-bottom: 0 !important;
}
.THIS .content-wrap .promotion-item-wrap {
    margin-bottom: 8px;
}
.THIS .content-wrap .promotion-item-wrap:last-child {
    margin-bottom: 0 !important;
}
.THIS .footer-wrap {
    display: flex;
    justify-content: center;
}
.THIS .promotion-detail-wrap {
    padding: 10px;
    background-color: #ECEEF1;
}
.THIS .goods-list-wrap {
    padding: 10px;
}
.THIS tr td {
    height: 41px !important;
}
.THIS .clickedProduct{
    background-color: rgb(226, 226, 226) !important;
}
.THIS .pd10_20{
    padding:10px 20px
}
.THIS .table_st{
    display: inline-block;
    width: 160px;
    text-align: center;
}
/* .THIS .tool-table-wrap {
    width: 100%;
    height: auto;
    overflow-y: auto;
} */
.THIS .table-wrap {
    width: 100%;
    height: 400px;
    max-height: 400px;
    position: relative;
    z-index: 2;
    overflow-x: auto; 
    overflow-y: auto;
    /* padding-bottom:300px; */
}
.THIS .thead-wrap {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #eee !important;
}
.THIS .header-title {
    width: 100% !important;
    height: auto;
    white-space: break-spaces !important;
}
.THIS .thead-wrap > tr {
    width: 100%;
    word-wrap: break-word;
    word-break: normal; 
}
.THIS .thead-wrap th {
    background-color: #eee !important;
}
.THIS .thead-wrap th:hover {
    background-color: #ffffff !important;
}
.THIS .productTable > tbody > tr {
    background-color: #ffffff !important;
}
.THIS .productTable > tbody > tr > td {
    background-color: #ffffff !important;
    text-align: center !important;
}
.THIS .productTable > tbody > tr:hover{
    background-color: #eee !important;
}
.THIS .tool-tabel > tbody > tr {
    background-color: #eee !important;
    }
.THIS .tool-tabel > tbody > tr > td {
    background-color: #eee !important;
text-align: center !important;
}
.THIS .tool-tabel > tbody > tr:hover{
    background-color: #ffffff;
}
.THIS th:nth-last-child(1),.THIS td:nth-last-child(1) {
    position: sticky;
    right: 0;
    z-index: 100;
}
/* .THIS .lookup-parent-wrap {
    height: 54px;
    position: relative;
} */
/* .THIS .lookup-wrap {
    height: 54px;
    position: relative;
} */
.THIS .footer-wrap {
    display: flex;
    justify-content: center;
}
.THIS .previous-btn {
    margin: 0 1.5rem;
}
.THIS .icon-size svg{
    width: 16px !important;
    height: 16px !important;
}
.THIS .show-flag {
    display: table-row;
}
.THIS .hidden-flag {
    display: none;
}
.THIS .icon-position-wrap {
    position: relative;
}
.THIS .icon-position-tool {
    position: absolute;
    top: -3px;
    left: 36px;
    opacity: 0;
}
.THIS .icon-position-kit {
    position: absolute;
    top: -3px;
    left: 23px;
    opacity: 0;
}
.THIS .w60{
    width: 60px !important;
}
.THIS .bg_promotion_th > td{
    background-color: #e7f6ff !important;
}
.THIS .bg_promotion_of > td{
    background-color: #eeeeee !important;
}
.THIS .field-required {
    margin-left:0px;
}
.THIS .field-required .slds-form-element__control::before{
    content:"*";
    display: inline-block;
    transform: scale(1.5);
    position:relative;
    color:rgb(194, 57, 52);
    position: absolute;
    left: -.5rem;
    top: 50%;
    transform: translateY(-50%);
}
.THIS .field-error.input{
    border: 2px solid rgb(194, 57, 52);
}
.THIS .field-error input, .THIS .field-error .slds-dueling-list__options{
    border:2px solid rgb(194, 57, 52);
}
.THIS .required-wrap {
    position: relative;
}
.THIS .error-text{
    color: rgb(194, 57, 52);
    display: flex;
    justify-content: end;
    position: absolute;
    bottom: -18px;
    right: 0px;
}
.THIS .search-wrap {
    margin-bottom: 18px;
}
.THIS .tool-title-center {
    display: flex;
    justify-content: center;
}
.THIS .tbody-wrap { 
    transform: unset !important;
}
.THIS .index-wrap > td { 
    z-index: unset !important;
}
.THIS .slds-button_outline-brand {
    color: #90c41f !important;
    border-color: #90c41f !important;
}
.THIS .tool-title-center {
    display: flex;
    justify-content: center;
}
.THIS .tbody-wrap { 
    transform: unset !important;
}
.THIS .pallet-table-wrap {
    width: 100%;
    height: auto;
}
.THIS .table-header {
    width: 30%;
    background-color: #f3f3f3 !important;
    color: #444;
    padding: 0.25rem 0.5rem;
    font-weight: 700;
    line-height: normal;
}

/* 固定前4列：Action, No., Product Description, Model # */
.THIS .productTable:not(.tool-tabel) th:nth-child(1),
.THIS .productTable:not(.tool-tabel) td:nth-child(1) {
    position: sticky;
    left: 0;
    z-index: 99;
}

.THIS .productTable:not(.tool-tabel) th:nth-child(2),
.THIS .productTable:not(.tool-tabel) td:nth-child(2) {
    position: sticky;
    left: 40px; /* Action列的宽度 */
    z-index: 99;
}

.THIS .productTable:not(.tool-tabel) th:nth-child(3),
.THIS .productTable:not(.tool-tabel) td:nth-child(3) {
    position: sticky;
    left: 80px; /* Action(40px) + No.(40px) */
    z-index: 99;
}

.THIS .productTable:not(.tool-tabel) th:nth-child(4),
.THIS .productTable:not(.tool-tabel) td:nth-child(4) {
    position: sticky;
    left: 320px; /* Action(40px) + No.(40px) + Product Description(240px) */
    z-index: 99;
}

/* 确保固定列的内容完全覆盖 */
.THIS .productTable th:nth-child(1) *,
.THIS .productTable td:nth-child(1) *,
.THIS .productTable th:nth-child(2) *,
.THIS .productTable td:nth-child(2) *,
.THIS .productTable th:nth-child(3) *,
.THIS .productTable td:nth-child(3) *,
.THIS .productTable th:nth-child(4) *,
.THIS .productTable td:nth-child(4) * {
    position: relative;
    z-index: 1;
}

/* 强制固定列的背景色和层级 */
.THIS .productTable tbody tr td:nth-child(1),
.THIS .productTable tbody tr td:nth-child(2),
.THIS .productTable tbody tr td:nth-child(3),
.THIS .productTable tbody tr td:nth-child(4) {
/*    background-color: inherit !important;*/
    position: sticky !important;
    z-index: 99 !important;
}

/* 确保表格容器的层级设置 */
.THIS .table-wrap {
    position: relative;
    z-index: 1;
}

/* 修复可能的层级冲突 */
.THIS .productTable {
    position: relative;
    z-index: 2;
}

/* 重新设置固定列的z-index，覆盖上面的unset */
.THIS .index-wrap > td:nth-child(1),
.THIS .index-wrap > td:nth-child(2),
.THIS .index-wrap > td:nth-child(3),
.THIS .index-wrap > td:nth-child(4) {
    z-index: 99 !important;
}

/* 确保表头固定列的z-index高于数据行 */
.THIS .thead-wrap th:nth-child(1),
.THIS .thead-wrap th:nth-child(2),
.THIS .thead-wrap th:nth-child(3),
.THIS .thead-wrap th:nth-child(4) {
    z-index: 101 !important;
}

/* 确保固定列不受tbody-wrap的transform影响 */
.THIS .tbody-wrap .index-wrap > td:nth-child(1),
.THIS .tbody-wrap .index-wrap > td:nth-child(2),
.THIS .tbody-wrap .index-wrap > td:nth-child(3),
.THIS .tbody-wrap .index-wrap > td:nth-child(4) {
    position: sticky !important;
    z-index: 99 !important;
    transform: none !important;
}

/* 强制设置固定列的位置和背景 */
.THIS .productTable:not(.tool-tabel) .index-wrap > td:nth-child(1) {
    left: 0 !important;
}

.THIS .productTable:not(.tool-tabel) .index-wrap > td:nth-child(2) {
    left: 40px !important;
}

.THIS .productTable:not(.tool-tabel) .index-wrap > td:nth-child(3) {
    left: 80px !important;
}

.THIS .productTable:not(.tool-tabel) .index-wrap > td:nth-child(4) {
    left: 320px !important;
}