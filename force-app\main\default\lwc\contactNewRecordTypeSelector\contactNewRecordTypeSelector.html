<template>
    <template if:true={showRecordTypeSelection}>
        <div class="slds-brand-band slds-brand-band_cover slds-brand-band_narrow_view_two slds-template_default forceBrandBand" style="align-items: center;">
            <div class="modal-header slds-modal__header" style="width:100%">
                <h2 class="slds-text-heading--medium slds-hyphenate">New Contact</h2>
            </div>

            <div class="modal-body scrollable slds-modal__content slds-p-top--medium" style="padding-bottom:1rem; width:100%">
                <div class="select-label">
                    <span style="font-size: 15px;">Select a record type</span>
                </div>
                <!-- <lightning-layout class="radio-center">
                    <lightning-radio-group name="radioGroup"
                            label="Radio Group"
                            options={recordTypeOptions}
                            value={selectedRecordTypeId}
                            type="radio"
                            variant="label-hidden"
                            onchange={handleRecordTypeSelected}></lightning-radio-group>
                </lightning-layout> -->

                <lightning-layout class="radio-center">
                    <template for:each={recordTypeOptions} for:item="typeOption">
                        <div key={typeOption.label}>
                            <input type="radio" id={typeOption.label} name="recordTypeGroups" value={typeOption.value} class="radio-selector" onclick={handleRecordTypeSelected}>
                            <label for={typeOption.label}>{typeOption.label}</label>
                        </div>
                    </template>
                    
                </lightning-layout>
            </div>

            <div class="modal-footer slds-modal__footer slds-p-around--medium" style="width:100%">
                <lightning-button 
                    variant="neutral" 
                    label="Cancel" 
                    onclick={handleCancel} style="padding-right:5px">
                </lightning-button>
                <lightning-button 
                    variant="brand" 
                    label="Next" 
                    onclick={handleNext}
                    disabled={isNextDisabled}>
                </lightning-button>
            </div>
        </div>
    </template>
</template>