/**
 * <AUTHOR>
 * @date 2025-07-30
 * @description Sales target report util - link invoice to sales target detail
 */
public without sharing class CCM_SalesTargetReportUtil {
    public static void linkInvoiceToSalesTargetDetail(List<String> invoiceIds) {
        List<Invoice__c> invoices = [SELECT Invoice_Date__c, Order__r.Sales_Rep__r.Name, Order__r.Sales_Rep__c FROM Invoice__c WHERE Id IN :invoiceIds AND Order__r.RecordType.DeveloperName NOT IN ('Warranty_Claim', 'Warranty_Order') AND Sales_Target_Detail__c = null];
        if(!invoices.isEmpty()) {
            Date minDate = null;
            Date maxDate = null;
            Set<String> salesPersonNames = new Set<String>();
            for(Invoice__c invoice : invoices) {
                if(minDate == null || invoice.Invoice_Date__c < minDate) {
                    minDate = invoice.Invoice_Date__c;
                }
                if(maxDate == null || invoice.Invoice_Date__c > maxDate) {
                    maxDate = invoice.Invoice_Date__c;
                }
                if(String.isNotBlank(invoice.Order__r.Sales_Rep__r.Name)) {
                    salesPersonNames.add(invoice.Order__r.Sales_Rep__r.Name);
                }
            }
            List<Sales_Target_Detail_Report__c> salesTargetDetails = [SELECT Sales_Person_Name__c, Quota_Allocation__r.Start_Date__c, Quota_Allocation__r.End_Date__c
                                                                   FROM Sales_Target_Detail_Report__c WHERE Sales_Person_Name__c IN :salesPersonNames
                                                                   AND ((Quota_Allocation__r.Start_Date__c <= :minDate AND Quota_Allocation__r.End_Date__c >= :minDate)
                                                                   OR (Quota_Allocation__r.Start_Date__c <= :maxDate AND Quota_Allocation__r.End_Date__c >= :maxDate))];
            if(!salesTargetDetails.isEmpty()) {
                List<Invoice__c> invoiceToUpdateList = new List<Invoice__c>();
                List<Sales_Target_Detail_Report__c> salesTargetDetailToUpdateList = new List<Sales_Target_Detail_Report__c>();
                for(Invoice__c invoice : invoices) {
                    for(Sales_Target_Detail_Report__c salesTargetDetail : salesTargetDetails) {
                        if(invoice.Order__r.Sales_Rep__r.Name == salesTargetDetail.Sales_Person_Name__c
                            && invoice.Invoice_Date__c >= salesTargetDetail.Quota_Allocation__r.Start_Date__c
                            && invoice.Invoice_Date__c <= salesTargetDetail.Quota_Allocation__r.End_Date__c) {
                                invoice.Sales_Target_Detail__c = salesTargetDetail.Id;
                                salesTargetDetail.OwnerId = invoice.Order__r.Sales_Rep__c;
                                invoiceToUpdateList.add(invoice);
                                salesTargetDetailToUpdateList.add(salesTargetDetail);
                                break;
                        }
                    }
                }
                if(!salesTargetDetailToUpdateList.isEmpty()) {
                    List<Log__c> logs = new List<Log__c>();
                    Integer i = 0;
                    for(Database.SaveResult result : Database.update(salesTargetDetailToUpdateList, false)) {
                        if(!result.isSuccess()) {
                            Log__c log = new Log__c();
                            log.Name = 'Update Sales Target Detail Report Owner Error';
                            log.RecordId__c = salesTargetDetailToUpdateList[i].Id;
                            log.ApexName__c = 'CCM_SalesTargetReportUtil';
                            log.Error_Message__c = result.getErrors()[0].getMessage();
                            logs.add(log);
                        }
                        i++;
                    }
                    if(!logs.isEmpty()) {
                        insert logs;
                    }
                }
                if(!invoiceToUpdateList.isEmpty()) {
                    List<Log__c> logs = new List<Log__c>();
                    Integer i = 0;
                    for(Database.SaveResult result : Database.update(invoiceToUpdateList, false)) {
                        if(!result.isSuccess()) {
                            Log__c log = new Log__c();
                            log.Name = 'Link Invoice to Sales Target Detail Error';
                            log.RecordId__c = invoiceToUpdateList[i].Id;
                            log.ApexName__c = 'CCM_SalesTargetReportUtil';
                            log.Error_Message__c = result.getErrors()[0].getMessage();
                            logs.add(log);
                        }
                        i++;
                    }
                    if(!logs.isEmpty()) {
                        insert logs;
                    }
                }
            }
        }

    }
}