/**************************************************************************************************
 * Name: WarrantyClaimBasicHandler
 * Object: Lead
 * Purpose: Controller class for Warranty Claim
 * Author:Aria W Zhong
 * Create Date: 2023-09-27
 * Modify History:
 **************************************************************************************************/
public without sharing class WarrantyClaimCreateController{
    /** 保存在for a free时新增sn的方法 */
    @AuraEnabled
    public static String saveNewSN(String calimId, String serialNumber){
        return WarrantyClaimBasicHandler.saveNewSN(calimId,serialNumber);
    }
    /** 获取claim information,claim payment information*/
    @AuraEnabled
    public static String queryClaimInfoMation(String accountId){
        return WarrantyClaimSaveHandler.queryClaimInfoMation(accountId);
    }
    /** 判断是否是Portal端 */
    @AuraEnabled
    public static Boolean IsPortal(){
        return WarrantyClaimUtil.IsPortal();
    }
    /**非Portal端需要选择Customer */
    @AuraEnabled
    public static String queryCustomer(String name, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimUtil.queryCustomer(name, pageNumber, allPageSize);
    }
    /**选择存在的warranty claim */
    @AuraEnabled
    public static String queryWarrantyClaim(String name, Integer pageNumber, Integer allPageSize, String ConsumerId){
        return WarrantyClaimUtil.queryWarrantyClaim(name, pageNumber, allPageSize, ConsumerId);
    }
    /**保存发票 */
    @AuraEnabled
    public static String saveInvoice(String idStr, String receiptType, String receiptName, String receiptBody, String warrantyItemId){
        return WarrantyClaimBasicHandler.saveInvoice(idStr, receiptType, receiptName, receiptBody, warrantyItemId);
    }
    /**basic information check */
    @AuraEnabled
    public static String checkBasicInformation(String emailAddress, String dropDate, String repairDate, String brand, String serialNumber, String modelnumber, String accId){
        return WarrantyClaimBasicHandler.checkBasicInformation(emailAddress, dropDate, repairDate, brand, serialNumber, modelnumber, accId);
    }
    /**查询发票 */
    @AuraEnabled
    public static String queryInvoice(String modelNumber, String serialNumber, String accId, String email){
        return WarrantyClaimBasicHandler.queryInvoice(modelNumber, serialNumber, accId, email);
    }
    /**查询orderModel */
    @AuraEnabled
    public static String queryModelNumber(String accId, String orderModel, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimBasicHandler.queryModelNumber(accId, orderModel, pageNumber, allPageSize);
    }
    /**查询orderModel不关联Customer */
    @AuraEnabled
    public static String queryModelNumberWithoutCustomer(String orderModel, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimBasicHandler.queryModelNumberWithoutCustomer(orderModel, pageNumber, allPageSize);
    }
    /**获取customer信息:判断是 dealer还是distributor,获取labor Price */
    @AuraEnabled
    public static String getCustomerInformation(String accId){
        return WarrantyClaimUtil.getCustomerInformation(accId);
    }
    /**获取picklist value */
    @AuraEnabled
    public static String getPicklistOption(String objectAPI, String fieldAPI, String fifterString){
        return WarrantyClaimUtil.getPicklistOption(objectAPI, fieldAPI, fifterString);
    }
    /**查询对应产品下的parts */
    @AuraEnabled
    public static String queryParts(String productId, Integer pageNumber, Integer allPageSize, String customerId, String name){
        return WarrantyClaimBasicHandler.queryParts(productId, pageNumber, allPageSize, customerId, name, null);
    }
    /** 获取 对应地址 */
    @AuraEnabled
    public static String queryAddress(String AccountId, String type, String name, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimBasicHandler.queryAddress(AccountId, type, name, pageNumber, allPageSize);
    }
    /**保存数据 */
    @AuraEnabled
    public static String saveClaim(String saveStr, String claimId, List<String> fileIds){
        return WarrantyClaimSaveHandler.saveClaim(saveStr, claimId, fileIds);
    }
    /**提交数据 */
    @AuraEnabled
    public static String submitClaim(String saveStr, String claimId, List<String> fileIds){
        return WarrantyClaimSaveHandler.submitClaim(saveStr, claimId, fileIds);
    }
    /**查询详情 */
    @AuraEnabled
    public static String queryClaimDetail(String claimId){
        return WarrantyClaimSaveHandler.queryClaimDetail(claimId);
    }
    /**删除数据 */
    @AuraEnabled
    public static String deleteClaim(String claimId){
        return WarrantyClaimSaveHandler.deleteClaim(claimId);
    }
    /**list查询 */
    @AuraEnabled
    public static String queryClaimList(String accountId, Integer pageNumber, Integer allPageSize, String modelNumber, String status, String invoiceStatus, String serialNumber, String name, String companyName, String claimReferenceNumber){
        return WarrantyClaimSaveHandler.queryClaimList(accountId, pageNumber, allPageSize, modelNumber, status, invoiceStatus, serialNumber, name, companyName, claimReferenceNumber);
    }
    /**通过SN获取Project Code */
    @AuraEnabled
    public static String getProjectInfo(String snParam){
        List<ProjectRow> projectInfoList = New List<ProjectRow>();
        List<Project_SN__c> projectSnList = New List<Project_SN__C>();
        Set<String> muliCheckSet = New Set<String>();

        String modelCodeParam;
        Integer snCodeParam;
        if(String.isNotBlank(snParam)) {
            if (snParam.length() != 15){
                return null;
            } else{
                modelCodeParam = snParam.Left(5);
                snCodeParam = Integer.ValueOf(snParam.Mid(5, 9));
            }
        }

        projectSnList = [Select id, Start_SN__c, End_SN__c, Project__c, Project__r.Name, Project__r.Product__r.Name, Project__r.Project_Code__c, Project__r.Recall_Reason__c, Project__r.Solution__c
                         From Project_SN__c
                         Where SN_Model_Code__c = :modelCodeParam And SN_Start_Serial_Numbers__c <= :snCodeParam And SN_End_Serial_Numbers__c >= :snCodeParam];

        for (Project_SN__c item : projectSnList){
            ProjectRow newRow = New ProjectRow();
            newRow.projectId = item.ProJect__c;
            newRow.projectName = item.Project__r.Name;
            newRow.projectCode = item.Project__r.Project_Code__c;
            newRow.solution = item.Project__r.Solution__c;
            newRow.product = item.Project__r.Product__r.Name;
            newRow.reason = item.Project__r.Recall_Reason__c;
            newRow.projectSnRange = item.Start_SN__c + ' ~ ' + item.End_SN__c;
            newRow.isSelected = true;
            projectInfoList.add(newRow);
        }
        if (projectInfoList.size() == 0)
            return null;
        return JSON.serialize(projectInfoList);
    }
    /**验证Project Code是否存在 */
    @AuraEnabled
    public static String checkProjectExist(String projectCodeParam){
        List<Project__c> projectList = New List<Project__C>();

        projectList = [Select id
                       From Project__c
                       Where Project_Code__c = :projectCodeParam];

        if (projectList.size() == 0)
            return null;
        return JSON.serialize(projectList);
    }
    //add haibo
    /** 获取发票地址 */
    @AuraEnabled
    public static String getAWSSignedURL(String url){
        String signedUrl = '';
        if (url != null){
            String[] fileName = url.split('/');
            system.debug(fileName);
            signedUrl = CCM_WarrantyDetailReceiptPageCtl.getAWSSignedURL(fileName[3], Label.Warranty_AWS_S3_BucketName, Label.Warranty_AWS_S3_Access_Key, Label.Warranty_AWS_S3_Secret_Key, Label.Warranty_AWS_S3_Region);
        }
        return signedUrl;
    }
    //add haibo
    /** 获取附件插入ID */
    @AuraEnabled
    public static String ReceiptJunkPreCreate(){
        //记录上传错误的file
        Receipt_Junk_Data__c rjd = New Receipt_Junk_Data__c();
        INSERT rjd;

        return rjd.Id;
    }
    //add haibo
    /** 获取发票第三方Url */
    @AuraEnabled
    public static AuraResponseEntity UploadReceiptToAws(String idStr, String receiptType, String receiptName, String receiptId){
        return CCM_ProductRegistration.UploadReceiptToAws(idStr, receiptType, receiptName, receiptId);
        // //返回值
        // AuraResponseEntity result = new AuraResponseEntity();
        // //记录上传错误的file
        // Receipt_Junk_Data__c rjd = New Receipt_Junk_Data__c();

        // ContentVersion file = [SELECT Title, VersionData, ContentDocumentId
        //                        FROM ContentVersion
        //                        WHERE id = :receiptId];
        // ContentDocument docDelete = [SELECT id
        //                              FROM ContentDocument
        //                              WHERE id = :file.ContentDocumentId];
        // rjd = [select id
        //        from Receipt_Junk_Data__c
        //        Where Id = :idStr];
        // receiptName = receiptName.replaceAll('\\s+', '_');
        // receiptName = receiptName.replaceAll('\\(', '');
        // receiptName = receiptName.replaceAll('\\)', '');
        // receiptName = receiptName.replaceAll('/', '');

        // //callout AWS
        // HttpRequest request = new HttpRequest();
        // //方法设置PUT
        // request.setMethod('PUT');
        // //内容设置
        // request.setBodyAsBlob(file.VersionData);
        // request.setHeader('Content-Encoding', 'base64');
        // if (receiptType == 'pdf')
        //     receiptType = 'application/pdf';
        // request.setHeader('Content-Type', receiptType);
        // request.setHeader('Connection', 'keep-alive');
        // //callout AWS认证设置 Named Credential
        // request.setEndpoint('callout:AWS_S3/' + idStr + receiptName);

        // try{
        //     //callout执行
        //     Http http = new Http();
        //     HttpResponse res = http.send(request);
        //     System.debug('>>>> The response');
        //     System.debug(res);
        //     System.debug('>>>> Status code');
        //     System.debug(res.getStatusCode());
        //     //callout status跟踪
        //     if (res.getStatusCode() == 200 && res.getStatus() == 'OK'){
        //         //成功的情况将link更新到跟踪数据
        //         rjd.Receipt_Junk_Data_Link__c = Label.Warranty_AWS_S3_URL + idStr + receiptName;
        //         UPDATE rjd;
        //         result.message = rjd.Receipt_Junk_Data_Link__c;
        //     } else{
        //         //失败的情况返回失败信息
        //         DELETE rjd;
        //         result.code = 201;
        //         result.message = Label.Warranty_AWS_S3_File_Upload_Error;
        //     }
        //     DELETE docDelete;
        // } catch (System.CalloutException e){
        //     system.debug('AWS Service Callout Exception: ' + e.getMessage());
        //     DELETE rjd;
        //     result.code = 201;
        //     result.message = e.getMessage();
        // }

        // return result;
    }

    @AuraEnabled
    public static String getProductInfo(String productId, String partIds) {
        Map<String, Object> resultMap = new Map<String, Object>();
        String customerInfo = getCustomerInformation(null);
        Map<String, Object> customerInfoMap = (Map<String, Object>)JSON.deserializeUntyped(customerInfo);
        String customerId = (String)customerInfoMap.get('AccountId');
        String orderModel = null;
        for(Product2 product : [SELECT Order_Model__c FROM Product2 WHERE Id = :productId]) {
            orderModel = product.Order_Model__c;
        }
        String productInfo = CCM_ProductRegistration.queryModelNumber(customerId, orderModel, 1, 1);
        Map<String, Object> productInfoMap = (Map<String, Object>)JSON.deserializeUntyped(productInfo);
        List<WarrantyClaimBasicHandler.ProductEntity> productList = (List<WarrantyClaimBasicHandler.ProductEntity>)JSON.deserialize(JSON.serialize(productInfoMap.get('List')), List<WarrantyClaimBasicHandler.ProductEntity>.class);
        if(productList != null && !productList.isEmpty()) {
            Map<String, Object> productInfoResult = new Map<String, Object>();
            productInfoResult.put('Name', productList[0].modelNumber);
            productInfoResult.put('productId', productList[0].productId);
            productInfoResult.put('productName', productList[0].productName);
            productInfoResult.put('unitPrice', productList[0].unitPrice);
            productInfoResult.put('noCalculate', productList[0].noCalculate);
            productInfoResult.put('modelNumber', productList[0].modelNumber);
            resultMap.put('productInfo', productInfoResult);
        }

        if(String.isNotBlank(partIds)) {
            List<String> partIdList = (List<String>)JSON.deserialize(partIds, List<String>.class);
            String partInfo = CCM_ProductRegistration.queryParts(productId, 1, 100, customerId, null, partIdList);
            Map<String, Object> partInfoMap = (Map<String, Object>)JSON.deserializeUntyped(partInfo);
            List<Object> partsList = (List<Object>)partInfoMap.get('List');
            if(!partsList.isEmpty()) {
                resultMap.put('partsInfo', JSON.serialize(partInfoMap.get('List')));    
            }
            
        }
        return JSON.serialize(resultMap);
    }

    @AuraEnabled
    public static String getModelInfoBySN(String serialNumber, String customerId){
        if(String.isNotBlank(serialNumber)) {
            Map<String, Object> resultMap = new Map<String, Object>();
            String model = null;
            List<Warranty_Item__c> items = [SELECT Product__r.Order_Model__c FROM Warranty_Item__c WHERE Serial_Number__c = :serialNumber];
            if(!items.isEmpty()) {
                model = items[0].Product__r.Order_Model__c;
            }
            else {
                String code = serialNumber.substring(1, 5);
                for(Warranty_Rules__c rule : [SELECT Product_Model__c FROM Warranty_Rules__c WHERE RecordType.developerName = 'Model_Code' AND Product_Model__c != null AND Code_in_Serial__c != null
                                              AND Code_in_Serial__c = :code]) {
                    model = rule.Product_Model__c;
                }
            }

            if(String.isNotBlank(model)) {
                String customerInfo = getCustomerInformation(customerId);
                Map<String, Object> customerInfoMap = (Map<String, Object>)JSON.deserializeUntyped(customerInfo);
                customerId = (String)customerInfoMap.get('AccountId');
                String productInfo = CCM_ProductRegistration.queryModelNumber(customerId, model, 1, 1);
                Map<String, Object> productInfoMap = (Map<String, Object>)JSON.deserializeUntyped(productInfo);
                List<WarrantyClaimBasicHandler.ProductEntity> productList = (List<WarrantyClaimBasicHandler.ProductEntity>)JSON.deserialize(JSON.serialize(productInfoMap.get('List')), List<WarrantyClaimBasicHandler.ProductEntity>.class);
                if(productList != null && !productList.isEmpty()) {
                    Map<String, Object> productInfoResult = new Map<String, Object>();
                    productInfoResult.put('Name', productList[0].modelNumber);
                    productInfoResult.put('productId', productList[0].productId);
                    productInfoResult.put('productName', productList[0].productName);
                    productInfoResult.put('unitPrice', productList[0].unitPrice);
                    productInfoResult.put('noCalculate', productList[0].noCalculate);
                    productInfoResult.put('modelNumber', productList[0].modelNumber);
                    resultMap.put('productInfo', productInfoResult);
                    return JSON.serialize(resultMap);
                }
            }
        }
        return null;
    }

    @AuraEnabled
    public static void deleteReceiptImp(String receiptContentVersionId){
        String receiptFileId = null;
        for(ContentVersion version : [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :receiptContentVersionId]) {
            receiptFileId = version.ContentDocumentId;
        }
        if(String.isNotBlank(receiptFileId)) {
            delete new ContentDocument(Id = receiptFileId);
        }
    }

    @AuraEnabled
    public static String getReceiptWithoutWarranty(String claimId){
        Map<String, Object> receiptInfoMap = new Map<String, Object>();
        List<Warranty_Claim__c> claims = [SELECT Warranty_Claim_Receipt_Link__c, Warranty_Claim_Receipt_Name__c FROM Warranty_Claim__c where Id = :claimId];
        if(!claims.isEmpty() && String.isNotBlank(claims[0].Warranty_Claim_Receipt_Link__c)) {
            receiptInfoMap.put('receiptLink', claims[0].Warranty_Claim_Receipt_Link__c);
            receiptInfoMap.put('receiptName', claims[0].Warranty_Claim_Receipt_Name__c);
        }
        else {
            String receiptName = null;
            String receiptContentVersionId = null;
            String contentDocumentId = null;
            for(ContentDocumentLink link : [SELECT ContentDocument.Title, ContentDocument.FileExtension, ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId = :claimId AND ContentDocument.Description Like '%receipt%']) {
                receiptName = link.ContentDocument.Title + '.' + link.ContentDocument.FileExtension;
                contentDocumentId = link.ContentDocumentId;
            }
            if(String.isNotBlank(contentDocumentId)) {
                for(ContentVersion receiptFile : [SELECT Id FROM ContentVersion WHERE ContentDocumentId = :contentDocumentId]){
                    receiptContentVersionId = receiptFile.Id;
                }
            }
            receiptInfoMap.put('receiptContentVersionId', receiptContentVersionId);
            receiptInfoMap.put('receiptName', receiptName);
        }
        return JSON.serialize(receiptInfoMap);
    }

    @AuraEnabled
    public static String generatePresignedURL(String receiptLink) {
        CCM_AWSS3Uploader uploder = new CCM_AWSS3Uploader();
        String signedUrl = uploder.generatePreSignedURL(receiptLink);
        return signedUrl;
    }

    @AuraEnabled
    public static void deleteImage(String fileId){
        delete [SELECT Id FROM ContentDocument WHERE Id = :fileId];
    }

    // project 行的信息 table 展示内容
    public class ProjectRow{
        public String projectId;
        public String projectName;
        public String projectCode;
        public String solution;
        public String product;
        public String reason;
        public String projectSnRange;
        public Boolean isSelected;
    }
}