<aura:component access="global" implements="forceCommunity:availableForAllPageTypes"
                description="CCM_Community_ResourceCenter">
    <!-- 默认选中 Knowledge Base -->
    <aura:attribute name="selectedTab" type="String" default="KnowledgeBase" />
    <!-- 联系方式 -->
    <!-- <aura:attribute name="egoDealerTel" type="String" default="" />
    <aura:attribute name="egoDealerEmail" type="String" default="" />
    <aura:attribute name="powerToolDealerTel" type="String" default="" />
    <aura:attribute name="powerToolDealerEmail" type="String" default="" />
    <aura:attribute name="partsHelpDeskTel" type="String" default="" />
    <aura:attribute name="partsHelpDeskEmail" type="String" default="" /> -->
    <aura:attribute name="insideSales" type="String" />
    <aura:attribute name="insideSalesContactTel" type="String" />
    <aura:attribute name="insideSalesContactEmail" type="String" />
    <aura:attribute name="service" type="String" />
    <aura:attribute name="serviceContactTel" type="String" />
    <aura:attribute name="serviceContactEmail" type="String" />
    <aura:attribute name="accounting" type="String" />
    <aura:attribute name="accountingContactTel" type="String" />
    <aura:attribute name="accountingContactEmail" type="String" />
    <aura:attribute name="marketing" type="String" />
    <aura:attribute name="marketingContactTel" type="String" />
    <aura:attribute name="marketingContactEmail" type="String" />
    <aura:attribute name="ecube" type="String" />
    <aura:attribute name="ecubeContactTel" type="String" />
    <aura:attribute name="ecubeContactEmail" type="String" />
    
    <aura:handler action="{!c.doInit}" name="init" value="{!this}" />
    <lightning:tabset selectedTabId="{!v.selectedTab}" variant="vertical">
        <!-- contact -->
        <div class="contact">
            <p>{!v.insideSales + ':'}</p>
            <p><lightning:formattedEmail value="{!v.insideSalesContactEmail}"  hideIcon="true"/></p>
            <p>{!v.insideSalesContactTel}</p>
            <br/>
            <p>{!v.service + ':'}</p>
            <p><lightning:formattedEmail value="{!v.serviceContactEmail}"  hideIcon="true"/></p>
            <p>{!v.serviceContactTel}</p>
            <br/>
            <p>{!v.accounting + ':'}</p>
            <p><lightning:formattedEmail value="{!v.accountingContactEmail}"  hideIcon="true"/></p>
            <p>{!v.accountingContactTel}</p>
            <br/>
            <p>{!v.marketing + ':'}</p>
            <p><lightning:formattedEmail value="{!v.marketingContactEmail}"  hideIcon="true"/></p>
            <p>{!v.marketingContactTel}</p>
            <br/>
            <p>{!v.ecube + ':'}</p>
            <p><lightning:formattedEmail value="{!v.ecubeContactEmail}"  hideIcon="true"/></p>
            <p>{!v.ecubeContactTel}</p>
            <br/>
            <br/>
            <br/>
            <br/>
        </div>
        <!-- Knowledge-->
        <lightning:tab label="{!$Label.c.Knowledge_Knowledge_Base}" id="KnowledgeBase">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="Repair Guidance">
                                            <span><strong>{!$Label.c.Knowledge_Knowledge_Base}</strong></span>
                                     </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_Community_KnowledgeList />
            </div>
            </article>
        </lightning:tab>
    </lightning:tabset>
</aura:component>