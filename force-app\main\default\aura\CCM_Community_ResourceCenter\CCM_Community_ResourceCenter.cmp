<aura:component access="global" implements="forceCommunity:availableForAllPageTypes"
                description="CCM_Community_ResourceCenter">
    <!-- 默认选中 Knowledge Base -->
    <aura:attribute name="selectedTab" type="String" default="KnowledgeBase" />
    <!-- 联系方式 -->
    <aura:attribute name="egoDealerTel" type="String" default="" />
    <aura:attribute name="egoDealerEmail" type="String" default="" />
    <aura:attribute name="powerToolDealerTel" type="String" default="" />
    <aura:attribute name="powerToolDealerEmail" type="String" default="" />
    <aura:attribute name="partsHelpDeskTel" type="String" default="" />
    <aura:attribute name="partsHelpDeskEmail" type="String" default="" />
    
    <aura:handler action="{!c.doInit}" name="init" value="{!this}" />
    <lightning:tabset selectedTabId="{!v.selectedTab}" variant="vertical">
        <!-- contact -->
        <div class="contact">
            <p>{!$Label.c.CCM_ServiceContacts}-</p>
            <br/>
            <p>{!$Label.c.CCM_EGODealerSupport}-</p>
            <p>{!v.egoDealerTel}</p>
            <p><lightning:formattedEmail value="{!v.egoDealerEmail}"  hideIcon="true"/></p>
            <!-- <br/>
            <p>{!$Label.c.CCM_PowerToolDealerSupport}-</p>
            <p>{!v.powerToolDealerTel}</p>
            <p><lightning:formattedEmail value="{!v.powerToolDealerEmail}"  hideIcon="true"/></p>
            <br/>
            <p>{!$Label.c.CCM_PartsHelpdesk}-</p>
            <p>{!v.partsHelpDeskTel}</p>
            <p><lightning:formattedEmail value="{!v.partsHelpDeskEmail}"  hideIcon="true"/></p> -->
            <br/>
            <br/>
            <br/>
            <br/>
        </div>
        <!-- Knowledge-->
        <lightning:tab label="{!$Label.c.Knowledge_Knowledge_Base}" id="KnowledgeBase">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="Repair Guidance">
                                            <span><strong>{!$Label.c.Knowledge_Knowledge_Base}</strong></span>
                                     </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_Community_KnowledgeList />
            </div>
            </article>
        </lightning:tab>
    </lightning:tabset>
</aura:component>