/**
 * <AUTHOR>
 * @date 2025-07-01
 * @description Query prospect address information
 */
public without sharing class CCM_ProspectAddressInfoQueryCtrl {
    @AuraEnabled
    public static String queryAddressInfo(String leadId){
        Map<String, String> addressInfoMap = new Map<String, String>();
        List<Lead> prospectList = [SELECT State__c, Country__c, Province__c, City__c, Street_1__c, Street_2__c, Postal_Code__c FROM Lead WHERE Id = :leadId];

        List<Util.SelectItem> countryOptions = Util.getSelectOptions(new Account_Address__c(), 'Country_All__c');
        if(!prospectList.isEmpty()) {
            Lead prospect = prospectList[0];
            if(String.isNotBlank(prospect.Country__c)) {
                for(Util.SelectItem item : countryOptions) {
                    List<String> valueList = item.value.split('-');
                    for(String value : valueList) {
                        if(value.equalsIgnoreCase(prospect.Country__c)) {
                            addressInfoMap.put('Country_All__c', item.value);
                        }
                    }
                }
            }
            if(String.isNotBlank(prospect.State__c)) {
                addressInfoMap.put('State__c', prospect.State__c);
            }
            if(String.isNotBlank(prospect.Street_1__c)) {
                addressInfoMap.put('Street_1__c', prospect.Street_1__c);
            }
            if(String.isNotBlank(prospect.Street_2__c)) {
                addressInfoMap.put('Street_2__c', prospect.Street_2__c);
            }
            if(String.isNotBlank(prospect.Province__c)) {
                addressInfoMap.put('Province__c', prospect.Province__c);
            }
            if(String.isNotBlank(prospect.City__c)) {
                addressInfoMap.put('City__c', prospect.City__c);
            }
            if(String.isNotBlank(prospect.Postal_Code__c)) {
                addressInfoMap.put('Postal_Code__c', prospect.Postal_Code__c);
            }
        }
        return JSON.serialize(addressInfoMap);
    }
}