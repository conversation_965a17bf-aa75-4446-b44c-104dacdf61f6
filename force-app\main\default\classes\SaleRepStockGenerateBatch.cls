public with sharing class SaleRepStockGenerateBatch implements Database.Batchable<sObject>{
    Map<String, String> usermap = new Map<String, String>();
    Map<String, String> productAllmap = new Map<String, String>();
    Map<String, List<String>> productmap = new Map<String, List<String>>();
    public SaleRepStockGenerateBatch(){
        //先删除之前创建的数据
        List<Sales_Rep_Stock__c> stockListList = [select id, Sub_Inventory_Code__c
                                                  from Sales_Rep_Stock__c
                                                  where CreatedById = :UserInfo.getUserId()];
        if (stockListList.size() > 0){
            delete stockListList;

        }
        //获取user
        List<user> userList = [select Sub_Inventory_Code__c, id
                               from user
                               where Sub_Inventory_Code__c <> null];
        for (user u : userList){
            usermap.put(u.Sub_Inventory_Code__c, u.Id);
        }
        //获取组件-product
        List<Kit_Item__c> productList = [select Kit__r.Order_Model__c, VK_Product__r.Order_Model__c, Id
                                         from Kit_Item__c
                                         where RecordType.developerName = 'Kits_and_Products'];
        for (Kit_Item__c p : productList){
            if (productmap.containsKey(p.Kit__r.Order_Model__c)){
                List<String> pL = productmap.get(p.Kit__r.Order_Model__c);
                pL.add(p.VK_Product__r.Order_Model__c);
                productmap.put(p.Kit__r.Order_Model__c, pL);
            } else{
                List<String> pL = new List<String>();
                pL.add(p.VK_Product__r.Order_Model__c);
                productmap.put(p.Kit__r.Order_Model__c, pL);
            }
        }
        //获取对应的order-model与productId
        List<Product2> productAllList = [select id, Order_Model__c
                                         from Product2
                                         where Order_Model__c <> null];

        for (Product2 product : productAllList){
            productAllmap.put(product.Order_Model__c, product.Id);
        }
    }
    public SaleRepStockGenerateBatch(Id userId){
        //先删除之前创建的数据
        List<Sales_Rep_Stock__c> stockListList = [select id, Sub_Inventory_Code__c
                                                  from Sales_Rep_Stock__c
                                                  where CreatedById = :userId];
        if (stockListList.size() > 0){
            delete stockListList;

        }
        //获取user
        List<user> userList = [select Sub_Inventory_Code__c, id
                               from user
                               where Sub_Inventory_Code__c <> null];
        for (user u : userList){
            usermap.put(u.Sub_Inventory_Code__c, u.Id);
        }
        //获取组件-product
        List<Kit_Item__c> productList = [select Kit__r.Order_Model__c, VK_Product__r.Order_Model__c, Id
                                         from Kit_Item__c
                                         where RecordType.developerName = 'Kits_and_Products'];
        for (Kit_Item__c p : productList){
            if (productmap.containsKey(p.Kit__r.Order_Model__c)){
                List<String> pL = productmap.get(p.Kit__r.Order_Model__c);
                pL.add(p.VK_Product__r.Order_Model__c);
                productmap.put(p.Kit__r.Order_Model__c, pL);
            } else{
                List<String> pL = new List<String>();
                pL.add(p.VK_Product__r.Order_Model__c);
                productmap.put(p.Kit__r.Order_Model__c, pL);
            }
        }
        //获取对应的order-model与productId
        List<Product2> productAllList = [select id, Order_Model__c
                                         from Product2
                                         where Order_Model__c <> null];

        for (Product2 product : productAllList){
            productAllmap.put(product.Order_Model__c, product.Id);
        }
    }
    public Database.QueryLocator start(Database.BatchableContext BC){
        //获取inventory
        return Database.getQueryLocator([select Sub_Inventory_Code__c, Available_QTY__c, Model_No__c
                                         from Inventory__c
                                         where RecordType.developerName = 'Sales_Rep_Inventory' OR Sub_Inventory_Code__c='EGS02']);
    }
    public void execute(Database.BatchableContext BC, list<Sobject> scope){
        List<Inventory__c> InventoryList = (List<Inventory__c>)scope;
        List<Sales_Rep_Stock__c> salesList = new List<Sales_Rep_Stock__c>();
        for (Inventory__c inv : InventoryList){
            Id stockRcordTypeId = null;
            if (inv.Sub_Inventory_Code__c == 'EGS02'){
                stockRcordTypeId = Schema.SObjectType.Sales_Rep_Stock__c.getRecordTypeInfosByDeveloperName().get('Chervon_Stock').getRecordTypeId();
            } else{
                stockRcordTypeId = Schema.SObjectType.Sales_Rep_Stock__c.getRecordTypeInfosByDeveloperName().get('Sales_Rep_Stock').getRecordTypeId();

            }
            //product
            String Request_For = null;
            if (usermap.containsKey(inv.Sub_Inventory_Code__c)){
                Request_For = usermap.get(inv.Sub_Inventory_Code__c);
            }
            //model
            if (productmap.containsKey(inv.Model_No__c)){
                for (String model : productmap.get(inv.Model_No__c)){
                    Sales_Rep_Stock__c stock = new Sales_Rep_Stock__c();
                    stock.Model__c = model;
                    stock.Request_For__c = Request_For;
                    stock.QTY__c = inv.Available_QTY__c;
                    stock.Sub_Inventory_Code__c = inv.Sub_Inventory_Code__c;
                    stock.RecordTypeId = stockRcordTypeId;
                    salesList.add(stock);
                }
            } else{
                Sales_Rep_Stock__c stock = new Sales_Rep_Stock__c();
                stock.Model__c = inv.Model_No__c;
                stock.Request_For__c = Request_For;
                stock.QTY__c = inv.Available_QTY__c;
                stock.Sub_Inventory_Code__c = inv.Sub_Inventory_Code__c;
                stock.RecordTypeId = stockRcordTypeId;

                salesList.add(stock);
            }
        }
        System.debug('salesList1' + JSON.serialize(salesList));

        //根据qty拆分
        List<Sales_Rep_Stock__c> changeSalesList = new List<Sales_Rep_Stock__c>();
        for (Sales_Rep_Stock__c stock : salesList){
            if (stock.QTY__c > 1){
                for (Integer i = 0; i < stock.QTY__c; i++){
                    Sales_Rep_Stock__c newStock = stock.clone();
                    newStock.Qty__c = 1;
                    changeSalesList.add(newStock);
                }
            } else{
                changeSalesList.add(stock);

            }
        }
        //给每条数据给与Product_Description__c
        for (Sales_Rep_Stock__c stock : changeSalesList){
            if (productAllmap.containsKey(stock.Model__c)){
                stock.Product_Description__c = productAllmap.get(stock.Model__c);
            }
        }
        System.debug('stockList:' + JSON.serialize(changeSalesList));
        System.debug('stockList.size:' + changeSalesList.size());
        insert changeSalesList;
    }
    public void finish(Database.BatchableContext BC){
    }
}