/**
 * <AUTHOR>
 * @date 2025-07-10
 * @description Sales Target Actual Sales Schedule
 */
public with sharing class CCM_SalesTargetActualSalesSchedule implements Schedulable {
    
    public void execute(SchedulableContext sc) {
        Date today = Date.today();
        Date startDate = Date.valueof(System.Label.Quota_StartDate);
        Date endDate = Date.valueof(System.Label.Quota_EndDate);
        Integer batchSize = String.isNotEmpty(System.Label.Quota_Batch_Size) ? Integer.valueOf(System.Label.Quota_Batch_Size) : 200;

        Quota_ActualSalesBatch actualSalesBatch = new Quota_ActualSalesBatch(startDate, endDate, today.month(), new Set<Id>());
        Database.executeBatch(actualSalesBatch, batchSize);
    }

    public static void executeMe() {
        CCM_SalesTargetActualSalesSchedule scheduleJob = new CCM_SalesTargetActualSalesSchedule();
        System.schedule('Calculate Actual Sales on Sales Target', '0 0 21 * * ?', scheduleJob);
    }
}