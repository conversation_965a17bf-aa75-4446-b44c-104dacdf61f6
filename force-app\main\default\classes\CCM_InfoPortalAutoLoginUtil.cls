/**
 * <AUTHOR>
 * @date 2024-12-25
 * @description Info Portal auto login: 1.Create user, get user id. 2.Use user id to login.
 */
public without sharing class CCM_InfoPortalAutoLoginUtil {
    
    private static Map<String, Map<String, String>> connectionConfig = new Map<String, Map<String, String>>{
        'Sandbox' => new Map<String, String>{
            'newuser_endpoint' => 'https://infoportal-qas.chervon.com.cn/api/v2/users',
            'updateuser_endpoint' => 'https://infoportal-qas.chervon.com.cn/api/v2/users',
            'deleteuser_endpoint' => 'https://infoportal-qas.chervon.com.cn/api/v2/users/[userId]',
            'portalaccess_endpoint' => 'https://infoportal-qas.chervon.com.cn/de_DE/ego?e_code=[userId]',
            'roleid_dealer' => 'rol_O1SycR1IdUYuRuYD',
            'roleid_distributor' => 'rol_dyBF1OpjmROSembH',
            'roleid_egoemea' => 'rol_1jrfbBU3uxTSmjav'
        },
        'Production' => new Map<String, String>{
            'newuser_endpoint' => 'https://infoportal.chervon.com.cn/api/v2/users',
            'updateuser_endpoint' => 'https://infoportal.chervon.com.cn/api/v2/users',
            'deleteuser_endpoint' => 'https://infoportal.chervon.com.cn/api/v2/users/[userId]',
            'portalaccess_endpoint' => 'https://infoportal.chervon.com.cn/de_DE/ego?e_code=[userId]',
            'roleid_dealer' => 'rol_VqWAfOk8wPSGTOhS',
            'roleid_distributor' => 'rol_C4Hfo5dncQq4D1Zr',
            'roleid_egoemea' => 'rol_fGBAdXiSkgghWcz5'
        }
    };

    public static String createNewUserInInfoPortal(User userInfo) {
        String infoportal_userId;
        try {
            Boolean isSandBox = CCM_Service.IsSandboxOrg();
            InfoPortalParam param = generateParam(userInfo, false);
            String dealerOrDistribuor = 'dealer';
            if(String.isNotBlank(userInfo.Contact.Account.Sales_Channel__c)) {
                if(userInfo.Contact.Account.Sales_Channel__c.containsIgnoreCase('Distributor')) {
                    dealerOrDistribuor = 'distributor';
                }
            }
            if(isSandBox) {
                param.roleIds = new List<String>();
                param.roleIds.add(connectionConfig.get('Sandbox').get('roleid_egoemea'));
                if(dealerOrDistribuor == 'dealer') {
                    param.roleIds.add(connectionConfig.get('Sandbox').get('roleid_dealer'));
                }
                else if(dealerOrDistribuor == 'distributor') {
                    param.roleIds.add(connectionConfig.get('Sandbox').get('roleid_distributor'));
                }
            }
            else {
                param.roleIds = new List<String>();
                param.roleIds.add(connectionConfig.get('Production').get('roleid_egoemea'));
                if(dealerOrDistribuor == 'dealer') {
                    param.roleIds.add(connectionConfig.get('Production').get('roleid_dealer'));
                }
                else if(dealerOrDistribuor == 'distributor') {
                    param.roleIds.add(connectionConfig.get('Production').get('roleid_distributor'));
                }
            }

            String endpoint = isSandBox ? connectionConfig.get('Sandbox').get('newuser_endpoint') : connectionConfig.get('Production').get('newuser_endpoint');
            infoportal_userId = sendRequest(endpoint, JSON.serialize(param), userInfo.Id, 'insert', 'POST');
            if(userInfo != null) {
                userInfo.InfoPortal_UserId__c = infoportal_userId;
                update userInfo;
            }
        }
        catch(Exception ex) {
            // log error
            Util.logIntegration('Info Portal Exception', 'createNewUserInInfoPortal', 'Create New User', ex.getMessage(), '', '', 'User', userInfo.Id);
        }
        return infoportal_userId;
    }

    public static String getHomeLink(String userId) {
        User userInfo = [SELECT FirstName, LastName, Email, InfoPortal_UserId__c, Contact.Account.Sales_Channel__c, LanguageLocaleKey FROM User WHERE Id = :userId];
        Boolean isSandBox = CCM_Service.IsSandboxOrg();
        String endpoint = isSandBox ? connectionConfig.get('Sandbox').get('portalaccess_endpoint') : connectionConfig.get('Production').get('portalaccess_endpoint');
        if(userInfo.LanguageLocaleKey == 'en_US') {
            endpoint = endpoint.replace('de_DE', 'en_GB');
        }
        if(String.isBlank(userInfo.InfoPortal_UserId__c)) {
            // create user in info portal
            String infoportal_userId = createNewUserInInfoPortal(userInfo);
            infoportal_userId = infoportal_userId.replace('auth0|', '');
            return endpoint.replace('[userId]', infoportal_userId);
        }
        else {
            String infoportal_userId = userInfo.InfoPortal_UserId__c.replace('auth0|', '');
            return endpoint.replace('[userId]', infoportal_userId);
        }
    }

    public static void updateUserInInfoPortal(String userId) {
        try {
            User userInfo = [SELECT FirstName, LastName, Email, InfoPortal_UserId__c FROM User WHERE Id = :userId];
            InfoPortalParam param = generateParam(userInfo, true);
            Boolean isSandBox = CCM_Service.IsSandboxOrg();
            if(isSandBox) {
                param.roleIds = new List<String>();
                param.roleIds.add(connectionConfig.get('Sandbox').get('roleid'));
            }
            else {
                param.roleIds = new List<String>();
                param.roleIds.add(connectionConfig.get('Production').get('roleid'));
            }
            String endpoint = isSandBox ? connectionConfig.get('Sandbox').get('updateuser_endpoint') : connectionConfig.get('Production').get('updateuser_endpoint');
            sendRequest(endpoint, JSON.serialize(param), userInfo.Id, 'update', 'PUT');
        }
        catch(Exception ex) {
            Util.logIntegration('Info Portal Exception', 'updateUserInInfoPortal', 'Update User', ex.getMessage(), '', '', 'User', userId);
        }
    }

    public static void deleteUserInInfoPortal(String userId) {
        try {
            User userInfo = [SELECT FirstName, LastName, Email, InfoPortal_UserId__c FROM User WHERE Id = :userId];
            Boolean isSandBox = CCM_Service.IsSandboxOrg();
            String endpoint = isSandBox ? connectionConfig.get('Sandbox').get('deleteuser_endpoint') : connectionConfig.get('Production').get('deleteuser_endpoint');
            if(String.isNotBlank(userInfo.InfoPortal_UserId__c)) {
                String userIdParam = EncodingUtil.urlEncode(userInfo.InfoPortal_UserId__c, 'UTF-8');
                endpoint = endpoint.replace('[userId]', userIdParam);
                sendRequest(endpoint, null, userInfo.Id, 'delete', 'DELETE');
            }
        }
        catch(Exception ex) {
            Util.logIntegration('Info Portal Exception', 'deleteUserInInfoPortal', 'Delete User', ex.getMessage(), '', '', 'User', userId);
        }
    }

    private static String sendRequest(String endpoint, String params, String userId, String actionType, String method) {
        Http http = new Http();
        HttpRequest req = new HttpRequest();
        req.setEndpoint(endpoint);
        req.setHeader('Content-Type', 'application/json');
        req.setMethod(method);
        if(String.isNotBlank(params)) {
            req.setBody(params);
        }
        HttpResponse res = http.send(req);
        String resBody = res.getBody();
        if(res.getStatusCode() == 200) {
            Map<String, Object> resJSON = (Map<String, Object>)JSON.deserializeUntyped(resBody);
            if(resJSON.containsKey('msg')) {
                if((String)resJSON.get('msg') == 'success') {
                    if(actionType == 'insert') {
                        Map<String, Object> dataMap = (Map<String, Object>)resJSON.get('data');
                        String infoportal_userId = ((String)dataMap.get('userId')).replace('&#124;', '|');
                        return infoportal_userId;
                    }
                    else if(actionType == 'delete') {
                        User u = new User(Id=userId);
                        u.InfoPortal_UserId__c = null;
                        update u;
                    }
                }
                else {
                    // log error
                    Util.logIntegration('Info Portal Exception', actionType, actionType, (String)resJSON.get('msg'), '', resBody, 'User', userId);
                }
            }
            else {
                // log error
                Util.logIntegration('Info Portal Exception', actionType, actionType, resBody, '', '', 'User', userId);
            }
        }
        else if(res.getStatusCode() == 500) {
            Map<String, Object> resJSON = (Map<String, Object>)JSON.deserializeUntyped(resBody);
            if(resJSON.containsKey('msg')) {
                if(((String)resJSON.get('msg')).containsIgnoreCase('The user already exists')) {
                    if(resJSON.containsKey('userId')) {
                        String infoportal_userId = ((String)resJSON.get('userId')).replace('&#124;', '|');
                        return infoportal_userId;
                    }
                }
            }
        }
        else {
            Util.logIntegration('Info Portal Exception', actionType, actionType, resBody, '', '', 'User', userId);
        }
        return null;
    }

    private static InfoPortalParam generateParam(User userInfo, Boolean isUpdate) {
        InfoPortalParam param = new InfoPortalParam();
        param.firstName = userInfo.FirstName;
        param.lastName = userInfo.LastName;
        param.email = userInfo.Email;
        if(!isUpdate) {
            param.password = 'Chervon_' + String.valueOf(Math.rint(Math.random() * 100000));
        }
        param.approvalStatus = true;
        param.dealerId = 'Chervon';
        param.dealerName = 'Chervon';
        if(String.isNotBlank(userInfo.InfoPortal_UserId__c)) {
            param.userId = userInfo.InfoPortal_UserId__c;
        }
        return param;
    }

    @future(callout=true)
    public static void deleteUserInInfoPortalFuture(String userId) {
        deleteUserInInfoPortal(userId);
    }

    @future(callout=true)
    public static void createNewUserInInfoPortalFuture(String userId) {
        User userInfo = [SELECT FirstName, LastName, Email, InfoPortal_UserId__c FROM User WHERE Id = :userId];
        createNewUserInInfoPortal(userInfo);
    }

    @future(callout=true)
    public static void deactiveAndNewUserInInfoPortalFuture(String userId) {
        deleteUserInInfoPortal(userId);
        User userInfo = [SELECT FirstName, LastName, Email, InfoPortal_UserId__c FROM User WHERE Id = :userId];
        createNewUserInInfoPortal(userInfo);
    }

    public class InfoPortalParam {
        public String firstName;
        public String lastName;
        public String email;
        public String password; // auto generate
        public List<String> roleIds; // pre-assign value: ["rol_L7Ig62jJmy02v9YX"]
        public Boolean approvalStatus; // pre-assign value: true
        public String dealerId; // pre-assign value: Chervon
        public String dealerName; // pre-assign value: Chervon
        public String userId;
    }
}