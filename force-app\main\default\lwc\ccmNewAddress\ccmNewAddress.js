import { LightningElement, wire } from 'lwc';
import { CurrentPageReference, NavigationMixin } from 'lightning/navigation';
import queryAddressInfo from '@salesforce/apex/CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo';

export default class CcmNewAddress extends NavigationMixin(LightningElement) {

    leadId;
    accountId;
    recordTypeId;

    @wire(CurrentPageReference)
    getPageRef(pageRef) {
        if (pageRef) {
            debugger;
            const inContextOfRef = pageRef?.state?.inContextOfRef;
            if(inContextOfRef) {
                const objectContext = JSON.parse(atob(inContextOfRef.replace('1.','')));
                if(objectContext.attributes.objectApiName === 'Lead') {
                    this.leadId = objectContext.attributes.recordId;
                }
                else if(objectContext.attributes.objectApiName === 'Account') {
                    this.accountId = objectContext.attributes.recordId;
                }
            }
            this.recordTypeId = pageRef?.state?.recordTypeId;
        }
    }

    connectedCallback() {
        if(this.leadId) {
            queryAddressInfo({'leadId': this.leadId}).then(result=>{
                if(result) {
                    let addressInfo = JSON.parse(result);
                    debugger;
                    addressInfo.Prospect__c = this.leadId;
                    let defaultValues = {...addressInfo};
                    const defaultFieldValues =  this.buildDefaultValues(defaultValues);
                    this.handleNew(defaultFieldValues);
                }
            });
        }
        else if(this.accountId) {
            let defaultValues = {};
            defaultValues.Customer__c = this.accountId;
            const defaultFieldValues = this.buildDefaultValues(defaultValues);
            this.handleNew(defaultFieldValues);
        }
        else {
            let defaultFieldValues = '';
            this.handleNew(defaultFieldValues);
        }
    }

    buildDefaultValues(defaultValues) {
        const pairs = [];
        for (const [key, value] of Object.entries(defaultValues)) {
            pairs.push(`${key}=${value}`);
        }
        return pairs.join(',');
    }

    handleNew(defaultFieldValues) {
        this[NavigationMixin.Navigate]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Account_Address__c',
                actionName: 'new'
            },
            state: {
                recordTypeId: this.recordTypeId,
                defaultFieldValues: defaultFieldValues,
                nooverride: 1
            }
        });
    }
}