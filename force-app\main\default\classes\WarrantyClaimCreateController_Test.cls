@isTest
public with sharing class WarrantyClaimCreateController_Test{
    @TestSetup
    static void makeData(){
        Account acc1 = new Account(
        );
        acc1.Name = 'Test channel';
        acc1.Country_All__c = 'AD-Andorra';
        acc1.Postal_Code__c = '621044';
        acc1.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        acc1.Labor_Rate__c = 12.2;
        insert acc1;
        Account consumer = new Account(
        );
        consumer.LastName = 'Test consumer';
        consumer.Country_All__c = 'AD-Andorra';
        consumer.Postal_Code__c = '621044';
        consumer.Consumer_Status__c = 'Active';
        consumer.RecordTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
        consumer.Labor_Rate__c = 12.2;
        consumer.PersonEmail = '<EMAIL>';
        insert consumer;
        User thisUser = [select Id
                         from User
                         where Id = :UserInfo.getUserId()];
        Contact contact1 = new Contact(
            FirstName = 'Test', 
            Lastname = 'McTesty', 
            AccountId = acc1.Id, 
            Email = System.now().millisecond() + '<EMAIL>'
        );
        insert contact1;
        Account_Address__c address1 = new Account_Address__c(
        );
        address1.RecordTypeId = CCM_Constants.BILLING_ADDRESS_RECORDTYPEID;
        address1.name = 'Testbill';
        address1.Customer__c = acc1.Id;
        insert address1;
        Account_Address__c address2 = new Account_Address__c(
        );
        address2.RecordTypeId = CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID;
        address2.name = 'TestShip';
        address2.Customer__c = acc1.Id;
        insert address2;
        Product2 product1 = new Product2(
            Name = 'Product1'
        );
        product1.recordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
        product1.Order_Model__c = 'PTX1000';
        insert product1;
        Product2 product2 = new Product2(
            Name = 'Product2'
        );
        product2.recordTypeId = CCM_Constants.PRODUCT_PARTS_RECORD_TYPE_ID;
        product2.Order_Model__c = 'PTX1000-ST';
        insert product2;
        Kit_Item__c kit = new Kit_Item__c(
            Product__c = product1.Id, 
            Parts__c = product2.Id, 
            RecordTypeId = CCM_Constants.KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_ID
        );
        insert kit;
        System.runAs(thisUser){
            Profile profile1 = [Select Id
                                from Profile
                                where name = 'Partner Community User'];
            UserRole role1 = [Select Id
                              from UserRole
                              where PortalType = 'Partner'
                              limit 1];
            User portalAccountOwner1 = new User(
                isActive = true, 
                ContactId = contact1.Id, 
                ProfileId = profile1.Id, 
                Username = System.now().millisecond() + '<EMAIL>', 
                Alias = 'batman', 
                Email = '<EMAIL>', 
                EmailEncodingKey = 'UTF-8', 
                Firstname = 'Bruce', 
                Lastname = 'Portal', 
                LanguageLocaleKey = 'en_US', 
                LocaleSidKey = 'en_US', 
                TimeZoneSidKey = 'America/Chicago'
            );
            insert portalAccountOwner1;
        }
        Warranty_Item__c warrantyItem = new Warranty_Item__c(
        );
        warrantyItem.Serial_Number__c = 'ETP03210711599X';
        warrantyItem.Product__c = product1.Id;
        warrantyItem.Consumer__c = consumer.Id;
        warrantyItem.Receipt_Link__c = 'https://www.example.com/receipt.pdf';
        insert warrantyItem;
        Warranty_Claim__c testWarrantyClaim = new Warranty_Claim__c(
        );
        testWarrantyClaim.Dealer_Name__c = acc1.Id;
        insert testWarrantyClaim;
    }
    @isTest
    public static void testGetProjectInfo(){
        Project__c project = new Project__c();
        project.Name = 'test project';
        project.Deadline__c = date.today().addDays(100);
        project.Project_Code__c = 'test project';
        project.Recall_Reason__c = 'test project';
        project.Solution__c = 'Repair';
        insert project;
        Project_SN__c productSN = new Project_SN__c();
        productSN.Start_SN__c = 'ETP03210711499X';
        productSN.End_SN__c = 'ETP03210711699X';
        productSN.ProJect__c = project.Id;
        productSN.Total_SN__c = 10;
        productSN.Total_SN_on_hand__c = 10;
        insert productSN;
        WarrantyClaimCreateController.getProjectInfo('ETP03210711599X');
        WarrantyClaimCreateController.checkProjectExist('ETP03210711599X');
        try{
            WarrantyClaimCreateController.getAWSSignedURL('http://123.232.com');
        } catch (Exception e){

        }
        WarrantyClaimCreateController.ReceiptJunkPreCreate();
        try{
            WarrantyClaimCreateController.UploadReceiptToAws('', '', '', '');
        } catch (Exception e){

        }
    }
    @isTest
    public static void testQueryAddress(){
        // 创建测试数据
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];

        // 运行测试方法
        Test.startTest();
        WarrantyClaimCreateController.queryAddress(acc.Id, 'bill', 'Test', 1, 10);
        WarrantyClaimCreateController.queryAddress(acc.Id, 'ship', 'Test', 1, 10);
        Test.stopTest();
    }
    @isTest
    public static void testQueryParts(){
        // 创建测试数据
        Product2 product = [select id
                            from Product2
                            WHERE Order_Model__c = 'PTX1000'
                            limit 1];
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        // 运行测试方法
        Test.startTest();
        String result = WarrantyClaimCreateController.queryParts(product.Id, 1, 10, acc.Id, '');
        Test.stopTest();
    }
    @isTest
    public static void testQueryModelNumber(){
        // 创建测试数据
        Product2 product = [select id, Order_Model__c
                            from Product2
                            WHERE Order_Model__c = 'PTX1000'
                            limit 1];
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        // 运行测试方法
        Test.startTest();
        String result = WarrantyClaimCreateController.queryModelNumber(acc.Id, product.Order_Model__c, 1, 10);
        Test.stopTest();
    }
    @isTest
    public static void testQueryModelNumberWithoutCustomer(){
        // 创建测试数据
        Product2 product = [select id, Order_Model__c
                            from Product2
                            WHERE Order_Model__c = 'PTX1000'
                            limit 1];

        // 运行测试方法
        Test.startTest();
        String result = WarrantyClaimCreateController.queryModelNumberWithoutCustomer(product.Order_Model__c, 1, 10);
        Test.stopTest();
    }
    @isTest
    public static void testSaveInvoice(){
        try{
            WarrantyClaimCreateController.saveInvoice('', '', '', '', '');
        } catch (Exception e){

        }
    }
    @isTest
    public static void testCheckBasicInformation(){
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        WarrantyClaimCreateController.checkBasicInformation('<EMAIL>', '2023-11-14', '2023-11-14', 'EGO', 'ETP03210711599X', 'PTX1000', acc.Id);
    }
    @isTest
    public static void testQueryInvoice(){
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.PERSONACCOUNT_RECORDTYPEID
                       limit 1];
        List<Warranty_Item__c> wiList = [select id, Consumer__r.PersonEmail, Serial_Number__c, Product__r.Order_Model__c
                                         from Warranty_Item__c
                                         ORDER BY CreatedDate DESC];
        system.debug('wiList:' + wiList);
        WarrantyClaimCreateController.queryInvoice('PTX1000', 'ETP03210711599X', acc.Id, '<EMAIL>');
    }
    @IsTest
    public static void testsaveClaim(){
        Account acc = new Account(
        );
        acc.Name = 'Test Company';
        acc.Country_All__c = 'AD-Andorra';
        acc.Postal_Code__c = '621044';
        acc.Consumer_Status__c = 'Active';
        acc.RecordTypeId = CCM_Constants.CommercialConsumer_RECORDTYPEID;
        acc.Consumer_Status__c = 'Waiting Customer Approval';

        insert acc;
        Product2 prod1 = new Product2(
        );
        prod1.Name = 'BH1001';
        prod1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        prod1.ExternalId__c = 'BH1001';
        prod1.Order_Model__c = 'BH1001';
        prod1.Master_Product__c = 'BH1001';
        insert prod1;
        Pricebook2 testPricebook = new Pricebook2(
            Name = 'Test Pricebook'
        );
        insert testPricebook;
        Modifier__c modifier1 = new Modifier__c(
            Name = 'Test'
        );
        insert modifier1;
        Modifier__c modifier2 = new Modifier__c(
            Name = 'Test2'
        );
        insert modifier2;
        Modifier_Entry__c modifierentry = new Modifier_Entry__c(
            Modifier_Header_Number__c = 'M123', 
            ExternalID__c = 'M123', 
            Modifier__c = modifier1.Id
        );
        insert modifierentry;
        Pricebook_Entry__c testPricebookEntry = new Pricebook_Entry__c(
            PriceBook__c = testPricebook.Id, 
            Product__c = prod1.Id, 
            End_Date__c = Date.today(), 
            Start_Date__c = Date.today().addDays(-7), 
            IsActive__c = true, 
            UnitPrice__c = 100
        );
        insert testPricebookEntry;
        Pricebook_Entry__c testPricebookEntry2 = new Pricebook_Entry__c(
            PriceBook__c = testPricebook.Id, 
            Product__c = prod1.Id, 
            End_Date__c = Date.today(), 
            Start_Date__c = Date.today().addDays(-7), 
            UnitPrice__c = 200
        );
        insert testPricebookEntry2;
        Sales_Program__c testSalesProgram = new Sales_Program__c(
            List_Price_1__c = testPricebook.Id, 
            List_Price_2__c = testPricebook.Id, 
            List_Price_3__c = testPricebook.Id, 
            Customer__c = acc.Id, 
            Price_Book__c = testPricebook.Id, 
            Modifier_1__c = modifier1.Id, 
            Modifier_2__c = modifier2.Id, 
            Order_Type__c = 'Sales Order - DSV'
        );
        insert testSalesProgram;
        MasterProductPrice__c testMasterProductPrice = new MasterProductPrice__c(
            Account__c = acc.Id, 
            Product__c = prod1.Id, 
            Start_Date__c = Date.today().addDays(-7), 
            End_Date__c = Date.today(), 
            Final_Price__c = 90, 
            List_Price__c = 100, 
            Has_AllItem__c = false, 
            Modifier_Entry__c = modifierentry.Id, 
            CurrencyIsoCode = 'EUR'
        );
        insert testMasterProductPrice;

        LinkProductAndPrice__c testLinkProductAndPrice = new LinkProductAndPrice__c(
            MasterProductPrice__c = testMasterProductPrice.Id, 
            value__c = 10, 
            Final_Priority__c = 1, 
            Application_Method__c = 'Percent', 
            Modifier_Entry__c = modifierentry.Id, 
            CurrencyIsoCode = 'EUR'
        );
        insert testLinkProductAndPrice;
        WarrantyClaimSaveHandler.WarrantyClaimEntity objClaim = new WarrantyClaimSaveHandler.WarrantyClaimEntity();
        Date testDate = Date.today();
        objClaim.claimStatus = 'draft';
        objClaim.paymentStatus = 'Issued';
        objClaim.emailAddress = '<EMAIL>';
        objClaim.dropOffDate = testDate;
        objClaim.repairDate = testDate;
        objClaim.brand = 'EGO';
        objClaim.modelNumber = 'BH1001';
        objClaim.serialNumber = 'BH1001';
        objClaim.userType = 'Residential';
        objClaim.productId = prod1.Id;
        objClaim.productName = 'BH1001';
        objClaim.consumerId = acc.Id;
        objClaim.CustomerId = acc.Id;
        objClaim.receiptLink = 'testlink';
        objClaim.placeOfPurchase = 'testPurchase';
        objClaim.purchaseDate = testDate;
        objClaim.expiredDate = testDate;
        objClaim.receiptName = 'testname';
        objClaim.receiptLost = false;
        List<WarrantyClaimSaveHandler.WarrantyClaimItemEntity> lstListEntity = new List<WarrantyClaimSaveHandler.WarrantyClaimItemEntity>();
        WarrantyClaimSaveHandler.WarrantyClaimItemEntity objEntity = new WarrantyClaimSaveHandler.WarrantyClaimItemEntity();
        objEntity.partNumber = 'E1201';
        objEntity.partId = prod1.Id;
        lstListEntity.add(objEntity);
        objClaim.partList = lstListEntity;
        String result = WarrantyClaimCreateController.saveClaim(Json.serialize(objClaim), null, new List<String>());
        //通过Id查询
        Map<String, String> mapresult = (Map<String, String>)JSON.deserialize(result, Map<String, String>.class);
        String claimId = mapresult.get('claimId');
        Warranty_Claim__c objClaimQuery = [SELECT ID, Claim_Status__c, Payment_Status__c, Email_Address__c, Drop_off_Date__c, Repair_Date__c, Brand__c, Model_Number__c, Serial_Number__c, User_Type__c, Product__c, Product_Name__c, Consumer__c, Warranty_Item__c, Receipt_Link__c, Place_Of_Purchase__c, Purchase_Date__c, Expired_Date__c, Receipt_Name__c, Receipt_Lost__c, Basic_Permission__c, Service_Option__c, Replacement_Option__c, Repair_Type__c, Description__c, Failure_Code__c, Bill_Address_Name__c, Bill_Address__c, Ship_Address_name__c, Ship_Address__c, Explanation__c, Dealer_Name__c, Distributor_Or_Dealer__c, CurrencyIsoCode, Project__c, Project_Code__c, Labor_Rate__c, Actual_Labor_Rate__c, Labor_Input_Time__c, Claim_Date__c
                                           FROM Warranty_Claim__c
                                           WHERE ID = :claimId];
        String result4 = WarrantyClaimCreateController.submitClaim(Json.serialize(objClaim), claimId, new List<String>());
        String result5 = WarrantyClaimCreateController.queryClaimDetail(claimId);
        String result6 = WarrantyClaimCreateController.queryClaimInfoMation(acc.Id);
        String result7 = WarrantyClaimCreateController.queryClaimList(acc.Id, 1, 10, 'BH1001', 'draft', '1', '1', '1', '1', '');
        String result9 = WarrantyClaimCreateController.queryClaimList(acc.Id, 1, 10, 'BH1001', '', '', '', '', '', '');
        WarrantyClaimCreateController.saveNewSN(claimId, 'test');
        String result8 = WarrantyClaimCreateController.deleteClaim(claimId);

        system.debug('result-->' + result);
    }
    @isTest
    public static void testIsPortal(){
        User portalUser = [select id
                           from user
                           where UserRole.PortalType = 'Partner'
                           limit 1];

        // 运行测试方法
        Test.startTest();
        WarrantyClaimCreateController.IsPortal();
        System.runAs(portalUser){
            WarrantyClaimCreateController.IsPortal();
        }
        Test.stopTest();
    }
    @isTest
    public static void testQueryCustomer(){
        // 运行测试方法
        Test.startTest();
        String result = WarrantyClaimCreateController.queryCustomer('Test', 1, 10);
        Test.stopTest();
    }
    @isTest
    public static void testQueryWarrantyClaim(){
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        // 运行测试方法
        Test.startTest();
        WarrantyClaimCreateController.queryWarrantyClaim('', 1, 10, '');
        WarrantyClaimCreateController.queryWarrantyClaim('', 1, 10, acc.Id);
        Test.stopTest();
    }
    @isTest
    public static void testGetCustomerInformation(){
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        User portalUser = [select id
                           from user
                           where UserRole.PortalType = 'Partner'
                           limit 1];
        // 运行测试方法
        Test.startTest();
        System.runAs(portalUser){
            WarrantyClaimCreateController.getCustomerInformation(null);
        }
        WarrantyClaimCreateController.getCustomerInformation(acc.Id);
        Test.stopTest();
    }
    @isTest
    public static void testgetPicklistOption(){
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        // 运行测试方法
        Test.startTest();
        WarrantyClaimCreateController.getPicklistOption('Account', 'Sales_Channel__c', '');
        Test.stopTest();
    }

    @IsTest
    static void testGetProductInfo(){
        Test.startTest();
        try{
            String proudctId = '';
            List<String> partIdList = new List<String>();
            String partIds = '';
            for(Product2 prod : [SELECT Id FROM Product2 WHERE RecordType.DeveloperName = :CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID]) {
                proudctId = prod.Id;
            }

            for(Product2 part : [SELECT Id FROM Product2 WHERE RecordType.DeveloperName = :CCM_Constants.PRODUCT_PARTS_RECORD_TYPE_ID]) {
                partIdList.add(part.Id);
            }
            partIds = JSON.serialize(partIdList);
            WarrantyClaimCreateController.getProductInfo(proudctId, partIds);
        }
        catch(Exception e) {

        }
        Test.stopTest();
    }

    @IsTest
    static void testGetModelInfoBySN(){
        Test.startTest();
        try{
            String proudctId = '';
            for(Product2 prod : [SELECT Id, Order_Model__c FROM Product2 WHERE RecordTypeId = :CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID]) {
                proudctId = prod.Id;
            }

            Warranty_Item__c item = new Warranty_Item__c();
            item.Product__c = proudctId;
            item.Serial_Number__c = 'test';
            insert item;

            WarrantyClaimCreateController.getModelInfoBySN('test', null);
        }
        catch(Exception e) {

        }
        Test.stopTest();
    }
}