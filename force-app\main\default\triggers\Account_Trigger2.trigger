trigger Account_Trigger2 on Account (before insert, after insert, before update, after update, before delete, after delete){
    new Triggers()
    .bind(Triggers.Evt.beforeupdate, new CCM_CustomerUpdateHandler())//Added by Aria on 2023-05-20
    //.bind(Triggers.Evt.afterinsert, new CCM_UpdateShippingAddressRelation()) //delete by Aria,由于逻辑合并到CCM_AddressStoreLocation里,删除方法//Added by Zoe on 2023-05-25 ,当因为创建address自动生成store location后，storelocation要关联address
    .bind(Triggers.Evt.beforeupdate, new CCM_ModifierUpdateHandler()) // assign modifier when classification 1 change
    .bind(Triggers.Evt.afterupdate, new CCM_CustomerAfterUpdateHandler())
    .bind(Triggers.Evt.afterinsert, new CCM_UpdateAssociationGroup()) //Added by Zoe on 2023-05-26 ,channel与association的联动
    .bind(Triggers.Evt.afterupdate, new CCM_UpdateAssociationGroup()) //Added by Zoe on 2023-05-26 ,channel与association的联动
    .bind(Triggers.Evt.beforeinsert, new CCM_CustomerCountryDescriptionHandler()) //Added by Aria on 2023-05-31 ,Country_All__c创建赋值
    .bind(Triggers.Evt.beforeupdate, new CCM_CustomerCountryDescriptionHandler()) //Added by Aria on 2023-05-31 ,Country_All__c创建赋值
    .bind(Triggers.Evt.afterinsert, new CCM_CustomerSalesTargetHandler())//Added by Aria on 2023-07-28 ,sales target 相关逻辑
    .bind(Triggers.Evt.afterupdate, new CCM_CustomerSalesTargetHandler())//Added by Aria on 2023-07-28 ,sales target 相关逻辑
    .bind(Triggers.Evt.afterupdate, new CCM_AssginPricebookHdl()) //Added by Vicce on 2023-07-07 ,更新二级价格册
    .bind(Triggers.Evt.afterinsert, new CCM_AssginPricebookHdl()) //Added by Vicce on 2023-07-07 ,更新二级价格册
    .bind(Triggers.Evt.beforeupdate, new CCM_AssginPricebookHdl()) //Added by Vicce on 2023-07-07 ,更新二级价格册
    .bind(Triggers.Evt.afterinsert, new CCM_StandardAccountSendEmail()) //Added by Yanko on 2023-10-09 ,通过标准页面新建residential/commercial Account时向account邮箱发送激活邮件
    .bind(Triggers.Evt.afterupdate, new CCM_StandardAccountSendEmail()) //Added by Yanko on 2023-10-09 ,通过标准页面更新residential/commercial Account时向account邮箱发送激活邮件
    .bind(Triggers.Evt.beforeupdate, new CCM_UpdateAccountSalesTerritory()) //Added by Yanko on 2023-10-17 ,更新Account sales_Territory__c 后同步更新address Sales_Territory_Lookup__c
    .bind(Triggers.Evt.beforeinsert, new CCM_ConsumerGenerateAutoNumber())//Added By Vince on 2023-11-13,官网注册生成自动编号
    .bind(Triggers.Evt.afterdelete, new CCM_SyncDeletedConsumerToIOTHandler())
    .manage();
}