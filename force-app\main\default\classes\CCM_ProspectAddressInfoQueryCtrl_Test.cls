/**
 * <AUTHOR>
 * @date 2025-07-01
 * @description Test class for CCM_ProspectAddressInfoQueryCtrl
 */
@isTest
public class CCM_ProspectAddressInfoQueryCtrl_Test {
    
    @TestSetup
    static void makeData() {
        // Create test Lead records with different address information scenarios
        List<Lead> testLeads = new List<Lead>();
        
        // Lead with complete address information
        Lead leadComplete = new Lead();
        leadComplete.TestFlag__c = true;
        leadComplete.RecordTypeId = CCM_Constants.PROSPECT_CHANNEL_RECORD_TYPE_ID;
        leadComplete.Status = 'Open';
        leadComplete.LastName = 'Test Complete';
        leadComplete.Company = 'Test Complete Company';
        leadComplete.Prospect_Type__c = 'Dealer';
        leadComplete.Sales_Channel__c = 'OPE Dealer';
        leadComplete.Classification_1__c = 'Dealer_Standard';
        leadComplete.Re_automatic__c = false;
        leadComplete.Country__c = 'DE';
        leadComplete.State__c = 'Bavaria';
        leadComplete.Province__c = 'Bayern';
        leadComplete.City__c = 'Munich';
        leadComplete.Street_1__c = 'Test Street 123';
        leadComplete.Street_2__c = 'Building A';
        leadComplete.Postal_Code__c = '80331';
        leadComplete.Country_All__c = 'DE-Germany';
        testLeads.add(leadComplete);
        
        // Lead with partial address information
        Lead leadPartial = new Lead();
        leadPartial.TestFlag__c = true;
        leadPartial.RecordTypeId = CCM_Constants.PROSPECT_CHANNEL_RECORD_TYPE_ID;
        leadPartial.Status = 'Open';
        leadPartial.LastName = 'Test Partial';
        leadPartial.Company = 'Test Partial Company';
        leadPartial.Prospect_Type__c = 'Distributor';
        leadPartial.Sales_Channel__c = 'Distributor';
        leadPartial.Re_automatic__c = false;
        leadPartial.Country__c = 'FR';
        leadPartial.City__c = 'Paris';
        leadPartial.Postal_Code__c = '75001';
        leadPartial.Country_All__c = 'FR-France';
        // Note: State__c, Province__c, Street_1__c, Street_2__c are intentionally left blank
        testLeads.add(leadPartial);
        
        // Lead with minimal address information
        Lead leadMinimal = new Lead();
        leadMinimal.TestFlag__c = true;
        leadMinimal.RecordTypeId = CCM_Constants.PROSPECT_ENDUSER_RECORD_TYPE_ID;
        leadMinimal.Status = 'Open';
        leadMinimal.LastName = 'Test Minimal';
        leadMinimal.Company = 'Test Minimal Company';
        leadMinimal.Prospect_Type__c = 'End User';
        leadMinimal.Sales_Channel__c = 'End User';
        leadMinimal.Classification_1__c = 'Commercial User';
        leadMinimal.Re_automatic__c = false;
        leadMinimal.Country__c = 'US';
        leadMinimal.Postal_Code__c = '12345'; // Add postal code to satisfy validation rule
        leadMinimal.Country_All__c = 'US-United States';
        // Note: Only Country__c and Country_All__c are set
        testLeads.add(leadMinimal);

        // Lead with no address information
        Lead leadEmpty = new Lead();
        leadEmpty.TestFlag__c = true;
        leadEmpty.RecordTypeId = CCM_Constants.PROSPECT_BRANDPARTNER_RECORD_TYPE_ID;
        leadEmpty.Status = 'Open';
        leadEmpty.LastName = 'Test Empty';
        leadEmpty.Company = 'Test Empty Company';
        leadEmpty.Prospect_Type__c = 'Brand Partner';
        leadEmpty.Sales_Channel__c = 'Others';
        leadEmpty.Re_automatic__c = false;
        leadEmpty.Postal_Code__c = '54321'; // Add postal code to satisfy validation rule
        leadEmpty.Country_All__c = 'DE-Germany';
        // Note: All address fields are intentionally left blank
        testLeads.add(leadEmpty);
        
        insert testLeads;
    }
    
    @isTest
    static void testQueryAddressInfo_CompleteAddress() {
        // Get test lead with complete address information
        Lead testLead = [SELECT Id FROM Lead WHERE LastName = 'Test Complete' LIMIT 1];
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(testLead.Id);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals('{}', result, 'Result should not be empty JSON');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify all address fields are included
        System.assertEquals('DE-Germany', addressMap.get('Country_All__c'), 'Country_All__c should be mapped correctly');
        System.assertEquals('Bavaria', addressMap.get('State__c'), 'State__c should be included');
        System.assertEquals('Bayern', addressMap.get('Province__c'), 'Province__c should be included');
        System.assertEquals('Munich', addressMap.get('City__c'), 'City__c should be included');
        System.assertEquals('Test Street 123', addressMap.get('Street_1__c'), 'Street_1__c should be included');
        System.assertEquals('Building A', addressMap.get('Street_2__c'), 'Street_2__c should be included');
        System.assertEquals('80331', addressMap.get('Postal_Code__c'), 'Postal_Code__c should be included');
    }
    
    @isTest
    static void testQueryAddressInfo_PartialAddress() {
        // Get test lead with partial address information
        Lead testLead = [SELECT Id FROM Lead WHERE LastName = 'Test Partial' LIMIT 1];
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(testLead.Id);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify only non-blank fields are included
        System.assertEquals('FR-France', addressMap.get('Country_All__c'), 'Country_All__c should be mapped correctly');
        System.assertEquals('Paris', addressMap.get('City__c'), 'City__c should be included');
        System.assertEquals('75001', addressMap.get('Postal_Code__c'), 'Postal_Code__c should be included');
        
        // Verify blank fields are not included
        System.assertEquals(null, addressMap.get('State__c'), 'State__c should not be included when blank');
        System.assertEquals(null, addressMap.get('Province__c'), 'Province__c should not be included when blank');
        System.assertEquals(null, addressMap.get('Street_1__c'), 'Street_1__c should not be included when blank');
        System.assertEquals(null, addressMap.get('Street_2__c'), 'Street_2__c should not be included when blank');
    }
    
    @isTest
    static void testQueryAddressInfo_MinimalAddress() {
        // Get test lead with minimal address information
        Lead testLead = [SELECT Id FROM Lead WHERE LastName = 'Test Minimal' LIMIT 1];
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(testLead.Id);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify Country_All__c and Postal_Code__c are included
        System.assertEquals('US-United States', addressMap.get('Country_All__c'), 'Country_All__c should be mapped correctly');
        System.assertEquals('12345', addressMap.get('Postal_Code__c'), 'Postal_Code__c should be included');

        // Verify other fields are not included
        System.assertEquals(null, addressMap.get('State__c'), 'State__c should not be included when blank');
        System.assertEquals(null, addressMap.get('Province__c'), 'Province__c should not be included when blank');
        System.assertEquals(null, addressMap.get('City__c'), 'City__c should not be included when blank');
        System.assertEquals(null, addressMap.get('Street_1__c'), 'Street_1__c should not be included when blank');
        System.assertEquals(null, addressMap.get('Street_2__c'), 'Street_2__c should not be included when blank');
    }
    
    @isTest
    static void testQueryAddressInfo_EmptyAddress() {
        // Get test lead with no address information
        Lead testLead = [SELECT Id FROM Lead WHERE LastName = 'Test Empty' LIMIT 1];
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(testLead.Id);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify only Postal_Code__c is included (since other address fields are blank)
        System.assertEquals('54321', addressMap.get('Postal_Code__c'), 'Postal_Code__c should be included');
        System.assertEquals(1, addressMap.size(), 'Address map should only contain Postal_Code__c');
    }
    
    @isTest
    static void testQueryAddressInfo_InvalidLeadId() {
        // Test with a non-existing Lead ID
        String fakeLeadId = '00Q000000000000AAA'; // Fake Lead ID
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(fakeLeadId);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify the map is empty since no lead was found
        System.assertEquals(0, addressMap.size(), 'Address map should be empty when lead is not found');
    }
    
    @isTest
    static void testQueryAddressInfo_NullLeadId() {
        // Test with null Lead ID
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(null);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify the map is empty since leadId is null
        System.assertEquals(0, addressMap.size(), 'Address map should be empty when leadId is null');
    }
    
    @isTest
    static void testQueryAddressInfo_CountryMapping() {
        // Test the country mapping logic specifically
        // Create a lead with a country that should be found in the Country_All__c picklist
        Lead testLead = new Lead();
        testLead.TestFlag__c = true;
        testLead.RecordTypeId = CCM_Constants.PROSPECT_CHANNEL_RECORD_TYPE_ID;
        testLead.Status = 'Open';
        testLead.LastName = 'Test Country Mapping';
        testLead.Company = 'Test Country Mapping Company';
        testLead.Prospect_Type__c = 'Dealer';
        testLead.Sales_Channel__c = 'OPE Dealer';
        testLead.Re_automatic__c = false;
        testLead.Country__c = 'DE'; // This should map to 'DE-Germany' in Country_All__c
        testLead.Postal_Code__c = '12345'; // Add postal code to satisfy validation rule
        testLead.Country_All__c = 'DE-Germany';
        insert testLead;
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(testLead.Id);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify the country mapping worked correctly
        System.assertNotEquals(null, addressMap.get('Country_All__c'), 'Country_All__c should be mapped');
        System.assert(addressMap.get('Country_All__c').contains('DE'), 'Country_All__c should contain DE');
    }
}
