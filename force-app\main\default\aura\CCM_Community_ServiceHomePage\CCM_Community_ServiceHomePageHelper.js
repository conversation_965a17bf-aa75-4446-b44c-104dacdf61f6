({
    // 获取user history表格数据
    getUserTableData : function(component) {
        var action = component.get("c.allUserHistory");
        action.setParams({
            userId: component.get('v.userId'),
            pageNumber: component.get('v.pageNumber'),
            allPageSize: component.get('v.pageCount'),
            companyName: component.get('v.companyName'),
            name: component.get('v.name'),
            email: component.get('v.emailAddress'),
        });
        console.log(action, 'action========');
        action.setCallback(this, function (response) {
            const state = response.getState();
            console.log(state, 'state==========');
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, 'res=======');
                component.set('v.totalRecords', res.TotalSize);
                const userData = [];
                if (res.UserHistory.length) {
                    res.UserHistory.forEach((item)=>{
                        userData.push(
                            {
                                Id: item.Id,
                                consumerOrContact: {
                                    consumerOrContactID: item.NameId,
                                    userType: item.UserType
                                },
                                rowData: {
                                    id: item.Id,
                                    userType: item.UserType,
                                    email: item.Email,
                                },
                                userType: item.UserType,
                                userTypeLabel: item.userTypeLabel,
                                company: item.UserType == 'Residential' ? '' : item.Company,
                                name: item.Name,
                                email: item.Email,
                                applyDate: item.ApplyDate,
                                confirmDate: item.ConfirmeDate,
                                dueDate: item.Status === 'Active' ? '' : item.DueDate,
                                // status: item.Status,
                                status: item.statusLabel,
                                isShowViewBtn: item.isEdit === false ? 'showbtn' : 'hidebtn',
                                isShowEditBtn: item.isEdit === false ? 'hidebtn' : 'showbtn',
                                isShowDeleteBtn: item.isEdit === false ? 'hidebtn' : 'showbtn',
                                isShowProductBtn: item.Status == 'Waiting Customer Approval' ? 'hidebtn' : 'showbtn',
                                isShowClaimBtn: item.Status == 'Waiting Customer Approval' ? 'hidebtn' : 'showbtn',
                                // solution 新增
                                companyId: item.CompanyId,
                                nameId: item.NameId,
                            }
                        )
                    })
                }
                component.set('v.currentUser', userData);
                if (userData.length) {
                    component.set('v.showUserFooter', true);
                } else {
                    component.set('v.showUserFooter', false);
                }
                console.log(component.get('v.showUserFooter'), 'showUserFooter=========');
                
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },

    // 删除user history表格数据
    deleteUserDetail : function(component, RecordId, helper) {
        // TODO:后端接口
        var action = component.get("c.deleteUserHistory");
        action.setParams({
            "RecordId": RecordId,
        });
        console.log(action, '删除action========');
        action.setCallback(this, function (response) {
            const state = response.getState();
            console.log(state, '删除state==========');
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, 'res=======');
                helper.getUserTableData(component);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取warranty history表格数据
    getWarrantyTableData : function(component,event, helper) {
        const action = component.get("c.allRegistrationHistory");
        action.setParams({
            isPortal: true,
            pageNumber: component.get('v.pageNumber'),
            allPageSize: component.get('v.pageCount'),
            companyName: component.get('v.companyName'),
            name: component.get('v.name'),
            model: component.get('v.modelNumberInfo').Name,
            sn: component.get('v.serialNumber'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'warranty history state ============');
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, 'warranty res=======');
                console.log(response.getReturnValue(), 'response.getReturnValue() =======');
                component.set('v.warrantyTotalRecords', res.TotalSize);
                const warrantyData = [];
                if (res.RegistrationHistory && res.RegistrationHistory.length) {
                    res.RegistrationHistory.forEach((item)=>{
                        warrantyData.push(
                            {
                                userType: item.UserType,
                                userTypeLabel: item.userTypeLabel,
                                company: item.Company,
                                name: item.Name,
                                masterProduct: item.MasterProduct,
                                masterProductId: item.MasterProductId,
                                modelNumber: item.ModelNumber,
                                modelNumberId: item.PartId,
                                serialNumber: item.SerialNumber,
                                warrantyStatus: item.WarrantyStatus,
                                warrantyStatusLabel: item.warrantyStatusLabel,
                                expirationDate: item.ExpirationDate,
                                createdDate: item.CreatedDate,
                                // solution 新增
                                companyId: item.CompanyId,
                                nameId: item.NameId,
                                consumerOrContact: {
                                    consumerOrContactID: item.NameId,
                                    userType: item.UserType
                                },
                                rowData: {
                                    id: item.CompanyId,
                                    modelNumber: item.ModelNumber,
                                    modelNumberId: item.PartId,
                                    serialNumber: item.SerialNumber,
                                    userType: item.UserType,
                                    email: item.Email
                                },
                                isShowClaimBtn: item.WarrantyStatus == 'Waiting Customer Approval' ? 'hidebtn' : 'showbtn'
                            }
                        )
                    })
                }
                component.set('v.currentWarranty', warrantyData);
                component.set('v.totalRecords', res.TotalSize);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取Claim history表格数据
    getClaimTableData : function(component,event, helper) {
        const action = component.get("c.queryClaimList");
        action.setParams({
            accountId: component.get('v.customerId'),
            pageNumber: component.get('v.pageNumber'),
            allPageSize: component.get('v.pageCount'),
            modelNumber: component.get('v.modelNumberInfo').Name,
            status: '',
            invoiceStatus: '',
            serialNumber: component.get('v.serialNumber'),
            name: component.get('v.name'),
            companyName: component.get('v.companyName'),
            claimReferenceNumber: component.get('v.claimReferenceNumber')
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, '获取Claim history state ============');
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, '获取Claim res=======');
                const currentClaims = [];
                if (res.Data.length) {
                    res.Data.forEach((item)=>{
                        currentClaims.push(
                            {
                                id: item.claimId,
                                name: item.claimName,
                                status: item.claimStatus,
                                statusLabel: item.claimStatusLabel,
                                paymentStatus: item.paymentStatus,
                                modelNumber: item.modelNumber,
                                serialNumber: item.serialNumber,
                                laborCost: item.laborCost,
                                materialCost: item.materialCost,
                                total: item.Total,
                                creditMemo: item.creditMemoName,
                                creditMemoId: item.creditMemoId,
                                warrantyOrder: item.warrantyOrderName,
                                warrantyOrderId: item.warrantyOrderId,
                                creditMemoOrWarrantyOrder: item.creditMemoName ? item.creditMemoName : item.warrantyOrderName ? item.warrantyOrderName : '',
                                companyName: item.CompanyName,
                                // solution 新增
                                companyId: item.CompanyId,
                                nameId: item.NameId,
                                isDraft: item.claimStatus == 'Draft' ? 'showbtn' : 'hidebtn',
                                notDraft: item.claimStatus !== 'Draft' ? 'showbtn' : 'hidebtn',
                                isReject: item.claimStatus == 'Rejected' ? 'showbtn' : 'hidebtn',
                                rejectComments: item.rejectComments,
                                rowData: {
                                    customerId: item.CustomerId,
                                    claimId: item.claimId,
                                    warrantyOrderId: item.warrantyOrderId,
                                    modelNumber: item.modelNumber,
                                    serialNumber: item.serialNumber,
                                    creditMemoId: item.creditMemoId,
                                    type: item.creditMemoName ? 'creditmemo' : 'warrantyorder'
                                },
                                claimReferenceNumber: item.claimReferenceNumber
                            }
                        )
                    })
                }
                component.set('v.currentClaims', currentClaims);
                component.set('v.totalRecords', res.TotalSize);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 删除claim数据
    deleteClaim : function(component, id) {
        let self = this;
        const action = component.get("c.deleteClaim");
        action.setParams({
            claimId: id
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, '删除claim数据 res=======');
                if (res.isSuccess == 'True') {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Success"),
                        "message": $A.get("$Label.c.CCM_DeleteClaimSuccess"),
                        "type": "success",
                    }).fire();
                    self.getClaimTableData(component);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Error",
                        "message": res.message,
                        "type": "error"
                    }).fire();
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取当前用户信息
    getCustomerInformation: function(component) {
        let self = this;
        var action = component.get("c.getCustomerInformation");
        action.setParams({
            accId: '',
        });
        action.setCallback(this, function (response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                let res = JSON.parse(response.getReturnValue());
                component.set('v.customerId', res.AccountId);
                component.set('v.registrationLink', '/s/productregistration?registrationType=residentialUser&id=' + res.AccountId);

                let DistributorOrDealer = res.DistributorOrDealer;
                if(DistributorOrDealer === 'Distributor') {
                    component.set('v.showClaimMassUpload', true);
                }

                // 获取claim 统计数据
                self.queryClaimInfoMation(component, res.AccountId);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取统计数字
    queryClaimInfoMation: function(component, id) {
        let self = this;
        var action = component.get("c.queryClaimInfoMation");
        action.setParams({
            accountId: id,
        });
        action.setCallback(this, function (response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                let res = JSON.parse(response.getReturnValue());
                let claimInformation = JSON.parse(res.claimInformation);
                console.log(JSON.stringify(res), '获取统计数字============');
                component.set('v.pending', claimInformation ? (claimInformation.Submitted) : 0);
                component.set('v.approved', claimInformation ? (claimInformation.Approved) : 0);
                component.set('v.rejected', claimInformation ? (claimInformation.Rejected) : 0);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
})