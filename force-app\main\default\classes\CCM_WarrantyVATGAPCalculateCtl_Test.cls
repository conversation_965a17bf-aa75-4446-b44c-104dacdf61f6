/**
 * <AUTHOR>
 * @date 2025-06-30
 * @description Test class for CCM_WarrantyClaim_VATGAPCalculateCtl
 */
@isTest
public class CCM_WarrantyVATGAPCalculateCtl_Test {
    
    @TestSetup
    static void makeData() {
        // Create test Account (Dealer)
        Account dealer = new Account();
        dealer.Name = 'Test Dealer';
        dealer.Country_All__c = 'DE-Germany';
        dealer.Country__c = 'DE';
        dealer.Sales_Channel__c = 'OPE Dealer';
        dealer.Classification1__c = 'Dealer';
        dealer.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        insert dealer;
        
        // Create test Product
        Product2 product = new Product2();
        product.Name = 'Test Product';
        product.Order_Model__c = 'TEST001';
        product.Master_Product__c = 'TEST001';
        product.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        insert product;
        
        // Create test Invoice
        Invoice__c invoice = new Invoice__c();
        invoice.Customer__c = dealer.Id;
        invoice.Invoice_Date__c = Date.today().addDays(-30);
        invoice.Invoice_Number__c = 'INV-001';
        insert invoice;
        
        // Create test Invoice Item
        Invoice_Item__c invoiceItem = new Invoice_Item__c();
        invoiceItem.Invoice__c = invoice.Id;
        invoiceItem.Product__c = product.Id;
        invoiceItem.Price__c = 100.00;
        invoiceItem.Amount__c = 100.00;
        insert invoiceItem;
        
        // Create test Consumer Account
        Account consumer = new Account();
        consumer.LastName = 'Test Consumer';
        consumer.Country_All__c = 'DE-Germany';
        consumer.Postal_Code__c = '12345';
        consumer.RecordTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
        insert consumer;

        // Create test Warranty Item
        Warranty_Item__c warrantyItem = new Warranty_Item__c();
        warrantyItem.Serial_Number__c = 'TEST123456789X';
        warrantyItem.Product__c = product.Id;
        warrantyItem.Consumer__c = consumer.Id;
        warrantyItem.Purchase_Date__c = Date.today().addDays(-30);
        warrantyItem.Purchase_Place__c = 'Online Dealer';
        warrantyItem.Related_Invoice__c = invoice.Id;
        warrantyItem.Invoice_No__c = 'INV-001';
        insert warrantyItem;
        
        // Create test Warranty Claims
        List<Warranty_Claim__c> claims = new List<Warranty_Claim__c>();

        // Historical approved claim
        Warranty_Claim__c historicalClaim = new Warranty_Claim__c();
        historicalClaim.Serial_Number__c = 'TEST123456789X';
        historicalClaim.Dealer_Name__c = dealer.Id;
        historicalClaim.Service_Option__c = 'Repair';
        historicalClaim.Claim_Status__c = 'Approved';
        historicalClaim.Labor_Input_Time__c = 2.0;
        historicalClaim.Actual_Labor_Rate__c = 15.0; // Labor_Input_Cost = 2 * 15 = 30
        historicalClaim.Diagnostic_Fee__c = 10.00;
        claims.add(historicalClaim);

        // Current claim for testing
        Warranty_Claim__c currentClaim = new Warranty_Claim__c();
        currentClaim.Serial_Number__c = 'TEST123456789X';
        currentClaim.Dealer_Name__c = dealer.Id;
        currentClaim.Service_Option__c = 'Repair';
        currentClaim.Claim_Status__c = 'Draft';
        currentClaim.Labor_Input_Time__c = 2.5;
        currentClaim.Actual_Labor_Rate__c = 10.0; // Labor_Input_Cost = 2.5 * 10 = 25
        currentClaim.Diagnostic_Fee__c = 5.00;
        currentClaim.Bench_Mark__c = 0; // Initialize to prevent null pointer
        currentClaim.Historical_Warranty_Efforts__c = 0; // Initialize to prevent null pointer
        currentClaim.Bench_Mark_Calculated__c = false;
        claims.add(currentClaim);

        insert claims;

        // Create Warranty Claim Items to populate Material_Actual_Cost__c
        List<Warranty_Claim_Item__c> claimItems = new List<Warranty_Claim_Item__c>();

        // Items for historical claim
        Warranty_Claim_Item__c historicalItem = new Warranty_Claim_Item__c();
        historicalItem.Warranty_Claim__c = historicalClaim.Id;
        historicalItem.Actual_Price__c = 50.00;
        historicalItem.Quantity__c = '1';
        claimItems.add(historicalItem);

        // Items for current claim
        Warranty_Claim_Item__c currentItem = new Warranty_Claim_Item__c();
        currentItem.Warranty_Claim__c = currentClaim.Id;
        currentItem.Actual_Price__c = 40.00;
        currentItem.Quantity__c = '1';
        claimItems.add(currentItem);

        insert claimItems;
        
        // Create SN_Export record for testing
        SN_Export__c snExport = new SN_Export__c();
        snExport.Request_Id__c = 'WarrantyClaimQuery20250630120000001';
        snExport.Product_Model__c = 'TEST001';
        snExport.Customer__c = dealer.Id;
        snExport.Warranty_Claim_Id__c = currentClaim.Id;
        insert snExport;
    }
    
    @isTest
    static void testCalculateBenchMark_WithInvoiceInWarranty() {
        // Disable the handler to avoid interference
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = false;

        // Get test data
        Warranty_Claim__c claim = [SELECT Id, Serial_Number__c, Dealer_Name__c FROM Warranty_Claim__c WHERE Claim_Status__c = 'Draft' LIMIT 1];
        Product2 product = [SELECT Order_Model__c FROM Product2 LIMIT 1];

        Test.startTest();
        CCM_WarrantyClaim_VATGAPCalculateCtl.calculateBenchMark(
            claim.Id,
            claim.Serial_Number__c,
            claim.Dealer_Name__c,
            product.Order_Model__c
        );
        Test.stopTest();

        // Re-enable the handler
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = true;
        
        // Verify SN_Export record was created
        List<SN_Export__c> exports = [SELECT Id, Request_Id__c FROM SN_Export__c WHERE Warranty_Claim_Id__c = :claim.Id];
        System.assert(!exports.isEmpty(), 'SN_Export record should be created');
    }
    
    @isTest
    static void testProcessWarrantyClaimQuery_Success() {
        // Disable the handler to avoid interference
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = false;

        // Get test data
        SN_Export__c snExport = [SELECT Request_Id__c, Product_Model__c, Warranty_Claim_Id__c FROM SN_Export__c LIMIT 1];

        // Create test request objects
        List<CCM_RestService_DealSNInfo.ReqestObj> reqObjList = new List<CCM_RestService_DealSNInfo.ReqestObj>();
        CCM_RestService_DealSNInfo.ReqestObj reqObj = new CCM_RestService_DealSNInfo.ReqestObj();
        reqObj.UNION_BATCH = snExport.Request_Id__c;
        reqObj.INVOICE_NO = 'INV-001';
        reqObjList.add(reqObj);

        Test.startTest();
        Boolean result = CCM_WarrantyClaim_VATGAPCalculateCtl.processWarrantyClaimQuery(reqObjList);
        Test.stopTest();

        // Re-enable the handler
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = true;
        
        // Verify result
        System.assertEquals(true, result, 'Should find price in logistic');
        
        // Verify warranty claim was updated
        Warranty_Claim__c updatedClaim = [SELECT Bench_Mark__c, Bench_Mark_Calculated__c FROM Warranty_Claim__c WHERE Id = :snExport.Warranty_Claim_Id__c];
        System.assertEquals(true, updatedClaim.Bench_Mark_Calculated__c, 'Bench mark should be calculated');
        System.assertEquals(100.00, updatedClaim.Bench_Mark__c, 'Bench mark should be set to invoice item price');
    }
    
    @isTest
    static void testProcessWarrantyClaimQuery_NoPrice() {
        // Get test data
        SN_Export__c snExport = [SELECT Request_Id__c, Product_Model__c, Warranty_Claim_Id__c FROM SN_Export__c LIMIT 1];
        
        // Create test request objects with non-existing invoice
        List<CCM_RestService_DealSNInfo.ReqestObj> reqObjList = new List<CCM_RestService_DealSNInfo.ReqestObj>();
        CCM_RestService_DealSNInfo.ReqestObj reqObj = new CCM_RestService_DealSNInfo.ReqestObj();
        reqObj.UNION_BATCH = snExport.Request_Id__c;
        reqObj.INVOICE_NO = 'NON-EXISTING-INV';
        reqObjList.add(reqObj);
        
        Test.startTest();
        Boolean result = CCM_WarrantyClaim_VATGAPCalculateCtl.processWarrantyClaimQuery(reqObjList);
        Test.stopTest();
        
        // Verify result
        System.assertEquals(false, result, 'Should not find price in logistic');
    }
    
    @isTest
    static void testFindPriceInSellableProductList() {
        // Disable the handler to avoid interference
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = false;

        // Get test data
        SN_Export__c snExport = [SELECT Request_Id__c, Warranty_Claim_Id__c FROM SN_Export__c LIMIT 1];

        Test.startTest();
        CCM_WarrantyClaim_VATGAPCalculateCtl.findPirceInSellableProductList(snExport.Request_Id__c);
        Test.stopTest();

        // Re-enable the handler
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = true;

        // Verify warranty claim was updated
        Warranty_Claim__c updatedClaim = [SELECT Bench_Mark_Calculated__c FROM Warranty_Claim__c WHERE Id = :snExport.Warranty_Claim_Id__c];
        System.assertEquals(true, updatedClaim.Bench_Mark_Calculated__c, 'Bench mark should be calculated');
    }
    
    @isTest
    static void testCalculateHistoricalWarrantyEfforts() {
        // Get test data
        Warranty_Claim__c claim = [SELECT Id, Serial_Number__c FROM Warranty_Claim__c WHERE Claim_Status__c = 'Draft' LIMIT 1];
        
        Test.startTest();
        Decimal result = CCM_WarrantyClaim_VATGAPCalculateCtl.calculateHistoricalWarrantyEfforts(
            claim.Id, 
            claim.Serial_Number__c, 
            'Repair', 
            null
        );
        Test.stopTest();
        
        // Verify result (should include the historical approved claim total)
        System.assertEquals(90.00, result, 'Historical warranty efforts should be 90.00');
        
        // Verify warranty claim was updated
        Warranty_Claim__c updatedClaim = [SELECT Historical_Warranty_Efforts__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals(90.00, updatedClaim.Historical_Warranty_Efforts__c, 'Historical warranty efforts should be updated');
    }
    
    @isTest
    static void testCalculateVATGAPForVATCorrection_ExceedsBenchmark() {
        // Get test data from setup
        Account dealer = [SELECT Id FROM Account WHERE Name = 'Test Dealer' LIMIT 1];

        // Create test warranty claim with related items to populate Total_Actual__c
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Dealer_Name__c = dealer.Id;
        claim.Serial_Number__c = 'TESTEXCEEDSX';
        claim.Service_Option__c = 'Repair';
        claim.Claim_Status__c = 'Draft';
        claim.Bench_Mark__c = 100.00;
        claim.Historical_Warranty_Efforts__c = 80.00;
        claim.Labor_Input_Time__c = 2.0;
        claim.Actual_Labor_Rate__c = 15.0; // Labor_Input_Cost = 30.00
        claim.Diagnostic_Fee__c = 5.00;
        insert claim;

        // Create claim item to populate Material_Actual_Cost__c (which contributes to Total_Actual__c)
        Warranty_Claim_Item__c item = new Warranty_Claim_Item__c();
        item.Warranty_Claim__c = claim.Id;
        item.Actual_Price__c = 15.00; // This will make Total_Actual__c = 15 + 30 + 5 = 50
        item.Quantity__c = '1';
        insert item;

        Test.startTest();

        // Refresh claim to get calculated Total_Actual__c (after rollup field calculation)
        claim = [SELECT Id, Bench_Mark__c, Historical_Warranty_Efforts__c, Total_Actual__c, Material_Actual_Cost__c, Labor_Final_Cost__c, Diagnostic_Fee__c FROM Warranty_Claim__c WHERE Id = :claim.Id];

        System.debug('Material_Actual_Cost__c: ' + claim.Material_Actual_Cost__c);
        System.debug('Labor_Final_Cost__c: ' + claim.Labor_Final_Cost__c);
        System.debug('Diagnostic_Fee__c: ' + claim.Diagnostic_Fee__c);
        System.debug('Total_Actual__c: ' + claim.Total_Actual__c);

        Decimal vatGap = CCM_WarrantyClaim_VATGAPCalculateCtl.calculateVATGAPForVATCorrection(claim);

        Test.stopTest();

        // Verify VAT gap calculation
        // Since totalWarrantyEfforts (80 + Total_Actual__c) > benchMark (100), VAT gap should equal Total_Actual__c
        System.debug('Calculated VAT Gap: ' + vatGap);
        System.assertEquals(claim.Total_Actual__c, vatGap, 'VAT gap should equal current claim total when exceeding benchmark');
    }

    @isTest
    static void testCalculateVATGAPForVATCorrection_WithinBenchmark() {
        // Get test data from setup
        Account dealer = [SELECT Id FROM Account WHERE Name = 'Test Dealer' LIMIT 1];

        // Create test warranty claim
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Dealer_Name__c = dealer.Id;
        claim.Serial_Number__c = 'TESTWITHINX';
        claim.Service_Option__c = 'Repair';
        claim.Claim_Status__c = 'Draft';
        claim.Bench_Mark__c = 200.00;
        claim.Historical_Warranty_Efforts__c = 80.00;
        claim.Labor_Input_Time__c = 2.0;
        claim.Actual_Labor_Rate__c = 15.0; // Labor_Input_Cost = 30.00
        claim.Diagnostic_Fee__c = 5.00;
        insert claim;

        // Create claim item
        Warranty_Claim_Item__c item = new Warranty_Claim_Item__c();
        item.Warranty_Claim__c = claim.Id;
        item.Actual_Price__c = 15.00; // Total_Actual__c = 15 + 30 + 5 = 50
        item.Quantity__c = '1';
        insert item;

        // Refresh claim to get calculated Total_Actual__c
        claim = [SELECT Id, Bench_Mark__c, Historical_Warranty_Efforts__c, Total_Actual__c FROM Warranty_Claim__c WHERE Id = :claim.Id];

        Test.startTest();
        Decimal vatGap = CCM_WarrantyClaim_VATGAPCalculateCtl.calculateVATGAPForVATCorrection(claim);
        Test.stopTest();

        // Verify VAT gap calculation
        System.assertEquals(0, vatGap, 'VAT gap should be 0 when within benchmark');
    }

    @isTest
    static void testCalculateVATGAPForVATCorrection_PreviousVATGAPNegative() {
        // Get test data from setup
        Account dealer = [SELECT Id FROM Account WHERE Name = 'Test Dealer' LIMIT 1];

        // Create test warranty claim
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Dealer_Name__c = dealer.Id;
        claim.Serial_Number__c = 'TESTNEGATIVEX';
        claim.Service_Option__c = 'Repair';
        claim.Claim_Status__c = 'Draft';
        claim.Bench_Mark__c = 100.00;
        claim.Historical_Warranty_Efforts__c = 60.00; // Previous VAT GAP = 60 - 100 = -40
        claim.Labor_Input_Time__c = 2.0;
        claim.Actual_Labor_Rate__c = 15.0; // Labor_Input_Cost = 30.00
        claim.Diagnostic_Fee__c = 5.00;
        insert claim;

        // Create claim item
        Warranty_Claim_Item__c item = new Warranty_Claim_Item__c();
        item.Warranty_Claim__c = claim.Id;
        item.Actual_Price__c = 15.00; // Total_Actual__c = 15 + 30 + 5 = 50
        item.Quantity__c = '1';
        insert item;

        // Refresh claim to get calculated Total_Actual__c
        claim = [SELECT Id, Bench_Mark__c, Historical_Warranty_Efforts__c, Total_Actual__c FROM Warranty_Claim__c WHERE Id = :claim.Id];

        Test.startTest();
        Decimal vatGap = CCM_WarrantyClaim_VATGAPCalculateCtl.calculateVATGAPForVATCorrection(claim);
        Test.stopTest();

        // Verify VAT gap calculation: previousVATGAP + currentTotal = -40 + 50 = 10
        System.assertEquals(10.00, vatGap, 'VAT gap should be adjusted when previous VAT GAP is negative');
    }
}
