({
    doInit: function (component, event, helper) {

        helper.getCustomerCurrency(component);

        // var invRecordType = helper.getUrlParameter("invRecordType");
        // component.set('v.isDE', $A.get("$Label.c.CCM_Translate"));
        // console.log("invRecordType:", invRecordType);
        // component.set("v.invRecordType", invRecordType);
        // // invoice type 的下拉选择框选项
        // const invoiceTypeOptions = [
        //     {
        //         'label': $A.get("$Label.c.Invoice_CREDIT"),
        //         'value': 'CREDIT'
        //     },
        //     {
        //         'label': $A.get("$Label.c.Invoice_INV"),
        //         'value': 'INV'
        //     },        
        // ];
        // component.set("v.invoiceTypeOptions", invoiceTypeOptions);
        //  // scene type 的下拉选择框选项
        //  const sceneTypeOptions = [
        //     {
        //         'label': $A.get("$Label.c.Invoice_Traning_Order"),
        //         'value': 'trainingorder'
        //     },
        //     {
        //         'label': $A.get("$Label.c.Invoice_Sales_Order"),
        //         'value': 'salesorder'
        //     },
        //     {
        //         'label': $A.get("$Label.c.Invoice_Warranty_Order"),
        //         'value': 'warrantyorder'
        //     },
        //     {
        //         'label': $A.get("$Label.c.Invoice_Warranty_Claim"),
        //         'value': 'warrantyclaim'
        //     },            
        // ];
        // component.set("v.sceneTypeOptions", sceneTypeOptions);
        // // 表格列名 
        // const columns = [
        //     {
        //         label: $A.get("$Label.c.CCM_Action"),
        //         type: "lightning:buttonIcon",
        //         tdStyle: "text-align: left",
        //         attributes: {
        //             value: "${rowData}",
        //             variant: "bare",
        //             iconName: "utility:preview",
        //             alternativeText: "View",
        //             onclick: component.getReference("c.doPreview")
        //         }
        //     },
        //     { label: $A.get("$Label.c.Invoice_Invoice_Number"), fieldName: "invoiceNumber" },
        //     { label: $A.get("$Label.c.Invoice_Your_VAT_Reg_No"), fieldName: "vatRegNo" },
        //     { label: $A.get("$Label.c.Invoice_Order_Number"), fieldName: "orderNumber" },
        //     { label: $A.get("$Label.c.Invoice_Order_Date"), fieldName: "orderDate" },
        //     { label: $A.get("$Label.c.Invoice_Amount"), fieldName: "invoiceAmount", type: 'currency', typeAttributes: { currencyCode: component.get('v.currencyCode')}},
        //     { label: $A.get("$Label.c.Invoice_Type"), fieldName: "invoiceType" },
        //     { label: $A.get("$Label.c.Invoice_Date"), fieldName: "invoiceDate" }
        // ]
        // component.set("v.columns", columns);

        // // 页面进入，不做筛选条件的查询结果
        // helper.getObjectRecords(component, event, helper);
    },
    handleSearch: function (component, event, helper) {
        helper.getObjectRecords(component, event, helper);
    },
    doReset: function (component, event, helper) {
        component.set("v.pageNumber", "1");
        component.set("v.pageCount", "20");
        component.set("v.invoiceNumber", "");
        component.set("v.vatRegNo", "");
        component.set("v.invoiceType", "");
        component.set("v.invoiceDateStart", "");
        component.set("v.invoiceDateEnd", "");

        helper.getObjectRecords(component, event, helper);
    },
    pageChange: function (component, event, helper) {
        var pageNumber = event.getParam("pageNumber");
        console.log('PageNumber' + pageNumber);
        component.set("v.pageNumber", pageNumber);
        helper.getObjectRecords(component, event, helper);
        event.stopPropagation();
    },
    pageCountChange: function (component, event, helper) {
        var pageCount = event.getParam("pageCount");
        console.log('pageCount' + pageCount);
        component.set("v.pageCount", pageCount);
        component.set("v.pageNumber", 1);
        helper.getObjectRecords(component, event, helper);
        event.stopPropagation();
    },
    doPreview: function (component, event, helper) {
        console.log("doPreview1:", JSON.stringify(event.getSource().get('v.value')));
        let rowData = event.getSource().get('v.value');
        let recordId = rowData.sceneType.split("@")[0];
        let sceneType = rowData.sceneType.split("@")[1];
        let invoiceType = rowData.invoiceType;
        console.log("recordId:", recordId);
        console.log("sceneType:", sceneType);
        let isDE = component.get('v.isDE');
        let url = "";
        // 如果invoice 的 sceneType == "Sales Order"
        // test
        sceneType = "Sales Order";
        if (sceneType == "Sales Order") {
            // if (invoiceType === $A.get("$Label.c.Invoice_CREDIT")) {
            //     if (isDE == 'DE') {
            //         url = '/apex/OrderPDFNoteViewDE?recordId=' + recordId;
            //     } else {
            //         url = '/apex/OrderPDFNoteView?recordId=' + recordId;
            //     }    
            // } else {
            //     if (isDE == 'DE') {
            //         url = '/apex/OrderPDFViewDE?recordId=' + recordId;
            //     } else {
            //         url = '/apex/OrderPDFView?recordId=' + recordId;
            //     }
            // }

            // ldox process
            url = "/apex/pdfviewer?recordId=" + recordId;

            // if (isDE == 'DE') {
            //     url = '/apex/OrderPDFViewDE?recordId=' + recordId;
            // } else {
            //     url = '/apex/OrderPDFView?recordId=' + recordId;
            // }
            // url = "/apex/OrderPDFView?recordId=" + recordId;
            
            window.open(url, "_blank");
        // 如果invoice 的 sceneType == "Training Request"
        } else if (sceneType == "Training Request") {
            url = "/apex/TrainingPDFView?recordId=" + recordId;
            window.open(url, "_blank");
        }
    },
})