<aura:component description="CCM_ProductRegistration" extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout"
                access="global" controller="CCM_ProductRegistration">
                <!-- implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes" -->
    <!--owner information-->

    <!-- 产品注册用户类别 -->
    <aura:attribute name="showResidentialInfo" type="String" default="true"/>
    <aura:attribute name="showCommercialInfo" type="String" default="false"/>

    <aura:attribute name="organizationName" type="String" default=""/>
    <aura:attribute name="firstName" type="String" default=""/>
    <aura:attribute name="lastName" type="String" default=""/>
    <aura:attribute name="addressLine1" type="String" default=""/>
    <aura:attribute name="address" type="String" default=""/>
    <aura:attribute name="emailAddress" type="String" default=""/>
    <aura:attribute name="phone" type="String" default=""/>
    <aura:attribute name="zipPostalCode" type="String" default=""/>
    <aura:attribute name="postCode" type="String" default=""/>
    <aura:attribute name="city" type="String" default=""/>
    <aura:attribute name="state" type="String" default=""/>
    <aura:attribute name="country" type="String" default=""/>
    <aura:attribute name="street" type="String" default=""/>

    <!-- 表格参数 -->
    <aura:attribute name="tableData" type="Object" default="[]"/>
    <aura:attribute name="columns" type="List"/>
    <aura:attribute name="exportData" type="List" default="[]"/>
    <aura:attribute name="parseData" type="List" default="[]"/>
    <aura:attribute name="draftValues" type="List" default="[]"/>
    <aura:attribute name="selectList" type="List" default="[]"/>

    <aura:attribute name="purchaseDate" type="Date" default=""/>
    <aura:attribute name="brandName" type="String" default="EGO"/>
    <aura:attribute name="masterModelnumber" type="String" default="" />
    <aura:attribute name="purchasePlace" type="String" default="" />
    <aura:attribute name="toDay" type="String" default="" />
    

    <!--product information-->
    <aura:attribute name="modelNumber" type="String" default="product"/>
    <aura:attribute name="ProductList" type="List" default=""/>
    <aura:attribute name="masterProductObj" type="Map" default=""/>
    <aura:attribute name="purchasePlaceObj" type="Map" default=""/>
    <aura:attribute name="brandList" type="List" default=""/>
    <aura:attribute name="purchasePlaceList" type="List" default="{label: 'Online Dealers', value: 'Online Dealers'}, {label: 'Stationary Stores', value: 'Stationary Stores'}"/>
    <aura:attribute name="purchaseUseTypeList" type="List" default=""/>
    <aura:attribute name="productListData" type="List"/>
    <aura:attribute name="firstSave" type="Boolean" default="true" />
    <aura:attribute name="errortip" type="Boolean" default="false" />
    <aura:attribute name="uploadFileName" type="String" default="" />
    <aura:attribute name="contentId" type="String" default="" />
    <aura:attribute name="Company" type="String" default="" />
    <aura:attribute name="fleetManager" type="String" default="" />
    <aura:attribute name="fleetManagerEmail" type="String" default="" />
    <aura:attribute name="errorList" type="List" default="[]" />
    <!-- yanko add -->
    <aura:attribute name="isNew" type="Boolean" default="true" />
    <aura:attribute name="submitFlag" type="Boolean" default="true" />
    <aura:attribute name="isDisabled" type="Boolean" default="false" />
    <aura:attribute name="isPending" type="Boolean" default="false" />
    <aura:attribute name="isActive" type="Boolean" default="true" />
    <aura:attribute name="isInactive" type="Boolean" default="false" />
    

    <aura:attribute name="isReceiveInfo" type="List" default="[
    {'label': 'Yes', 'value': 'true'},
    {'label': 'No', 'value': 'false'}
    ]"/>
    <aura:attribute name="isVisitWebsite" type="List" default="[
    {'label': 'Yes', 'value': 'true'},
    {'label': 'No', 'value': 'false'}
    ]"/>
    <aura:attribute name="options" type="List" default="[]"/>
    <aura:attribute name="visitWebsiteValue" type="String" default="true"/>
    <aura:attribute name="receiveInfoValue" type="String" default="true"/>
    <aura:attribute name="targetRecord" type="Object" access="private"/>
    <aura:attribute name="targetFields" type="Object" access="private"/>
    <aura:attribute name="targetError" type="String" access="private"/>
    <aura:attribute name="lostReceipt" type="Boolean" access="private" default="false"/>
    <aura:attribute name="registrationType" type="String" default="residentialUser"/>
    <aura:attribute name="commercialId" type="String" default=""/>

    <!--file upload-->
    <aura:attribute name="multiple" type="Boolean" default="true"/>
    <aura:attribute name="accept" type="List" default="['.jpg', '.png']"/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="disabled" type="Boolean" default="false"/>

    <aura:attribute name="fileList" type="List" default="[]"/>
    <aura:attribute name="receiptList" type="List" default="[]"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="resetFlag" type="Boolean" default="true"/>
    <aura:attribute name="receiptUrl" type="Map" default="{}"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <!-- <aura:set attribute="else"> -->
    <section class="slds-p-around_x-small">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
        <article class="slds-card">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                title="{!$Label.c.CCM_OrganizationInformation}">
                                    <span><strong>{!$Label.c.CCM_OrganizationInformation}</strong></span>
                            </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <div class="slds-grid slds-wrap slds-align_absolute-center width80">
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <div>
                            <strong>{!$Label.c.CCM_DealerDistributorName}: </strong>
                            <span>{!v.organizationName}</span>
                        </div>
                    </div>
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <div>
                            <strong>{!$Label.c.CCM_RegistrationSource}: </strong>
                            <span> {!$Label.c.CCM_BusinessPortal}</span>
                        </div>
                    </div>
                </div>
            </div>
        </article>
        <article class="slds-card">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                title="{!$Label.c.CCM_OwnerInformation}">
                                    <span><strong>{!$Label.c.CCM_OwnerInformation}</strong></span>
                            </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <!-- <aura:if isTrue="{!v.isPrivacy}">
                    <p class="show-help-text">
                        <span>* This user has been registered before, due to the user's privacy setting, the users' information is hidden from the dealers</span>
                    </p>
                </aura:if> -->
                <aura:if isTrue="{!v.isPending}">
                    <p class="show-help-text">
                        <span>{!$Label.c.CCM_ProductRegistration_UserInfo1}</span>
                    </p>
                </aura:if>
                <aura:if isTrue="{!v.isInactive}">
                    <p class="show-help-text">
                        <span>{!$Label.c.CCM_ProductRegistration_UserInfo2}​</span>
                    </p>
                </aura:if>
                <!-- 个人注册组织信息 -->
                <aura:if isTrue="{!v.showResidentialInfo}">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 residential-wrap">
                        <div class="left-wrap">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                <lightning:input type="Email" aura:id="emailAddress" label="{!$Label.c.CCM_EmailAddress + ':'}" value="{!v.emailAddress}" class="field-required" onblur="{!c.emailOrPhoneGetCustomerInfo}" maxlength="255"/>
                                <div aura:id="emailAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input aura:id="firstName" label="{!$Label.c.CCM_FirstName + ':'}" value="{!v.firstName}" class="field-required slds-text-align_right" onblur="{!c.chengeFirstName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <div aura:id="firstName-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_FirstName + ':'}" value="" class="slds-text-align_right" onblur="{!c.chengeFirstName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input aura:id="lastName" label="{!$Label.c.CCM_LastName + ':'}" value="{!v.lastName}" class="field-required slds-text-align_right" onblur="{!c.chengeLastName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <div aura:id="lastName-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_LastName + ':'}" value="" class="slds-text-align_right" onblur="{!c.chengeLastName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                <aura:if isTrue="{!v.isNew}">
                                    <c:CCM_Community_LookUp aura:id="country"
                                                        fieldName="Country"
                                                        fieldNameLabel="{!$Label.c.CCM_Country}"
                                                        selectedValue="{!v.country}"
                                                        class="field-required"
                                                        isDisabled="{!v.isDisabled}"
                                                        onSelect="{!c.changeCountry}"
                                    />
                                    <div aura:id="country-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_Country + ':'}" value="" class="slds-text-align_right" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if> 
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input aura:id="postCode" label="{!$Label.c.CCM_PostalCode + ':'}" value="{!v.postCode}" class="field-required slds-text-align_right" onblur="{!c.changePostCode}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <div aura:id="postCode-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_PostalCode + ':'}" value="" class="slds-text-align_right" onblur="{!c.changePostCode}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                        </div>
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input label="{!$Label.c.CCM_Phone + ':'}" value="{!v.phone}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_Phone + ':'}" value="" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small addressPosition">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input aura:id="address" label="{!$Label.c.CCM_Address + ':'}" value="{!v.address}" class="slds-text-align_right" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <aura:set attribute="else">
                                        <lightning:input aura:id="address" label="{!$Label.c.CCM_Address + ':'}" value="" class="slds-text-align_right" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small addressPosition">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input aura:id="street" label="{!$Label.c.CCM_StreetNo + ':'}" value="{!v.street}" class="field-required slds-text-align_right" onblur="{!c.changeStreet}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <div aura:id="street-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_StreetNo + ':'}" value="" class="slds-text-align_right" onblur="{!c.changeStreet}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input aura:id="city" label="{!$Label.c.CCM_City + ':'}" value="{!v.city}" class="field-required slds-text-align_right" onblur="{!c.changeCity}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <div aura:id="city-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_City + ':'}" value="" class="slds-text-align_right" onblur="{!c.changeCity}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <aura:if isTrue="{!v.isActive}">
                                    <lightning:input aura:id="state" label="{!$Label.c.CCM_StateProvince + ':'}" value="{!v.state}" class="field-required slds-text-align_right" onblur="{!c.changeState}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    <div aura:id="state-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                                    <aura:set attribute="else">
                                        <lightning:input label="{!$Label.c.CCM_StateProvince + ':'}" value="" class="slds-text-align_right" onblur="{!c.changeState}" maxlength="255" disabled="{!v.isDisabled}"/>
                                    </aura:set>
                                </aura:if>
                            </div>
                        </div>
                    </div>
                </aura:if>
                <!-- 公司注册组织信息 -->
                <aura:if isTrue="{!v.showCommercialInfo}">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 commercial-info-wrap">
                        <!-- Company -->
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required slds-col slds-size_1-of-3">
                            <div>
                                <strong>{!$Label.c.CCM_Company}:</strong>
                                <span>{!v.Company}</span>
                            </div>
                        </div>
                        <!-- Fleet Manager -->
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required slds-col slds-size_1-of-3">
                            <div>
                                <strong>{!$Label.c.CCM_FleetManager}: </strong>
                                <span>{!v.fleetManager}</span>
                            </div>
                        </div>
                        <!-- Fleet Manager Email -->
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required slds-col slds-size_1-of-3">
                            <div>
                                <strong>{!$Label.c.CCM_FleetManagerEmailAddress}:</strong>
                                <span>{!v.fleetManagerEmail}</span>
                            </div>
                        </div>
                    </div>
                </aura:if>
            </div>
        </article>
        <!-- Product information -->
        <article class="slds-card relative">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                            title="{!$Label.c.CCM_ProductInformation}">
                                <span><strong>{!$Label.c.CCM_ProductInformation}</strong></span>
                        </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small productSection slds-clearfix">
                <div class="select-wrap">
                    <!-- Purchase date -->
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small datePicker field-required select-item date-required">
                        <label class="slds-form-element__label" style="margin-top: 5px;">{!$Label.c.CCM_PurchaseDate}:</label>
                        <lightning:input aura:id="purchaseDate" type="date" class="date-box-item slds-p-top--xx-small" variant="label-hidden" value="{!v.purchaseDate}" max="{!v.toDay}"/>
                        <!-- <ui:inputDate aura:id="purchaseDate" value="{!v.purchaseDate}" displayDatePicker="true" format="MM/dd/yy" /> -->
                        <div aura:id="purchaseDate-error-required" class="error-text slds-hide purchaseDate-error-required">{!$Label.c.CCM_RequiredFields}</div>
                    </div>
                    <!-- brand -->
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required select-item" style="margin-top: 5px;">
                        <lightning:select label="{!$Label.c.CCM_Brand + ':'}" aura:id="brand" value="{!v.brandName}" >
                            <aura:iteration items="{!v.brandList}" var="brand">
                                <option text="{!brand}" value="{!brand}"></option>
                            </aura:iteration>
                        </lightning:select>
                        <div aura:id="brand-error-required" class="error-text slds-hide">{!$Label.c.CCM_RequiredFields}</div>
                    </div>
                    <!-- Master Model number -->
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required select-item">
                        <c:CCM_Community_LookUp aura:id="masterProduct"
                                                fieldName="Kit/Model:"
                                                fieldNameLabel="{!$Label.c.CCM_Model}"
                                                brandName="EGO"
                                                selectedValue="{!v.masterModelnumber}"
                                                fromProductRegistration="true"
                        />
                        <div aura:id="masterProduct-error-required" class="error-text slds-hide masterProduct-error-required">{!$Label.c.CCM_RequiredFields}</div>
                    </div>
                    <!-- Purchase place -->
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex purchasePlace field-required select-item" style="margin-top: 5px;">
                        <lightning:select label="{!$Label.c.CCM_PurchasePlace + ':'}" aura:id="purchasePlace" value="{!v.purchasePlace}">
                            <option value="">-- {!$Label.c.CCM_None} --</option>
                            <aura:iteration items="{!v.purchasePlaceList}" var="purchasePlace">
                                <option text="{!purchasePlace.label}" value="{!purchasePlace.value}"></option>
                            </aura:iteration>
                        </lightning:select>
                        <!-- <c:CCM_Community_LookUp  fieldName="Purchase place:"
                                                brandName="{!v.brandName}"
                                                selectedValue="{!v.purchasePlaceObj}"
                                                aura:id="purchasePlace"
                        /> -->
                        <div aura:id="purchasePlace-error-required" class="error-text slds-hide purchaseDate-error-required">This field is
                            required</div>
                    </div>
                    <!-- Upload your receipt -->
                    <div class="upload-receipt-wrap">
                        <div class="fileName-wrap" style="margin-top: 5px;">
                            <!-- <lightning:input aura:id="upload" class="upload-wrap" name="" type="file" label="Upload Your Receipt:" multiple="true" accept="image/png, .pdf" onchange="{!c.handleFilesChange}"/>
                            <aura:if isTrue="{!v.receiptUrl.fileName}">
                                <p class="uploadFinished">
                                    <span class="fileName">{!v.receiptUrl.fileName}</span>
                                    <a class="delete" onclick="{!c.deleteReceipt}">Delete</a>
                                </p>
                            </aura:if> -->
                            <lightning:fileUpload 
                                class="upload-wrap" 
                                label="{!$Label.c.CCM_UploadYourReceipt + ':'}"
                                name="fileUploader"
                                multiple="false"
                                accept="['.png', '.jpg', '.jpeg', '.pdf']"
                                recordId="{!v.recordId}"
                                onuploadfinished="{!c.handleUploadFinished}" 
                            />
                            <aura:if isTrue="{!v.receiptUrl.fileName}">
                                <p class="uploadFinished">
                                    <span class="fileName">{!v.receiptUrl.fileName}</span>
                                    <a class="delete" onclick="{!c.deleteReceipt}">{!$Label.c.CCM_Delete}</a>
                                </p>
                            </aura:if>
                        </div>
                        <!-- 避免添加按钮被单独挤到下一行，和最后一个item放一起 -->
                        <!-- <lightning:button class="addBtn" label="{!$Label.c.CCM_Verify}" variant="bare" onclick="{!c.getTableData}"/>  -->
                        <lightning:buttonIcon class="addBtn" iconName="utility:add" variant="bare" onclick="{!c.getTableData}"/> 
                    </div>
                </div>
                <!-- calvin -->
                <aura:if isTrue="{!v.errortip}">
                    <div class="error-tips-wrap">
                        <div class="error-tips">
                            <aura:iteration items="{!v.errorList}" var="item">
                                <p>{!item}</p>
                            </aura:iteration>
                        </div>
                    </div>
                </aura:if>
                <div class="slds-grid slds-wrap select-wrap">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 select-btn-wrap">
                        <div class="flex-center-wrap template-btn-wrap">
                            <!-- 导出模板 -->
                            <!-- <c:ccmExcelTools type="export" btnText="Download Template" btnType="brand" excelData="{!v.exportData}" excelTitle="template"></c:ccmExcelTools> -->
                            <div class="select-template-wrap">
                                <lightning:buttonMenu label="{!$Label.c.CCM_DownloadTemplate}" variant="border-inverse" onselect="{!c.handleSelect }">
                                    <lightning:menuItem label="{!$Label.c.CCM_WarrantyRegistrationTemplate}" value="warrantyRegistrationTemplate" />
                                </lightning:buttonMenu>
                            </div>
                            <!-- 导入模板 -->
                            <aura:if isTrue="{!v.resetFlag}">
                                <c:ccmExcelTools ontabledata="{!c.getParseData}" type="parse" btnText="{!$Label.c.CCM_MassUpload}" btnType="brand"></c:ccmExcelTools>
                            </aura:if>
                        </div>
                        <div class="apply-btn-wrap">
                            <lightning:button variant="brand" label="{!$Label.c.CCM_LostReceiptForSelectedLine}" title="{!$Label.c.CCM_LostReceiptForSelectedLine}" onclick="{!c.lostReceipt}"/>

                            <!-- <lightning:input class="" type="checkbox" label="Lost Receipt For Selected Line" name="" checked="{!v.lostReceipt}" aura:id="lostReceipt"/> -->
                            <lightning:button variant="brand" label="{!$Label.c.CCM_ApplyReceiptForSelectedLine}" title="{!$Label.c.CCM_ApplyReceiptForSelectedLine}" onclick="{!c.applyReceipt}"/>
                        </div>
                    </div>
                </div>
                <!-- 表格数据 -->
                <div class="table-wrap">
                    <lightning:datatable class="table simple-table"
                        columns="{!v.columns}"
                        data="{!v.tableData}"
                        keyField="id"
                        onrowselection="{!c.tableSelected}"
                        onrowaction="{!c.handleRowAction}"
                        draftValues="{!v.draftValues}"
                        onsave="{!c.handleSaveEdition}" 
                        oncancel="{!c.handleCancelEdition}"
                        oncellchange="{!c.handleCellChange}"
                    />
                </div>
            </div>
        </article>
        <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
            <lightning:button class="" variant="brand"  label="{!$Label.c.CCM_ExportPDF}" title="{!$Label.c.CCM_ExportPDF}" onclick="{!c.onExportPDF}"/>
            <aura:if isTrue="{!v.isPending}">
                <lightning:button class="{!v.isDisabled ? 'disabled-btn' : 'submit-btn'}" variant="brand"  label="{!$Label.c.CCM_Submit}" title="{!$Label.c.CCM_Submit}" onclick="{!c.onClickSubmit}" disabled="{!v.isPending}"/>
                <aura:set attribute="else">
                    <lightning:button class="submit-btn" variant="brand"  label="{!$Label.c.CCM_Submit}" title="{!$Label.c.CCM_Submit}" onclick="{!c.onClickSubmit}"/>
                </aura:set>
            </aura:if>
            <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.onClickCancel}"/>
        </div>
    </section>

</aura:component>