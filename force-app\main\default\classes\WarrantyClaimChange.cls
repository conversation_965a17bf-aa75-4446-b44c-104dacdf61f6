public with sharing class WarrantyClaimChange implements Triggers.Handler{
    static boolean isRun = true;
    public void handle(){
        if (isRun){
            if (Trigger.isBefore){
                giveValueForSNRepeat((List<Warranty_Claim__c>)Trigger.new, (Map<Id, Warranty_Claim__c>)Trigger.oldMap);
            }
            if (Trigger.isBefore && Trigger.isupdate){
                giveCrmOrderNumber((List<Warranty_Claim__c>)Trigger.new, (Map<Id, Warranty_Claim__c>)Trigger.oldMap);
                calculateBenchMark((List<Warranty_Claim__c>)Trigger.new, (Map<Id, Warranty_Claim__c>)Trigger.oldMap);
            }
            if (Trigger.isAfter && Trigger.isupdate){
                syncOracle((List<Warranty_Claim__c>)Trigger.new, (Map<Id, Warranty_Claim__c>)Trigger.oldMap);
            }
            if (Trigger.isAfter){
                givevalueToWarranty((List<Warranty_Claim__c>)Trigger.new, (Map<Id, Warranty_Claim__c>)Trigger.oldMap);
            }
        }
    }
    /**给审批通过massupload的claim赋予批次号*/
    public void giveCrmOrderNumber(List<Warranty_Claim__c> newList, Map<Id, Warranty_Claim__c> oldMap){
        List<String> idList = new List<String>();
        for (Warranty_Claim__c item : newList){
            Warranty_Claim__c oldItem = oldMap.get(item.Id);
            if (item.Is_Mass_Upload__c&&item.Claim_Status__c == 'Approved' && oldItem.Claim_Status__c != 'Approved'){
                item.CRM_Order_Number__c=item.CRM_Order_Number_Formula__c;
            }
        }
    }

    public void calculateBenchMark(List<Warranty_Claim__c> newList, Map<Id, Warranty_Claim__c> oldMap) {
        List<String> approvedClaims = new List<String>();
        for (Warranty_Claim__c item : newList) { 
            Warranty_Claim__c oldItem = oldMap.get(item.Id);
            if (item.Claim_Status__c == 'Approved' && oldItem.Claim_Status__c != 'Approved'){ 
               approvedClaims.add(item.Id); 
            }
        }
        if(!approvedClaims.isEmpty()) {
            CCM_WarrantyClaim_BenchMarkCalBatch bc = new CCM_WarrantyClaim_BenchMarkCalBatch(approvedClaims);
            Database.executeBatch(bc, 1);
        }
    }

    /**审批完同步Oracle */
    public void syncOracle(List<Warranty_Claim__c> newList, Map<Id, Warranty_Claim__c> oldMap){
        List<String> idList = new List<String>();
        List<Warranty_Purchase_Order__c> poList = new List<Warranty_Purchase_Order__c>();
        for (Warranty_Claim__c item : newList){
            Warranty_Claim__c oldItem = oldMap.get(item.Id);
            if ((item.Claim_Status__c == 'Approved' && oldItem.Claim_Status__c != 'Approved' && item.All_Cost_Calculated__c)
                || (!oldItem.All_Cost_Calculated__c && item.All_Cost_Calculated__c && item.Claim_Status__c == 'Approved')){
                if (String.isBlank(item.Warranty_Purchase_Order__c)){
                    idList.add(item.Id);
                } else{
                    idList.add(item.Warranty_Purchase_Order__c);
                    Warranty_Purchase_Order__c po = new Warranty_Purchase_Order__c(
                        Id = item.Warranty_Purchase_Order__c, 
                        Order_Status__c = 'Approved'
                    );
                    poList.add(po);
                }
            }

        }
        if (idList.size() > 0){
            // WarrantyClaimSyncBatch bc = new WarrantyClaimSyncBatch(idList);
            // Database.executeBatch(bc, 1);
            CCM_WarrantyClaimSyncQueue queueJob = new CCM_WarrantyClaimSyncQueue(idList);
            System.enqueueJob(queueJob);
        }
        update poList;
    }
    public void givevalueToWarranty(List<Warranty_Claim__c> newList, Map<Id, Warranty_Claim__c> oldMap){
        Map<String, Warranty_Claim__c> claimSNMap = new Map<String, Warranty_Claim__c>();
        Set<String> idWarrantySet = new Set<String>();
        for (Warranty_Claim__c item : newList){
            if (String.isNotBlank(item.New_Serial_Number__c) && item.Claim_Status__c <> 'Draft' && String.isNotBlank(item.Warranty_Item__c)){
                if (Trigger.isInsert){
                    claimSNMap.put(item.Warranty_Item__c, item);
                    idWarrantySet.add(item.Warranty_Item__c);
                }
                if (Trigger.isUpdate){
                    Warranty_Claim__c oldItem = oldMap.get(item.Id);
                    if (item.New_Serial_Number__c <> oldItem.New_Serial_Number__c){
                        claimSNMap.put(item.Warranty_Item__c, item);
                        idWarrantySet.add(item.Warranty_Item__c);
                    }
                }
            }
        }
        if (idWarrantySet.size() > 0){
            List<Warranty_Item__c> warrantyList = new List<Warranty_Item__c>();
            for (Warranty_Item__c item : [select Id, Serial_Number__c, Historical_SN__c
                                          from Warranty_Item__c
                                          where Id in:idWarrantySet]){
                item.Historical_SN__c = item.Serial_Number__c;
                item.Serial_Number__c = claimSNMap.get(item.Id).New_Serial_Number__c;
                warrantyList.add(item);
            }
            update warrantyList;
        }
    }
    /**give checkBox Value for SN Repeat */
    public void giveValueForSNRepeat(List<Warranty_Claim__c> newList, Map<Id, Warranty_Claim__c> oldMap){
        Map<String, List<Warranty_Claim__c>> claimMap = new Map<String, List<Warranty_Claim__c>>();
        List<Warranty_Claim__c> claimList = new List<Warranty_Claim__c>();
        Set<String> IdSet = new Set<String>();
        for (Warranty_Claim__c claim : newList){
            if (String.isNotBlank(claim.Serial_Number__c)){
                if (Trigger.isInsert){
                    if (claimMap.containsKey(claim.Serial_Number__c)){
                        claimMap.put(claim.Serial_Number__c, (List<Warranty_Claim__c>)claimMap.get(claim.Serial_Number__c).add(claim));
                    } else{
                        claimMap.put(claim.Serial_Number__c, new List<Warranty_Claim__c>{ claim });
                    }
                }
                if (Trigger.isUpdate && claim.Serial_Number__c <> oldMap.get(claim.Id).Serial_Number__c){
                    if (claimMap.containsKey(claim.Serial_Number__c)){
                        claimMap.put(claim.Serial_Number__c, (List<Warranty_Claim__c>)claimMap.get(claim.Serial_Number__c).add(claim));
                    } else{
                        claimMap.put(claim.Serial_Number__c, new List<Warranty_Claim__c>{ claim });
                    }
                    IdSet.add(claim.Id);
                }
            }
        }
        //查询是否有重复
        List<Warranty_Claim__c> quertList = [select id, Serial_Number__c
                                             from Warranty_Claim__c
                                             where Id Not in:IdSet and Serial_Number__c in:claimMap.keySet() and Service_Option__c = 'Replacement'];
        if (quertList.size() > 0){
            for (Warranty_Claim__c queryClaim : quertList){
                if (claimMap.containsKey(queryClaim.Serial_Number__c)){
                    claimList.addAll((List<Warranty_Claim__c>)claimMap.get(queryClaim.Serial_Number__c));
                }
            }
        }
        for (Warranty_Claim__c item : claimList){
            item.SN_Repeat__c = true;
        }
    }
}