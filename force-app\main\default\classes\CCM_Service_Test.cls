@isTest
private class CCM_Service_Test{
    @isTest
    static void testMethod1(){
        Test.startTest();
        try{


            String rlt = CCM_Service.getProductSeries('test', 'test');
            String rlt1 = CCM_Service.getPartFromPIM('test', 'test', false);
            String rlt2 = CCM_Service.getKitsAndProducts('test', 'test', false);
            String rlt3 = CCM_Service.getAccessoriesAndParts('test', 'test', false);
            String rlt4 = CCM_Service.getToolsOnlyAndParts('test', 'test', false);
            String rlt5 = CCM_Service.getAccessoriesProductsAndDiagram('test', 'test', false);
            String rlt6 = CCM_Service.getToolsOnlyAndDiagram('test', 'test', false);
            HttpResponse rlt7 = CCM_Service.getProductsAndDiagram('test');
            String rlt8 = CCM_Service.pullSNInfoByCondition('test');

            String rlt9 = CCM_Service.getContentServProductline('test');
            String rlt10 = CCM_Service.getEGOHomelink();
            String rlt11 = CCM_Service.getContentServSession('test', 'test');

        } catch (exception e){

        }

        Test.stopTest();
    }
    @isTest
    static void testMethod2(){
        Test.startTest();
        try{


            recordtype territoryRtObj = [select id
                                         from recordtype
                                         where developername = 'territory'];
            recordtype territoryBrObj = [select id
                                         from recordtype
                                         where developername = 'Brand'];
            recordtype accChannelObj = [select id
                                        from recordtype
                                        where developername = 'Channel'];


            Account acc = new Account(
            );
            acc.recordtypeId = accChannelObj.Id;
            acc.Name = 'testclass';
            acc.Approval_Status__c = 'Approved';
            INSERT acc;

            String rlt = CCM_Service.pushCustInfo(acc.Id, null);


        } catch (exception e){

        }

        Test.stopTest();
    }
    @isTest
    static void testMethod3(){
        Test.startTest();
        Profile profile2 = [Select Id
                            from Profile
                            where name = 'Sales Manager'];
        User userSales = new User(ProfileId = profile2.Id, Username = System.now().millisecond() + '<EMAIL>', Alias = 'batman', Email = '<EMAIL>', EmailEncodingKey = 'UTF-8', Firstname = 'Bruce', Lastname = 'Wayne', LanguageLocaleKey = 'en_US', LocaleSidKey = 'en_US', TimeZoneSidKey = 'America/Chicago');
        insert userSales;        
        try{
            Recordtype accChannelObj = [select id
                                        from recordtype
                                        where developername = 'Channel'];
            Account acc = new Account(
            );
            acc.recordtypeId = accChannelObj.Id;
            acc.Name = 'testclass';
            acc.Approval_Status__c = 'Approved';
            INSERT acc;
            Account_Address__c aa = new Account_Address__c(
            );
            aa.Approval_Status__c = 'Approved';
            aa.Dunning_Letter_Finance__c = UserInfo.getUserId();
            aa.Invoice_Credit_Finance__c = userSales.Id;
            aa.Customer__c = acc.Id;
            INSERT aa;


            sales_program__c ap = new sales_program__c(
            );
            ap.Order_Type__c = 'Sales Order - DSV';
            ap.Approval_Status__c = 'Approved';
            INSERT ap;

            List<Address_With_Program__c> lstAddress = new List<Address_With_Program__c>();
            Address_With_Program__c awp = new Address_With_Program__c(
            );
            awp.Account_Address__c = aa.Id;
            awp.Program__c = ap.Id;
            INSERT awp;

            List<String> parList = new List<String>();
            parList.add(awp.Id);


            CCM_Service.pushAddressInfo(parList, false);

        } catch (exception e){

        }

        Test.stopTest();
    }
    @isTest
    static void testMethod4(){
        Test.startTest();
        try{
            CCM_Service.getContentServSession('endPointName', 'ctsWebsite');
        } catch (Exception e){

        }
        Test.stopTest();
    }
    @isTest
    static void testMethod5(){
        Test.startTest();
        try{
            CCM_Service.getPimSession('endPointName');
        } catch (Exception e){

        }
        Test.stopTest();
    }
}