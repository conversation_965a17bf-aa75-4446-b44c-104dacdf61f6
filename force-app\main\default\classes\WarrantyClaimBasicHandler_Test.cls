@isTest
public with sharing class WarrantyClaimBasicHandler_Test{
    @TestSetup
    static void makeData(){
        Account acc1 = new Account(
        );
        acc1.Name = 'Test channel';
        acc1.Country_All__c = 'AD-Andorra';
        acc1.Postal_Code__c = '621044';
        acc1.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        acc1.Labor_Rate__c = 12.2;
        insert acc1;
        Account consumer = new Account(
        );
        consumer.LastName = 'Test consumer';
        consumer.Country_All__c = 'AD-Andorra';
        consumer.Postal_Code__c = '621044';
        consumer.Consumer_Status__c = 'Active';
        consumer.RecordTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
        consumer.Labor_Rate__c = 12.2;
        consumer.PersonEmail = '<EMAIL>';
        insert consumer;
        User thisUser = [select Id
                         from User
                         where Id = :UserInfo.getUserId()];
        Contact contact1 = new Contact(
            FirstName = 'Test', 
            Lastname = 'McTesty', 
            AccountId = acc1.Id, 
            Email = System.now().millisecond() + '<EMAIL>'
        );
        insert contact1;
        Account_Address__c address1 = new Account_Address__c(
        );
        address1.RecordTypeId = CCM_Constants.BILLING_ADDRESS_RECORDTYPEID;
        address1.name = 'Testbill';
        address1.Customer__c = acc1.Id;
        insert address1;
        Account_Address__c address2 = new Account_Address__c(
        );
        address2.RecordTypeId = CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID;
        address2.name = 'TestShip';
        address2.Customer__c = acc1.Id;
        insert address2;
        Product2 product1 = new Product2(
            Name = 'Product1'
        );
        product1.recordTypeId = CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID;
        product1.Order_Model__c = 'PTX1000';
        insert product1;
        Product2 product2 = new Product2(
            Name = 'Product2'
        );
        product2.recordTypeId = CCM_Constants.PRODUCT_PARTS_RECORD_TYPE_ID;
        product2.Order_Model__c = 'PTX1000-ST';
        insert product2;
        Kit_Item__c kit = new Kit_Item__c(
            Product__c = product1.Id, 
            Parts__c = product2.Id, 
            RecordTypeId = CCM_Constants.KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_ID
        );
        insert kit;
        System.runAs(thisUser){
            Profile profile1 = [Select Id
                                from Profile
                                where name = 'Partner Community User'];
            UserRole role1 = [Select Id
                              from UserRole
                              where PortalType = 'Partner'
                              limit 1];
            User portalAccountOwner1 = new User(
                isActive = true, 
                ContactId = contact1.Id, 
                ProfileId = profile1.Id, 
                Username = System.now().millisecond() + '<EMAIL>', 
                Alias = 'batman', 
                Email = '<EMAIL>', 
                EmailEncodingKey = 'UTF-8', 
                Firstname = 'Bruce', 
                Lastname = 'Portal', 
                LanguageLocaleKey = 'en_US', 
                LocaleSidKey = 'en_US', 
                TimeZoneSidKey = 'America/Chicago'
            );
            insert portalAccountOwner1;
        }
        Warranty_Item__c warrantyItem = new Warranty_Item__c(
        );
        warrantyItem.Serial_Number__c = 'ETP03210711599X';
        warrantyItem.Product__c = product1.Id;
        warrantyItem.Consumer__c = consumer.Id;
        warrantyItem.Receipt_Link__c = 'https://www.example.com/receipt.pdf';
        insert warrantyItem;
        Warranty_Claim__c testWarrantyClaim = new Warranty_Claim__c(
        );
        testWarrantyClaim.Dealer_Name__c = acc1.Id;
        insert testWarrantyClaim;
    }
    @isTest
    public static void testQueryAddress(){
        // 创建测试数据
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];

        // 运行测试方法
        Test.startTest();
        WarrantyClaimBasicHandler.queryAddress(acc.Id, 'bill', 'Test', 1, 10);
        WarrantyClaimBasicHandler.queryAddress(acc.Id, 'ship', 'Test', 1, 10);
        Test.stopTest();
    }
    @isTest
    public static void testQueryParts(){
        // 创建测试数据
        Product2 product = [select id
                            from Product2
                            WHERE Order_Model__c = 'PTX1000'
                            limit 1];
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        // 运行测试方法
        Test.startTest();
        String result = WarrantyClaimBasicHandler.queryParts(product.Id, 1, 10, acc.Id, '', null);
        Test.stopTest();
    }
    @isTest
    public static void testQueryModelNumber(){
        // 创建测试数据
        Product2 product = [select id, Order_Model__c
                            from Product2
                            WHERE Order_Model__c = 'PTX1000'
                            limit 1];
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        // 运行测试方法
        Test.startTest();
        String result = WarrantyClaimBasicHandler.queryModelNumber(acc.Id, product.Order_Model__c, 1, 10);
        Test.stopTest();
    }
    @isTest
    public static void testQueryModelNumberWithoutCustomer(){
        // 创建测试数据
        Product2 product = [select id, Order_Model__c
                            from Product2
                            WHERE Order_Model__c = 'PTX1000'
                            limit 1];

        // 运行测试方法
        Test.startTest();
        String result = WarrantyClaimBasicHandler.queryModelNumberWithoutCustomer(product.Order_Model__c, 1, 10);
        Test.stopTest();
    }
    @isTest
    public static void testSaveInvoice(){
        try{
            WarrantyClaimBasicHandler.saveInvoice('', '', '', '', '');
        } catch (Exception e){

        }
    }
    @isTest
    public static void testCheckBasicInformation(){
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.CHANNEL_RECORDTYPEID
                       limit 1];
        WarrantyClaimBasicHandler.checkBasicInformation('<EMAIL>', '2023-11-14', '2023-11-14', 'EGO', 'ETP03210711599X', 'PTX1000', acc.Id);
    }
    @isTest
    public static void testcheckDateFormt(){
        WarrantyClaimBasicHandler.checkDateFormt('2023-11-22');
    }
    @isTest
    public static void testQueryInvoice(){
        Account acc = [select id
                       from account
                       where RecordTypeId = :CCM_Constants.PERSONACCOUNT_RECORDTYPEID
                       limit 1];
        List<Warranty_Item__c> wiList = [select id, Consumer__r.PersonEmail, Serial_Number__c, Product__r.Order_Model__c
                                         from Warranty_Item__c
                                         ORDER BY CreatedDate DESC];
        system.debug('wiList:' + wiList);
        WarrantyClaimBasicHandler.queryInvoice('PTX1000', 'ETP03210711599X', acc.Id, '<EMAIL>');
    }
}