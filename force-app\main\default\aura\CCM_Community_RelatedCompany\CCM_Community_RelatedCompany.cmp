<aura:component description="CCM_CommunityRelatedCompany" extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout" access="global" 
                controller="CCMRealtedCompany"          
>
    <aura:attribute name="companyName" type="String" default="" />
    <aura:attribute name="country" type="String" default="" />
    <aura:attribute name="postcode" type="String" default="" />
    <!-- 表格参数 -->
    <aura:attribute name="tableData" type="Object" default="[]"/>
    <aura:attribute name="columns" type="List"/>
    <aura:attribute name="isBusy" type="Boolean" default="false" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <section class="slds-p-around_x-small">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed loading-wrap' : 'slds-hide' }"/>
        <!-- company info -->
        <article class="slds-card">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                title="{!$Label.c.CCM_OrganizationInformation}">
                                    <span><strong>{!$Label.c.CCM_SearchCompany}</strong></span>
                            </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <div class="slds-theme_default">
                    <lightning:layout horizontalAlign="space" verticalAlign="center" class="search-wrap">
                        <lightning:layoutItem size="3" class="slds-p-top_small required-wrap">
                            <lightning:input class="field-required" aura:id="company" label="{!$Label.c.CCM_CompanyName}" value="{!v.companyName}" onblur="{!c.changeCompany}"/>
                            <div aura:id="company-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_small required-wrap">
                            <c:CCM_Community_LookUp aura:id="country"
                                                    fieldName="Country"
                                                    fieldNameLabel="{!$Label.c.CCM_Country}"
                                                    selectedValue="{!v.country}"
                                                    class="field-required"
                                                    onSelect="{!c.changeCountry}"
                            />
                            <div aura:id="country-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="3" class="slds-p-top_small required-wrap">
                            <lightning:input class="field-required" aura:id="postcode" label="{!$Label.c.CCM_PostalCode}" value="{!v.postcode}" onblur="{!c.changePostcode}"/>
                            <div aura:id="postcode-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                        </lightning:layoutItem>
                    </lightning:layout>
                    <lightning:layout horizontalAlign="center">
                        <lightning:layoutItem >
                            <lightning:button variant="brand" label="{!$Label.c.CCM_Search}" onclick="{!c.handlerSearch}"/>
                            <lightning:button class="left-margin" variant="brand" label="{!$Label.c.CCM_Reset}" onclick="{!c.handlerReset}"/>
                        </lightning:layoutItem>
                    </lightning:layout>
                </div>
            </div>
        </article>
        <!-- personal info -->
        <article class="slds-card">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                title="{!$Label.c.CCM_OrganizationInformation}">
                                    <span><strong>{!$Label.c.CCM_Result}</strong></span>
                            </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <div class="slds-grid slds-wrap slds-align_absolute-center width80 company-form-wrap">
                    <div class="table-wrap">
                        <lightning:datatable class="table"
                            columns="{!v.columns}"
                            data="{!v.tableData}"
                            keyField="id"
                            onrowaction="{!c.handleRowAction}"
                            hideCheckboxColumn="true"
                        />
                    </div>
                </div>
            </div>
        </article>
        <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
            <lightning:button class="" variant="brand" label="{!$Label.c.CCM_RegisterACompany}" title="{!$Label.c.CCM_RegisterACompany}" onclick="{!c.handlerRegistration}"/>
            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.handlerCancel}"/>
        </div>
    </section>
</aura:component>