<aura:component description="CCM_IN_OrderListView" controller="CCM_Community_OrderInformationCtl" implements="forceCommunity:availableForAllPageTypes,force:appHostable,flexipage:availableForAllPageTypes" access="global">

    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="allData" type="List" default="[]"/>
    <aura:attribute name="currentData" type="List" default="[]"/>
    <aura:attribute name="filterObj" type="Object" default="{}"/>
    <aura:attribute name="hasDelegatePermission" type="Boolean" default="false"/>
    <aura:attribute name="poTypeOptions" type="List" default="[]"/>
    <aura:attribute name="statusOptions" type="List" default="[]"/>
    <aura:attribute name="createdByInput" type="String" default=""/>
    <aura:attribute name="cOwnerInput" type="String" default=""/>
    <aura:attribute name="snFlag" type="Boolean" default="true"/>
    
    <aura:attribute name="columnsSN" type="List" default="[]"/>
    <aura:attribute name="currentSNData" type="List" default="[]"/>

    <!-- 分页相关属性：页号、每页记录数、总记录数 -->
    <aura:attribute name="pageNumber" type="String" default="1"/>
    <aura:attribute name="pageCount" type="String" default="10" />
    <aura:attribute name="totalRecords" type="String" default="0" />
    <aura:handler name="pageChange" event="c:CCM_ListPageFooter_PageChangeEvt" action="{!c.pageChange}" />
    <aura:handler name="pageCountChange" event="c:CCM_ListPageFooter_PageCountChangeEvt" action="{!c.pageCountChange}" />

    <!-- sn弹框参数 -->
    <aura:attribute name="modalFlag" type="Boolean" default="false" />
    <aura:attribute name="orderNo" type="String" default="" />
    <aura:attribute name="invoiceNo" type="String" default="" />
    <aura:attribute name="containerNo" type="String" default="" />
    <aura:attribute name="serialNumber" type="String" default="" />
    <aura:attribute name="shipmentNo" type="String" default="" />
    <aura:attribute name="customerInfo" type="Map" default="{}"/>
    <aura:attribute name="exportType" type="String" default="EXCEL" />
    <aura:attribute name="exportTypeList" type="List" default="['EXCEL', 'CSV', 'PDF']"/>
    <aura:attribute name="exportData" type="List" default="[]"/>
    <!-- tab页 -->
    <aura:attribute name="tabId" type="String" default="Order" />
    <aura:handler name="change" value="{!v.tabId}" action="{!c.handleChange}"/>

    <!-- 计时器 -->
    <aura:attribute name="timer" type="Object" default="" />

    <!-- 父子传参 -->
    <aura:attribute name="requestValue" type="String" default=""/>
    <aura:attribute name="requestType" type="String" default=""/>

    <aura:attribute name="currentUserId" type="String" default=""/>

    <!-- 控制input -->
    <aura:attribute name="isEditOrder" type="Boolean" default="false"/>
    <aura:attribute name="isEditInvoice" type="Boolean" default="false"/>
    <aura:attribute name="isEditContainer" type="Boolean" default="false"/>
    <aura:attribute name="isEditSerialNumber" type="Boolean" default="false"/>
    <aura:attribute name="isEditShipment" type="Boolean" default="false"/>
    <aura:attribute name="isError" type="Boolean" default="false"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="slds-grid slds-grid_align-space">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed loading-wrap' : 'slds-hide' }"/>
        <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
            <lightning:tabset selectedTabId="{!v.tabId}">
                <lightning:tab label="{!$Label.c.CCM_OrderList}" id="Order">
                    <div class="slds-box slds-theme_default">
                        <lightning:spinner aura:id="loading" variant="brand" alternativeText="loading" class="slds-hide"/>
                        
                        <lightning:layout multipleRows="true">
                            <!-- Order Type​ -->
                            <lightning:layoutItem size="4" class="slds-p-top_small">
                                <lightning:combobox class="ccm_display"
                                        name="orderType"
                                        value="{!v.filterObj.orderType}" 
                                        options="{!v.poTypeOptions}" 
                                        label="{!$Label.c.CCM_OrderType}"
                                        onchange="{!c.changeOrderType}"
                                />
                            </lightning:layoutItem>
                            <!-- Order Status -->
                            <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large">
                                <c:CCM_MultipleSelect label="{!$Label.c.Order_OrderStatus}"
                                                options="{!v.statusOptions}"
                                                value="{!v.filterObj.orderStatus}"
                                                aura:id='status'/>
                            </lightning:layoutItem>
                            <!-- Order Number​ -->
                            <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large">
                                <lightning:input name="" label="{!$Label.c.CCM_OrderNumber}" value="{!v.filterObj.orderNumber}" messageWhenTooLong="Please enter no more than 20 bytes." maxlength="20"/>
                            </lightning:layoutItem>
                            <!-- Customer PO -->
                            <lightning:layoutItem size="4" class="slds-p-top_small">
                                <lightning:input name="" label="{!$Label.c.Order_CustomerPO}" value="{!v.filterObj.customerPO}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255"/>
                            </lightning:layoutItem>
                            <!-- PO Number -->
                            <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large">
                                <lightning:input name="" label="{!$Label.c.Order_PONumber}" value="{!v.filterObj.PONumber}" messageWhenTooLong="Please enter no more than 20 bytes." maxlength="20"/>
                            </lightning:layoutItem>
                            <!-- Customer -->
                            <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large order-lookup">
                                <c:CCM_Community_LookUp aura:id="customer"
                                                        fieldName="{!$Label.c.Order_Customer}"
                                                        selectedValue="{!v.filterObj.customer}"
                                                        orderMethod="{!c.getOrderFromLookup}"
                                                        invoiceMethod="{!c.getInvoiceFromLookup}"
                                                        class="order-lookup-item"
                                />
                            </lightning:layoutItem>
                            <!-- Customer Number -->
                            <lightning:layoutItem size="4" class="slds-p-top_small">
                                <lightning:input name="" label="{!$Label.c.Order_CustomerNumber}" value="{!v.filterObj.customerNumber}"  messageWhenTooLong="Please enter no more than 20 bytes." maxlength="20"/>
                            </lightning:layoutItem>
                            <!-- Salesperson -->
                            <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large order-lookup">
                                <c:CCM_Community_LookUp aura:id="salesperson"
                                                        fieldName="{!$Label.c.Order_Salesperson}"
                                                        selectedValue="{!v.filterObj.salesperson}"
                                                        orderMethod="{!c.getOrderFromLookup}"
                                                        invoiceMethod="{!c.getInvoiceFromLookup}"
                                                        class="order-lookup-item"
                                />
                            </lightning:layoutItem>
                            <!-- Model # -->
                            <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large">
                                <lightning:input name="" label="{!$Label.c.Order_Model}" value="{!v.filterObj.model}"  messageWhenTooLong="Please enter no more than 20 bytes." maxlength="20"/>
                            </lightning:layoutItem>
                            <!-- Order Date -->
                            <lightning:layoutItem size="4" class="slds-p-top_small">
                                <lightning:layout>
                                    <lightning:layoutItem size="5">
                                        <lightning:input type="date" class="date-box-item" name="submitFrom"  label="{!$Label.c.Order_OrderDate}" value="{!v.filterObj.submitDateMin}"/>
                                    </lightning:layoutItem>
                                    <lightning:layoutItem size="2">
                                        <div class="date-box-b">——</div>
                                    </lightning:layoutItem>
                                    <lightning:layoutItem size="5">
                                        <lightning:input type="date" class="date-box-item slds-p-top--xx-small" name="submitTo" value="{!v.filterObj.submitDateMax}" min="{!v.filterObj.submitDateMin}"/>
                                    </lightning:layoutItem>
                                </lightning:layout>
                            </lightning:layoutItem>
                            <!-- Created By -->
                            <lightning:layoutItem size="4" class="slds-p-top_small slds-p-left--large order-lookup">
                                <c:CCM_Community_LookUp aura:id="createdBy"
                                                        fieldName="{!$Label.c.CCM_CreatedBy}"
                                                        selectedValue="{!v.filterObj.createdBy}"
                                                        orderMethod="{!c.getOrderFromLookup}"
                                                        invoiceMethod="{!c.getInvoiceFromLookup}"
                                                        class="order-lookup-item"
                                />
                            </lightning:layoutItem>
                        </lightning:layout>
                
                        <div class="slds-m-top--large slds-m-bottom--large">
                            <lightning:layout horizontalAlign="center">
                                <lightning:layoutItem >
                                    <lightning:button variant="brand" label="{!$Label.c.CCM_Search}" onclick="{! c.handleSearch }"/>
                                    <lightning:button  variant="brand" label="{!$Label.c.CCM_Reset}" onclick="{!c.doReset}"/>
                                </lightning:layoutItem>
                            </lightning:layout>
                            <c:CCM_DataTable columns="{!v.columns}" data="{!v.currentData}" />
                            <div class="slds-m-top--large slds-m-bottom--large">
                                <aura:renderIf isTrue="{! v.currentData.length > 0}">
                                    <c:CCM_ListPageFooter totalRecords="{! v.totalRecords}" />
                                </aura:renderIf>
                            </div>
                        </div>
                    </div>
                </lightning:tab>
                <lightning:tab label="{!$Label.c.CCM_SNExportList}"  id="SN">
                    <div class="buttonGroup" role="group">
                        <!-- 新增SN导出按钮 -->
                        <button class="slds-button slds-button_neutral" onclick="{!c.openSnPopup}">
                            {!$Label.c.CCM_ExportSNList}
                        </button>
                    </div>
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                        title="{!$Label.c.CCM_SNExportRequest}">&nbsp;
                                    </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-wrap">
                            <div class="slds-box slds-color__background_gray-1">
                                <c:CCM_DataTable columns="{!v.columnsSN}" data="{!v.currentSNData}"/>
                            </div>
                            <aura:renderIf isTrue="{!v.currentSNData.length > 0}">
                                <c:CCM_ListPageFooter totalRecords="{!v.totalRecords}" pageCount="{!v.pageCount}" pageNumber="{!v.pageNumber}"/>
                            </aura:renderIf>
                            <c:ccmExcelTools aura:id="excelTools" style="display: none" type="export" excelTitle="SN List" excelData="{!v.exportData}"></c:ccmExcelTools>
                        </div>
                    </div>
                </lightning:tab>
                <!-- export SN 弹框 -->
                <!-- <c:ccmExportSNPopup style="margin-right: 100px;" showFlag="{!v.modalFlag}" onclose="{!c.closeSnPopup}" sizeStyle="width: 850px;"></c:ccmExportSNPopup> -->
                <aura:if isTrue="{!v.modalFlag}">
                    <div class="dialog-wrap">
                        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                            <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; height:auto !important; transform: translate(0%, 50%);">
                                <div class="modal-header slds-modal__header">
                                    <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                                        <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                        <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                                    </button>
                                    <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_ExportSNList}</h2>
                                </div>
                                <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                                    <div class="content-wrap">
                                        <aura:if isTrue="{!v.isError}">
                                            <p class="show-error-text">
                                                <span>{!$Label.c.CCM_SNApplyRequiredTips}</span>
                                            </p>
                                        </aura:if>
                                        <!-- customer -->
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                            <c:CCM_Community_LookUp aura:id="customer"
                                                                    fieldName="{!$Label.c.CCM_Customer}"
                                                                    class="content-item-right"
                                                                    selectedValue="{!v.customerInfo}"
                                                                    onSelect="{!c.selectCustomer}"
                                            />
                                        </div>
                                        <!-- Order No. -->
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                            <c:CCM_Community_LookUp fieldName="{!$Label.c.CCM_OrderNo}"
                                                                    selectedValue="{!v.orderNo}"
                                                                    aura:id="orderNo"
                                                                    class="content-item-right"
                                                                    orderMethod="{!c.getOrderFromLookup}"
                                                                    invoiceMethod="{!c.getInvoiceFromLookup}"
                                                                    isDisabled="{!v.isEditOrder}"
                                                                    userId="{!v.currentUserId}"
                                            />
                                        </div>
                                        <!-- Invoice No. -->
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                            <c:CCM_Community_LookUp fieldName="{!$Label.c.CCM_InvoiceNo}"
                                                                    selectedValue="{!v.invoiceNo}"
                                                                    aura:id="invoiceNo"
                                                                    class="content-item-right"
                                                                    orderMethod="{!c.getOrderFromLookup}"
                                                                    invoiceMethod="{!c.getInvoiceFromLookup}"
                                                                    isDisabled="{!v.isEditInvoice}"
                                                                    userId="{!v.currentUserId}"
                                            />
                                        </div>
                                        <!-- Container No. -->
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                            <!-- <c:CCM_Community_LookUp fieldName="Container No."
                                                                    selectedValue="{!v.containerNo}"
                                                                    aura:id="containerNo"
                                                                    class="content-item-right"
                                                                    parentAction="{!c.getInfoFromLookup}"
                                            /> -->
                                            <lightning:input aura:id="containerNo" label="{!$Label.c.CCM_ContainerNo}" value="{!v.containerNo}" class="content-input" onblur="{!c.containerChange}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255" disabled="{!v.isEditContainer}"/>
                                        </div>
                                        <!-- SN -->
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                            <lightning:input aura:id="serialNumber" label="{!$Label.c.CCM_SerialNumber}" value="{!v.serialNumber}" class="content-input" onblur="{!c.serialNumberChange}" minlength="15" maxlength="15" disabled="{!v.isEditSerialNumber}"/>
                                        </div>
                                        <!-- Shipment No. -->
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                            <c:CCM_Community_LookUp fieldName="{!$Label.c.CCM_ShipmentNo}"
                                                                    selectedValue="{!v.shipmentNo}"
                                                                    aura:id="shipmentNo"
                                                                    class="content-item-right"
                                                                    shipmentMethod="{!c.getShipmentFromLookup}"
                                                                    isDisabled="{!v.isEditShipment}"
                                                                    userId="{!v.currentUserId}"
                                            />
                                        </div>
                                        <div class="content-item content-item-flex">
                                            <lightning:select label="{!$Label.c.CCM_ExportType}" value="{!v.exportType}">
                                                <aura:iteration items="{!v.exportTypeList}" var="exportType">
                                                    <option text="{!exportType}" value="{!exportType}"></option>
                                                </aura:iteration>
                                            </lightning:select>
                                        </div>
                                    </div>
                                </div>
                                <footer class="slds-modal__footer">
                                    <!-- <button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">Cancel</button>
                                    <button class="slds-button slds-button_brand" onclick="{!c.saveRecord}">Save</button> -->
                                    <!-- <lightning:button class="" variant="brand"  label="demo" title="Apply Request" onclick="{!c.getOrderFromLookup}"/> -->
                                    <lightning:button class="" variant="brand"  label="{!$Label.c.CCM_ApplyRequest}" title="Apply Request" onclick="{!c.applyRequest}"/>
                                    <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="Cancel" onclick="{!c.cancelEvent}"/>
                                </footer>
                            </div>
                        </section>
                        <div class="slds-backdrop slds-backdrop_open"></div>
                    </div>
                </aura:if>
            </lightning:tabset>
        </div>
    </div>
    
    
    
</aura:component>