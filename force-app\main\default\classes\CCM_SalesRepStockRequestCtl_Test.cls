/**
 * Honey 
 * 2023-09-09
 */
@isTest
public with sharing class CCM_SalesRepStockRequestCtl_Test {
    @isTest
    static void testQueryProductByModel() {
        // Create test data
        String PRODUCT_RECORDTYPEID = Schema.SObjectType.Product2.getRecordTypeInfosByName().get(CCM_Constants.PRODUCT).getRecordTypeId();
        Product2 testProduct = new Product2(
            Name = 'Test Product',
            Order_Model__c = 'Test Model',
            RecordTypeId = PRODUCT_RECORDTYPEID,
            Item_Description_EN__c = 'Test Description',
            IsActive = true
        );
        insert testProduct;
        Product2 testProductTool = new Product2(
            Name = 'Test testProductTool',
            Order_Model__c = 'Test testProductTool',
            RecordTypeId = PRODUCT_RECORDTYPEID,
            Item_Description_EN__c = 'Test Description',
            IsActive = true
        );
        insert testProductTool;
        Kit_Item__c objKit = new Kit_Item__c();
        objKit.Kit__c = testProduct.Id;
        objKit.vk_product__c = testProductTool.Id;
        objKit.RecordTypeId = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByName().get(CCM_Constants.Kits_And_Products).getRecordTypeId();
        insert objKit;
        Account objAccount = new Account();
        objAccount.Name = '1232';
        objAccount.AccountNumber = 'E01111';
        insert objAccount;
        Pricebook2 testPricebook = new Pricebook2(Name = 'Test Pricebook');
        insert testPricebook;
        Modifier__c modifier1 = new Modifier__c(Name = 'Test');
        insert modifier1;
        Modifier__c modifier2 = new Modifier__c(Name = 'Test2');
        insert modifier2;
        Modifier_Entry__c modifierentry = new Modifier_Entry__c(Modifier_Header_Number__c = 'M123', ExternalID__c = 'M123', Modifier__c = modifier1.Id);
        insert modifierentry;
        Pricebook_Entry__c testPricebookEntry = new Pricebook_Entry__c(PriceBook__c = testPricebook.Id, Product__c = testProduct.Id, End_Date__c = Date.today(), Start_Date__c = Date.today().addDays(-7), IsActive__c = true, UnitPrice__c = 100);
        insert testPricebookEntry;
        Pricebook_Entry__c testPricebookEntry2 = new Pricebook_Entry__c(PriceBook__c = testPricebook.Id, Product__c = testProduct.Id, End_Date__c = Date.today(), Start_Date__c = Date.today().addDays(-7), UnitPrice__c = 200);
        insert testPricebookEntry2;
        Sales_Program__c testSalesProgram = new Sales_Program__c(List_Price_1__c = testPricebook.Id, List_Price_2__c = testPricebook.Id, List_Price_3__c = testPricebook.Id, Customer__c = objAccount.Id, Price_Book__c = testPricebook.Id, Modifier_1__c = modifier1.Id, Modifier_2__c = modifier2.Id, Order_Type__c = 'Sales Order - DSV');
        insert testSalesProgram;
        MasterProductPrice__c testMasterProductPrice = new MasterProductPrice__c(Account__c = objAccount.Id, Product__c = testProduct.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today(), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = true, Modifier_Entry__c = modifierentry.Id,CurrencyIsoCode = 'EUR');
        insert testMasterProductPrice;


        // Call the method being tested
        Map<String, Object> result = CCM_SalesRepStockRequestCtl.QueryProductByModel('Test', 'EUR', UserInfo.getUserId());

        // Perform assertions to verify the expected results
        System.assertEquals(CCM_Constants.SUCCESS, result.get('state'));
        System.assertEquals('EUR', result.get('currencycode'));
        List<Map<String, Object>> productList = (List<Map<String, Object>>) result.get('data');
        System.assertEquals(1, productList.size());
        Map<String, Object> product = productList[0];
        System.assertEquals(testProduct.Id, product.get('productid'));
        System.assertEquals('Test Description', product.get('name'));
        System.assertEquals('Test Model', product.get('model'));
       
        List<Object> tools = (List<Object>) product.get('tools');
        System.assertEquals(1, tools.size());
        System.assertEquals(CCM_Constants.RED, product.get('inventorystatus'));
    }
    @isTest
    static void testQueryUserInfo() {
        // Create test data
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Sales Rep'];
        User u = new User(
            ProfileId = p.Id,
            Username = 'testuser2222' + String.valueOf(Math.random()) + '@example.com',
            Email = '<EMAIL>',
            Alias = '11',
            LastName = 'test',
            CommunityNickname = 'tuse2222r',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US'
        );

        // Call the method being tested
        system.runAs(u){
            Map<String, Object> result = CCM_SalesRepStockRequestCtl.QueryUserInfo();
            // Perform assertions to verify the expected results
            System.assertEquals(CCM_Constants.SUCCESS, result.get('state'));
            Map<String, Object> data = (Map<String, Object>) result.get('data');
            System.assertEquals(u.Id, data.get('userId'));
        }
    }

    @isTest
    static void testQueryInfo() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Sales Rep'];
        User u = new User(
            ProfileId = p.Id,
            Username = '<EMAIL>',
            Email = '<EMAIL>',
            Alias = '11',
            LastName = 'test',
            CommunityNickname = 'tuse2222r',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US'
        );
        // Call the method being tested
        List<Object> result = CCM_SalesRepStockRequestCtl.QueryInfo('test');

     
    }
    @isTest
    static void testQueryInventoryInfo() {
        Product2 objProduct = new Product2();
        objProduct.Name = 'test';
        objProduct.Order_Model__c = 'E01212';
        insert objProduct;
        // Create test data
        Inventory__c inventory = new Inventory__c(
            Product__c = objProduct.Id,
            Available_QTY__c = 10,
            RecordTypeId = Schema.SObjectType.Inventory__c.getRecordTypeInfosByDeveloperName().get('All_Inventory').getRecordTypeId(),
            Sub_Inventory_Code__c = 'EGD01'
        );
        insert inventory;
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Sales Rep'];
        User user = new User(
            ProfileId = p.Id,
            Username = 'testuser2222' + String.valueOf(Math.random()) + '@example.com',
            Email = '<EMAIL>',
            Alias = '11',
            LastName = 'test',
            CommunityNickname = 'tuse2222r',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            FederationIdentifier = 'UserId',
            LanguageLocaleKey = 'en_US'
        );
        insert user;

        // Call the method being tested
        Map<String, Object> result = CCM_SalesRepStockRequestCtl.queryInventoryInfo(objProduct.Id, user.Id, 5);

        // Perform assertions to verify the expected results
        System.assertEquals('SUCCESS', result.get('state'));
        System.assertEquals('else', result.get('else'));
        System.assertEquals(CCM_Constants.GREEN_LIGHT, result.get('inventorystatus'));
    }

    @isTest
    static void testUpsertRequestInfo() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Sales Rep'];
        User u = new User(
            ProfileId = p.Id,
            Username = 'testuser2222' + String.valueOf(Math.random()) + '@example.com',
            Email = '<EMAIL>',
            Alias = '11',
            LastName = 'test',
            CommunityNickname = 'tuse2222r',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US'
        );
        insert u;
        // Create test data
        Sub_inventory_Request__c objSubInvotory = new Sub_inventory_Request__c();
        objSubInvotory.Sales_Rep_Name__c = u.Id;
        objSubInvotory.Request_For__c = u.Id;
        objSubInvotory.CurrencyIsoCode = 'EUR';
        objSubInvotory.Request_Status__c = CCM_Constants.DRAFT;
        insert objSubInvotory;
        Product2 objProduct = new Product2();
        objProduct.Name = '12';
        objProduct.Order_Model__c = 'E1102';
        insert objProduct;

        Sub_inventory_Request_Product__c objSubInvotoryItemProduct = new Sub_inventory_Request_Product__c();
        objSubInvotoryItemProduct.Sub_Invotory_Request__c = objSubInvotory.Id;
        objSubInvotoryItemProduct.Model__c = 'E1102';
        objSubInvotoryItemProduct.Product_Description__c = objProduct.Id;
        objSubInvotoryItemProduct.Qty__c = 5;
        objSubInvotoryItemProduct.subTotal_Price__c = 10;
        objSubInvotoryItemProduct.CurrencyIsoCode = 'EUR';
        objSubInvotoryItemProduct.Is_Tools__c = false;
        insert objSubInvotoryItemProduct;

        // Call the method being tested
        Map<String, Object> result = CCM_SalesRepStockRequestCtl.UpsertRequestInfo('{"requestInfo":{"requestId":"' + objSubInvotory.Id + '","currencyCode":"EUR","salesRepId":"'+
        u.Id+'","salesRepForId":"'+ u.Id+'"},"productList":[{"model":"E1102","productId":"'+objProduct.Id
        +'","qty":5,"inventoryStatus":"","subTotal":10,"recordTypeName":""}]}');

        // Perform assertions to verify the expected results
        System.assertEquals('SUCCESS', result.get('state'));
        Map<String, Object> data = (Map<String, Object>) result.get('data');
        System.assertEquals(objSubInvotory.Id, data.get('recordId'));
    }

    @isTest
    static void testQueryRepStockByUpdate() {
        // Create test data
        Sub_inventory_Request__c objSubRequest = new Sub_inventory_Request__c();
        // Set required fields for objSubRequest
        insert objSubRequest;

        Sub_inventory_Request_Product__c objSubItem = new Sub_inventory_Request_Product__c();
        // Set required fields for objSubItem
        objSubItem.Sub_Invotory_Request__c = objSubRequest.Id;
        insert objSubItem;

        // Call the method being tested
        String result = CCM_SalesRepStockRequestCtl.queryRepStockByUpdate(objSubRequest.Id);

        // Perform assertions to verify the expected results
        // Add assertions as needed
    }

    public CCM_SalesRepStockRequestCtl_Test() {

    }
}