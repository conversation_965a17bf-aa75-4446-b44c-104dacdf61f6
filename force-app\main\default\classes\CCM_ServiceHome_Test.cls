@isTest
public with sharing class CCM_ServiceHome_Test{
    @testSetup
    static void setup(){
        // Create test data
        Account acc = new Account(
        );
        acc.Name = 'Test Company';
        acc.Country_All__c = 'AD-Andorra';
        acc.Postal_Code__c = '621044';
        acc.Consumer_Status__c = 'Active';
        acc.RecordTypeId = CCM_Constants.CommercialConsumer_RECORDTYPEID;
        acc.Consumer_Status__c = 'Waiting Customer Approval';
        insert acc;
        Account acc2 = new Account(
        );
        acc2.Name = 'test2';
        acc2.RecordTypeId = CCM_Constants.CommercialConsumer_RECORDTYPEID;
        acc2.Consumer_Status__c = 'Active';
        insert acc2;
        Account acc2TO = new Account(
        );
        acc2TO.Name = 'test2To';
        acc2TO.RecordTypeId = CCM_Constants.CommercialConsumer_RECORDTYPEID;
        acc2TO.Consumer_Status__c = 'Waiting Customer Approval';
        insert acc2TO;
        Account acc3 = new Account(
        );
        acc3.LastName = 'test3';
        acc3.RecordTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
        acc3.Consumer_Status__c = 'Active';

        Warranty_Item__c warranty1 = new Warranty_Item__c(
        );
        warranty1.Consumer__c = acc.Id;
        insert warranty1;

        Dealer_Share__c testDealerShare = new Dealer_Share__c(
        );
        insert acc3;
        testDealerShare.RelatedDealer__c = acc.Id;
        testDealerShare.RelatedConsumer__c = acc2.Id;
        insert testDealerShare;
        User thisUser = [select Id
                         from User
                         where Id = :UserInfo.getUserId()];
        Contact contact1 = new Contact(
            FirstName = 'Test', 
            Role__c = 'Fleet Manager', 
            Lastname = 'McTesty', 
            AccountId = acc.Id, 
            Email = System.now().millisecond() + '<EMAIL>'
        );
        insert contact1;

        System.runAs(thisUser){
            Profile profile1 = [Select Id
                                from Profile
                                where name = 'Partner Community User'];
            User portalAccountOwner1 = new User(
                ContactId = contact1.Id, 
                ProfileId = profile1.Id, 
                Username = System.now().millisecond() + '<EMAIL>', 
                Alias = 'batman', 
                Email = '<EMAIL>', 
                EmailEncodingKey = 'UTF-8', 
                Firstname = 'Bruce', 
                Lastname = 'Wayne', 
                LanguageLocaleKey = 'en_US', 
                LocaleSidKey = 'en_US', 
                TimeZoneSidKey = 'America/Chicago'
            );
            insert portalAccountOwner1;
        }
        Warranty_Claim__c claim = new Warranty_Claim__c(
        );
        claim.Claim_Status__c = 'Draft';
        claim.Serial_Number__c = '***************';
        insert claim;
    }
    @IsTest
    static void testUpdateUserHistory(){
        Account account = [select id
                           from Account
                           where Name = 'Test Company'
                           limit 1];
        Account account2 = [select id
                            from Account
                            where Name = 'test2'
                            limit 1];
        Test.startTest();
        String result1 = CCM_ServiceHome.updateUserHistory(account.Id, '<EMAIL>');
        String result2 = CCM_ServiceHome.updateUserHistory(account2.Id, '<EMAIL>');
        Test.stopTest();
    }
    @IsTest
    static void testDeleteUserHistory(){
        try {
            Account account = [select id
                            from Account
                            where Name = 'test2'
                            limit 1];
            Test.startTest();
            String result = CCM_ServiceHome.deleteUserHistory(account.Id);
            Test.stopTest();
            //test false id
            String result2 = CCM_ServiceHome.deleteUserHistory(UserInfo.getUserId());
        } catch (Exception e){

        }
    }
    @isTest
    static void testAllUserHistory(){
        // Test positive case
        // Create test data
        User testUser = [select Id, Contact.AccountId
                         from User
                         where Username like '%<EMAIL>'
                         order by CreatedDate Desc
                         limit 1];

        // Call the method to test
        String result = CCM_ServiceHome.allUserHistory(testUser.Id, 1, 10, '', '', '');
        CCM_ServiceHome.allUserHistoryCRM(1, 10, '', '', '');
        // Test negative case
        // Call the method to test with invalid userId
        try{
            String invalidResult = CCM_ServiceHome.allUserHistory('invalidUserId', 1, 10, '', '', '');
        } catch (Exception e){

        }
    }
    @isTest
    static void testGetUserHistoryById(){
        // Setup test data
        Account testAccount = [select id
                               from Account
                               where Name = 'Test Company'
                               limit 1];
        // Test getUserHistoryById method
        String result = CCM_ServiceHome.getUserHistoryById(testAccount.Id);
        // Setup test data
        Account testAccount2 = [select id
                                from Account
                                where Name = 'test3'
                                limit 1];
        // Test getUserHistoryById method
        String result2 = CCM_ServiceHome.getUserHistoryById(testAccount2.Id);
        // Setup test data
        Account testAccount2To = [select id
                                  from Account
                                  where Name = 'test2To'
                                  limit 1];
        // Test getUserHistoryById method
        String result3 = CCM_ServiceHome.getUserHistoryById(testAccount2To.Id);
    }
    @isTest
    static void testGetUserInfo(){
        // Test getUserInfo method
        String result = CCM_ServiceHome.getUserInfo();
    }
    @isTest
    static void testAllRegistrationHistory(){
        // Setup test data
        User testUser = [select Id, Contact.AccountId
                         from User
                         where Username like '%<EMAIL>'
                         order by CreatedDate Desc
                         limit 1];
        // Test allRegistrationHistory method
        String result1 = CCM_ServiceHome.allRegistrationHistory(testUser.Id, false, 1, 10, '', '', '', '');
        String result2 = CCM_ServiceHome.allRegistrationHistory(testUser.Id, true, 1, 10, '', '', '', '');
    }
    @isTest
    static void testAllOrder(){
        // Setup test data
        User testUser = [select Id, Contact.AccountId
                         from User
                         where Username like '%<EMAIL>'
                         order by CreatedDate Desc
                         limit 1];
        // Test allOrder method
        String result = CCM_ServiceHome.allOrder(testUser.Id, 1, 10);
    }
    @isTest
    static void testOther(){
        Account testAccount = [select id
                               from Account
                               where Name = 'Test Company'
                               limit 1];
        Warranty_Claim__c claim = [select id
                                   from Warranty_Claim__c
                                   limit 1];
        CCM_ServiceHome.IsPortal();
        CCM_ServiceHome.queryClaimList(testAccount.Id, 1, 10, '', '', '', '', '', '', '');
        CCM_ServiceHome.deleteClaim(claim.Id);
        CCM_ServiceHome.getCustomerInformation(testAccount.Id);
        CCM_ServiceHome.queryClaimInfoMation(testAccount.Id);
        CCM_ServiceHome.getUserProfileName();
        CCM_ServiceHome.getAWSSignedURL('test/test/test/test');
    }
}