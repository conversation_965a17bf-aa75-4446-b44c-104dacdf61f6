/**
 * <AUTHOR>
 * @date 2025-01-15
 * @description LDOX Interface - get connection, get file content
 */
public without sharing class CCM_LDOX_InterfaceUtil {
    
    /**
     *  login to ldox by user crediential and get connection id from response
     */
    public static String getConnectionIdByLogin() {
        String connectionId;
        String endPoint = 'https://dms.eu.chervongroup.net/InfoShare/Json/Authentication/Logon';
        Http http = new Http();
        HttpRequest req = new HttpRequest();
        req.setEndpoint(endPoint);
        req.setMethod('POST');
        String userName = 'tu_salesforce';
        String password = 'a;Ypx04=;Ej>e{U';
        LoginParam bodyParam = new LoginParam();
        bodyParam.userName = userName;
        bodyParam.passwordHashed = generateSHA512Hash(password);
        req.setBody(JSON.serialize(bodyParam));
        HttpResponse res = http.send(req);
        if(res.getStatusCode() == 200) {
            String resBody = res.getBody();
            Integer startIndex = resBody.indexOf('<ConnectionId>');
            if(startIndex > 0) {
                Integer endIndex = resBody.indexOf('</ConnectionId>', startIndex);
                if(endIndex > 0) {
                    connectionId = resBody.substring(startIndex + '<ConnectionId>'.length(), endIndex);
                }
            }
            // Map<String, Object> resJSON = (Map<String, Object>)JSON.deserializeUntyped(res.getBody());
            // if(resJSON.containsKey('LogonResult')) {
            //     resJSON = (Map<String, Object>)resJSON.get('LogonResult');
            //     if(resJSON.containsKey('ConnectionId')) {
            //         connectionId = (String)resJSON.get('ConnectionId');
            //     }
            // }
        }
        else {
            // log exception
            Log__c log = new Log__c();
            log.Name = 'LDOX Get Connection Error';
            log.ApexName__c = 'CCM_LDOX_InterfaceUtil';
            log.Error_Message__c = res.getBody();
            insert log;
        }
        return connectionId;
    }

    /**
     * get file content by connection id and document id
     */
    public static String getFileContent(String connectionId, String documentId) {
        String fileContent = '';
        String endPoint = 'https://dms.eu.chervongroup.net/InfoShare/Json//File/DownloadFile';
        Http http = new Http();
        HttpRequest req = new HttpRequest();
        req.setEndpoint(endPoint);
        req.setMethod('POST');
        GetFileContentParam bodyParam = new GetFileContentParam();
        bodyParam.connectionId = connectionId;
        bodyParam.documentId = documentId;
        bodyParam.ignoreHashValidation = true;
        req.setBody(JSON.serialize(bodyParam));
        HttpResponse res = http.send(req);
        if(res.getStatusCode() == 200) {
            Blob resultBlob = res.getBodyAsBlob();
            fileContent = EncodingUtil.base64Encode(resultBlob);
            // System.debug(res.getBody());
            // fileContent = decodeBase64(resBody);
        }
        return fileContent;
    }

    /**
     * get document id on ldox
     */
    public static void getDocumentList(String connectionId) {
        String endPoint = 'https://dms.eu.chervongroup.net/InfoShare/Json/Search/Search';
        Http http = new Http();
        HttpRequest req = new HttpRequest();
        req.setEndpoint(endPoint);
        req.setMethod('POST');
        SearchDocumentParam bodyParam = new SearchDocumentParam();
        bodyParam.connectionId = connectionId;
        req.setBody(JSON.serialize(bodyParam));
        HttpResponse res = http.send(req);
        if(res.getStatusCode() == 200) {
            Map<String, Object> resJSON = (Map<String, Object>)JSON.deserializeUntyped(res.getBody());
            if(resJSON.containsKey('SearchResult')) {
                resJSON = (Map<String, Object>)resJSON.get('SearchResult');
                if(resJSON.containsKey('Documents')) {
                    List<Object> documents = (List<Object>)resJSON.get('Documents');
                    for(Object document : documents) {
                        Map<String, Object> documentMap = (Map<String, Object>)document;
                        if(documentMap.containsKey('Id')) {
                            System.debug((String)documentMap.get('Id'));
                        }
                    }
                }
            }
        }
    }

    /**
     * get filed id on ldox
     */
    public static void getFileList(String connectionId, String documentId) {
        String endPoint = 'https://dms.eu.chervongroup.net/InfoShare/Json/Document/GetDocument';
        Http http = new Http();
        HttpRequest req = new HttpRequest();
        req.setEndpoint(endPoint);
        req.setMethod('POST');
        SearchFileParam bodyParam = new SearchFileParam();
        bodyParam.connectionId = connectionId;
        bodyParam.documentId = documentId;
        req.setBody(JSON.serialize(bodyParam));
        HttpResponse res = http.send(req);
        if(res.getStatusCode() == 200) {
            Map<String, Object> resJSON = (Map<String, Object>)JSON.deserializeUntyped(res.getBody());
            if(resJSON.containsKey('GetDocumentResult')) {
                resJSON = (Map<String, Object>)resJSON.get('GetDocumentResult');
                if(resJSON.containsKey('DocumentData')) {
                    List<Object> documents = (List<Object>)resJSON.get('DocumentData');
                    for(Object docuemnt : documents) {
                        resJSON = (Map<String, Object>)docuemnt;
                        if(resJSON.containsKey('Renditions')) {
                            List<Object> renditions = (List<Object>)resJSON.get('Renditions');
                            for(Object rendition : renditions) {
                                resJSON = (Map<String, Object>)rendition;
                                if(resJSON.containsKey('Files')) {
                                    for(Object fItem : (List<Object>)resJSON.get('Files')) {
                                        resJSON = (Map<String, Object>)fItem;
                                        if(resJSON.containsKey('Id')) {
                                            System.debug((String)resJSON.get('Id'));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private static String generateSHA512Hash(String input) {
        Blob inputBlob = Blob.valueOf(input);
        // 使用Crypto类的generateDigest方法生成SHA-512哈希
        Blob hash = Crypto.generateDigest('SHA-512', inputBlob);
        // 将哈希结果进行Base64编码
        String base64Hash = EncodingUtil.base64Encode(hash);
        return base64Hash;
    }

    private static String decodeBase64(String base64String) {
        try {
            // 将 Base64 编码的字符串转换为 Blob
            Blob decodedBlob = EncodingUtil.base64Decode(base64String);
            // 将 Blob 转换为字符串
            String decodedString = decodedBlob.toString();
            return decodedString;
        } catch (Exception e) {
            // 处理可能出现的异常，如无效的 Base64 字符串
            System.debug('Error decoding Base64 string: ' + e.getMessage());
            return null;
        }
    }


    private class LoginParam {
        public String userName;
        public String passwordHashed;
    }

    private class SearchDocumentParam {
        public String connectionId;
    }

    private class SearchFileParam {
        public String connectionId;
        public String documentId;
    }

    private class GetFileContentParam {
        public Boolean ignoreHashValidation;
        public String connectionId;
        public String documentId;
    }
}