({
    // 获取基本信息
    getBaseInfo: function (component) {
        var action = component.get("c.QueryPirchaseAndItemInfo");
        action.setParams({
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            IsProtal: false,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                component.set('v.baseInfo', result);
                component.set('v.TotalDueAmount', result.TotalDueAmount || 0);
                component.set('v.CountLine', result.CountLine || 0);
                component.set('v.orderItemList', JSON.parse(JSON.stringify(result.lstPurchaseOrderItem)));
                if(result.isSubmitted) {
                    component.set('v.notSubmit', false);
                }
                else {
                    component.set('v.notSubmit', true);
                }
                if(result.isSynced) {
                    component.set('v.notSync', false);
                }
                else {
                    component.set('v.notSync', true);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 提交事件
    handleSubmitEvent: function (component) {
        var action = component.get("c.SubmitAndSync");
        action.setParams({
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            TotalDueAmount: component.get('v.TotalDueAmount'),
            CountLine: component.get('v.CountLine'),
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                // let url = window.location.origin + '/lightning/n/PO_Detail?0.recordId=' + component.get('v.purchaseOrderId');
                // window.open(url, '_self');
                component.set('v.notSubmit', false);
                component.set('v.notSync', true);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },

    handleSync: function(component) {
        component.set('v.isBusy', true);
        let action = component.get("c.sync");
        action.setParams({'purchaseOrderId': component.get('v.purchaseOrderId')});
        action.setCallback(this, function(response){
            if(response.getState() === 'SUCCESS') {
                let syncStatus = response.getReturnValue();
                if(!syncStatus || syncStatus === 'Failed') {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Error",
                        "message": 'Sync failed, Please contact system administrator!',
                        "type": "error"
                    }).fire();
                }
                else {
                    component.set('v.notSync', false);
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Success",
                        "message": 'Sync to EBS successful!',
                        "type": "success"
                    }).fire();
                    let url = window.location.origin + '/lightning/n/Purchase_Order_List';
                    window.open(url, '_self');
                }
            }
            else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    }
})