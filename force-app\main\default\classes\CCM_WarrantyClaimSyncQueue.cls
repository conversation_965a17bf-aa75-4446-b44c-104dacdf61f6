/**
 * <AUTHOR>
 * @date 2025-07-14
 * @description Use queue to push warranty claim to EBS
 */
public with sharing class CCM_WarrantyClaimSyncQueue implements Queueable, Database.AllowsCallouts {
    private List<String> claimIdList;

    public CCM_WarrantyClaimSyncQueue(List<String> claimIdList) {
        this.claimIdList = claimIdList;
    }

    public void execute(QueueableContext context) {
        for(String claimId : this.claimIdList) {
            CCM_WarrantyRequestCallout.pushRequestInfo(claimId);    
        }
    }
}