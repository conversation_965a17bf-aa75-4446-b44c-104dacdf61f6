({
    doInit: function(component, event, helper){
        // 判断url参数
        const params = new URLSearchParams(window.location.search);
        const tabsValue =  params.get('tabsValue');
        console.log(tabsValue, 'tabsValue==========');
        if (tabsValue) {
            component.set('v.tabId', tabsValue);
        }
        var poTypeOptions = [
            // {'label': $A.get("$Label.c.CCM_PreSeasonOrder"), 'value': 'PreSeason_Order'},
            // {'label': $A.get("$Label.c.CCM_RegularOrder"), 'value': 'Regular_Order'},
            // {'label': $A.get("$Label.c.CCM_ResaleOrder"), 'value': 'Resales_Request'},
            // {'label': 'Retrun Disposal', 'value': 'Retrun_Disposal'},
            {'label': $A.get("$Label.c.EEG_Sales_Domestic"), 'value': 'EEG Sales Domestic'},
            {'label': $A.get("$Label.c.EEG_Sales_EU"), 'value': 'EEG Sales EU'},
            {'label': $A.get("$Label.c.EEG_Sales_Outside_EU"), 'value': 'EEG Sales Outside EU'},
            {'label': $A.get("$Label.c.EEG_Pre_Season_Domestic"), 'value': 'EEG Pre Season Domestic'},
            {'label': $A.get("$Label.c.EEG_Pre_Season_EU"), 'value': 'EEG Pre Season EU'},
            {'label': $A.get("$Label.c.EEG_Pre_Season_Outside_EU"), 'value': 'EEG Pre Season Outside EU'},
            {'label': $A.get("$Label.c.EEG_DropShipment_Domestic"), 'value': 'EEG DropShipment Domestic'},
            {'label': $A.get("$Label.c.EEG_DropShipment_EU"), 'value': 'EEG DropShipment EU'},
            {'label': $A.get("$Label.c.EEG_DropShipment_Outside_EU"), 'value': 'EEG DropShipment Outside EU'},
            {'label': $A.get("$Label.c.EEG_3rd_party_EU"), 'value': 'EEG 3rd party EU'},
            {'label': $A.get("$Label.c.EEG_Return_EU"), 'value': 'EEG Return EU'},
            {'label': $A.get("$Label.c.EEG_Return_Outside_EU"), 'value': 'EEG Return Outside EU'},
            {'label': $A.get("$Label.c.EEG_Bill_Only_EU"), 'value': 'EEG Bill Only EU'},
            {'label': $A.get("$Label.c.EEG_Bill_Only_Outside_EU"), 'value': 'EEG Bill Only Outside EU'},
            {'label': $A.get("$Label.c.CCM_InvoiceDamageOrder"), 'value': 'Invoice_Damage'},
            {'label': $A.get("$Label.c.EEG_Warranty_Domestic"), 'value': 'EEG Warranty Domestic'},
        ];
        component.set('v.poTypeOptions', poTypeOptions);
        var columns = [
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '80px',
                tdStyle:'text-align: left',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${row}",
                        variant:"bare",
                        showTitle:false,
                        iconName:"utility:preview",
                        alternativeText:"View",
                        onclick: component.getReference('c.doView')
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${Id}",
                        variant:"bare",
                        showTitle:false,
                        label:"",
                        iconName:"utility:edit",
                        alternativeText:"Edit",
                        class: "${editStyleCss}",
                        onclick: component.getReference("c.doEdit")
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${Id}",
                        variant:"bare",
                        iconName:"utility:delete",
                        alternativeText:"Delete",
                        class: "${deleteStyleCss}",
                        onclick: component.getReference("c.doDelete")
                    }
                }]
            },
            {label: $A.get("$Label.c.Order_Customer"), fieldName:'Customer'},
            {label: $A.get("$Label.c.Order_CustomerNumber"), fieldName:'CustomerNumber'},
            {label: $A.get("$Label.c.Order_CustomerPO"), fieldName:'CustomerPo'},
            {label: $A.get("$Label.c.Order_PONumber"), fieldName: 'PoNumber'},
            {label: $A.get("$Label.c.CCM_OrderNumber"), fieldName: 'OrderNumber'},
            {label: $A.get("$Label.c.CCM_OrderType"), fieldName: 'OrderType'},
            {label: $A.get("$Label.c.Order_OrderStatus"), fieldName: 'OrderStatus'},
            {label: $A.get("$Label.c.Order_TotalDueAmount"),
                children:[
                {
                    type: "lightning:formattedNumber",
                    attributes:{
                        value: "${TotalDueAmount}",
                        currencyCode: "${currencyCode}",
                        style:"currency"
                    }
                }]
            },
            {label: $A.get("$Label.c.Order_OrderCreatedDateinEBS"), fieldName: 'OrderCreatedDateInEBS'},
            {label: $A.get("$Label.c.CCM_CreatedBy"), fieldName: 'CreatedBy'},
        ];
        component.set('v.columns', columns);
        let newFilterObj = {
            orderType: null,
            orderStatus: [],
            orderNumber: null,
            customerPO: null,
            PONumber: null,
            customer: {Id: '', Name: ''},
            customerNumber: null,
            salesperson: {Id: '', Name: ''},
            model: null,
            submitDateMin: null,
            submitDateMax: null,
            createdBy: {Id: '', Name: ''},
        }
        component.set('v.filterObj',newFilterObj);
        // component.set('v.filterObj.orderType', null);
        // component.set('v.filterObj.orderStatus', []);
        // component.set('v.filterObj.orderNumber', null);
        // component.set('v.filterObj.customerPO', null);
        // component.set('v.filterObj.PONumber', null);
        // component.set('v.filterObj.customer', {Id: '', Name: ''});
        // component.set('v.filterObj.customerNumber', null);
        // component.set('v.filterObj.salesperson', {Id: '', Name: ''});
        // component.set('v.filterObj.model', null);
        // component.set('v.filterObj.submitDateMin', null);
        // component.set('v.filterObj.submitDateMax', null);
        // component.set('v.filterObj.createdBy', {Id: '', Name: ''});
        // var userInfo = $A.get("$SObjectType.CurrentUser");
        // component.set('v.currentUserId', userInfo.Id);
        // console.log(component.get('v.currentUserId'), 'currentUserId===========');
        
        var columnsSN= [
            {   
                label: $A.get("$Label.c.CCM_Action"),
                width: '50px',
                tdStyle: 'text-align: center',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${rowData}",
                        variant:"bare",
                        iconName:"utility:arrow_bottom",
                        alternativeText:"download",
                        class: "${isShowBtn}",
                        onclick: component.getReference('c.doDownload')
                    }
                },
          ]},
            {label: $A.get("$Label.c.CCM_RequestTime"), fieldName: 'requestDate'},
            {label: $A.get("$Label.c.CCM_RequestType"), fieldName: 'exportType'},
            {label: $A.get("$Label.c.CCM_RequestUser"), fieldName:'requestUser'},
            {label: $A.get("$Label.c.CCM_Type"), fieldName: 'type'},
            {label: $A.get("$Label.c.CCM_RequestValue"), fieldName: 'requestValue'},
            {label: $A.get("$Label.c.CCM_TotalNum"), fieldName: 'totalNum'},
            {label: $A.get("$Label.c.CCM_Status"), fieldName: 'status'},
            {label: $A.get("$Label.c.CCM_Error"), fieldName: 'errorMsg', tdStyle: 'color: red; max-width: 300px;'}, //Added by Zoe
        ];
        component.set('v.columnsSN',columnsSN);
        helper.loading(component, true);
        helper.getObjectRecords(component, event, helper);
        // 获取status list
        helper.getStatusList(component);

        // 初始化调用SN列表
        // helper.getSNTableData(component,event, helper);

    },
    handleSearch : function(component, event, helper){
        component.set('v.pageNumber', 1);
        helper.getObjectRecords(component, event, helper, false);
    },
    // 子组件lookup传值
    getOrderFromLookup : function(component, event, helper){
        component.set('v.invoiceNo', '');
        const childCmp = component.find('orderNo');
        console.log(childCmp.get('v.requestType'), JSON.parse(JSON.stringify(component.get('v.orderNo'))), 'order子组件lookup传值==========');
        const requestType = childCmp.get('v.requestType');
        const orderValue = JSON.parse(JSON.stringify(component.get('v.orderNo')));
        // 控制输入框
        if (requestType === 'Order' && orderValue.Id) {
            console.log('只显示order============');
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', true);
            component.set('v.isEditContainer', true);
            component.set('v.isEditSerialNumber', true);
            component.set('v.isError', false);
        } else {
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', false);
            component.set('v.isEditSerialNumber', false);
        }
    },
    containerChange : function(component, event, helper){
        console.log(event.currentTarget.value, 'containerInput==========');
        if (!event.currentTarget.value) {
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', false);
            component.set('v.isEditSerialNumber', false);

        } else {
            component.set('v.isEditOrder', true);
            component.set('v.isEditInvoice', true);
            component.set('v.isEditContainer', false);
            component.set('v.isEditSerialNumber', true);
            component.set('v.isError', false);
        }
    },
    // 修改SN
    serialNumberChange : function(component, event, helper){
        let serialNumber = component.get('v.serialNumber');
        // sn 校验
        if (!serialNumber) {
            return;
        }
        if (serialNumber.length < 15 || serialNumber[0] != 'E') {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": "Please enter the correct serial number!",
                "type": "Warning"
            }).fire();
            return;
        }
        if (!serialNumber) {
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', false);
            component.set('v.isEditSerialNumber', false);
        } else {
            component.set('v.isEditOrder', true);
            component.set('v.isEditInvoice', true);
            component.set('v.isEditContainer', true);
            component.set('v.isEditSerialNumber', false);
            component.set('v.isError', false);
        }
    },
    getInvoiceFromLookup : function(component, event, helper){
        component.set('v.orderNo', '');
        const childCmp = component.find('invoiceNo');
        console.log(childCmp.get('v.requestType'), JSON.parse(JSON.stringify(component.get('v.invoiceNo'))), 'invoice子组件lookup传值==========');
        const requestType = childCmp.get('v.requestType');
        const orderValue = JSON.parse(JSON.stringify(component.get('v.invoiceNo')));
        // 控制输入框
        if (requestType === 'Invoice' && orderValue.Id) {
            console.log('只显示Invoice============');
            component.set('v.isEditOrder', true);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', true);
            component.set('v.isEditSerialNumber', true);
            component.set('v.isError', false);
        } else {
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', false);
            component.set('v.isEditSerialNumber', false);
        }
    },
    doReset : function(component, event, helper){
        let newFilterObj = {
            orderType: null,
            orderStatus: [],
            orderNumber: null,
            customerPO: null,
            PONumber: null,
            customer: {Id: '', Name: ''},
            customerNumber: null,
            salesperson: {Id: '', Name: ''},
            model: null,
            submitDateMin: null,
            submitDateMax: null,
            createdBy: {Id: '', Name: ''},
        }
            // console.log(newFilterObj, 'doReset--------------1');
            // component.set('v.filterObj',{});
            // component.set('v.filterObj.orderType', null);
            // component.set('v.filterObj.orderStatus', []);
            // component.set('v.filterObj.orderNumber', null);
            // component.set('v.filterObj.customerPO', null);
            // component.set('v.filterObj.PONumber', null);
            // component.set('v.filterObj.customer', {Id: '', Name: ''});
            // component.set('v.filterObj.customerNumber', null);
            // component.set('v.filterObj.salesperson', {Id: '', Name: ''});
            // component.set('v.filterObj.model', null);
            // component.set('v.filterObj.submitDateMin', null);
            // component.set('v.filterObj.submitDateMax', null);
            // component.set('v.filterObj.createdBy', {Id: '', Name: ''});
        component.set('v.filterObj', newFilterObj);
        helper.getObjectRecords(component, event, helper, false);
        console.log(component.get('v.filterObj'), 'doReset--------------2');
    },
    pageChange: function(component, event, helper) {
        const tabStr = component.get('v.tabId');
        var pageNumber = event.getParam("pageNumber");
        component.set("v.pageNumber",pageNumber);
        switch (tabStr) {
            case 'Order':
                helper.getObjectRecords(component, event, helper, true);
                break;
            case 'SN':
                component.set('v.isBusy', true);
                helper.getSNTableData(component,event, helper);
                // component.set('v.isBusy', true);
                break;
            default:
                break;
        }
        event.stopPropagation();
    },
    pageCountChange : function(component, event, helper){
        const tabStr = component.get('v.tabId');
        var pageCount = event.getParam("pageCount");
        component.set("v.pageCount",pageCount);
        component.set("v.pageNumber", 1);
        switch (tabStr) {
            case 'Order':
                helper.getObjectRecords(component, event, helper);
                break;
            case 'SN':
                component.set('v.isBusy', true);
                helper.getSNTableData(component,event, helper);
                break;
            default:
                break;
        }
        event.stopPropagation();
    },
    doView : function(component, event, helper){
        let rowInfo = event.getSource().get('v.value');
        let url = '';
        console.log(JSON.stringify(rowInfo), 'rowInfo--------------');
        // 判断是否同步后类型
        if (rowInfo.recordType == 'Resales_Request') {
            url = window.location.origin + '/lightning/n/Re_Sales_Sync_Detail?0.recordId=' + rowInfo.Id;
            window.open(url, '_self');
        } else if (rowInfo.recordType == 'Invoice_Damage') {
            url = window.location.origin + '/lightning/n/Invoice_Damage_After_Sync_Detail?0.recordId=' + rowInfo.Id;
            window.open(url, '_self');
        } else if (rowInfo.recordType == 'Warranty_Order') {
            let url = window.location.origin + '/lightning/n/Warranty_Order?0.recordId=' + rowInfo.Id + '&0.accId=' + rowInfo.customerId;
            window.open(url, '_self');
        } else {
            helper.getObjectInfo(component, event, helper, rowInfo.Id);
        }
    },

    // SN导出按钮
    openSnPopup : function(component, event, helper){
        component.set("v.modalFlag", true);
        // 关闭计时器
        clearInterval(component.get('v.timer'));
        console.log('清除计时器===========');
    },
    // tabs点击事件
    handleChange: function(component, event, helper){
        // 关闭计时器
        clearInterval(component.get('v.timer'));
        var selected = component.get("v.tabId");
        component.set('v.tabId', selected);
        // 重置分页
        component.set('v.pageNumber', 1);
        component.set('v.pageCount', 10);
        component.set('v.totalRecords', 0);
        switch (selected) {
            case 'Order':
                console.log('Order=========');
                helper.getObjectRecords(component, event, helper, true);
                break;
            case 'SN':
                console.log('SN=========');
                component.set('v.isBusy', true);
                helper.getSNTableData(component,event, helper);
                // 计时器（每10s刷新表格数据）
                const timer = setInterval(() => {
                    console.log('计时器===========');
                    helper.getSNTableData(component,event, helper);
                }, 15000);
                component.set('v.timer', timer);
                break;
            default:
                break;
        }
    },
    // 提交事件
    applyRequest : function(component, event, helper){
        // 四选一参数判断
        const orderNo = JSON.parse(JSON.stringify(component.get('v.orderNo')));
        const invoiceNo = JSON.parse(JSON.stringify(component.get('v.invoiceNo')));
        const containerNo = JSON.parse(JSON.stringify(component.get('v.containerNo')));
        const serialNumber = JSON.parse(JSON.stringify(component.get('v.serialNumber')));
        console.log(orderNo.Id, invoiceNo.Id, containerNo, '四选一参数判断==========');
        if (orderNo.Id) {
            component.set('v.requestValue', orderNo.Name);
            component.set('v.requestType', 'Order');
            console.log(orderNo.Id, 'Order===========');
        } else if (invoiceNo.Id) {
            component.set('v.requestValue', invoiceNo.Name);
            component.set('v.requestType', 'Invoice');
            console.log(invoiceNo.Id, 'Invoice===========');
        } else if (containerNo) {
            component.set('v.requestValue', containerNo);
            component.set('v.requestType', 'Container');
            console.log(containerNo, 'container===========');
        }  else if (serialNumber) {
            component.set('v.requestValue', serialNumber);
            component.set('v.requestType', 'SN');
            console.log(serialNumber, 'Sn===========');
        } else {
            component.set('v.isError', true);
            return;
        }
        const year = new Date().getFullYear().toString();
        const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
        const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
        const hour = new Date().getHours().toString().length < 2 ? '0' + new Date().getHours() : new Date().getHours();
        const minutes = new Date().getMinutes().toString().length < 2 ? '0' + new Date().getMinutes() : new Date().getMinutes();
        const seconds = new Date().getSeconds().toString().length < 2 ? '0' + new Date().getSeconds() : new Date().getSeconds();
        const milliseconds = new Date().getMilliseconds().toString();
        // 毫秒补零
        if (milliseconds.length === 1) {
            milliseconds = '00' + milliseconds;
        } else if (milliseconds.length === 2) {
            milliseconds = '0' + milliseconds;
        }
        console.log('赋值===========');
        const requestDate = `${year}-${month}-${day} ${hour}:${minutes}:${seconds}`;
        const requestId = `${year}${month}${day}${hour}${minutes}${seconds}${milliseconds}`;
        const requestValue = component.get('v.requestValue');
        const type = component.get('v.requestType');
        const exportType = component.get('v.exportType');
        const exportParams = {
            requestDate,
            requestId,
            requestValue,
            type,
            exportType,
            orgType: 'CRM',
        };
        console.log(JSON.stringify(exportParams), 'exportParams===========');
        component.set('v.isBusy', true);
        component.set("v.modalFlag", false);
        component.set('v.isEditOrder', false);
        component.set('v.isEditInvoice', false);
        component.set('v.isEditContainer', false);
        component.set('v.isEditSerialNumber', false);
        component.set('v.isError', false);
        var action = component.get("c.UpsertExportRecord");
        action.setParams({
            exportJsonString: JSON.stringify(exportParams),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                helper.showToast('Success', $A.get("$Label.c.CCM_SNApplySuccessTips"), 10000);
                // 置空
                component.set('v.orderNo', {});
                component.set('v.containerNo', '');
                component.set('v.invoiceNo', {});
                // 刷新表格
                const tabStr = component.get('v.tabId');
                if (tabStr === 'SN') {
                    helper.getSNTableData(component,event, helper);
                    // 计时器（每10s刷新表格数据）
                    const timer = setInterval(() => {
                        console.log('计时器===========');
                        helper.getSNTableData(component,event, helper);
                    }, 10000);
                    component.set('v.timer', timer);
                } else {
                    component.set('v.tabId', 'SN');
                }
            } else {
                helper.showToast('Failed', $A.get("$Label.c.CCM_SNApplyFailTips"), 10000);
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 关闭sn导出弹框
    cancelEvent: function(component, event, helper) {
        component.set("v.modalFlag", false);
        component.set('v.isEditOrder', false);
        component.set('v.isEditInvoice', false);
        component.set('v.isEditContainer', false);
        component.set('v.isError', false);
        // 置空
        component.set('v.orderNo', {});
        component.set('v.containerNo', '');
        component.set('v.invoiceNo', {});
        // 计时器（每10s刷新表格数据）
        const timer = setInterval(() => {
            console.log('计时器===========');
            helper.getSNTableData(component,event, helper);
        }, 10000);
        component.set('v.timer', timer);
    },
    // 下载SN Excel
    doDownload : function(component, event, helper){
        component.set('v.isBusy', true);
        var rowData = event.getSource().get('v.value');
        console.log(rowData, 'rowData=============');
        helper.getSNExportData(component, rowData.id, rowData.exportType, 'SN_Report_'+rowData.type+'_' + rowData.requestValue);
    },
    // orderType change
    changeOrderType : function(component, event, helper){
        let selectedOptionValue = event.getParam("value");
        console.log(selectedOptionValue, 'orderType=======');
        component.set('v.filterObj.orderType', selectedOptionValue);
    },
    // Change Serial Number
    changeSerialNumber: function(component, event, helper){
        let serialNumber = component.get('v.serialNumber');
        // sn 校验
        if (!serialNumber) {
            return;
        }
        if (serialNumber.length < 15 || serialNumber[0] != 'E') {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": "Please enter the correct serial number!",
                "type": "Warning"
            }).fire();
            return;
        }
    },
    selectCustomer: function(component, event, helper){
        let customerInfo = component.get('v.customerInfo');
        console.log(JSON.stringify(customerInfo), 'customerInfo========');
        if (customerInfo.Id) { 
            component.set('v.currentUserId', customerInfo.Id);
        } else {
            component.set('v.currentUserId', '');
        }
    },
})