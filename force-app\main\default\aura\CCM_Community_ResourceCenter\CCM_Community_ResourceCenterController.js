({
    doInit: function (component, event, helper) {
        // contact 联系方式
        // const egoDealerSupport = $A.get("$Label.c.CCM_EGO_Dealer_Support").split(";");
        // const powerToolDealerSupport = $A.get("$Label.c.CCM_Power_Tool_Dealer_Support").split(";");
        // const partsHelpDesk = $A.get("$Label.c.CCM_Parts_Helpdesk").split(";");
        // component.set("v.egoDealerTel", egoDealerSupport[0]);
        // component.set("v.egoDealerEmail", egoDealerSupport[1]);

        // component.set("v.powerToolDealerTel", powerToolDealerSupport[0]);
        // component.set("v.powerToolDealerEmail", powerToolDealerSupport[1]);
        
        // component.set("v.partsHelpDeskTel", partsHelpDesk[0]);
        // component.set("v.partsHelpDeskEmail", partsHelpDesk[1]);

        const insideSalesContactInfo = $A.get('$Label.c.CCM_InsideSalesContactInfo');
        const insideSalesContactInfos = insideSalesContactInfo.split(";");
        component.set('v.insideSales', insideSalesContactInfos[0]);
        component.set('v.insideSalesContactTel', insideSalesContactInfos[2]);
        component.set('v.insideSalesContactEmail', insideSalesContactInfos[1]);

        const serviceContactInfo = $A.get('$Label.c.CCM_ServiceContactInfo');
        const serviceContactInfos = serviceContactInfo.split(";");
        component.set('v.service', serviceContactInfos[0]);
        component.set('v.serviceContactTel', serviceContactInfos[2]);
        component.set('v.serviceContactEmail', serviceContactInfos[1]);

        const accountingContactInfo = $A.get('$Label.c.CCM_AccountingContactInfo');
        const accountingContactInfos = accountingContactInfo.split(";");
        component.set('v.accounting', accountingContactInfos[0]);
        component.set('v.accountingContactTel', accountingContactInfos[2]);
        component.set('v.accountingContactEmail', accountingContactInfos[1]);

        const marketingContactInfo = $A.get('$Label.c.CCM_MarketingContactInfo');
        const marketingContactInfos = marketingContactInfo.split(";");
        component.set('v.marketing', marketingContactInfos[0]);
        component.set('v.marketingContactTel', marketingContactInfos[2]);
        component.set('v.marketingContactEmail', marketingContactInfos[1]);

        const ecudeContactInfo = $A.get('$Label.c.CCM_ECubeContactInfo');
        const ecudeContactInfos = ecudeContactInfo.split(";");
        component.set('v.ecube', ecudeContactInfos[0]);
        component.set('v.ecubeContactTel', ecudeContactInfos[2]);
        component.set('v.ecubeContactEmail', ecudeContactInfos[1]);

        var selectedTabId = helper.getUrlParameter('selectedTab');
        if (selectedTabId) {
            component.set("v.selectedTab", selectedTabId);
        }
    }
})