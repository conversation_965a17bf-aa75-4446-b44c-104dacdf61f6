({
    doInit: function (component, event, helper) {
        // contact 联系方式
        const egoDealerSupport = $A.get("$Label.c.CCM_EGO_Dealer_Support").split(";");
        const powerToolDealerSupport = $A.get("$Label.c.CCM_Power_Tool_Dealer_Support").split(";");
        const partsHelpDesk = $A.get("$Label.c.CCM_Parts_Helpdesk").split(";");
        component.set("v.egoDealerTel", egoDealerSupport[0]);
        component.set("v.egoDealerEmail", egoDealerSupport[1]);

        component.set("v.powerToolDealerTel", powerToolDealerSupport[0]);
        component.set("v.powerToolDealerEmail", powerToolDealerSupport[1]);
        
        component.set("v.partsHelpDeskTel", partsHelpDesk[0]);
        component.set("v.partsHelpDeskEmail", partsHelpDesk[1]);

        var selectedTabId = helper.getUrlParameter('selectedTab');
        if (selectedTabId) {
            component.set("v.selectedTab", selectedTabId);
        }
    }
})