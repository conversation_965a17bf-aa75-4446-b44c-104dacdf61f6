({
    doInit: function(component, event, helper){
        // add by haibo
        // 配置picklist
        component.set('v.brandOptions', [
            {
                label: 'EGO', 
                value: 'EGO'
            }
        ]);
        component.set('v.consumerTypeOptions', [
            {
                label: $A.get("$Label.c.CCM_Residential"),
                value: 'Residential'
            },
            {
                label: $A.get("$Label.c.CCM_Commercial"), 
                value: 'Commercial'
            }
        ]);
        component.set('v.serviceOption', [
            {
                label: $A.get("$Label.c.CCM_Replacement"), 
                value: 'Replacement'
            },
            {
                label: $A.get("$Label.c.CCM_Repair"), 
                value: 'Repair'
            }
        ]);
        component.set('v.replacementOption', [
            {
                label: $A.get("$Label.c.CCM_CreditMemo"), 
                value: 'Credit Memo'
            },
            // {
            //     label: $A.get("$Label.c.CCM_ForAFreeTool"),
            //     value: 'For a free tools'
            // }
        ]);
        component.set('v.replacementByDistributor', [
            {
                label: $A.get("$Label.c.CCM_CreditMemo"), 
                value: 'Credit Memo'
            },
        ]);
        component.set('v.replacementByDealer', [
            // {
            //     label: $A.get("$Label.c.CCM_ForAFreeTool"), 
            //     value: 'For a free tools'
            // }
            {
                label: $A.get("$Label.c.CCM_CreditMemo"), 
                value: 'Credit Memo'
            },
        ]);
        component.set('v.repairOption', [
            {
                label: $A.get("$Label.c.CCM_Parts"), 
                value: 'Parts'
            },
            {
                label: $A.get("$Label.c.CCM_LaborTimeOnly"), 
                value: 'Labor Time Only'
            }
        ]);
        component.set('v.repairByDistributor', [
            {
                label: $A.get("$Label.c.CCM_Parts"), 
                value: 'Parts'
            },
        ]);
        component.set('v.repairByDealer', [
            {
                label: $A.get("$Label.c.CCM_LaborTimeOnly"), 
                value: 'Labor Time Only'
            }
        ]);
        // 判断portal或CRM
        helper.getEnvironmentType(component);
        
        // avoid too many actions, lead to action handler abandon
        window.setTimeout(
           $A.getCallback(function() {
            // 获取picklist下拉
            helper.getPicklist(component, 'Failure_Code__c');
            //获取projectInfo
            // helper.getProjectInfoBySn(component);
        }),2000);
        // helper.preFillInforFromEligibilityCheck(component);
        
    },
    // add haibo
    // change Drop-off Date
    changeDropOffDate: function(component, event, helper){
        console.log('change Drop-off Date-----------');
        helper.getElementRequiredError(component, 'dropOffDate');
        // 获取提示信息
        helper.getBaseInfoTips(component);
    },
    // change Repair Date
    changeRepairDate: function(component, event, helper){
        helper.getElementRequiredError(component, 'repairDate');
        // 获取提示信息
        helper.getBaseInfoTips(component);
    },
    // change Brand
    changeBrand: function(component, event, helper){
        helper.getElementRequiredError(component, 'brand');
        // 获取提示信息
        helper.getBaseInfoTips(component);
    },
    // select model number
    selectModelNumber: function(component, event, helper){
        let modelNumberInfo = component.get('v.modelNumberInfo');
        const modelNumberElement = component.find('modelNumber');
        const modelNumberRequiredText = component.find('modelNumber-error-required');
        if (modelNumberInfo.Id) {
            $A.util.removeClass(modelNumberElement, 'field-error');
            $A.util.addClass(modelNumberRequiredText, 'slds-hide');
            component.set('v.modelNumber', modelNumberInfo.Name);
            component.set('v.productId', modelNumberInfo.productId);
            component.set('v.productName', modelNumberInfo.productName);
            // component.set('v.laborTime', modelNumberInfo.laborTime);
            // component.set('v.unitPrice', modelNumberInfo.unitPrice);
            // 获取关联product信息
            component.set('v.modelProductInfo', {
                unitPrice: modelNumberInfo.unitPrice,
                productId: modelNumberInfo.productId,
                productName: modelNumberInfo.productName,
                noCalculate: modelNumberInfo.noCalculate,
                modelNumber: modelNumberInfo.modelNumber,
            })
            // 判断是否为Credit Memo
            helper.changeScenario(component);
        } else {
            $A.util.addClass(modelNumberElement, 'field-error');
            $A.util.removeClass(modelNumberRequiredText, 'slds-hide');
            component.set('v.modelNumber', '');
            component.set('v.productId', '');
            component.set('v.productName', '');
            // component.set('v.laborTime', '');
            // component.set('v.unitPrice', '');
        }
        // 获取发票信息
        helper.getReceiptInfo(component);
        // 获取提示信息
        helper.getBaseInfoTips(component);
    },

    // select new model number for replacement
    selectNewModel: function(component, event, helper) {
        let modelNumberInfo = component.get('v.replacementNewModel');
        component.set('v.modelProductInfo', {
            unitPrice: modelNumberInfo.unitPrice,
            productId: modelNumberInfo.productId,
            productName: modelNumberInfo.productName,
            noCalculate: modelNumberInfo.noCalculate,
            modelNumber: modelNumberInfo.modelNumber,
        });
        helper.changeScenario(component);
    },

    // Change Serial Number
    changeSerialNumber: function(component, event, helper){
        let serialNumber = component.get('v.serialNumber');
        helper.isAdditionalRequired(component);
        // sn 校验
        if (!serialNumber) {
            return;
        }
        if (serialNumber.length < 15 || serialNumber[0] != 'E') {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_EnterCorrectSnMsg"),
                "type": "Warning"
            }).fire();
            return;
        }
        // 获取发票信息
        // helper.getReceiptInfo(component);
        // 获取提示信息
        // helper.getBaseInfoTips(component);

        helper.getModelInfo(component, serialNumber, true, false);

        // 获取Project信息
        helper.getProjectInfoBySn(component);
    },

    changeNewReplacementSerialNumber: function(component, event, helper) {
        let newReplacementSerialNumber = component.get('v.newReplacementSerialNumber');
        // sn 校验
        if (!newReplacementSerialNumber) {
            return;
        }
        if (newReplacementSerialNumber.length < 15 || newReplacementSerialNumber[0] != 'E') {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_EnterCorrectSnMsg"),
                "type": "Warning"
            }).fire();
            return;
        }

        helper.getModelInfo(component, newReplacementSerialNumber, false, true);
    },

    changeNewSerialNumber: function(component, event, helper){
        let newSerialNumber = component.get('v.newSerialNumber');
        // sn 校验
        if (!newSerialNumber) {
            return;
        }
        if (newSerialNumber.length < 15 || newSerialNumber[0] != 'E') {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_EnterCorrectSnMsg"),
                "type": "Warning"
            }).fire();
            return;
        }
    },
    // Change Email Address
    changeEmailAddress: function(component, event, helper){
        // 获取提示信息
        helper.getBaseInfoTips(component);
        // 获取发票信息
        helper.getReceiptInfo(component);
    },
    // 发票预览
    toReceiptDetail: function(component, event, helper){
        let receiptName = component.get('v.receiptName');
        let receiptLink = component.get('v.receiptLink');
        if (receiptName && receiptLink) {
            helper.showReceiptUrl(component, receiptLink);
        }
    },
    // 发票上传
    handleFilesChange : function(component, event, helper) {
        var files = event.getSource().get("v.files");
        let fileType = files[0].type.slice(-3);
        // 校验发票类型
        console.log(fileType.toLowerCase(), '校验发票类型-------------');
        if (fileType.toLowerCase() !== 'pdf' && fileType.toLowerCase() !== 'png') {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_InvoiceFormatIncorrectMsg"),
                "type": "Warning"
            }).fire();
            return;
        }
        helper.fileByBase64ForReceipt(component, files, fileType);
    },
    // 删除发票
    deleteReceipt : function(component, event, helper){
        component.set('v.receiptUploadInfo', {});
    },
    // 修改 Service type
    changeServiceType: function(component, event, helper){
        let serviceType = component.get('v.serviceType');
        // 判断两端，修改默认值
        let isPortal = component.get('v.isPortal');
        let distributorOrDealer = component.get('v.distributorOrDealer');
        if (serviceType == 'Replacement') {
            // if (isPortal) {
            //     if (distributorOrDealer == 'Dealer') {
            //         component.set('v.replacementType', 'For a free tools');
            //     } else if (distributorOrDealer == 'Distributor') {
            //         component.set('v.replacementType', 'Credit Memo');
            //     }
            // } else {
            //     component.set('v.replacementType', 'For a free tools'); 
            // }
            component.set('v.replacementType', 'Credit Memo');
            component.set('v.repairType', ''); 
        } else {
            component.set('v.repairType', 'Parts');
            component.set('v.replacementType', '');
        }
        
        // 判断当前Scenario
        helper.changeScenario(component);
    },
    // 修改 Replacement Type
    changeReplacementType: function(component, event, helper){
        // 判断当前Scenario
        helper.changeScenario(component);
    },
    // 修改 Replacement Type
    changeRepairType: function(component, event, helper){
        // 判断当前Scenario
        helper.changeScenario(component);
    },
    // 修改Description
    changeDescription: function(component, event, helper){
        helper.getElementRequiredError(component, 'description');
    },
    // 校验Address必填
    selectShipAddressInfo: function(component, event, helper){
        let addressInfo = component.get('v.addressInfo');
        console.log(JSON.stringify(addressInfo), 'addressInfo========');
        const addressInfoElement = component.find('address');
        const addressInfoRequiredText = component.find('address-error-required');
        if (addressInfo.Id) {
            $A.util.removeClass(addressInfoElement, 'field-error');
            $A.util.addClass(addressInfoRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(addressInfoElement, 'field-error');
            $A.util.removeClass(addressInfoRequiredText, 'slds-hide');
        }
    },
    // 校验 Bill to Address必填
    selectBillAddressInfo: function(component, event, helper){
        let billAddressInfo = component.get('v.billAddressInfo');
        console.log(JSON.stringify(billAddressInfo), 'billAddressInfo========');
        const billAddressInfoElement = component.find('billToAddress');
        const billAddressInfoRequiredText = component.find('billToAddress-error-required');
        if (billAddressInfo.Id) {
            $A.util.removeClass(billAddressInfoElement, 'field-error');
            $A.util.addClass(billAddressInfoRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(billAddressInfoElement, 'field-error');
            $A.util.removeClass(billAddressInfoRequiredText, 'slds-hide');
        }
    },
    // 计算总价
    getFinialLaborHour: function(component, event, helper){
        // 计算总价
        helper.calculateTotalHourAndPrice(component);
    },
    // 修改 Failure Code
    changeFailureCode: function(component, event, helper){
        helper.getElementRequiredError(component, 'failureCode');
        let failureCode = component.get('v.failureCode');
        let batteryDiagnosticScope = component.get('v.batteryDiagnosticScope');
        if(failureCode === '0 - Battery/Charger') {
            if(batteryDiagnosticScope) {
                // set Diagnostic Flat Rate
                let repairablePartsInfo = {
                    Name: 'Diagnostic Flat Rate',
                    unitPrice: 15,
                    productId: 'Diagnostic Flat Rate',
                    productName: 'Diagnostic Flat Rate',
                    noCalculate: false,
                    modelNumber: 'Diagnostic Flat Rate',
                    laborTime: 0,
                };
                component.set('v.repairablePartsInfo', repairablePartsInfo);
                component.set('v.partProductInfo', {
                    unitPrice: repairablePartsInfo.unitPrice,
                    productId: repairablePartsInfo.productId,
                    productName: repairablePartsInfo.productName,
                    noCalculate: repairablePartsInfo.noCalculate,
                    modelNumber: repairablePartsInfo.modelNumber,
                    laborTime: repairablePartsInfo.laborTime,
                });
            }
        }
        else {
            // remove Diagnostic Flat Rate
            component.set('v.repairablePartsInfo', {});
            component.set('v.partProductInfo', {});
            // component.set('v.partsItemList', []);
        }
        helper.changeScenario(component);
    },
    changeFailureCode4: function(component, event, helper){
        console.log('修改 Failure Code---------');
        helper.getElementRequiredError(component, 'failureCode4');
    },
    // 修改 Repairable Parts
    selectRepairableParts: function(component, event, helper){
        // 判断是否有modelNumber
        let modelNumber = component.get('v.modelNumber');
        if (!modelNumber) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectModelNumberMsg"),
                "type": "Warning"
            }).fire();
            return;
        }
        let repairablePartsInfo = component.get('v.repairablePartsInfo');
        const repairablePartsElement = component.find('repairableParts');
        const repairablePartsRequiredText = component.find('repairableParts-error-required');
        if (repairablePartsInfo.Id) {
            $A.util.removeClass(repairablePartsElement, 'field-error');
            $A.util.addClass(repairablePartsRequiredText, 'slds-hide');
            component.set('v.laborTime', Number(repairablePartsInfo.laborTime));
            // 获取关联parts信息
            component.set('v.partProductInfo', {
                unitPrice: repairablePartsInfo.unitPrice,
                productId: repairablePartsInfo.productId,
                productName: repairablePartsInfo.productName,
                noCalculate: repairablePartsInfo.noCalculate,
                modelNumber: repairablePartsInfo.modelNumber,
                laborTime: repairablePartsInfo.laborTime,
            })
            // 判断是否为Credit Memo
            helper.changeScenario(component);
        } else {
            $A.util.addClass(repairablePartsElement, 'field-error');
            $A.util.removeClass(repairablePartsRequiredText, 'slds-hide');
            component.set('v.laborTime', 0);
        }
    },
    // parts table 选择产品
    selectRepairablePartsForTable: function(component, event, helper){
        let selectedValue = event.getSource().get('v.selectedValue');
        let tableIndex = event.getSource().get('v.class');
        let partsItemList = component.get('v.partsItemList');
        console.log(JSON.stringify(selectedValue), tableIndex, '添加parts---------');
        partsItemList.forEach((item, index)=>{
            if (index == tableIndex) {
                item.partName = selectedValue.Name;
                item.itemNumber = selectedValue.modelNumber;
                item.unitPrice = selectedValue.unitPrice;
                item.noCalculate = selectedValue.noCalculate;
                item.laborTime = selectedValue.laborTime;
                item.total = Number(selectedValue.unitPrice) * item.quantity;
            }
        });
        component.set('v.partsItemList', [...partsItemList]);
        helper.calculateTotalLaborTime(component);
        // 计算总价
        helper.calculateTotalHourAndPrice(component);
    },
    // 添加parts 事件
    addItionalParts:function(component, event, helper){
        // 判断是否选择model number
        let modelNumber = component.get('v.modelNumber');
        if (!modelNumber) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectModelNumberMsg"),
                "type": "Warning"
            }).fire();
            return;
        }
        let newPart = {
            partInfo: {},
            partName: '',
            itemNumber: '',
            quantity: 1,
            unitPrice: '',
            total: '',
            noCalculate: false,
            type: 'additionalPart'
        };
        let partsItemList = component.get('v.partsItemList');
        partsItemList.push(newPart);
        console.log(JSON.parse(JSON.stringify(partsItemList)), 'partsItemList-------------');
        component.set('v.partsItemList', partsItemList);
        // 计算总价
        helper.calculateTotalHourAndPrice(component);
    },
    // 修改单行parts数量
    calculatePartsTotalPrice:function(component, event, helper){
        let tableIndex = event.getSource().get('v.id');
        let qty = event.getSource().get('v.value');
        let partsItemList = component.get('v.partsItemList');
        console.log(tableIndex, qty, 'tableIndex-----------');
        partsItemList.forEach((item, index)=>{
            if (index == tableIndex) {
                // 数量不可小于1
                if (qty < 1) {
                    item.quantity = 1;
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Warning"),
                        "message": $A.get("$Label.c.CCM_NumberOfPartsMsg"),
                        "type": "Warning"
                    }).fire();
                }
                item.total = Number(item.unitPrice) * item.quantity;
            }
        });
        component.set('v.partsItemList', [...partsItemList]);
        // 计算总价
        helper.calculateTotalHourAndPrice(component);
    },
    // 删除part行
    handleDelete:function(component, event, helper){
        let tableIndex = event.getSource().get('v.id');
        let partsItemList = component.get('v.partsItemList');
        partsItemList.splice(tableIndex, 1);
        console.log(JSON.parse(JSON.stringify(partsItemList)), 'partsItemList--------');
        component.set('v.partsItemList', [...partsItemList]);
        helper.calculateTotalLaborTime(component);
        // 计算总价
        helper.calculateTotalHourAndPrice(component);
     },
    // 修改OverTime Description
    changeOverTimeDescription:function(component, event, helper){
        helper.getElementRequiredError(component, 'overTimeDescription');
    },
    // 修改OverTime Hour
    changeOverTimeHour:function(component, event, helper){
        let overTimeHour = Number(component.get('v.overTimeHour'));
        // 不可小于0
        if (overTimeHour < 0) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_NumberOfActualTime"),
                "type": "Warning"
            }).fire();
            component.set('v.overTimeHour', 0);
            return;
        }
        // 校验是否必填
        if (!overTimeHour || overTimeHour == '0') {
            let element = component.find('overTimeDescription');
            $A.util.removeClass(element, 'field-error');
            let requiredText = component.find('overTimeDescription-error-required');
            $A.util.addClass(requiredText, 'slds-hide');
        }
        // 计算总价
        helper.calculateTotalHourAndPrice(component);
    },
    // cancel事件
    handleCancel: function(component, event, helper){
        let isPortal = component.get('v.isPortal');
        if (isPortal) {
            
            let url = '/s/servicehome';
            window.open(url, '_self');
        } else {
            let url = '/lightning/n/Service_List';
            window.open(url, '_self');
        }
    },
    // 提交事件
    handleSubmit: function(component, event, helper){
        // 校验必填
        let scenarioType = component.get('v.scenarioType');
        // 校验lookup字段
        // modelNumberInfo
        let modelNumberVaild = true;
        let modelNumberInfo = component.get('v.modelNumberInfo');
        const modelNumberElement = component.find('modelNumber');
        const modelNumberRequiredText = component.find('modelNumber-error-required');
        if (modelNumberInfo.Id) {
            $A.util.removeClass(modelNumberElement, 'field-error');
            $A.util.addClass(modelNumberRequiredText, 'slds-hide');
            modelNumberVaild = true;
        } else {
            $A.util.addClass(modelNumberElement, 'field-error');
            $A.util.removeClass(modelNumberRequiredText, 'slds-hide');
            modelNumberVaild = true;
        }

        // billAddress
        let billAddressVaild = true;
        let billAddressInfo = component.get('v.billAddressInfo');
        const billAddressInfoElement = component.find('billToAddress');
        const billAddressInfoRequiredText = component.find('billToAddress-error-required');
        if (billAddressInfo.Id) {
            $A.util.removeClass(billAddressInfoElement, 'field-error');
            $A.util.addClass(billAddressInfoRequiredText, 'slds-hide');
            billAddressVaild = true;
        } else {
            $A.util.addClass(billAddressInfoElement, 'field-error');
            $A.util.removeClass(billAddressInfoRequiredText, 'slds-hide');
            billAddressVaild = false;
        }

        // customer claim reference number
        // let claimNumberVaild = true;
        // let claimReferenceNumber = component.get('v.customerClaimReferenceNumber');
        // const claimReferenceNumberElement = component.find('claimReferenceNumber');
        // const claimReferenceNumberRequiredText = component.find('claimReferenceNumber-error-required');
        // if (claimReferenceNumber) {
        //     $A.util.removeClass(claimReferenceNumberElement, 'field-error');
        //     $A.util.addClass(claimReferenceNumberRequiredText, 'slds-hide');
        //     claimNumberVaild = true;
        // } else {
        //     $A.util.addClass(claimReferenceNumberElement, 'field-error');
        //     $A.util.removeClass(claimReferenceNumberRequiredText, 'slds-hide');
        //     claimNumberVaild = false;
        // }
        let requestDateVaild = true;
        let noWarranty = component.get('v.noWarranty');
        let serialNumber = component.get('v.serialNumber');
        if(noWarranty || !serialNumber) {
            let receiptContentVersionId = component.get('v.receiptContentVersionId');
            if(!receiptContentVersionId) {
                let toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Warning"),
                    "message": $A.get("$Label.c.CCM_UploadReceiptWarning"),
                    "type": "warning",
                }).fire();
                return ;
            }

            let requestDateInfo = component.get('v.requestDate');
            const requestDateInfoElement = component.find('requestDate');
            const requestDateInfoRequiredText = component.find('requestDate-error-required');
            if (requestDateInfo) {
                $A.util.removeClass(requestDateInfoElement, 'field-error');
                $A.util.addClass(requestDateInfoRequiredText, 'slds-hide');
                requestDateVaild = true;
            } else {
                $A.util.addClass(requestDateInfoElement, 'field-error');
                $A.util.removeClass(requestDateInfoRequiredText, 'slds-hide');
                requestDateVaild = false;
            }
        }
        
        // Scenario 1
        if (scenarioType == 'Scenario 1' || scenarioType == 'Scenario 2') {
            let valid = helper.getValidation(component, 'Scenario 1');
            // addressInfo
            let addressVaild = true;
            let addressInfo = component.get('v.addressInfo');
            const addressInfoElement = component.find('address');
            const addressInfoRequiredText = component.find('address-error-required');
            if (addressInfo.Id) {
                $A.util.removeClass(addressInfoElement, 'field-error');
                $A.util.addClass(addressInfoRequiredText, 'slds-hide');
                addressVaild = true;
            } else {
                $A.util.addClass(addressInfoElement, 'field-error');
                $A.util.removeClass(addressInfoRequiredText, 'slds-hide');
                addressVaild = false;
            }
            if (valid && modelNumberVaild && addressVaild && billAddressVaild && requestDateVaild) {
                helper.submitEvent(component);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Warning"),
                    "message": $A.get("$Label.c.CCM_FieldRequired"),
                    "type": "warning",
                }).fire();
            }
        } else if (scenarioType == 'Scenario 3') {
            let valid = helper.getValidation(component, 'Scenario 3');
            console.log('Scenario 3-valid--------------111');
            // Repairable Parts
            let repairablePartsVaild = true;
            let repairablePartsInfo = component.get('v.repairablePartsInfo');
            const repairablePartsElement = component.find('repairableParts');
            const repairablePartsRequiredText = component.find('repairableParts-error-required');
            if (repairablePartsInfo.Id) {
                $A.util.removeClass(repairablePartsElement, 'field-error');
                $A.util.addClass(repairablePartsRequiredText, 'slds-hide');
                repairablePartsVaild = true;
            } else {
                $A.util.addClass(repairablePartsElement, 'field-error');
                $A.util.removeClass(repairablePartsRequiredText, 'slds-hide');
                repairablePartsVaild = false;
            }
            // 判断parts 表格
            let partsItemList = component.get('v.partsItemList');
            let talbeValid = true;
            if (partsItemList.length > 0) {
                partsItemList.forEach((item)=>{
                    if (!item.partInfo.Name) {
                        talbeValid = false;
                    }
                })
                if (!talbeValid) {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Warning"),
                        "message": $A.get("$Label.c.CCM_EnterCompletePartsMsg"),
                        "type": "warning",
                    }).fire();
                    return;
                }
            }
            if (valid && modelNumberVaild && billAddressVaild && repairablePartsVaild && requestDateVaild) {
                console.log('submit------------');
                helper.submitEvent(component);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Warning"),
                    "message": $A.get("$Label.c.CCM_FieldRequired"),
                    "type": "warning",
                }).fire();
            }
        } else if (scenarioType == 'Scenario 4') {
            let valid = helper.getValidation(component, 'Scenario 4');
            if (valid && modelNumberVaild && billAddressVaild && requestDateVaild) {
                console.log('submit------------');
                helper.submitEvent(component);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Warning"),
                    "message": $A.get("$Label.c.CCM_FieldRequired"),
                    "type": "warning",
                }).fire();
            }
        }
    },
    //选择projectcode
    selectProject: function(component, event, helper) {
        // for Display Model,set the "isOpen" attribute to "true"
        console.log(component.get("v.selectedProject"));
        var parentId2 = event.currentTarget.value;
        console.log(parentId2);
        if(!component.get("v.isSelected")){
            component.set("v.selectedProject",parentId2);
            component.set("v.isSelected",true);
        }else{
            component.set("v.isSelected",false);
            component.set("v.selectedProject",'');
        }
        
     },
     //提交按钮确认
     submitConfirm: function(component, event, helper) {
        if(component.get("v.projectCode") != null && component.get("v.projectCode") !== ''){
            //检查projectcode
            var action = component.get("c.checkProjectExist");
            action.setParams({
                projectCodeParam: component.get('v.projectCode')
            });
            component.set('v.isBusy', true);
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === "SUCCESS") {
                    var results = JSON.parse(response.getReturnValue());
                    console.log(JSON.stringify(results));
                    if(results == null){
                        var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": $A.get("$Label.c.CCM_Error"),
                            "message": $A.get("$Label.c.CCM_ProjectCodeNotExistMsg"),
                            "type": "error"
                        }).fire();
                    }else{
                        component.set('v.projectId', results[0].Id);
                        component.set('v.isBusy', false);
                        var doSubmit = component.get('c.handleSubmit');
                        $A.enqueueAction(doSubmit);
                    }
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            alert("ERROR: " + errors[0].message);
                        }
                    } else {
                        alert("ERROR: Unknown error");
                    }
                }
                component.set('v.isBusy', false);
            });
            
            $A.enqueueAction(action);
        }else{
            var doSubmit = component.get('c.handleSubmit');
            $A.enqueueAction(doSubmit);
        }
     },
     //不用设值projectcode直接提交
     continueToSubmit: function(component, event, helper) {
        // 提交信息
        var doSubmit = component.get('c.handleSubmit');
        component.set("v.isOpen", false);
        $A.enqueueAction(doSubmit);
     },
     //打开project info画面
    openModel: function(component, event, helper) {
        // for Display Model,set the "isOpen" attribute to "true"
        component.set("v.isOpen", true);
     },
     //关闭project info画面
     closeModel: function(component, event, helper) {
        // for Hide/Close Model,set the "isOpen" attribute to "Fasle"  
        component.set("v.isOpen", false);
     },
     //将选择的project code追加到字段
     setProjectCode: function(component, event, helper) {
        // 选择的project设置到Claim
        component.set("v.isOpen", false);
        var code = component.get("v.selectedProject");
        component.set("v.projectCode", code);
     },
     //展示project info画面
     showProjectInfo: function(component, event, helper) {
        component.set("v.isOpen", true);
     },
     // 保存草稿
     handleSaveAsDraft: function(component, event, helper){
        console.log('保存草稿----------1');
        helper.saveClaimAsDraft(component);
    },
    // 标准发票上传事件
    handleUploadFinished: function (component, event, helper) {
        var uploadedFiles = event.getParam("files");
        console.log(JSON.stringify(uploadedFiles), 'uploadedFiles-------------');
        // Get the file name
        uploadedFiles.forEach((file) => {
            // type
            let fileType = file.mimeType.slice(-3);
            helper.getReceiptId(component, file.contentVersionId, file.name, fileType);
        });
    },
    // copy claim
    copyClaim : function(component, event){
        let claimId = component.get('v.claimId');
        let accId = component.get('v.accId');
        let modelNumber = component.get('v.modelNumber');
        let isPortal = component.get('v.isPortal');
        if (isPortal) {
            let url = window.location.origin + '/s/warranty-claim-create?0.customerId=' + accId + '&0.claimId=' + claimId + '&0.type=create&0.modelNumber=' + modelNumber + '&0.isCopy=true';
            window.open(url, '_self');
        } else {
            let url = window.location.origin + '/lightning/n/Warranty_Claim_Create?0.customerId=' + accId + '&0.claimId=' + claimId + '&0.type=create&0.modelNumber=' + modelNumber + '&0.isCopy=true';
            window.open(url, '_self');
        }
    },
    // 打开new sn弹框
    openNewSNPopup : function(component, event){
        component.set('v.showNewSNFlag', true);
    },
    // 关闭new sn弹框
    closeNewSNPopup : function(component, event){
        component.set('v.showNewSNFlag', false);
        component.set('v.newSerialNumber', '');
    },
    // 保存新sn
    saveNewSNEvent: function(component, event, helper){
        if (!component.get('v.newSerialNumber')) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_EnterNewSerialNumberMsg"),
                "type": "warning"
            }).fire();
            return;
        }
        helper.saveNewSN(component);
    },

    handlePartsSelect: function(component, event, helper) {
        let partsSelected = JSON.parse(event.getParam('message'));
        let partsItemList = component.get('v.partsItemList');
        if(!partsItemList || partsItemList.length === 0) {
            partsItemList = [];
        }
        let locale = $A.get("$Locale.language");
        partsSelected.forEach(part=>{
            let item = {};
            let partName = locale == 'en' ? part.Parts__r.Item_Description_EN__c : part.Parts__r.Item_Description_DE__c;
            item.partName = partName;
            item.itemNumber = part.Parts__r.ProductCode;
            item.unitPrice = part.Parts__r.Base_Cost__c;
            item.noCalculate = false;
            if(!item.unitPrice) {
                item.noCalculate = true;
            }
            item.laborTime = part.EstimatedRepairTime__c;
            item.quantity = 1;
            item.total = Number(item.unitPrice) * item.quantity;
            item.type = 'repairPart';
            item.partInfo = {
                Name: partName
            };
            partsItemList.push(item);
        });
        component.set('v.partsItemList', partsItemList);
        helper.calculateTotalLaborTime(component);
        helper.calculateTotalHourAndPrice(component);
    },

    placeNewOrder: function(component, event, helper) {
        let claimId = component.get('v.claimId');
        let partsItemList = component.get('v.partsItemList');
        let params = [];
        partsItemList.forEach(item=>{
            let partId = item.partInfo.Id;
            let quantity = item.quantity;
            let param = partId + ';' + quantity;
            params.push(param);
        });

        if(params.length > 0) {
            let finalParam = params.join(',');
            let url = '/s/place-order?0.recordType=Regular_Order&claimId=' + claimId + '&params=' + finalParam;
            window.open(url, '_blank');
        }
        else {
            let url = '/s/place-order?0.recordType=Regular_Order&claimId=' + claimId;
            window.open(url, '_blank');
        }
    },

    handleUploadFinished2: function(component, event, helper) {
        let uploadedFiles = event.getParam("files");
        let contentVersionId;
        let fileName;
        uploadedFiles.forEach(file=>{
            contentVersionId = file.contentVersionId;
            fileName = file.name;
        });
        component.set('v.receiptContentVersionId', contentVersionId);
        let receiptUrl = {
            fileName: fileName
        };
        component.set('v.receiptUrl', receiptUrl);
    },

    deleteReceipt2: function(component, event, helper) {
        let receiptContentVersionId = component.get('v.receiptContentVersionId');
        if(receiptContentVersionId) {
            let action = component.get('c.deleteReceiptImp');
            action.setParams({'receiptContentVersionId': receiptContentVersionId});
            action.setCallback(this, function(response){
                let state = response.getState();
                if(state === 'SUCCESS') {
                    component.set('v.receiptUrl', '');
                }
                else {
                    console.log(response.getError());
                }
            });
            $A.enqueueAction(action);
        }
    },

    downloadLink: function(component, event, helper) {
        let fileLink = event.currentTarget.dataset.link;
        let action = component.get('c.generatePresignedURL');
        action.setParams({'receiptLink': fileLink});
        action.setCallback(this, function(response){
            let state = response.getState();
            if(state === 'SUCCESS') {
                let result = response.getReturnValue();
                window.open(result, '_blank');
            }
        });
        $A.enqueueAction(action);
    },

    handleUploadToolImage: function(component, event, helper) {
        let uploadedFiles = event.getParam("files");
        let uploadImages = [];
        uploadedFiles.forEach(file=>{
            uploadImages.push({
                'fileName': file.name,
                'fileId': file.documentId
            });
        });
        let toolImages = component.get('v.uploadToolImages');
        toolImages = [...toolImages, ...uploadImages];
        component.set('v.uploadToolImages', toolImages);
    },

    deleteToolImage: function(component, event, helper) {
        let fileId = event.currentTarget.dataset.fileid;
        helper.deleteToolImageImp(component, fileId);
    }
})