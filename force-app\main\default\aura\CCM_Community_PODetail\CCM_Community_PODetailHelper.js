({
    getUrlParameter : function(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    },
    showToast : function(title, message, type){
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type" : type
        });
        toastEvent.fire();
    },
    fileByBase64 : function(file, component, attachmentName) {
        console.log(file, attachmentName, 'file===========123123');
        const self = this;
        const reader = new FileReader();
        // 传入一个参数对象即可得到基于该参数对象的文本内容
        reader.readAsDataURL(file[0]);
        reader.onload = function (e) {
            // target.result 该属性表示目标对象的DataURL
            let fileStr = e.target.result;
            let index = fileStr.indexOf(',') + 1;
            let fileValue = fileStr.substring(index);
            self.getFileId(component, fileValue, file[0].name, attachmentName);
            // let uploadItem = component.get('v.attachmentItem');
            // uploadItem.base64 = fileValue;
            // // 存附件
            // component.set('v.attachmentItem', uploadItem);
        };
    },
    // 保存附件
    uplaodFileEvent : function(component) {
        var action = component.get('c.uploadFile');
        let attachmentList = JSON.parse(JSON.stringify(component.get('v.attachment')));
        console.log(attachmentList, 'attachmentList---------------');
        let paramsArr = [];
        attachmentList.forEach((item)=>{
            paramsArr.push({
                contentId: item.contentId,
                fileType: item.attachmentType,
                fileName: item.attachmentName,
                fileDate: item.attachmentDate,
            })
        })
        action.setParams({
            purchaseOrderId: component.get('v.recordId'),
            lstuploadFileInfo: paramsArr
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, '保存附件-----------------');
            if (state === 'SUCCESS' ) {
                var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Success",
                   "message": 'Attachment saved successfully!',
                   "type": "success"
               }).fire();
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
        });
        $A.enqueueAction(action);
    },

    handleSync: function(component) {
        component.set('v.isBusy', true);
        let action = component.get('c.sync');
        action.setParams({'purchaseOrderId': component.get('v.recordId')});
        action.setCallback(this, function(response){
            if(response.getState() === 'SUCCESS') {
                let syncStatus = response.getReturnValue();
                if(!syncStatus || syncStatus === 'Failed') {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Error",
                        "message": 'Sync failed, Please contact system administrator!',
                        "type": "error"
                    }).fire();
                }
                else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Success",
                        "message": 'Sync to EBS successful!',
                        "type": "success"
                    }).fire();
                    let url = window.location.origin + '/lightning/n/Purchase_Order_List';
                    window.open(url, '_self');
                }
            }
            else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },

    // 同步数据
    syncEvent : function(component) {
        var action = component.get('c.SubmitAndSync');
        action.setParams({
            PurchaseOrderId: component.get('v.recordId'),
            TotalDueAmount: Number(component.get('v.TotalDueAmount')),
            CountLine: component.get('v.CountLine'),
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, '同步数据-----------------');
            if (state === 'SUCCESS' ) {
                // haibo: 新增同步失败提示
                if (result == 'Failed') {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Error",
                        "message": 'Sync failed, Please contact system administrator!',
                        "type": "error"
                    }).fire();
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Success",
                        "message": 'Sync to EBS successful!',
                        "type": "success"
                    }).fire();
                    let url = window.location.origin + '/lightning/n/Purchase_Order_List';
                    window.open(url, '_self');
                }
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // customer提示信息
    queryAlertMessage : function(component, customerNumber) {
        var action = component.get('c.queryAlertMessage');
        action.setParams({
            CustomerNumber: customerNumber,
            AlertMode: 'Delivery',
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'customer提示信息=========');
            if (state === 'SUCCESS' ) {
                var result = response.getReturnValue();
                console.log(result, 'customer提示信息 result=========');
                var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Success",
                   "message": result,
                   "type": "success",
                   "duration": "pester"
               }).fire();
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 存附件
    getFileId : function(component, content, name, attachmentName) {
        var action = component.get('c.uploadFileMidel');
        action.setParams({
            content: content,
            uploadFileName: name,
            fileName: attachmentName
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, '存附件=========');
            if (state === 'SUCCESS' ) {
                var result = JSON.parse(response.getReturnValue());
                console.log(result, '存附件 result=========');
                let uploadItem = component.get('v.attachmentItem');
                uploadItem.contentId = result.ContentId;
                component.set('v.attachmentItem', uploadItem);
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 刷新红绿灯
    refreshLight: function(component) {
        let self = this;
        console.log('刷新红绿灯=========');
        component.set('v.isBusy', true);
        var action = component.get("c.refreshInventory");
        action.setParams({
            purchaseOrderId: component.get('v.recordId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                self.getBaseInfo(component);
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Success",
                    "message": 'Refresh Traffic Light succeeded!',
                    "type": "success"
                }).fire();
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 取消同步
    cancelSyncEvent: function(component) {
        console.log('取消同步=========');
        component.set('v.isBusy', true);
        var action = component.get("c.CancelOrder");
        action.setParams({
            purchaseOrderId: component.get('v.recordId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                console.log(results, '取消同步==========');
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Success",
                    "message": 'Cancel to EBS succeeded!',
                    "type": "success"
                }).fire();
                let url = window.location.origin + '/lightning/n/Purchase_Order_List';
                window.open(url, '_self');
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    getBaseInfo: function(component) {
        var action = component.get("c.QueryPirchaseAndItemInfo");
        action.setParams({
            PurchaseOrderId: component.get('v.recordId'),
            IsProtal: false,
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                component.set('v.orderItemList', results.lstPurchaseOrderItem);
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
})