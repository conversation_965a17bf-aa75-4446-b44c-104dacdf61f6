<template>
    <!-- 页面遮罩 -->
    <!-- 等待加载动画 -->
    <template if:true={showSpinner}>
        <div class="spinner-wrapper">
            <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
        </div>
    </template>
    <!-- TITLE -->
    <div class="container">
        <!-- title -->
        <div style="margin:0 auto;font-size:x-large">New Requests for Products</div>
        <!-- Sales Rep Name & Request for -->
        <div class="slds-grid">
            <div class="slds-col slds-size-7-of-12" style="display:flex">
                <lightning-input type="text" label="Sales Rep Name" value={salesRep.salesRep.name} disabled></lightning-input>
                <div style="display: inline-block;width:50px"></div>
                <template if:true={blCanSelectRequestFor}>
                    <c-ccm-autocomplete label='Request For' default-value={selectedUser} onselected={handleUserSelected}
                                        onsearchinputchange={handleUserSearchInputChange} source={userSource}
                                        display-text={selectedUser.label} content-field="label"
                                        realtime-search={realtimeSearch}>
                    </c-ccm-autocomplete>
                </template>
                <template if:false={blCanSelectRequestFor}>
                    <lightning-input type="text" label="Sales Rep For" value={salesRep.salesRepFor.name}
                                     disabled></lightning-input>
                </template>
            </div>
        </div>
    </div>

    <!-- 请求的表格 -->
    <div class="container tb_product_list" style="padding: 0px;height: 300px;">
        <table aria-multiselectable="true" style=""
               class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols"
               role="grid">
            <thead>
                <tr class="slds-line-height_reset">
                    <!-- show -->
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate"></span>
                            </div>
                        </a>
                    </th>
                    <!-- No. -->
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">No.</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 12%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">Product Description</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">Model #</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 8%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">Qty</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">List Price</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">Subtotal</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">{Labels.inventory}</span>
                            </div>
                        </a>
                    </th>
                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                        style="width: 5%">
                        <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                <span class="slds-truncate">Action</span>
                            </div>
                        </a>
                    </th>
                </tr>
            </thead>
            <tbody>
                <template for:each={productList} for:item="request" for:index="index">
                    <tr aria-selected="false" class="slds-hint-parent" key={request.tbindex} data-tbindex={index} style="height: auto;" onclick={showToolList}>
                        <!-- Tbindx. -->
                        <th scope="row">
                            <template if:true={request.bltools}>
                                <div class="slds-truncate" style="text-align: center;">
                                    <lightning-icon icon-name="utility:chevrondown" size="xx-small"></lightning-icon>
                                </div>
                            </template>
                        </th>
                        <!-- Tbindx. -->
                        <th scope="row">
                            <div class="slds-truncate" title="No.">
                                {request.tbindex}
                            </div>
                        </th>
                        <!-- Product Description -->
                        <td role="gridcell" title="Product Description">
                            <c-ccm-autocomplete default-value={request.reRender} onselected={handProductSelected}
                                                onsearchinputchange={handleProductInputChange}
                                                source={request.productSource} display-text={request.reRender.label}
                                                content-field="name" realtime-search={realtimeSearch}
                                                data-tbindex={request.tbindex}>
                            </c-ccm-autocomplete>
                        </td>
                        <td role="gridcell" title="Model #">
                            <div class="slds-truncate">
                                {request.model}
                            </div>
                        </td>
                        <td role="gridcell" title="Qty">
                            <div class="slds-truncate" style="transform: translate(0px, -10px);">
                                <button onclick={handleItemQtyMinus} data-tbindex={request.tbindex} style="height: 30px;width: 30px;">-</button>
                                <lightning-input type="number" onchange={handleItemQtyChange}
                                                 data-name="qty" value={request.qty} data-tbindex={request.tbindex}
                                                 style="width:100px;display: inline-block;margin:0 10px"></lightning-input>
                                <button onclick={handleItemQtyPlus} data-tbindex={request.tbindex} style="height: 30px;width: 30px;">+</button>
                            </div>
                        </td>
                        <td role="gridcell" title="List Price">
                            <div class="slds-truncate">
                                {request.price} {currencyCode}
                            </div>
                        </td>

                        <td role="gridcell" title="Subtotal">
                            <div class="slds-truncate">
                                {request.subtotal} {currencyCode}
                            </div>
                        </td>
                        <td role="gridcell" title="Inventory">
                            <template if:false={request.bltools}>
                                <div class={request.inventory}>
                                </div>
                            </template>
                        </td>
                        <td role="gridcell" title="Remove">
                            <div class="slds-truncate">
                                <lightning-button label="Remove" onclick={handleRemoveItem} value={index}
                                                  disabled={blCanEdit}>
                                </lightning-button>
                            </div>
                        </td>
                    </tr>
                    <template if:true={request.bltools}>
                        <tr aria-selected="false" class={request.showToolStyle} key={request.tbindex}>
                            <td colspan="9" style="padding: 0;">
                                <div class="tool-table-wrap">
                                    <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols" role="grid">
                                        <thead>
                                            <tr class="slds-line-height_reset">
                                                <!-- show -->
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 5%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate"></span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <!-- No. -->
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 5%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate">No.</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 12%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate">Product Description</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 5%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate">Model #</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 8%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate">Qty</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 5%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate">List Price</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 5%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate">Subtotal</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 5%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate">Inventory</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                                    style="width: 5%">
                                                    <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate"></span>
                                                        </div>
                                                    </a>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template for:each={request.tools} for:item="tool" for:index="toolIndex">
                                                <tr aria-selected="false" class="slds-hint-parent" key={tool.tooid}  style="height: auto;">
                                                    <!-- Tbindx. -->
                                                    <th scope="row">
                                                        <div class="slds-truncate" style="text-align: center;">
                                                        </div>
                                                    </th>
                                                    <!-- Tbindx. -->
                                                    <th scope="row">
                                                        <div class="slds-truncate" title={tool.tbindex}>
                                                            {request.tbindex}.{tool.toolNo}
                                                        </div>
                                                    </th>
                                                    <!-- Product Description -->
                                                    <td role="gridcell" title="Product Description">
                                                        <div class="slds-truncate" title={tool.toolname}>
                                                            {tool.toolname}
                                                        </div>
                                                    </td>
                                                    <td role="gridcell" title={tool.toolmodel}>
                                                        <div class="slds-truncate">
                                                            {tool.toolmodel}
                                                        </div>
                                                    </td>
                                                    <td role="gridcell" title="Qty">
                                                        <div class="slds-truncate">
                                                            {tool.qty}
                                                        </div>
                                                    </td>
                                                    <td role="gridcell" title="List Price">
                                                        <div class="slds-truncate">
                                                            {currencyCode} : {tool.listPrice}
                                                        </div>
                                                    </td>
                            
                                                    <td role="gridcell" title="Subtotal">
                                                        <div class="slds-truncate">
                                                            {tool.subTotal}
                                                        </div>
                                                    </td>
                                                    <td role="gridcell" title="Inventory">
                                                        <div class={tool.inventory}>
                                                            <!-- <div> -->
                                                            <!-- {request.inventorystatus} -->
                                                        </div>
                                                    </td>
                                                    <td role="gridcell" title="Remove">
                                                        <div class="slds-truncate">
                                                        </div>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </div>
                                
                            </td>
                        </tr>
                    </template>
                </template>
            </tbody>
        </table>
    </div>
    <!-- add item , total qty, total value -->
    <div class="page_buttons">
        <div>
            <lightning-button label="Add Item" onclick={handleAddItem} disabled={blCanEdit}>
            </lightning-button>
        </div>
        <div class="slds-grid total-wrap">
            <div class="slds-text-align--right">
                <p><strong>{Labels.totalQty}:</strong></p>
                <p><strong>{Labels.totalValue}:</strong></p>
            </div>
            <div class="total-value-wrap">
                <p><strong>&nbsp;{totalQty}</strong></p>
                <p><strong>&nbsp;{totalValue} {currencyCode}</strong></p>
            </div>
        </div>
        <!-- clear float -->
        <div style="clear: both;"></div>
        <div class="total_buttons">
            <lightning-button label="Back to Overview" onclick={handleRequestCancel}></lightning-button>
            <lightning-button label="Save" onclick={handleRequestSubmit} disabled={blSubmitClick}></lightning-button>
        </div>
    </div>
</template>