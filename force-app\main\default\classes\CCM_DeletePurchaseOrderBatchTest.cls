/**
 * Author : Honey
 * Date : 2023-09-05
 * Description : CCM_DeletePurchaseOrderBatch 批量删除测试类
 * 测试删除30天前为草稿状态且由Partner Community用户创建的无订单项的PO订单
 */
@IsTest
public with sharing class CCM_DeletePurchaseOrderBatchTest {

    @TestSetup
    static void setupTestData() {
        // 创建测试账户
        Account testAccount = new Account(
            Name = 'Test Account',
            AccountNumber = 'TEST001'
        );
        insert testAccount;

        // 创建产品
        Product2 testProduct = new Product2(
            Name = 'Test Product',
            ProductCode = 'TEST-PROD-001',
            IsActive = true
        );
        insert testProduct;
    }

    @IsTest
    static void testBatchExecuteMethod() {
        // 测试批处理的execute方法逻辑
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];
        Product2 testProduct = [SELECT Id FROM Product2 WHERE ProductCode = 'TEST-PROD-001' LIMIT 1];

        List<Purchase_Order__c> purchaseOrders = new List<Purchase_Order__c>();

        // 创建应该被删除的PO（草稿状态，无订单项）
        for (Integer i = 0; i < 3; i++) {
            Purchase_Order__c po = new Purchase_Order__c(
                Status__c = CCM_Constants.DRAFT_STOCK,
                Customer__c = testAccount.Id,
                Warehouse__c = 'Germany (DSV)'
            );
            purchaseOrders.add(po);
        }

        // 创建不应该被删除的PO（有订单项）
        for (Integer i = 0; i < 2; i++) {
            Purchase_Order__c po = new Purchase_Order__c(
                Status__c = CCM_Constants.DRAFT_STOCK,
                Customer__c = testAccount.Id,
                Warehouse__c = 'Germany (DSV)'
            );
            purchaseOrders.add(po);
        }

        insert purchaseOrders;

        // 为后2个PO创建订单项（这些PO不应该被删除）
        List<Purchase_Order_Item__c> orderItems = new List<Purchase_Order_Item__c>();
        for (Integer i = 3; i < 5; i++) {
            Purchase_Order_Item__c item = new Purchase_Order_Item__c(
                Purchase_Order__c = purchaseOrders[i].Id,
                Product__c = testProduct.Id,
                Quantity__c = 1
            );
            orderItems.add(item);
        }
        insert orderItems;

        Test.startTest();

        // 直接测试execute方法的逻辑
        CCM_DeletePurchaseOrderBatch batch = new CCM_DeletePurchaseOrderBatch();
        Database.BatchableContext bc = null;

        // 模拟批处理传入的数据
        batch.execute(bc, purchaseOrders);

        Test.stopTest();

        // 验证结果
        List<Purchase_Order__c> remainingPOs = [SELECT Id, (SELECT Id FROM Purchase_Order_Items__r) FROM Purchase_Order__c];

        // 应该剩余2个PO（有订单项的）
        System.assertEquals(2, remainingPOs.size(), '应该剩余2个有订单项的PO');

        // 验证剩余的PO都有订单项
        for (Purchase_Order__c po : remainingPOs) {
            System.assert(po.Purchase_Order_Items__r.size() > 0, '剩余的PO应该都有订单项');
        }
    }

    @IsTest
    static void testBatchStartMethod() {
        // 测试start方法返回QueryLocator
        CCM_DeletePurchaseOrderBatch batch = new CCM_DeletePurchaseOrderBatch();
        Database.BatchableContext bc = null;

        Test.startTest();

        Database.QueryLocator ql = batch.start(bc);

        Test.stopTest();

        // 验证QueryLocator不为空
        System.assertNotEquals(null, ql, 'QueryLocator应该不为空');

        // 验证查询字符串包含正确的条件
        String query = ql.getQuery();
        System.assert(query.contains('Purchase_Order__c'), '查询应该包含Purchase_Order__c');
        System.assert(query.contains('LAST_N_DAYS:10'), '查询应该包含日期条件');
        System.assert(query.contains('Partner Community'), '查询应该包含Partner Community条件');
    }

    @IsTest
    static void testBatchDoesNotDeleteRecentPOs() {
        // 获取测试数据
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];

        List<Purchase_Order__c> recentPOs = new List<Purchase_Order__c>();

        // 创建最近的PO（不应该被删除）
        for (Integer i = 0; i < 3; i++) {
            Purchase_Order__c po = new Purchase_Order__c(
                Status__c = CCM_Constants.DRAFT_STOCK,
                Customer__c = testAccount.Id,
                Warehouse__c = 'Germany (DSV)'
            );
            recentPOs.add(po);
        }
        insert recentPOs;

        Test.startTest();

        // 执行批处理
        CCM_DeletePurchaseOrderBatch batch = new CCM_DeletePurchaseOrderBatch();
        Database.executeBatch(batch, 200);

        Test.stopTest();

        // 验证结果 - 最近的PO不应该被删除
        List<Purchase_Order__c> remainingPOs = [SELECT Id FROM Purchase_Order__c];
        System.assertEquals(3, remainingPOs.size(), '最近创建的PO不应该被删除');
    }

    @IsTest
    static void testBatchDoesNotDeleteNonDraftPOs() {
        // 获取测试数据
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];

        List<Purchase_Order__c> nonDraftPOs = new List<Purchase_Order__c>();

        // 创建非草稿状态的PO（不应该被删除）
        Purchase_Order__c submittedPO = new Purchase_Order__c(
            Status__c = 'Submitted',
            Customer__c = testAccount.Id,
            Warehouse__c = 'Germany (DSV)'
        );
        nonDraftPOs.add(submittedPO);

        Purchase_Order__c cancelledPO = new Purchase_Order__c(
            Status__c = 'Cancelled',
            Customer__c = testAccount.Id,
            Warehouse__c = 'Germany (DSV)'
        );
        nonDraftPOs.add(cancelledPO);

        insert nonDraftPOs;

        // 设置历史数据创建时间
        for(Purchase_Order__c po : nonDraftPOs){
            Test.setCreatedDate(po.Id, DateTime.newInstance(2023, 1, 1));
        }

        Test.startTest();

        // 执行批处理
        CCM_DeletePurchaseOrderBatch batch = new CCM_DeletePurchaseOrderBatch();
        Database.executeBatch(batch, 200);

        Test.stopTest();

        // 验证结果 - 非草稿状态的PO不应该被删除
        List<Purchase_Order__c> remainingPOs = [SELECT Id, Status__c FROM Purchase_Order__c];
        System.assertEquals(2, remainingPOs.size(), '非草稿状态的PO不应该被删除');
    }

    @IsTest
    static void testBatchConstructor() {
        // 测试构造函数
        CCM_DeletePurchaseOrderBatch batch = new CCM_DeletePurchaseOrderBatch();
        System.assertNotEquals(null, batch, '批处理实例应该被成功创建');
    }

    @IsTest
    static void testBatchFinishMethod() {
        // 测试finish方法（虽然当前为空实现）
        CCM_DeletePurchaseOrderBatch batch = new CCM_DeletePurchaseOrderBatch();
        Database.BatchableContext bc = null;

        // 调用finish方法不应该抛出异常
        try {
            batch.finish(bc);
            System.assert(true, 'finish方法应该正常执行');
        } catch (Exception e) {
            System.assert(false, 'finish方法不应该抛出异常: ' + e.getMessage());
        }
    }
}