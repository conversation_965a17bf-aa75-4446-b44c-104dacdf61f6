/**
 * Test class for CCM_InfoPortalAutoLoginUtil
 */
@IsTest
public with sharing class CCM_InfoPortalAutoLoginUtilTest {
    
    @IsTest
    static void testCreateNewUser(){
        Test.startTest();
        try {
            CCM_InfoPortalAutoLoginUtil.getHomeLink(UserInfo.getUserId());
        }
        catch(Exception ex) {

        }
        Test.stopTest();
    }

    @IsTest
    static void testUpdateUser(){
        Test.startTest();
        try {
            CCM_InfoPortalAutoLoginUtil.updateUserInInfoPortal(UserInfo.getUserId());
        }
        catch(Exception ex) {

        }
        Test.stopTest();
    }
}