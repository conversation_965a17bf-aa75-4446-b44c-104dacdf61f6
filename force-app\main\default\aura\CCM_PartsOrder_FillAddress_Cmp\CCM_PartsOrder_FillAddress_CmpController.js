({
    doInit : function(component, event, helper) {
        console.log(component.get('v.showHelpText'), component.get('v.customerType'), component.get('v.freightCost'), '初始化------------fill in');
        component.set('v.isDE', $A.get("$Label.c.CCM_Translate"));
        if (component.get('v.isDropShip') === 'N') {
            component.set('v.disableFlag', true);
        } else {
            component.set('v.disableFlag', false);
        }
        component.set('v.showDetailfield', false);
        // 获取picklist
        helper.getDropshipList(component);
                // 判断是否edit
        let actionType = component.get('v.actionType');
        if (actionType === 'edit' || actionType === 'draft') {
            // 获取旧数据
            console.log('获取旧数据-----------');
            helper.getPODetailInfo(component);
        } else {
            // 自动填充address
            helper.getBillAddressList(component);
            helper.getShipAddressList(component);
            helper.getDropAddressList(component);
        }
    },
    // 下一步
    nextStep: function(component, event, helper){
        // 校验必填项
        let shipToAddressValid = true;
        let billToAddressValid = true;
        let showShip = component.get('v.showShip');;
        let shipToAddress = JSON.parse(JSON.stringify(component.get('v.shipToAddress')));
        const shipToAddressElement = component.find('shipToAddress');
        const shipToAddressRequiredText = component.find('shipToAddress-error-required');
        if (showShip) {
            if (shipToAddress.Id) {
                shipToAddressValid = true;
                $A.util.removeClass(shipToAddressElement, 'field-error');
                $A.util.addClass(shipToAddressRequiredText, 'slds-hide');
            } else {
                shipToAddressValid = false;
                $A.util.addClass(shipToAddressElement, 'field-error');
                $A.util.removeClass(shipToAddressRequiredText, 'slds-hide');
            }
        }
        let billToAddress = JSON.parse(JSON.stringify(component.get('v.billToAddress')));
        const billToAddressElement = component.find('billToAddress');
        const billToAddressRequiredText = component.find('billToAddress-error-required');
        if (billToAddress.Id) {
            billToAddressValid = true;
            $A.util.removeClass(billToAddressElement, 'field-error');
            $A.util.addClass(billToAddressRequiredText, 'slds-hide');
        } else {
            billToAddressValid = false;
            $A.util.addClass(billToAddressElement, 'field-error');
            $A.util.removeClass(billToAddressRequiredText, 'slds-hide');
        };
            console.log(component.get('v.isDropShip'), component.get('v.dropshipType'), '判断是否DI类型------------');
            // 判断是否DI类型
            if (component.get('v.isDropShip') == 'Y') {
                // 判断type是否为End Consumer
                let valid = true;
                let dropshipAddressValid = false;
                let dropShipCountryValid = false;

                if (component.get('v.dropshipType') == 'End Consumer') {
                    valid = helper.getValidation(component, 'detail');
                    // 校验country
                    let DropShipCountry = JSON.parse(JSON.stringify(component.get('v.DropShipCountry')));
                    console.log(DropShipCountry, '校验country------------test');
                    const dropShipCountryElement = component.find('dropShipCountry');
                    const dropShipCountryRequiredText = component.find('dropShipCountry-error-required');
                    if (DropShipCountry.Id) {
                        dropShipCountryValid = true;
                        $A.util.removeClass(dropShipCountryElement, 'field-error');
                        $A.util.addClass(dropShipCountryRequiredText, 'slds-hide');
                    } else {
                        dropShipCountryValid = false;
                        $A.util.addClass(dropShipCountryElement, 'field-error');
                        $A.util.removeClass(dropShipCountryRequiredText, 'slds-hide');
                    };
                    // 下一步
                    if (billToAddressValid && shipToAddressValid && valid && dropShipCountryValid) {
                        console.log('下一步校验通过-----------');
                        helper.handleNextStep(component, true);
                    } else {
                        var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": "Warning",
                            "message": $A.get("$Label.c.CCM_FillRequiredFields"),
                            "type": "warning",
                            "duration": "dismissible"
                        }).fire();
                    }
                } else {
                    valid = helper.getValidation(component, '');
                    // 校验dropshipAddress
                    let dropshipAddress = JSON.parse(JSON.stringify(component.get('v.dropshipAddress')));
                    const dropshipAddressElement = component.find('dropshipAddress');
                    const dropshipAddressRequiredText = component.find('dropshipAddress-error-required');
                    if (dropshipAddress.Id) {
                        dropshipAddressValid = true;
                        $A.util.removeClass(dropshipAddressElement, 'field-error');
                        $A.util.addClass(dropshipAddressRequiredText, 'slds-hide');
                    } else {
                        dropshipAddressValid = false;
                        $A.util.addClass(dropshipAddressElement, 'field-error');
                        $A.util.removeClass(dropshipAddressRequiredText, 'slds-hide');
                    };
                    console.log(valid, 'valid------------');
                    // 下一步
                    if (billToAddressValid && shipToAddressValid && valid && dropshipAddressValid) {
                        console.log('下一步校验通过-----------');
                        helper.handleNextStep(component, true);
                    } else {
                        var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": "Warning",
                            "message": $A.get("$Label.c.CCM_FillRequiredFields"),
                            "type": "warning",
                            "duration": "dismissible"
                        }).fire();
                    }
            }
        } else {
            // 下一步
            if (billToAddressValid && shipToAddressValid) {
                console.log('下一步校验通过-----------');
                helper.handleNextStep(component, true);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Warning",
                    "message": $A.get("$Label.c.CCM_FillRequiredFields"),
                    "type": "warning",
                    "duration": "dismissible"
                }).fire();
            }
        }
        
        // component.set("v.currentStep", component.get("v.currentStep") + 1);
    },
    // 上一步
    previousStep: function(component, event, helper){
        // var currentStep = component.get("v.currentStep");
        // var actionType = component.get("v.actionType");
        // if (actionType !== 'edit') {
        //     component.set('v.actionType', 'draft');
        // }
        // component.set("v.currentStep", currentStep - 1);
        helper.handleNextStep(component, false);
    },
    // 取消事件
    cancel: function(component){
        let url = window.location.origin + '/lightning/n/Purchase_Order_List';
        window.open(url, '_self');
    },
    // 监听DropshipType
    changeDropshipType: function(component, event, helper){
            let dropshipTypeValue = component.get('v.dropshipType');
            if (dropshipTypeValue == 'End Consumer') {
                component.set('v.dropshipAddressFlag', true);
                component.set('v.showDetailfield', true);
                component.set('v.dropshipAddress', {Id: null, Name: null});
                component.set('v.showShip', true);
            } else if (dropshipTypeValue && dropshipTypeValue != 'End Consumer') {
                component.set('v.dropshipAddressFlag', false);
                component.set('v.showDetailfield', false);
                component.set('v.dropshipAddress', dropshipAddress.Id ? dropshipAddress : component.get('v.defaultDropAddressInfo'));
                component.set('v.showShip', false);
                component.set('v.shipToAddress', {Id: null, Name: null});
                // 清空详细信息
                component.set('v.DropShipName', '');
                component.set('v.DropShipAddress1', '');
                component.set('v.DropShipAddress2', '');
                component.set('v.DropShipPhone', '');
                component.set('v.DropShipCountry', {Id: null, Name: null});
                component.set('v.DropShipCity', '');
                component.set('v.DropShipZip', '');
                component.set('v.DropShipState', '');
            } else if (!dropshipTypeValue) {
                component.set('v.showShip', true);
            }
            if (component.get('v.isDropShip') == 'Y') {
                // 校验必填
                helper.getElementRequiredError(component, 'dropshipType');
            }
    },
    // 检验必填
    changeBillToAddress: function(component, event, helper){
        let billToAddress = JSON.parse(JSON.stringify(component.get('v.billToAddress')));
        console.log(billToAddress, 'billToAddress========');
        
        const billToAddressElement = component.find('billToAddress');
        const billToAddressRequiredText = component.find('billToAddress-error-required');
        if (billToAddress.Id) {
            $A.util.removeClass(billToAddressElement, 'field-error');
            $A.util.addClass(billToAddressRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(billToAddressElement, 'field-error');
            $A.util.removeClass(billToAddressRequiredText, 'slds-hide');
        }
    },
    changeShipToAddress: function(component, event, helper){
        let shipToAddress = JSON.parse(JSON.stringify(component.get('v.shipToAddress')));
        console.log(shipToAddress, 'shipToAddress========');
        
        const shipToAddressElement = component.find('shipToAddress');
        const shipToAddressRequiredText = component.find('shipToAddress-error-required');
        if (shipToAddress.Id) {
            $A.util.removeClass(shipToAddressElement, 'field-error');
            $A.util.addClass(shipToAddressRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(shipToAddressElement, 'field-error');
            $A.util.removeClass(shipToAddressRequiredText, 'slds-hide');
        }
    },
    changeDropshipAddress: function(component, event, helper){
        if (component.get('v.dropshipType') != 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            let dropshipAddress = component.get('v.dropshipAddress');
            console.log(JSON.stringify(dropshipAddress), 'dropshipAddress========');
            const dropshipAddressElement = component.find('dropshipAddress');
            const dropshipAddressRequiredText = component.find('dropshipAddress-error-required');
            if (dropshipAddress.Id) {
                $A.util.removeClass(dropshipAddressElement, 'field-error');
                $A.util.addClass(dropshipAddressRequiredText, 'slds-hide');
                component.set('v.dropshipAddressId', dropshipAddress.Id);
            } else {
                $A.util.addClass(dropshipAddressElement, 'field-error');
                $A.util.removeClass(dropshipAddressRequiredText, 'slds-hide');
            }
        }
    },
    changeDropShipCountry: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            let DropShipCountry = JSON.parse(JSON.stringify(component.get('v.DropShipCountry')));
            console.log(DropShipCountry, 'DropShipCountry========');
            const dropShipCountryElement = component.find('dropShipCountry');
            const dropShipCountryRequiredText = component.find('dropShipCountry-error-required');
            if (DropShipCountry.Id) {
                $A.util.removeClass(dropShipCountryElement, 'field-error');
                $A.util.addClass(dropShipCountryRequiredText, 'slds-hide');
            } else {
                $A.util.addClass(dropShipCountryElement, 'field-error');
                $A.util.removeClass(dropShipCountryRequiredText, 'slds-hide');
            }
        }
    },
    changeDropShipName: function(component, event, helper){
        console.log('blur------------');
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropShipName');
        }
    },
    changeDropshipStreetAndStreetNo: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropshipStreetAndStreetNo');
        }
    },
    changeDropshipStreetAndStreetNo: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropshipStreetAndStreetNo');
        }
    },
    changeDropShipZip: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropShipZip');
        }
    },
    changeDropShipCity: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropShipCity');
        }
    },
    // 校验insuranceFee不为负数
    changeInsuranceFee: function(component, event, helper){
        let insuranceFee = Number(component.get('v.insuranceFee'));
        if (insuranceFee < 0) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": $A.get("$Label.c.CCM_GreaterThan0"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            component.set('v.insuranceFee', 0);
        }
    },
    // 校验insuranceFee不为负数
    changeOtherFee: function(component, event, helper){
        let otherFee = Number(component.get('v.otherFee'));
        console.log(otherFee, 'otherFee----------------');
        if (otherFee < 0) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": $A.get("$Label.c.CCM_GreaterThan0"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            component.set('v.otherFee', 0);
        }
    },
})