/**************************************************************************************************
 * Name: Quota_ActualSalesBatch 
 * Author: <PERSON>
 * Create Date: ********
 * Purpose: 计算实际销量
 * Modify History:
 **************************************************************************************************/ 
global without sharing class Quota_ActualSalesBatch implements Database.Batchable<sObject> {

    private class InvoiceItemFilter{
        private Set<String> salesGroupSet;
        private Set<Id> accountIdSet;
        private Set<String> brandSet;
        private Date startDate;
        private Date endDate;
        private Set<String> orgCode;

        private InvoiceItemFilter(){
            this.salesGroupSet = new Set<String>();
            this.accountIdSet = new Set<Id>();
            this.brandSet = new Set<String>();
            this.orgCode = new Set<String>();
        }
    }

    private class CalculateResult{
        private Decimal mtdAmount;
        private Decimal mtdLastYearAmount;
        private Decimal ytdAmount;
        private Decimal ytdLastYearAmount;
        private Decimal lastYearYearAmount;

        private CalculateResult(){
            this.mtdAmount = 0;
            this.mtdLastYearAmount = 0;
            this.ytdAmount = 0;
            this.ytdLastYearAmount = 0;
            this.lastYearYearAmount = 0;
        }
    }

    private Integer currentMonth;
    private Date startDate;
    private Date endDate;
    private Date minYear;
    private Date maxYear;

    private Set<Id> quotaIdSet;

    private String orgCode;
    global Quota_ActualSalesBatch(Date minYear, Date maxYear, Integer currentMonth, Set<Id> quotaIdSet) {
        this.minYear = minYear;
        this.maxYear = maxYear;
        this.currentMonth = currentMonth;
        this.startDate = Date.newInstance(minYear.Year(), minYear.Month(), MinYear.Day());
        this.endDate = Date.newInstance(MaxYear.Year(), MaxYear.Month(), MaxYear.Day());
        this.quotaIdSet = quotaIdSet;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        String queryStr = 
            ' SELECT Id, Account__c, Account__r.ORG_Code__c, Quota_Allocation__c, CurrencyIsoCode,  ' +
            ' Quota_Allocation__r.Start_Date__c, '+
            ' Quota_Allocation__r.End_Date__c, '+
            ' Last_MTD_Actual_Sales__c, Last_YTD_Actual_Sales__c, Last_Actual_Sales__c, ' +
            ' MTD_Actual_Sales__c, YTD_Actual_Sales__c,	Is_Other_Cutomer_Group__c,Other_Customer_Group_Member__c, ' +
            ' Parent_Quota_Detail__r.Territory_Relationship__r.Territory__r.Developer_Name__c, ' +
            ' Parent_Quota_Detail__r.Territory_Relationship__r.Territory__r.Territory_Name__c, ' +
            ' Parent_Quota_Detail__r.Territory_Relationship__r.Brand__r.Developer_Name__c, ' +
            ' Parent_Quota_Detail__r.Territory_Relationship__r.Brand__r.Brand_Name__c ' +
            ' FROM Quota_Allocation_Detail__c ';

        String filterStr = '';
        if(!this.quotaIdSet.isEmpty()){
            filterStr += ' AND Quota_Allocation__c IN:quotaIdSet ';
            system.debug('quotaIdSet'+quotaIdSet);
        }
        filterStr += ' AND Account__c != NULL ';//客户类型的 quota detail
        if(this.startDate != null){
            filterStr += ' AND Quota_Allocation__r.Start_Date__c >= ' + String.valueOf(this.startDate);
        }
        if(this.endDate != null){
            filterStr += ' AND Quota_Allocation__r.End_Date__c <= ' + String.valueOf(this.endDate);
        }
        filterStr += ' AND Actived__c = true ';
        filterStr += ' AND Quota_Allocation__r.Actived__c = true ';
        if(String.isNotBlank(filterStr)){
            //将第一个 'AND' 替换 'WHERE'
            queryStr += ' WHERE ' + filterStr.removeStart(' AND ');
        }
        system.debug('Quota_Allocation_Detail__cQuota_Allocation_Detail__c' + queryStr);
        return Database.getQueryLocator(queryStr);
    }

    global void execute(Database.BatchableContext BC, list<Sobject> scope) {
        //try{
            InvoiceItemFilter invoiceItemFilter = new InvoiceItemFilter();
            invoiceItemFilter.startDate = this.startDate.addYears(-1);//由于需要计算 "去年", 所以日期再往前推一年
            invoiceItemFilter.endDate = this.endDate;
            

            Set<Id> quotaDetailIdSet = new Set<Id>();

            for(Quota_Allocation_Detail__c quotaDetail : (List<Quota_Allocation_Detail__c>)scope){
                if (quotaDetail.Account__r.ORG_Code__c == 'EEG' || quotaDetail.Account__r.ORG_Code__c == '') {
                    invoiceItemFilter.orgCode.add(quotaDetail.Account__r.ORG_Code__c);
                    if(quotaDetail.Is_Other_Cutomer_Group__c && quotaDetail.Other_Customer_Group_Member__c.contains('-')){

                        for(String otherAccId: quotaDetail.Other_Customer_Group_Member__c.split('-')){
                            invoiceItemFilter.accountIdSet.add((Id)otherAccId);
                        }

                    }else{
                        invoiceItemFilter.accountIdSet.add(quotaDetail.Account__c);
                    }
                    
                    if(
                        quotaDetail.Parent_Quota_Detail__c != null 
                        && quotaDetail.Parent_Quota_Detail__r.Territory_Relationship__c != null
                    ){
                        if(quotaDetail.Parent_Quota_Detail__r.Territory_Relationship__r.Territory__c != null){
                            invoiceItemFilter.salesGroupSet.add(quotaDetail.Parent_Quota_Detail__r.Territory_Relationship__r.Territory__r.Territory_Name__c);
                        }
                        if(quotaDetail.Parent_Quota_Detail__r.Territory_Relationship__r.Brand__c != null){
                            invoiceItemFilter.brandSet.add(quotaDetail.Parent_Quota_Detail__r.Territory_Relationship__r.Brand__r.Brand_Name__c);
                        }
                    }
                }
                
                quotaDetailIdSet.add(quotaDetail.Id);
            }
            system.debug('invoiceItemFilterinvoiceItemFilter'+invoiceItemFilter);
            List<AggregateResult> invoiceItemArList = getInvoiceItemAggregateResultList(invoiceItemFilter);
            system.debug(invoiceItemArList.size());
            calculateActualSalesByORGCode((List<Quota_Allocation_Detail__c>)scope, invoiceItemArList, 'EEG', this.currentMonth);

        //}catch(Exception ex){
        //    Quota_Generate_Utils.exceptionLog (ex, 'Quota_ActualSalesBatch', 'execute');
        //}
    }

    /**
     * @description: finish method
     */
    global void finish(Database.BatchableContext BC) {
        //迭代统计实际销量
        Quota_ActualSalesCountBatch actualSalesCountBatch = new Quota_ActualSalesCountBatch(this.minYear, this.maxYear, this.quotaIdSet);
        Integer batchSize = String.isNotEmpty(System.Label.Quota_Generate_Batch_Size) ? Integer.valueOf(System.Label.Quota_Generate_Batch_Size) : 200;
        Database.executeBatch(actualSalesCountBatch, batchSize);
    }

    /**
     * @description: get invoice by sales group, customer, invoice date and invoice source
     */
    private static List<AggregateResult> getInvoiceItemAggregateResultList(InvoiceItemFilter invoiceItemFilter){
        String queryStr =
            ' SELECT CurrencyIsoCode currencyIsoCode, ' + //币种
            ' CALENDAR_YEAR(Invoice__r.Invoice_Date__c) year, '+ //年
            ' CALENDAR_MONTH(Invoice__r.Invoice_Date__c) month, '+ //月
            ' Sum(Amount__c) amount, ' + //金额
            ' Invoice__r.Customer__c accountId, ' + //客户
            ' Sales_Group__r.Sales_Group__c salesGroup, ' + //销售组
            ' Catalog_Item__r.Brand_Name__c brand ' + //品牌
            ' FROM Invoice_Item__c ';

        String filterStr = '';

        if(!invoiceItemFilter.accountIdSet.isEmpty()){
            Set<Id> accountIdSet = invoiceItemFilter.accountIdSet;
            filterStr += ' AND Invoice__r.Customer__c IN :accountIdSet';
        }

        if(!invoiceItemFilter.salesGroupSet.isEmpty()){
            Set<String> salesGroupSet = invoiceItemFilter.salesGroupSet;
        }

        if(!invoiceItemFilter.brandSet.isEmpty()){
            Set<String> brandSet = invoiceItemFilter.brandSet;
        }

        if(invoiceItemFilter.startDate != null){
            Date startDate = invoiceItemFilter.startDate;
            filterStr += ' AND Invoice__r.Invoice_Date__c >= :startDate';
        }

        if(invoiceItemFilter.endDate != null){
            Date endDate = invoiceItemFilter.endDate;
            filterStr += ' AND Invoice__r.Invoice_Date__c <= :endDate';
        }


        filterStr += ' AND Amount__c != NULL AND Invoice__r.Order__r.RecordType.DeveloperName NOT IN (\'Warranty_Claim\', \'Warranty_Order\') ';


        if(String.isNotBlank(filterStr)){
            //将第一个 'AND' 替换 'WHERE'
            queryStr += ' WHERE ' + filterStr.removeStart(' AND ');
        }

        queryStr += ' GROUP BY ' +
            ' Invoice__r.Customer__c, ' +
            ' Sales_Group__r.Sales_Group__c, ' +
            ' Catalog_Item__r.Brand_Name__c, ' +
            ' CALENDAR_YEAR(Invoice__r.Invoice_Date__c), ' +
            ' CALENDAR_MONTH(Invoice__r.Invoice_Date__c), ' +
            ' CurrencyIsoCode ';

       
        System.debug(LoggingLevel.INFO, '***Invoice_Item__cInvoice_Item__c: ' + queryStr);
        system.debug(invoiceItemFilter.accountIdSet);
        system.debug(invoiceItemFilter.salesGroupSet);
        return Database.query(queryStr);
    }

    /**
     * @description: generate map for invoice item
     */
    private static Map<String, Map<Integer, Decimal>> getKey2yearAmountMap(String orgCode, List<AggregateResult> invoiceItemArList, Map<String, Decimal> isoRateMap){
        Map<String, Map<Integer, Decimal>> key2yearAmountMap = new Map<String, Map<Integer, Decimal>>();

        for(AggregateResult invoiceItemAr : invoiceItemArList){
            if(
                invoiceItemAr.get('year') == null
                || invoiceItemAr.get('month') == null
                || invoiceItemAr.get('accountId') == null
            ){
                continue;
            }

            //key = year + Customer__c + Sales_Group__c + Brand_Name__c
            String key = String.valueOf(invoiceItemAr.get('year'));
            key += '-' + String.valueOf(invoiceItemAr.get('accountId'));
            

            //金额
            Decimal amount = Decimal.valueOf(String.valueOf(invoiceItemAr.get('amount')));
            System.debug('*** Quota_ActualSalesBatch Invoice Item amount: ' + amount);
            //币种
            String currencyIsoCode = String.valueOf(invoiceItemAr.get('currencyIsoCode'));
            //汇率
            Decimal rate = isoRateMap.get(currencyIsoCode);
            //Decimal rate = 1;
            System.debug('*** Quota_ActualSalesBatch Invoice Item Currency: ' + currencyIsoCode + ' --- ' + rate);
            if(rate == null) rate = 1;
            //换算成系统默认币种的金额
            amount = amount / rate;
            System.debug('*** Quota_ActualSalesBatch Invoice Item convert amount: ' + String.valueOf(amount));

            if(!key2yearAmountMap.containsKey(key)){
                key2yearAmountMap.put(key, new Map<Integer, Decimal>());
            }

            Map<Integer, Decimal> month2amountMap = key2yearAmountMap.get(key);

            Integer month = Integer.valueOf(invoiceItemAr.get('month'));

            if(!month2amountMap.containsKey(month)){
                month2amountMap.put(month, 0);
            }

            Decimal amountTemp = month2amountMap.get(month);
            amountTemp += amount;

            month2amountMap.put(month, amountTemp);
        }

        return key2yearAmountMap;
    }

    /**
     * @description: set calaulate result to sales target detail
     */
    private static CalculateResult getActualSales(
        Boolean isCurrentYear,
        Integer currentMonth,
        Map<String, Decimal> isoRateMap,
        Quota_Allocation_Detail__c quotaDetail,
        Map<Integer, Decimal> month2amountMap,
        String mtdField,
        String ytdField,
        String yearSalesField
    ){
        CalculateResult result = new CalculateResult();
        if(month2amountMap == null) return result;
        
        //detail 汇率
        Decimal rate = isoRateMap.get(quotaDetail.CurrencyIsoCode);
        System.debug('*** Quota_ActualSalesBatch Sales Target Detail Currency: ' + quotaDetail.CurrencyIsoCode + ' --- ' + rate);
        if(rate == null) rate = 1;

        //当前 quota 的开始月, 与结束月
        Integer startMonth = quotaDetail.Quota_Allocation__r.Start_Date__c.month();
        Integer endMonth = quotaDetail.Quota_Allocation__r.End_Date__c.month();
        
        Decimal mtdAmount = 0;
        if(currentMonth >= startMonth && currentMonth <= endMonth){
            mtdAmount = month2amountMap.get(currentMonth);
        }
        system.debug('month2amountMapmonth2amountMapmonth2amountMap'+month2amountMap);
        if(mtdAmount == null) mtdAmount = 0;
        Decimal ytdAmount = 0;

        Decimal yearAmount = 0;

        for(Integer month : month2amountMap.keySet()){

            Decimal amount = month2amountMap.get(month);
            if(amount == null) amount = 0;

            //算去年总 sales 时, 从1月~12月
            //Last_Actual_Sales__c
            if(yearSalesField == 'Last_Actual_Sales__c'){
                yearAmount += amount;
            }

            //算去年 YTD 时, 从1月到现在
            //Last_YTD_Actual_Sales__c,
            if(ytdField == 'Last_YTD_Actual_Sales__c'){
                if(month >= 1 && month <= currentMonth){
                    //去年 ytd 销量累加
                    ytdAmount += amount;
                }
            }

            //算当前年时
            //如果是当前年的计算, 需要参考 quota 的周期
            if(isCurrentYear){
                if(month < startMonth || month > endMonth) continue;
            }

            //算当前年 YTD 时
            //YTD_Actual_Sales__c, 需要判断 quota 的周期, startMonth ~ 现在
            if(ytdField == 'YTD_Actual_Sales__c'){
                if(month >= startMonth && month <= currentMonth){
                    //今年 ytd 销量累加
                    ytdAmount += amount;
                }
            }
        }

        //需要跟实际的记录比较值时, 需要换算汇率
        mtdAmount = mtdAmount * rate;
        ytdAmount = ytdAmount * rate;
        yearAmount = yearAmount * rate;

        if(Decimal.valueOf(String.valueOf(quotaDetail.get(mtdField))) != mtdAmount){
            result.mtdAmount = mtdAmount;
        }

        if(Decimal.valueOf(String.valueOf(quotaDetail.get(ytdField))) != ytdAmount){
            result.ytdAmount = ytdAmount;
        }

        if(yearSalesField == 'Last_Actual_Sales__c'){
            if(Decimal.valueOf(String.valueOf(quotaDetail.get(yearSalesField))) != yearAmount){
                result.lastYearYearAmount = yearAmount;
            }
        }

        return result;
    }

    /**
     * @Description: calcualte actual Sales by different org code
     */
    private static void calculateActualSalesByORGCode(List<Quota_Allocation_Detail__c> detailList, List<AggregateResult> invoiceItemArList, String orgCode, Integer month) {
        //获取币种汇率map
        Map<String, Decimal> isoRateMap = Quota_Generate_Utils.getIsoRateMap();
        Map<String, Map<Integer, Decimal>> key2yearAmountMap = new Map<String, Map<Integer, Decimal>>();
        if (orgCode == 'EEG') {
            key2yearAmountMap = getKey2yearAmountMap('EEG', invoiceItemArList, isoRateMap);
            system.debug('key2yearAmountMapkey2yearAmountMap'+key2yearAmountMap);
        } else {
            key2yearAmountMap = getKey2yearAmountMap(CCM_Constants.ORG_CODE_CCA, invoiceItemArList, isoRateMap);
        }
        List<Quota_Allocation_Detail__c> qdUpdateList = new List<Quota_Allocation_Detail__c>();

        for(Quota_Allocation_Detail__c quotaDetail : detailList){
            //key = year + Customer__c + Sales_Group__c + Brand_Name__c
            Integer year = quotaDetail.Quota_Allocation__r.Start_Date__c.year();
            String key = String.valueOf(year);
            String lastYearKey = String.valueOf(year - 1);

            key += '-' + quotaDetail.Account__c;
            lastYearKey += '-' + quotaDetail.Account__c;
            if (quotaDetail.Parent_Quota_Detail__c != null && quotaDetail.Parent_Quota_Detail__r.Territory_Relationship__c != null){
            }

            if (quotaDetail.MTD_Actual_Sales__c == null) quotaDetail.MTD_Actual_Sales__c = 0;
            if (quotaDetail.YTD_Actual_Sales__c == null) quotaDetail.YTD_Actual_Sales__c = 0;
            if (quotaDetail.Last_MTD_Actual_Sales__c == null) quotaDetail.Last_MTD_Actual_Sales__c = 0;
            if (quotaDetail.Last_YTD_Actual_Sales__c == null) quotaDetail.Last_YTD_Actual_Sales__c = 0;
            if (quotaDetail.Last_Actual_Sales__c == null) quotaDetail.Last_Actual_Sales__c = 0;

            Boolean flag = false;
            CalculateResult result = new CalculateResult();
            CalculateResult lastYearResult = new CalculateResult();
            CalculateResult otherCustomerResult = new CalculateResult();
            
            if(quotaDetail.Is_Other_Cutomer_Group__c && quotaDetail.Other_Customer_Group_Member__c.contains('-')){
                for(String otherAccId: quotaDetail.Other_Customer_Group_Member__c.split('-')){
                    CalculateResult subResult1 = new CalculateResult();
                    CalculateResult subResult2 = new CalculateResult();
                    Integer otherGourpYear = quotaDetail.Quota_Allocation__r.Start_Date__c.year();
                    String otherGourpKey = String.valueOf(otherGourpYear);
                    String otherGourpLastYearKey = String.valueOf(otherGourpYear - 1);

                    otherGourpKey += '-' + otherAccId;
                    otherGourpLastYearKey += '-' + otherAccId;
                    if (key2yearAmountMap.containsKey(otherGourpKey)){
                        subResult1 = getActualSales(true, month, isoRateMap, quotaDetail, key2yearAmountMap.get(otherGourpKey), 'MTD_Actual_Sales__c', 'YTD_Actual_Sales__c', null);
                        otherCustomerResult.mtdAmount += subResult1.mtdAmount;
                        otherCustomerResult.ytdAmount += subResult1.ytdAmount;
                        flag = true;
                    }

                    if (key2yearAmountMap.containsKey(otherGourpLastYearKey)){
                        subResult2 = getActualSales(false, month, isoRateMap, quotaDetail, key2yearAmountMap.get(otherGourpLastYearKey), 'Last_MTD_Actual_Sales__c', 'Last_YTD_Actual_Sales__c', 'Last_Actual_Sales__c');
                        otherCustomerResult.mtdLastYearAmount += subResult2.mtdAmount;
                        otherCustomerResult.ytdLastYearAmount += subResult2.ytdAmount;
                        otherCustomerResult.lastYearYearAmount += subResult2.lastYearYearAmount;
                        flag = true;
                    }
                }

                quotaDetail.MTD_Actual_Sales__c = otherCustomerResult.mtdAmount;
                quotaDetail.YTD_Actual_Sales__c = otherCustomerResult.ytdAmount;
                quotaDetail.Last_MTD_Actual_Sales__c = otherCustomerResult.mtdLastYearAmount;
                quotaDetail.Last_YTD_Actual_Sales__c = otherCustomerResult.ytdLastYearAmount;
                quotaDetail.Last_Actual_Sales__c = otherCustomerResult.lastYearYearAmount;

            }else{
                if (key2yearAmountMap.containsKey(key)){
                    result = getActualSales(true, month, isoRateMap, quotaDetail, key2yearAmountMap.get(key), 'MTD_Actual_Sales__c', 'YTD_Actual_Sales__c', null);
                    flag = true;
                }

                if (key2yearAmountMap.containsKey(lastYearKey)){
                    lastYearResult = getActualSales(false, month, isoRateMap, quotaDetail, key2yearAmountMap.get(lastYearKey), 'Last_MTD_Actual_Sales__c', 'Last_YTD_Actual_Sales__c', 'Last_Actual_Sales__c');
                    flag = true;
                }
                quotaDetail.MTD_Actual_Sales__c = result.mtdAmount;
                quotaDetail.YTD_Actual_Sales__c = result.ytdAmount;
                quotaDetail.Last_MTD_Actual_Sales__c = lastYearResult.mtdLastYearAmount;
                quotaDetail.Last_YTD_Actual_Sales__c = lastYearResult.ytdLastYearAmount;
                quotaDetail.Last_Actual_Sales__c = lastYearResult.lastYearYearAmount;
            }
            if (flag) {
                qdUpdateList.add(quotaDetail);
            }

        }
        system.debug('calculateActualSalesByORGCode'+qdUpdateList);
        if(!qdUpdateList.isEmpty()) UPDATE qdUpdateList;
    }
}