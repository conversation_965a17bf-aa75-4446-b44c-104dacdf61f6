.THIS {}

.THIS .ccm_widthStyle {
    width: 48%;
}

.THIS .ccm_iconStyle {
    width: 0%;
    padding-top: 2.5%;
    margin-left: -25px;
    color: blue;
}

.THIS .slds-button_icon-bare {
    line-height: 1;
    vertical-align: middle;
    color: blue;
}
.THIS .address-wrap .slds-combobox__input {
    width: 100%;
}
.THIS .address-content {
    margin-top: 10px;
    margin-left: 0px;
}
.THIS .cCCM_Community_LookUp {
    display: flex;
    flex-direction: column;
}
.THIS .cCCM_Community_LookUp .slds-form-element__control {
    display: block;
}
.THIS .cCCM_Community_LookUp .slds-combobox__form-element {
    margin-top: 0 !important;
}
.THIS .footer-wrap {
    display: flex;
    justify-content: center;
}
.THIS .slds-button_outline-brand {
    color: #90c41f !important;
    border-color: #90c41f !important;
}
.THIS .previous-btn {
    margin: 0 1.5rem;
}

.THIS .previous-btn-left {
    margin-left: 1.5rem;
}

.THIS .freight-cost-wrap {
    position: relative;
}
.THIS .hlep-wrap {
    position: absolute;
    top: 1px;
    left: 90px;
}
.THIS .hlep-wrap .slds-button_icon-bare {
    color: #747474 !important;
}
.THIS .field-required {
    margin-left:0px;
}
.THIS .field-required .slds-form-element__control::before{
    content:"*";
    display: inline-block;
    transform: scale(1.5);
    position:relative;
    color:rgb(194, 57, 52);
    position: absolute;
    left: -.5rem;
    top: 50%;
    transform: translateY(-50%);
}
.THIS .field-error.input{
    border: 2px solid rgb(194, 57, 52);
}
.THIS .field-error input, .THIS .field-error .slds-dueling-list__options{
    border:2px solid rgb(194, 57, 52);
}
.THIS .required-wrap {
    position: relative;
}
.THIS .error-text{
    color: rgb(194, 57, 52);
    display: flex;
    justify-content: end;
    position: absolute;
    bottom: 0px;
    right: 12px;
}
.THIS .search-wrap {
    margin-bottom: 18px;
}
.THIS .currency-wrap {
    position: absolute;
    width: 7px;
    overflow: hidden;
    left: 12px;
    top: 33px;
    z-index: 5;
}
.THIS .freight-cost-wrap .slds-input {
    padding-left: 22px !important;
}