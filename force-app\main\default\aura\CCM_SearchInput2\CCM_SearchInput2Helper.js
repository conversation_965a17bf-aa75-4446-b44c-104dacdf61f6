({
    fireEvent:function(comp,eventType,data)
    {
        //Obtain an event
        var event=comp.getEvent(eventType);
        //The value is set to the parameter of the event
        event.setParams({
            type:eventType,
            data:data
        });
        console.log('search---------------');
        //Emission
        event.fire();
    },

    //fetch the initialization inputValue
    getInputValue:function(comp)
    {
        var options=comp.get('v.options');
        if(Array.isArray(options))
        {
            var value=comp.get('v.value');
            var inputValue=comp.get('v.inputValue');
            // var descVal = comp.get('v.description');
            //value is valid
            if(this.isValidString(value))
            {                
                for(var i=0,len=options.length;i<len;i++)
                {
                    var option=options[i];
                    var _value=option.value;
//                    var _label=option.label;
                    var _label=inputValue;
                    if(value===_value)
                    {
//                        if(inputValue!==_label)
//                        {
                            comp.set('v.inputValue',_label);
                            break;                         
//                        }
                                              
                    }
                }                
            }            
        }
    },
    isValidString:function(str)
    {
        return str!==undefined&&str!==null&&str!=='';
    },
    updateUl:function(comp)
    {
        //Clear the previous requestIdleCallback
        if(Array.isArray(comp.idleIds))
        {
            comp.idleIds.forEach(function(id){
                window.cancelIdleCallback(id);
            });
            comp.idleIds=[];
        }

        var ulComp=comp.find('ul');
        if(ulComp)
        {
            var ul=ulComp.getElement();
            if(ul)
            {
                var options=comp.get("v.options");
                if(Array.isArray(options))
                {
                    var lisOld=Array.from(ul.querySelectorAll('li')); 
                    var numPerBatch=100;
                    var num=options.length;
                    var batches=Math.ceil(num/numPerBatch);

                    //If the options are valid, you need to clear the ul
                    while(ul.firstChild)
                    {
                        ul.removeChild(ul.firstChild);
                    }
                    for(var batch=0;batch<batches;batch++)
                    {
                        if(batch===0)
                        {                            
                            this.doBatchWork(ul,lisOld,options,batch,numPerBatch);
                        }
                        else
                        {
                            var self=this;                            
                            (function(batch){
                                //Don't set the timeout property, completely depends on the browse idle                           
                                var id=window.requestIdleCallback(function(idleDeadline){                                    
                                    self.doBatchWork(ul,lisOld,options,batch,numPerBatch);
                                });
                                if(!Array.isArray(comp.idleIds))
                                {
                                    comp.idleIds=[];
                                }
                                comp.idleIds.push(id);
                            })(batch);
                        }
                    }
                } 
            }
        }
    },
    doBatchWork:function(ul,lisOld,options,batch,numPerBatch)
    {
        var df=document.createDocumentFragment();
        var indexStart=numPerBatch*batch;
        var indexEnd=Math.min(numPerBatch*(batch+1)-1,options.length-1);
        var lis=[];
        for(var i=indexStart;i<=indexEnd;i++)
        {
            var option=options[i];
            var li=lisOld[i];
            if(!li)
            {
                li=document.createElement('li');
            }
            li.setAttribute('data-value',option.value);
            li.innerHTML = option.label;
            lis.push(li);
        }        
        lis.forEach(function(li){
            df.appendChild(li);
        });

        ul.appendChild(df);
    },
    polyfill:function(){
        window.requestIdleCallback = window.requestIdleCallback || function(handler) {
          var startTime = Date.now();
         
          return setTimeout(function() {
            handler({
              didTimeout: false,
              timeRemaining: function() {
                return Math.max(0, 50.0 - (Date.now() - startTime));
              }
            });
          }, 1);
        };
        window.cancelIdleCallback = window.cancelIdleCallback || function(id) {
          clearTimeout(id);
        };
    }    
})