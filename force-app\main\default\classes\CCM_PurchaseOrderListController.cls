/**
* Author: Honey
* Description: 查询Purchase Order List返回给前端
* date : 2023/06/09
*/
public without sharing class CCM_PurchaseOrderListController {
    //数据权限UserType
    public static String userType = '';
    public static String status = 'Booked';
    //功能权限userType
    public static String functionUserType = '';
    public static String aggregateSqlOrder = 'SELECT count(Id) total2 FROM Purchase_Order__c WHERE IsDeleted = false  AND Sync_Status__c != \'Success\' AND Status__c NOT IN (\'Booked\', \'Cancelled\') ';
    
    /**Description:对应Invoice Information,Order Information的数据 */
       
    @AuraEnabled
    public static String getPurchaseOrderInfo(Integer pageNumber, Integer pageSize, String fifter, Boolean IsPortal){
        system.debug('fifter-->'+fifter);
        String sqlOrderString =  'SELECT Id, '
        + 'Name, '
        + 'Customer__c,'
        + 'Customer__r.Name,'
        + 'Salesperson__c, Salesperson__r.Name,'
        + 'Model__c,'
        + 'Customer__r.AccountNumber,'
        + 'Status__c, Order_Number__c  , Order_Type__c,'
        + 'Submit_Date__c, CurrencyIsoCode,'
        + 'Order_Create_Date_In_EBS__c,'
        + 'Total_Amount__c, '
        + 'CreatedById, '
        + 'Customer_PO_Num__c,'
        + 'CreatedBy.Name,RecordTypeId,ToLabel(RecordType.Name) ,'
        + 'CreatedBy.FirstName,'
        + 'CreatedBy.LastName, '
        + 'LastModifiedDate, '
        + 'Sync_Date__c, '
        + 'Sync_Status__c, '
        + 'Sync_Message__c '
        + 'FROM Purchase_Order__c '
        + 'WHERE IsDeleted = false AND Status__c NOT IN (\'Booked\', \'Cancelled\') ';
        try{
            User usr = Util.getUserInfo(UserInfo.getUserId());
            InitData initD = new InitData();
            if (IsPortal){
                if (usr != null && String.isNotBlank(usr.Contact.AccountId)){
                    String userId = usr.Id;
                    sqlOrderString += 'AND Customer__c = \'' + usr.Contact.AccountId + '\' ';
                    sqlOrderString += 'AND CreatedById = :userId ';
                    aggregateSqlOrder += 'AND Customer__c = \'' + usr.Contact.AccountId + '\' ';
                    aggregateSqlOrder += 'AND CreatedById = :userId ';
                } else{
                    initD.isSuccess = false;
                    initD.errorMessage = 'No found the account information.';
                }
            } else{
                String profileName = [SELECT Id, Name
                                      FROM Profile
                                      WHERE Id = :UserInfo.getProfileId()].Name;
                Set<String> SpecialProfile = new Set<String>(Label.Special_Profile.split(',')) ;
                Set<String> InsideSalesProfile = new Set<String>(Label.Inside_Sales_Profile.split(',')) ;
                Set<String> SalesRepProfile = new Set<String>(Label.Sales_Rep_Profile.split(',')) ;
                
                system.debug('profileName--->'+profileName);
                if (InsideSalesProfile.contains(profileName) || SpecialProfile.contains(profileName)){
                    userType = 'InsideSales';
                    functionUserType = 'InsideSales';
                    if(SpecialProfile.contains(profileName) ){
                        functionUserType = 'SalesRep';
                    }
                } else if (SalesRepProfile.contains(profileName) ){
                    userType = 'SalesRep';
                    functionUserType = 'SalesRep';
                    sqlOrderString += ' AND CreatedById = \'' + UserInfo.getUserId() + '\' ';
                } else{
                    initD.isSuccess = false;
                    initD.errorMessage = 'This Profile can not see data.';
                }
            }
            system.debug('initD--->'+initD);
            if (initD.isSuccess){
                List<OrderInfo> wrappers = new List<OrderInfo>();
                Map<String, OrderInfo> OrderMap = new Map<String, OrderInfo>();
                //Order
                String filterCondition2 = getFilterCondition(fifter);
                if(String.isNotBlank(fifter)) {
                    FilterWrapper filters = (FilterWrapper)JSON.deserialize(fifter, FilterWrapper.class);
                    if(String.isNotBlank(filters.PurchaseStatus)) {
                        sqlOrderString = sqlOrderString.replace('AND Status__c NOT IN (\'Booked\', \'Cancelled\')', '');
                    }
                }
                sqlOrderString += filterCondition2;
                
                sqlOrderString += ' ORDER BY LastModifiedDate Desc LIMIT 2000 ';
                aggregateSqlOrder += filterCondition2;
                system.debug('aggregateSqlOrder--->'+aggregateSqlOrder);
                List<Purchase_Order__c> allOrderList = Database.query(sqlOrderString);
                if (allOrderList != null && allOrderList.size() > 0){
                    for (Purchase_Order__c objPurchaseorder : allOrderList){
                        OrderInfo orderInfo = new OrderInfo();
                        orderInfo.id = objPurchaseorder.Id;
                        orderInfo.Customer = objPurchaseorder.Customer__r != null ? objPurchaseorder.Customer__r.Name : null;
                        orderInfo.CustomerPo = objPurchaseorder.Customer_PO_Num__c;
                        orderInfo.CustomerNumber = objPurchaseorder.Customer__r.AccountNumber;
                        orderInfo.OrderNumber = objPurchaseorder.Order_Number__c;
                        orderInfo.PoNumber = objPurchaseorder.Name;
                        orderInfo.PurchaseOrderType = objPurchaseorder.RecordType.Name;
                        if(objPurchaseOrder.Status__c == 'New Order'){
                            orderInfo.PurchaseOrderStatus = Label.New_Order;
                        }else if(objPurchaseOrder.Status__c == 'Submitted' || objPurchaseOrder.Status__c == 'Pending Review'){
                            
                            orderInfo.PurchaseOrderStatus = Label.Submitted;
            
                        }else if(objPurchaseOrder.Status__c == 'Cancelled'){
                            orderInfo.PurchaseOrderStatus = Label.Stock_Cancelled;
                        }else if(objPurchaseOrder.Status__c == 'review in process'){
                            orderInfo.PurchaseOrderStatus =  Label.review_in_process;
                        }
                        //orderInfo.PurchaseOrderStatus = objPurchaseorder.Status__c;
                        orderInfo.TotalDueAmount = String.ValueOf(objPurchaseorder.Total_Amount__c); 
                        orderInfo.currencyCode = objPurchaseorder.CurrencyIsoCode;
                        orderInfo.OrderCreatedDateInEBS = objPurchaseorder.Order_Create_Date_In_EBS__c <> null ? objPurchaseorder.Order_Create_Date_In_EBS__c.format () : null;
                        orderInfo.CreatedBy = (objPurchaseorder.CreatedBy.FirstName == null ? '':objPurchaseorder.CreatedBy.FirstName)  +' '+
                        (objPurchaseorder.CreatedBy.LastName == null ? '':objPurchaseorder.CreatedBy.LastName) ;
                        orderInfo.viewStyleCss = 'showbtn';
                        if(orderInfo.PurchaseOrderStatus == Label.New_Order  && (objPurchaseorder.CreatedById == UserInfo.getUserId() ) ){
                            orderInfo.editStyleCss = 'showbtn';
                            orderInfo.deleteStyleCss = 'showbtn';
                        }
                        // }else if(objPurchaseOrder.Status__c == 'Submitted' && userType == 'InsideSales'){
                        //     orderInfo.editStyleCss = 'showbtn';
                        // }
                        
                        if(IsPortal &&objPurchaseorder.Customer__c ==  usr.Contact.AccountId && orderInfo.PurchaseOrderStatus ==Label.New_Order){
                            orderInfo.editStyleCss = 'showbtn';
                        }
                        orderInfo.lastModifiedDate = objPurchaseorder.LastModifiedDate;
                        orderInfo.isSynced = false;
                        if(objPurchaseorder.Sync_Date__c != null) {
                            orderInfo.isSynced = true;
                        }
                        orderInfo.syncStatus = objPurchaseorder.Sync_Status__c;
                        orderInfo.syncMessage = objPurchaseorder.Sync_Message__c;
                        OrderMap.put(orderInfo.id, orderInfo);
                        wrappers.add(orderInfo);
                    }
                     //model select
                    String model = ((FilterWrapper)JSON.deserialize(fifter, FilterWrapper.class)).Model;
                    if (String.isNotBlank(model)){
                        //不能子搜索父,得从orderItem 查
                        Map<String, OrderInfo> OrderMapReturn = new Map<String, OrderInfo>();
                        model = '%' + model + '%';
                        List<Purchase_Order_Item__c> orderItemList = [select id, Purchase_Order__c
                                                            from Purchase_Order_Item__c
                                                            where Purchase_Order__c in:OrderMap.keySet() and Product__r.Order_Model__c like:model];
                        for (Purchase_Order_Item__c orderItem : orderItemList){
                            if (!OrderMapReturn.containsKey(orderItem.Purchase_Order__c)){
                                OrderMapReturn.put(orderItem.Purchase_Order__c, OrderMap.get(orderItem.Purchase_Order__c));
                            }
                        }
                        wrappers = OrderMapReturn.values();
                    } else{
                        wrappers = OrderMap.values();
                    }
                }
               

                if (wrappers != null && wrappers.size() > 0){
                    /*initD.allData = wrappers;*/
                    
                    //算出Order数据的总量
                    List<AggregateResult> resultOrder = Database.query(aggregateSqlOrder);
                    Integer countOrder = (Integer) resultOrder[0].get('total2');
                    initD.totalRecords = wrappers.size();
                    initD.currentData = getCurrentData(wrappers, pageNumber, pageSize);
                }
            
            System.debug('initD-->'+initD);
            return JSON.serialize(initD);
            }
        }catch(Exception e){
            system.debug('报错行数--->'+e.getLineNumber()+'报错信息-->'+e.getMessage());
        }
        
        return null;
        
    }
    
   
    
    @AuraEnabled
    public static String getPurchaseOrderExcel(String fifter, Boolean IsPortal){
        String sqlOrderString =  'SELECT Id, '
        + 'Name, '
        + 'Customer__c,'
        + 'Customer__r.Name,'
        +'Salesperson__c, Salesperson__r.Name,'
        +'Model__c,'
        + 'Customer__r.AccountNumber,'
        + 'Status__c, Order_Number__c  , Order_Type__c,'
        + 'Submit_Date__c, CurrencyIsoCode,'
        +'Order_Create_Date_In_EBS__c,'
        + 'Total_Amount__c, '
        + 'CreatedById, '
        + 'Customer_PO_Num__c,'
        + 'CreatedBy.Name,RecordTypeId,ToLabel(RecordType.Name) ,'
        +'CreatedBy.FirstName,'
        +'CreatedBy.LastName '
        + 'FROM Purchase_Order__c '
        + 'WHERE IsDeleted = false AND  Status__c != :status ';
        system.debug('fifter-->'+fifter);
        try{
            User usr = Util.getUserInfo(UserInfo.getUserId());
            InitData initD = new InitData();
            if (IsPortal){
                if (usr != null && String.isNotBlank(usr.Contact.AccountId)){
                    sqlOrderString += 'AND Customer__c = \'' + usr.Contact.AccountId + '\' ';
                    aggregateSqlOrder += 'AND Customer__c = \'' + usr.Contact.AccountId + '\' ';
                } else{
                    initD.isSuccess = false;
                    initD.errorMessage = 'No found the account information.';
                }
            } else{
                Set<String> SetSalesRepProfile = new Set<String>(Label.Sales_Rep_Profile.split(','));
                Set<String> SetInsideSalesProfile = new Set<String>(Label.Inside_Sales_Profile.split(','));
                String profileName = [SELECT Id, Name
                                      FROM Profile
                                      WHERE Id = :UserInfo.getProfileId()].Name;
                if (SetInsideSalesProfile.contains(profileName)){
                    userType = 'InsideSales';
                } else if (SetSalesRepProfile.contains(profileName)){
                    userType = 'SalesRep';
                    sqlOrderString += 'AND CreatedById = \'' + UserInfo.getUserId() + '\' ';
                } else{
                    initD.isSuccess = false;
                    initD.errorMessage = 'This Profile can not see data.';
                }
            }
            if (initD.isSuccess){
                List<OrderInfo> wrappers = new List<OrderInfo>();
                Map<String, OrderInfo> OrderMap = new Map<String, OrderInfo>();
                //Order
                String filterCondition2 = getFilterCondition(fifter);
                sqlOrderString += filterCondition2;
                
                sqlOrderString += ' ORDER BY LastModifiedDate Desc LIMIT 2000 ';
                aggregateSqlOrder += filterCondition2;
                system.debug('sqlOrderString-->'+sqlOrderString);
                List<Purchase_Order__c> allOrderList = Database.query(sqlOrderString);
                if (allOrderList != null && allOrderList.size() > 0){
                    for (Purchase_Order__c objPurchaseorder : allOrderList){
                        OrderInfo orderInfo = new OrderInfo();
                        orderInfo.id = objPurchaseorder.Id;
                        orderInfo.Customer = objPurchaseorder.Customer__r != null ? objPurchaseorder.Customer__r.Name : null;
                        orderInfo.CustomerPo = objPurchaseorder.Customer_PO_Num__c;
                        orderInfo.currencyCode = objPurchaseorder.CurrencyIsoCode;
                        // orderInfo.CustomerNumber = order.Account_Text__c;
                        orderInfo.OrderNumber = objPurchaseorder.Order_Number__c;
                        orderInfo.PoNumber = objPurchaseorder.Name;
                        orderInfo.PurchaseOrderType = objPurchaseorder.RecordType.Name;
                        if(objPurchaseOrder.Status__c == 'New Order'){
                            orderInfo.PurchaseOrderStatus = objPurchaseorder.Status__c;
                        }else if(objPurchaseOrder.Status__c == 'Submitted' || objPurchaseOrder.Status__c == 'Pending Review'){
                            
                            orderInfo.PurchaseOrderStatus = 'Submitted';
            
                        }
                        //orderInfo.PurchaseOrderStatus = objPurchaseorder.Status__c;
                        orderInfo.TotalDueAmount = String.ValueOf(objPurchaseorder.Total_Amount__c); 
                        
                        orderInfo.OrderCreatedDateInEBS = objPurchaseorder.Order_Create_Date_In_EBS__c <> null ? objPurchaseorder.Order_Create_Date_In_EBS__c.format () : null;
                        orderInfo.CreatedBy = objPurchaseorder.CreatedBy.Name;
                        orderInfo.viewStyleCss = 'showbtn';
                        if(orderInfo.PurchaseOrderStatus == 'New Order' && objPurchaseorder.CreatedById == UserInfo.getUserId() ){
                            orderInfo.editStyleCss = 'showbtn';
                        }else if(orderInfo.PurchaseOrderStatus == 'Submitted' && userType == 'InsideSales'){
                            orderInfo.editStyleCss = 'showbtn';
                        }
                        OrderMap.put(orderInfo.id, orderInfo);
                        wrappers.add(orderInfo);
                    }
                     //model select
                    String model = ((FilterWrapper)JSON.deserialize(fifter, FilterWrapper.class)).Model;
                    if (String.isNotBlank(model)){
                        //不能子搜索父,得从orderItem 查
                        Map<String, OrderInfo> OrderMapReturn = new Map<String, OrderInfo>();
                        model = '%' + model + '%';
                        List<Purchase_Order_Item__c> orderItemList = [select id, Purchase_Order__c
                                                            from Purchase_Order_Item__c
                                                            where Purchase_Order__c in:OrderMap.keySet() and Product__r.Order_Model__c like:model];
                        for (Purchase_Order_Item__c orderItem : orderItemList){
                            if (!OrderMapReturn.containsKey(orderItem.Purchase_Order__c)){
                                OrderMapReturn.put(orderItem.Purchase_Order__c, OrderMap.get(orderItem.Purchase_Order__c));
                            }
                        }
                        wrappers = OrderMapReturn.values();
                    } else{
                        wrappers = OrderMap.values();
                    }
                }
                
                

                if (wrappers != null && wrappers.size() > 0){
                    /*initD.allData = wrappers;*/
                    
                    //算出Order数据的总量
                    List<AggregateResult> resultOrder = Database.query(aggregateSqlOrder);
                    Integer countOrder = (Integer) resultOrder[0].get('total2');
                    initD.totalRecords = countOrder;
                    initD.currentData = wrappers;
                }
            
            System.debug('initD-->'+initD);
            return JSON.serialize(initD);
            }
        }catch(Exception e){
            system.debug('报错行数--->'+e.getLineNumber()+'报错信息-->'+e.getMessage());
        }
        
        return null;
        
    }
    public static String getFilterCondition(String filterString){
        String sqlString = '';
        if(String.isBlank(filterString)){
            return sqlString;
        }
        FilterWrapper filters = (FilterWrapper)JSON.deserialize(filterString, FilterWrapper.class);
        system.debug('orderType--->'+filters.OrderType);
        if (String.isNotBlank(filters.OrderType)){
            String recordType = '';
            if((String) filters.OrderType == 'PreSeason_Order'){
                recordType = 'Pre_season_Order';
            }else if((String) filters.OrderType == 'Regular_Order'){
                recordType = 'Regular_Order';
            }
            system.debug('recordType--->'+recordType);
            
            sqlString += ' AND RecordType.DeveloperName =\'' + recordType + '\'';
        }
        if (String.isNotBlank(filters.PurchaseOrderType)){
            String recordType = '';
            if((String) filters.PurchaseOrderType == 'PreSeason_Order'){
                recordType = 'Pre_season_Order';
            }else if((String) filters.PurchaseOrderType == 'Regular_Order'){
                recordType = 'Regular_Order';
            }
            system.debug('recordType--->'+recordType);
            
            sqlString += ' AND RecordType.DeveloperName =\'' + recordType + '\'';
        }
        if (String.isNotBlank(filters.CreatedBy)){
            system.debug('filters.CreatedBy--->'+filters.CreatedBy);
            sqlString += ' AND CreatedById = \'' + (String) filters.CreatedBy + '\'';
        }
        if (String.isNotBlank(filters.Customer)){
            sqlString += ' AND Customer__c = \'' + (String) filters.Customer + '\'';
            
        }
        if (String.isNotBlank(filters.CustomerNumber)){
            sqlString += ' AND Customer__r.AccountNumber like \'%' + (String) filters.CustomerNumber + '%\'';
        }
        if (String.isNotBlank(filters.CustomerPo)){
            sqlString += ' AND Customer_PO_Num__c like \'%' + (String) filters.CustomerPo + '%\'';
        }
        if (String.isNotBlank(filters.Model)){
              //sqlString += ' AND Purchase_Order_Item__r.Product__r.Order_Model__c LIKE \'%' + (String) filters.Model + '%\'';
        }
        if (String.isNotBlank(filters.OrderDateFrom)){
            
            sqlString += ' AND Submit_Date__c >=' + String.ValueOf(filters.OrderDateFrom);
        }
        if (String.isNotBlank(filters.OrderDateTo)){
            sqlString += ' AND Submit_Date__c  <=' + String.ValueOf(filters.OrderDateTo);
        }
        if (String.isNotBlank(filters.OrderNumber)){
            sqlString += ' AND Order_Number__c like\'%' + filters.OrderNumber + '%\'';
        }
        if (String.isNotBlank(filters.PurchaseStatus)){
            String statusCondition = '';
            String[] OrderStatusList = filters.PurchaseStatus.split(',');
            for (String st : OrderStatusList){
               
                statusCondition += '\'' + st + '\',';
                if(st == 'Submitted'){
                    String review = 'Pending Review';
                    statusCondition += '\'' + st + '\',';
                }
            }
            sqlString += ' AND Status__c in(' + statusCondition.removeEnd(',') + ')';
        }

        if (String.isNotBlank(filters.OrderStatus)){
            String statusCondition = '';
            String[] OrderStatusList = filters.OrderStatus.split(',');
            for (String st : OrderStatusList){
                statusCondition += '\'' + st + '\',';
            }
            sqlString += ' AND Status__c in(' + statusCondition.removeEnd(',') + ')';
        }

        if (String.isNotBlank(filters.PoNumber)){
            sqlString += ' AND Name like\'%' + filters.PoNumber + '%\'';
        }
        if (String.isNotBlank(filters.SalesPerson)){
            sqlString += ' AND  Salesperson__c  = \'' + filters.SalesPerson + '\'';
        }
        return sqlString;
    }

    public static String getFilterConditionResales(String filterString){
        String sqlString = '';
        if(String.isBlank(filterString)){
            return sqlString;
        }
        FilterWrapper filters = (FilterWrapper)JSON.deserialize(filterString, FilterWrapper.class);
        if (String.isNotBlank(filters.OrderType)){
            sqlString += ' AND order_Type__c =\'' + (String) filters.PurchaseOrderType + '\'';
        }
       
        if (String.isNotBlank(filters.CreatedBy)){
            sqlString += ' AND CreatedBy.Name like \'%' + (String) filters.CreatedBy + '%\'';
        }
        if (String.isNotBlank(filters.Customer)){
            sqlString += ' AND Customer__c = \'' + (String) filters.Customer + '\'';
            
        }
        if (String.isNotBlank(filters.CustomerNumber)){
            sqlString += ' AND Customer__r.AccountNumber like \'%' + (String) filters.CustomerNumber + '%\'';
        }
        if (String.isNotBlank(filters.CustomerPo)){
            sqlString += ' AND customer_Po__c like \'%' + (String) filters.CustomerPo + '%\'';
        }
        if (String.isNotBlank(filters.Model)){
             // sqlString += ' AND Sales_RepStock_ReSale_Product__r.Product__r.Order_Model__c LIKE \'%' + (String) filters.Model + '%\'';
        }
        if (String.isNotBlank(filters.OrderDateFrom)){
            
            sqlString += ' AND submit_Date__c >=' + String.ValueOf(filters.OrderDateFrom);
        }
        if (String.isNotBlank(filters.OrderDateTo)){
            sqlString += ' AND submit_Date__c  <=' + String.ValueOf(filters.OrderDateTo);
        }
        if (String.isNotBlank(filters.OrderNumber)){
           // sqlString += 'AND Order_Number__c like\'%' + filters.OrderNumber + '%\'';
        }
        if (String.isNotBlank(filters.PurchaseStatus)){
            String statusCondition = '';
            String[] OrderStatusList = filters.PurchaseStatus.split(',');
            for (String st : OrderStatusList){
                statusCondition += '\'' + st + '\',';
            }
            sqlString += ' AND Status__c in(' + statusCondition.removeEnd(',') + ')';
        }

        if (String.isNotBlank(filters.SalesPerson)){
            sqlString += ' AND  Salesperson__r.Name  like\'%' + filters.SalesPerson + '%\'';
        }
        return sqlString;
    }
    
    @AuraEnabled
    public static String startCalculatePriceList(String customerId) {
        Set<String> lstAccountIds = new Set<String>();
        lstAccountIds.add(customerId);
        CCM_UpsertLink1stPriceBatch batch = new CCM_UpsertLink1stPriceBatch(lstAccountIds, Label.SalesPrice_OrderType, 'Export Sales Price');
        batch.setStartTime(System.now());
        String jobId = Database.executeBatch(batch, 1);

        String OrderType = Label.SalesPrice_OrderType;
        List<AggregateResult> results = [SELECT Count(Id) total FROM Sales_Program__c WHERE (Modifier_1__c != null OR Modifier_2__c != null) AND Status__c = 'Active'
        AND Customer__r.Is_Update__c = true and Customer__r.RecordType.Name in ('Association Group','Channel Customer') 
        AND Customer__c IN : lstAccountIds
        AND Order_Type__c = : OrderType and(List_Price_1__c != null OR List_Price_2__c != null OR List_Price_3__c != null )];

        Boolean noDatainScope = false;
        for(AggregateResult result : results) {
            Integer total = Integer.valueOf(result.get('total'));
            if(total == 0) {
                noDatainScope = true;
            }
        }

        Log__c log = new Log__c();
        log.Name = 'Export Sales Price';
        log.ObjType__c = 'AsyncApexJob';
        log.RecordId__c = jobId;
        log.ResParam__c = 'In Progress';
        if(noDatainScope) {
            log.ResParam__c = 'Complete';
        }
        insert log;
        return jobId;
    }

    @AuraEnabled
    public static string getBatchResult(String jobId){
        List<Log__c> logs = [SELECT ResParam__c FROM Log__c WHERE Name = 'Export Sales Price' AND RecordId__c = :jobId];
        return JSON.serialize(logs);
    }

    @AuraEnabled
    public static String QueryAllProductInfo(String customerId,Date PrcingDate){
        // Set<String> lstAccountIds = new Set<String>();
        // lstAccountIds.add(customerId);
        // system.debug('重新计算Account--->'+lstAccountIds);
       
        // CCM_UpsertLink1stPriceBatch batch = new CCM_UpsertLink1stPriceBatch(lstAccountIds,Label.SalesPrice_OrderType);
        // Database.executeBatch(batch, 1);
        //计算成功后再执行
        system.debug('计算结果---->');
        String JsonAllProductInfo = CCM_RequestPurchaseOrderController.GetProductInfo('',customerId,PrcingDate);
        List<Product2> lstProduct = ( List<Product2>)JSON.deserialize(JsonAllProductInfo, List<Product2>.class);
        //获取产品Id
        List<String> lstProductIds = new List<String>();
        Map<String,Double> mapProdictId2Qty = new Map<String,Double>();
       
        for(Product2 objProduct : lstProduct){
            lstProductIds.add(objProduct.Id);
            mapProdictId2Qty.put(objProduct.Id, 1);
            
        }
        // List<PriceProductInfo> lstPriceInfo = new List<PriceProductInfo>();
        //通过customerID查询Customer信息
        Account objAccount = new Account();
        objAccount = [
            SELECT Id,Name,AccountNumber,CurrencyIsoCode FROM Account WHERE Id = :customerId
        ];
        
        Map<String,Map<String,Object>> mapProductId2Values = new Map<String,Map<String,Object>>();
        mapProductId2Values = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(customerId,lstProductIds,PrcingDate,mapProdictId2Qty);

        // 得到 productId 和 Pricebook_Entry__c 的映射关系
        Sales_Program__c authorizedBrand = [
            SELECT  Id, Price_Book__c, List_Price_1__c, List_Price_2__c, List_Price_3__c
	        FROM Sales_Program__c
	        WHERE customer__c = :customerId AND Status__c = 'Active' LIMIT 1
        ];

        String parentPb = authorizedBrand.Price_Book__c;
        List<String> childPb = new List<String>{authorizedBrand.List_Price_1__c, authorizedBrand.List_Price_2__c, authorizedBrand.List_Price_3__c};

        Map<Id, Pricebook_Entry__c> parentPbMap = new Map<Id, Pricebook_Entry__c>();
        Map<Id, Pricebook_Entry__c> childPbMap = new Map<Id, Pricebook_Entry__c>();
        List<Pricebook_Entry__c> listParentPb = [
            SELECT p.PriceBook__r.Name, p.Product__c
            FROM Pricebook_Entry__c p 
            WHERE p.PriceBook__r.Id = :parentPb AND p.Start_Date__c <= :PrcingDate AND p.End_Date__c >= :PrcingDate
        ];
        List<Pricebook_Entry__c> listChildPb = [
            SELECT p.PriceBook__r.Name, p.Product__c
            FROM Pricebook_Entry__c p 
            WHERE p.PriceBook__r.Id in :childPb AND p.Start_Date__c <= :PrcingDate AND p.End_Date__c >= :PrcingDate
        ];

        for(Pricebook_Entry__c entry : listParentPb){
            parentPbMap.put(entry.Product__c, entry);
        }

        for(Pricebook_Entry__c entry : listChildPb){
            childPbMap.put(entry.Product__c, entry);
        }

        // for(Product2 objProduct : lstProduct){
        //      PriceProductInfo objPriceInfo = new PriceProductInfo();
        //     Map<String,Object> mapFeild2Value = mapProductId2Values.get(objProduct.Id);
        //     // system.debug('mapFeild2Value--->'+mapFeild2Value);
        //     if(mapFeild2Value != null){
        //         objPriceInfo.ListPrice =  ((Decimal)mapFeild2Value.get(CCM_Constants.LIST_PRICE)).setScale(2,RoundingMode.HALF_UP) ;       
        //         objPriceInfo.salesPrice =  ((Decimal)mapFeild2Value.get(CCM_Constants.FINAL_PRICE)).setScale(2,RoundingMode.HALF_UP) ; 
        //         objPriceInfo.StartDate = (Date)mapFeild2Value.get(CCM_Constants.START_DATE) ; 

        //         objPriceInfo.EndDate = (Date)mapFeild2Value.get(CCM_Constants.END_DATE) ; 
        //         objPriceInfo.country = (String)mapFeild2Value.get('country') ; 
        //         objPriceInfo.countryDescription = (String)mapFeild2Value.get('countryDescription') ; 
        //         objPriceInfo.salesChannel = (String)mapFeild2Value.get('salesChannel') ; 
        //         objPriceInfo.Classification1 = (String)mapFeild2Value.get('Classification1') ; 
        //         objPriceInfo.productClass = (String)mapFeild2Value.get('productClass') ; 
        //         objPriceInfo.PPCCode = (String)mapFeild2Value.get('PPCCode') ; 
        //         objPriceInfo.PPCDescription = (String)mapFeild2Value.get('PPCDescription') ; 
        //         objPriceInfo.productSeries = (String)mapFeild2Value.get('ProductSeries') ; 
        //         objPriceInfo.modiferType = (String)mapFeild2Value.get('modiferType') ; 
        //         objPriceInfo.modifer = (String)mapFeild2Value.get('modifer') ; 
        //         objPriceInfo.orderType = (String)mapFeild2Value.get('orderType') ; 
        //         objPriceInfo.incoTerm = (String)mapFeild2Value.get('incoTerm') ; 
        //         // System.debug('parentPbMap.containsKey(objProduct.Id)='+parentPbMap);
        //         // System.debug('childPbMap.get(objProduct.Id)='+childPbMap.containsKey(objProduct.Id));
        //         // system.debug('objProduct.Id------->'+objProduct.Id);
               
        //         objPriceInfo.PriceName = (String)(parentPbMap.containsKey(objProduct.Id) ?
        //                                          parentPbMap.get(objProduct.Id).PriceBook__r.Name : childPbMap.get(objProduct.Id).PriceBook__r.Name);
        //     }
        //     // system.debug('objProduct--->'+objProduct);
        //     objPriceInfo.productName = objProduct.Name;
        //     objPriceInfo.productModel = objProduct.Order_Model__c;
        //     objPriceInfo.productDescription = objProduct.Item_Description_DE__c;  
        //     objPriceInfo.CurrencyCode = objAccount.CurrencyIsoCode;
        //     objPriceInfo.CustomerName = objAccount.Name;
        //     objPriceInfo.CustomerNumber = objAccount.AccountNumber;
        //     lstPriceInfo.add(objPriceInfo);
        //     // system.debug('objPriceInfo--->'+objPriceInfo);
        // }
        // return JSON.serialize(lstPriceInfo);

        String jobId = Database.executeBatch(new CCM_SalesPriceExportBatch(mapProductId2Values, lstProduct, objAccount, parentPbMap, childPbMap), 100);
        System.debug('*** start jobId: ' + jobId);
        Log__c log = new Log__c();
        log.RecordId__c = jobId;
        log.Name = 'Export Sales Price';
        log.ResParam__c = 'In Progress';
        insert log;
        return jobId;
    }

    @AuraEnabled
    public static string getPriceListInfo2(String jobId){
        List<PriceProductInfo> lstPriceInfo = new List<PriceProductInfo>();
        List<Log__c> logs = [SELECT ResParam__c FROM Log__c WHERE Name = 'Sales Price Export Data' AND RecordId__c = :jobId];
        for(Log__c log : logs) {
            List<PriceProductInfo> objPriceInfo = (List<PriceProductInfo>) JSON.deserialize(log.ResParam__c, List<PriceProductInfo>.class);
            lstPriceInfo.addAll(objPriceInfo);
        }
        // to do - delete logs
        return JSON.serialize(lstPriceInfo);
    }
    
    //分页
    public static List<OrderInfo> getCurrentData(List<OrderInfo> allData, Decimal pageNumber, Integer pageSize){
        List<OrderInfo> currentData = new List<OrderInfo>();
        Integer min = ((Integer) pageNumber - 1) * pageSize;
        Integer max = (Integer) pageNumber * pageSize - 1;
        for (Integer i = min; i <= max; i++){
            if (i < allData.size()){
                currentData.add(allData.get(i));
            }
        }
        return currentData;
    }
    
    //删除行记录
    @AuraEnabled
    public static String deleteOrderInfo(String recordId){
        if (String.isNotBlank(recordId)){
            try{
                delete [SELECT Id
                        FROM Purchase_Order__c
                        WHERE Id = :recordId];
                return 'Success';
            } catch (Exception ex){
                System.debug(LoggingLevel.INFO, ' *  *  * ex.getStackTraceString() : ' + ex.getStackTraceString());
            }
        }
        return 'Fail';
    }
    
    @AuraEnabled
    public static String getPicklistOption(String objectAPI, String fieldAPI, String fifterString){
        List<SelectOption> selectList = new List<SelectOption>();
        List<SelectOptionItem> selectListReturn = new List<SelectOptionItem>();
        if (String.isNotBlank(fifterString)){
            List<String> exceptValueList = New List<String>();
            exceptValueList.add(fifterString);
            selectList = CCM_Untils.getPicklistOption(objectAPI, fieldAPI, exceptValueList);
        } else{
            selectList = CCM_Untils.getPicklistOption(objectAPI, fieldAPI);
            
        }
        for (SelectOption item : selectList){
            SelectOptionItem option = new SelectOptionItem();
            option.label = item.getLabel();
            option.value = item.getValue();
            selectListReturn.add(option);
        }
        return JSON.serialize(selectListReturn);
    }
    
    
    
    @AuraEnabled
    public static String lookUpSearch(String fifterString, String fieldAPI){
        String queryStr = '';
        if (fieldAPI == 'CreatedBy'|| fieldAPI == 'SalesPerson'){
            queryStr = 'Select Id,Name from User where  Name like \'%' + fifterString + '%\'';
        }
        if (fieldAPI == 'Customer' || fieldAPI =='Select Customer'){
            queryStr = 'Select Id, Name  from Account where RecordType.developerName in (\'Channel\',\'Association_Group\')' + 
                ' And Name like \'%' + fifterString + '%\'';
        }
        System.debug('queryStr:' + queryStr);
        List<SObject> queryList = Database.query(queryStr);
        system.debug('queryList-->'+queryList);
        if (queryList.isEmpty()){
            return null;
        } else{
            return JSON.serialize(queryList);
        }
    }
    
    @AuraEnabled
    public static String UpsertExportRecord(String exportJsonString){
        return CCM_SNRequestController.UpsertExportRecord(exportJsonString);
    }
    
    @AuraEnabled
    public static String getAllExportHistory(Integer pageNumber, Integer allPageSize, String orgType){
        return CCM_SNRequestController.getAllExportHistory(pageNumber, allPageSize, orgType);
    }
    
    @AuraEnabled
    public static String getSnTempByRequestId(String requestId){
        return CCM_SNRequestController.getSnTempByRequestId(requestId);
    }
    
    private class SelectOptionItem{
        public String label;
        
        public String value;
        
    }
    
    public class FilterWrapper{
        public String OrderNumber;
       
        
        public String OrderType;
        public String PurchaseOrderType;
        
        public String CreatedBy;
        
        public String Customer;
        
        public String CustomerPo;
        
        public String CustomerNumber;
        
        public String SalesPerson;
        
        public String PoNumber;
        
        public String OrderDateFrom;
        
        public String OrderDateTo;
        
        public String Model;
        
        public String PurchaseStatus;

        public String OrderStatus;
        
    }
     public class PriceProductInfo{
        @AuraEnabled public String country{get;set;}

        @AuraEnabled public String countryDescription{get;set;}
        @AuraEnabled public String salesChannel{get;set;}
        @AuraEnabled public String Classification1{get;set;}
        @AuraEnabled public String productName{get;set;}
        @AuraEnabled public String productClass{get;set;}
        @AuraEnabled public String PPCCode{get;set;}
        @AuraEnabled public String PPCDescription{get;set;}
        @AuraEnabled public String productSeries{get;set;}
        @AuraEnabled public String modiferType{get;set;}
        @AuraEnabled public String modifer{get;set;}
        @AuraEnabled public String orderType{get;set;}
        @AuraEnabled public String incoTerm{get;set;}
        @AuraEnabled public String productModel{get;set;}
        @AuraEnabled public String productDescription{get;set;}
        @AuraEnabled public String CustomerName{get;set;}
        @AuraEnabled public String CustomerNumber{get;set;}
        @AuraEnabled public Decimal ListPrice{get;set;}
        @AuraEnabled public Decimal SalesPrice{get;set;}
        @AuraEnabled public Date StartDate{get;set;}
        @AuraEnabled public Date EndDate{get;set;}
        @AuraEnabled public String CurrencyCode{get;set;}
        @AuraEnabled public String PriceName{get;set;}
    }
    
    public class OrderInfo{
        @AuraEnabled
        public String Id{ get; set; }
        
        @AuraEnabled
        public String Customer{ get; set; }
        
        @AuraEnabled
        public String CustomerNumber{ get; set; }
        
        @AuraEnabled
        public String CustomerPo{ get; set; }
        
        @AuraEnabled
        public String OrderNumber{ get; set; }
        
        @AuraEnabled
        public String PoNumber{ get; set; }

        @AuraEnabled
        public String currencyCode{ get; set; }
        
        @AuraEnabled
        public String PurchaseOrderType{ get; set; }
        
        @AuraEnabled
        public String PurchaseOrderStatus{ get; set; }
        
        @AuraEnabled
        public String TotalDueAmount{ get; set; }
        
        @AuraEnabled
        public String OrderCreatedDateInEBS{ get; set; }
        
        @AuraEnabled
        public String CreatedBy{ get; set; }
        
        @AuraEnabled
        public String editStyleCss{ get; set; }
        
        @AuraEnabled
        public String viewStyleCss{ get; set; }
        
        @AuraEnabled
        public String deleteStyleCss{ get; set; }

        @AuraEnabled
        public DateTime lastModifiedDate{ get; set; }

        @AuraEnabled
        public Boolean isSynced {get;set;}

        @AuraEnabled
        public String syncStatus{ get; set; }

        @AuraEnabled
        public String syncMessage{ get; set; }
        
        public OrderInfo(){
            this.editStyleCss = 'hidebtn';
            this.viewStyleCss = 'hidebtn';
            this.deleteStyleCss = 'hidebtn';
        }
        
    }
    
    public class InitData{
        public List<OrderInfo> currentData;
        
        public Integer totalRecords;
        
        public Boolean isSuccess;
        
        public String errorMessage;
        
        public InitData(){
            this.currentData = new List<OrderInfo>();
            this.totalRecords = 0;
            this.isSuccess = true;
            this.errorMessage = '';
        }
        
    }
    public CCM_PurchaseOrderListController() {
        
    }

    
    // add by haibo
    @AuraEnabled
    public static String GetUserType(){
        return CCM_PurchaseOrderDetailController.GetUserType();
    }
}