({
    doInit : function(component, event, helper) {
        component.set('v.columns', [
            { label: $A.get("$Label.c.CCM_CompanyName"), fieldName: 'companyName', initialWidth: 160, type: 'text' },
            { label: $A.get("$Label.c.CCM_Country"), fieldName: 'country', type: 'text' },
            { label: $A.get("$Label.c.CCM_PostalCode"), fieldName: 'postcode', type: 'text' },
            { label: $A.get("$Label.c.CCM_Status"), fieldName: 'status', type: 'text' },
            { label: $A.get("$Label.c.CCM_City"), fieldName: 'city', type: 'text' },
            { label: $A.get("$Label.c.CCM_Street"), fieldName: 'street', type: 'text' },
            { label: $A.get("$Label.c.CCM_Address"), fieldName: 'address', type: 'text' },
            // { label: 'Fleet Manager', fieldName: 'fleetManager', initialWidth: 160, type: 'text' },
            // { label: 'Action', type: 'button', initialWidth: 140, typeAttributes: { label: 'New User', name: 'New User', class: 'table-btn'}},
            { label: $A.get("$Label.c.CCM_Action"), type: 'button', initialWidth: 240, typeAttributes: { label: $A.get("$Label.c.CCM_ProductRegistration"), name: 'Product Registration', class: {fieldName: 'calssName'}, disabled: {fieldName: 'actionDisabled'}}},
        ]);
        component.set('v.tableData', []);
	},

    // Search Event
    handlerSearch : function(component, event, helper) {
        // 必填校验
        const isValid = helper.getValidation(component);
        let countryValid = false;
        const countryElement = component.find('country');
        const countryRequiredText = component.find('country-error-required');
        // country校验
        const countryValue = JSON.parse(JSON.stringify(component.get('v.country')));
        if (countryValue.Id) {
            countryValid = true;
            $A.util.removeClass(countryElement, 'field-error');
            $A.util.addClass(countryRequiredText, 'slds-hide');
        } else {
            countryValid = false;
            $A.util.addClass(countryElement, 'field-error');
            $A.util.removeClass(countryRequiredText, 'slds-hide');
        };
        if (isValid && countryValid) {
            helper.getSerachResult(component);
        }
    },

    // handleRowAction
    handleRowAction : function (cmp, event, helper) {
        var action = event.getParam('action');
        var row = event.getParam('row');
        console.log(JSON.parse(JSON.stringify(row)), 'rowData=========');
        switch (action.name) {
            case 'New User':
                window.location.href = '/s/productregistration?registrationType=residentialUser';
                break;
            case 'Product Registration':
                window.location.href = `/s/productregistration?registrationType=commercialUser&id=${row.id}&0.accountEmail=${row.accountEmail}`;
                break;
            default:
                break;
        }
    },

    // handlerRegistration
    handlerRegistration : function (cmp, event, helper) {
        window.location.href = '/s/user-registration';
    },
    // 取消按钮
    handlerCancel : function(component, event, helper) {
        console.log('onClick handlerCancel');
        // window.history.back();
        var specifiedUrl = "https://chervoneuro--uat.sandbox.my.site.com/s/servicehome"; // 指定页面的URL
        window.location.href = specifiedUrl;
    },
    // change Company
    changeCompany: function(component, event, helper) {
        // 校验必填
        helper.getElementRequiredError(component, 'company');
    },
    // change Postcode
    changePostcode: function(component, event, helper) {
        // 校验必填
        helper.getElementRequiredError(component, 'postcode');
    },
    // change Country
    changeCountry: function(component, event, helper) {
        let country = JSON.parse(JSON.stringify(component.get('v.country')));
        const countryElement = component.find('country');
        const countryRequiredText = component.find('country-error-required');
        if (country.Id) {
            $A.util.removeClass(countryElement, 'field-error');
            $A.util.addClass(countryRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(countryElement, 'field-error');
            $A.util.removeClass(countryRequiredText, 'slds-hide');
        }
    },
    // 重置事件
    handlerReset: function(component, event, helper) {
        component.set('v.companyName', null);
        component.set('v.postcode', null);
        component.set('v.country', {});
    },
})