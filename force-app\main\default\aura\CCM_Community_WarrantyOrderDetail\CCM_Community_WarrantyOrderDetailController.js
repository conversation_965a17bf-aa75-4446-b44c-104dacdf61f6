({
    doInit: function(component, event, helper){
        component.set('v.isDE', $A.get("$Label.c.CCM_Translate"));
        // 判断url参数
        const params = new URLSearchParams(window.location.search);
        const recordId =  params.get('0.recordId');
        const accId =  params.get('0.accId');
        component.set('v.recordId', recordId);
        component.set('v.accId', accId);
        // 进度条
        let processData = [
            {label: $A.get("$Label.c.Order_PendingReview"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.CCM_Approved"), icon: 'entitlement'},
            {label: $A.get("$Label.c.CCM_OrderProcessing"), icon: 'privately_shared'},
            {label: $A.get("$Label.c.CCM_ShipmentComplete"), icon:'success'}
        ];
        component.set('v.processData', processData);
        // attachment table
        component.set('v.attachmentColumns', [
            {   
                label: $A.get("$Label.c.CCM_Action"),
                width: '60px',
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${Id}",
                            variant:"bare",
                            iconName:"utility:preview",
                            alternativeText:"download",
                            onclick: component.getReference('c.doView')
                        }
                    },
                ]
            },
            {label: $A.get("$Label.c.CCM_AttachmentName"), fieldName: 'AttachmentName'},
            {label: $A.get("$Label.c.CCM_AttachmentType"), fieldName: 'AttachmentType'},
            {label: $A.get("$Label.c.CCM_Date"), fieldName:'AttachMentDate'},
        ]);
        // attachmentTypeOptions
        component.set('v.attachmentTypeOptions', [
            {
                label: 'Profoma Invoice',
                value: 'Profoma Invoice',
            },
            {
                label: 'Packing List',
                value: 'Packing List',
            },
            {
                label: 'Purchase Order',
                value: 'Purchase Order',
            },
            {
                label: 'Others',
                value: 'Others',
            },
        ]);
        // ship type
        component.set('v.shipTypeOpyions', [
            {
                label: 'Ship To',
                value: 'Ship To',
            },
            {
                label: 'Other',
                value: 'Other',
            },
        ]);
        // 获取product list
        helper.getProductList(component);
        // 判断portal或CRM
        helper.getEnvironmentType(component);
        // 获取detail数据
        helper.getClaimDetail(component);
    },
    doBack: function(component) {
        let isPortal = component.get('v.isPortal');
        if (isPortal) {
            let url = window.location.origin + '/s/servicehome';
            window.open(url, '_self');
        } else {
            let url = window.location.origin + '/lightning/n/Service_List';
            window.open(url, '_self');
        }
        
    },
    doView: function(component, event, helper){
        var Id = event.getSource().get('v.value');
        let isDE = component.get('v.isDE');
        let url = '';
        if (isDE == 'DE') {
            url = '/apex/WarrantyOrderPDFViewDE?recordId=' + Id;
        } else {
            url = '/apex/WarrantyOrderPDFView?recordId=' + Id;
        }
        window.open(url);
    },
    // 从列表上删除附件
    doDelete : function(component, event, helper) {
        let contentId = event.getSource().get('v.value');
        let attachment = component.get('v.attachment');
        console.log(contentId, 'contentId----------');
        attachment = attachment.filter((item)=>{
            return item.contentId !== contentId;
        });
        component.set('v.attachment', attachment);
    },
    showToolList: function(component, event, helper){
        var id = event.currentTarget.getAttribute('id');
        var expanded = event.currentTarget.getAttribute('data-expanded');
        if(expanded == 'true'){
            document.getElementById('tool' + id).style.display = 'none'
            event.currentTarget.setAttribute('data-expanded', false);
        }else{
            document.getElementById('tool' + id).style.display = 'table-row';
            event.currentTarget.setAttribute('data-expanded', true);
        }
    },
    // 同步操作
    doSync: function(component, event, helper){
        helper.syncEvent(component);
    },
    // inside 用户编辑操作
    doEdit: function(component, event, helper){
        console.log('doEdit----------');
        let data = JSON.parse(JSON.stringify(component.get('v.basicInformation')));
        let orderItemList = JSON.parse(JSON.stringify(component.get('v.orderItemList')));
        console.log(JSON.stringify(data), 'data-----------------');
        // // eidt baseinfo
        let editInfo = {
            billToAddress: {
                Name: data.BillToAddress.CompanyName,
                Id: data.BillToAddress.Id,
                address: {
                    CompanyName: data.BillToAddress.CompanyName,
                    Country: data.BillToAddress.Country,
                    City: data.BillToAddress.City,
                    Street: data.BillToAddress.Street,
                }
            },
            shipToAddress: {
                Name: data.ShipToAddress.CompanyName,
                Id: data.ShipToAddress.Id,
                address: {
                    CompanyName: data.ShipToAddress.CompanyName,
                    Country: data.ShipToAddress.Country,
                    City: data.ShipToAddress.City,
                    Street: data.ShipToAddress.Street,
                }
            },
            shipType: data.shipType,
            country: data.country,
            province: data.province,
            address: data.address,
            city: data.city,
            postalCode: data.postalCode,
            orderItemList: JSON.parse(JSON.stringify(orderItemList))
        };
        component.set('v.editInfo', editInfo);
        component.set('v.isEdit', true);
    },
    doSaveEdit: function(component, event, helper){
        let basicInformation = JSON.parse(JSON.stringify(component.get('v.basicInformation')));
        let editInfo = JSON.parse(JSON.stringify(component.get('v.editInfo')));
        console.log(JSON.stringify(editInfo), 'editInfo----------');
        basicInformation.ShipToAddress = {
            CompanyName: editInfo.shipToAddress.address.CompanyName,
            Id: editInfo.shipToAddress.Id,
            Country: editInfo.shipToAddress.address.Country,
            City: editInfo.shipToAddress.address.City,
            Street: editInfo.shipToAddress.address.Street,
        };
        basicInformation.BillToAddress = {
            CompanyName: editInfo.billToAddress.address.CompanyName,
            Id: editInfo.billToAddress.Id,
            Country: editInfo.billToAddress.address.Country,
            City: editInfo.billToAddress.address.City,
            Street: editInfo.billToAddress.address.Street,
        };
        basicInformation.orderItemList = editInfo.orderItemList;
        basicInformation.shipType = editInfo.shipType;
        basicInformation.country = editInfo.country;
        basicInformation.province = editInfo.province;
        basicInformation.address = editInfo.address;
        basicInformation.city = editInfo.city;
        basicInformation.postalCode = editInfo.postalCode;

        console.log(JSON.stringify(basicInformation), 'basicInformation------------');

        helper.saveEditInfo(component, basicInformation, basicInformation.orderItemList);
    },
    doCancelEdit: function(component, event, helper){
        component.set('v.isEdit', false);
    },
    // 打开附件弹框
    uploadFileItem: function(component, event, helper){
        console.log('打开附件弹框-----------');
        component.set('v.uploadModalFlag', true);
    },
    // 关闭附件弹框
    cancelEvent: function(component, event, helper){
        component.set('v.uploadModalFlag', false);
        component.set('v.uploadFinished', false);
        component.set('v.attachmentName', '');
        component.set('v.attachmentType', '');
    },
    // 上传附件
    handleFilesChange : function(component, event, helper) {
        component.set('v.isBusy', true);
        var files = event.getSource().get("v.files");
        let uploadItem = component.get('v.attachmentItem');
        let attachmentName = component.get('v.attachmentName');
        console.log(files, 'file=======');
        // 显示附件信息
        if (files.length > 0) {
            component.set('v.uploadFinished', true);
        }
        // 附件赋值
        if (!attachmentName) {
            component.set('v.attachmentName', files[0].name);
        }
        uploadItem.name = files[0].name;
        component.set('v.attachmentItem', uploadItem);
        // 转base64
        helper.fileByBase64(files, component, attachmentName || files[0].name );
    },
    // 删除附件
    deleteAttachmentItem : function(component, event, helper) {
        component.set('v.uploadFinished', false);
        component.set('v.attachmentItem', {});
    },
    // 保存当前附件
    saveFileItem : function(component, event, helper) {
        let attachment = component.get('v.attachment');
        let attachmentItem = component.get('v.attachmentItem');
        let attachmentName = component.get('v.attachmentName');
        let attachmentType = component.get('v.attachmentType');
        console.log(JSON.parse(JSON.stringify(attachmentItem)), 'attachmentItem-------------');
        // 获取当前时间
        const year = new Date().getFullYear().toString();
        const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
        const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
        attachment.push({
            attachmentName: attachmentName,
            attachmentType: attachmentType,
            attachmentDate: `${year}-${month}-${day}`,
            contentId: attachmentItem.contentId,
        });
        // 关闭弹框
        component.set('v.attachment', attachment);
        component.set('v.uploadModalFlag', false);
        component.set('v.uploadFinished', false);
        component.set('v.attachmentName', '');
        component.set('v.attachmentType', '');
    },
    // 保存附件
    saveFileLIst : function(component, event, helper) {
        helper.uplaodFileEvent(component);
    },
    // 
    toAfterSync : function(component, event, helper) {
        let afterSyncId = component.get('v.afterSyncId');
        let url = window.location.origin + '/lightning/n/Re_Sales_Sync_Detail?0.recordId=' + afterSyncId;
        window.open(url, '_self');
    },
    // 切换产品
    selectProduct : function(component, event, helper) {
        let index = event.getSource().get("v.id");
        let selectValue = event.getParam('data');
        let productOptions = component.get('v.productOptions');
        let editInfo = component.get('v.editInfo');
        let selectRow = {};
        if (selectValue) {
            productOptions.forEach((productItem)=>{
                if (productItem.productId == selectValue) {
                    selectRow = productItem;
                }
            })
            console.log(JSON.stringify(selectRow), 'selectRow------------');
            editInfo.orderItemList.forEach((editItem, editIndex)=>{
                if (editIndex == index) {
                    editItem.ProductDescription = selectRow.productDescription;
                    editItem.Model = selectRow.orderModel;
                    editItem.Inventory = selectRow.inventoryInfo.CurrentStatus == 'green Light' ? 'Green' : (selectRow.inventoryInfo.CurrentStatus == 'yellow Light' ? 'Yellow' : 'Red');
                }
            })
            component.set('v.editInfo', JSON.parse(JSON.stringify(editInfo))); 
            console.log(JSON.stringify(component.get('v.editInfo')), 'editInfo-------------');
        }
    },
    // 修改bill to
    changeBillToAddress: function(component, event, helper){
        let billToAddress = JSON.parse(JSON.stringify(component.get('v.editInfo.billToAddress')));
        console.log(JSON.stringify(billToAddress), 'billToAddress========');
        const billToAddressElement = component.find('billToAddress');
        const billToAddressRequiredText = component.find('billToAddress-error-required');
        if (billToAddress.Id) {
            $A.util.removeClass(billToAddressElement, 'field-error');
            $A.util.addClass(billToAddressRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(billToAddressElement, 'field-error');
            $A.util.removeClass(billToAddressRequiredText, 'slds-hide');
        }
    },
    // 修改ship to
    changeShipToAddress: function(component, event, helper){
        let shipToAddress = JSON.parse(JSON.stringify(component.get('v.editInfo.shipToAddress')));
        console.log(JSON.stringify(shipToAddress), 'shipToAddress========');
        const shipToAddressElement = component.find('shipToAddress');
        const shipToAddressRequiredText = component.find('shipToAddress-error-required');
        if (shipToAddress.Id) {
            $A.util.removeClass(shipToAddressElement, 'field-error');
            $A.util.addClass(shipToAddressRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(shipToAddressElement, 'field-error');
            $A.util.removeClass(shipToAddressRequiredText, 'slds-hide');
        }
    },
})