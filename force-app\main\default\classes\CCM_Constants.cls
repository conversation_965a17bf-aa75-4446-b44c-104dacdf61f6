public class CCM_Constants{
    public static final String CUSTOMER_RECORD_TYPE_STORE_LOCATION_NAME = 'Store_Location';
    // Product2
    public static final String PRODUCT_PRODUCT_RECORD_TYPE_DEVELOPER_NAME = 'TLS_Product';
    public static final String PRODUCT_PARTS_RECORD_TYPE_DEVELOPER_NAME = 'SP';
    public static final String PRODUCT_KIT_RECORD_TYPE_DEVELOPER_NAME = 'TLS_KIT';
    // Kit Item
    public static final String KIT_ITEM_KITS_AND_PRODUCTS_RECORD_TYPE_DEVELOPERNAME = 'Kits_and_Products';
    public static final String KIT_ITEM_PRODUCTS_AND_DIAGRAM_RECORD_TYPE_DEVELOPERNAME = 'Products_and_Diagram';
    public static final String KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_DEVELOPERNAME = 'Products_and_Parts';
    //Customer
    public static final String CHANNEL = 'Channel';//recordtype Name
    public static final String ASSOCIATION_GROP = 'Association_Group';//recordtype Name
    //Order Status__c
    public static final String ORDER_PROCESS = 'Order Processing';
    public static final String PARTIAL_SHIPMENT = 'Partial Shipment';
    public static final String SHIPMENT_COMPLETE = 'Shipment Complete';
    public static final String CANCELLED = 'Cancelled';
    //Order Status
    public static final String DRAFT = 'draft';
    //Order Status In EBS
    public static final String BOOKED = 'BOOKED';
    public static final String CLOSED = 'CLOSED';
    public static final String CANCELLED_EBS = 'CANCELLED';
    //Invoice RecordType
    public static final String CREDIT = 'Credit';
    public static final String INVOICE = 'Invoice';
    //Product Model
    public static final String INSURANCE_FEE = 'Insurance';
    public static final String OTHER_FEE = 'OTHERFEE';
    public static final String FREIGHT_FEE = 'Fracht/ Freight';
    public static final String CHARGE_FORWEAR_FEE = 'WEARCHARGE';
    public static final String LABOR = 'Labor';
    //Order Line Status
    public static final String AWAIT_SHIPPING = 'AWAITING_SHIPPING';
    public static final String RELEASE_TO_WAREHOUSE = 'RELEASE_TO_ WAREHOUSE';
    public static final String PICKED_PACKED = 'PICKED_PACKED';
    public static final String SHIPPED = 'SHIPPED';
    public static final String INVOICED = 'INVOICED';
    //Product Status
    public static final String PRODUCT_STUATUS_INFO = 'INFO';
    public static final String PRODUCT_STUATUS_INACT = 'INACT';
    //Sales Type in Oracle
    public static final String EEG_SALES_DOMESTIC = 'EEG Sales Domestic';
    public static final String EEG_PRESEASON_DOMESTIC = 'EEG Pre Season Domestic';
    public static final String EEG_DROPSHIPMENT_DOMESTIC = 'EEG DropShipment Domestic';
    //record Type in SF
    public static final String REGULAR_ORDER = 'Regular_Order';
    public static final String PRESEASON_ORDER = 'Pre-season Order';
    public static final String EEG_Influencer_EU_ORDER = 'EEG_Influencer_EU';
    public static final String PRESEASON_ORDER_RETURN_ORDER = 'PreSeason_Order';
    public static final String PRE_SEASON_ORDER_DEV_NAME = 'Pre_season_Order';
    //Sales Type in SF
    // inside de
    public static final String DSVR = 'DSVR';
    public static final String DIR = 'DIR';
    public static final String DSVP = 'DSVP';
    public static final String DIP = 'DIP';
    // outside de
    // inside eu, sales, dsv
    public static final String EEG_SALES_EU = 'EEG Sales EU';
    // outside eu, sales, dsv
    public static final String EEG_SALES_OUTSIDE_EU = 'EEG Sales Outside EU';
    // inside eu, pre season, dsv
    public static final String EEG_PRE_SEASON_EU = 'EEG Pre Season EU';
    // outside eu, pre season, dsv
    public static final String EEG_PRE_SEASON_OUTSIDE_EU = 'EEG Pre Season Outside EU';
    // inside eu, pre season, di
    public static final String EEG_DROPSHIPMENT_EU = 'EEG DropShipment EU';
    // outside eu, pre season, di
    public static final String EEG_DROPSHIPMENT_OUTSIDE_EU = 'EEG DropShipment Outside EU';
    //Sales WareHouse in SF
    public static final String GERMANY_DSV = 'Germany (DSV)';
    public static final String CHINA_DI = 'China (DI)';
    //SN Response Status
    public static final String SN_EXPORT_REP_MSG_NO_RESULT = 'no results';
    public static final String SN_EXPORT_REP_MSG_INCORRECT = 'incorrect';
    //Authorized Brand RecordType DeveloperName
    public static final String AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SASC = 'Sales_and_Service_Customized';
    public static final String AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SASS = 'Sales_and_Service_Standard';
    public static final String AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SALES_CUSTOMIZED = 'Sales_Customized';
    public static final String AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SALES_STANDARD = 'Sales_Standard';
    public static final String AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SERVICE_CUSTOMIZED = 'Service_Customized';
    public static final String AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SERVICE_STANDARD = 'Service_Standard';
    public static final Id AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SASC_Id = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SASC).getRecordTypeId();
    public static final Id AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SALES_CUSTOMIZED_Id = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SALES_CUSTOMIZED).getRecordTypeId();
    public static final Id AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SERVICE_CUSTOMIZED_Id = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SERVICE_CUSTOMIZED).getRecordTypeId();
    // UAT MERGE 23-7-15
    public static final String BILLING_ADDRESS = 'Billing Address';//recordtype Name
    public static final String SHIPPING_ADDRESS = 'Shipping Address';//recordtype Name
    public static final String DROPSHIP_ADDRESS = 'Dropship Address';
    public static final String DROPSHIP_SHIPPING_ADDRESS = 'Dropship Shipping Address';
    public static final String DROPSHIP_BILLING_ADDRESS = 'Dropship Billing Address';
    public static final String BILLING_ADDRESS_API = 'Billing_Address';//recordtype Name
    public static final String SHIPPING_ADDRESS_API = 'Shipping_Address';//recordtype Name
    public static final String DROPSHIP_SHIPPING_ADDRESS_API = 'Dropship_Shipping_Address';
    public static final String DROPSHIP_BILLING_ADDRESS_API = 'Dropship_Billing_Address';
    public static final String BILLING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.BILLING_ADDRESS_API).getRecordTypeId();
    public static final String SHIPPING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SHIPPING_ADDRESS_API).getRecordTypeId();
    public static final String DROPSHIP_BILLING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.DROPSHIP_BILLING_ADDRESS_API).getRecordTypeId();
    public static final String DROPSHIP_SHIPPING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.DROPSHIP_SHIPPING_ADDRESS_API).getRecordTypeId();
    public static final String CHANNEL_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CCM_Constants.CHANNEL) == null ? '' : Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CCM_Constants.CHANNEL).getRecordTypeId();
    public static final String PERSONACCOUNT = 'Residential_Consumer';//recordtype Name
    public static final String PERSONACCOUNT_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PERSONACCOUNT).getRecordTypeId();
    public static final String CommercialConsumerName = 'Commercial_Consumer';//recordtype Name
    public static final String CommercialConsumer_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CCM_Constants.CommercialConsumerName).getRecordTypeId();
    public static final String ASSOCIATIONGROP = 'Association_Group';//recordtype Name
    public static final String ASSOCIATIONGROP_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CCM_Constants.ASSOCIATIONGROP).getRecordTypeId();
    public static final String STORELOCATION = 'Store_Location';//recordtype Name
    public static final String STORELOCATION_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CCM_Constants.STORELOCATION).getRecordTypeId();
    public static final String PRODUCT = 'TLS_Product';//recordtype Name
    public static final String PARTS = 'SP';//recordtype Name
    public static final String PRODUCT_RECORDTYPEID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PRODUCT).getRecordTypeId();
    public static final String PRODUCT_RECORDTYPEID_C = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
    public static final String PARTS_RECORDTYPEID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PARTS).getRecordTypeId();
    public static final String KITS_AND_PRODUCTS = 'Kits and Products';//recordtype Names
    public static final String PRODUCTS_AND_PARTS = 'Products and Parts';//recordtype Name
    public static final String PRODUCTS_AND_DIAGRAM = 'Products and Diagram';//recordtype Name
    public static final String KITS_AND_PRODUCTS_API = 'Kits_and_Products';//recordtype api
    public static final String PRODUCTS_AND_PARTS_API = 'Products_and_Parts';//recordtype api
    public static final String PRODUCTS_AND_DIAGRAM_API = 'Products_and_Diagram';//recordtype api
    public static final String KITS_AND_PRODUCTS_RECORDTYPEID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.KITS_AND_PRODUCTS_API).getRecordTypeId();
    public static final String PRODUCTS_AND_PARTS_RECORDTYPEID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PRODUCTS_AND_PARTS_API).getRecordTypeId();
    public static final String PRODUCTS_AND_DIAGRAM_RECORDTYPEID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PRODUCTS_AND_DIAGRAM_API).getRecordTypeId();
    public static final String PLACE_ORDER = 'Regular_Order';//recordtype Name
    public static final String PLACE_PARTS_ORDER = 'PreSeason_Order';//recordtype Name
    public static final String PLACE_PURCHASE_ORDER = 'Place_Parts_Order';//recordtype Name
    public static final String PLACE_PURCHASE_ORDER_RECORDTYPEID = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PLACE_PURCHASE_ORDER) == null ? '' : Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PLACE_PURCHASE_ORDER).getRecordTypeId();
    public static final String SERVICE_PROGRAM = 'Sales Standard';//recordtype Name
    public static final String SERVICE_PROGRAM_API = 'Sales_Standard';//recordtype Name
    public static final String SERVICE_PROGRAM_RECORDTYPEID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SERVICE_PROGRAM_API) == null ? '' : Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SERVICE_PROGRAM_API).getRecordTypeId();
    public static final String SALES_STANDARD_PROGRAM = 'Service Standard';//recordtype Name
    public static final String SALES_STANDARD_PROGRAM_API = 'Service_Standard';//recordtype Name
    public static final String SALES_STANDARD_PROGRAM_RECORDTYPEID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SALES_STANDARD_PROGRAM_API) == null ? '' : Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SALES_STANDARD_PROGRAM_API).getRecordTypeId();
    public static final String SALES_CUSTOMIZED_PROGRAM = 'Sales Customized';//recordtype Name
    public static final String SALES_CUSTOMIZED_PROGRAM_API = 'Sales_Customized';//recordtype Name
    public static final String SALES_CUSTOMIZED_PROGRAM_RECORDTYPEID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SALES_CUSTOMIZED_PROGRAM_API) == null ? '' : Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SALES_CUSTOMIZED_PROGRAM_API).getRecordTypeId();
    public static final String SERVICE_CUSTOMIZED_PROGRAM = 'Service Customized';//recordtype Name
    public static final String SERVICE_CUSTOMIZED_PROGRAM_API = 'Service_Customized';//recordtype Name
    public static final String SERVICE_CUSTOMIZED_PROGRAM_RECORDTYPEID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SERVICE_CUSTOMIZED_PROGRAM_API) == null ? '' : Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.SALES_CUSTOMIZED_PROGRAM_API).getRecordTypeId();
    public static final String SN_QUERY_SN = 'SN';
    public static final String SN_QUERY_ORDER = 'Order';
    public static final String SN_QUERY_SN_SECTION = 'SN Section';
    public static final String SN_QUERY_SHIPPING_ORDER = 'Shipping Order';
    public static final String PROMOTION_SPECIAL_CUSTOMER = 'Specified Customer';//recordtype Name
    public static final String PROMOTION_SPECIAL_CUSTOMER_API = 'Special_Customer_Promotion';//recordtype Name
    public static final String PROMOTION_SPECIAL_CUSTOMER_RECORDTYPEID = Schema.SObjectType.Promotion_Target_Group__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PROMOTION_SPECIAL_CUSTOMER_API).getRecordTypeId();
    public static final Decimal SALES_POTENTIAL1 = 250000;
    public static final Decimal SALES_POTENTIAL2 = 500000;
    public static final Decimal SALES_POTENTIAL3 = 1000000;
    public static final Decimal SALES_POTENTIAL4 = 5000000;
    public static final Decimal SALES_POTENTIALCCA1 = 5000;
    public static final Decimal SALES_POTENTIALCCA2 = 50000;
    public static final Decimal SALES_POTENTIALCCA3 = 250000;
    public static final Decimal SALES_POTENTIALCCA4 = 750000;
    public static final Decimal SALES_POTENTIAL_POINT1 = 10;
    public static final Decimal SALES_POTENTIAL_POINT2 = 25;
    public static final Decimal SALES_POTENTIAL_POINT3 = 50;
    public static final Decimal SALES_POTENTIAL_POINT4 = 75;
    public static final Decimal SALES_POTENTIAL_POINT5 = 100;
    public static final Decimal SALES_POTENTIAL_POINT_WEIGHT = 0.35;
    public static final Decimal STORE_SIZE_WEIGHT = 0.2;
    public static final Decimal NUMBER_OF_OUTSIDE_SALES_WEIGHT = 0.25;
    public static final Decimal WILLINGNESS_TO_WORK_WITH_CHERVON_WEIGHT = 0.2;
    public static final Decimal PNTENTIAL_INPUT_POINT1 = 13;
    public static final Decimal PNTENTIAL_INPUT_POINT2 = 35;
    public static final Decimal PNTENTIAL_INPUT_POINT3 = 56;
    public static final Decimal PNTENTIAL_INPUT_POINT4 = 78;
    public static final String PRICEBOOK_PRODUCT_CONTEXT_MODEL = 'Model';
    public static final String PRICEBOOK_ORACLE_PRODUCT_CONTEXT_ITEM = 'Item';
    //AutSales_Program__c
    public static final String SALES_STANDARD = 'Sales_Standard';
    public static final String SERVICE_STANDARD = 'Service_Standard';
    public static final String SALES_ORDER_DSV = 'Sales Order - DSV';
    public static final String SALES_ORDER_DI = 'Sales Order - DI';
    public static final String EEG01 = 'EEG01';
    public static final String DAP = 'DAP';
    public static final String EEG03 = 'EEG03';
    public static final String DAP_FREE_PORT = 'DAP Free Port';
    public static final String ASSOCIATIONGROPTYPE1 = 'Bill to Association Group + AG’s Payment Term';
    public static final String ASSOCIATIONGROPTYPE2 = 'Bill to Association Members + AG’s Payment Term';
    public static final String ASSOCIATIONGROPTYPE3 = 'Bill to Association Members + Own Payment Term';
    //Contact
    public static final String CONTACT_STORE_NAME = 'Store Contact';
    public static final String CONTACT_STORE_NAME_API = 'Store_Contact';
    public static final String CONTACT_STORE_NAME_RECORDTYPEID = Schema.SObjectType.Contact.getRecordTypeInfosByDeveloperName().get(CCM_Constants.CONTACT_STORE_NAME_API) == null ? '' : Schema.SObjectType.Contact.getRecordTypeInfosByDeveloperName().get(CCM_Constants.CONTACT_STORE_NAME_API).getRecordTypeId();
    // UAT Merge end 23-7-15
    // DEV 2 常量类 23-7-15
    @TestVisible
    private CCM_Constants(){
        System.debug(LoggingLevel.INFO, 'This is the constructor to stop being instantiated.');
    }
    // Common
    public static final String DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS = 'yyyy-MM-dd HH:mm:ss.SSS';
    public static final String SEMICOLON_REGEXP_SPACE_IGNORED = '\\s*;\\s*';
    public static final String CARRIAGE_RETURN = '\r';
    public static final String EMPTY = '';
    public static final String TAB = '\t';
    public static final String NEXT_LINE = '\n';
    public static final String COLON = ':';
    public static final String COMMA = ',';
    public static final String DOT = '.';
    public static final String ORG_CODE_CNA = 'CNA';
    public static final String ORG_CODE_CCA = 'CCA';
    public static final Set<String> CANADA_USER_ROLE_DEVELOPER_NAME = new Set<String>{ 'CA_General_Manager', 'CA_Inside_Sales', 'CA_TT_Sales_Manager', 'Nanjing_CA_BEAM_Manager', 'Nanjing_CA_BEAM', 'CA_Sales_Rep_1', 'CA_Sales_Rep_2', 'CA_Sales_Rep_3', 'CA_Sales_Rep_4', 'CA_Sales_Rep_5' };
    public static final Set<String> CANADA_PAYMENT_TERM_PICKLIST_VALUE = new Set<String>{ 'NA999', 'CA001', 'CA003', 'CA005', 'CA006', 'CA014', 'CA017', 'CA019', 'CA020', 'CA055', 'CA064', '30 NET', 'NA051', 'NA085', 'NA086', 'NA087', 'NA088', 'NA089', 'NA090', 'NA072', 'NA081' };
    public static final Set<String> US_PAYMENT_TERM_PICKLIST_VALUE = new Set<String>{ 'NA001', 'NA002', 'NA007', 'NA009', 'NA010', 'NA012', 'NA015', 'NA016', 'NA017', 'NA019', 'NA022', 'NA027', 'NA028', 'NA031', 'NA032', 'NA035', 'NA036', 'NA037', 'NA038', 'NA039', 'NA040', 'NA041', 'NA042', 'NA043', 'NA052', 'NA055', 'NA057', 'NA059', 'NA060', 'NA061', 'NA0010', 'NA101', 'NA102', 'NA103', 'NA104', 'NA105', 'NA106', 'NA107', 'NA108', 'NA999', 'Z074', 'NA066', 'NA067', 'NA068', 'NA122', 'NA071', 'NA072', 'NA073', 'NA074', 'NA058', 'NA021', 'NA076', 'NA077', 'NA078', 'NA085', 'NA086' };
    // Territory User Relationship
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_GENERAL_MANAGER = 'CA General Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_BEAM_MANAGER = 'CA BEAM Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER = 'CA TT Sales Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER_Developer_NAME = 'CA_TT_Sales_Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_ACCOUNT_MANAGER = 'CA Account Manager (for Sales Rep)';
    public static final Set<String> TERRITORY_USER_RELATIONSHIP_ROLE_SET_CANADA = new Set<String>{ TERRITORY_USER_RELATIONSHIP_ROLE_CA_GENERAL_MANAGER, TERRITORY_USER_RELATIONSHIP_ROLE_CA_BEAM_MANAGER, TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER, TERRITORY_USER_RELATIONSHIP_ROLE_CA_ACCOUNT_MANAGER };
    // Profile
    public static final String PROFILE_NAME_PARTNER_COMMUNITY_SERVICE = 'Partner Community Service';
    // Customer
    // public static final String CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_DEVELOPER_NAME = 'PersonAccount';
    public static final String CUSTOMER_RECORD_TYPE_CHANNEL_DEVELOPER_NAME = 'Channel';
    public static final Id CUSTOMER_RECORD_TYPE_STORE_LOCATION_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_RECORD_TYPE_STORE_LOCATION_NAME).getRecordTypeId();
    // public static final Id CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id CUSTOMER_RECORD_TYPE_CHANNEL_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_RECORD_TYPE_CHANNEL_DEVELOPER_NAME).getRecordTypeId();
    public static final String CUSTOMER_BUSINESS_TYPE_SALES = 'Sales';
    public static final String CUSTOMER_BUSINESS_TYPE_SERVICE = 'Service';
    public static final String CUSTOMER_BUSINESS_TYPE_SALES_AND_SERVICE = 'Sales & Service';
    // Account Team
    public static final String ACCOUNT_TEAM_ROLE_SALES_OPPORTUNITY = 'Sales Opportunity';
    public static final String ACCOUNT_TEAM_ROLE_SERVICE_OPPORTUNITY = 'Service Opportunity';
    // Customer Profile
    public static final String CUSTOMER_PROFILE_RECORD_TYPE_CUSTOMER_DEVELOPER_NAME = 'Customer_Profile';
    public static final Id CUSTOMER_PROFILE_RECORD_TYPE_CUSTOMER_ID = Schema.SObjectType.Customer_Profile__c.getRecordTypeInfosByDeveloperName().get(CUSTOMER_PROFILE_RECORD_TYPE_CUSTOMER_DEVELOPER_NAME).getRecordTypeId();
    // Product2
    public static final Id PRODUCT_PRODUCT_RECORD_TYPE_ID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(PRODUCT_PRODUCT_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id PRODUCT_PARTS_RECORD_TYPE_ID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(PRODUCT_PARTS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final String PRODUCT_EBS_STATUS_DISCONTINUED = 'DISC';
    public static final String PRODUCT_EBS_STATUS_INACTIVE = 'Inactive';
    public static final String PRODUCT_EBS_STATUS_ACTIVE = 'Active';
    public static final String PRODUCT_EBS_STATUS_WIP = 'WIP';
    public static final String PRODUCT_EBS_STATUS_RTO = 'RTO';
    public static final String PRODUCT_SOURCE_PIM = 'PIM';
    public static final String PRODUCT_SOURCE_EBS = 'EBS';
    public static final String PRODUCT_CATEGORY_3_PROFESSIONAL = 'Professional';
    public static final String PRODUCT_CATEGORY_3_PROFESSIONAL_X = 'Professional-X';
    public static final String PRODUCT_PRODUCT_TYPE_PRODUCT = 'Product';
    public static final String PRODUCT_PRODUCT_TYPE_CHARGER = 'Charger';
    public static final String PRODUCT_PRODUCT_TYPE_BATTERY = 'Battery';
    // Warranty
    public static final String WARRANTY_PRODUCT_USE_TYPE_2_INDUSTRIAL_PROFESSIONAL_COMMERCIAL = 'Industrial/Professional/Commercial';
    public static final String WARRANTY_PRODUCT_USE_TYPE_2_RESIDENTIAL = 'Residential';
    public static final String WARRANTY_PURCHASE_PLACE_PICKLIST_UNAUTHORIZED_DEALER = 'Unauthorized Dealer';
    // Warranty Rule
    public static final String WARRANTY_RULE_RECORD_TYPE_DEVELOPER_NAME = 'Model_Warranty_Term';
    public static final Id WARRANTY_RULE_RECORD_TYPE_ID = Schema.SObjectType.Warranty_Rules__c.getRecordTypeInfosByDeveloperName().get(WARRANTY_RULE_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final String WARRANTY_RULE_PURCHASE_PLACE_UNAUTHORIZED_DEALER = 'Unauthorized Dealer';
    // Account Address
    public static final String ADDRESS_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Billing_Address';
    public static final String ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Dropship_Billing_Address';
    public static final String ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Dropship_Shipping_Address';
    public static final String ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Shipping_Address';
    public static final String ADDRESS_APPROVAL_STATUS_PENDING_FOR_APPROVAL = 'Pending for Approval';
    public static final Id ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    // Authorized Brand
    public static final String SALES_PROGRAM_APPROVAL_STATUS_APPROVED = 'Approved';
    public static final String SALES_PROGRAM_APPROVAL_STATUS_PENDING_FOR_APPROVAL = 'Pending for Approval';
    public static final String SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME = 'Service_Customized';
    public static final String SALES_PROGRAM_RECORD_TYPE_SERVICE_DEVELOPER_NAME = 'Sales_Standard';
    public static final String SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME = 'Service_Customized';
    public static final String SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME = 'Service_Standard';
    public static final Id SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_SERVICE_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_SERVICE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME).getRecordTypeId();
    public static final Set<Id> SALES_PROGRAM_RECORD_TYPE_SALES_IDS = new Set<Id>{ SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID };
    public static final Set<Id> SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS = new Set<Id>{ SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID, SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID };
    public static final List<String> SALES_PROGRAM_SALES_GROUP = new List<String>{ 'SG01', 'SG02', 'SG03', 'SG04', 'SG05', 'SG06', 'SG07', 'SG08', 'SG09', 'SG10', 'SG21', 'SG22', 'SG23,', 'SG24', 'SG25', 'SG26', 'SG27', 'SG28', 'SG30', 'SG31', 'SG32', 'SG29',        // us
    'SG15', 'SG40', 'SG36', 'SG41', 'SG37', 'SG39', 'SG38', 'SG13' // canada
     };
    public static final List<String> SALES_PROGRAM_PAYMENT_TERM = new List<String>{ 'NA001', 'NA002', 'NA007', 'NA009', 'NA010', 'NA012', 'NA015', 'NA016', 'NA017', 'NA019', 'NA022', 'NA027', 'NA028', 'NA031', 'NA032', 'NA035', 'NA036', 'NA037', 'NA038', 'NA039', 'NA040', 'NA041', 'NA042', 'NA043', 'NA052', 'NA055', 'NA057', 'NA059', 'NA060', 'NA061', 'NA0010', 'NA101', 'NA102', 'NA103', 'NA104', 'NA105', 'NA106', 'NA107', 'NA108', 'NA999', 'Z074', 'NA066', 'NA067', 'NA068', 'NA122', 'NA071', 'NA072', 'NA073', 'NA074', 'NA058', 'NA021', 'NA076', 'NA077', 'NA051', 'NA078', 'CA001', 'CA003', 'CA005', 'CA006', 'CA014', 'CA017', 'CA019', 'CA020', 'CA055', 'CA064', '30 NET' // Canada
     };
    public static final List<String> SALES_PROGRAM_FREIGHT_TERM = new List<String>{ 'COLLECT', 'PICK', 'FOB', 'Paid', 'PPF', 'PPE', 'PPB', 'PPC', 'PPD', 'Due', 'PPA', 'DUECOST', 'THIRD_PARTY', 'TBD', 'PPG', 'PPH', 'PPJ', 'PPK', 'PPL', 'PPM', 'Prepaid @CAD150', 'Prepaid @CAD2000', 'CAD750' };
    public static final String SALES_PROGRAM_ORDER_TYPE_CA = 'CA Sales Order - CAD';
    public static final String SALES_PROGRAM_ORDER_TYPE_FOR_SERVICE = 'CNA Dropship Order';
    // Prospect Assignment Rule
    public static final String PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR = 'Canada Distributor';
    public static final String PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_NA_DISTRIBUTOR = 'Distributor';
    public static final String PROSPECT_ASSIGNMENT_RULE_CLUSTER_CANADA_HOME_CENTER = 'CA-CG01';
    public static final String PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA = 'KA019';
    public static final String PROSPECT_ASSIGNMENT_RULE_CLUSTER_FOR_NA_CNA_CG01 = 'CNA-CG01';
    public static final String PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_NA_KA001 = 'KA001';
    // Sales Target
    public static final List<String> QUOTA_ALLOCATION_CHANNEL = new List<String>{ 'PP-TT', 'OPE-TT', 'CA TT Team 1', 'CA TT Team 2' };
    // Process Instance
    public static final String PROCESS_INSTANCE_STATUS_PENDING = 'Pending';
    // Process Instance History
    public static final String PROCESS_INSTANCE_HISTORY_STEP_STATUS_PENDING = 'Pending';
    // Purchase Order
    public static final String PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_DEVELOPER_NAME = 'Place_Order';
    public static final String PURCHASE_ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_DEVELOPER_NAME = 'Place_Parts_Order';
    public static final String PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD = 'CNA Parts Sales Order - USD';
    public static final String PURCHASE_ORDER_ORDER_TYPE_CNA_SALES_ORDER_USD = 'CNA Sales Order - USD';
    public static final String PURCHASE_ORDER_STATUS_SUBMITTED = 'Submitted';
    public static final String PURCHASE_ORDER_SYNC_STATUS_IN_PROCESSING = 'In Processing';
    // Order
    public static final String ORDER_RECORD_TYPE_ACTIVATED_ORDER_DEVELOPER_NAME = 'Actived_Order';
    public static final String ORDER_RECORD_TYPE_CANCELED_ORDER_DEVELOPER_NAME = 'Canceled_Order';
    public static final String ORDER_RECORD_TYPE_DRAFT_ORDER_DEVELOPER_NAME = 'Draft_Order';
    // public static final String ORDER_RECORD_TYPE_PLACE_ORDER_DEVELOPER_NAME = 'Place_Order';
    public static final String ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_DEVELOPER_NAME = 'Place_Parts_Order';
    public static final String ORDER_RECORD_TYPE_PLACE_REPOST_ORDER_DEVELOPER_NAME = 'Re_Post_Order';
    // Chervon Survey Question
    public static final String CHERVON_SURVEY_QUESTION_TYPE_FREE_TEXT = 'Free Text';
    public static final String CHERVON_SURVEY_QUESTION_TYPE_MULTI_SELECT = 'Multi Select';
    public static final String CHERVON_SURVEY_QUESTION_TYPE_SINGLE_SELECT = 'Single Select';
    // Claim Pack
    public static final String CLAIM_PACK_TYPE_SERVICE_CLAIM = 'Service Claim';
    public static final String CLAIM_PACK_TYPE_FLEET_CLAIM = 'Fleet Claim';
    // Warranty Claim
    public static final String WARRANTY_CLAIM_STATUS_APPROVED = 'Approved';
    // Fleet Claim
    public static final String FLEET_CLAIM_APPROVAL_STATUS_APPROVED = 'Approved';
    // System Configuration
    public static final String SYSTEM_CONFIGURATION_RECORD_TYPE_SN_FORMAT_DEVELOPER_NAME = 'SN_Format';
    // Kit Item
    public static final String KIT_ITEM_KITS_AND_PRODUCTS_RECORD_TYPE_ID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(KIT_ITEM_KITS_AND_PRODUCTS_RECORD_TYPE_DEVELOPERNAME).getRecordTypeId();
    public static final String KIT_ITEM_PRODUCTS_AND_DIAGRAM_RECORD_TYPE_ID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(KIT_ITEM_PRODUCTS_AND_DIAGRAM_RECORD_TYPE_DEVELOPERNAME).getRecordTypeId();
    public static final String KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_ID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_DEVELOPERNAME).getRecordTypeId();
    public static final String All_ITEMS = 'All Items';
    public static final String PPC = 'PPC';
    public static final String ITEM_NUMBER = 'Item Number';
    public static final String PRE_SEASON_ORDER = 'Pre-season Order';
    public static final String GERMANY = 'Germany (DSV)';
    public static final String CHINA = 'China (DI)';
    public static final String DSV = 'DSV';
    public static final String DI = 'DI';
    //Honey Added -->Modifierd 优先级 的取值
    public static final String EXCLUSIVE_GROUP = 'Exclusive Group';
    public static final String LEVEL1 = 'Level 1 Incompatibility';
    public static final String LEVEL2 = 'Level 2 Incompatibility';
    public static final String LEVEL3 = 'Level 3 Incompatibility';
    //Honey Added -->Modifierd Pricing_Phase__c  的取值
    public static final String LIST_LINE = 'List Line Adjustment';
    public static final String ALL_LINE = 'All Line Adjustments';
    public static final String HEADER_LEVEL = 'Header Level Adjustments';
    public static final String BASE_PRICE = 'Base Price';
    //Honey Added -->Modifierd Application_Method__c  的取值
    public static final String PERCENT = 'Percent';
    public static final String NEW_PRICE = 'New Price';
    //Honey Added --> purchaseOrder Current_Step__c  的取值
    public static final String SELECT_CUSTOMER = '1';
    public static final String BRAND_PRODUCT = '2';
    public static final String FILL_IN_PO = '3';
    public static final String PREVIEW_SUBMIT = '4';
    //Honey Added -->Status  的取值
    public static final String SUBMITTED = 'Submitted';
    public static final String PENDING_REVIEW = 'Pending Review';
    //Honey Added-->Sales Rep Stock Status
    public static final String DRAFT_STOCK = 'New Order';
    public static final String SUBMITTED_DTOCK = 'pending for approval';
    public static final String APPROVAL_STOCK = 'Approved';
    public static final String REJECT_STOCK = 'Rejected';
    public static final String CANCELLED_STOCK = 'Cancelled';
    //Honey Added -->查询API名字
    public static final String ORDER_MODEL = 'Order_Model';
    public static final String ITEM_DESCRIPTION = 'Item Description';
    public static final String ITEM_DESCRIPTION_DE = 'Item_Description_DE__c';
    public static final String ITEM_DESCRIPTION_EN = 'Item_Description_EN__c';
    //英文或德文
    public static final String EN = 'en_US';
    public static final String DE = 'de';
    //Honey Added -->Customer->Sales Channel取值
    //OPE Dealer/Agricultural Machinery Dealer/Construction Material Dealer/Power Tool Dealer/Online Dealer/Other Dealers
    public static final String DISTRIBUTOR = 'Distributor';
    public static final String PBE = 'PbE';
    public static final String DEALER = 'Dealer';
    //Honey Added -->Product Record Type
    public static final String ACC = 'ACC';
    public static final String MKT = 'MKT';
    //
    public static final String SAM = 'SAM';
    public static final String KIT = 'TLS_KIT';
    public static final String SP = 'SP';
    public static final String TLS_PRODUCT = 'TLS_Product';
    public static final String LIST_PRICE = 'ListPrice';
    public static final String FINAL_PRICE = 'FinalPrice';
    public static final String STAND_DISCOUNT = 'discount';
    public static final String APPLICATION_METHOD = 'applicationMethod';
    //库存状态
    public static final String RED_LIGHT = 'red Light';
    public static final String YELLOW_LIGHT = 'yellow Light';
    public static final String GREEN_LIGHT = 'green Light';
    public static final String RED = 'Red Light';
    public static final String YELLOW = 'Yellow Light';
    public static final String GREEN = 'Green Light';
    //Honey Added DropShip TYpe
    public static final String END_CONSUMER = 'End Consumer';
    //Honey Added insade Sales Role
    public static final String INSIDE_SALES_MANAGER = 'Inside Sales Manager';
    public static final String SALES_MANAGER = 'Sales Manager';
    public static final String SALES_DIRECTOR = 'Sales Director';
    public static final String INSIDE_SALES_REP = 'Inside Sales Rep';
    public static final String DROP_SHIPPING = 'Dropship_Shipping_Address';
    public static final String SALES_PRICE = 'SalesPrice';
    //Honey Added By Request Status
    public static final String SUCCESS = 'SUCCESS';
    public static final String ERROR = 'ERROR';
    //Honey Added By Approval Level
    public static final String NO_REQUIRED = 'No Approver Required';
    public static final String LOGISTICS = 'Only Logistics';
    public static final String SUPERVISOR = 'Only Sales Supervisor';
    public static final String DIRECTOR = 'Sales Director';
    public static final String BILL_TO_ADDRESS = 'Bill To Address';
    public static final String SHIP_TO_ADDRESS = 'Ship To Address';
    public static final String BILL_TO_ADDRESS_TYPE = 'Billing_Address';
    public static final String SHIP_TO_ADDRESS_TYPE = 'Shipping_Address';
    public static final String DROPSHIP_ADDRESS_TYPE = 'Dropship_Shipping_Address';
    // dev2 常量类 23-7-
    //Aria Prospect
    public static final String PROSPECT_ASSOCIATIONGROUP_NAME = 'Prospect_Assoication_Group';
    public static final String PROSPECT_CHANNEL_NAME = 'Prospect_Channel';
    public static final String PROSPECT_BRANDPARTNER_NAME = 'Prospect_Brand_Partner';
    public static final String PROSPECT_ENDUSER_NAME = 'Prospect_End_User';
    public static final String PROSPECT_ASSOCIATIONGROUP_RECORD_TYPE_ID = Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_ASSOCIATIONGROUP_NAME) == null ? '' : Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_ASSOCIATIONGROUP_NAME).getRecordTypeId();
    public static final String PROSPECT_CHANNEL_RECORD_TYPE_ID = Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_CHANNEL_NAME) == null ? '' : Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_CHANNEL_NAME).getRecordTypeId();
    public static final String PROSPECT_BRANDPARTNER_RECORD_TYPE_ID = Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_BRANDPARTNER_NAME) == null ? '' : Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_BRANDPARTNER_NAME).getRecordTypeId();
    public static final String PROSPECT_ENDUSER_RECORD_TYPE_ID = Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_ENDUSER_NAME) == null ? '' : Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(PROSPECT_ENDUSER_NAME).getRecordTypeId();
    //Aria Prospect FileType
    public static final String PROSPECT_FILETYPE_TEST = 'TestReport';
    public static final String PROSPECT_FILETYPE_ATTACTMENT = 'Attachment';

    //Honey Added Price List DownLond
    public static final String START_DATE = 'StartDate';
    public static final String END_DATE = 'endDate';
    public static final String FILE_NAME = 'pwcTestForfrontendpreview';
    //prospect status
    public static final String Prospect_Status_ClosedLost = 'Closed Lost';
    public static final String Prospect_Status_Open = 'Open';
    public static final String Prospect_Status_Assigned = 'Assigned';
    public static final String Prospect_Status_Contacted = 'Contacted';
    public static final String Prospect_Status_Qualified = 'Qualified';
    public static final String Prospect_Status_PendingReview = 'Pending Review';
    public static final String Prospect_Status_Converted = 'Converted';

    //Honey Added Perpallet
    public static final String QTY = 'Qty';
    public static final String DESCRIPTION = 'Desc';
}