/**
 * <AUTHOR>
 * @date 2025-03-20
 * @description sales price export generate in chunk
 */
public with sharing class CCM_SalesPriceExportBatch implements Database.Batchable<sObject>{

    private Map<String,Map<String,Object>> mapProductId2Values;
    private List<Product2> lstProduct;
    private Account objAccount;
    private Map<Id, Pricebook_Entry__c> parentPbMap;
    private Map<Id, Pricebook_Entry__c> childPbMap;

    public CCM_SalesPriceExportBatch(Map<String,Map<String,Object>> mapProductId2Values, List<Product2> lstProduct, 
                                     Account objAccount, Map<Id, Pricebook_Entry__c> parentPbMap, Map<Id, Pricebook_Entry__c> childPbMap) {
        this.mapProductId2Values = mapProductId2Values;
        this.lstProduct = lstProduct;
        this.objAccount = objAccount;
        this.parentPbMap = parentPbMap;
        this.childPbMap = childPbMap;
    }

    private Map<String,Double> mapProdictId2Qty;
    private Date PrcingDate;
    public CCM_SalesPriceExportBatch(List<Product2> lstProduct, Map<String,Double> mapProdictId2Qty, Date PrcingDate,
                                     Account objAccount, Map<Id, Pricebook_Entry__c> parentPbMap, Map<Id, Pricebook_Entry__c> childPbMap) {        
        this.lstProduct = lstProduct;
        this.mapProdictId2Qty = mapProdictId2Qty;
        this.PrcingDate = PrcingDate;
        this.objAccount = objAccount;
        this.parentPbMap = parentPbMap;
        this.childPbMap = childPbMap;
    }

    public Iterable<sObject> start(Database.BatchableContext bc) {
        return this.lstProduct;
    }

    public void execute(Database.BatchableContext BC, list<Sobject> scope) {
        String jobId = BC.getJobId();
        jobId = jobId.substring(0, 15);
        System.debug('*** execute jobId: ' + jobId);
        List<Product2> productList = (List<Product2>)scope;
        List<String> lstProductIds = new List<String>();
        for(Product2 product : productList) {
            lstProductIds.add(product.Id);
        }
        Map<String,Map<String,Object>> mapProductId2Values = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(this.objAccount.Id, lstProductIds, this.PrcingDate, this.mapProdictId2Qty);
        if(this.mapProductId2Values == null) {
            this.mapProductId2Values = mapProductId2Values;
        }
        List<CCM_PurchaseOrderListController.PriceProductInfo> lstPriceInfo = new List<CCM_PurchaseOrderListController.PriceProductInfo>();
        for(Product2 objProduct : productList){
           CCM_PurchaseOrderListController.PriceProductInfo objPriceInfo = new CCM_PurchaseOrderListController.PriceProductInfo();
           Map<String,Object> mapFeild2Value = this.mapProductId2Values.get(objProduct.Id);
           if(mapFeild2Value != null){
               objPriceInfo.ListPrice =  ((Decimal)mapFeild2Value.get(CCM_Constants.LIST_PRICE)).setScale(2,RoundingMode.HALF_UP) ;       
               objPriceInfo.salesPrice =  ((Decimal)mapFeild2Value.get(CCM_Constants.FINAL_PRICE)).setScale(2,RoundingMode.HALF_UP) ; 
               objPriceInfo.StartDate = (Date)mapFeild2Value.get(CCM_Constants.START_DATE) ; 

               objPriceInfo.EndDate = (Date)mapFeild2Value.get(CCM_Constants.END_DATE) ; 
               objPriceInfo.country = (String)mapFeild2Value.get('country') ; 
               objPriceInfo.countryDescription = (String)mapFeild2Value.get('countryDescription') ; 
               objPriceInfo.salesChannel = (String)mapFeild2Value.get('salesChannel') ; 
               objPriceInfo.Classification1 = (String)mapFeild2Value.get('Classification1') ; 
               objPriceInfo.productClass = (String)mapFeild2Value.get('productClass') ; 
               objPriceInfo.PPCCode = (String)mapFeild2Value.get('PPCCode') ; 
               objPriceInfo.PPCDescription = (String)mapFeild2Value.get('PPCDescription') ; 
               objPriceInfo.productSeries = (String)mapFeild2Value.get('ProductSeries') ; 
               objPriceInfo.modiferType = (String)mapFeild2Value.get('modiferType') ; 
               objPriceInfo.modifer = (String)mapFeild2Value.get('modifer') ; 
               objPriceInfo.orderType = (String)mapFeild2Value.get('orderType') ; 
               objPriceInfo.incoTerm = (String)mapFeild2Value.get('incoTerm') ; 
              
               objPriceInfo.PriceName = (String)(this.parentPbMap.containsKey(objProduct.Id) ?
                                                this.parentPbMap.get(objProduct.Id).PriceBook__r.Name : this.childPbMap.get(objProduct.Id).PriceBook__r.Name);
           }
           objPriceInfo.productName = objProduct.Name;
           objPriceInfo.productModel = objProduct.Order_Model__c;
           objPriceInfo.productDescription = objProduct.Item_Description_DE__c;  
           objPriceInfo.CurrencyCode = this.objAccount.CurrencyIsoCode;
           objPriceInfo.CustomerName = this.objAccount.Name;
           objPriceInfo.CustomerNumber = this.objAccount.AccountNumber;
           lstPriceInfo.add(objPriceInfo);
        }

        // insert data into log
        Log__c log = new Log__c();
        log.RecordId__c = jobId;
        log.Name = 'Sales Price Export Data';
        log.ResParam__c = JSON.serialize(lstPriceInfo);
        insert log;
    }

    public void finish(Database.BatchableContext BC) {
        String jobId = BC.getJobId();
        jobId = jobId.substring(0, 15);
        System.debug('***finish jobId: ' + jobId);
        List<Log__c> logs = [SELECT Id FROM Log__c WHERE RecordId__c = :jobId AND Name = 'Export Sales Price'];
        for(Log__c log : logs) {
            log.ResParam__c = 'Complete';
        }
        update logs;
    }
}