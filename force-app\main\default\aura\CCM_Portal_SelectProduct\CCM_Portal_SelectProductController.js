({
    // 初始化
    doInit:function(component, event, helper){
        // 获取customer上的product 列表
        helper.getCustomerProduct(component);
        console.log(component.get('v.actionType'), component.get('v.userType'), '初始化------------product');
        // portal
        component.set('v.isDropShip', 'N');
        component.set('v.freightCost', 0);
        // 获取下拉
        helper.getPicklist(component, 'incoTerm');
        helper.getPicklist(component, 'paymentTerm');
        // 获取运费类型
        helper.getQueryCustomerType(component);
        component.set('v.accessoriesColumns', [
            { label: $A.get("$Label.c.CCM_No"), fieldName: 'no', type: 'text' },
            { label: $A.get("$Label.c.CCM_ProductDescriptionAccessories"), fieldName: 'productDescription', type: 'text' },
            { label: $A.get("$Label.c.CCM_OriginalProductName"), fieldName: 'originalProductName', type: 'text' },
            { label: $A.get("$Label.c.CCM_Model"), fieldName: 'model', type: 'text' },
            { label: $A.get("$Label.c.CCM_RequestDate"), fieldName: 'requestDate', type: 'text' },
            { label: $A.get("$Label.c.CCM_Qty"), fieldName: 'qty', type: 'text' },
            {
                label: $A.get("$Label.c.CCM_AddToShoppingChart"),
                width: '100px',
                tdStyle:'text-align: center',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${rowInfo}",
                        variant:"bare",
                        iconName:"utility:cart",
                        alternativeText:"cart", 
                        onclick: component.getReference('c.addProductList')
                    }
                },
            ]},
        ]);
        let warehouse = component.get('v.warehouse');
        if (warehouse === 'China (DI)') {
            component.set('v.termFlag', false);
        } else {
            component.set('v.termFlag', true);
        };
        // 判断数据状态
        let actionType = component.get('v.actionType');
        console.log(actionType, 'actionType-----------customer');
        if (actionType === 'edit' || actionType === 'draft') {
            // 获取旧数据
            helper.getPODetailInfo(component);
        } else {
            // 获取当前时间
            const year = new Date().getFullYear().toString();
            const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
            const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
            component.set('v.expectedDeliveryDate', `${year}-${month}-${day}`);
            // 获取品牌相关信息
            helper.getBrandInfo(component);
            // if from warranty claim
            let urlParams = new URLSearchParams(window.location.search);
            if(urlParams.get('claimId')) {
                helper.getWarrantyClaimNumber(component, urlParams.get('claimId'));
                helper.preFillProductInfoFromClaim(component);
            }
            else {
                helper.addNewProductLine(component);
            }
        }
    },
    // 下一步
    nextStep: function(component, event, helper){
        // 判断是否选择product & qty不能为空
        let productList = component.get('v.productList');
        if (!productList.length) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectLeastOneProduct"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }

        const hasInactive = helper.checkProductIsActive(productList);
        if(hasInactive) {
            return;
        }
        
        // if last product line: product is empty and qty is empty, not validate this last product line
        let productValid = true;
        let qtyValid = true;
        let size = productList.length;
        productList.forEach((item, index)=>{
            // if last line
            if(index + 1 === size) {
                if(!item.productId && !item.qty && !item.linediscount && productValid && qtyValid) {
                    item.lastBlankLine = true;
                    return;
                }
            }
            if(!item.productId && !item.linediscount) {
                productValid = false;
            }
            if (!item.qty && !item.linediscount) {
                qtyValid = false;
            }
        });
        if(!productValid) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": "Please fill in product!",
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        else if (!qtyValid) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_FillinQtyWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }

        let hasPromotionError = false;
        let errorPromotionCodes = [];
        productList.forEach(item => {
            if(item.type === "MainProduct") {
                if(item.hasPromotionError) {
                    errorPromotionCodes.push(item.promotioncode);
                    hasPromotionError = true;
                }
            }
        });
        if(hasPromotionError) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": "Promotions: " + errorPromotionCodes.join(", ") + " Thresholds not match, please adjust products or remove promotion",
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }

        // 必填校验
        const isValid = helper.getValidation(component);
        if (isValid) {
            helper.handleNextStep(component, true);
        } else {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_FillinRequiredFieldsWarning·"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
        }
    },
    // 上一步
    previousStep: function(component, event, helper){
        helper.handleNextStep(component, false);
        // component.set('v.actionType', 'draft');
        // component.set("v.currentStep", 1);
    },
    // 取消事件
    cancel: function(component){
        let url = window.location.origin + '/s/orderinformation';
        window.open(url, '_self');
    },
    // Available Promotion点击事件
    handleAvailablePromotion: function(component, event, helper){
        const index = component.get('v.operationRow');
        let productList = component.get('v.productList');
        component.set('v.mainProduct', productList[index]);
        helper.getOrderPromotionList(component, index);
    },
    // 添加product item
    addItem: function(component, event, helper){
        let newProductList = component.get('v.productList');
        const newProductItem = {
            'productDescription': null,
            'productId': null,
            'model': '',
            'requestDate': component.get('v.expectedDeliveryDate'),
            'qty': null,
            'unitPerPallet': '',
            'inventory': '',
            'listPrice': '',
            'discount': '',
            'unitNetPrice': '',
            'totalNetPrice': '',
            'remark': '',
            'promotioncode': '',
            'isPromotion': false,
            'key': Date.now().toString(),
            'type':'MainProduct',
            'isKit': false,
            'filteredProducts': [],
            'expanded': false,
        };
        newProductList.push(newProductItem);
        component.set('v.productList', [...newProductList]);
        helper.scrollToBottom(component);
    },
    // 输入产品数量(QTY)更新价格
    handleInputQty: function(component, event, helper){
        let intQty = event.getSource().get('v.value');
        let index = event.getSource().get('v.id');
        let productList = component.get('v.productList');
        let newProductList = JSON.parse(JSON.stringify(productList));
        let minqty;
        // 判断是否选择product
        if (!(newProductList[index].productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectProductFirstWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        // 输入值不得小于等于0
        if((intQty + 0) < 1){
            minqty = 1
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_QtyLessThan1Warning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
        }
        
        // 判断是否为discountOff || FixedAmount
        if (newProductList[index].showPromotionType == 'DiscountOff' || newProductList[index].showPromotionType =='FixedAmount') {
            if (Number(intQty) > newProductList[index].Max_Qty && Number(intQty) > 0) {
                intQty = newProductList[index].Max_Qty;
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Warning"),
                    "message": $A.get("$Label.c.CCM_NoMoreThanMaximum") + ',' + newProductList[index].Max_Qty,
                    "type": "warning",
                    "duration": "dismissible"
                }).fire();
            }
        }
        newProductList[index].qty = minqty || intQty;
        newProductList[index].totalNetPrice = Number(newProductList[index].qty) * Number(newProductList[index].unitNetPrice);
        // 判断是否kit
        if(newProductList[index].type != "threshold"){
            if (newProductList[index].filteredProducts && newProductList[index].filteredProducts.length) {
                // tool字段处理
                newProductList[index].filteredProducts.forEach((toolItem)=>{
                    // 判断红绿灯
                    let toolQty = toolItem.scale * newProductList[index].qty;
                    switch (toolItem.invotoryInfo) {
                        case 'green Light':
                            toolItem.inventory = 'Green';
                            if (toolQty <= toolItem.maxGreenLight) {
                                toolItem.inventory = 'Green';
                            } else if (toolQty > toolItem.maxGreenLight && toolQty <= toolItem.maxYellowLight) {
                                toolItem.inventory = 'Yellow';
                            } else {
                                toolItem.inventory = 'Red';
                            };
                            break;
                        case 'yellow Light':
                            toolItem.inventory = 'Yellow';
                            break;
                        case 'red Light':
                            toolItem.inventory = 'Red';
                            break;
                        default:
                            toolItem.inventory = 'Green';
                            break;
                    };
                    toolItem.qty = (toolItem.scale * newProductList[index].qty);
                })
            } else {
                // 判断红绿灯
                let qty = newProductList[index].qty;
                switch (newProductList[index].CurrentStatus) {
                    case 'green Light':
                        if (qty <= newProductList[index].MaxGreenLight) {
                            newProductList[index].inventory = 'Green';
                        } else if (qty > newProductList[index].MaxGreenLight && qty <= newProductList[index].MaxYellowLight) {
                            newProductList[index].inventory = 'Yellow';
                        } else {
                            newProductList[index].inventory = 'Red';
                        };
                        break;
                    case 'yellow Light':
                        newProductList[index].inventory = 'Yellow';
                        break;
                    case 'red Light':
                        newProductList[index].inventory = 'Red';
                        break;
                    default:
                        newProductList[index].inventory = 'Green';
                        break;
                };
            }
        }
        setTimeout(()=>{
            // 价格计算
            let isheaderdiscountProductlist = newProductList.filter(item=>item.key == newProductList[index].key && item.type == "MainProduct")
            if(newProductList[index].type != "offering") {
                // (isheaderdiscountProductlist.length && newProductList.filter(item=>item.key == newProductList[index].key).length != 1) || (isheaderdiscountProductlist[0] && isheaderdiscountProductlist[0].cheapest) ? helper.applypromotioncheck(component,index,newProductList,0) : "";
                helper.applypromotioncheck(component, index, newProductList, 0);
            }
            helper.getTotalNumber(component, newProductList,newProductList[index]);
            
        },100);
    },
    // 点击产品数量减号(QTY)更新价格
    handleSubQty: function(component, event, helper){
        let index = component.get('v.operationRow');
        let productList = component.get('v.productList');
        let newProductList = JSON.parse(JSON.stringify(productList));
        // 判断是否选择product
        if (!(newProductList[index].productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectProductFirstWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        console.log("newProductList[index].qty",newProductList[index].qty,newProductList[index].type);

        if (Number(newProductList[index].qty) == 1 && newProductList[index].type == "offering") {
            let _productList = component.get('v.productList');
            let arr = [];
            arr = _productList.filter((item,_index)=>{
                return _index == index ? "" : item
            })
            let productList = JSON.parse(JSON.stringify(arr));
            // newProductList =  [...productList];
            component.set('v.productList',[...productList])
            helper.getTotalNumber(component,  component.get('v.productList'));

        }else{
            if (Number(newProductList[index].qty) > 0) {
                newProductList[index].qty = Number(newProductList[index].qty) - 1;
                newProductList[index].totalNetPrice = Number(newProductList[index].qty) * Number(newProductList[index].unitNetPrice);
            };
            // 判断是否kit
            if(newProductList[index].type != "threshold"){
                if (newProductList[index].filteredProducts && newProductList[index].filteredProducts.length) {
                    // tool字段处理
                    newProductList[index].filteredProducts.forEach((toolItem)=>{
                        // 判断红绿灯
                        let toolQty = toolItem.scale * newProductList[index].qty;
                        switch (toolItem.invotoryInfo) {
                            case 'green Light':
                                toolItem.inventory = 'Green';
                                if (toolQty <= toolItem.maxGreenLight) {
                                    toolItem.inventory = 'Green';
                                } else if (toolQty > toolItem.maxGreenLight && toolQty <= toolItem.maxYellowLight) {
                                    toolItem.inventory = 'Yellow';
                                } else {
                                    toolItem.inventory = 'Red';
                                };
                                break;
                            case 'yellow Light':
                                toolItem.inventory = 'Yellow';
                                break;
                            case 'red Light':
                                toolItem.inventory = 'Red';
                                break;
                            default:
                                toolItem.inventory = 'Green';
                                break;
                        };
                        toolItem.qty = (toolItem.scale * newProductList[index].qty);
                    })
                } else {
                    // 判断红绿灯
                    let qty = newProductList[index].qty;
                    switch (newProductList[index].CurrentStatus) {
                        case 'green Light':
                            if (qty <= newProductList[index].MaxGreenLight) {
                                newProductList[index].inventory = 'Green';
                            } else if (qty > newProductList[index].MaxGreenLight && qty <= newProductList[index].MaxYellowLight) {
                                newProductList[index].inventory = 'Yellow';
                            } else {
                                newProductList[index].inventory = 'Red';
                            };
                            break;
                        case 'yellow Light':
                            newProductList[index].inventory = 'Yellow';
                            break;
                        case 'red Light':
                            newProductList[index].inventory = 'Red';
                            break;
                        default:
                            newProductList[index].inventory = 'Green';
                            break;
                    };
                }
            }
            setTimeout(()=>{
                // 价格计算
                let isheaderdiscountProductlist = newProductList.filter(item=>item.key == newProductList[index].key && item.type == "MainProduct")
                if(newProductList[index].type != "offering") {
                    // (isheaderdiscountProductlist.length && newProductList.filter(item=>item.key == newProductList[index].key).length != 1) || (isheaderdiscountProductlist[0] && isheaderdiscountProductlist[0].cheapest) ? helper.applypromotioncheck(component,index,newProductList,-1) : "";
                    helper.applypromotioncheck(component, index, newProductList, -1);
                }
                helper.getTotalNumber(component, newProductList,newProductList[index]);
                
            },100);
        }
    },
    // 点击产品数量加号(QTY)更新价格
    handleAddQty: function(component, event, helper){
        let index = component.get('v.operationRow');
        let productList = component.get('v.productList');
        let newProductList = JSON.parse(JSON.stringify(productList));
        // 判断是否选择product
        if (!(newProductList[index].productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectProductFirstWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        newProductList[index].qty = Number(newProductList[index].qty) + 1;
        newProductList[index].totalNetPrice = Number(newProductList[index].qty) * Number(newProductList[index].unitNetPrice);
        // 判断是否kit
        if(newProductList[index].filteredProducts && newProductList[index].type != "threshold"){
            if (newProductList[index].filteredProducts.length) {
                // tool字段处理
                newProductList[index].filteredProducts.forEach((toolItem)=>{
                    // 判断红绿灯
                    let toolQty = toolItem.scale * newProductList[index].qty;
                    switch (toolItem.invotoryInfo) {
                        case 'green Light':
                            toolItem.inventory = 'Green';
                            if (toolQty <= toolItem.maxGreenLight) {
                                toolItem.inventory = 'Green';
                            } else if (toolQty > toolItem.maxGreenLight && toolQty <= toolItem.maxYellowLight) {
                                toolItem.inventory = 'Yellow';
                            } else {
                                toolItem.inventory = 'Red';
                            };
                            break;
                        case 'yellow Light':
                            toolItem.inventory = 'Yellow';
                            break;
                        case 'red Light':
                            toolItem.inventory = 'Red';
                            break;
                        default:
                            toolItem.inventory = 'Green';
                            break;
                    };
                    toolItem.qty = (toolItem.scale * newProductList[index].qty);
                })
            } else {
                // 判断红绿灯
                let qty = newProductList[index].qty;
                switch (newProductList[index].CurrentStatus) {
                    case 'green Light':
                        if (qty <= newProductList[index].MaxGreenLight) {
                            newProductList[index].inventory = 'Green';
                        } else if (qty > newProductList[index].MaxGreenLight && qty <= newProductList[index].MaxYellowLight) {
                            newProductList[index].inventory = 'Yellow';
                        } else {
                            newProductList[index].inventory = 'Red';
                        };
                        break;
                    case 'yellow Light':
                        newProductList[index].inventory = 'Yellow';
                        break;
                    case 'red Light':
                        newProductList[index].inventory = 'Red';
                        break;
                    default:
                        newProductList[index].inventory = 'Green';
                        break;
                };
            }
        }
        setTimeout(()=>{
            // 价格计算
            let isheaderdiscountProductlist = newProductList.filter(item=>item.key == newProductList[index].key && item.type == "MainProduct")
            if(newProductList[index].type != "offering") {
                // (isheaderdiscountProductlist.length && newProductList.filter(item=>item.key == newProductList[index].key).length != 1) || (isheaderdiscountProductlist[0] && isheaderdiscountProductlist[0].cheapest) ? helper.applypromotioncheck(component,index,newProductList,1) : "";
                helper.applypromotioncheck(component, index, newProductList, 1);
            }
            helper.getTotalNumber(component, newProductList,newProductList[index]);
            
        },100);
    },
    // lookup select
    onSelect: function(component, event, helper){
        let id = event.getParam('data');
        if (id) {
            let productInfo = null;
            let productOptions = component.get('v.productOptions');
            productOptions.forEach((productItem)=>{
                if (productItem.Id == id) {
                    productInfo = JSON.parse(JSON.stringify(productItem));
                }
            });
            let index = component.get('v.operationRow');
            // 查询product detail
            helper.getProductDetailInfo(component, productInfo, index);
        }
    },
    // 删除事件
    handleDelete: function(component, event, helper){
        let index = component.get('v.operationRow');
        let productDraftList = component.get('v.productList');
        let deleteProductKey = productDraftList[index].key;
        // 遍历数组，删除order product & promotion product
        let arr = [];
        arr = productDraftList.filter((item)=>{
            return !item.key.includes(deleteProductKey);
        })
        // productDraftList.splice(index, 1);
        let productList = JSON.parse(JSON.stringify(arr));
        // 价格计算
        helper.getTotalNumber(component, productList);
        component.set('v.productList', [...productList]);

    },
    // 获取当前行下标
    rowFocus:function(component, event, helper) {
        var indexRow = event.currentTarget.id;
        component.set('v.operationRow', indexRow);
        // console.log(indexRow, 'indexRow--------------');
    },
    //选择品牌
    handleBrandChange:function(component, event, helper) {
        // 获取品牌相关信息
        helper.getBrandInfo(component);
    },
    // 关闭弹框
    cancelEvent:function(component, event, helper) {
        component.set('v.promotionModalFlag', false);
        component.set('v.bogoFlag', false);
        component.set('v.thresholdAndoffering',[]) // 暂存数据清空

    },
    // 应用promotion  推上表格只分两种  Ⅰ:直接Push   Ⅱ:select Product后Push
    applyEvent:function(component, event, helper) {
        let productList = component.get('v.productList')
        productList = productList.filter(item => item.key != component.get('v.mainProduct').key || (item.key == component.get('v.mainProduct').key && item.type == "MainProduct"))
        productList.forEach(item =>{
            if(component.get('v.mainProduct').key == item.key){
                if(item.cheapest){
                    item.discount = 0
                    item.unitNetPrice = item.salesPrice
                    item.discount = ""
                    item.unitNetPrice = item.salesPrice
                    item.totalNetPrice = item.qty * item.salesPrice
                    item.Max_Qty = ""
                    item.useHeaderDiscount = true
                    delete item.cheapest
                }
            }
        })
        component.set('v.productList',productList)
        let index = event.getSource().get('v.value');                     // 当前行索引
        let promotionList = component.get('v.promotionList');             // 表格所有值
        let applyPromotionItem = promotionList[index];                    // 当前行数据
        // 为offerig添加类型标识
        for (let key in component.get('v.category')) {
            applyPromotionItem.ruleList.forEach((item,index)=>{
                item.offeringList.forEach((v,i)=>{
                    if(component.get('v.category')[key].includes(v.offering.RecordType.DeveloperName)){
                        applyPromotionItem.ruleList[index].offeringList[i].Largetype = key
                    }
                })
            })
        }
        // 点击感叹号的当前行数据
        component.set('v.Currentrowdata',applyPromotionItem)
        // threshold的类型为需要选择产品的情况
        if(component.get('v.thresholdProductSelect').includes(applyPromotionItem.ruleList[0].thresholdList[0].threshold.RecordType.DeveloperName)){
            helper.disposePromotion(component, applyPromotionItem, "multiple",true);
        // threshold的类型为不需要选择产品的情况
        }else{
            // 跑一遍threshold的代码,添加一些挂载在主产品的上校验
            helper.disposePromotion(component, applyPromotionItem, "single",true);
            // offering需要弹窗的情况
            if(component.get('v.offeringProductSelect').includes(applyPromotionItem.ruleList[0].offeringList[0].offering.RecordType.DeveloperName)){
                helper.disposePromotion(component, applyPromotionItem, "multiple",false);
            }else{
                // offering不需要弹窗的情况
                helper.disposePromotion(component, applyPromotionItem, "single",false); //不需要二次选择product弹窗的逻辑,直接push
            }
        }   
    },
        // 清除已应用的promotion
    resetPromotion:function(component, event, helper){
        let index = event.getSource().get('v.value');                     // 当前行索引
        let productList = component.get('v.productList');                 // 表格所有值
        let _Product = productList[index];                                // 当前行数据
        let _PromotionCode = _Product.promotioncode
        let _Search
        component.set('v.mainProduct',_Product)
        // 如果为search方法
        if(event.target.name == "Search" && _Product.productDescription && _Product.promotioncode){
            component.set('v.isBusy',true)
            var action = component.get("c.getPromotionsByProduct");
            action.setParams({
                prodId: _Product.productId,
                customerId: component.get('v.customerId'),
                isPortal: true,
                isDropShip: component.get('v.isDropShip') === 'Y' ? true : false,
                PricingDate: component.get('v.pricingDate'),
            });
            action.setCallback(this, function (response) {
                component.set('v.isBusy',false)
                var state = response.getState();
                if (state === "SUCCESS") {
                    var result = response.getReturnValue();
                    result = result.filter(item => (item.promotionWindow[0].Availability_in_Dealer_Portal__c != "unavailable in the portal") && (item.promotionWindow[0].Availability_in_Dealer_Portal__c != "available in the portal and it cannot be placed by the dealers"))
                    // console.log(result, '获取当前行的promotion选择列表==========');
                    if (result && result.length) {
                        result.forEach(item=>{
                            item.Name = item.promotion.Promo_Code__c
                        })
                        _Search = result.filter(item=> item.Name == _Product.promotioncode)
                        if(_Search.length){
                            // 为offerig添加类型标识
                            for (let key in component.get('v.category')) {
                                _Search[0].ruleList.forEach((item,index)=>{
                                    item.offeringList.forEach((v,i)=>{
                                        if(component.get('v.category')[key].includes(v.offering.RecordType.DeveloperName)){
                                            _Search[0].ruleList[index].offeringList[i].Largetype = key
                                        }
                                    })
                                })
                            }
                            // 点击感叹号的当前行数据
                            component.set('v.Currentrowdata',_Search[0])
                            // threshold的类型为需要选择产品的情况
                            if(component.get('v.thresholdProductSelect').includes(_Search[0].ruleList[0].thresholdList[0].threshold.RecordType.DeveloperName)){
                                helper.disposePromotion(component, _Search[0], "multiple",true);
                                // threshold的类型为不需要选择产品的情况
                            }else{
                                // 跑一遍threshold的代码,添加一些挂载在主产品的上校验
                                helper.disposePromotion(component, _Search[0], "single",true);
                                // offering需要弹窗的情况
                                if(component.get('v.offeringProductSelect').includes(_Search[0].ruleList[0].offeringList[0].offering.RecordType.DeveloperName)){
                                    helper.disposePromotion(component, _Search[0], "multiple",false);
                                }else{
                                    // offering不需要弹窗的情况
                                    helper.disposePromotion(component, _Search[0], "single",false); //不需要二次选择product弹窗的逻辑,直接push
                                }
                            }
                        }else{
                            var toastEvt = $A.get("e.force:showToast");
                            toastEvt.setParams({
                                "title": $A.get("$Label.c.CCM_Warning"),
                                "message": $A.get("$Label.c.CCM_PromotionCodeIncorrectWarning"),
                                "type": "warning",
                                "duration": "dismissible"
                            }).fire();
                        }
                    }
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": response.getError()[0].message,
                        "type": "error"
                    }).fire();
                }
            });
            $A.enqueueAction(action);
        // 如果为Reset方法
        }else if(event.target.name == "Reset"){
            productList.forEach(item =>{
                if(item.key == _Product.key && item.type == "MainProduct"){
                    delete item.cheapest;
                    item.promotioncode = "";
                    item.Max_Qty = "";
                    item.Min_Qty = "";
                    item.Min_Total_Amount = "";
                    item.Threshold_Qty = "";
                    item.Mixgoods_Qty = "";
                    item.Mixgoods_Amount = "";
                    item.Threshold_Amount = "";
                    item.UsePromotion = false;
                    item.Min_Amount = "";
                    item.discount = 0;
                    item.unitNetPrice = item.salesPrice;
                    item.totalNetPrice = item.qty * item.salesPrice;
                    item.hasPromotionError = false;
                }
            })

            component.set('v.headerDiscountList',component.get('v.headerDiscountList').filter(v => v.promotionCode != _PromotionCode))
            component.set('v.woheaderdiscount',component.get('v.woheaderdiscount').filter(v => v[0].promotionCode != _PromotionCode))
            component.set('v.productList',productList.filter(item => item.key != _Product.key || (item.key == _Product.key && item.type == "MainProduct")))
            helper.getTotalNumber(component, component.get("v.productList"));
        }else{
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Error"),
                "message": $A.get("$Label.c.CCM_PromotionAndProductEmptyError"),
                "type": "error",
                "duration": "dismissible"
            }).fire();
        }
        },
    // 模糊过滤goods
    handleFilterGoods:function(component, event, helper) {
        let filterStr = event.getSource().get('v.value');
        let _product = component.get('v.pupUp_ProductSelect')
        _product.list.forEach(item=> {
            if(item.productDescription.indexOf(filterStr) == -1){
                item.hidden = true
            }else{
                item.hidden = false
            }
        })
        component.set('v.pupUp_ProductSelect',_product)
    },
    // threshold或offering为mix product时.选择单个产品触发的事件
    onSelect_Product : function(component, event, helper) {  // 点击Finish方法,筛出select为true的push进表格
        let _ProductSelect = component.get('v.pupUp_ProductSelect')
        // console.log("当前选择的",JSON.parse(JSON.stringify(_ProductSelect.list[event.target.index])));
        _ProductSelect.list[event.target.index].select = !_ProductSelect.list[event.target.index].select
        component.set('v.pupUp_ProductSelect',_ProductSelect)
    },
    // 选择产品后添加 && 为空校验
    bogoFinishEvent : function(component, event, helper){
        let mainProduct = component.get('v.mainProduct')
        let key = mainProduct.key                                                                                                                            // 绑定主产品与Promotion产品的标示
        let selectProduct = component.get('v.pupUp_ProductSelect').list.filter(item=>item && item.select)                                                    // mix product勾选的product
        // console.log("mainProduct",JSON.parse(JSON.stringify(mainProduct)));
        // console.log("mainProduct",JSON.parse(JSON.stringify(component.get('v.productList'))));
        if(mainProduct.Min_Total_Qty){
            let total_qty = mainProduct.Min_Total_Qty
            selectProduct.forEach(v=>{
                total_qty = total_qty - v.qty
            })
            // console.log("total_qty",total_qty);
            if(Number(total_qty) > 0){
                let _productList = component.get('v.productList')
                _productList.forEach(item=>{
                    if(item.key == key && item.type == "MainProduct"){
                        item.qty = item.qty - total_qty >= 0 ? item.qty : total_qty
                        item.totalNetPrice = item.qty * item.unitNetPrice
                    }
                })
                component.set('v.productList',_productList)
            }
        }else if(mainProduct.Min_Total_Amount){
            let total_amount = mainProduct.Min_Total_Amount
            selectProduct.forEach(v=>{
                total_amount = Number(total_amount - Number(v.qty) * Number(v.unitNetPrice))
            })
            // console.log("total_amount",total_amount);
            if(Number(total_amount) > 0){
                let _productList = component.get('v.productList')
                _productList.forEach(item=>{
                    if(item.key == key && item.type == "MainProduct"){
                        item.qty = item.qty - Math.ceil(total_amount/item.unitNetPrice) >= 0 ? item.qty : Math.ceil(total_amount/item.unitNetPrice)
                        item.totalNetPrice = item.qty * item.unitNetPrice
                    }
                })
                component.set('v.productList',_productList)
            }
        }
        let isthreshold = component.get('v.pupUp_ProductSelect').list.length > 0 && component.get('v.pupUp_ProductSelect').list[0].type == "threshold"
        let Max_DTM = component.get('v.Currentrowdata').ruleList[0].thresholdList[0].threshold.Maximum_Different_Tool_Models_Per_Order__c -1                 // mix product选择的最大种类数量,减去当前的主产品
        let Min_DTM = component.get('v.Currentrowdata').ruleList[0].thresholdList[0].threshold.Minimum_Different_Tool_Models_Per_Order__c -1                 // mix product选择的最小种类数量,减去当前的主产品
        let Free_Max_Total_Qty = component.get('v.Currentrowdata').ruleList[0].offeringList[0].offering.Free_Goods_Total_Quantity__c                         // 免费商品的最大数量限制
        if(Max_DTM > 0 && selectProduct.length > Max_DTM && isthreshold){        // 选择的MixGoods个数大于Max Different Tool Models && offering不需要校验
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Error"),
                "message": $A.get("$Label.c.CCM_MixGoodsMaxThresholdError").replace('{0}', Max_DTM),
                "type": "error",
                "duration": "dismissible"
            }).fire();
        }else if(Min_DTM > 0 && selectProduct.length < Min_DTM && isthreshold){  // 选择的MixGoods个数小于Min Different Tool Models  // offering不需要校验
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Error"),
                "message": $A.get("$Label.c.CCM_MixGoodsMinThresholdError").replace('{0}', Min_DTM),
                "type": "error",
                "duration": "dismissible"
            }).fire();
        }else{                                                                  // 通过校验,进入数据处理
            // 为threshold的mix product select的情况,校验一下offering是否也需要弹框选择product
            if(component.get('v.pupUp_ProductSelect').goon){
                // offering需要弹框
                if(component.get('v.offeringProductSelect').includes(component.get('v.Currentrowdata').ruleList[0].offeringList[0].offering.RecordType.DeveloperName)){
                    let _thresholdAndoffering = component.get('v.thresholdAndoffering')
                    _thresholdAndoffering.push(...selectProduct)
                    helper.disposePromotion(component, component.get('v.Currentrowdata'), "multiple",false);
                }
                // offering不需要弹框
                else{
                    component.set('v.bogoFlag', false);
                    helper.disposePromotion(component, component.get('v.Currentrowdata'), "single",false);
                }
            }
            // 如果是offering,直接表格赋值  && 添加一个大类标识
            else{
                let FreeGoodsTotalQty = 0
                selectProduct.map(item => FreeGoodsTotalQty += item.qty*1)
                if(Free_Max_Total_Qty && FreeGoodsTotalQty > Free_Max_Total_Qty){
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": $A.get("$Label.c.CCM_GetUpTo") + ':' + Free_Max_Total_Qty,
                        "type": "error",
                        "duration": "dismissible"
                    }).fire();
                }else if(selectProduct.filter(item => item.qty > item.Max_Qty).length > 0){
                    let _selectProduct = selectProduct.filter(item => item.qty > item.Max_Qty)[0]
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": $A.get("$Label.c.CCM_MaxFreeGoodsExceedError") + _selectProduct.Max_Qty,
                        "type": "error",
                        "duration": "dismissible"
                    }).fire();
                }else{
                let _thresholdAndoffering = component.get('v.thresholdAndoffering')
                _thresholdAndoffering.push(...selectProduct)
                component.set('v.bogoFlag', false);
                if(component.get('v.productList').filter(item => item.key == key).length >1){
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Success"),
                        "message": $A.get("$Label.c.CCM_NewPromotionSelectedInfo"),
                        "type": "success"
                    }).fire();
                }
                let productList = component.get('v.productList').filter(item => item.key != key || item.type == 'MainProduct')
                if(component.get('v.hasPoolProductCombine')) {
                    let signleProductOffering = component.get('v.signleProductOffering');
                    _thresholdAndoffering.push(...signleProductOffering);
                }
                helper.changeOrderList(component, productList, _thresholdAndoffering);
            }
            }
        }

    },

    // 关联配件
    handleRecommendedAccessories: function(component, event, helper){
        // let index = component.get('v.operationRow');
        // let productList = component.get('v.productList');
        // let newProductList = JSON.parse(JSON.stringify(productList));
        // let productInfo = newProductList[index];
        let rowInfo = event.getSource().get('v.id');
        console.log(rowInfo, '关联配件rowInfo---------');
        // 判断是否选择product
        if (!(rowInfo.productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectProductFirstWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        helper.getAccessoriesList(component, rowInfo);
    },
    // Unit Per Pallet提示
    handleUnitPerPallet: function(component, event, helper){
        let index = component.get('v.operationRow');
        let productDraftList = component.get('v.productList');
        // 判断是否选择product
        if (!(productDraftList[index].productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectProductFirstWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        let unitPerPallet = productDraftList[index].unitPerPallet;
        console.log(unitPerPallet, 'unitPerPallet--------------');
        if (unitPerPallet) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Success"),
                "message": unitPerPallet.toString() + $A.get("$Label.c.CCM_PiecesPerPallet"),
                "type": "success"
            }).fire();
        } else {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_NoDataAvailable"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
        }
    },
    // 设置Expected Delivery Date
    setExpectedDeliveryDate: function(component, event, helper){
        let productList = component.get('v.productList');
        let newProductList = JSON.parse(JSON.stringify(productList));
        let expectedDeliveryDate = component.get('v.expectedDeliveryDate');
        newProductList.forEach((item)=>{
            item.requestDate = expectedDeliveryDate;
            if (item.filteredProducts.length) {
                item.filteredProducts.forEach((toolItem)=>{
                    toolItem.requestDate = expectedDeliveryDate;
                })
            }
        });
        setTimeout(()=>{
            component.set('v.productList', newProductList);
        }, 
        50);
    },
    accessoriesCancelEvent: function(component, event, helper){
        component.set('v.accessoriesModalFlag', false);
    },
    // 添加配件product
    addProductList: function(component, event, helper){
        let accInfo = JSON.parse(JSON.stringify(event.getSource().get('v.value')));
        let productList = component.get('v.productList');
        let newProductList = JSON.parse(JSON.stringify(productList));
        const newProductItem = {
            'productDescription': accInfo.ProductDescription,
            'productId': accInfo.productId,
            'model': accInfo.Model,
            'qty': '',
            'unitPerPallet': accInfo.Qty,
            'listPrice': accInfo.listPrice,
            'discount': accInfo.discount,
            'unitNetPrice': accInfo.SalesPrice,
            'unitNetPriceCaculate': accInfo.SalesPriceCaculate,
            'totalNetPrice': accInfo.TotalNetPrice,
            'UsePromotion': false,
            'useHeaderDiscount': true,
            'remark': accInfo.remark,
            'promotioncode': '',
            'MaxGreenLight': accInfo.MaxGreenLight,
            'MaxYellowLight': accInfo.MaxYellowLight,
            'CurrentStatus': accInfo.CurrentStatus,
            'SalesPriceCaculate': accInfo.SalesPriceCaculate,
            'listPriceCaculate': accInfo.listPriceCaculate,
            'standDiscount': accInfo.standDiscount,
            'applicationMethod': accInfo.applicationMethod,
            'requestDate': component.get('v.expectedDeliveryDate'),
            'inventory': 'Red',
            'isPromotion': false,
            'key': Date.now().toString(),
            'type': 'MainProduct',
            'salesPrice': accInfo.SalesPrice,
            'filteredProducts': accInfo.filteredProducts,
            'expanded': false,
        };
        if (newProductItem.filteredProducts.length) {
            newProductItem.isKit = true;
            let newFilteredProducts = JSON.parse(JSON.stringify(newProductItem.filteredProducts));
            newProductItem.filteredProducts = [];
            newFilteredProducts.forEach((toolItem, toolIndex)=>{
                // 判断红绿灯
                let toolQty = toolItem.Units_Per_Master_Carton_EA__c * toolItem.Qty;
                switch (toolItem.inventory) {
                    case 'green Light':
                        toolItem.inventory = 'Green';
                        if (toolQty <= toolItem.MaxGreenLight) {
                            toolItem.inventory = 'Green';
                        } else if (toolQty > toolItem.MaxGreenLight && toolQty <= toolItem.MaxYellowLight) {
                            toolItem.inventory = 'Yellow';
                        } else {
                            toolItem.inventory = 'Red';
                        };
                        break;
                    case 'yellow Light':
                        toolItem.inventory = 'Yellow';
                        break;
                    case 'red Light':
                        toolItem.inventory = 'Red';
                        break;
                    default:
                        toolItem.inventory = 'Green';
                        break;
                }
                newProductItem.filteredProducts.push({
                    id: toolItem.Id,
                    productDescription: toolItem.Item_Description_EN__c,
                    model: toolItem.Order_Model__c,
                    requestDate: component.get('v.expectedDeliveryDate'),
                    qty: (toolItem.Units_Per_Master_Carton_EA__c * newProductItem.qty),
                    // 缺失
                    unitPerPallet: '',
                    inventory: toolItem.inventory,
                    listPrice: 0,
                    discount: 0,
                    unitNetPrice: 0,
                    totalNetPrice: 0,
                    scale: toolItem.Units_Per_Master_Carton_EA__c,
                    invotoryInfo: toolItem.InvotoryInfo__c,
                    maxGreenLight: toolItem.MaxGreenLight__c,
                    maxYellowLight: toolItem.MaxYellowLight__c,
                })
            })
            
        } else {
            newProductItem.isKit = false;
            newProductItem.filteredProducts = [];
        }
        // 判断红绿灯
        switch (newProductItem.CurrentStatus) {
            case 'green Light':
                if (newProductItem.qty <= newProductItem.MaxGreenLight) {
                    newProductItem.inventory = 'Green';
                } else if (newProductItem.qty > newProductItem.MaxGreenLight && newProductItem.qty <= newProductItem.MaxYellowLight) {
                    newProductItem.inventory = 'Yellow';
                } else {
                    newProductItem.inventory = 'Red';
                };
                break;
            case 'yellow Light':
                newProductItem.inventory = 'Yellow';
                break;
            case 'red Light':
                newProductItem.inventory = 'Red';
                break;
            default:
                newProductItem.inventory = 'Green';
                break;
        };
        if(accInfo['replaceIndex']) {
            newProductList[accInfo['replaceIndex']] = JSON.parse(JSON.stringify(newProductItem));
        }
        else {
            const lastItem = newProductList[newProductList.length - 1];
            if(!lastItem.productId && !lastItem.qty) {
                newProductList.splice(newProductList.length - 1, 0, JSON.parse(JSON.stringify(newProductItem)));
            }
            else {
                newProductList.push(JSON.parse(JSON.stringify(newProductItem)));
            }
        }
        setTimeout(()=>{
            // 价格计算
            helper.getTotalNumber(component, JSON.parse(JSON.stringify(newProductList)));
            component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));

            const showOnlySubstitute = component.get('v.showOnlySubstitute');
            if(showOnlySubstitute) {
                component.set('v.relatedSubstituteModalFlag', false);
            }
        }, 100)
    },

// 检索promotion
    searchpromotion : function(component, event, helper){
        if(component.get('v.promotionList').filter(item=>item.Name == event.detail.value).length > 0){
            let index = event.getSource().get('v.value');                                                                                    // 当前行索引
            let promotionList = component.get('v.promotionList');                                                                            // 表格所有值
            let applyPromotionItem = (component.get('v.promotionList').filter(item=>item.Name == event.detail.value))[0];                    // 当前行数据
            let productList_View = component.get('v.productList_View')                                                                       // 新的数据结构
            for (let key in component.get('v.category')) {
                if(component.get('v.category')[key].includes(applyPromotionItem.ruleList[0].offeringList[0].offering.RecordType.DeveloperName)){
                    applyPromotionItem.Largetype = key
                    if(productList_View[0] && productList_View[0].promotionList[key] && productList_View[0].promotionList[key].offering.length >= 0){
                        productList_View[0].promotionList[key] = {}
                        component.set('v.productList_View',productList_View)
                        var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": $A.get("$Label.c.CCM_Error"),
                            "message": $A.get("$Label.c.CCM_NewPromotionSelectedInfo") + [key],
                            "type": "error"
                        }).fire();
                    }
                    // ======================================= 未重复的弹框逻辑 Start
                    // threshold是否需要弹窗
                    if(component.get('v.thresholdProductSelect').includes(applyPromotionItem.ruleList[0].thresholdList[0].threshold.RecordType.DeveloperName)){
                        helper.disposePromotion(component, applyPromotionItem, "multiple",true);
                    }else{
                        helper.disposePromotion(component, applyPromotionItem, "single",true);
                        // offering是否需要弹窗
                        if(component.get('v.offeringProductSelect').includes(applyPromotionItem.ruleList[0].offeringList[0].offering.RecordType.DeveloperName)){ 
                            helper.disposePromotion(component, applyPromotionItem, "multiple",false); // 需要二次选择product弹窗的逻辑,处理一下   
                        }else{
                            helper.disposePromotion(component, applyPromotionItem, "single",false);   //不需要二次选择product弹窗的逻辑,直接push
                        }
                    }   
                    // ======================================= 未重复的弹框逻辑 End
                }
                component.set('v.Currentrowdata',applyPromotionItem)                                  // 点击感叹号的当前行数据
            }
        }
    },
    // tool折叠
    showShipToolList: function(component, event, helper){
        var id = event.currentTarget.getAttribute('id');
        // var expanded = event.currentTarget.getAttribute('data-expanded');
        // if(expanded == 'true'){
        //     document.getElementById('tool' + id).style.display = 'none'
        //     event.currentTarget.setAttribute('data-expanded', false);
        // }else{
        //     document.getElementById('tool' + id).style.display = 'table-row';
        //     event.currentTarget.setAttribute('data-expanded', true);
        // }
        let index = component.get('v.operationRow');
        let productList = component.get('v.productList');
        let newProductList = JSON.parse(JSON.stringify(productList));
        let expanded = newProductList[index].expanded;
        console.log(expanded, 'expanded-------------');
        if(expanded){
            document.getElementById('tool' + id).style.display = 'none';
            newProductList[index].expanded = false;
            // event.currentTarget.setAttribute('data-expanded', false);
        }else{
            document.getElementById('tool' + id).style.display = 'table-row';
            newProductList[index].expanded = true;
            // event.currentTarget.setAttribute('data-expanded', true);
        }
        setTimeout(()=>{
            component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));
        },100);
    },
    // 修改Request Date
    handleRequestDate: function(component, event, helper){
        let value = event.getSource().get('v.value');
        let index = component.get('v.operationRow');
        let productList = component.get('v.productList');
        let newProductList = JSON.parse(JSON.stringify(productList));
        // 判断是否选择product
        if (!(newProductList[index].productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectProductFirstWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            newProductList[index].requestDate = null;
            component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));
            return;
        }
        if (newProductList[index].filteredProducts.length) {
            newProductList[index].filteredProducts.forEach((item)=>{
                item.requestDate = value;
            })
            setTimeout(()=>{
                component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));
            },100);
        }
    },
    // 获取 header level promotion code
    // 应当 settimeout
    searchHeaderPromotionByCode: function (component, event, helper) {
        //
        component.set("v.promotionCodeHeaderLevel", event.detail.value);
    },
    // 点击应用 header promotion code 的 event
    applyHeaderPromotionCode: function (component, event, helper) {
        component.set('v.isBusy',true);
        let productList = component.get('v.productList');
        let _woheaderdiscount = component.get('v.woheaderdiscount')
        let _promotionCodeHeaderLevel = component.get("v.promotionCodeHeaderLevel")
        if(_woheaderdiscount.length > 0 && _woheaderdiscount.filter(item => item[0].promotionCode == _promotionCodeHeaderLevel).length > 0){
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Error"),
                "message": $A.get("$Label.c.CCM_SameCodeUsedError"),
                "type": "error",
                "duration": "dismissible"
            }).fire();
            component.set('v.isBusy',false)
        }else{
            // promotion code 是精确匹配，只会有一个 promotion code
            const uploadData = {
                customerid : component.get("v.customerId"),
                promotioncode: component.get("v.promotionCodeHeaderLevel"),
                isdropship: false,
                isportal: true,
                pricingdate:component.get('v.pricingDate')
            };
            var action = component.get('c.getWholePromotionOffering');
            action.setParams({
                uploadData: JSON.stringify(uploadData)
            });
            action.setCallback(this, function (response) {
                var state = response.getState();
                var result = response.getReturnValue();
                component.set('v.isBusy',false)
                if (state === 'SUCCESS') {
                    // 处理头折扣
                    let header_discount = []
                    let headerproductList = []
                    let promotioncode = result.data[0].promotionid
                    result.data.forEach((item,index) =>{
                        let minPriceProduct = {}
                        if(item.threshold[0].thresholdrecordtype){
                            header_discount.push(
                                {
                                    Different_Categories:      item.threshold[0].minimumdiffcate,                   // 当前threshold要求产品种类
                                    promotionCode:             item.promotioncode,                                  // 当前PromotionCode
                                    minQty:                    item.threshold[0].miniwholeorderquantityinclude,     // 整单最小数量
                                    maxQty:                    item.threshold[0].maxwholeorderquantityexclude,      // 整单最大数量
                                    minAmount:                 item.threshold[0].miniwholeorderamountinclude,       // 整单最小金额
                                    maxAmount:                 item.threshold[0].maxwholeorderamountexclude,        // 整单最小金额
                                })

                            let isFallshortofthreshold = true;
                            if(item.threshold[0].thresholdrecordtype == "Whole_Order_By_Quantity") {
                                let minQty = item.threshold[0].miniwholeorderquantityinclude;
                                let maxQty = item.threshold[0].maxwholeorderquantityexclude;
                                let totalQty = 0;
                                productList.forEach(item=>{
                                    totalQty = totalQty + Number(item.qty); 
                                });
                                if(totalQty >= minQty && totalQty < maxQty) {
                                    isFallshortofthreshold = false;
                                }
                            }
                            else if(item.threshold[0].thresholdrecordtype == "Whole_Order_By_Amount") {
                                let minAmount = item.threshold[0].miniwholeorderamountinclude;
                                let maxAmount = item.threshold[0].maxwholeorderamountexclude;
                                let totalAmount = 0;
                                productList.forEach(item => {
                                    totalAmount = totalAmount + (item.totalNetPrice || 0);
                                });
                                if(totalAmount >= minAmount && totalAmount < maxAmount) {
                                    isFallshortofthreshold = false;
                                }
                            }
                            else {
                                isFallshortofthreshold = '';
                            }
                            headerproductList = []
                            item.offering.forEach(v =>{
                                // 如果有折扣的情况,加上折扣
                                if(v.offeringrecordtype  == "Whole_Order_Discount_Off"){
                                    header_discount[index].discountoff = v.discountoff
                                // 如有改变付款方式,则在这里进行
                                }else if(v.offeringrecordtype  == "Whole_Order_Payment_Term"){
                                    header_discount[index].payment_type = v.paymentterm
                                // offering上有产品的情况
                                }else{
                                    v.oproducts.forEach( (value,item_index) =>{
                                        let _Max_Qty = v.Maximum_Quantity_Per_order__c || value.Max_Qty_Per_Order__c || 0
                                        // console.log(item.promotioncode,"整单下的产品的最大数量",_Max_Qty,v.Maximum_Quantity_Per_order__c,value.Max_Qty_Per_Order__c);
                                        let _unitNetPrice = v.Fixed_Price__c || value.Fixed_Price__c || (v.oproductsprice[item_index] ? v.oproductsprice[item_index].salesprice * ((value.Discount_Off__c || v.discountoff)/100 ? (1 - (value.Discount_Off__c || v.discountoff)/100) : 0 || 1) : 0);
                                        let _itemProduct = {
                                            "key": Date.now().toString(),
                                            'useHeaderDiscount': true,
                                            'type':'offering',
                                            "discounttype": "",
                                            "applicationMethod": v.oproductsprice[item_index] ? v.oproductsprice[item_index].applicationmethod : "",
                                            "standDiscount": v.oproductsprice[item_index] ? v.oproductsprice[item_index].standarddiscountvalue : "",
                                            "classify": "",
                                            'productDescription': value.Product__r.Name,
                                            'productId': value.Product__c,
                                            'model': value.Product__r.ProductCode,
                                            'requestDate': null,
                                            'qty': _Max_Qty || 1,
                                            'unitPerPallet': null,
                                            'inventory': null,
                                            'listPrice': v.oproductsprice[item_index] ? v.oproductsprice[item_index].listprice : 0,
                                            'discount': value.Discount_Off__c || v.discountoff || 0,
                                            'unitNetPrice': _unitNetPrice,
                                            "salesPrice" : v.oproductsprice[item_index] ? v.oproductsprice[item_index].salesprice : 0,
                                            'totalNetPrice': (_Max_Qty || 1) * _unitNetPrice,
                                            'remark': "",
                                            'promotioncode': item.promotioncode,
                                            'isPromotion': true,
                                            "UsePromotion": true,
                                            'select':false,
                                            "Min_Qty": "", 
                                            "Max_Qty": _Max_Qty, 
                                            "Min_Amount": "",
                                            "Min_Total_Amount": "", 
                                            "showPromotionType": "",
                                            "Fallshortofthreshold": isFallshortofthreshold,
                                            "ruleIndex": index
                                        };
                                        headerproductList.push(_itemProduct);
                                    })

                                    if(v.offeringrecordtype  == "Discount_Off_Cheapest_Among_Products") {
                                        let minprice = v.oproducts[0];
                                        minprice.salesPrice = v.oproductsprice[0].salesprice;
                                        minPriceProduct.id = minprice.Product__c;
                                        minPriceProduct.maxqty = v.Maximum_Quantity_Per_order__c;
                                        minPriceProduct.discountoff = v.discountoff;
                                        minPriceProduct.ruleindex = index;
                                        minPriceProduct.useHeaderDiscount = true;

                                        if(headerproductList.filter(pi => pi.ruleindex == index).length > 1){
                                            headerproductList.forEach(p => {
                                                if(Number(p.salesPrice) < Number(minprice.salesPrice) && p.ruleindex == index){
                                                    minPriceProduct.id = p.productId;
                                                }
                                            })
                                        }else{
                                            minPriceProduct.id = v.oproducts[0].Product__c;
                                        }

                                        headerproductList = headerproductList.filter(item1 => (item1.type == "offering" && item1.productId == minPriceProduct.id));
                                        headerproductList.forEach(item => item['discounttype'] = 'New Price');
                                        header_discount[index].products = headerproductList;
                                    }
                                    else {
                                        header_discount[index].products = headerproductList;
                                    }
                                }
                            })
                        }
                    })
                    component.set('v.woPromotionCode', promotioncode)
                    let woheaderdiscount = component.get('v.woheaderdiscount')
                    woheaderdiscount.push(header_discount)
                    component.set('v.woheaderdiscount', woheaderdiscount)
                    helper.getTotalNumber(component, component.get("v.productList"));
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": response.getError()[0].message,
                        "type": "error"
                    }).fire();
                }
            });
            $A.enqueueAction(action);
        }
    },
    // 移除整单折扣
    RemoveHeaderDiscount:function(component, event, helper){
        // 移除整单折扣 && Payment Term && Promotion下面的产品
        component.set('v.headerDiscountList',component.get('v.headerDiscountList').filter(item => item.promotionCode != event.target.name))
        component.set('v.woheaderdiscount',component.get('v.woheaderdiscount').filter(item => item[0].promotionCode != event.target.name))
        component.set('v.paymentTerm',component.get('v.paymentTerm_copy'))
        component.set('v.productList',component.get('v.productList').filter(item =>item.promotioncode != event.target.name))
        helper.getTotalNumber(component,component.get('v.productList'))
    },
    checkPo: function (component, event, helper) {
        helper.checkPo(component);
    },
    // 替代品
    handleRelatedSubstitute: function(component, event, helper){
        let rowInfo = event.getSource().get('v.id');
        // 判断是否选择product
        if (!(rowInfo.productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SelectProductFirstWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        // helper.getRelatedList(component, rowInfo);
        // helper.getSubstituteList(component, rowInfo);
        component.set('v.showOnlySubstitute', false);
        helper.getRelatedSubstituteList(component, rowInfo, '');
    },
    // 关闭related Substitute弹框
    relatedSubstituteCancelEvent : function(component, event, helper){
        component.set('v.relatedSubstituteModalFlag', false);
    },
    // 点击 unit per Pallet
    clickUnitPerPallet : function(component, event, helper){
        const index = component.get('v.operationRow');
        let productList = component.get('v.productList');
        let productId = productList[index].productId;
        console.log(productId, '点击unit per Pallet productId===============');
        // 显示unit per Pallet弹框
        if (productId) {
            component.set('v.unitPerPalletModalFlag', true);
            helper.productInformationById(component, productId);
        }
    },
    // 关闭unit per Pallet弹框
    unitPerPalletCancelEvent : function(component, event, helper){
        component.set('v.unitPerPalletModalFlag', false);
    },
})