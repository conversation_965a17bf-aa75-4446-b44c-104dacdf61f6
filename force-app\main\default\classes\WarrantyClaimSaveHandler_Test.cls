/**
 * Honey
 * 测试类
 * 2023/11/15
 */
@IsTest
public without sharing class WarrantyClaimSaveHandler_Test {
    @IsTest
    public static void testsaveClaim(){
        Account acc = new Account();
        acc.Name = 'Test Company';
        acc.Country_All__c = 'AD-Andorra';
        acc.Postal_Code__c = '621044';
        acc.Consumer_Status__c = 'Active';
        acc.RecordTypeId = CCM_Constants.CommercialConsumer_RECORDTYPEID;
        acc.Consumer_Status__c = 'Waiting Customer Approval';

        insert acc;
        Product2 prod1 = new Product2();
        prod1.Name = 'BH1001';
        prod1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        prod1.ExternalId__c = 'BH1001';
        prod1.Order_Model__c = 'BH1001';
        prod1.Master_Product__c = 'BH1001';
        insert prod1;
        Pricebook2 testPricebook = new Pricebook2(Name = 'Test Pricebook');
        insert testPricebook;
        Modifier__c modifier1 = new Modifier__c(Name = 'Test');
        insert modifier1;
        Modifier__c modifier2 = new Modifier__c(Name = 'Test2');
        insert modifier2;
        Modifier_Entry__c modifierentry = new Modifier_Entry__c(Modifier_Header_Number__c = 'M123', ExternalID__c = 'M123', Modifier__c = modifier1.Id);
        insert modifierentry;
        Pricebook_Entry__c testPricebookEntry = new Pricebook_Entry__c(PriceBook__c = testPricebook.Id, Product__c = prod1.Id, End_Date__c = Date.today(), Start_Date__c = Date.today().addDays(-7), IsActive__c = true, UnitPrice__c = 100);
        insert testPricebookEntry;
        Pricebook_Entry__c testPricebookEntry2 = new Pricebook_Entry__c(PriceBook__c = testPricebook.Id, Product__c = prod1.Id, End_Date__c = Date.today(), Start_Date__c = Date.today().addDays(-7), UnitPrice__c = 200);
        insert testPricebookEntry2;
        Sales_Program__c testSalesProgram = new Sales_Program__c(List_Price_1__c = testPricebook.Id, List_Price_2__c = testPricebook.Id, List_Price_3__c = testPricebook.Id, Customer__c = acc.Id, Price_Book__c = testPricebook.Id, Modifier_1__c = modifier1.Id, Modifier_2__c = modifier2.Id, Order_Type__c = 'Sales Order - DSV');
        insert testSalesProgram;
        MasterProductPrice__c testMasterProductPrice = new MasterProductPrice__c(Account__c = acc.Id, Product__c = prod1.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today(), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = false, Modifier_Entry__c = modifierentry.Id,CurrencyIsoCode = 'EUR');
        insert testMasterProductPrice;

        LinkProductAndPrice__c testLinkProductAndPrice = new LinkProductAndPrice__c(MasterProductPrice__c = testMasterProductPrice.Id, value__c = 10, Final_Priority__c = 1, Application_Method__c = 'Percent', Modifier_Entry__c = modifierentry.Id,CurrencyIsoCode = 'EUR');
        insert testLinkProductAndPrice;
        WarrantyClaimSaveHandler.WarrantyClaimEntity  objClaim = new WarrantyClaimSaveHandler.WarrantyClaimEntity();
        Date testDate = Date.today();
        objClaim.claimStatus = 'draft';
        objClaim.paymentStatus = 'Issued';
        objClaim.emailAddress = '<EMAIL>';
        objClaim.dropOffDate = testDate;
        objClaim.repairDate = testDate;
        objClaim.brand = 'EGO';
        objClaim.modelNumber = 'BH1001';
        objClaim.serialNumber = 'BH1001';
        objClaim.userType = 'Residential';
        objClaim.productId = prod1.Id;
        objClaim.productName = 'BH1001';
        objClaim.consumerId = acc.Id;
        objClaim.CustomerId = acc.Id;
        objClaim.receiptLink = 'testlink';
        objClaim.placeOfPurchase = 'testPurchase';
        objClaim.purchaseDate = testDate;
        objClaim.expiredDate = testDate;
        objClaim.receiptName = 'testname';
        objClaim.receiptLost = false;
        List<WarrantyClaimSaveHandler.WarrantyClaimItemEntity > lstListEntity = new List<WarrantyClaimSaveHandler.WarrantyClaimItemEntity>();
        WarrantyClaimSaveHandler.WarrantyClaimItemEntity objEntity = new WarrantyClaimSaveHandler.WarrantyClaimItemEntity();
        objEntity.partNumber = 'E1201';
        objEntity.partId = prod1.Id;
        lstListEntity.add(objEntity);
        objClaim.partList = lstListEntity;
        String result = WarrantyClaimSaveHandler.saveClaim(Json.serialize(objClaim), null, new List<String>());
        //通过Id查询
        Map<String,String> mapresult = (Map<String,String>)JSON.deserialize(result, Map<String,String>.class);
        String claimId = mapresult.get('claimId');
        Warranty_Claim__c objClaimQuery = [
            SELECT ID,Claim_Status__c,Payment_Status__c,Email_Address__c,Drop_off_Date__c,Repair_Date__c,
            Brand__c,Model_Number__c,Serial_Number__c,User_Type__c,Product__c,Product_Name__c,Consumer__c,
            Warranty_Item__c,Receipt_Link__c,Place_Of_Purchase__c,Purchase_Date__c,Expired_Date__c,Receipt_Name__c,
            Receipt_Lost__c,Basic_Permission__c,Service_Option__c,Replacement_Option__c,Repair_Type__c,
            Description__c,Failure_Code__c,Bill_Address_Name__c,Bill_Address__c,Ship_Address_name__c,Ship_Address__c,
            Explanation__c,Dealer_Name__c,Distributor_Or_Dealer__c,CurrencyIsoCode,Project__c,Project_Code__c,
            Labor_Rate__c,Actual_Labor_Rate__c,Labor_Input_Time__c,Claim_Date__c
            FROM Warranty_Claim__c WHERE ID = : claimId
        ];
        List<Warranty_Claim_Item__c> result2 = WarrantyClaimSaveHandler.getClaimItemForSubmit(Json.serialize(objClaim), claimId,objClaimQuery);
        Map<String, String> result3 = WarrantyClaimSaveHandler.checkSubmit(Json.serialize(objClaim), claimId);
        String result4 = WarrantyClaimSaveHandler.submitClaim(Json.serialize(objClaim), claimId, new List<String>());
        String result5 = WarrantyClaimSaveHandler.queryClaimDetail( claimId);
        String result6 = WarrantyClaimSaveHandler.queryClaimInfoMation( acc.Id);
        String result7 = WarrantyClaimSaveHandler.queryClaimList( acc.Id,1,10,'BH1001','draft','1','1','1','1', '');
        String result9 = WarrantyClaimSaveHandler.queryClaimList( acc.Id,1,10,'BH1001','','','','','', '');
        String result8 = WarrantyClaimSaveHandler.deleteClaim( claimId);
        
        system.debug('result-->'+result);
    }
   
    public WarrantyClaimSaveHandler_Test() {

    }
}