/**
 * Created by gluo006 on 7/16/2019.
 */
({
    init:function(component){
        var self = this;
        // 初始化获取注册用户类型
        const urlParams = new URLSearchParams(window.location.search);
        let recordId = urlParams.get('id');
        let registrationType = urlParams.get('registrationType');
        let emailAddress = urlParams.get('0.accountEmail');
        component.set('v.commercialId', recordId);
        component.set('v.registrationType', registrationType);
        if (registrationType === 'residentialUser') {
            component.set('v.showResidentialInfo', true);
            component.set('v.showCommercialInfo', false);
        } else if (registrationType === 'commercialUser') {
            component.set('v.showResidentialInfo', false);
            component.set('v.showCommercialInfo', true);
            component.set('v.emailAddress', emailAddress);
        }
        // 获取用户信息
        if (recordId) {
            // 判断用户类型
            if (registrationType == 'residentialUser') {
                var action = component.get('c.getConsumerInfoById');
                action.setParams({
                    ConsumerId: recordId,
                })
                action.setCallback(this, function (response) {
                    var state = response.getState();
                    if (state === 'SUCCESS' ) {
                        var result = JSON.parse(response.getReturnValue());
                        component.set('v.emailAddress', result.ownerInformation.EmailAddress);
                        component.set('v.phone', result.ownerInformation.Phone);
                        component.set('v.firstName', result.ownerInformation.FirstName);
                        component.set('v.address', result.ownerInformation.Address);
                        component.set('v.lastName', result.ownerInformation.LastName);
                        component.set('v.street', result.ownerInformation.StreetNo);
                        component.set('v.country', {
                            Name: result.ownerInformation.Country,
                            Id: result.ownerInformation.Country,
                        });
                        component.set('v.city', result.ownerInformation.City);
                        component.set('v.postCode', result.ownerInformation.Postcode);
                        component.set('v.state', result.ownerInformation.State);
                    }else{
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": response.getError()[0].message,
                        "type": "error"
                    }).fire();
                    }
                });
                $A.enqueueAction(action);
            }
            var action = component.get('c.setupProductRegistration');
            action.setParams({
                'commercialId': recordId,
            })
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === 'SUCCESS' ) {
                    var result = JSON.parse(response.getReturnValue());
                    component.set('v.organizationName', result.Name);
                    component.set('v.customerId', result.CustomerId);
                    component.set('v.Company', result.comapnyName);
                    component.set('v.fleetManager', result.fleetManager);
                    component.set('v.fleetManagerEmail', result.fleetManagerEmail);
                }else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
                }
            });
            $A.enqueueAction(action);
        }
    },
    // email 校验
    getCustomerInfo: function(component){
        component.set('v.isBusy', true);
        const emailAddress = component.get('v.emailAddress');
        var action = component.get("c.checkEmail");
        action.setParams({
           email: emailAddress,
//           'phone': phone
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            if (state === "SUCCESS") {
                if (result.message === 'Waiting Customer Approval') {
                    // 必填设置
                    component.set('v.isDisabled', true);
                    // 是否待审批
                    component.set('v.isPending', true);
                    component.set('v.isNew', false);
                    component.set('v.isActive', false);
                    component.set('v.isInactive', false);
                }else if (result.message === 'No Data'){
                    component.set('v.isNew', true);
                    component.set('v.isDisabled', false);
                    component.set('v.isInactive', false);
                    component.set('v.isPending', false);
                    component.set('v.isActive', true);
                } else {
                    if(result.message === 'DealerView'){
                        component.set('v.isActive', true);
                        component.set('v.isInactive', false);
                    }else if(result.message === 'NoPermission'){
                        component.set('v.isActive', false);
                        component.set('v.isInactive', true);
                    }
                    component.set('v.isDisabled', true);
                    component.set('v.isPending', false);
                    component.set('v.isNew', false);
                    const res = JSON.parse(result.data);
                    // 带入数据
                    component.set('v.firstName', res.FirstName);
                    component.set('v.lastName', res.LastName);
                    component.set('v.country', res.ShippingCountry);
                    component.set('v.postCode', res.ShippingPostalCode);
                    component.set('v.phone', res.Phone);
                    component.set('v.address', res.Address_Detail__c);
                    component.set('v.street', res.ShippingStreet);
                    component.set('v.city', res.ShippingCity);
                    component.set('v.state', res.ShippingState);
                    // component.set('v.firstName', res.FirstName);
                    // component.set('v.lastName', res.LastName);
                    // component.set('v.country', res.Country__c);
                    // component.set('v.postCode', res.Postal_Code__c);
                    // component.set('v.phone', res.Phone);
                    // component.set('v.address', res.Address_Detail__c);
                    // component.set('v.street', res.Street__c);
                    // component.set('v.city', res.City__c);
                    // component.set('v.state', res.State__c);
                } 
                component.set('v.isBusy', false);
            } else {
                component.set('v.isBusy', false);
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getAddressByZipCode: function(component){
        var zipCode = component.get('v.zipPostalCode');
        var country = component.get('v.country');
        var action = component.get('c.getAddressByCode');
        action.setParams({
            'postalCode': zipCode,
            'country': country
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            if (state === 'SUCCESS' ) {
                result = result.split(',');
                var newCity = result[0];
                var newState = result[1];
                var newCountry = result[2];
                component.set('v.city', newCity);
                component.set('v.state', newState);
                component.set('v.country', newCountry);
            }else{
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    changeMasterProduct:function(component, productId){
        var self = this;
        var action = component.get('c.changeMasterProduct');
        action.setParams({
            'productId': productId,//'01t0h000004XO1d',
            'warrantyId': component.get('v.recordId')
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS' ) {
                if(result.productCode){
                    if(result.proList){
                        var len = result.proList.length;
                        for(var i=0; i<len; i++){
                            result.proList[i].isRequired = true;
                            if (result.proList[i].surveyId) {
                                component.set('v.surveyId', result.proList[i].surveyId);
                                component.set('v.surveyTitle', result.proList[i].surveyTitle);
                                component.set('v.surveyComments', result.proList[i].surveyComments);
                            }
                        }
                        
                        //add to productListData
                        let productListData = component.get('v.productListData');
                        //去重
                        let addProductFlag = true;
                        if (productListData.length > 0) {
                            for (var i = 0; i < productListData.length; i++) {
                                if (productListData[i].productCode == result.productCode) {
                                    addProductFlag = false;
                                    break;
                                }
                            }
                        }
                        if (addProductFlag) {
                            productListData.push(result); 
                        }
                        console.log('productListData',productListData);
                        console.log('test',result);
                        component.set('v.productListData', productListData);
                        console.log('test',result.proList);
                    }
                }else{
                    if(result.Message.indexOf('Product Id can not be null') < 0){
                        self.showToast('Failed', result.Message);
                    }
                    //component.set('v.productListData', '');
                }
            }
            self.hideEle(component, 'proListSpinner');
        });
        $A.enqueueAction(action);
    },
    checkSNAndUpdateIndicator:function(component){
        var self = this;
        var brand = component.get('v.brandName');
        var action = component.get('c.checkSNAndUpdateIndicator');
        action.setParams({
            'warrantyBrandName': brand,
            'proListStr': JSON.stringify(component.get('v.productListData')),
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result;
            if(response.getReturnValue().indexOf('proList') < 0){
                result = response.getReturnValue();
            }else{
                result = JSON.parse(response.getReturnValue());
            }
            if (state === 'SUCCESS' ) {
                if(result.proList){
                    component.set('v.productListData', result);
                    self.hideEle(component, 'proListSpinner');
                }else{
                    self.showToast('Failed', result);
                    return;
                }
                self.hideEle(component, 'proListSpinner');
            }else{
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
            self.hideEle(component, 'proListSpinner');
        });
        $A.enqueueAction(action);
    },
    deleteReceipt: function(component){
        var action = component.get('c.deleteFile');
        action.setParams({
            'fileId': component.get('v.contentId'),
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS' ) {
                if(result.Status == 'Success'){
                    component.set('v.uploadFinished', false);
                    $A.get("e.force:showToast").setParams({
                     "title": $A.get("$Label.c.CCM_Success"),
                     "message": $A.get("$Label.c.CCM_DeleteSuccess"),
                     "type": "Success"
                    }).fire();
                }else{
                    $A.get("e.force:showToast").setParams({
                     "title": $A.get("$Label.c.CCM_Error"),
                     "message": result.Message,
                     "type": "Error"
                    }).fire();
                }
            }else{
                component.set('v.uploadFinished', false);
                $A.get("e.force:showToast").setParams({
                 "title": $A.get("$Label.c.CCM_Error"),
                 "message": response.getError()[0].message,
                 "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    onSaveRecord: function(component){
        var self = this;
        var registrationInfo = {
            FirstName: component.get('v.firstName'),
            LastName: component.get('v.lastName'),
            PersonEmail: component.get('v.emailAddress'),
            ShippingStreet: component.get('v.addressLine1'),
            Phone: component.get('v.phone'),
            ShippingPostalCode: component.get('v.zipPostalCode'),
            ShippingCity: component.get('v.city'),
            ShippingState: component.get('v.state'),
            purchaseDate: component.find('purchaseDate').get('v.value'),
            brand: component.get('v.brandName'),
            masterProduct: component.get('v.masterProductObj').Id,
            purchasePlace: component.get('v.purchasePlaceObj').Name,
            purchaseUseType: component.find('purchaseUseType').get('v.value'),
//            receiveInfoValue: component.get('v.receiveInfoValue'),
//            visitWebsiteValue: component.get('v.visitWebsiteValue'),
            proListStr: JSON.stringify(component.get('v.productListData')),
            lostReceipt: component.get('v.lostReceipt'),
            ContentId: component.get('v.contentId')
        };
        registrationInfo = JSON.stringify(registrationInfo);
        var action = component.get('c.SaveWarranty');
        action.setParams({
            'proListStr': registrationInfo,
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result;
            result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS' ) {
                if(result && result.message){
                    self.showToast('Error', result.message);
                    component.set('v.firstSave', true);
                    self.hideEle(component, 'spinner');
                }else{
                    self.showToast('Success', $A.get("$Label.c.CCM_SubmitSuccess"));
                    self.hideEle(component, 'spinner');
                    if (component.get('v.surveyId') == null || component.get('v.surveyId') == '') {
                        window.location.href = '/s/servicehome'
                    } else{
                        if (component.find('purchaseUseType').get('v.value') === 'Residential' ) {
                            component.set('v.isShowSurveyConfirmation', true);
                            component.set('v.warrantyId', result.warrantyId);
                        } else {
                            window.location.href = '/s/servicehome';
                        }
                    }
                }
            }else{
                component.set('v.firstSave', true);
                self.hideEle(component, 'spinner');
                $A.get("e.force:showToast").setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "Error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    showToast: function(status, message){
         var toastEvent = $A.get('e.force:showToast');
         if(status == 'Success'){
              toastEvent.setParams({
                  'title': $A.get("$Label.c.CCM_Success"),
                  'message': message,
                  'type':'success',
              });
         }else if(status == 'Failed'){
              toastEvent.setParams({
                  'title': $A.get("$Label.c.CCM_Error"),
                  'message': message,
                  'type':'error',
              });
         }
         toastEvent.fire();
    },
    showEle:function(component, ele){
        $A.util.removeClass(
              component.find(ele),
              "slds-hide"
        );
    },
    hideEle:function(component, ele){
        $A.util.addClass(
              component.find(ele),
              "slds-hide"
        );
    },

    /**
     * @date 2022/03/13
     * @description call apex class to save survey result
     */
     onSaveSurveyRecord: function (component) {
        let self = this,
            idCustomer = component.get("v.customerId"),
            idSurvey = component.get("v.surveyId"),
            surveyData = component.get("v.surveyData"),
            idWarranty = component.get("v.warrantyId"),
            lstResponse = [];
        const QUESTION_TYPE = {
            FREE_TEXT: "Free Text",
            MULTI_SELECT: "Multi Select",
            SINGLE_SELECT: "Single Select"
        };
        (surveyData || []).forEach((q) => {
            let { Id: questionId, Type__c, Chervon_Survey_Question_Choice__r } = q,
                localResponseList = [],
                localMultiResponse = {};
            (Chervon_Survey_Question_Choice__r || [])
                .filter((c) => c.checked === true || c.checked === undefined)
                .forEach((c) => {
                    let { Id: choiceId, checked, answerText } = c,
                        response = Object.assign({}, { questionId: questionId, result: {} });
                    if (Type__c === QUESTION_TYPE.FREE_TEXT || Type__c === QUESTION_TYPE.SINGLE_SELECT) {
                        response.result = {
                            answer: choiceId,
                            haveManualText: $A.util.isEmpty(answerText) === false,
                            manualSpecialChoice: checked === true && $A.util.isEmpty(answerText) === false ? choiceId : null,
                            manualText: $A.util.isEmpty(answerText) ? null : answerText
                        };
                        localResponseList.push(response);
                    } else if (Type__c === QUESTION_TYPE.MULTI_SELECT && checked === true) {
                        let { result } = localMultiResponse,
                            { answer } = result || {};
                        result = $A.util.isEmpty(result) ? {} : result;
                        result.answer = $A.util.isEmpty(answer) ? choiceId : answer + "," + choiceId;
                        if ($A.util.isEmpty(answerText) === false) {
                            Object.assign(result, {
                                haveManualText: true,
                                manualSpecialChoice: choiceId,
                                manualText: answerText
                            });
                        }
                        Object.assign(localMultiResponse, { questionId, result });
                    }
                });
            if (localMultiResponse && Object.keys(localMultiResponse).length > 0) {
                lstResponse = [...lstResponse, localMultiResponse];
            }
            if (localResponseList && localResponseList.length > 0) {
                lstResponse = [...lstResponse, ...localResponseList];
            }
        });
        let action = component.get("c.saveSurvey");
        action.setParams({
            idSurvey,
            idCustomer,
            idWarranty,
            strResponseJSON: JSON.stringify(lstResponse)
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                window.location.href = '/s/servicehome';
            } else {
                self.showToast("Failed", response.getError()[0].message);
            }
        });
        $A.enqueueAction(action);
    },

    // PostCode & Country event
    postCodeAndCountryCheck: function (component) {
        const action = component.get('c.getAddressByCode');
        action.setParams({
            postalCode: '60437',
            country: 'Germany'
            // postalCode: component.get('v.country'),
            // country: component.get('v.zipPostalCode')
        });
        action.setCallback(this, function (response) {
            const state = response.getState();
            if (state === 'SUCCESS') {
                const resValue = JSON.parse(response.getReturnValue());
                // 当地址信息为空时，带入返回地址信息
                if (!component.get('v.addressLine1')) {
                    console.log('地址信息为空======');
                    component.set('v.addressLine1', resValue.address);
                }
                if (!component.get('v.street')) {
                    component.set('v.street', resValue.street);
                }
                if (!component.get('v.city')) {
                    component.set('v.city', resValue.city);
                }
                if (!component.get('v.state')) {
                    component.set('v.state', resValue.provinceOrState);
                }
            }
        });
        $A.enqueueAction(action);
    },

    // 获取表格数据
    getTableDataByApex: function (component) {
        component.set('v.isBusy', true);
        const self = this;
        console.log('获取表格数据==========');
        const action = component.get("c.addWarrantyItem");
        action.setParams({
            email: component.get('v.emailAddress'),
            purchaseDate: component.get('v.purchaseDate'),
            branName: component.get('v.brandName'),
            masterModelNo: component.get('v.masterModelnumber.Name'),
            masterModelId: component.get('v.masterModelnumber.Id'),
            purchasePlace: component.get('v.purchasePlace'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, '获取表格数据 ============');
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, '获取表格数据 res=======');
                const arr = [];
                if (res.length) {
                    res.forEach((item)=>{
                        arr.push(
                            {
                                purchaseDate: item.purchaseDate,
                                brand: item.branName,
                                purchasePlace: item.purchasePlace,
                                masterModelNumber: item.masterModelNo,
                                masterModelId: item.masterModelId,
                                masterModelCode: item.masterModelCode,
                                modelNo: item.modelNo,
                                modelId: item.modelId,
                                modelCode: item.modelCode,
                                serialNumber: item.serialNo,
                                verifyResult: item.verifyResult,
                                verifyMessages: item.verifyMessages,
                                snCheck: '',
                                isEdit: false,
                                receiptName: null,
                                receipt: null,
                            }
                        )
                    })
                    // 判断是否存在发票
                    const tableArr = JSON.parse(JSON.stringify(component.get('v.tableData')));
                    const receiptUrl = component.get('v.receiptUrl');
                    if (receiptUrl && receiptUrl.fileUrl) {
                        arr.forEach((arrItem)=>{
                            arrItem.receipt = receiptUrl.fileUrl;
                            arrItem.receiptName = receiptUrl.fileName;
                        });
                        tableArr.push(...arr);
                        component.set('v.tableData', tableArr);
                        console.log(component.get('v.tableData'), '上传附件url==========');
                        self.setTableDataKey(component);
                    } else {
                        tableArr.push(...arr);
                        component.set('v.tableData', tableArr);
                        self.setTableDataKey(component);
                    }
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Warning"),
                        "message": res,
                        "type": "Warning"
                    }).fire();
                };
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    }, 
    // 获取当前公司信息
    getRecordIdInfo : function (component) {
        
    },
    // 保存表格SN编辑
    saveEdition : function (component, draftValues) {
        const self = this;
        const dataArr = component.get('v.tableData');
        // 检验sn
        if (draftValues[0].serialNumber.length != 15) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_EnterCorrectSNWarning"),
                "type": "Warning"
            }).fire();
        } else {
            // 校验SN是否和list中的sn重复
            let isDuplicate = false;
            draftValues.forEach((draftItem)=>{
                dataArr.forEach((item, index)=>{
                    if (draftItem.serialNumber == item.serialNumber && draftItem.id != item.id) {
                        isDuplicate = true;
                    }
                });
            });
            if (isDuplicate) {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Warning"),
                    "message": $A.get("$Label.c.CCM_RepeatedSNWarning"),
                    "type": "Warning"
                }).fire();
            } else {
                draftValues.forEach((draftItem)=>{
                    dataArr.forEach((item, index)=>{
                        if (item.id === draftItem.id) {
                            // item.serialNumber = draftItem.serialNumber;
                            console.log(draftItem.serialNumber, item.modelCode, 'SN校验================');
                            self.SNModelCheck(component, draftItem.serialNumber, item.modelNo, item.id);
                        }
                    });
                });
            }
            
        } 
    },
    // 表格赋值
    setTableDataKey : function (component) {
        let self = this;
        const dataArr = component.get('v.tableData');
        dataArr.forEach((item, index)=>{
            item.id = `row-${index}`;
            if (item.receipt && item.snCheck === 'invalid') {
                item.checkout = 'Serial Number';
                item.viewStyleCss = 'table-error';
            } else if (item.receipt && item.snCheck === 'valid') {
                item.checkout = 'SUCCESS';
                item.viewStyleCss = 'table-success';
            } else if (item.receipt && !item.serialNumber) {
                item.checkout = 'Serial Number';
                item.viewStyleCss = 'table-error';
            } else if (!item.receipt && item.snCheck === 'invalid') {
                item.checkout = 'Receipt & Serial Number';
                item.viewStyleCss = 'table-error';
            } else if (!item.receipt && item.snCheck === 'valid') {
                item.checkout = 'Receipt';
                item.viewStyleCss = 'table-error';
            } else if (!item.receipt && !item.serialNumber) {
                item.checkout = 'Receipt & Serial Number';
                item.viewStyleCss = 'table-error';
            }
        });
        component.set('v.tableData', dataArr);
        console.log(JSON.parse(JSON.stringify(component.get('v.tableData'))), '表格赋值==========');
        this.quitTableEdit(component);
    },

    // submitEvent
    submitEvent : function (component) {
        const self = this;
        const tableArr = JSON.parse(JSON.stringify(component.get('v.tableData')));
        console.log(tableArr, '获取表格数据1==========');
        const submitArr = [];
        tableArr.forEach((tableItem)=>{
            submitArr.push(
                {
                    masterModelNo: tableItem.masterModelNumber,
                    masterModelId: tableItem.masterModelId,
                    modelNo: tableItem.modelNo,
                    modelId: tableItem.modelId,
                    purchaseDate: tableItem.purchaseDate,
                    purchasePlace: tableItem.purchasePlace,
                    branName: tableItem.brand,
                    serialNo: tableItem.serialNumber,
                    receipt: tableItem.receipt,
                    receiptName: tableItem.receiptName,
                }
            )
        })
        const countryValue = component.get('v.country');
        const params = {
            commercialAccountId: component.get('v.commercialId'),
            registrationType: component.get('v.registrationType'),
            personEmail: component.get('v.emailAddress'),
            firstName: component.get('v.firstName'),
            lastName: component.get('v.lastName'),
            shippingCountry: countryValue.Id,
            shippingPostalCode: component.get('v.postCode'),
            phone: component.get('v.phone'),
            address: component.get('v.address'),
            isMassUpload: false,
            shippingStreet: component.get('v.street'),
            shippingCity: component.get('v.city'),
            shippingState: component.get('v.state'),
            productList: submitArr
        };
        const action = component.get("c.SaveWarranty");
        console.log("入参1=============",JSON.stringify(params));
        console.log("入参2=============",params);
        action.setParams({
            proListStr: JSON.stringify(params)
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            const res = JSON.parse(response.getReturnValue());
            if(state != 'ERROR'){
                if (res.Status == 'Success') {
                    console.log('Save Warranty Successful');
                    component.set('v.uploadFinished', false);
                    $A.get("e.force:showToast").setParams({
                        "title": $A.get("$Label.c.CCM_Success"),
                        "message": $A.get("$Label.c.CCM_SaveWarrantySuccess"),
                        "type": "Success"
                    }).fire();
                    var registrationType = component.get('v.registrationType');
                    if (registrationType == 'residentialUser') {
                        let url = '/s/user-history-detail?recordId=' + res.AccountID + '&userType=Residential&actionType=view';
                        window.open(url,'_self'); 
                    } else {
                        let url = '/s/user-history-detail?recordId=' + res.AccountID + '&userType=Commercial&actionType=view';
                        window.open(url,'_self'); 
                    }
                }else{
                    $A.get("e.force:showToast").setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": res.Message,
                        "type": "error"
                    }).fire();
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 必填校验
    getValidation : function (component, type) {
        if (type === 'form') {
            let valid1 = this.getElementRequiredError(component, 'emailAddress');
            let valid2 = this.getElementRequiredError(component, 'firstName');
            let valid3 = this.getElementRequiredError(component, 'lastName');
            let valid4 = this.getElementRequiredError(component, 'postCode');
            let valid5 = this.getElementRequiredError(component, 'street');
            let valid6 = this.getElementRequiredError(component, 'city');
            let valid7 = this.getElementRequiredError(component, 'state');
            return (valid1 && valid2 && valid3 && valid4 && valid5 && valid6 && valid7);
        } else {
            let valid1 = this.getElementRequiredError(component, 'purchaseDate');
            let valid2 = this.getElementRequiredError(component, 'purchasePlace');
            return (valid1 && valid2);
        }
    },

    // 校验错误提示信息
    getElementRequiredError : function (component, ele) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = element.get('v.value');
        var valid = !!val;
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }

        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },

    // 上传附件转base64
    fileByBase64 : function(id, file, component) {
        const self = this;
        const reader = new FileReader();
        // 传入一个参数对象即可得到基于该参数对象的文本内容
        reader.readAsDataURL(file[0]);
        reader.onload = function (e) {
            // target.result 该属性表示目标对象的DataURL
            let fileStr = e.target.result;
            let index = fileStr.indexOf(',') + 1;
            let fileValue = fileStr.substring(index);
            const tableArr = JSON.parse(JSON.stringify(component.get('v.tableData')));
            tableArr.forEach((item)=>{
                if (item.id === id) {
                    item.receiptBase64 = fileValue;
                }
            })
            component.set('v.tableData', tableArr);
            self.setTableDataKey(component);
        };
    },

    // 获取附件Id
    getFileId : function(id, name, fileBase64, component) {
        const action = component.get("c.uploadFile");
        action.setParams({
            fileName: name,
            content: fileBase64
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                const tableData = JSON.parse(JSON.stringify(component.get('v.tableData')));
                tableData.forEach((item)=>{
                    if (item.id === id) {
                        item.receiptId = res.ContentId;
                    }
                })
                component.set('v.tableData', tableData);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },

    // SN model 校验
    SNModelCheck : function (component, sn, modelNo, id) {
        const self = this;
        const action = component.get("c.snVerify");
        action.setParams({
            modelNo,
            sn,
            purchaseDate: component.get('v.purchaseDate'),
            orgType: 'Portal',
            byPass: false
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            const res = JSON.parse(response.getReturnValue());
            if (state === "SUCCESS") {
                const tableData = JSON.parse(JSON.stringify(component.get('v.tableData')));
                if (res.isSuccess == 'false') {
                    
                    // tableData.forEach((item)=>{
                    //     if (item.id === id) {
                    //         item.snCheck = 'invalid';
                    //     }
                    // })
                    let errList = JSON.parse(res.errList);
                    let info = errList.join('');
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Warning"),
                        "message": info,
                        "type": "Warning"
                    }).fire();
                } else {
                    tableData.forEach((item)=>{
                        if (item.id === id) {
                            item.serialNumber = sn;
                            item.snCheck = 'valid';
                        }
                    })
                    component.set('v.draftValues', []);
                    component.set('v.tableData', tableData);
                    self.setTableDataKey(component);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    
    // mass upload校验
    checkMassUploadData : function (component, data) {
        const tableDate = component.get('v.tableData');
        component.set('v.isBusy', true);
        const self = this;
        const action = component.get("c.massUploadVerification");
        action.setParams({
            proListStr: JSON.stringify(data)
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            const res = JSON.parse(response.getReturnValue());
            // calvin start 2023/06/05
            let Allres = [...res.productList].every(item=>{
                return item.verifyResult == true
            })
            console.log("Allres===",Allres);
            if (state === "SUCCESS") {
                let newdata = [...res.productList];
                if(Allres){
                    newdata.forEach(item=>{
                        tableDate.push({
                            brand:"EGO",
                            checkout:item.checkout,
                            id:item.id,
                            // masterModelId:item.modelId,
                            // masterModelNumber:item.modelNo,
                            masterModelId:item.masterModelId,
                            masterModelNumber:item.masterModelNo,
                            modelId:item.modelId,
                            modelNo:item.modelNo,
                            purchaseDate:item.purchaseDate,
                            purchasePlace:item.purchasePlace,
                            serialNumber:item.serialNo,
                            snCheck:"valid",
                            verifyMessages:null,
                            verifyResult:null,
                            viewStyleCss:"table-error",
                            isEdit: false,
                            receiptName: null,
                            receipt: null,
                        })
                    });
                    component.set('v.errortip',false);
                    component.set('v.tableData',tableDate);
                    self.setTableDataKey(component);
                }else{
                    let errorList = [];
                    let errorFormList = res.ErrorFormList;
                    let valueList = Object.values(errorFormList);
                    valueList.forEach((item)=>{
                        if (item) {
                            errorList.push(item);
                        }
                    })
                    component.set('v.errortip',true);
                    component.set('v.errorList',errorList);
                }
                // end
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 日期格式处理
    changeDateType : function (date){
        const dateObj = new Date(date);
        const year = dateObj.getFullYear().toString();
        const month = (dateObj.getMonth() + 1).toString().length < 2 ? '0' + (dateObj.getMonth() + 1) : dateObj.getMonth() + 1;
        const day = dateObj.getDate().toString().length < 2 ? '0' + dateObj.getDate() : dateObj.getDate();
        return `${year}-${month}-${day}`;
    },    
    // calvin start 2023/06/03
    // 校验Model Number
    warrantyMasterProduct:function(component,ModelNumber){
        const self = this;
        var action = component.get("c.masterModelNumber");
        action.setParams({
        //    brandName: component.get('v.brandName'),
           filterStr: ModelNumber
        });
        action.setCallback(this, function (response) {
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               if(result){
                if(!JSON.parse(result)[0]){ // 校验Model Number是否存在
                    self.showToast('Failed', $A.get("$Label.c.CCM_IncorrectModelNumberError"));
                    // console.log("5555"); 校验SN的2-5位数是否匹配
                }
               }
           } else {
            console.log(response, 'warrantyMasterProduct===============');

               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },

    //yanko add 09-11
    saveWarrantyPDF : function(component){
        const tableArr = JSON.parse(JSON.stringify(component.get('v.tableData')));
        const submitArr = [];

        tableArr.forEach((tableItem)=>{
            submitArr.push(
                {
                    MasterProductNumber: tableItem.masterModelNumber,
                    ModelNumber: tableItem.modelNo,
                    PurchaseDate: tableItem.purchaseDate,
                    PurchasePlace: tableItem.purchasePlace,
                    Brand: tableItem.brand,
                    SerialNumber: tableItem.serialNumber
                }
            )
        })

        let url = window.location.origin + '/apex/ProductRegistrationPDF?mySet=' + JSON.stringify(submitArr);
        window.open(url);
        component.set('v.isBusy', false);
        return;
    },
    // end
    // 先获取附件插入ID
    getReceiptId : function (component, fileValue, fileName, fileType) {
        const self = this;
        const action = component.get("c.ReceiptJunkPreCreate");
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            let res = response.getReturnValue();
            console.log(res, '先获取附件插入ID--------------');
            if (state === "SUCCESS") {
                let receiptId = res;
                console.log(receiptId, 'receiptId-------------');
                self.getReceiptUrl(component, fileValue, fileName, fileType, receiptId);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
                component.set('v.isBusy', false);
            }
        });
        $A.enqueueAction(action);
    },
    // 发票base64
    fileByBase64ForReceipt : function(component, file, fileType) {
        const self = this;
        const reader = new FileReader();
        // 传入一个参数对象即可得到基于该参数对象的文本内容
        reader.readAsDataURL(file[0]);
        reader.onload = function (e) {
            // target.result 该属性表示目标对象的DataURL
            let fileStr = e.target.result;
            let index = fileStr.indexOf(',') + 1;
            let fileValue = fileStr.substring(index);
            // 先获取附件插入ID
            self.getReceiptId(component, file[0], file[0].name, fileType);
        };
    },
    // 获取发票Url
    getReceiptUrl : function (component, fileValue, fileName, fileType, receiptId) {
        const self = this;
        const action = component.get("c.UploadReceiptToAws");
        action.setParams({
            idStr: receiptId,
            receiptName: fileName,
            receiptType: fileType,
            receiptId: fileValue
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            let res = response.getReturnValue();
            if (state === "SUCCESS") {
                if (res.code == 200) {
                    component.set('v.receiptUrl', {
                        fileName: fileName,
                        fileUrl: res.message
                    });
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": res.message,
                        "type": "error"
                    }).fire();
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 退出表格编辑
    quitTableEdit : function (component){
        let tableData = component.get('v.tableData');
        tableData.forEach((item)=>{
            item.isEdit = false;
        })
        component.set('v.tableData', JSON.parse(JSON.stringify(tableData)));
    },   
    // 校验mass uplod sn是否重复
    checkMassUploadSNDuplicate : function (component, serialNo, snIndex){
        // 校验模板中的数据
        let tableData = component.get('v.tableData');
        let errorList = component.get('v.errorList');
        let isDuplicate = false;
        tableData.forEach((item)=>{
            if (item.serialNumber == serialNo) {
                isDuplicate = true;
            }
        });
        if (isDuplicate) {
            if (errorList[0]) {
                errorList[0] = errorList[0] + ', ' + (snIndex + 1);
            } else {
                errorList.push($A.get("$Label.c.CCM_DuplicateSNError") + (snIndex + 1));
            }
            // errorinfo.duplicateSerialNumberFromList = 'Duplicate Serial Number From List : Line'+ (snIndex + 1);
        }
        component.set('v.errorList', errorList);
    }, 
})