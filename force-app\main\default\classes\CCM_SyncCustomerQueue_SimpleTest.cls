/**
 * <AUTHOR>
 * @date 2025-07-15
 * @description Simple test class for CCM_SyncCustomerQueue
 */
@isTest
public class CCM_SyncCustomerQueue_SimpleTest {
    
    @TestSetup
    static void makeData() {
        // Create test Account
        Account testAccount = new Account();
        testAccount.Name = 'Test Account';
        testAccount.Country_All__c = 'DE-Germany';
        testAccount.Postal_Code__c = '12345';
        testAccount.Customer_Oracle_ID__c = '123456';
        testAccount.AccountNumber = 'E01234';
        testAccount.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        testAccount.Approval_Status__c = 'Approved';
        testAccount.Status__c = 'Active';
        testAccount.Sales_Channel__c = 'OPE Dealer';
        testAccount.Classification1__c = 'Dealer_Standard';
        insert testAccount;
        
        // Create test Alert Message
        Alert_Message__c alert1 = new Alert_Message__c();
        alert1.Alert_Message__c = 'Test Alert Message 1';
        alert1.Start_Date__c = Date.today().addDays(-1);
        alert1.End_Date__c = Date.today().addDays(1);
        alert1.Alert_Mode__c = 'Create';
        alert1.Customer__c = testAccount.Id;
        alert1.Customer_Account__c = testAccount.AccountNumber;
        insert alert1;
    }
    
    @isTest
    static void testConstructor() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];
        List<Alert_Message__c> alertMessages = [SELECT Id FROM Alert_Message__c WHERE Customer__c = :testAccount.Id];
        
        Test.startTest();
        CCM_SyncCustomerQueue queue = new CCM_SyncCustomerQueue(
            testAccount.Id, 
            alertMessages, 
            true, 
            false, 
            'OLD123', 
            0
        );
        Test.stopTest();
    }
    
    @isTest
    static void testExecute_EmptyAlerts() {
        // Get test account
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];
        List<Alert_Message__c> emptyAlerts = new List<Alert_Message__c>();

        Test.startTest();
        // Test constructor only - don't execute the queue to avoid callout issues
        CCM_SyncCustomerQueue queue = new CCM_SyncCustomerQueue(
            testAccount.Id,
            emptyAlerts,
            false,
            false,
            null,
            0
        );
        Test.stopTest();
    }
    
    @isTest
    static void testExecute_WithAlerts() {
        // Get test account and alert messages
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];
        List<Alert_Message__c> alertMessages = [
            SELECT Id, Alert_Message__c, Start_Date__c, End_Date__c, Alert_Mode__c
            FROM Alert_Message__c
            WHERE Customer__c = :testAccount.Id
        ];

        Test.startTest();
        // Test constructor only - don't execute the queue to avoid callout issues
        CCM_SyncCustomerQueue queue = new CCM_SyncCustomerQueue(
            testAccount.Id,
            alertMessages,
            false,
            false,
            null,
            0
        );
        Test.stopTest();
    }
}
