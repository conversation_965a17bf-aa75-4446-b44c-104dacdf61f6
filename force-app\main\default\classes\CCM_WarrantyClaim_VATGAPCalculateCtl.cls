/**
 * <AUTHOR>
 * @date 2025-06-24
 * @description Calculate Bench Mark, Total Warranty Efforts and VAT GAP for VAT Correction
 */
public without sharing class CCM_WarrantyClaim_VATGAPCalculateCtl {
    

    public static void calculateBenchMark(String warrantyClaimId, String serialNumber, String customerId, String productModel) {
        // Check invoice information on warranty item
        Boolean isInvoiceExistInWarranty = false;
        List<Warranty_Item__c> wiList = [SELECT Invoice_No__c, Related_Invoice__c
                                         FROM Warranty_Item__c
                                         WHERE Serial_Number__c = :serialNumber
                                         AND Related_Invoice__c != null
                                         ORDER BY CreatedDate DESC];
        if (!wiList.isEmpty() && wiList[0].Related_Invoice__c != null) {
            String invoiceId = wiList[0].Related_Invoice__c;
            Decimal price = 0;
            for(Invoice_Item__c item : [SELECT Price__c 
                                        FROM Invoice_Item__c WHERE Invoice__c = :invoiceId
                                        AND Product__r.Order_Model__c = :productModel]) {
                price = item.Price__c;
                isInvoiceExistInWarranty = true;
            }

            if(isInvoiceExistInWarranty) {
                Warranty_Claim__c claim = new Warranty_Claim__c();
                claim.Id = warrantyClaimId;
                claim.Bench_Mark__c = price;
                claim.Bench_Mark_Calculated__c = true;
                update claim;
            }
            
        }
        if(isInvoiceExistInWarranty) {
            return;
        }
        // Get invoice and customer information from logistic database
        CCM_SNRequestController.SNExportInfo snExportInfo = new CCM_SNRequestController.SNExportInfo();
        snExportInfo.requestValue = serialNumber;
        snExportInfo.requestId = 'WarrantyClaimQuery' + Datetime.now().format('yyyyMMddHHmmssSSS');
        snExportInfo.type = 'SN';
        snExportInfo.orgType = 'CRM';
        snExportInfo.usage = 'WarrantyClaimQuery';
        snExportInfo.productModel = productModel;
        snExportInfo.customerId = customerId;
        snExportInfo.warrantyClaimId = warrantyClaimId;
        CCM_SNRequestController.UpsertExportRecord(JSON.serialize(snExportInfo));
    }

    public static Boolean processWarrantyClaimQuery(List<CCM_RestService_DealSNInfo.ReqestObj> reqObjList) {
        Boolean findPriceInLogistic = false;
        Set<String> invoiceNoSet = new Set<String>();
        String snExportRequestId = reqObjList[0].UNION_BATCH;
        for(CCM_RestService_DealSNInfo.ReqestObj reqObj : reqObjList) { 
            if(String.isNotBlank(reqObj.INVOICE_NO)) {
                invoiceNoSet.add(reqObj.INVOICE_NO);
            }
        }

        String productModel = null;
        String warrantyClaimId = null;
        for(SN_Export__c snExport : [SELECT Product_Model__c, Warranty_Claim_Id__c FROM SN_Export__c WHERE Request_Id__c = :snExportRequestId]) {
            productModel = snExport.Product_Model__c;
            warrantyClaimId = snExport.Warranty_Claim_Id__c;
        }

        if(String.isNotBlank(productModel)) {
            Decimal price = null;
            for(Invoice_Item__c item : [SELECT Price__c, Product__r.Order_Model__c 
                                        FROM Invoice_Item__c 
                                        WHERE Invoice__r.Invoice_Number__c IN :invoiceNoSet
                                        AND Product__r.Order_Model__c = :productModel]) {
                price = item.Price__c;
            }

            if(price != null) {
                findPriceInLogistic = true;
                Warranty_Claim__c claim = new Warranty_Claim__c();
                claim.Id = warrantyClaimId;
                claim.Bench_Mark__c = price;
                claim.Bench_Mark_Calculated__c = true;
                update claim;
            }
        }
        return findPriceInLogistic;
    }

    public static void findPirceInSellableProductList(String snExportRequestId) {
        String productModel = null;
        String customerId = null;
        String warrantyClaimId = null;
        for(SN_Export__c snExport : [SELECT Product_Model__c, Customer__c, Warranty_Claim_Id__c FROM SN_Export__c WHERE Request_Id__c = :snExportRequestId]) {
            productModel = snExport.Product_Model__c;
            customerId = snExport.Customer__c;
            warrantyClaimId = snExport.Warranty_Claim_Id__c;
        }
        Account objAccount = [SELECT CurrencyIsoCode FROM Account WHERE Id = :customerId];
        Product2 product = [SELECT Id FROM Product2 WHERE Order_Model__c = :productModel];
        
        Map<String, Object> mapFeild2Price = CCM_GetProductInfoUtil.CaucaltePrice(objAccount.Id, product.Id, null, objAccount.CurrencyIsoCode);
        if(mapFeild2Price != null) {
            Decimal price = (Decimal)mapFeild2Price.get(CCM_Constants.FINAL_PRICE);
            Warranty_Claim__c claim = new Warranty_Claim__c();
            claim.Id = warrantyClaimId;
            claim.Bench_Mark__c = price;
            claim.Bench_Mark_Calculated__c = true;
            update claim;
        }
        else {
            Warranty_Claim__c claim = new Warranty_Claim__c();
            claim.Id = warrantyClaimId;
            claim.Bench_Mark__c = 0;
            claim.Bench_Mark_Calculated__c = true;
            update claim;
        }
    }

    public static Decimal calculateHistoricalWarrantyEfforts(String warrantyClaimId, String serialNumber, String serviceOption, String replacementOption) {
        Decimal totalHistoricalWarrantyEfforts = 0;
        for(Warranty_Claim__c historicalWarrantyClaim : [SELECT Total_Actual__c, VAT__c 
                                                         FROM Warranty_Claim__c WHERE Serial_Number__c = :serialNumber
                                                         AND Claim_Status__c = 'Approved'
                                                         AND (Service_Option__c = 'Repair' OR (Service_Option__c = 'Replacement' AND Replacement_Option__c = 'Credit Memo'))]) {
            totalHistoricalWarrantyEfforts += (historicalWarrantyClaim.Total_Actual__c - (historicalWarrantyClaim.VAT__c != null ? historicalWarrantyClaim.VAT__c : 0));
        }
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Id = warrantyClaimId;
        claim.Historical_Warranty_Efforts__c = totalHistoricalWarrantyEfforts;
        update claim;
        return totalHistoricalWarrantyEfforts;
    }

    public static Decimal calculateVATGAPForVATCorrection(Warranty_Claim__c claim) {
        Decimal currentVATGAP = 0;
        Decimal totalWarrantyEfforts = claim.Historical_Warranty_Efforts__c + (claim.Total_Actual__c - (claim.VAT__c != null ? claim.VAT__c : 0));
        Decimal benchMark = claim.Bench_Mark__c;
        if(totalWarrantyEfforts > benchMark) {
            currentVATGAP = claim.Total_Actual__c - (claim.VAT__c != null ? claim.VAT__c : 0);
            Decimal previousVATGAP = claim.Historical_Warranty_Efforts__c - benchMark;
            if(previousVATGAP < 0) {
                currentVATGAP = previousVATGAP + (claim.Total_Actual__c - (claim.VAT__c != null ? claim.VAT__c : 0));
            }
        }
        return ((currentVATGAP * 19) / 100).setScale(2, RoundingMode.HALF_UP);
    }
}