({
	doInit : function(component, event, helper) {
        // 获取当前时间
        const year = new Date().getFullYear().toString();
        const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
        const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
        console.log(`${year}-${month}-${day}`, '获取当前时间------------');
        component.set('v.toDay', `${year}-${month}-${day}`);
	    //init the brand  set EGO as default
        component.set("v.brandList", ['EGO']);
        //init the place of purchase when brand default as EGO
        component.set('v.purchasePlaceList', [{label: $A.get("$Label.c.CCM_OnlineDealer"), value: 'Online Dealers'}, {label: $A.get("$Label.c.CCM_StationaryStores"), value: 'Stationary Stores'}]);
        //init the purchase use type
        component.set('v.purchaseUseTypeList', ['--None--', 'Industrial/Professional/Commercial', 'Residential', 'Rental'])
        //get warranty brand and purchase place dependency relationship
		helper.init(component);
        // 初始化配置表格参数
        component.set('v.columns', [
            { label: $A.get("$Label.c.CCM_Action"), type: 'button-icon', initialWidth: 65, typeAttributes: { iconName: 'utility:add'}, name: 'Add' },
            { type: 'button-icon', initialWidth: 65, typeAttributes: { iconName: 'utility:dash'}, name: 'Delete' },
            { label: $A.get("$Label.c.CCM_PurchaseDate"), fieldName: 'purchaseDate', type: 'text' },
            { label: $A.get("$Label.c.CCM_Brand"), fieldName: 'brand', type: 'text' },
            { label: $A.get("$Label.c.CCM_PurchasePlace"), fieldName: 'purchasePlace', type: 'text' },
            { label: $A.get("$Label.c.CCM_KitModel"), fieldName: 'masterModelNumber', type: 'text' },
            { label: $A.get("$Label.c.CCM_Model"), fieldName: 'modelNo', type: 'text' },
            { label: $A.get("$Label.c.CCM_SerialNumber"), fieldName: 'serialNumber', initialWidth: 240, type: 'text', editable: true, typeAttributes: { required: true }},
            { label: $A.get("$Label.c.CCM_Receipt"), fieldName: 'receiptName', type: 'text' },
            { label: $A.get("$Label.c.CCM_Checkout"), fieldName: 'checkout', type: 'text', cellAttributes: { class: { fieldName: 'viewStyleCss' }}},
        ]);
        component.set('v.tableData', []);
	},
    getAddressInfo: function(component, event, helper){
        helper.getAddressByZipCode(component);
    },
    checkSN:function(component, event, helper){
        var serialNum = event.getSource().get('v.value');
        var index = event.target.getAttribute('data-index');
        if(!serialNum){
            return;
        }else{
            serialNum = serialNum.length;
        }
        var brand = component.get('v.brandName');
        var Purchase_Date__c = component.find('purchaseDate').get('v.value');
        if(!brand){
            helper.showToast('Failed', $A.get("$Label.c.CCM_BrandNameisrequired"));
            return;
        }
        if(!Purchase_Date__c){
            helper.showToast('Failed', $A.get("$Label.c.CCM_PurchaseDateisrequired"));
            return;
        }
        helper.showEle(component, 'proListSpinner');
        helper.checkSNAndUpdateIndicator(component);
    },
    handlePurchasePlace: function(component, event, helper){
      console.log('purchase place')
    },
    checkIsRequire: function(component, event, helper){
        var index = event.target.getAttribute('data-index');
        var productListData = component.get('v.productListData');
        var isSelect = productListData.proList[index].isSelect;
        productListData.proList[index].isRequired = isSelect;
        component.set('v.productListData', productListData);
    },
    handleChangeProItem: function (component, event, helper) {
        // This will contain the string of the "value" attribute of the selected option
        //var selectedOptionValue = event.getParam("value");
        //alert("Option selected with value: '" + selectedOptionValue + "'");
        var selectItemId = event.getSource().get('v.value');
        //console.log('selectItemId',selectItemId); 
        var selectIndex = event.getSource().get('v.name');
        //console.log('selectIndex',selectIndex); 
        var result = component.get('v.productListData');
        var replaceProMap = result.proList[selectIndex].replaceProMap;
        result.proList[selectIndex].warrantyItem = replaceProMap[selectItemId];
        //console.log('result.warrantyItem',JSON.stringify(result.proList[selectIndex].warrantyItem));
        component.set('v.productListData', result);
    },

    doSurvey: function(component, event, helper) {
        component.set('v.isShowSurveyConfirmation', false);
        component.set('v.isShowSurveyContent', true);
    },

    doCancelSurvey: function(component, event, helper) {
        window.location.href = '/s/servicehome'
    },

    handleSurveyData: function (component, event, helper) {
        component.set("v.surveyData", JSON.parse(event.getParam("surveyData")));
    },

    onClickSurveySave: function (component, event, helper) {
        var valid = true;
        let survey = component.find("survey");
        console.log("survey======",survey);
        if (survey) {
            valid = valid && survey.validate();
        }
        if (valid) {
            helper.showEle(component, 'spinner');
            helper.onSaveSurveyRecord(component);
        } else {
            //alert to show required fields
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Error"),
                "message": $A.get("$Label.c.CCM_SaveDataError"),
                "type": "error"
            }).fire();
            helper.hideEle(component, 'spinner');
        }
    },

    closeModal: function(component, event, helper) {
        window.location.href = '/s/servicehome'
    },
    delMasterProductByProductCode:function(component,event, helper){
        var productCode = event.getParam('productCode');
        // console.log('productCode',productCode);
        let productList = component.get("v.productListData");
        for (var i = 0; i < productList.length; i++) {
            if (productList[i].productCode == productCode) {
                productList.splice(i,1);
            } 
        }
        // console.log('productList',productList);
        component.set("v.productListData", productList);
    },
    // closeEmailModal : function(component,event,helper) {
    //     console.log('closeEmailModal=============');
    //     var filters = event.getParam('filters');
    //     console.log(filters);
    //     component.set("v.hiddenSectionInfo", false);
    // },

    // Country or PostCode Change
    getCountryOrPostCode: function(component,event,helper) {
        const countryStr = component.get('v.country');
        const zipPostalCodeStr = component.get('v.zipPostalCode');
        if (countryStr && zipPostalCodeStr) {
            helper.postCodeAndCountryCheck(component);
        }
    },

    // 页面跳转到Commercial User Registration 
    toUserRegistration: function(component, event, helper) {
        window.location.href = '/s/user-registration';
    },
    
    // 表格选中
    tableSelected: function(component, event, helper) {
        var selectList = event.getParam('selectedRows');
        component.set('v.selectList', selectList);
        console.log(JSON.stringify(selectList), 'selectList==========');
    },

    // 表格操作
    handleRowAction: function(component, event, helper) {
        // 先判断是否处于SN编辑状态
        let tableData = JSON.parse(JSON.stringify(component.get('v.tableData')));
        let isEdit = false;
        tableData.forEach((item)=>{
            if (item.isEdit) {
                isEdit = true;
            }
        })
        if (isEdit) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SaveSNWarning"),
                "type": "Warning"
            }).fire();
            component.set('v.resetFlag', true);
            return;
        }
        const action = event.getParam('action');
        const row = event.getParam('row');
        // 将SN置空
        const rowData = JSON.parse(JSON.stringify(row));
        rowData.serialNumber = '';
        // calvin start 2023/06/02
        // 重置收据信息以及SN信息
        rowData.receiptUrl = '';
        rowData.receiptName = '';
        rowData.snCheck = '';
        // end
        console.log(JSON.parse(JSON.stringify(action)), row, '表格操作===========');
        // TODO:表格操作
        switch (action.iconName) {
            case 'utility:add':
                const arr = component.get('v.tableData');
                const changeTableData = JSON.parse(JSON.stringify(arr));
                console.log("rowData.checkout=======",rowData);

                arr.forEach((item, index)=>{
                    if (item.id === rowData.id) {
                        // console.log("rowData=======",rowData);
                        changeTableData.splice(index + 1, 0, rowData);
                    }
                })
                console.log(changeTableData, 'changeTableData========');
                component.set('v.tableData', changeTableData);
                helper.setTableDataKey(component);
                break;
            case 'utility:dash':
                const delArr = component.get('v.tableData');
                const changeDelTableData = JSON.parse(JSON.stringify(delArr));
                delArr.forEach((item, index)=>{
                    if (item.id === rowData.id) {
                        changeDelTableData.splice(index, 1);
                    }
                })
                console.log(changeDelTableData, 'changeTableData========');
                component.set('v.tableData', changeDelTableData);
                helper.setTableDataKey(component);
                break;
            default:
                break;
        }
    },

    // 获取excel解析数据
    getParseData : function(component, event, helper) {
        component.set('v.resetFlag', false);
        // 先判断是否处于SN编辑状态
        let tableData = JSON.parse(JSON.stringify(component.get('v.tableData')));
        let isEdit = false;
        tableData.forEach((item)=>{
            if (item.isEdit) {
                isEdit = true;
            }
        })
        if (isEdit) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SaveSNFirstWarning"),
                "type": "Warning"
            }).fire();
            component.set('v.resetFlag', true);
            return;
        }
        let parseData = '';
        console.log('获取excel解析数据');
        const self = this;
        parseData = JSON.parse(JSON.stringify(event.getParam('parseData')));
        console.log(parseData, 'parseData===========');
        // 对上传数据做校验
        const emailAddress = component.get('v.emailAddress');
        if (!emailAddress) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SaveEmailWarning"),
                "type": "Warning"
            }).fire();
            component.set('v.resetFlag', true);
            return;
        };

        let newArr = [];
        newArr = JSON.parse(JSON.stringify(parseData));
        // 上传数据为空
        if (!newArr.length) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_FillInUploadDataWarning"),
                "type": "Warning"
            }).fire();
            component.set('v.resetFlag', true);
            return;
        };
        // 添加上传数据
        let changeTableData = [];
        newArr.forEach((item)=>{
            changeTableData.push(
                {
                    email: item['Email Address'],
                    purchaseDate: helper.changeDateType(item['Purchase Date\r\nYYYY-MM-DD']),
                    brand: '',
                    purchasePlace: item['Purchase Place\r\nPicklist value is \"Online Dealers\" or \"Stationary Stores\"'],
                    masterModelNumber: '',
                    masterModelId: '',
                    modelNo: item['Model Number'],
                    modelId: '',
                    serialNo: item['Serial Number'],
                    receiptId:'',
                    isEdit: false,
                    receiptName: null,
                    receipt: null,
                }
            );
        });
        const params = {
            personEmail: emailAddress,
            productList: changeTableData,
            orgType: 'Portal'
        };
        console.log(JSON.parse(JSON.stringify(params)), 'params===========');
        // calvin start 2023/06/03
        component.set('v.errorList', []);
        params.productList.forEach((item,index) =>{
            if(item && item.email != emailAddress){ // 校验邮箱是否一致
                helper.showToast('Failed', $A.get("$Label.c.CCM_CheckEmailWarning"));
                params.productList = [];
            } else if (item.serialNo) {
                console.log('checkMassUploadSNDuplicate-----------------');
                helper.checkMassUploadSNDuplicate(component,item.serialNo, index);
            } else if(item.modelNo){                                                                               // 校验模板中的Model Number是否存在
                helper.warrantyMasterProduct(component,item.modelNo);
            }
        })

        let errorList = component.get('v.errorList');
        if (errorList.length) {
            component.set('v.errortip', true);
        } else{
            // 对上传数据校验
            console.log("params",JSON.parse(JSON.stringify(params)));
            helper.checkMassUploadData(component, params);
        }
        component.set('v.resetFlag', true);
    },

    // 获取表格数据
    getTableData: function(component, event, helper) {
        // 先判断是否处于SN编辑状态
        let tableData = JSON.parse(JSON.stringify(component.get('v.tableData')));
        let isEdit = false;
        tableData.forEach((item)=>{
            if (item.isEdit) {
                isEdit = true;
            }
        })
        if (isEdit) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SaveSNWarning"),
                "type": "Warning"
            }).fire();
            component.set('v.resetFlag', true);
            return;
        }
        // 校验必填项
        const isValid = helper.getValidation(component, 'table');
        let masterProductValid = false;
        const masterProductElement = component.find('masterProduct');
        const masterProductRequiredText = component.find('masterProduct-error-required');
        // masterProduct校验
        const masterProductValue = JSON.parse(JSON.stringify(component.get('v.masterModelnumber')));
        if (masterProductValue.Id) {
            masterProductValid = true;
            $A.util.removeClass(masterProductElement, 'field-error');
            $A.util.addClass(masterProductRequiredText, 'slds-hide');
        } else {
            masterProductValid = false;
            $A.util.addClass(masterProductElement, 'field-error');
            $A.util.removeClass(masterProductRequiredText, 'slds-hide');
        };
        let purchaseDate = component.get('v.purchaseDate');
        let toDay = component.get('v.toDay');
        let maxDate = Date.parse(purchaseDate) <= Date.parse(toDay);
        console.log(maxDate, 'maxDate---------');
        if (isValid && masterProductValid && maxDate) {
            helper.getTableDataByApex(component);
        }
    },

    // 模板选择
    handleSelect : function(component, event, helper) {
        let selectedMenuItemValue = event.getParam("value");
        if (selectedMenuItemValue === 'warrantyRegistrationTemplate') {
            let a = document.createElement('a'); 
            // 创建一个隐藏的a标签
            a.style = 'display: none';
            a.download = 'Warranty Registration Template';
            // a.href = $A.get('$Resource.Template') + '/Template/Warranty_Registration_Template.xlsx';
            a.href = $A.get('$Resource.Warranty_Registration_Template') + '/Warranty_Registration_Template.xlsx';
            document.body.appendChild(a);
             // 触发a标签的click事件
            a.click();
            document.body.removeChild(a);
        }
    },

    // 表格SN编辑
    handleSaveEdition : function(component, event, helper) {
        var draftValues = event.getParam('draftValues');
        helper.saveEdition(component, draftValues);
    },
    handleCancelEdition : function(component, event, helper) {
        // component.set('v.draftValues', []);
        console.log('取消表格SN编辑-----------------');
        helper.quitTableEdit(component);
    },
    // 发票上传
    handleFilesChange : function(component, event, helper) {
        var files = event.getSource().get("v.files");
        let fileType = files[0].type.slice(-3);
        // 校验发票类型
        console.log(fileType, '校验发票类型-------------');
        if (fileType !== 'pdf' && fileType !== 'png') {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_UploadFileFormatError"),
                "type": "Warning"
            }).fire();
            return;
        }
        helper.fileByBase64ForReceipt(component, files, fileType);
    },

    // 删除发票
    deleteReceipt : function(component, event, helper){
        component.set('v.receiptUrl', {});
    },

    // 应用发票
    applyReceipt : function(component, event, helper){
        let dataArr = component.get('v.tableData');
        let selectList = component.get('v.selectList');
        let receiptUrl = component.get('v.receiptUrl');
        console.log(selectList, receiptUrl, 'selectList, receiptUrl-----------------');
        if (selectList.length && receiptUrl.fileUrl) {
            selectList.forEach((selectItem)=>{
                dataArr.forEach((item)=>{
                    if (selectItem.id === item.id) {
                        item.receipt = receiptUrl.fileUrl;
                        item.receiptName = receiptUrl.fileName;
                    }
                })
            });
            component.set('v.tableData', dataArr);
            helper.setTableDataKey(component);
            console.log(component.get('v.tableData'), '表格应用发票==========');
        } else {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_CheckInvoiceWarning"),
                "type": "Warning"
            }).fire();
        }
    },

    // 提交事件
    onClickSubmit : function(component, event, helper){
        // 先判断是否处于SN编辑状态
        let tableData = JSON.parse(JSON.stringify(component.get('v.tableData')));
        let isEdit = false;
        tableData.forEach((item)=>{
            if (item.isEdit) {
                isEdit = true;
            }
        })
        if (isEdit) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SaveSNWarning"),
                "type": "Warning"
            }).fire();
            component.set('v.resetFlag', true);
            return;
        }
        const isNew = component.get('v.isNew');
        console.log(isNew, '提交事件isNew==========');
        // 必填校验 (为new，需要校验owner info)
        const tableArr = JSON.parse(JSON.stringify(component.get('v.tableData')));
        // 判空
        if (!tableArr.length) {
            helper.showToast('Failed', $A.get("$Label.c.CCM_FillWarrantyDataError"));
            return;
        };
        // checkout检验
        component.set('v.submitFlag', true);
        tableArr.forEach((item)=>{
            if (item.checkout !== 'SUCCESS') {
                console.log("item=====",item);
                component.set('v.submitFlag', false);
            }
        });
        const submitFlag = component.get('v.submitFlag');
        if (!submitFlag) {
            helper.showToast('Failed', $A.get("$Label.c.CCM_CheckoutNotificationWarning"));
            return;
        }
        let snErrorTips = [];
        component.set('v.errortip', false);
        // 校验list sn是否重复
        tableArr.forEach((tableItem, tableIndex)=>{
            tableArr.forEach((dataItem, dataIndex)=>{
                if (tableItem.serialNumber == dataItem.serialNumber && tableIndex != dataIndex) {
                    snErrorTips.push(tableIndex + 1);
                }
            })
        })
        // 数据去重
        snErrorTips = snErrorTips.filter((item, index, snErrorTips) => {
            return snErrorTips.indexOf(item) === index;
        });
        if (snErrorTips.length) {
            component.set('v.errortip', true);
            console.log(snErrorTips.join(', '), 'sn重复---------------');
            component.set('v.errorList', $A.get("$Label.c.CCM_DuplicateSNError") + snErrorTips.join(', '));
            return;
        } else {
            component.set('v.errortip', false);
        }
        // 校验注册类型
        let registrationType = component.get('v.registrationType');
        console.log(registrationType, '校验注册类型-----------');
        if (registrationType == 'residentialUser') {
            if (isNew) {
                const isValid = helper.getValidation(component, 'form');
                let countryValid = false;
                const countryElement = component.find('country');
                const countryRequiredText = component.find('country-error-required');
                // country校验
                const countryValue = JSON.parse(JSON.stringify(component.get('v.country')));
                if (countryValue.Id) {
                    countryValid = true;
                    $A.util.removeClass(countryElement, 'field-error');
                    $A.util.addClass(countryRequiredText, 'slds-hide');
                } else {
                    countryValid = false;
                    $A.util.addClass(countryElement, 'field-error');
                    $A.util.removeClass(countryRequiredText, 'slds-hide');
                };
                console.log(isValid, countryValid, 'isValid && countryValid==========');
                if (isValid && countryValid) {
                    helper.submitEvent(component);
                }
            } else {
                helper.submitEvent(component);
            }
        } else {
            helper.submitEvent(component);
        }
    },
    // 缺失发票
    lostReceipt : function(component, event, helper){
        const selectList = component.get('v.selectList');
        const dataArr = component.get('v.tableData');
        if (selectList.length) {
            selectList.forEach((selectItem)=>{
                dataArr.forEach((item)=>{
                    if (selectItem.id === item.id) {
                        item.receipt = 'Lost';
                        item.receiptName = 'Lost';
                    }
                })
            });
            component.set('v.tableData', dataArr);
            helper.setTableDataKey(component);
            console.log(component.get('v.tableData'), '缺失发票==========');
        } else {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_CheckDataError"),
                "type": "Warning"
            }).fire();
            return;
        }
    },

    //Yanko add 2023-9-8
    onClickCancel : function(component, event, helper){
        let url = window.location.origin + '/s/servicehome';
        window.open(url, '_self');
    },

    onExportPDF : function(component, event, helper){
        // 先判断是否处于SN编辑状态
        let tableData = JSON.parse(JSON.stringify(component.get('v.tableData')));
        let isEdit = false;
        tableData.forEach((item)=>{
            if (item.isEdit) {
                isEdit = true;
            }
        })
        if (isEdit) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_SaveSNWarning"),
                "type": "Warning"
            }).fire();
            component.set('v.resetFlag', true);
            return;
        }
        const isNew = component.get('v.isNew');
        console.log(isNew, '提交事件isNew==========');
        const tableArr = JSON.parse(JSON.stringify(component.get('v.tableData')));
        // warrnty list不为空校验
        if (!tableArr.length) {
            helper.showToast('Failed', $A.get("$Label.c.CCM_FillWarrantyDataError"));
            return;
        };
        // SN必填校验
        component.set('v.submitFlag', true);
        tableArr.forEach((item)=>{
            if (item.checkout !== 'SUCCESS') {
                console.log("item=====",item);
                component.set('v.submitFlag', false);
            }
        });
        const submitFlag = component.get('v.submitFlag');
        if (!submitFlag) {
            helper.showToast('Failed', $A.get("$Label.c.CCM_CheckoutNotificationWarning"));
            return;
        }
        helper.saveWarrantyPDF(component);
    },
    emailOrPhoneGetCustomerInfo: function(component, event, helper){
        // 校验必填
        helper.getElementRequiredError(component, 'emailAddress');
        let emailInput = component.find('emailAddress');
        let emailValid= emailInput.get('v.validity');
        if (emailValid.valid && component.get('v.emailAddress')) {
	        helper.getCustomerInfo(component);
        }
    },
    // 必填校验
    chengeFirstName: function(component, event, helper){
        helper.getElementRequiredError(component, 'firstName');
    },
    chengeLastName: function(component, event, helper){
        helper.getElementRequiredError(component, 'lastName');
    },
    // change Country
    changeCountry: function(component, event, helper) {
        let country = JSON.parse(JSON.stringify(component.get('v.country')));
        const countryElement = component.find('country');
        const countryRequiredText = component.find('country-error-required');
        if (country.Id) {
            $A.util.removeClass(countryElement, 'field-error');
            $A.util.addClass(countryRequiredText, 'slds-hide');
        } else {
            $A.util.addClass(countryElement, 'field-error');
            $A.util.removeClass(countryRequiredText, 'slds-hide');
        }
    },
    changePostCode: function(component,event,helper) {
        helper.getElementRequiredError(component, 'postCode');
    },
    changeStreet: function(component,event,helper) {
        helper.getElementRequiredError(component, 'street');
    },
    changeCity: function(component,event,helper) {
        helper.getElementRequiredError(component, 'city');
    },
    changeState: function(component,event,helper) {
        helper.getElementRequiredError(component, 'state');
    },
    // 判断表格编辑
    handleCellChange: function(component,event,helper) {
        let draftValues = event.getParam('draftValues');
        let tableData = component.get('v.tableData');
        draftValues.forEach((draftItem)=>{
            tableData.forEach((item)=>{
                if (item.id === draftItem.id) {
                    item.isEdit = true;
                }
            });
        })
        component.set('v.tableData', JSON.parse(JSON.stringify(tableData)));
    },
    // 标准发票上传事件
    handleUploadFinished: function (component, event, helper) {
        var uploadedFiles = event.getParam("files");
        // Get the file name
        uploadedFiles.forEach((file) => {
            // type
            let fileType = file.mimeType.slice(-3);
            helper.getReceiptId(component, file.contentVersionId, file.name, fileType);
        });
    }
})