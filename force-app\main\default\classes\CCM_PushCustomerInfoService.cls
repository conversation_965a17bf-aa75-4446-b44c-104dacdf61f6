/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-11-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_PushCustomerInfoService {
    private static final String ENDPOINT_NAME_SEEBURGER_UAT = 'chervon_seeburger_uat'; 
    private static final String ENDPOINT_NAME_SEEBURGER_PROD = 'chervon_seeburger_prod'; 
    private static final String CONTACT_RT_INVOICE = 'Invoice';
    private static final String CONTACT_RT_DUNNING = 'Dunning';
    private static final String CONTACT_RT_ACCKNOWLEDGMENT = 'Acknowledgment';
    private static final String CUST_STATUS_ACT = 'Active';
    private static final String CUST_STATUS_INACT = 'Inactive';
    private static final String CUST_STATUS_DISCONTINUE = 'DisContinue';
    private static final String CUST_INTERNAL = 'Internal';
    private static final String CUST_EXTERNAL = 'External';
    //获取当前环境类型
    public static Boolean IsSandboxOrg(){
        Boolean isSandbox = true;
        isSandbox = [SELECT IsSandbox FROM Organization LIMIT 1].IsSandbox;
        return isSandbox;
    }

    public static String pushCustInfo(String recordId, Boolean agRemoved, String oldAGAccountNumber, Alert_Message__c alertMessage) {
        System.debug('CCM_PushCustomerInfoService开始同步Customer---Id===》'+recordId);
        List<String> paramStrlist = new List<String>();
        HttpResponse res = new HttpResponse();
        String endPointName ='';
        String querystr =
            'SELECT Id,Customer_SF_ID__c, Customer_SF_Number__c, Customer_Oracle_ID__c, AccountNumber,'+
            ' Name, Account_Description__c, Country__c, Country_Description__c, Sales_Channel__c, Classification1__c,'+
            ' Association_Group__c, Association_Group__r.AccountNumber,Association_Membership_No__c, Owner.FederationIdentifier, Status__c, Discount_Type__c, '+
            'Record_Type_Name__c, DSV_Instruction__c, Insurance_Currency__c, Insurance_Credit_Limit__c, Cost_Center__c, '+
            'Cost_Center_Descrption__c, AR_Overdue_Check__c, Credit_Check__c, Credit_Hold__c, Credit_Limit__c, Currency__c, '+
            'Risk_Code__c, List_Price__c, Allow_Backorder__c, Default_Reporting_Country_Name__c,Planning_Territory_No__c,Planning_Territory_No__r.Territory_Name__c, '+
            'Default_Reporting_Regsitration_Number__c, Regime_Code__c, Tax__c, CreatedById, CreatedDate, Payment_Term_To_Oracle__c,List_Price__r.name, '+
            'Send_dunning_letters__c, Association_Group__r.Association_Type__c FROM Account ' +
            'WHERE id = :recordId and Approval_Status__c = \'Approved\' ';
        if(IsSandboxOrg()){
            endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
        }else {
            endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
        }
        
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        //TODO 待Seeburger提供地址
        String methodName = 'customer';
        String endPoint = MeIS.End_Point__c + methodName;
        // String HeaderKey = headerToken;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));

        Account acc = (Account)Database.query(querystr);
        String accountNumber = acc.AccountNumber;
        Date searchDate = Date.today();
        String alertMode = 'Create';
        List<Alert_Message__c> lstAlert = [SELECT Alert_Message__c, Start_Date__c, End_Date__c, Alert_Mode__c
                                           FROM Alert_Message__c
                                           WHERE Start_Date__c < :searchDate AND (End_Date__c = NULL OR End_Date__c > :searchDate) AND Customer_Account__c = :accountNumber AND Alert_Mode__c = :alertMode];

        // Create a new instance of the Customer map
        Map<String, Object> customerMap = new Map<String, Object>();
        // Set the values for the Customer fields
        customerMap.put('HEADERID', 'Salesforce-' + acc.Customer_SF_ID__c + '-' +system.now().getTime());
        customerMap.put('CUSTOMER_SF_ID', acc.Customer_SF_ID__c);
        customerMap.put('CUSTOMER_SF_NUMBER', acc.Customer_SF_Number__c);
        customerMap.put('CUSTOMER_ORACLE_ID', acc.Customer_Oracle_ID__c);
        customerMap.put('ACCOUNTNUMBER', acc.AccountNumber);
        customerMap.put('CUSTOMER_NAME', acc.Name);
        customerMap.put('ACCOUNT_DESCRIPTION', acc.Account_Description__c);
        customerMap.put('COUNTRY', acc.Country__c);
        customerMap.put('COUNTRY_DESCRIPTION', acc.Country_Description__c);
        customerMap.put('SALES_CHANNAL', acc.Sales_Channel__c);
        customerMap.put('CLASSIFICATION_1', acc.Classification1__c);
        //TODO 取Group Number
        customerMap.put('ASSOCIATION_GROUP_NUMBER', acc.Association_Group__r.AccountNumber);
        customerMap.put('ASSOCIATION_MEMBERSHIP_NO', acc.Association_Membership_No__c);
        //TODO 取User的FederationId ,Oracle 员工号
        customerMap.put('OWNER_ORACLE_NUMBER', acc.Owner.FederationIdentifier);

        //start update vince ********
        String strStatus = '';
        if(acc.Status__c == CUST_STATUS_DISCONTINUE){
            strStatus =  CUST_STATUS_ACT;
        }else {
            strStatus = acc.Status__c;
        }
        customerMap.put('STATUS', strStatus);
        
        String strRecordTypeName = '';
        if(acc.Sales_Channel__c == CUST_INTERNAL){
            strRecordTypeName = acc.Sales_Channel__c;
        }else {
            strRecordTypeName = CUST_EXTERNAL;
        }
        customerMap.put('RECORD_TYPE_NAME', strRecordTypeName);
        //end update vince ********
        
        customerMap.put('DISCOUNT_TYPE', acc.Discount_Type__c);
        customerMap.put('DSV_INSTRUCTION', acc.DSV_Instruction__c);
        customerMap.put('INSURANCE_CURRENCY', acc.Insurance_Currency__c);
        customerMap.put('INSURANCE_CREDIT_LIMIT', acc.Insurance_Credit_Limit__c);
        customerMap.put('COST_CENTER', acc.Cost_Center__c);
        customerMap.put('COST_CENTER_DESCRIPTION',acc.Cost_Center_Descrption__c);
        customerMap.put('AR_OVERDUE_CHECK', acc.AR_Overdue_Check__c);
        customerMap.put('CREDIT_CHECK', acc.Credit_Check__c);
        customerMap.put('CREDIT_HOLD', acc.Credit_Hold__c);
        customerMap.put('CREDIT_LIMIT', acc.Credit_Limit__c);
        customerMap.put('CREDIT_CURRENCY', acc.Currency__c);
        customerMap.put('RISK_CODE', acc.Risk_Code__c);
        customerMap.put('LIST_PRICE_NAME', acc.List_Price__r.name);
        if(String.isNotBlank(acc.Planning_Territory_No__c)){
            customerMap.put('PLANNING_TERRITORY_NAME', acc.Planning_Territory_No__r.Territory_Name__c);
        }else{
            customerMap.put('PLANNING_TERRITORY_NAME', '');
        }
        String allbackOrder = '';
        if(acc.Allow_Backorder__c == 'Yes'){
            customerMap.put('ALLOW_BACKORDER', 'YES');
        }else if(acc.Allow_Backorder__c == 'No'){
            customerMap.put('ALLOW_BACKORDER', 'NO');
        }else {
            customerMap.put('ALLOW_BACKORDER', acc.Allow_Backorder__c);
        }
        
        System.debug('ALLOW_BACKORDER============>'+customerMap.get('ALLOW_BACKORDER'));
        customerMap.put('DEFAULT_REPORTING_COUNTRY_NAME', acc.Default_Reporting_Country_Name__c);
        customerMap.put('DEFAULT_REPORTING_REGSITRATION_NUMBER', acc.Default_Reporting_Regsitration_Number__c);
        customerMap.put('REGIME_CODE', acc.Regime_Code__c);
        customerMap.put('TAX', acc.Tax__c);
        customerMap.put('CREATED_USER_CODE', acc.CreatedById);
        customerMap.put('CREATED_DATETIME', acc.CreatedDate.format('yyyy-MM-dd HH:mm:ss'));
        //查询Ab 获取PAYMENT_TERM
        Sales_Program__c ab = new Sales_Program__c();
        List<Sales_Program__c> lstAb = new List<Sales_Program__c>();
        lstAb = [Select Payment_Term__c, Status__c from Sales_Program__c  where Customer__c = :recordId And Status__c = 'Active' limit 1];
        if(lstAb.size() > 0){
            ab = lstAb[0];
        }
        if(ab != null){
            customerMap.put('PAYMENT_TERM_TO_ORACLE', ab.Payment_Term__c);
        }else{
            customerMap.put('PAYMENT_TERM_TO_ORACLE', '');
        }
       
        if(String.isNotBlank(acc.Send_dunning_letters__c)){
            customerMap.put('SEND_DUNNING_LETTERS', acc.Send_dunning_letters__c);
        }else{
            customerMap.put('SEND_DUNNING_LETTERS', '');
        }

        if(agRemoved) {
            customerMap.put('Attribute1', oldAGAccountNumber);
            customerMap.put('Attribute5', 'N');
        }
        else if(acc.Association_Group__c != null) {
            customerMap.put('Attribute1', acc.Association_Group__r.AccountNumber);
            customerMap.put('Attribute2', 'N');
            if(String.isNotBlank(acc.Association_Group__r.Association_Type__c) && acc.Association_Group__r.Association_Type__c.contains('Bill to Association Group')) {
                customerMap.put('Attribute3', 'Y');
            }
            else {
                customerMap.put('Attribute3', 'N');
            }
            customerMap.put('Attribute4', 'N');
            customerMap.put('Attribute5', 'Y');
        }
        if(alertMessage != null) {
            customerMap.put('Attribute6', alertMessage.Alert_Message__c);
            if(alertMessage.Alert_Mode__c == 'Delivery') {
                alertMessage.Alert_Mode__c = 'INQUIRY';
            }
            customerMap.put('Attribute7', alertMessage.Alert_Mode__c);
            if(alertMessage.Start_Date__c != null) {
                customerMap.put('Attribute8', String.valueOf(alertMessage.Start_Date__c));
            }
            if(alertMessage.End_Date__c != null) {
                customerMap.put('Attribute9', String.valueOf(alertMessage.End_Date__c));
            }
        }
        
        String paramStr = JSON.serialize(customerMap);
        System.debug('pushCustomer--paramStr:'+paramStr);
        if (!Test.isRunningTest()) {
            res = CCM_ServiceCallout.getDataViaHttpInEU( // callout
                paramStr,
                endPoint,
                'POST',
                HeaderKey
            );
            String resBody = res.getBody();
            CustomerProcessResult objRes = (CustomerProcessResult)JSON.deserialize(resBody,CustomerProcessResult.class);
            String logId;
            if(String.isNotBlank(objRes.PROCESS_MESSAGE)){
                objRes.PROCESS_MESSAGE = objRes.PROCESS_MESSAGE.replace('"','');
            }
            System.debug('objRes.PROCESS_STATUS==>'+objRes.PROCESS_STATUS);
            if(objRes.PROCESS_STATUS.equals('Success')){
                System.debug('pushCustomer====>Success');
                Boolean needUpdate = false;
                if(String.isBlank(acc.Customer_Oracle_ID__c) && String.isNotBlank(objRes.CUSTOMER_ORACLE_ID)){
                    acc.Customer_Oracle_ID__c = objRes.CUSTOMER_ORACLE_ID;
                    needUpdate = true;
                }
                if(String.isBlank(acc.AccountNumber) && String.isNotBlank(objRes.ACCOUNTNUMBER)){
                    acc.AccountNumber = objRes.ACCOUNTNUMBER;
                    needUpdate = true;
                }
                if(needUpdate){
                    Database.upsert(acc,false);
                } 
                
                logId = Util.logIntegration('Push Customer Log', 'CCM_Service', 'POST','',paramStr, resBody);
                // //同步地址
                // List<String> lstBaabId = new List<String>();
                // List<Address_With_Program__c> lstBaab = [Select a.Customer_Line_Oracle_ID__c, a.Customer_Line_SF_ID__c, a.Customer_Oracle_ID__c, a.Customer_SF_ID__c, a.Program__c, a.Status__c from Address_With_Program__c a
                // where Customer_SF_ID__c = :acc.Customer_SF_ID__c];
                // for(Address_With_Program__c baab : lstBaab ){
                //     lstBaabId.add(baab.Id);
                // }
                // CCM_Service.pushAddressInfo(lstBaabId);
            }else {
                System.debug('pushCustomer====>Error');
                logId = Util.logIntegration('Push Customer Exception', 'CCM_Service', 'POST',objRes.PROCESS_MESSAGE,paramStr, resBody);
                Util.pushExceptionEmail('Sync Customer Exception', logId,resBody);
            }
            //res.setBody('{"Process_Status":"Success"}');      
        }else{
            res.setBody('{"Process_Status":"Success"}');
        }
        System.debug('pushCustomer==========>'+res.getBody());
        return res.getBody();
    }

    public class SyncRes {
        public String ReturnCode;
        public String ReturnMessage;
    }

    public class SyncResult {
        public String ReturnCode;
        public String ReturnMessage;
        public String Process_Status;
        public List<ProcessResult> Process_Result;
    }

    public class ProcessResult {
        public String SFDC_Id;
        public String Oracle_Id;
        public String Error_Message;
        public String Error_Detail;
    }
    /**
     * Customer  SF->Oracle
     * Response Sample：
     * {
        "CUSTOMER_HEADER_ID":"Cust-EU-********",
        "CUSTOMER_SF_ID":"Cust-EU-********",
        "CUSTOMER_ORACLE_ID":"",
        "PROCESS_STATUS":"Error",
        "PROCESS_MESSAGE":"Creation of Party and customer account failed:1) 2)"
    }
     */
    public class CustomerProcessResult {
        public String CUSTOMER_HEADER_ID;
        public String CUSTOMER_SF_ID;
        public String CUSTOMER_ORACLE_ID;
        public String ACCOUNTNUMBER;
        public String PROCESS_STATUS;
        public String PROCESS_MESSAGE;
    }

    
    public class AddressResult {
        public String ADDRESS_SF_ID;
        public String ADDRESS_ORACLE_NUMBER;
        public String CUSTOMER_ORACLE_ID;
        public List<ContactLineResult> ContactLine;
        public String PROCESS_STATUS;
        public String PROCESS_MSG;
    }
    public class ContactLineResult {
        public String ADDRESS_SF_ID;
        public String ADDRESS_ORACLE_NUMBER;
        public String CONTACT_SF_ID;
        public String CONTACT_ORACLE_ID;
        public String CONTACT_ORACLE_NUMBER;
    }
}