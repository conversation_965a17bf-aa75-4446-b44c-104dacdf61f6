/**
 * Author : Honey
 * Description : 价格中间表计算。一级价格册计算
 */
public without sharing class CCM_UpsertLink1stPriceBatch implements Database.Batchable<sObject>,Database.Stateful{
    public Set<String> lstCustomerId;
    public Set<String> setCustomerId = new Set<String>();
    public String OrderType;

    private Set<String> tranCustomerIdSet = new Set<String>();
    private String processEntry;
    private Datetime startTime;
    
    public CCM_UpsertLink1stPriceBatch(Set<String> lstCustomerIds,String OrderTypeInfo){
        this.lstCustomerId = lstCustomerIds;
        this.OrderType = OrderTypeInfo;
    }

    public CCM_UpsertLink1stPriceBatch(String OrderTypeInfo){
        this.OrderType = OrderTypeInfo;
    }
    
    public CCM_UpsertLink1stPriceBatch() {

    }

    public void setStartTime(Datetime startTime) {
        this.startTime = startTime;
    }

    public CCM_UpsertLink1stPriceBatch(Set<String> lstCustomerIds, String OrderTypeInfo, String processEntry){
        this.lstCustomerId = lstCustomerIds;
        this.OrderType = OrderTypeInfo;
        this.processEntry = processEntry;
    }

    public Database.QueryLocator start(Database.BatchableContext BC){
        //查询出所有未计算的Modifier
        // if(this.processEntry == 'Export Sales Price') {
        //     Log__c log = new Log__c();
        //     log.Name = 'Export Sales Price';
        //     log.ObjType__c = 'AsyncApexJob';
        //     log.RecordId__c = BC.getJobId();
        //     log.ResParam__c = 'In Progress';
        //     insert log;
        // }
        if(lstCustomerId != null && lstCustomerId.size() !=0){
            return Database.getQueryLocator([
                SELECT Id,Customer__c,Modifier_1__c,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,Order_Type__c,
                Modifier_2__c FROM Sales_Program__c WHERE (Modifier_1__c != null OR Modifier_2__c != null) AND Status__c = 'Active'
                AND Customer__r.Is_Update__c = true and Customer__r.RecordType.Name in ('Association Group','Channel Customer') 
                AND Customer__c IN : lstCustomerId
                AND Order_Type__c = : OrderType and(List_Price_1__c != null OR List_Price_2__c != null OR List_Price_3__c != null )] );
        }else{
            return Database.getQueryLocator([
                SELECT Id,Customer__c,Modifier_1__c,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,Order_Type__c,
                Modifier_2__c FROM Sales_Program__c WHERE (Modifier_1__c != null OR Modifier_2__c != null) AND Status__c = 'Active'
                AND Customer__r.Is_Update__c = true and Customer__r.RecordType.Name in ('Association Group','Channel Customer') 
                AND Order_Type__c = : OrderType and(List_Price_1__c != null OR List_Price_2__c != null OR List_Price_3__c != null )]);
        }
    }
    public void execute(Database.BatchableContext BC, List<Sales_Program__c> lstSalesGram){
         List<LinkProductAndPrice__c> lstLinkAllProduct = new List<LinkProductAndPrice__c>();
         List<MasterProductPrice__c>  lstAllMasterInfo = new List<MasterProductPrice__c>(); 
        try{ 
            
            this.tranCustomerIdSet = new Set<String>();
            for(Sales_Program__c objAccount : lstSalesGram){
                this.setCustomerId.add(objAccount.Customer__c);
                this.tranCustomerIdSet.add(objAccount.Customer__c);
            }
            List<Account> lstAccount = [ SELECT Id, Is_Update__c, Last_Price_Caculate_Date__c, Is_2st_Caculate__c 
                                         FROM Account WHERE RecordType.Name IN ('Association Group','Channel Customer') 
                                         AND Id IN :this.tranCustomerIdSet AND Is_Update__c = TRUE];
           
            //存放ModifireId--->用于查询ModifireEntry
            Set<String> SetModifireIds = new Set<String>();
            //将CustomerId与ModifireId绑定
            Map<String,Set<String>> mapCustomerId2ModifireId = new Map<String,Set<String>>();
            //将Customer与PriceBook绑定
            Map<String,Set<String>> mapCustomerId2PriceBook = new Map<String,Set<String>>();
            //创建PriceBook集合。用于查询PriceBookEntry获取与其绑定得Product信息
            Set<String> setPriceBookIds = new Set<String>();
            //setPriceBookIds.addAll(mapCustomerId21stPriceId.values());
            
            //创建AccountId到以及价格册的映射
            Map<String,String> mapAccountId21PriceBook = new Map<String,String>();
            Map<String,String> mapCustoemrId2AuthBrand = new Map<String,String>();
            //遍历AuthBrand信息
            for (Sales_Program__c objSalesProgram : lstSalesGram) {
                
                //一个sales_program一个Customer
                String CustomerId = objSalesProgram.Customer__c;
                mapCustoemrId2AuthBrand.put(CustomerId,objSalesProgram.Id);
                String modifierMKT = objSalesProgram.Modifier_1__c;
                String modifierToolPart = objSalesProgram.Modifier_2__c;
                if(String.isNotBlank(objSalesProgram.Price_Book__c)) {
                    setPriceBookIds.add(objSalesProgram.Price_Book__c);
                    mapAccountId21PriceBook.put(CustomerId, objSalesProgram.Price_Book__c);
                }
                
                SetModifireIds.add(modifierMKT);
                SetModifireIds.add(modifierToolPart);
                Set<String> setModifireIdbyCustomer = mapCustomerId2ModifireId.containsKey(CustomerId) ?
                    mapCustomerId2ModifireId.get(CustomerId) : new Set<String>();
                setModifireIdbyCustomer.add(modifierMKT);
                setModifireIdbyCustomer.add(modifierToolPart);
                mapCustomerId2ModifireId.put(CustomerId, setModifireIdbyCustomer);
            }
           
            //根据Price Book得id获取priceBookEntry
            if(SetModifireIds.size() == 0 || setPriceBookIds.size()  == 0){
                return;
            }
            
            Datetime searchDateTime = Datetime.now();
            
            List<Pricebook_Entry__c> lstPriceEntry = [
                SELECT Id, End_Date__c, Start_Date__c, Product__c, UnitPrice__c, PriceBook__c, PriceBook__r.IsActive,
                Product__r.Order_Model__c, Product__r.PPC_CODE__c, PriceBook__r.CreatedDate
                FROM Pricebook_Entry__c  
                WHERE  PriceBook__r.IsActive = true AND PriceBook__c IN :setPriceBookIds  
                AND End_Date__c >= :searchDateTime
            ];
           
            //将Customer与PriceBookEntry做映射--->只做一级价格册的映射
            Map<String, Set<Pricebook_Entry__c>> mapCustomerId21stPriceBookEntry = new Map<String, Set<Pricebook_Entry__c>>();
          
            for(Pricebook_Entry__c objPriceBookEntry : lstPriceEntry){
                for(String CustomerId : mapAccountId21PriceBook.keySet()){
                     //表示这个Customer包含这个Entry
                        Set<Pricebook_Entry__c> lstPriceBookEntry1stByCustomer = mapCustomerId21stPriceBookEntry.containsKey(CustomerId)?
                        mapCustomerId21stPriceBookEntry.get(CustomerId) : new Set<Pricebook_Entry__c>();
                        lstPriceBookEntry1stByCustomer.add(objPriceBookEntry);
                        mapCustomerId21stPriceBookEntry.put(CustomerId, lstPriceBookEntry1stByCustomer);
                    }
            }
            
            //通过ModifireId查询Modifire Entry信息
            List<Modifier_Entry__c> lstModifierEntry = [
                SELECT Id, Application_Method__c, End_Date__c, Incompatibility_Group__c, Value__c,CurrencyIsoCode,
                Modifier__c, Precedence__c, Pricing_Phase__c, Product_Attributes__c, Product_Code__c, Start_Date__c, Modifier__r.Active__c
                FROM Modifier_Entry__c WHERE Modifier__c IN :SetModifireIds AND Modifier__r.Active__c = 'Y'
            ];
            //创建Customer到Modifire Entry的映射--->Customer到二级价格册Entry的映射
            Map<String,Set<Modifier_Entry__c>> mapCustomerId2ModifireEntry = new Map<String,Set<Modifier_Entry__c>>();
            for(Modifier_Entry__c objModifireEntry : lstModifierEntry){
                for(String CustomerId : mapCustomerId2ModifireId.keySet()){
                    Set<String> setModifireIdByCustomerId = mapCustomerId2ModifireId.get(CustomerId);
                    if(setModifireIdByCustomerId.contains(objModifireEntry.Modifier__c)){
                        //表示这个Customer包含这个Entry
                        Set<Modifier_Entry__c> lstMOdifireEntryByCustomer = mapCustomerId2ModifireEntry.containsKey(CustomerId)?
                            mapCustomerId2ModifireEntry.get(CustomerId) : new Set<Modifier_Entry__c>();
                        lstMOdifireEntryByCustomer.add(objModifireEntry);
                        mapCustomerId2ModifireEntry.put(CustomerId,lstMOdifireEntryByCustomer);
                    }
                }
            }
            
            //在计算二级价格之前先计算一级价格册--->一级价格册与Modifire先取交集?时间交集--->Todo     
            Map<String,List<LinkProductAndPrice__c>> mapCustomer2LinkProduct = new Map<String,List<LinkProductAndPrice__c>>();
            List<PriceBook_Priority__mdt> lstPricePriority = [
                SELECT Id, Label, Priority_Level__c FROM PriceBook_Priority__mdt
            ];
            Map<String,Integer> mapLabel2Level = new Map<String,Integer>();
            for(PriceBook_Priority__mdt objPricePriority : lstPricePriority){
                mapLabel2Level.put(objPricePriority.Label , Integer.valueOf(objPricePriority.Priority_Level__c) );
            }

            for(Account objAccount : lstAccount){
                String customerId = objAccount.Id;
                //同一个customer的modifire和PriceBook遍历
                Set<Modifier_Entry__c> lstModifireEntryByCustomer = mapCustomerId2ModifireEntry.get(CustomerId);

                //默认需要计算二级价格册
                Boolean IsCaculate = true;
                //用于存储一级价格册已经计算的产品
                Set<String> setProductCodes1st = new Set<String>();
                Set<Pricebook_Entry__c> lstPriceBookEntry1stByCustomer = mapCustomerId21stPriceBookEntry.get(CustomerId);
                //计算一级价格册
                if(lstModifireEntryByCustomer!= null && lstPriceBookEntry1stByCustomer != null ){
                    //遍历所有产品--->一个用户下的一个产品一个时间范围为一个master.
                    //同一个用户下同一个产品同一时间范围。多个entry信息为一个Detail
                    for(Modifier_Entry__c objModifireEntry :lstModifireEntryByCustomer){
                        if(lstPriceBookEntry1stByCustomer != null ){
                            for(Pricebook_Entry__c objPriceBookEntry : lstPriceBookEntry1stByCustomer){
                                if( (objModifireEntry.Product_Attributes__c =='Item Number' && objModifireEntry.Product_Code__c == objPriceBookEntry.Product__r.Order_Model__c)
                                 ||(objModifireEntry.Product_Attributes__c =='PPC' && objModifireEntry.Product_Code__c == objPriceBookEntry.Product__r.PPC_CODE__c )  ){
                                    //先校验时间匹配--->获取相关的startDate和End Date
                                    //start取最大值
                                    Date maxDate = String.valueOf(objPriceBookEntry.Start_Date__c)  > String.valueOf(objModifireEntry.Start_Date__c) ? 
                                        Date.valueOf(objPriceBookEntry.Start_Date__c) : Date.valueOf(objModifireEntry.Start_Date__c);
                                    //endDate取最小值
                                    Date minDate =  String.valueOf(objPriceBookEntry.End_Date__c)  > String.valueOf(objModifireEntry.End_Date__c) ? 
                                        Date.valueOf(objModifireEntry.End_Date__c) : Date.valueOf(objPriceBookEntry.End_Date__c);
                                    
                                    //如果start Date 和EndDate是一个正常的时间范围则将产品信息存入--->开始时间小于结束时间
                                    if(String.valueOf(maxDate) <= String.valueOf(minDate)  && minDate >= Date.today()){
                                        LinkProductAndPrice__c objLinkProductPrice = new LinkProductAndPrice__c();
                                        String midelPriority = SetProvity2Number( objLinkProductPrice, objModifireEntry, mapLabel2Level);
                                        objLinkProductPrice.Has_AllItem__c = false;
                                        SetLinkProduct(objLinkProductPrice, objModifireEntry, objPriceBookEntry, CustomerId, midelPriority, maxDate, minDate);
                                        lstLinkAllProduct.add(objLinkProductPrice);
                                        setProductCodes1st.add(objPriceBookEntry.Product__c);
                                    }
                                }
                            }
                             //对All Item单独处理
                             if(CCM_CONSTANTS.All_ITEMS.equals(objModifireEntry.Product_Attributes__c)){
                                LinkProductAndPrice__c objLinkProductPrice = new LinkProductAndPrice__c();
                                String midelPriority = SetProvity2Number( objLinkProductPrice, objModifireEntry,mapLabel2Level);
                                objLinkProductPrice.Has_AllItem__c = true;
                                objLinkProductPrice.Account__c = CustomerId;
                                //一级价格册已经计算所有。不需要后续计算
                                IsCaculate = false;
                                SetLinkProductAllItem( objLinkProductPrice, objModifireEntry, CustomerId, MidelPriority);
                                lstLinkAllProduct.add(objLinkProductPrice);
                            }
                        } 
                    }
                }
                if(!setProductCodes1st.isEmpty()) {
                    List<String> productCodes1stList = new List<String>();
                    productCodes1stList.addAll(setProductCodes1st);
                    String caculatedProduct = String.join(productCodes1stList, ',');
                    objAccount.Caculate_Product_Price__c = caculatedProduct;
                }
                
                objAccount.Is_2st_Caculate__c = IsCaculate;
            }
            
           
            Map<String,List<LinkProductAndPrice__c>> mapCustomerProduct2LinkProductAllItem = new Map<String,List<LinkProductAndPrice__c>>();
            //所有ALL Item的在一起了。遍历 lstPriceBookEntryByCustomer-->将同一产品、同一时间段的放在一起。组成Master的部分
            for(LinkProductAndPrice__c objLinkProduct : lstLinkAllProduct){
                String Key = '' + objLinkProduct.Account__c + '' + objLinkProduct.Product__c + '' +
                    String.valueOf(objLinkProduct.Start_Date__c) + '' + String.valueOf(objLinkProduct.End_Date__c);
                List<LinkProductAndPrice__c> lstLinkProductByCustomerAndProductAllitem =  
                mapCustomerProduct2LinkProductAllItem.containsKey(key)? mapCustomerProduct2LinkProductAllItem.get(key) : new List<LinkProductAndPrice__c>();
                lstLinkProductByCustomerAndProductAllitem.add(objLinkProduct);
                mapCustomerProduct2LinkProductAllItem.put(key, lstLinkProductByCustomerAndProductAllitem);
            }
            
            //分别遍历每个维度的Link-->获取主对象信息-->一个Key只创建一个数据
            for(String key :  mapCustomerProduct2LinkProductAllItem.KeySet()){
                List<LinkProductAndPrice__c> lstLinkProductByAll = mapCustomerProduct2LinkProductAllItem.get(key);
              
                if(lstLinkProductByAll == null || lstLinkProductByAll.size() == 0){
                    continue;
                }
                MasterProductPrice__c objMasterProductPrice = new MasterProductPrice__c();
                objMasterProductPrice.Account__c = lstLinkProductByAll[0].Account__c;
          
                objMasterProductPrice.CurrencyIsoCode = lstLinkProductByAll[0].CurrencyIsoCode;
                objMasterProductPrice.Order_Type__c = OrderType;
                objMasterProductPrice.List_Price__c = lstLinkProductByAll[0].List_Price__c;
                objMasterProductPrice.Final_Price__c = lstLinkProductByAll[0].Final_Price__c;
                objMasterProductPrice.Start_Date__c = lstLinkProductByAll[0].Start_Date__c;
                objMasterProductPrice.End_Date__c = lstLinkProductByAll[0].End_Date__c;
                if(lstLinkProductByAll[0].Product__c != null){
                    objMasterProductPrice.Authorized_Brand__c = mapCustoemrId2AuthBrand.get(lstLinkProductByAll[0].Account__c);
                    objMasterProductPrice.Product__c = lstLinkProductByAll[0].Product__c;
                    objMasterProductPrice.Has_AllItem__c = false;
                }else{
                    objMasterProductPrice.Has_AllItem__c = true;
                }
                
                lstAllMasterInfo.add(objMasterProductPrice);
            }
            insert lstAllMasterInfo;
            Map<String,String> mapKey2MasterId = new Map<String,String>();
            for(MasterProductPrice__c objMasterProduct : lstAllMasterInfo){
                String Key = '' + objMasterProduct.Account__c + '' + objMasterProduct.Product__c + '' +
                    String.valueOf(objMasterProduct.Start_Date__c) + '' + String.valueOf(objMasterProduct.End_Date__c);
                    mapKey2MasterId.put(Key, objMasterProduct.Id);
                
            }
             //遍历插入后的主数据
            List<LinkProductAndPrice__c> lstLinkProductInsert = new List<LinkProductAndPrice__c>();
        
            for(LinkProductAndPrice__c objLink : lstLinkAllProduct){
                String Key = '' + objLink.Account__c + '' + objLink.Product__c + '' +
                String.valueOf(objLink.Start_Date__c) + '' + String.valueOf(objLink.End_Date__c);
                if(String.isBlank(objLink.MasterProductPrice__c)){
                    objLink.MasterProductPrice__c = mapKey2MasterId.get(Key);
                }
                
                lstLinkProductInsert.add(objLink);
            }
            insert lstLinkProductInsert;
            //update lstAccount;
            Database.update(lstAccount, false);
        }Catch(Exception e){
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
        }
    }

    public String SetProvity2Number(LinkProductAndPrice__c objLinkProductPrice, Modifier_Entry__c objModifireEntry, Map<String,Integer> mapLabel2Level){
        //查询metadate
        String MidelPrority = '';
        Integer FirstPrority = mapLabel2Level.get(objModifireEntry.Product_Attributes__c);
        Integer SecondPrority = mapLabel2Level.get(objModifireEntry.Incompatibility_Group__c);
        Integer ThirdPrority = mapLabel2Level.get(objModifireEntry.Pricing_Phase__c);
        MidelPrority = FirstPrority +''+SecondPrority+''+ThirdPrority;
        return MidelPrority;
    }

    /**
     * Digit  需要补全的数字
     * place  补全多少位数
     */
    public String AutoFillNumber(Decimal digit , Integer place ){
        String digitString = String.valueOf(digit);
        Integer digitLength = digitString.length();
    
        if (digitLength < place) {
            Integer paddingLength = place - digitLength;
            String paddingZeros = '0'.repeat(paddingLength);
            return paddingZeros + digitString;
        }
        return digitString;
    }

    public void SetLinkProduct(LinkProductAndPrice__c objLinkProductPrice, Modifier_Entry__c objModifireEntry, Pricebook_Entry__c objPriceBookEntry, String CustomerId, String MidelPriority, Date maxDate, Date minDate){
        try{
            objLinkProductPrice.Account__c = CustomerId;
            //对于价格。优先取一级价格册上的价格再取二级价格册的价格
            objLinkProductPrice.List_Price__c = objPriceBookEntry.UnitPrice__c;
            objLinkProductPrice.Modifier_Entry__c = objModifireEntry.Id;
            objLinkProductPrice.Product__c = objPriceBookEntry.Product__c;
            objLinkProductPrice.End_Date__c = minDate;
            objLinkProductPrice.Start_Date__c = maxDate;
            objLinkProductPrice.CurrencyIsoCode = objModifireEntry.CurrencyIsoCode;
            //获取precend
            String precedencePrority = AutoFillNumber(objModifireEntry.Precedence__c,Integer.valueOf(Label.AutoPrecedence));
            //
            Decimal FinalPrice = 0;
            if(objModifireEntry.Application_Method__c == 'New Price'){
                FinalPrice = objModifireEntry.value__c;
            }else if(objModifireEntry.Application_Method__c == 'Percent'){
               // FinalPrice = (1-objModifireEntry.value__c/100) * objLinkProductPrice.List_Price__c;
               // FinalPrice的价格计算变动
               Decimal starndDiscountPrice =  ( objLinkProductPrice.List_Price__c*objModifireEntry.value__c/100).setScale(2,RoundingMode.HALF_UP);
               FinalPrice = (objLinkProductPrice.List_Price__c - starndDiscountPrice).setScale(2,RoundingMode.HALF_UP);
            }
            //获取价格补齐
            String PricePrority = AutoFillNumber(FinalPrice,Integer.valueOf(Label.AutoPrice));
            String FinalPriority = MidelPriority+''+precedencePrority+''+PricePrority;
            objLinkProductPrice.Final_Priority__c = Decimal.valueOf(FinalPriority);
            objLinkProductPrice.Final_Price__c = FinalPrice;
        }catch(Exception e){
            system.debug('报错信息--》'+e.getMessage()+'报错行数-->'+e.getLineNumber());
        }
    }

    public void SetLinkProductAllItem( LinkProductAndPrice__c objLinkProductPrice,Modifier_Entry__c objModifireEntry,String CustomerId,String MidelPriority){
        try{
            objLinkProductPrice.Account__c = CustomerId;
            objLinkProductPrice.End_Date__c = objModifireEntry.End_Date__c;
            objLinkProductPrice.Start_Date__c = objModifireEntry.Start_Date__c;
            objLinkProductPrice.Application_Method__c = objModifireEntry.Application_Method__c;
            objLinkProductPrice.Modifier_Entry__c = objModifireEntry.Id;
            objLinkProductPrice.value__c = objModifireEntry.value__c;
            objLinkProductPrice.CurrencyIsoCode = objModifireEntry.CurrencyIsoCode;
            //获取precend
            String precedencePrority = AutoFillNumber(objModifireEntry.Precedence__c,Integer.valueOf(Label.AutoPrecedence));
            Decimal FinalPrice = 0;
            //获取价格补齐
            String PricePrority = AutoFillNumber(FinalPrice,Integer.valueOf(Label.AutoPrice));
            String FinalPriority = MidelPriority+''+precedencePrority+''+PricePrority;
            objLinkProductPrice.Final_Priority__c = Decimal.valueOf(FinalPriority);
        }catch(Exception e){
            system.debug('报错信息--》'+e.getMessage()+'报错行数-->'+e.getLineNumber());
        }
    }

    public void finish(Database.BatchableContext BC) {
        system.debug('调用计算二级'+setCustomerId.size());
        String jobId = BC.getJobId();
        jobId = jobId.substring(0, 15);
        List<Log__c> logs = [SELECT RecordId__c FROM Log__c WHERE RecordId__c = :jobId AND Name = 'Export Sales Price'];
        if(!setCustomerId.isEmpty()){
            if(logs.isEmpty()) {
                jobId = '';
            }
            CCM_UpsertLinkBatch clsUpsertLink = new CCM_UpsertLinkBatch(setCustomerId, Label.SalesPrice_OrderType, jobId);
            if(this.startTime != null) {
                clsUpsertLink.setStartTime(this.startTime);
            }
            Database.executeBatch(clsUpsertLink, 1);
        }
        else {
            if(!logs.isEmpty()) {
                for(Log__c log : logs) {
                    log.ResParam__c = 'Complete';
                }
                update logs;
            }
        }
    }
}