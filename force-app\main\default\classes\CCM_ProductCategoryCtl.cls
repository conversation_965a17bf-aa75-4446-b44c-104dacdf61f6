/**************************************************************************************************
 * Name: CCM_ProductCategoryCtl
 * Object: Product2
 * Purpose: customers can only see their authorized brands’ products
 * Author:  ()
 * Create Date: 2018-05-07
 * Modify History:
 * 2019-10-07        Create this class      查看所有已授权产品价格册中的产品
 * 2019-11-08        Sharon         Product Categories 改成可以看到品牌下的所有产品
 * 2019-11-21        Sharon         由于ContentService没有Kit和Parts的数据，所以不显示着两种product的查看图标
 **************************************************************************************************/
public without sharing class CCM_ProductCategoryCtl {

    //public static String egoURL = 'https://ego-us.saas.contentserv.com/admin/rest/deepsearch/?t=3&CSTicketID=92882a6c99964b93592cce8bbb63a53d';
    public static String egoURL = '';

    /**1、原逻辑只支持portal端，弃用-vince ********
    @AuraEnabled
    public static String InitProducts(Decimal pageNumber, Integer pageSize, String customerId, String brand, String modelNumber, String keyword, List<String> brandsSelected){
        System.debug(LoggingLevel.INFO, '*** InitProducts: ' );
        if(String.isBlank(customerId)){
            User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.Distributor_or_Dealer__c FROM User WHERE Id = :UserInfo.getUserId()];
            if(currentUser.ContactId != null && currentUser.Contact.AccountId != null){
                customerId = currentUser.Contact.AccountId;
            }
        }else{
            List<Account> customers = [SELECT Id, Distributor_or_Dealer__c FROM Account WHERE Id = :customerId];
        }

        String JsonAllProductInfo = CCM_RequestPurchaseOrderController.GetProductInfo('',customerId,System.today());
        List<Product2> lstProduct = ( List<Product2>)JSON.deserialize(JsonAllProductInfo, List<Product2>.class);
        Set<String> productIdSet = new Set<String>();
        Set<String> showProductType = new Set<String>{'TLS_KIT','ACC','TLS_Product'};
        for(Product2 prod: lstProduct){
            if(showProductType.contains(prod.RecordType.DeveloperName)){
                productIdSet.add(prod.Id);
            }            
        }
        System.debug(LoggingLevel.INFO, '*** productIdSet: ' + productIdSet.size());
        Map<String,Object> result = new Map<String,Object>();
        List<ReturnData> returnList = new List<ReturnData>();

        String queryStr = 'SELECT Id, Name, Order_Model__c, Brand__c, Brand_Name__c, Item_Description_EN_Formula__c, Item_Description_DE_Formula__c, Source__c, ';
        queryStr += ' RecordType.Name, RecordType.DeveloperName, Master_Product__c FROM Product2 ';
        queryStr += ' WHERE IsActive = true AND Id in: productIdSet';
        

        if(String.isNotBlank(brand)){
            queryStr += ' AND Brand_Name__c = \'' + brand + '\'';
        }
        if(brandsSelected != null && brandsSelected.size() > 0){
            String statusCondition = '';
            for(String st : brandsSelected){
                statusCondition += '\'' + st + '\',';
            }
            queryStr += ' AND Brand_Name__c IN (' + statusCondition.removeEnd(',') + ')';
        }
        String userLanguage = UserInfo.getLanguage();

        if(String.isNotBlank(keyword)){
            if(CCM_Constants.EN == userLanguage){
                queryStr += ' AND (Item_Description_EN_Formula__c LIKE \'%' + keyword + '%\' or PPC_Desp__c LIKE \'%' + keyword + '%\' )';
                queryStr += ' AND (PPC_Desp__c LIKE \'%' + keyword + '%\' )';
            }else if(CCM_Constants.DE == userLanguage){
                queryStr += ' AND (Item_Description_DE_Formula__c LIKE \'%' + keyword + '%\' or PPC_Desc_DE__c LIKE \'%' + keyword + '%\')';
                queryStr += ' AND (PPC_Desc_DE__c LIKE \'%' + keyword + '%\')';

            }
        }

        if(String.isNotBlank(modelNumber)){
            queryStr += ' AND (Master_Product__c LIKE \'%' + modelNumber + '%\' )';
        }
        queryStr += ' LIMIT 10000';

        System.debug(LoggingLevel.INFO, '*** queryStr: ' + queryStr);
        for(Product2 pro : (List<Product2>) Database.query(queryStr)){
            ReturnData productData = new ReturnData();
            productData.name = pro.Name;
            productData.moduleNumber = pro.Master_Product__c;
            productData.brand = pro.Brand_Name__c;
            productData.productType = pro.RecordType.DeveloperName;//Add by Zoe
            if(CCM_Constants.EN == userLanguage){
                productData.description = pro.Item_Description_EN_Formula__c;//翻译的问题
            }else if(CCM_Constants.DE == userLanguage){
                productData.description = pro.Item_Description_DE_Formula__c;//翻译的问题
            }
            productData.productId = pro.Id;
            productData.styleClass = '';
            productData.iconName = 'utility:preview';
            returnList.add(productData);
        }        

        result.put('totalData', returnList);

        List<ReturnData> currentData = new List<ReturnData>();
        Integer min = ((Integer)pageNumber - 1) * pageSize;
        Integer max = (Integer)pageNumber * pageSize -1;
        for (Integer i = min ; i <= max; i++ ) {
            if (i < returnList.size()) {
                currentData.add(returnList.get(i));
            }
        }
        if(String.isBlank(egoURL)){
            egoURL = CCM_Service.getEGOHomelink();
        }
        result.put('currentData', currentData);
        result.put('egoHomeURL', egoURL);
        return JSON.serialize(result);
    } */

    /**
     * 查询product category数据，portal端根据可售清单查询，CRM端查全部
     */
    @AuraEnabled
    public static String InitProducts(Decimal pageNumber, Integer pageSize, String customerId, String brand, String modelNumber, String keyword, List<String> brandsSelected,Boolean isPortal){
        System.debug(LoggingLevel.INFO, '*** InitProducts: ' );
        if(String.isBlank(customerId)){
            User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.Distributor_or_Dealer__c FROM User WHERE Id = :UserInfo.getUserId()];
            if(currentUser.ContactId != null && currentUser.Contact.AccountId != null){
                customerId = currentUser.Contact.AccountId;
            }
        }else{
            List<Account> customers = [SELECT Id, Distributor_or_Dealer__c FROM Account WHERE Id = :customerId];
        }
        
        Set<String> productIdSet = new Set<String>();
        Set<String> showProductType = new Set<String>{'TLS_KIT','ACC','TLS_Product'};
        String queryByIdsStr = '';
        Map<String,Object> result = new Map<String,Object>();
        List<ReturnData> returnList = new List<ReturnData>();
        
        String queryStr = 'SELECT Id, Name, Order_Model__c, Brand__c, Brand_Name__c, Item_Description_EN_Formula__c, Item_Description_DE_Formula__c, Source__c, ';
        queryStr += ' RecordType.Name, RecordType.DeveloperName, Master_Product__c FROM Product2 ';
        queryStr += ' WHERE IsActive = true ';
        //如果是portal端 根据可售清单查询'TLS_KIT','ACC','TLS_Product'产品
        if(isPortal){
            String JsonAllProductInfo = CCM_RequestPurchaseOrderController.GetProductInfo('',customerId,System.today());
            List<Product2> lstProduct = ( List<Product2>)JSON.deserialize(JsonAllProductInfo, List<Product2>.class);
            for(Product2 prod: lstProduct){
                if(showProductType.contains(prod.RecordType.DeveloperName)){
                    productIdSet.add(prod.Id);
                }            
            }
            queryByIdsStr = ' AND Id in: productIdSet';
        }else{
            queryByIdsStr = ' And RecordType.DeveloperName in :showProductType';
        }
        
        System.debug(LoggingLevel.INFO, '*** productIdSet: ' + productIdSet.size());
        
        queryStr += queryByIdsStr;

        if(String.isNotBlank(brand)){
            queryStr += ' AND Brand_Name__c = \'' + brand + '\'';
        }
        if(brandsSelected != null && brandsSelected.size() > 0){
            String statusCondition = '';
            for(String st : brandsSelected){
                statusCondition += '\'' + st + '\',';
            }
            queryStr += ' AND Brand_Name__c IN (' + statusCondition.removeEnd(',') + ')';
        }
        String userLanguage = UserInfo.getLanguage();

        if(String.isNotBlank(keyword)){
            if(CCM_Constants.EN == userLanguage){
                queryStr += ' AND (Item_Description_EN_Formula__c LIKE \'%' + keyword + '%\' or PPC_Desp__c LIKE \'%' + keyword + '%\' )';
            }else if(CCM_Constants.DE == userLanguage){
                queryStr += ' AND (Item_Description_DE_Formula__c LIKE \'%' + keyword + '%\' or PPC_Desc_DE__c LIKE \'%' + keyword + '%\')';
            }
        }

        if(String.isNotBlank(modelNumber)){
            queryStr += ' AND (Master_Product__c LIKE \'%' + modelNumber + '%\' )';
        }
        queryStr += ' LIMIT 10000';

        System.debug(LoggingLevel.INFO, '*** queryStr: ' + queryStr);
        for(Product2 pro : (List<Product2>) Database.query(queryStr)){
            ReturnData productData = new ReturnData();
            productData.name = pro.Name;
            productData.moduleNumber = pro.Master_Product__c;
            productData.brand = pro.Brand_Name__c;
            productData.productType = pro.RecordType.DeveloperName;//Add by Zoe
            if(CCM_Constants.EN == userLanguage){
                productData.description = pro.Item_Description_EN_Formula__c;//翻译的问题
            }else if(CCM_Constants.DE == userLanguage){
                productData.description = pro.Item_Description_DE_Formula__c;//翻译的问题
            }
            productData.productId = pro.Id;
            productData.styleClass = '';
            productData.iconName = 'utility:preview';
            returnList.add(productData);
        }        

        result.put('totalData', returnList);

        List<ReturnData> currentData = new List<ReturnData>();
        Integer min = ((Integer)pageNumber - 1) * pageSize;
        Integer max = (Integer)pageNumber * pageSize -1;
        for (Integer i = min ; i <= max; i++ ) {
            if (i < returnList.size()) {
                currentData.add(returnList.get(i));
            }
        }
        if(String.isBlank(egoURL)){
            // egoURL = CCM_Service.getEGOHomelink();
            egoURL = CCM_InfoPortalAutoLoginUtil.getHomeLink(UserInfo.getUserId());
        }
        result.put('currentData', currentData);
        result.put('egoHomeURL', egoURL);
        return JSON.serialize(result);
    } 

    @AuraEnabled
    public static String getContentServLink(String modelNumber){
        return CCM_Service.getContentServProductline(modelNumber);
    }
    // add haibo
    /** 判断是否是Portal端 */
    @AuraEnabled
    public static Boolean IsPortal(){
        return WarrantyClaimUtil.IsPortal();
    }
    public class ReturnData {
        public String name {get; set;}
        public String moduleNumber {get; set;}
        public String brand {get; set;}
        public String productType {get; set;}
        public String description {get; set;}
        public String productId {get; set;}

        public String styleClass {get; set;}
        public String iconName {get; set;}
    }
}