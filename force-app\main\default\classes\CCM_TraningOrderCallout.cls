/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 05-15-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_TraningOrderCallout {

    public static final String ORG_EEG = 'EEG';
    public static final String FLAG_YES = 'YES';
    public static final String FLAG_NONE = 'None';
    public static final String SCENE_TYPE_TRAINING = 'Training Request';
    public static final String ORDER_TYPE_TRAINING_DE = 'EEG Training Domestic';
    public static final String ENDPOINT_NAME_SEEBURGER_UAT = 'chervon_seeburger_uat';
    public static final String ENDPOINT_NAME_SEEBURGER_PROD = 'chervon_seeburger_prod';

    public static String pushRequestInfo(String recordId){
        
        String logPreName = 'Training Order';
        String paramStr = '';
        String logId = '';
        try{
            Map<String, Object> mapRequest = new Map<String, Object>();
            mapRequest = getTrainingRequestInfo(recordId);

            paramStr = Json.serialize(mapRequest);
            System.debug('traning paramStr=====》'+paramStr);
            SyncRes res = new SyncRes();
            res = requestCallOut(paramStr);
            if(String.isNotBlank(res.PROCESS_MSG)){
                res.PROCESS_MSG = res.PROCESS_MSG.replace('"','');
            }
            if(res.Process_Status == 'Failed'){
                
                logId = Util.logIntegration(logPreName + ' Sync Exception','CCM_TraningOrderCallout','POST','',paramStr, res.PROCESS_MSG);
            }else{
                logId = Util.logIntegration(logPreName + ' Sync Log','CCM_TraningOrderCallout','POST','',paramStr, JSON.serialize(res));
            }
            
            return res.Process_Status;
        }catch(Exception e){
            SyncRes res = new SyncRes();
            res.Process_Status = 'Failed';
            system.debug('报错行数---->'+e.getLineNumber()+'报错信息---->'+e.getMessage());
            logId = Util.logIntegration(logPreName + ' Sync Exception','CCM_TraningOrderCallout','POST','',paramStr, e.getMessage());
            return res.Process_Status;
        }
    }

    public static SyncRes requestCallOut(String param){
        SyncRes objRes = new SyncRes();
        String endPointName = '';
        if(CCM_Service.IsSandboxOrg()){
            endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
        }else {
            endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
        }
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'order';
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        system.debug('请求参数为-->'+param);
        if (!Test.isRunningTest()) {
            HttpResponse res = new HttpResponse();
            res = CCM_ServiceCallout.getDataViaHttp(param, endPoint, 'POST', HeaderKey);
            objRes = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
        }
        return objRes;
    }

    //获取Training Order申请参数
    public static Map<String, Object> getTrainingRequestInfo(String recordId){
        Map<String, Object> paramMap = new Map<String, Object>();
        String soqlStr = 'Select Name,CreatedDate,Billing_Address__c,Billing_Address__r.Customer_Line_Oracle_ID__c, Course_Arrangement__c, Customer__c,Customer__r.Id,Customer__r.AccountNumber, Id, Order_Type__c, '+
        'Payment_Term__c, PCS__c, Price_Pcs__c, Salesperson__c, Shiping_Address__c, Shiping_Address__r.Customer_Line_Oracle_ID__c,Status__c, Total_Amount__c,CurrencyIsoCode,Billing_Address__r.Country__c, '+
        'Course_Arrangement__r.Traning_PriceBook__c,Course_Arrangement__r.Traning_PriceBook__r.Name,Shiping_Address__r.City__c,Customer__r.Owner.FederationIdentifier, '+
        'Training_Amount__c, Training_Course__c, VAT__c,Course_Arrangement__r.Course_Date__c, Course_Arrangement__r.Course_End_Date__c, Course_Arrangement__r.End_Time__c, Course_Arrangement__r.Start_Time__c, '+
        '(Select Name,Course_Arrangement__c,Course_Arrangement__r.Training_Course_Setting__c, '+
        'Course_Arrangement__r.Training_Course_Setting__r.Course_Name__c,Course_Arrangement__r.Training_Course_Setting__r.Course_Name__r.Order_Model__c, '+
        'Course_Register__c, CurrencyIsoCode, Id, Order_Date__c, Trainee__c From Course_Register_Items__r) '+
        'From Course_Register__c Where id =:recordId ';
        SObject obj = Database.query(soqlStr);
        String strDataTime = String.valueOf(system.now().getTime());
        Course_Register__c headData = (Course_Register__c) obj;
        List<Course_Register_Item__c> itemlist = headData.Course_Register_Items__r;

        String accId = headData.Customer__r.Id;
        String incoTerm = '';
        Sales_Program__c authBrand = new Sales_Program__c();
        authBrand = [SELECT Id,Status__c,Customer__c,Incoterm__c,Approval_Status__c 
                    FROM Sales_Program__c 
                    WHERE Customer__c =:accId AND Status__c = 'Active' AND Approval_Status__c = 'Approved' limit 1];
        if(authBrand != null){
            incoTerm = authBrand.Incoterm__c;
        }
        // paramMap.put('ORDER_NUMBER_CRM', headData.Id);
        paramMap.put('ORDER_NUMBER_CRM', headData.Name);
        // system.debug('ORDER_NUMBER_CRM-->'+headData.Id);
        paramMap.put('HEADERID',  getInterfaceId(headData.Name.replace('CR-','')));
        //paramMap.put('HEADERID', '121313');
        paramMap.put('PROMOTION_CODE',  '');
        paramMap.put('RETURN_TO_SUBINVENTORY',  '');
        paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
        paramMap.put('REQUEST_TO_SUBINVENTORY',  '');
        paramMap.put('REQUEST_FROM_SUBINVENTORY', '');
        paramMap.put('CURRENCY_CODE',  headData.CurrencyIsoCode);
        paramMap.put('ORDER_FLAG',  FLAG_YES);
        paramMap.put('SCENE_TYPE',  SCENE_TYPE_TRAINING);

        system.debug('HEADERID-->'+paramMap.get('HEADERID'));
        String customerCode = '';
        String listPriceName = '';
        customerCode = headData.Customer__r.AccountNumber;
        listPriceName = headData.Course_Arrangement__r.Traning_PriceBook__r.Name;
        paramMap.put('CUSTOMER',customerCode);
        paramMap.put('ORG_CODE', ORG_EEG);
        system.debug('OrgCode-->'+paramMap.get('ORG_CODE'));

        //课程日期+时间段（2023-10-16 08：00：00-10：00：00）
        String courseTime = '';
        String startDate = '';
        String endDate = '';
        String startTime = '';
        String endTime = '';
        if(headData.Course_Arrangement__r.Course_Date__c != null){
            startDate = String.valueOf(headData.Course_Arrangement__r.Course_Date__c);
        }
        if(headData.Course_Arrangement__r.Course_End_Date__c != null){
            endDate = String.valueOf(headData.Course_Arrangement__r.Course_End_Date__c);
        }
        if(headData.Course_Arrangement__r.Start_Time__c != null){
            startTime = String.valueOf(headData.Course_Arrangement__r.Start_Time__c).replace('.000Z','');
        }
        if(headData.Course_Arrangement__r.End_Time__c != null){
            endTime = String.valueOf(headData.Course_Arrangement__r.End_Time__c).replace('.000Z','');
        }
        if(String.isNotEmpty(startDate) && String.isNotEmpty(startTime) && String.isNotEmpty(endTime)){
            courseTime = startDate + ' ' + endDate + ' ' + startTime + '-' + endTime;
        }
        paramMap.put('PO_NUMBER', courseTime);

        paramMap.put('SHIPTO', headData.Shiping_Address__r.Customer_Line_Oracle_ID__c);
        paramMap.put('BILLTO', headData.Billing_Address__r.Customer_Line_Oracle_ID__c);
        system.debug('SHIPTO-->'+paramMap.get('SHIPTO'));
        system.debug('BILLTO-->'+paramMap.get('BILLTO'));
        // paramMap.put('ORDER_TYPE', ORDER_TYPE_TRAINING_DE);
        //change 0207
        String resOrderType = CCM_CovertOrderTypeUtil.covertOther2OracleType('Training', '');
        paramMap.put('ORDER_TYPE', resOrderType);
        system.debug('ORDER_TYPE-->'+paramMap.get('ORDER_TYPE'));
        paramMap.put('PRICE_LIST', listPriceName);
        system.debug('PRICE_LIST-->'+paramMap.get('PRICE_LIST'));

        paramMap.put('WARE_HOUSE_ORACLE', ORG_EEG);
        system.debug('WARE_HOUSE_ORACLE-->'+paramMap.get('WARE_HOUSE_ORACLE'));
        paramMap.put('SALES_REP_Code', headData.Customer__r.Owner.FederationIdentifier);
        system.debug('SALES_REP_Code-->'+paramMap.get('SALES_REP_Code'));
        
        paramMap.put('DATE_ORDER', String.valueOf(Date.valueOf(headData.CreatedDate)));
        system.debug('DATE_ORDER-->'+paramMap.get('DATE_ORDER'));
        paramMap.put('CURRENCY_CODE', headData.CurrencyIsoCode);
        paramMap.put('PAYMENT_TERM', headData.Payment_Term__c);
        paramMap.put('FREIGHT_TERM', '');
        //根据Customer查激活的AB上的Inco term--Vince
        paramMap.put('INCO_TERM', incoTerm);

        //Shipping Place根据Icon Term联动
        if(String.isEmpty(incoTerm)){
            paramMap.put('SHPPING_PLACE', '');
        }else if(incoTerm == 'FCA'|| incoTerm == 'EXW'){
            paramMap.put('SHPPING_PLACE', 'Möckmühl');
        }else{
            paramMap.put('SHPPING_PLACE', headData.Shiping_Address__r.City__c);
        }
        paramMap.put('PRICE_DATE', String.valueOf(headData.Course_Arrangement__r.Course_Date__c));
        system.debug('PRICE_DATE-->'+paramMap.get('PRICE_DATE'));
        paramMap.put('FERIGHT_FEE', '');
        paramMap.put('INSURANCE_FEE', '');
        paramMap.put('OTHER_FEE', '');
        
        paramMap.put('CHARGE_FORWEAR_FEE', '');
        paramMap.put('ORDER_STATUS','Submitted');
        paramMap.put('DROPSHIP_NAME','');
        paramMap.put('DROPSHIP_ADDRESS1', '');
        paramMap.put('DROPSHIP_ADDRESS2', '');
        paramMap.put('DROPSHIP_PHONE', '');
        paramMap.put('DROPSHIP_COUNTRY', '');
        paramMap.put('DROPSHIP_ZIP', '');
        paramMap.put('DROPSHIP_STATE', '');
        paramMap.put('DROPSHIP_CITY', '');
        paramMap.put('INSTRUCTION_TO_DSV', '');
        paramMap.put('CARRIER_INFORMATION', '');
        paramMap.put('TOTAL_VALUE', headData.Training_Amount__c);
        paramMap.put('HEADER_DISCOUNT', '');
        paramMap.put('HEADER_DISCOUNT_AMOUNT', '');
        paramMap.put('TOTAL_VALUE_NET', headData.Training_Amount__c);
        paramMap.put('TOTAL_AMOUNT', headData.Total_Amount__c);
        paramMap.put('VAT',  headData.Vat__c);
        String isDropship = 'N';
        paramMap.put('AUTO_BOOK', 'NO');
        paramMap.put('IS_DROPSHIP', isDropship);
        
        paramMap.put('LABOR_FEE', '');
        paramMap.put('PURCHASE_DATE', '');
        paramMap.put('REPAIR_DATE', '');
        paramMap.put('FAILURE_DATE', '');
        paramMap.put('WARRANTY_REMARK', '');

        paramMap.put('ATTRIBUTE1', '');
        paramMap.put('ATTRIBUTE2', '');
        paramMap.put('ATTRIBUTE3', '');
        paramMap.put('ATTRIBUTE4', '');
        paramMap.put('ATTRIBUTE5', '');
        paramMap.put('ATTRIBUTE6', '');
        paramMap.put('ATTRIBUTE7', '');
        paramMap.put('ATTRIBUTE8', '');
        paramMap.put('ATTRIBUTE9', '');
        paramMap.put('ATTRIBUTE10', '');
        paramMap.put('ATTRIBUTE11', '');
        paramMap.put('ATTRIBUTE12', '');
        paramMap.put('ATTRIBUTE13', '');
        paramMap.put('ATTRIBUTE14', '');
        paramMap.put('ATTRIBUTE15', '');
        //封装行请求体
        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        if (itemlist != null && itemlist.size() > 0) {
            for (Course_Register_Item__c item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                // itemMap.put('ORDER_NUMBER_CRM', headData.id);
                itemMap.put('ORDER_NUMBER_CRM', headData.Name);
                // system.debug('ORDER_NUMBER_CRM-->'+itemMap.get('ORDER_NUMBER_CRM'));
                itemMap.put('ORDERLINE_CRM_ID', item.id);
                itemMap.put('LINEID', getInterfaceId(item.Name.replace('CRI-','')));
                system.debug('ORDERLINE_CRM_ID-->'+itemMap.get('ORDERLINE_CRM_ID'));
                itemMap.put('HEADERID', paramMap.get('HEADERID'));
                system.debug('HEADERID-->'+itemMap.get('HEADERID'));
                itemMap.put('CRM_ORDERLINE_NUMBER', item.Id);
                system.debug('CRM_ORDERLINE_NUMBER-->'+itemMap.get('CRM_ORDERLINE_NUMBER'));
                itemMap.put('PRICE_DATE', paramMap.get('PRICE_DATE'));
                itemMap.put('PRODUCT_MODEL', item.Course_Arrangement__r.Training_Course_Setting__r.Course_Name__r.Order_Model__c);
                itemMap.put('ORDER_QUANTITY','1');
                itemMap.put('LIST_PRICE_NAME', paramMap.get('PRICE_LIST'));
                itemMap.put('SERIAL_NUMBER', '');
                itemMap.put('PRODUCT_RATING_STATUS', '');
                itemMap.put('PROMOTION_CODE', '');
                //取头上单价
                itemMap.put('LIST_PRICE', headData.Price_Pcs__c);
                itemMap.put('REQUEST_DATE', paramMap.get('DATE_ORDER'));
                system.debug('REQUEST_DATE-->'+itemMap.get('REQUEST_DATE'));
                itemMap.put('SUBINVENTORY_CODE', 'EGD01');
                itemMap.put('SALES_PRICE', headData.Price_Pcs__c);
                itemMap.put('PROMOTION_DISCOUNT1', 0);//0
                //promotion 头折扣
                itemMap.put('PROMOTION_DISCOUNT2', 0);
                //Modifire的折扣
                itemMap.put('STANDARD_DISCOUNT', 0);
                //经过promotion行折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE1', headData.Price_Pcs__c);//0
                //经过Promotion头折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE2', headData.Price_Pcs__c);
                //最终用户取值价格
                itemMap.put('UNIT_SELLING_PRICE', headData.Price_Pcs__c);
                //0821新增
                itemMap.put('STANDARD_DISCOUNT_TYPE', FLAG_NONE);
                itemMap.put('PROMOTION_DISCOUNT1_TYPE', FLAG_NONE);//new price
                
                itemMap.put('EST_REPLENISH_DATE','');
                itemMap.put('UOM', 'EA');
                itemMap.put('TOTAL_NET_PRICE', headData.Price_Pcs__c);
                itemMap.put('REMARK', item.Trainee__c);
                itemMap.put('SCHEDULE_SHIP_DATE',''); 
                itemMap.put('TRAFFIC_LIGHT', '');

                itemMap.put('ATTRIBUTE1', '');
                itemMap.put('ATTRIBUTE2', '');
                itemMap.put('ATTRIBUTE3', '');
                itemMap.put('ATTRIBUTE4', '');
                itemMap.put('ATTRIBUTE5', '');
                itemMap.put('ATTRIBUTE6', '');
                itemMap.put('ATTRIBUTE7', '');
                itemMap.put('ATTRIBUTE8', '');
                itemMap.put('ATTRIBUTE9', '');
                itemMap.put('ATTRIBUTE10', '');
                itemMap.put('ATTRIBUTE11', '');
                itemMap.put('ATTRIBUTE12', '');
                itemMap.put('ATTRIBUTE13', '');
                itemMap.put('ATTRIBUTE14', '');
                itemMap.put('ATTRIBUTE15', '');
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        return paramMap;
    }

     /**
     * 生成接口序列Id
     */
    public static String getInterfaceId(String autoNo){
        String dateStr = SystemUtils.getCurrentTimeStamp().substring(0,8);
        String resultDateStr = dateStr.substring(2);
        String strResult = resultDateStr + autoNo;
        return strResult;
    }

    public class SyncRes {
        public String Process_Status;
        public String PROCESS_MSG;
    }
    
    public class SyncResult {
        public String ReturnCode;
        public String ReturnMessage;
        public String Process_Status;
        public List<ProcessResult> Process_Result; 
    }

    public class ProcessResult {
        public String SFDC_Id;
        public String Oracle_Id;
        public String Error_Message;
        public String Error_Detail;
    }
}