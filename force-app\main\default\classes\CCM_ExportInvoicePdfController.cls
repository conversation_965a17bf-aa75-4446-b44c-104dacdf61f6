/**************************************************************************************************
 * Name: CCM_ExportInvoicePdfController
 * Object: Invoice__c
 * Purpose: 导出Invoice的PDF格式
 * Author:  (<PERSON>)
 * Create Date: 2023-06-06

 **************************************************************************************************/
public without sharing Class CCM_ExportInvoicePdfController {
    public String recordId { get; set; }
	public CCM_InvoicePdfController.Invoice invoiceobj { get; set; }
    public String pdfContent {get;set;}

    public CCM_ExportInvoicePdfController() {
        System.debug('=========>'+ApexPages.currentPage().getParameters().get('recordId'));
        recordId  = ApexPages.currentPage().getParameters().get('recordId');
        if(String.isNotBlank(recordId)) {
            String bodylist = CCM_InvoicePdfController.GetInvoicePdf(recordId);
            invoiceobj = (CCM_InvoicePdfController.Invoice)JSON.deserialize (bodylist,CCM_InvoicePdfController.Invoice.class);
        }
        init(recordId);
    }

    public void init(String invoiceId) {
        ContentVersion file = [SELECT VersionData FROM ContentVersion WHERE Id = '068QI000005JbH8YAK' LIMIT 1];
        // 检查是否找到了文件
        if (file!= null && file.VersionData!= null) {
            Blob fileData = file.VersionData;
            // 将 Blob 数据转换为 String 类型，假设文件是文本文件
            this.pdfContent = fileData.toString();
        } else {
            System.debug('File not found or has no content.');
        }

        // retrieve pdf content from invoice url

        // List<Invoice_Url__c> urls = [SELECT File_Id__c FROM Invoice_Url__c WHERE Invoice__c = :invoiceId];
        // if(!urls.isEmpty()) {
        //     String fileId = urls[0].File_Id__c;
        //     String connectionId = CCM_LDOX_InterfaceUtil.getConnectionIdByLogin();
        //     // base64 string
        //     String pdfStreamData =  CCM_LDOX_InterfaceUtil.getFileContent(connectionId, fileId);            
        //     // this.pdfContent = pdfStreamData.replace('application/octet-stream', 'application/pdf');
        //     this.pdfContent = 'application/pdf:' + pdfStreamData;
        // }
    }
}