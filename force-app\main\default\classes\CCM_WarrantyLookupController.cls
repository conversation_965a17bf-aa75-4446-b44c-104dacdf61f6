public without sharing class CCM_WarrantyLookupController{
    static String ResdentialConsumer = 'Residential Consumer';
    static String CommercialConsumer = 'Commercial Consumer';
    // @AuraEnabled
    // public static String getCountry(String fifter){
    //     return CCM_GetObjectInfo.getCountry(fifter);
    // }
    public static String aggregateSqlOrder = 'SELECT count(Id) total2 FROM Order WHERE IsDeleted = false ';
    public static String sqlString = 'SELECT Id, Name, ' + 
                                 'OwnerId,' + 
                                 'Consumer__r.DealerView__c,' + 
                                 'Consumer__c,' + 
                                 'Consumer__r.Name,' + 
                                 'Purchase_Place__c,' + 
                                 'Purchase_Date__c,' + 
                                 'Brand__c,' + 
                                 'Master_Product__c,' + 
                                 'Master_Product__r.Name,' + 
                                 'Product__r.Name,' + 
                                 'Serial_Number__c,' + 
                                 'Warranty_Status__c,' + 
                                 'Expiration_Date_New__c ' + 
                                 'FROM Warranty_Item__c ' + 
                                 'WHERE IsDeleted = false ';
    @AuraEnabled
    public static String getOrderInfo(Integer pageNumber, Integer pageSize, String fifter){
        System.debug('filter fifter ==== ' + fifter);
        InitData initD = new InitData();
        User usr = Util.getUserInfo(UserInfo.getUserId());
        Boolean IsPortal = false;
        List<Profile> profiles = [SELECT Id, Name
                                  FROM Profile
                                  WHERE Id = :UserInfo.getProfileId()];
        String profileName = profiles.get(0).Name;
        if (profileName.contains('Community')){
            IsPortal = true;
        }
        if (IsPortal){
            Set<String> consumerIds = new Set<String>();
            consumerIds.add(usr.Contact.AccountId);
            List<Dealer_Share__c> desList = [SELECT Id, RelatedConsumer__c
                                             FROM Dealer_Share__c
                                             WHERE RelatedConsumer__r.Consumer_Status__c <> 'Inactive' AND RelatedConsumer__r.RecordType.Name in ('Residential Consumer', 'Commercial Consumer') And RelatedDealer__c = :usr.Contact.AccountId];
            for (Dealer_Share__c shareItem : desList){
                consumerIds.add(shareItem.RelatedConsumer__c);
            }
            sqlString += ' and (Consumer__c in :consumerIds OR Consumer__r.DealerView__c = true)';
        }
        System.debug('IsPortal ==== ' + IsPortal);
        String filterCondition = getFilterCondition(fifter);
        sqlString += filterCondition;
        System.debug('search After WHERE ==== ' + sqlString);
        initD.sqlQuery = sqlString;
        try{
            List<Warranty_Item__c> allList = Database.query(sqlString);
            List<WarrantyData> wrappers = new List<WarrantyData>();
            initD.querySize = allList.size();
            initD.queryResult = allList;
            if (allList != null && allList.size() > 0){
                Integer totalSize = allList.size();
                Integer pageLength = (pageNumber - 1) * pageSize + pageSize - 1 < totalSize ? (pageNumber - 1) * pageSize + pageSize - 1:totalSize - 1;
                Integer initPageNumber = (pageNumber - 1) * pageSize < totalSize ? (pageNumber - 1) * pageSize : 1;
                for (Integer i = (pageNumber - 1) * pageSize; i <= pageLength; i++){
                    WarrantyData warrantyData = new WarrantyData();
                    warrantyData.Brand = allList[i].Brand__c;
                    warrantyData.Customer = allList[i].Consumer__r != null ? allList[i].Consumer__r.Name : null;
                    warrantyData.ExipirationDate = allList[i].Expiration_Date_New__c <> null ? String.ValueOf(allList[i].Expiration_Date_New__c) : null;
                    warrantyData.MasterProduct = allList[i].Master_Product__r != null ? allList[i].Master_Product__r.Name : null;
                    warrantyData.ModelNumber = allList[i].Product__r != null ? allList[i].Product__r.Name : null;
                    warrantyData.PurchaseDate = allList[i].Purchase_Date__c <> null ? String.ValueOf(allList[i].Purchase_Date__c) : null;
                    warrantyData.PurchasePlace = allList[i].Purchase_Place__c;
                    warrantyData.SerialNumber = allList[i].Serial_Number__c;
                    warrantyData.WarrantyStatus = allList[i].Warranty_Status__c;
                    wrappers.add(warrantyData);
                }
            }
            if (wrappers != null && wrappers.size() > 0){
                //算出总量
                Integer countOrder = wrappers.size();
                initD.totalRecords = countOrder;
                initD.currentData = wrappers;
            }
            initD.isSuccess = true;
        } catch (Exception e){
            initD.isSuccess = false;
            initD.errorMessage = e.getMessage();
            initD.errorLine = String.valueOf(e.getLineNumber());
        }
        return JSON.serialize(initD);
    }
    public static String getFilterCondition(String filterString){
        //根据fifter筛选
        System.debug('filterString filterString ==== ' + filterString);
        String ConditionString = '';
        FilterWrapper filters = (FilterWrapper)JSON.deserialize(filterString, FilterWrapper.class);
        System.debug('filters ==== ' + filters);
        if (String.isNotBlank(filters.UserType)){
            ConditionString += ' AND Consumer__r.RecordType.Name =\'' + filters.UserType + '\'';
        }
        if (String.isNotBlank(filters.City)){
            ConditionString += ' AND Consumer__r.ShippingCity like  \'%' + filters.City + '%\'';
        }
        if (String.isNotBlank(filters.CountryName)){
            ConditionString += ' AND Consumer__r.ShippingCountry like  \'%' + filters.CountryName + '%\'';
        }
        if (String.isNotBlank(filters.EmailAddress)){
            if (filters.UserType == CommercialConsumer){
                //如果是Commercial User, 查Emial__c    在新建Conmercial User时，会将Fleet Manager的邮箱反写进Commercial User的邮箱
                ConditionString += ' AND Consumer__r.Email__c like  \'%' + filters.EmailAddress + '%\'';
            }
            if (filters.UserType == ResdentialConsumer){
                ConditionString += ' AND Consumer__r.PersonEmail like  \'%' + filters.EmailAddress + '%\'';
            }
        }
        if (String.isNotBlank(filters.LastCompanyName)){
            if (filters.UserType == CommercialConsumer){
                ConditionString += ' AND Consumer__r.Last_name__c like  \'%' + filters.LastCompanyName + '%\'';
            }
            if (filters.UserType == ResdentialConsumer){
                ConditionString += ' AND Consumer__r.LastName like  \'%' + filters.LastCompanyName + '%\'';
            }
        }
        if (String.isNotBlank(filters.ModelNumber)){
            ConditionString += ' AND Product__r.Name like  \'%' + filters.ModelNumber + '%\'';
        }
        if (String.isNotBlank(filters.SearialNumber)){
            ConditionString += ' AND Serial_Number__c like  \'%' + filters.SearialNumber + '%\'';
        }
        ConditionString += ' LIMIT 5000 ';
        return ConditionString;
    }

    @AuraEnabled
    public static String getCustomerSalesChannel(){
        String salesChannel = '';
        User u = [SELECT Contact.Account.Sales_Channel__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        salesChannel = u.Contact.Account.Sales_Channel__c;
        if(salesChannel.containsIgnoreCase('distributor')) {
            return 'distributor';
        }
        else if(salesChannel.containsIgnoreCase('dealer')) {
            return 'dealer';
        }
        return salesChannel;
    }

    public class FilterWrapper{
        public String UserType;
        public String EmailAddress;
        // public String Country;
        public String CountryName;
        public String City;
        public String SearialNumber;
        public String LastCompanyName;
        public String ModelNumber;
    }
    public class WarrantyData{
        public String Customer;
        public String PurchasePlace;
        public String PurchaseDate;
        public String Brand;
        public String MasterProduct;
        public String ModelNumber;
        public String SerialNumber;
        public String WarrantyStatus;
        public String ExipirationDate;
    }
    public class InitData{
        public List<WarrantyData> currentData;
        public Integer totalRecords;
        public Boolean isSuccess;
        public String errorMessage;
        public String errorLine;
        public String sqlQuery;
        public Integer querySize;
        public List<Warranty_Item__c> queryResult;
        public InitData(){
            this.currentData = new List<WarrantyData>();
            this.totalRecords = 0;
            this.isSuccess = true;
            this.errorMessage = '';
        }
    }
}