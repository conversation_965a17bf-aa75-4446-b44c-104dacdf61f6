<aura:component description="CCM_Community_OrderInformation" extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout" controller="CCM_Community_OrderInformationCtl">
    <!-- implements="forceCommunity:availableForAllPageTypes" -->
    <aura:attribute name="allData" type="List" default="[]"/>
    <aura:attribute name="currentData" type="List" default="[]"/>
    <aura:attribute name="cartNum" type="Integer" default="0"/>
    <aura:attribute name="shippingNum" type="Integer" default="0"/>
    <aura:attribute name="completedNum" type="Integer" default="0"/>
    <aura:attribute name="inProcessInvoiceNum" type="Integer" default="0"/>
    <aura:attribute name="issuedInvoiceNum" type="Integer" default="0"/>
    <aura:attribute name="paidInvoiceNum" type="Integer" default="0"/>
    <aura:attribute name="orderInvoiceVal" type="String" default=""/>
    <aura:attribute name="orderType" type="String" default=""/>
    <aura:attribute name="fromDate" type="String" default=""/>
    <aura:attribute name="endDate" type="String" default=""/>
    <aura:attribute name="status" type="List" default="[]"/>
    <aura:attribute name="orderNum" type="String" default=""/>
    <aura:attribute name="customerPo" type="String" default=""/>
    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="historyOrders" type="List" default="[]"/>
    <aura:attribute name="objectName" type="String" default=""/>
    <aura:attribute name="isPlaceOrder" type="Boolean" default="true"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currencySymbol" type="String" default="USD"/>
    <aura:attribute name="boolIsTier2User" type="Boolean" default="false" />
    <aura:attribute name="boolIsAltaQuip" type="Boolean" default="false" />
    <aura:attribute name="columnsSN" type="List" default="[]"/>
    <aura:attribute name="currentSNData" type="List" default="[]"/>
    <aura:attribute name="snFlag" type="Boolean" default="true"/>


    <!-- sn弹框参数 -->
    <aura:attribute name="modalFlag" type="Boolean" default="false" />
    <aura:attribute name="orderNo" type="String" default="" />
    <aura:attribute name="invoiceNo" type="String" default="" />
    <aura:attribute name="containerNo" type="String" default="" />
    <aura:attribute name="shipmentNo" type="String" default="" />
    <aura:attribute name="exportType" type="String" default="EXCEL" />
    <aura:attribute name="exportTypeList" type="List" default="['EXCEL', 'CSV', 'PDF']"/>
    <aura:attribute name="exportData" type="List" default="[]"/>

    

    <!-- tab页 -->
    <aura:attribute name="tabId" type="String" default="Order" />
    <aura:handler name="change" value="{!v.tabId}" action="{!c.handleChange}"/>

    <!-- 计时器 -->
    <aura:attribute name="timer" type="Object" default="" />

    <!-- ORG Code: CCA -->
    <aura:attribute name="boolIsCCA" type="Boolean" default="false"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="onGoto" event="c:CCM_Community_OrderFilterEvt" action="{!c.doSearch}"/>
    <!-- 分页相关属性：页坷〝毝页记录数〝总记录数 -->
    <aura:attribute name="pageNumber" type="String" default="1"/>
    <aura:attribute name="pageCount" type="String" default="10" />
    <aura:attribute name="totalRecords" type="String" default="0" />
    <aura:handler name="pageChange" event="c:CCM_ListPageFooter_PageChangeEvt" action="{!c.pageChange}" />
    <aura:handler name="pageCountChange" event="c:CCM_ListPageFooter_PageCountChangeEvt" action="{!c.pageCountChange}" />

    <!-- 父子传参 -->
    <aura:attribute name="requestValue" type="String" default=""/>
    <aura:attribute name="requestType" type="String" default=""/>

    <aura:attribute name="currentUserId" type="String" default=""/>

    <!-- 控制input -->
    <aura:attribute name="isEditOrder" type="Boolean" default="false"/>
    <aura:attribute name="isEditInvoice" type="Boolean" default="false"/>
    <aura:attribute name="isEditContainer" type="Boolean" default="false"/>
    <aura:attribute name="isEditShipment" type="Boolean" default="false"/>
    <aura:attribute name="isError" type="Boolean" default="false"/>

    <aura:attribute name="showDoubleConfirmModel" type="Boolean" default="false" />
    <aura:attribute name="deleteId" type="String" />
    <aura:attribute name="isDeleteDisabled" type="Boolean" default="false" />

    <div class="slds-grid slds-grid_align-space">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
        <div class="slds-grid slds-grid--align-center slds-size--1-of-1">
            <div class="slds-col slds-wrap ccm_gridSpace leftCon">
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="{!$Label.c.CCM_OrderInformation}">
                                            <span><strong>{!$Label.c.CCM_OrderInformationCurrentYear}</strong></span>
                                     </span>
                                    <a href="/s/order-filter" class="more">{!$Label.c.CCM_More}<lightning:icon iconName="utility:chevronright" size="xx-small"/></a>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-grid slds-wrap slds-grid_align-space">
                            <c:CCM_Community_BigNumberAndIcon icon="utility:cart" text="{!$Label.c.CCM_OrderProcessing}" num="{!v.cartNum}" />
                            <c:CCM_Community_BigNumberAndIcon icon="utility:location" text="{!$Label.c.CCM_PartialShipment}" num="{!v.shippingNum}" />
                            <c:CCM_Community_BigNumberAndIcon icon="utility:paste" text="{!$Label.c.CCM_ShipComplete}" num="{!v.completedNum}"   borderRight="false"/>
                        </div>
                    </div>
                </article>
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="{!$Label.c.CCM_InvoiceInformation}">
                                            <span><strong>{!$Label.c.CCM_InvoiceInformation}</strong></span>
                                     </span>
                                    <a href="/s/Invoice?0.invRecordType=salesorder" class="more">{!$Label.c.CCM_More}<lightning:icon iconName="utility:chevronright" size="xx-small"/></a>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-grid slds-wrap slds-grid_align-space">
                            <c:CCM_Community_BigNumberAndIcon icon="utility:multi_select_checkbox" text="{!$Label.c.CCM_Issued}" num="{!v.issuedInvoiceNum}" />
                            <c:CCM_Community_BigNumberAndIcon icon="utility:edit_form" text="{!$Label.c.CCM_Open}" num="{!v.inProcessInvoiceNum}" />
                            <c:CCM_Community_BigNumberAndIcon icon="utility:moneybag" text="{!$Label.c.CCM_Closed}" num="{!v.paidInvoiceNum}"   borderRight="false"/>
                        </div>
                    </div>
                </article>
                <!-- <c:CCM_Community_OrderInvoiceFilterCmp isShowRange="false"/> -->
                
            </div>
            <div class="slds-col slds-wrap rightCon">
                <div class="slds-item_detail">
                    <c:CCM_PromotionBanner />
                </div>
                <div class="tabs-wrap">
                    <lightning:tabset selectedTabId="{!v.tabId}">
                        <!-- Order History -->
                        <lightning:tab label="{!$Label.c.CCM_OrderHistory}" id="Order">
                            <div class="slds-grid">
                                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                    <div class="slds-media__body">
                                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                            <span class="section-header-title slds-p-horizontal--small slds-truncate" title="{!$Label.c.CCM_OrderHistory}">&nbsp;</span>
                                        </h2>
                                    </div>
                                </header>
                            </div>
                            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                                <div class="slds-wrap">
                                    <div class="slds-box slds-color__background_gray-1">
                                        <c:CCM_DataTable columns="{!v.columns}" data="{!v.currentData}"/>
                                    </div>
                                    <aura:renderIf isTrue="{!v.currentData.length > 0}" >
                                        <c:CCM_ListPageFooter totalRecords="{!v.totalRecords}" pageCount="{!v.pageCount}" pageNumber="{!v.pageNumber}"/>
                                    </aura:renderIf>
                                </div>
                            </div>
                        </lightning:tab>
                        <!-- SN Export Request -->
                        <lightning:tab label="{!$Label.c.CCM_SNExportRequest}" id="SN">
                            <div class="slds-grid">
                                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                    <div class="slds-media__body">
                                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                                title="{!$Label.c.CCM_SNExportRequest}">&nbsp;
                                            </span>
                                        </h2>
                                    </div>
                                </header>
                            </div>
                            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                                <div class="slds-wrap">
                                    <div class="slds-box slds-color__background_gray-1">
                                        <c:CCM_DataTable columns="{!v.columnsSN}" data="{!v.currentSNData}"/>
                                    </div>
                                    <aura:renderIf isTrue="{!v.currentSNData.length > 0}">
                                        <c:CCM_ListPageFooter totalRecords="{!v.totalRecords}" pageCount="{!v.pageCount}" pageNumber="{!v.pageNumber}"/>
                                    </aura:renderIf>
                                    <c:ccmExcelTools aura:id="excelTools" style="display: none" type="export" excelTitle="SN List" excelData="{!v.exportData}"></c:ccmExcelTools>
                                </div>
                            </div>
                        </lightning:tab>
                    </lightning:tabset>
                    <div class="buttonGroup" role="group">
                        <button class="slds-button slds-button_neutral" onclick="{!c.orderBatchUpload}" style="margin-right:16px;">
                            {!$Label.c.CCM_OrderBatchUpload}
                        </button>
                        <button class="slds-button slds-button_neutral slds-m-right--small" onclick="{!c.placeOrder}">
                            <lightning:icon iconName="utility:add" size="xx-small" style="margin-right:5px;" />
                            {!$Label.c.CCM_PlaceOrder}
                        </button>
                        <!-- 新增SN导出按钮 -->
                        <button class="slds-button slds-button_neutral" onclick="{!c.openSnPopup}">
                            {!$Label.c.CCM_ExportSNList}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- export SN 弹框 -->
        <!-- aura不能嵌套lwc,最好封装aura组件 -->
        <!-- <c:ccmExportSNPopup style="margin-right: 100px;" showFlag="{!v.modalFlag}" onclose="{!c.closeSnPopup}" sizeStyle="width: 850px;"></c:ccmExportSNPopup> -->
        <aura:if isTrue="{!v.modalFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 54rem !important; max-width: 60rem !important; height:auto !important; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_ExportSNList}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <aura:if isTrue="{!v.isError}">
                                    <p class="show-error-text">
                                        <span>{!$Label.c.CCM_SNApplyRequiredTips}</span>
                                    </p>
                                </aura:if>
                                <!-- Order No. -->
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                    <!-- 相同组件用aura.action只能识别到第一个引用，大坑！！！ -->
                                    <c:CCM_Community_LookUp fieldName="{!$Label.c.CCM_OrderNo}"
                                                            selectedValue="{!v.orderNo}"
                                                            aura:id="orderNo"
                                                            class="content-item-right"
                                                            orderMethod="{!c.getOrderFromLookup}"
                                                            invoiceMethod="{!c.getInvoiceFromLookup}"
                                                            isDisabled="{!v.isEditOrder}"
                                                            userId="{!v.currentUserId}"
                                    />
                                </div>
                                <!-- Invoice No. -->
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                    <c:CCM_Community_LookUp fieldName="{!$Label.c.CCM_InvoiceNo}"
                                                            selectedValue="{!v.invoiceNo}"
                                                            aura:id="invoiceNo"
                                                            class="content-item-right"
                                                            isDisabled="{!v.isEditInvoice}"
                                                            userId="{!v.currentUserId}"
                                    />
                                </div>
                                <!-- Container No. -->
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                    <!-- <c:CCM_Community_LookUp fieldName="Container No."
                                                            selectedValue="{!v.containerNo}"
                                                            aura:id="containerNo"
                                                            class="content-item-right"
                                                            parentAction="{!c.getInfoFromLookup}"
                                    /> -->
                                    <lightning:input aura:id="containerNo" label="{!$Label.c.CCM_ContainerNo}" value="{!v.containerNo}" class="content-input" onchange="{!c.containerChange}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255" disabled="{!v.isEditContainer}"/>
                                </div>
                                <!-- Shipment No. -->
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small content-item">
                                    <c:CCM_Community_LookUp fieldName="{!$Label.c.CCM_ShipmentNo}"
                                                                selectedValue="{!v.shipmentNo}"
                                                                aura:id="shipmentNo"
                                                                class="content-item-right"
                                                                shipmentMethod="{!c.getShipmentFromLookup}"
                                                                isDisabled="{!v.isEditShipment}"
                                                                userId="{!v.currentUserId}"
                                        />
                                </div>
                                <div class="content-item content-item-flex">
                                    <lightning:select label="{!$Label.c.CCM_ExportType}" value="{!v.exportType}">
                                        <aura:iteration items="{!v.exportTypeList}" var="exportType">
                                            <option text="{!exportType}" value="{!exportType}"></option>
                                        </aura:iteration>
                                    </lightning:select>
                                </div>
                            </div>
                        </div>
                        <footer class="slds-modal__footer">
                            <!-- <button class="slds-button slds-button_neutral" onclick="{!c.closeModal}">Cancel</button>
                            <button class="slds-button slds-button_brand" onclick="{!c.saveRecord}">Save</button> -->
                            <!-- <lightning:button class="" variant="brand"  label="demo" title="Apply Request" onclick="{!c.getOrderFromLookup}"/> -->
                            <lightning:button class="" variant="brand"  label="{!$Label.c.CCM_ApplyRequest}" title="{!$Label.c.CCM_ApplyRequest}" onclick="{!c.applyRequest}"/>
                            <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.cancelEvent}"/>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>

        <aura:if isTrue="{!v.showDoubleConfirmModel}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 54rem !important; max-width: 60rem !important; height:auto !important; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.hideDoubleConfirmModel}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_DoubleConfirmMsg}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <aura:if isTrue="{!not(v.isDeleteDisabled)}">
                                        <lightning:button class="" variant="brand" label="{!$Label.c.CCM_Yes}" title="{!$Label.c.CCM_Yes}" onclick="{!c.confirmDelete}" disabled="{!v.isDeleteDisabled}"/>
                                        <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_AnswerNo}" title="{!$Label.c.CCM_AnswerNo}" onclick="{!c.confirmCancel}" />
                                    <aura:set attribute="else">
                                        <span>{!$Label.c.CCM_DeleteInProgress}</span>
                                    </aura:set>
                                </aura:if>
                            </div>
                        </div>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
    </div>
</aura:component>