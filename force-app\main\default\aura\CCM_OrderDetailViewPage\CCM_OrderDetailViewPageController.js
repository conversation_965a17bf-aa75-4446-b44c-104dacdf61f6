({
    doInit: function(component, event, helper){
        component.set('v.currencySymbol', $A.get("$Locale.currencyCode"));
        component.set('v.isDE', $A.get("$Label.c.CCM_Translate"));
        //Community User展示的进度条
        var pathDataForInsideSales = [
            {label: $A.get("$Label.c.Order_NewOrder"), icon: 'edit_form'},
            {label: $A.get("$Label.c.Order_PendingReview"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.Order_ReviewinProcess"), icon: 'entitlement'},
            {label: $A.get("$Label.c.CCM_OrderProcessing"), icon: 'privately_shared'},
            {label: $A.get("$Label.c.CCM_PartialShipment"), icon: 'travel_and_places'},
            {label: $A.get("$Label.c.CCM_ShipmentComplete"), icon:'success'}
        ];
        var pathDataForSalesRep = [
            {label: $A.get("$Label.c.Order_NewOrder"), icon: 'edit_form'},
            {label: $A.get("$Label.c.CCM_Submitted"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.Order_ReviewinProcess"), icon: 'entitlement'},
            {label: $A.get("$Label.c.CCM_OrderProcessing"), icon: 'privately_shared'},
            {label: $A.get("$Label.c.CCM_PartialShipment"), icon: 'travel_and_places'},
            {label: $A.get("$Label.c.CCM_ShipmentComplete"), icon:'success'}
        ];

        // shipment table
        component.set('v.shipmentColumns', [
            {label: $A.get("$Label.c.Order_ProductDescription"), fieldName: 'ProductDescription'},
            {label: $A.get("$Label.c.Order_Model"), fieldName: 'Model'},
            {label: $A.get("$Label.c.Order_ItemQtyinShipment"), fieldName: 'ItemQtyInShipment'},
            {label: $A.get("$Label.c.Order_ShipSet"), fieldName:'ShipSet'},
        ]);

        // attachment table
        component.set('v.attachmentColumns', [
            {   
                label: $A.get("$Label.c.CCM_Action"),
                width: '60px',
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${RowData}",
                            variant:"bare",
                            iconName:"utility:preview",
                            alternativeText:"download",
                            onclick: component.getReference('c.doView')
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${RowData}",
                            variant:"bare",
                            iconName:"utility:arrow_bottom",
                            alternativeText:"download",
                            onclick: component.getReference('c.doDownload')
                        }
                    },
                ]
            },
            {label: $A.get("$Label.c.CCM_AttachmentName"), fieldName: 'AttachmentName'},
            {label: $A.get("$Label.c.CCM_AttachmentType"), fieldName: 'AttachmentType'},
            {label: $A.get("$Label.c.CCM_Date"), fieldName:'AttachMentDate'},
        ]);

        var recordIdByUrl = helper.getUrlParameter('recordId');
        if (recordIdByUrl) {
            component.set('v.recordId', recordIdByUrl);
        }
        let recordId = component.get('v.recordId');
        component.set('v.isBusy', true);
        var action = component.get("c.getData");
        action.setParam('recordId', recordId);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(results), 'results==========');
                if(results){
                    component.set('v.currencySymbol', results.OrderData.currencyCode);
                    // 流程条
                    if (results.UserType === 'SalesRep') {
                        component.set('v.isInsideSales', false);
                        component.set('v.processData', pathDataForSalesRep);
                    } else {
                        component.set('v.isInsideSales', true);
                        component.set('v.processData', pathDataForInsideSales);
                        helper.queryAlertMessage(component, results.OrderData.CustomerNumber);
                    }

                    // order 1阶段状态直接到Order Processing
                    // 流程状态
                    if (results.CurrentStep < 2) {
                        component.set('v.currentStep', 4);
                    } else {
                        component.set('v.currentStep', results.CurrentStep);
                    }
                    const attachMentData = [];
                    if (results.AttachMentData.length) {
                        results.AttachMentData.forEach((item)=>{
                            attachMentData.push(
                                {
                                    Id: item.Id,
                                    AttachmentType: item.AttachmentType,
                                    AttachmentName: item.AttachmentName,
                                    AttachMentDate: item.AttachMentDate,
                                    RowData: {
                                        Id: item.Id,
                                        AttachmentType: item.AttachmentType,
                                    },
                                }
                            )
                            item.rowData = {
                                Id: item.Id,
                                Type: item.AttachmentType,
                            }
                        })
                    }
                    if (results.OrderItemData.length) {
                        results.OrderItemData.forEach((item)=>{
                            item.Discount = Number(item.Discount*100).toFixed(1) + '%';
                        })
                    }
                    component.set('v.basicInformation', results.OrderData);
                    component.set('v.shipmentInfo', results.ShipmentInfo);
                    component.set('v.attachment', attachMentData);
                    component.set('v.orderItemList', results.OrderItemData);
                    component.set('v.userType', results.UserType);
                    // 判断是否存在dropaddress
                    let DropshipAddress = results.OrderData.DropshipAddress;
                    if (DropshipAddress.City || DropshipAddress.CompanyName || DropshipAddress.Country || DropshipAddress.PostCode || DropshipAddress.Street) {
                        component.set('v.onlyShowDropAddress', true);
                    } else {
                        component.set('v.onlyShowDropAddress', false);
                    }
                    helper.refreshLight(component, false);
                }
            } else {
                var errors = response.getError();
                console.log('errors:',errors);
                component.set('v.isBusy', false);
            }
        });
        component.set('v.isBusy', false);
        $A.enqueueAction(action);
    },
    doBack: function(component) {
        let url = window.location.origin + $A.get("$Label.c.CCM_Back_To_Order_List_Relative_Link");
        window.open(url, "_self");
    },
    doView: function(component, event, helper){
        var rowData = event.getSource().get('v.value');
        let url = '';
        let isDE = component.get('v.isDE');
        console.log(isDE, 'isDE----------');
        console.log(rowData, 'rowData========');
        if (rowData.AttachmentType === 'Credit') {
            if (isDE == 'DE') {
                url = '/apex/OrderPDFNoteViewDE?recordId=' + rowData.Id;
            } else {
                url = '/apex/OrderPDFNoteView?recordId=' + rowData.Id;
            }
        } else {
            if (isDE == 'DE') {
                url = '/apex/OrderPDFViewDE?recordId=' + rowData.Id;
            } else {
                url = '/apex/OrderPDFView?recordId=' + rowData.Id;
            }
        }
        window.open(url);
    },
    doDownload: function(component, event, helper){
        var rowData = event.getSource().get('v.value');
        let url = '';
        let isDE = component.get('v.isDE');
        console.log(isDE, 'isDE----------');
        console.log(rowData, 'rowData========');
        if (rowData.AttachmentType === 'Credit') {
            if (isDE == 'DE') {
                url = '/apex/OrderPDFNoteViewDE?recordId=' + rowData.Id;
            } else {
                url = '/apex/OrderPDFNoteView?recordId=' + rowData.Id;
            }
        } else {
            if (isDE == 'DE') {
                url = '/apex/OrderPDFViewDE?recordId=' + rowData.Id;
            } else {
                url = '/apex/OrderPDFView?recordId=' + rowData.Id;
            }
        }
        window.open(url);
    },
    showToolList: function(component, event, helper){
        var id = event.currentTarget.getAttribute('id');
        var expanded = event.currentTarget.getAttribute('data-expanded');
        if(expanded == 'true'){
            document.getElementById('row' + id).style.display = 'none'
            event.currentTarget.setAttribute('data-expanded', false);
        }else{
            document.getElementById('row' + id).style.display = 'table-row';
            event.currentTarget.setAttribute('data-expanded', true);
        }
    },
    showShipToolList: function(component, event, helper){
        var id = event.currentTarget.getAttribute('id');
        var allIndex = event.currentTarget.getAttribute('data-id');
        var expanded = event.currentTarget.getAttribute('data-expanded');
        if(expanded == 'true'){
            document.getElementById('ship' + id + allIndex).style.display = 'none'
            event.currentTarget.setAttribute('data-expanded', false);
        }else{
            document.getElementById('ship' + id + allIndex).style.display = 'table-row';
            event.currentTarget.setAttribute('data-expanded', true);
        }
    },
    // 刷新红绿灯
    refreshTrafficLight : function(component, event, helper) {
        console.log('刷新红绿灯---------------');
        helper.refreshLight(component, true);
    },

    handleSort: function(component, event, helper) {
        let fieldName = event.currentTarget.dataset.field;
        let sortDirection = component.get("v.sortDirection");
        let sortedBy = component.get("v.sortedBy");
        
        if (sortedBy === fieldName) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortDirection = 'asc';
        }
        
        component.set("v.sortedBy", fieldName);
        component.set("v.sortDirection", sortDirection);
        helper.sortData(component, fieldName, sortDirection);
    }
})