public without sharing class CCM_Community_AccountBalanceTabCtl {
    // 获取 customer 关联的 accounting balance
    @AuraEnabled
    public static String searchBalance(
                                    Integer pageNumber
                                    , Integer pageSize
                                    , String invoiceDateRange
                                    , String memoType
                                    , String balanceStatus
                                    , String dateFrom
                                    , String dateTo
                                    ){
        // map， 返回信息封装
        Map<String,Object> result = new Map<String,Object>();
        // 最终过滤掉，满足条件的 invoice 集合
        List<BalanceRow> lstReturnBalanceRows = new List<BalanceRow>();
        // 返回值 - table data
        List<BalanceRow> tableData = new List<BalanceRow>();
        try {
            // 查找 account 相关的信息
            User currentUser = [
                                SELECT Id
                                , ContactId
                                , Contact.AccountId
                                , Contact.Account.CurrencyIsoCode
                                FROM User WHERE Id != NULL 
                                AND Id = :UserInfo.getUserId()
                            ];
            // 判断 currentUser 的类型
            String customerId = currentUser.Contact.AccountId;
            String strCurrencyIsoCode = currentUser.Contact.Account.CurrencyIsoCode;

            // 如果没有筛选条件，则全部查询
            String soqlBalanceQuery = ''
                                    + ' SELECT Id,Name,CurrencyIsoCode'
                                    + ' ,Invoice_Number__c'
                                    + ' ,Invoice_Date__c'
                                    + ' ,Gl_Date__c'
                                    + ' ,Due_Date__c'
                                    + ' ,Amt_Original__c'
                                    + ' ,Amt_Due_Remaining__c'
                                    + ' ,Invoice__c, Credit_note_for_Invoice__c, Invoice__r.Order__r.Order_Date__c, Invoice__r.Credit_note_for_Invoice__c, Invoice__r.Scene_Type__c'
                                    + ' FROM Accounting_Balance__c WHERE Id != NULL'
                                    + ' AND Customer__c = :customerId';
            //
            List<String> lstInvoiceType = new List<String>();
            lstInvoiceType.add('INV');
            lstInvoiceType.add('CREDIT');
            soqlBalanceQuery += ' AND Credit_note_for_Invoice__c IN :lstInvoiceType';

            // 根据 invoice date ，筛选过去的时间段的 balance
            // 要有可拓展性
            if (String.isNotBlank(invoiceDateRange)) {
                Integer intLastDays = 0;
                if (invoiceDateRange == 'last30' || invoiceDateRange == 'last90') {
                    if(invoiceDateRange == 'last30') {
                        intLastDays = 30;
                    }
                    else if(invoiceDateRange == 'last90') {
                        intLastDays = 90;
                    }
                    soqlBalanceQuery += ' AND Invoice_Date__c >= LAST_N_DAYS:' + intLastDays;
                } else if (invoiceDateRange == 'last366') {
                    soqlBalanceQuery += ' AND Invoice_Date__c = LAST_YEAR';
                }
            }
            if (String.isNotBlank(balanceStatus)) {
                soqlBalanceQuery += ' AND Balance_Status__c = \'' + balanceStatus + '\'';
            }

            if(String.isNotBlank(dateFrom)) {
                Date dateFromFilter = Date.valueOf(dateFrom);
                soqlBalanceQuery += ' AND Invoice_Date__c >= :dateFromFilter';
            }

            if(String.isNotBlank(dateTo)) {
                Date dateToFitler = Date.valueOf(dateTo);
                soqlBalanceQuery += ' AND Invoice_Date__c <= :dateToFitler';
            }

            soqlBalanceQuery += ' ORDER BY Lastmodifieddate DESC LIMIT 49999';

            System.debug('soqlBalanceQuery:' + soqlBalanceQuery);
            // 汇总
            // account balance ， debit memo，credit memo
            Decimal dcmAccountBalance = 0.00;
            Decimal dcmDebitMemo = 0.00;
            Decimal dcmCreditMemo = 0.00;

            List<Accounting_Balance__c> lstBalance = Database.query(soqlBalanceQuery);
            System.debug('soqlBalanceQuery:' + soqlBalanceQuery);
            System.debug('lstBalance:' + lstBalance);
            if (lstBalance != null && lstBalance.size() > 0) {
                for (Accounting_Balance__c ab : lstBalance) {
                    BalanceRow bRow = new BalanceRow();
                    bRow.balanceId = ab.Id;
                    bRow.currencyIsoCode = ab.CurrencyIsoCode;
                    bRow.invoiceNumber = ab.Invoice_Number__c;
                    bRow.invoiceDate = String.valueOf(ab.Invoice_Date__c);
                    bRow.giDate = String.valueOf(ab.Gl_Date__c);
                    bRow.orderDate = String.valueOf(ab.Invoice__r.Order__r.Order_Date__c);
                    bRow.dueDate = String.valueOf(ab.Due_Date__c);
                    bRow.originalAmount = ab.Amt_Original__c;
                    bRow.remainingDueAmount = ab.Amt_Due_Remaining__c;
                    bRow.creditNote = ab.Credit_note_for_Invoice__c;
                    bRow.invoiceType = ab.Invoice__r.Credit_note_for_Invoice__c;
                    bRow.sceneType = ab.Invoice__c + '@' + ab.Invoice__r.Scene_Type__c;
                    if (bRow.creditNote == 'INV') {
                        dcmDebitMemo += ab.Amt_Original__c;
                        if (memoType == 'INV') {
                            lstReturnBalanceRows.add(bRow);
                        }
                    } else if (bRow.creditNote == 'CREDIT') {
                        dcmCreditMemo += ab.Amt_Original__c;
                        if (memoType == 'CREDIT') {
                            lstReturnBalanceRows.add(bRow);
                        }
                    }
                }
                // account balance 汇总
                dcmAccountBalance = dcmDebitMemo + dcmCreditMemo;
            }

            Integer min = ((Integer)pageNumber - 1) * pageSize;
            Integer max = (Integer)pageNumber * pageSize -1;
            for (Integer i = min ; i <= max; i++ ) {
                if (i < lstReturnBalanceRows.size()) {
                    tableData.add(lstReturnBalanceRows.get(i));
                }
            }

            result.put('tableData', tableData);
            result.put('totalData', lstReturnBalanceRows.size());

            result.put('accountBalance', dcmAccountBalance);
            result.put('debitMemo', dcmDebitMemo);
            result.put('creditMemo', dcmCreditMemo);
            result.put('currencyCode', strCurrencyIsoCode);
            
            return JSON.serialize(result);
        } catch (Exception e) {
            System.debug('stack:');
            throw new AuraHandledException(e.getMessage());
        }
    }

    // Account Balance 每一行的详细信息 -》 table 展示内容
    public class BalanceRow {
        public String balanceId;
        public String currencyIsoCode;
        public String creditNote;
        public String invoiceNumber;
        public String invoiceDate;
        public String giDate;
        public String orderDate;
        public String dueDate;
        public Decimal originalAmount;
        public Decimal remainingDueAmount;
        public String invoiceType;
        public String sceneType;
    }
}