<template>
    <!-- 页面遮罩 -->
    <!-- 等待加载动画 -->
    <template if:true={showSpinner}>
        <div class="spinner-wrapper">
            <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
        </div>
    </template>
    <div>
        <div style="border: 1px solid #e1e1e1;border-radius: 5px;margin:20px 20%;padding: 8px;">
            <div class="slds-section slds-is-open" style="margin-bottom: 25px;">
                <p class="slds-section__title slds-theme_shade tb_section"
                   style="background-color:rgb(75 145 109);color:white;padding-left:8px;">{label.LentoutAgreement}
                </p>
            </div>
            <div style="display: flex;justify-content: space-between;">
                <div style="width: 75%;display:flex;flex-wrap:nowrap;">
                    <div style="width:40%;">
                        <div style="font-size: 13px;font-weight:bold;">{label.LentoutNumber}</div>
                        <div>{agreementInfo.no}</div>
                    </div>
                    <div>
                        <div style="font-size: 13px;font-weight:bold;">{label.LentOutAgreementName}</div>
                        <div>{agreementInfo.name}</div>
                    </div>
                </div>
                <div style="padding-bottom: 1vh;">
                    <lightning-button label={label.Preview} onclick={handlePreview}></lightning-button>
                    <lightning-button label={label.Download} style="margin-left: 10px;" variant="brand" onclick={handleDownload}></lightning-button>
                </div>
            </div>
        </div>

        <!-- previous 回到上一页 -->
        <!-- confirm 之后，跳转到 detail page -->
        <div style="display: flex;justify-content: space-around;padding: 0 32%;margin-top: 20px;">
            <lightning-button label={label.BackToOverView} onclick={handleLentoutCancel}></lightning-button>
            <lightning-button label={label.Previous} onclick={clickBackToSelectProduct}></lightning-button>
            <lightning-button label={label.Confirm} variant="brand" onclick={handleAgreementConfirm}></lightning-button>
        </div>
    </div>
</template>