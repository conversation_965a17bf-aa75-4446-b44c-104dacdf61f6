public with sharing class ContactNewRecordTypeController {
    
    /**
     * 获取Contact对象可用的Record Types
     * @param accountId 如果从Account创建Contact，传入Account ID
     * @return 可用的Record Type列表
     */
    @AuraEnabled(cacheable=true)
    public static List<RecordTypeOption> getAvailableRecordTypes(Boolean isFromAccount) {
        List<RecordTypeOption> recordTypeOptions = new List<RecordTypeOption>();
        
        try {
            // 获取当前用户的Profile名称
            String currentUserProfile = getCurrentUserProfile();
            
            // 获取Contact对象的所有活跃Record Types
            List<RecordType> contactRecordTypes = [
                SELECT Id, Name, DeveloperName, Description
                FROM RecordType 
                WHERE SObjectType = 'Contact' 
                AND IsActive = true
                ORDER BY Name
            ];
            
            // 需要根据profile限制的Record Types
            Set<String> restrictedRecordTypes = new Set<String>{
                'Acknowledgment_Contact',
                'Invoice_Credit_Contact', 
                'Dunning_Contact'
            };
            
            // 允许访问受限Record Types的Profiles
            Set<String> allowedProfiles = new Set<String>{
                'Inside Sales Manager',
                'Inside Sales Rep',
                'System Administrator'
            };
            
            for (RecordType rt : contactRecordTypes) {
                Boolean shouldInclude = true;
                
                // 如果是从Account创建，且是受限的Record Type，且用户Profile不在允许列表中
                if (isFromAccount && 
                    restrictedRecordTypes.contains(rt.DeveloperName) && 
                    !allowedProfiles.contains(currentUserProfile)) {
                    shouldInclude = false;
                }
                
                if (shouldInclude) {
                    recordTypeOptions.add(new RecordTypeOption(
                        rt.Id,
                        rt.Name,
                        rt.DeveloperName,
                        rt.Description
                    ));
                }
            }
            
        } catch (Exception e) {
            System.debug('Error in getAvailableRecordTypes: ' + e.getMessage());
            throw new AuraHandledException('Error retrieving record types: ' + e.getMessage());
        }
        
        return recordTypeOptions;
    }
    
    /**
     * 获取当前用户的Profile名称
     * @return Profile名称
     */
    private static String getCurrentUserProfile() {
        try {
            User currentUser = [
                SELECT Profile.Name 
                FROM User 
                WHERE Id = :UserInfo.getUserId() 
                LIMIT 1
            ];
            return currentUser.Profile.Name;
        } catch (Exception e) {
            System.debug('Error getting user profile: ' + e.getMessage());
            return '';
        }
    }
    
    /**
     * Record Type选项包装类
     */
    public class RecordTypeOption {
        @AuraEnabled
        public String value { get; set; }
        
        @AuraEnabled
        public String label { get; set; }
        
        @AuraEnabled
        public String developerName { get; set; }
        
        @AuraEnabled
        public String description { get; set; }
        
        public RecordTypeOption(String value, String label, String developerName, String description) {
            this.value = value;
            this.label = label;
            this.developerName = developerName;
            this.description = description;
        }
    }
}
