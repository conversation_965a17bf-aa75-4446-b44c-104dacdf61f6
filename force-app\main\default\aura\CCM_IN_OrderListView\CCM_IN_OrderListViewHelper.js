({
    getObjectRecords : function(component,event, helper){
        helper.loading(component, true);
        //Records
        const fifterStr = JSON.parse(JSON.stringify(component.get('v.filterObj')));
        console.log(fifterStr, 'fifterStr==========');
        const fifter = {
            OrderType: fifterStr.orderType,
            OrderStatus: fifterStr.orderStatus.join(','),
            OrderNumber: fifterStr.orderNumber,
            CustomerPo: fifterStr.customerPO,
            PoNumber: fifterStr.PONumber,
            Customer: fifterStr.customer.Id,
            CustomerNumber: fifterStr.customerNumber,
            SalesPerson: fifterStr.salesperson.Id,
            Model: fifterStr.model,
            OrderDateFrom: fifterStr.submitDateMin,
            OrderDateTo: fifterStr.submitDateMax,
            CreatedBy: fifterStr.createdBy.Id
        }
        var action = component.get("c.getOrderInfo");
        action.setParams({
            "pageNumber": component.get('v.pageNumber'),
            "pageSize": component.get('v.pageCount'),
            "fifter": JSON.stringify(fifter),
            "IsPortal": false,
            "isSearch": false
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->'+ state);
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                console.log(JSON.parse(results), 'results===========');
                if (results && results != undefined){
                    var data = JSON.parse(results);
                    data.currentData.forEach((item)=>{
                        item.row = {
                            Id: item.Id,
                            recordType: item.recordType,
                            customerId: item.CustomerId
                        }
                    })
                    component.set('v.currentData', data.currentData);
                    component.set('v.totalRecords', data.totalRecords);
                    console.log(component.get('v.currentData'), 'currentData===========');
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            helper.loading(component, false);
        });
        $A.enqueueAction(action);
    },
    // 获取SN表格数据
    getSNTableData : function(component,event, helper){
        // 判断上一次请求是否完成
        let snFlag = component.get('v.snFlag');
        if (!snFlag) {
            return;
        }
        snFlag = false;
        component.set('v.snFlag', snFlag);
        console.log('获取SN表格数据===========' + new Date());
        var action = component.get("c.getAllExportHistory");
        action.setParams({
            "pageNumber": component.get('v.pageNumber'),
            "allPageSize": component.get('v.pageCount'),
            "orgType": 'CRM',
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                const SNData = [];
                if (res.exportHistory.length) {
                    res.exportHistory.forEach((item)=>{
                        SNData.push({
                            rowData: {id: item.requestId, exportType: item.exportType, type: item.typeLabel,requestValue: item.requestValue},
                            requestDate: item.requestDate,
                            exportType: item.exportType,
                            requestUser: item.requestUser,
                            type: item.typeLabel,
                            requestValue: item.requestValue,
                            totalNum: item.totalNum,
                            status: item.statusLable,
                            errorMsg: item.errorMsg,
                            // status: item.status === 'Submited' ? $A.get("$Label.c.CCM_Submited") : $A.get("$Label.c.CCM_Finished"),
                            isShowBtn: (item.status === 'Finished' && !item.errorMsg) ? 'showbtn' : 'hidebtn', 
                        })
                    })
                }
                component.set('v.currentSNData', SNData);
                component.set('v.totalRecords', res.TotalSize);

            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            snFlag = true;
            component.set('v.snFlag', snFlag);
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    getObjectInfo : function(component,event, helper, recordId){
        let url = window.location.origin + '/lightning/n/Order_Detail_Page?0.recordId=' + recordId;
        window.open(url, '_self'); 
    },
    loading : function(component, isloading){
        if(isloading){
            $A.util.removeClass(component.find('loading'), 'slds-hide');
        }else{
            $A.util.addClass(component.find('loading'), 'slds-hide');
        }
    },
    // 提示
    showToast : function(status, message, time){
        var toastEvent = $A.get('e.force:showToast');
        if(status == 'Success'){
             toastEvent.setParams({
                 'title': $A.get("$Label.c.CCM_Success"),
                 'message': message,
                 'type':'success',
                 'duration': time || 5000,
             });
        }else if(status == 'Failed'){
             toastEvent.setParams({
                 'title': $A.get("$Label.c.CCM_Error"),
                 'message': message,
                 'type':'error',
                 'type':'success','duration': time || 5000,
             });
        }
        toastEvent.fire();
    },
    // SN 导出数据
    getSNExportData : function(component, id, type, name){
        var action = component.get("c.getSnTempByRequestId");
        action.setParams({
            requestId: id,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(JSON.parse(response.getReturnValue()), '下载SN RES==========');
            const data = JSON.parse(response.getReturnValue());
            data.forEach((dataItem)=>{
                dataItem.orderedQuantity = parseInt(dataItem.orderedQuantity);
            })
            if (state === "SUCCESS") {
                console.log(type, '导出类型=========');
                switch (type) {
                    case 'EXCEL':
                        this.exportSNExcel(component, data, name);
                        break;
                    case 'CSV':
                        // CSV数据格式处理
                        const defaultData = JSON.parse(JSON.stringify(data));
                        const arr = [
                            [
                                $A.get("$Label.c.CCM_OrderedDate"),
                                $A.get("$Label.c.CCM_ShipmentDate"),
                                $A.get("$Label.c.CCM_ModelNo"),
                                $A.get("$Label.c.CCM_BareToolModel"),
                                $A.get("$Label.c.CCM_ProductShortDescription"),
                                $A.get("$Label.c.CCM_CustomerCode"),
                                $A.get("$Label.c.CCM_BillToName"),
                                $A.get("$Label.c.CCM_ShipToName"),
                                // $A.get("$Label.c.CCM_ShipToCountry"),
                                $A.get("$Label.c.CCM_SalesOrderNo"),
                                $A.get("$Label.c.CCM_DeliveryNoteNo"),
                                // $A.get("$Label.c.CCM_Subinventory"),
                                $A.get("$Label.c.CCM_OrderedQuantity"),
                                $A.get("$Label.c.CCM_InvoiceNo"),
                                $A.get("$Label.c.CCM_ContainerNo"),
                                // $A.get("$Label.c.CCM_BatchNo"),
                                $A.get("$Label.c.CCM_SerialNo"),
                                $A.get("$Label.c.CCM_SerialNo"),
                                // 'Bom SN',
                            ]
                        ];
                        const keyList = [
                            'orderedDate', 'shipmentDate', 'modelNo', 'bareToolModel', 'productShortDescription', 'customerCode',
                            'billToName', 'shipToName', 'salesOrderNo', 'deliveryNoteNo', 'orderedQuantity',
                            'invoiceNo', 'containerNo', 'SN'
                        ];
                        // 是否判断data为空？
                        defaultData.forEach((item)=>{
                            const arrItem = [];
                            keyList.forEach((key)=>{ 
                                arrItem.push(item[key]);
                                console.log(item['batchNo']);
                                console.log(item['SN']);
                            });
                            arr.push(arrItem);
                        })
                        console.log(JSON.parse(JSON.stringify(arr)), '导出数据格式=========CSV');
                        this.exportSNCSV(arr, name);
                        break;
                    case 'PDF':
                        this.exportSNPDF(id);
                        break;
                    default:
                        break;
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                        component.set('v.isBusy', false);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
        component.set('v.isBusy', false);
    },

    // SN Excel 导出
    exportSNExcel : function(component, data, name){
        console.log(data, 'SN Excel 导出==========');
        const defaultData = JSON.parse(JSON.stringify(data));
        const arr = [
            $A.get("$Label.c.CCM_OrderedDate"),
            $A.get("$Label.c.CCM_ShipmentDate"),
            $A.get("$Label.c.CCM_ModelNo"),
            $A.get("$Label.c.CCM_BareToolModel"),
            $A.get("$Label.c.CCM_ProductShortDescription"),
            $A.get("$Label.c.CCM_CustomerCode"),
            $A.get("$Label.c.CCM_BillToName"),
            $A.get("$Label.c.CCM_ShipToName"),
            // $A.get("$Label.c.CCM_ShipToCountry"),
            $A.get("$Label.c.CCM_SalesOrderNo"),
            $A.get("$Label.c.CCM_DeliveryNoteNo"),
            // $A.get("$Label.c.CCM_Subinventory"),
            $A.get("$Label.c.CCM_OrderedQuantity"),
            $A.get("$Label.c.CCM_InvoiceNo"),
            $A.get("$Label.c.CCM_ContainerNo"),
            // $A.get("$Label.c.CCM_BatchNo"),
            $A.get("$Label.c.CCM_SerialNo"),
            // 'Bom SN'
        ];
        const keyList = [
            'orderedDate', 'shipmentDate', 'modelNo', 'bareToolModel', 'productShortDescription', 'customerCode',
            'billToName', 'shipToName', 'salesOrderNo', 'deliveryNoteNo',
            'orderedQuantity', 'invoiceNo', 'containerNo', 'SN'
        ];
        // 是否判断data为空？
        // defaultData.forEach((item)=>{
        //     const arrItem = [];
        //     keyList.forEach((key)=>{
        //         arrItem.push(item[key]);
        //         console.log(item['batchNo']);
        //         console.log(item['SN']);
        //     });
        //     arr.push(arrItem);
        // })
        // console.log(JSON.parse(JSON.stringify(arr)), '导出数据格式=========Excel');
        // component.set('v.exportData', JSON.parse(JSON.stringify(arr)));
        // 调用组件导出方法
        const childCmp = component.find("excelTools");
        childCmp.excelStyle(arr, keyList, defaultData, name);
    },
    // SN CSV 导出 (list 数据格式为[[], [],[]])
    exportSNCSV : function(list, name){
        const newList = list.map(res => res.join(','))
        const data = newList.join(',\n')
        // “\ufeff” BOM头
        var uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data);
        var downloadLink = document.createElement("a");
        downloadLink.href = uri;
        downloadLink.download = (name + ".csv") || "SN List.csv";
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    },
    // SN PDF 导出 
    exportSNPDF : function(id){
        console.log(id, 'Sn data========PDF');
        let url = window.location.origin + '/apex/SNExportPDF?recordId=' + id;
        window.open(url);
    },

    // 获取status list
    getStatusList : function(component){
        var action = component.get("c.getPicklistOption");
        action.setParams({
            objectAPI: 'Order',
            fieldAPI: 'Order_Status__c',
            fifterString: ''
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                console.log(JSON.parse(results), 'results===========');
                component.set('v.statusOptions', JSON.parse(results));
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    }
})