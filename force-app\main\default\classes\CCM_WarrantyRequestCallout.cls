/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 02-26-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_WarrantyRequestCallout {
    public static final String ORG_EEG = 'EEG';
    public static final String FLAG_YES = 'YES';
    public static final String FLAG_NONE = 'None';
    public static final String FLAG_NEW_PRICE = 'New Price';
    public static final String SCENE_TYPE_WARRANTY_ORDER = 'Warranty Order';
    public static final String SCENE_TYPE_WARRANTY_CLAIM = 'Warranty Claim';
    public static final String SCENE_TYPE_WARRANTY_INV = 'Warranty Inv';
    public static final String ENDPOINT_NAME_SEEBURGER_UAT = 'chervon_seeburger_uat';
    public static final String ENDPOINT_NAME_SEEBURGER_PROD = 'chervon_seeburger_prod';
    public static final String OBJ_API_WARRANTY_ORDER = 'Warranty_Purchase_Order__c';
    public static final String OBJ_API_WARRANTY_CLAIM = 'Warranty_Claim__c';
    public static final String ORDER_TYPE_WARRANTY_DOMESTIC = 'EEG Warranty Domestic';
    public static final String ORDER_TYPE_WARRANTY_CLAIM_COMPENSATION = 'EEG Compensation';

    public static String pushRequestInfo(String recordId){
        
        String logPreName = '';
        String paramStr = '';
        String logId = '';
        try{
            //根据记录Id获取Object Apiname
            String objApiName = Util.findObjectNameFromRecordIdPrefix(recordId);
            Map<String, Object> mapRequest = new Map<String, Object>();
            //根据所属Apiname判断场景：Warranty Order、Warranty Claim
            if(objApiName.equals(OBJ_API_WARRANTY_ORDER)){
                //Warranty Order
                mapRequest = getWarrantyOrderRequestInfo(recordId);
                logPreName = SCENE_TYPE_WARRANTY_ORDER;
            }else if(objApiName.equals(OBJ_API_WARRANTY_CLAIM)){
                mapRequest = getWarrantyClaimRequestInfo(recordId);
                logPreName = SCENE_TYPE_WARRANTY_CLAIM;
            }

            paramStr = Json.serialize(mapRequest);
            System.debug('paramStr===>'+paramStr);
            SyncRes res = new SyncRes();
            res = requestCallOut(paramStr);
            if(String.isNotBlank(res.PROCESS_MSG)){
                res.PROCESS_MSG = res.PROCESS_MSG.replace('"','');
            }
            if(res.Process_Status == 'Failed'){
                
                logId = Util.logIntegration(logPreName + ' Sync Exception','CCM_WarrantyRequestCallout','POST','',paramStr, JSON.serialize(res));
            }else{
                logId = Util.logIntegration(logPreName + ' Sync Log','CCM_WarrantyRequestCallout','POST','',paramStr, JSON.serialize(res));
            }
            return res.Process_Status;
        }catch(Exception e){
            SyncRes res = new SyncRes();
            res.Process_Status = 'Failed';
            system.debug('报错行数---->'+e.getLineNumber()+'报错信息---->'+e.getMessage());
            logId = Util.logIntegration(logPreName + ' Sync Exception','CCM_WarrantyRequestCallout','POST','',paramStr, e.getMessage());
            return res.Process_Status;
        }
    }

    public static SyncRes requestCallOut(String param){
        SyncRes objRes = new SyncRes();
        String endPointName = '';
        if(CCM_Service.IsSandboxOrg()){
            endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
        }else {
            endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
        }
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'order';
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        system.debug('请求参数为-->'+param);
        if (!Test.isRunningTest()) {
            HttpResponse res = new HttpResponse();
            res = CCM_ServiceCallout.getDataViaHttp(param, endPoint, 'POST', HeaderKey);
            objRes = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
        }
        return objRes;
    }

    //获取库存申请参数
    public static Map<String, Object> getWarrantyOrderRequestInfo(String recordId){
        Map<String, Object> paramMap = new Map<String, Object>();
        String soqlStr = 'Select Warranty_Claim__r.Serial_Number__c, Warranty_Claim__r.Model_Number__c, Warranty_Claim__r.Service_Option__c, Warranty_Claim__r.Consumer__r.Name, Warranty_Claim__r.Consumer__r.RecordType.Name, Warranty_Claim__r.CreatedBy.LanguageLocaleKey, Address__c, Bill_To_Address__c,Ship_To_Address__r.City__c, City__c, Country__c, CurrencyIsoCode, Customer__c,Customer__r.AccountNumber, Expected_Delivery_Date__c, '+
        'Id, Name, Order_Date__c, Order_Status__c, Order_Type__c, PostalCode__c, Province__c, Ship_To_Address__c,Bill_To_Address__r.Customer_Line_Oracle_ID__c,Bill_To_Address__r.Country__c, '+
        'Ship_To_Address__r.Customer_Line_Oracle_ID__c,Price_Book_List__c,Customer__r.Owner.FederationIdentifier,Warranty_Claim__c,Warranty_Claim__r.Purchase_Date__c, '+
        'Warranty_Claim__r.Repair_Date__c,Ship_Type__c,Warranty_Claim__r.Drop_off_Date__c,Total_Value__c,Total_Value_Net__c,Total_Due_Amount__c, '+
        '(Select Est_Replenish_Date__c, Id, Name, Order_Date__c, Product__c,Product__r.Order_Model__c, Qty__c, Remark__c, Schedule_Ship_Date__c, Total_Net_Price__c, '+
        'Price_Book_List__c,List_Price__c,Sales_Price__c,Stand_Discount__c,Inventory__c,Application_Method__c, '+
        'Unit_Net_Price__c, UOM__c From Warranty_Purchase_Order_Items__r) '+
        'From Warranty_Purchase_Order__c  Where id =:recordId ';
        SObject obj = Database.query(soqlStr);
        String strDataTime = String.valueOf(system.now().getTime());
        Warranty_Purchase_Order__c headData = (Warranty_Purchase_Order__c) obj;
        List<Warranty_Purchase_Order_Item__c> itemlist = headData.Warranty_Purchase_Order_Items__r;

        String accId = headData.Customer__r.Id;
        String incoTerm = '';
        String paymentTerm = '';
        Sales_Program__c authBrand = new Sales_Program__c();
        authBrand = [SELECT Id,Status__c,Customer__c,Payment_Term__c,Incoterm__c,Approval_Status__c 
                    FROM Sales_Program__c 
                    WHERE Customer__c =:accId AND Status__c = 'Active' AND Approval_Status__c = 'Approved' limit 1];
        if(authBrand != null){
            incoTerm = authBrand.Incoterm__c;
            paymentTerm = authBrand.Payment_Term__c;
        }
        paramMap.put('ORDER_NUMBER_CRM', headData.Name);
        paramMap.put('HEADERID',  getInterfaceId(headData.Name.replace('WPO-','')));
        paramMap.put('PROMOTION_CODE',  '');
        paramMap.put('RETURN_TO_SUBINVENTORY',  '');
        paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
        paramMap.put('REQUEST_TO_SUBINVENTORY',  '');
        paramMap.put('REQUEST_FROM_SUBINVENTORY', '');
        paramMap.put('CURRENCY_CODE',  headData.CurrencyIsoCode);
        paramMap.put('ORDER_FLAG',  FLAG_YES);
        paramMap.put('SCENE_TYPE',  SCENE_TYPE_WARRANTY_ORDER);

        String customerCode = '';
        String listPriceName = '';
        customerCode = headData.Customer__r.AccountNumber;
        listPriceName = headData.Price_Book_List__c;
        paramMap.put('CUSTOMER',customerCode);
        paramMap.put('ORG_CODE', ORG_EEG);
        paramMap.put('PO_NUMBER', headData.Warranty_Claim__r.Serial_Number__c);

        paramMap.put('SHIPTO', headData.Ship_To_Address__r.Customer_Line_Oracle_ID__c);
        paramMap.put('BILLTO', headData.Bill_To_Address__r.Customer_Line_Oracle_ID__c);
        paramMap.put('ORDER_TYPE', ORDER_TYPE_WARRANTY_DOMESTIC);
        paramMap.put('PRICE_LIST', listPriceName);

        paramMap.put('WARE_HOUSE_ORACLE', ORG_EEG);
        paramMap.put('SALES_REP_Code', headData.Customer__r.Owner.FederationIdentifier);
        
        paramMap.put('DATE_ORDER', String.valueOf(Date.valueOf(headData.Order_Date__c)));
        paramMap.put('PAYMENT_TERM', paymentTerm);

        paramMap.put('FREIGHT_TERM', '');

        //根据Customer查激活的AB上的Inco term--Vince
        paramMap.put('INCO_TERM', incoTerm);

        //Shipping Place根据Icon Term联动
        if(String.isEmpty(incoTerm)){
            paramMap.put('SHPPING_PLACE', '');
        }else if(incoTerm == 'FCA'|| incoTerm == 'EXW'){
            paramMap.put('SHPPING_PLACE', 'Möckmühl');
        }else{
            paramMap.put('SHPPING_PLACE', headData.Ship_To_Address__r.City__c);
        }
        paramMap.put('PRICE_DATE', String.valueOf(headData.Order_Date__c));
        paramMap.put('FERIGHT_FEE', '');
        paramMap.put('INSURANCE_FEE', '');
        paramMap.put('OTHER_FEE', '');
        paramMap.put('CHARGE_FORWEAR_FEE', '');
        paramMap.put('ORDER_STATUS','Submitted');
        String isDropship = 'N';
        if(headData.Ship_Type__c.equals('Other')){
            isDropship = 'Y';
        }
        paramMap.put('IS_DROPSHIP', isDropship);
        paramMap.put('DROPSHIP_NAME', headData.Address__c);
        paramMap.put('DROPSHIP_ADDRESS1', '');
        paramMap.put('DROPSHIP_ADDRESS2', '');
        paramMap.put('DROPSHIP_PHONE', '');
        paramMap.put('DROPSHIP_COUNTRY', headData.Country__c);
        paramMap.put('DROPSHIP_ZIP', headData.PostalCode__c);
        paramMap.put('DROPSHIP_STATE', headData.Province__c);
        paramMap.put('DROPSHIP_CITY', headData.City__c);
        paramMap.put('INSTRUCTION_TO_DSV', '');
        paramMap.put('CARRIER_INFORMATION', '');
        paramMap.put('HEADER_DISCOUNT', '');
        paramMap.put('HEADER_DISCOUNT_AMOUNT', '');
        //TODO
        paramMap.put('TOTAL_VALUE', headData.Total_Value__c);
        paramMap.put('TOTAL_VALUE_NET', headData.Total_Value_Net__c);
        paramMap.put('TOTAL_AMOUNT', headData.Total_Due_Amount__c);

        paramMap.put('VAT',  '');
        paramMap.put('AUTO_BOOK', FLAG_YES);
        paramMap.put('LABOR_FEE', '');
        paramMap.put('PURCHASE_DATE', String.valueOf(Date.valueOf(headData.Warranty_Claim__r.Purchase_Date__c)));
        paramMap.put('REPAIR_DATE', String.valueOf(Date.valueOf(headData.Warranty_Claim__r.Repair_Date__c)));
        //Warranty_Claim__r.Drop_off_Date__c
        paramMap.put('FAILURE_DATE', String.valueOf(Date.valueOf(headData.Warranty_Claim__r.Drop_off_Date__c)));

        String warrantyRemark = generateWarrantyRemark(headData.Warranty_Claim__r.Model_Number__c, headData.Warranty_Claim__r.Service_Option__c, headData.Warranty_Claim__r.Consumer__r.Name, headData.Warranty_Claim__r.Consumer__r.RecordType.Name, headData.Warranty_Claim__r.CreatedBy.LanguageLocaleKey);
        paramMap.put('WARRANTY_REMARK', warrantyRemark);
        paramMap.put('ATTRIBUTE1', '');
        paramMap.put('ATTRIBUTE2', '');
        paramMap.put('ATTRIBUTE3', '');
        paramMap.put('ATTRIBUTE4', '');
        paramMap.put('ATTRIBUTE5', '');
        paramMap.put('ATTRIBUTE6', '');
        paramMap.put('ATTRIBUTE7', '');
        paramMap.put('ATTRIBUTE8', '');
        paramMap.put('ATTRIBUTE9', '');
        paramMap.put('ATTRIBUTE10', '');
        paramMap.put('ATTRIBUTE11', '');
        paramMap.put('ATTRIBUTE12', '');
        paramMap.put('ATTRIBUTE13', '');
        paramMap.put('ATTRIBUTE14', '');
        paramMap.put('ATTRIBUTE15', '');
        

        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        if (itemlist != null && itemlist.size() > 0) {
            for (Warranty_Purchase_Order_Item__c item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                //TODO 封装行结构体
                itemMap.put('ORDER_NUMBER_CRM', headData.Name);
                itemMap.put('ORDERLINE_CRM_ID', item.id);
                itemMap.put('LINEID', getInterfaceId(item.Name.replace('WPOI-','')));
                itemMap.put('HEADERID', paramMap.get('HEADERID'));
                itemMap.put('CRM_ORDERLINE_NUMBER', item.Id);
                itemMap.put('PRICE_DATE', paramMap.get('PRICE_DATE'));
                itemMap.put('PRODUCT_MODEL', item.Product__r.Order_Model__c);
                itemMap.put('ORDER_QUANTITY', item.Qty__c);
                itemMap.put('LIST_PRICE_NAME', item.Price_Book_List__c);
                itemMap.put('SERIAL_NUMBER', '');
                itemMap.put('PRODUCT_RATING_STATUS', '');
                itemMap.put('PROMOTION_CODE', '');
                itemMap.put('LIST_PRICE', item.List_Price__c);
                itemMap.put('REQUEST_DATE', paramMap.get('DATE_ORDER'));

                //TODO-价格相关字段-start
                itemMap.put('SUBINVENTORY_CODE', 'EGD01');
                itemMap.put('STANDARD_DISCOUNT_TYPE', item.Application_Method__c);
                //Modifire的折扣
                if(FLAG_NEW_PRICE.equals(item.Application_Method__c)){
                    //(1-SALES_PRICE/LIST_PRICE)
                    if(item.List_Price__c == 0){
                        itemMap.put('STANDARD_DISCOUNT', 0); 
                    }else{
                        Decimal discount = (1-item.Sales_Price__c/item.List_Price__c)*100;
                        Decimal roundedDiscount = discount.setScale(2,RoundingMode.HALF_UP);
                        itemMap.put('STANDARD_DISCOUNT', roundedDiscount);    
                    } 
                }else{
                    //Modifire的折扣
                    itemMap.put('STANDARD_DISCOUNT',item.Stand_Discount__c);
                }
                itemMap.put('SALES_PRICE', item.Sales_Price__c);
                itemMap.put('PROMOTION_DISCOUNT1', 100);
                //promotion 头折扣
                itemMap.put('PROMOTION_DISCOUNT2', 0);
                
                //经过promotion行折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE1', item.Unit_Net_Price__c);
                //经过Promotion头折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE2', item.Unit_Net_Price__c);
                //最终用户取值价格
                itemMap.put('UNIT_SELLING_PRICE', item.Unit_Net_Price__c);
                //TODO
                
                itemMap.put('PROMOTION_DISCOUNT1_TYPE', FLAG_NEW_PRICE);
                //TODO-价格相关字段-end

                itemMap.put('EST_REPLENISH_DATE','');
                itemMap.put('UOM', item.UOM__c);
                itemMap.put('TOTAL_NET_PRICE', item.Total_Net_Price__c);
                String remark = '**kostenlos Austauschgerät**';
                // if(headData.Warranty_Claim__r.CreatedBy.LanguageLocaleKey.equalsIgnoreCase('DE')) {
                //     remark = '**kostenlos Austauschgerät** ';
                // }
                itemMap.put('REMARK', remark);
                itemMap.put('SCHEDULE_SHIP_DATE',''); 
                itemMap.put('TRAFFIC_LIGHT', item.Inventory__c);

                itemMap.put('ATTRIBUTE1', '');
                itemMap.put('ATTRIBUTE2', '');
                itemMap.put('ATTRIBUTE3', '');
                itemMap.put('ATTRIBUTE4', '');
                itemMap.put('ATTRIBUTE5', '');
                itemMap.put('ATTRIBUTE6', '');
                itemMap.put('ATTRIBUTE7', '');
                itemMap.put('ATTRIBUTE8', '');
                itemMap.put('ATTRIBUTE9', '');
                itemMap.put('ATTRIBUTE10', '');
                itemMap.put('ATTRIBUTE11', '');
                itemMap.put('ATTRIBUTE12', '');
                itemMap.put('ATTRIBUTE13', '');
                itemMap.put('ATTRIBUTE14', '');
                itemMap.put('ATTRIBUTE15', '');
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        return paramMap;
    }
    //获取Invoice Damage申请参数
    public static Map<String, Object> getWarrantyClaimRequestInfo(String recordId){
        Map<String, Object> paramMap = new Map<String, Object>();
        /**
         * Select Bill_Address__c, Claim_Date__c, Country__c, CurrencyIsoCode, Dealer_Name__c, Description__c, Drop_off_Date__c, Id, Name, Purchase_Date__c, Repair_Date__c, Ship_Address__c, Stand_Amount__c, Total__c, 
            (Select CurrencyIsoCode, Id, labor_Time__c, Name, Part_Name__c, Quantity__c, Total__c, Unit_Price__c From Warranty_Claim_Items__r) from Warranty_Claim__c
         */
         String soqlStr = 'Select Serial_Number__c, Model_Number__c, Consumer__r.Name, Consumer__r.RecordType.Name, Dealer_Name__r.Country__c, Bill_Address__c,repair_Work__c,Failure_Description__c,Bill_Address__r.Customer_Line_Oracle_ID__c, Claim_Date__c, Country__c, CurrencyIsoCode,CRM_Order_Number__c,Is_Mass_Upload__c, '+
         'Dealer_Name__c,Dealer_Name__r.AccountNumber, Description__c, Drop_off_Date__c,Dealer_Name__r.List_Price__r.Name,Labor_Cost_To_Oracle__c,Total_Actual_To_Oracle__c, '+
         'Replacement_Option__c,Service_Option__c,Dealer_Name__r.Owner.FederationIdentifier,Ship_Address__r.City__c,Dealer_Name__r.Labor_Price_Name__c, '+
         'Id, Name, Purchase_Date__c, Repair_Date__c, Ship_Address__c,Ship_Address__r.Customer_Line_Oracle_ID__c, Stand_Amount__c,Labor_Rate__c,Labor_Final_Time__c,Final_Labor_Rate__c,Labor_Price_Book__c,CreatedBy.LanguageLocaleKey,  '+
         '(Select CurrencyIsoCode, Id,Unit_Price_To_Oracle__c, labor_Time__c, Name,Part__c, Part__r.Order_Model__c, Quantity__c, PriceBook_Name__c, '+
         'Unit_Price__c,Original_Price__c,Application_Method__c,Stand_Discount_Value__c,Total_To_Oracle__c '+
         'From Warranty_Claim_Items__r) '+
         'from Warranty_Claim__c  Where id =:recordId ';
         System.debug('Claim SOQL===>'+soqlStr);
        SObject obj = Database.query(soqlStr);
        String strDataTime = String.valueOf(system.now().getTime());
        Warranty_Claim__c headData = (Warranty_Claim__c) obj;
        List<Warranty_Claim_Item__c> itemlist = headData.Warranty_Claim_Items__r;

        String accId = headData.Dealer_Name__r.Id;
        String incoTerm = '';
        String paymentTerm = '';
        Sales_Program__c authBrand = new Sales_Program__c();
        authBrand = [SELECT Id,Status__c,Customer__c,Payment_Term__c,Incoterm__c,Approval_Status__c 
                    FROM Sales_Program__c 
                    WHERE Customer__c =:accId AND Status__c = 'Active' AND Approval_Status__c = 'Approved' limit 1];
        if(authBrand != null){
            incoTerm = authBrand.Incoterm__c;
            paymentTerm = authBrand.Payment_Term__c;
        }
        if(headData.Is_Mass_Upload__c){
            paramMap.put('ORDER_NUMBER_CRM', headData.CRM_Order_Number__c);
            paramMap.put('SCENE_TYPE',  SCENE_TYPE_WARRANTY_INV);
            paramMap.put('ORDER_TYPE', FLAG_NONE);
            paramMap.put('ORDER_FLAG',  'INV');
        }else{
            paramMap.put('ORDER_NUMBER_CRM', headData.Name);
            paramMap.put('SCENE_TYPE',  SCENE_TYPE_WARRANTY_CLAIM);
            paramMap.put('ORDER_TYPE', ORDER_TYPE_WARRANTY_CLAIM_COMPENSATION);
            paramMap.put('ORDER_FLAG',  FLAG_YES);
        }
        
        paramMap.put('HEADERID',  getInterfaceId(headData.Name.replace('WC-','')));
        system.debug('HEADERID-->'+paramMap.get('HEADERID'));
        paramMap.put('PROMOTION_CODE',  '');
        paramMap.put('RETURN_TO_SUBINVENTORY',  '');
        paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
        paramMap.put('REQUEST_TO_SUBINVENTORY',  '');
        paramMap.put('REQUEST_FROM_SUBINVENTORY', '');
        paramMap.put('CURRENCY_CODE',  headData.CurrencyIsoCode);
        
        String customerCode = '';
        String listPriceName = '';
        customerCode = headData.Dealer_Name__r.AccountNumber;
        listPriceName = headData.Dealer_Name__r.List_Price__r.Name;
        paramMap.put('CUSTOMER',customerCode);
        paramMap.put('ORG_CODE', ORG_EEG);
        system.debug('OrgCode-->'+paramMap.get('ORG_CODE'));
        paramMap.put('PO_NUMBER', headData.Serial_Number__c);
        paramMap.put('SHIPTO', headData.Ship_Address__r.Customer_Line_Oracle_ID__c);
        paramMap.put('BILLTO', headData.Bill_Address__r.Customer_Line_Oracle_ID__c);
        system.debug('SHIPTO-->'+paramMap.get('SHIPTO'));
        system.debug('BILLTO-->'+paramMap.get('BILLTO'));
        
        system.debug('ORDER_TYPE-->'+paramMap.get('ORDER_TYPE'));
        paramMap.put('PRICE_LIST', listPriceName);
        system.debug('PRICE_LIST-->'+paramMap.get('PRICE_LIST'));
        paramMap.put('WARE_HOUSE_ORACLE', ORG_EEG);
        system.debug('WARE_HOUSE_ORACLE-->'+paramMap.get('WARE_HOUSE_ORACLE'));
        paramMap.put('SALES_REP_Code', headData.Dealer_Name__r.Owner.FederationIdentifier);
        system.debug('SALES_REP_Code-->'+paramMap.get('SALES_REP_Code'));
        paramMap.put('DATE_ORDER', String.valueOf(headData.Claim_Date__c));
        system.debug('DATE_ORDER-->'+paramMap.get('DATE_ORDER'));
        paramMap.put('PAYMENT_TERM', paymentTerm);

        paramMap.put('FREIGHT_TERM', '');

        //根据Customer查激活的AB上的Inco term--Vince
        paramMap.put('INCO_TERM', incoTerm);

        //Shipping Place根据Icon Term联动
        if(String.isEmpty(incoTerm)){
            paramMap.put('SHPPING_PLACE', '');
        }else if(incoTerm == 'FCA'|| incoTerm == 'EXW'){
            paramMap.put('SHPPING_PLACE', 'Möckmühl');
        }else{
            paramMap.put('SHPPING_PLACE', headData.Ship_Address__r.City__c);
        }
        paramMap.put('PRICE_DATE', String.valueOf(headData.Claim_Date__c));
        system.debug('PRICE_DATE-->'+paramMap.get('PRICE_DATE'));
        paramMap.put('FERIGHT_FEE', '');
        paramMap.put('INSURANCE_FEE', '');
        paramMap.put('OTHER_FEE', '');
        paramMap.put('CHARGE_FORWEAR_FEE', '');
        paramMap.put('ORDER_STATUS','Submitted');
        String isDropship = 'N';
        paramMap.put('IS_DROPSHIP', isDropship);
        paramMap.put('DROPSHIP_NAME', '');
        paramMap.put('DROPSHIP_ADDRESS1', '');
        paramMap.put('DROPSHIP_ADDRESS2', '');
        paramMap.put('DROPSHIP_PHONE', '');
        paramMap.put('DROPSHIP_COUNTRY', '');
        paramMap.put('DROPSHIP_ZIP', '');
        paramMap.put('DROPSHIP_STATE', '');
        paramMap.put('DROPSHIP_CITY', '');
        paramMap.put('INSTRUCTION_TO_DSV', '');
        paramMap.put('CARRIER_INFORMATION', '');
        paramMap.put('HEADER_DISCOUNT', '');
        paramMap.put('HEADER_DISCOUNT_AMOUNT', '');

        paramMap.put('TOTAL_VALUE', headData.Total_Actual_To_Oracle__c);
        paramMap.put('TOTAL_VALUE_NET', headData.Total_Actual_To_Oracle__c);
        paramMap.put('TOTAL_AMOUNT', headData.Total_Actual_To_Oracle__c);

        paramMap.put('VAT',  '');
        paramMap.put('AUTO_BOOK', FLAG_YES);
        paramMap.put('LABOR_FEE', headData.Labor_Cost_To_Oracle__c);
        paramMap.put('PURCHASE_DATE', String.valueOf(Date.valueOf(headData.Purchase_Date__c)));
        paramMap.put('REPAIR_DATE', String.valueOf(Date.valueOf(headData.Repair_Date__c)));
        paramMap.put('FAILURE_DATE', String.valueOf(Date.valueOf(headData.Drop_off_Date__c)));
        
        String warrantyRemark = generateWarrantyRemark(headData.Model_Number__c, headData.Service_Option__c, headData.Consumer__r.Name, headData.Consumer__r.RecordType.Name, headData.CreatedBy.LanguageLocaleKey);
        paramMap.put('WARRANTY_REMARK', warrantyRemark);
        paramMap.put('ATTRIBUTE1', '');
        paramMap.put('ATTRIBUTE2', '');
        paramMap.put('ATTRIBUTE3', '');
        paramMap.put('ATTRIBUTE4', '');
        paramMap.put('ATTRIBUTE5', '');
        paramMap.put('ATTRIBUTE6', '');
        paramMap.put('ATTRIBUTE7', '');
        paramMap.put('ATTRIBUTE8', '');
        paramMap.put('ATTRIBUTE9', '');
        paramMap.put('ATTRIBUTE10', '');
        paramMap.put('ATTRIBUTE11', '');
        paramMap.put('ATTRIBUTE12', '');
        paramMap.put('ATTRIBUTE13', '');
        paramMap.put('ATTRIBUTE14', '');
        paramMap.put('ATTRIBUTE15', '');
        

        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        if (itemlist != null && itemlist.size() > 0) {
            for (Warranty_Claim_Item__c item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                //TODO 封装行结构体
                itemMap.put('ORDER_NUMBER_CRM', headData.Name);
                itemMap.put('ORDERLINE_CRM_ID', item.id);
                itemMap.put('LINEID', getInterfaceId(item.Name.replace('WCI-','')));
                itemMap.put('HEADERID', paramMap.get('HEADERID'));
                itemMap.put('CRM_ORDERLINE_NUMBER', item.Id);
                itemMap.put('PRICE_DATE', paramMap.get('PRICE_DATE'));
                itemMap.put('PRODUCT_MODEL', item.Part__r.Order_Model__c);
                itemMap.put('ORDER_QUANTITY', Integer.valueOf(item.Quantity__c));
                itemMap.put('LIST_PRICE_NAME', item.PriceBook_Name__c);
                itemMap.put('SERIAL_NUMBER', '');
                itemMap.put('PRODUCT_RATING_STATUS', '');
                itemMap.put('PROMOTION_CODE', '');
                
                itemMap.put('REQUEST_DATE', String.valueOf(Date.today()));
                system.debug('REQUEST_DATE-->'+itemMap.get('REQUEST_DATE'));

                //TODO-价格相关字段-start
                itemMap.put('LIST_PRICE', item.Original_Price__c);
                itemMap.put('SUBINVENTORY_CODE', 'EGD01');
                itemMap.put('SALES_PRICE', item.Unit_Price__c);
                
                
                itemMap.put('STANDARD_DISCOUNT_TYPE', item.Application_Method__c);
                //Modifire的折扣
                if(FLAG_NEW_PRICE.equals(item.Application_Method__c)){
                    //(1-SALES_PRICE/LIST_PRICE)
                    if(item.Original_Price__c == 0){
                        itemMap.put('STANDARD_DISCOUNT', 0); 
                    }else{
                        Decimal discount = (1-item.Unit_Price__c/item.Original_Price__c)*100;
                        Decimal roundedDiscount = discount.setScale(2,RoundingMode.HALF_UP);
                        itemMap.put('STANDARD_DISCOUNT', roundedDiscount);    
                    } 
                }else{
                    itemMap.put('STANDARD_DISCOUNT', item.Stand_Discount_Value__c);
                }
                
                if(('For a free tools').equals(headData.Replacement_Option__c) && ('Replacement').equals(headData.Service_Option__c)){
                    itemMap.put('PROMOTION_DISCOUNT1_TYPE', FLAG_NEW_PRICE);
                   //经过promotion行折扣的计算后的价格
                    itemMap.put('UNIT_NET_PRICE1', item.Unit_Price_To_Oracle__c);
                    itemMap.put('PROMOTION_DISCOUNT1', 100);
                }else{
                    itemMap.put('PROMOTION_DISCOUNT1_TYPE', FLAG_NONE);
                    //经过promotion行折扣的计算后的价格
                    itemMap.put('UNIT_NET_PRICE1', item.Unit_Price_To_Oracle__c);
                    itemMap.put('PROMOTION_DISCOUNT1', 0);
                }
                //TODO-价格相关字段-end
                //promotion 头折扣
                itemMap.put('PROMOTION_DISCOUNT2', 0);
                //经过Promotion头折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE2', itemMap.get('UNIT_NET_PRICE1'));
                //最终用户取值价格
                itemMap.put('UNIT_SELLING_PRICE', itemMap.get('UNIT_NET_PRICE1'));
                itemMap.put('EST_REPLENISH_DATE','');
                itemMap.put('UOM', 'EA');
                itemMap.put('TOTAL_NET_PRICE', item.Total_To_Oracle__C);
                String description = '';
                String flag = '';
                if(headData.Is_Mass_Upload__c){
                    // if(String.isNotBlank(headData.repair_Work__c) && String.isNotBlank(headData.Failure_Description__c)){
                    //     flag = ';';
                    // }
                    // description = (headData.repair_Work__c == null ? '' : headData.repair_Work__c) + flag +
                    // (headData.Failure_Description__c == null ? '' :  headData.Failure_Description__c);
                    if(String.isNotBlank(headData.Failure_Description__c)) {
                        description = headData.Failure_Description__c;
                    }
                }else{
                    description = headData.Description__c;
                }
                itemMap.put('REMARK', description);
                itemMap.put('SCHEDULE_SHIP_DATE',''); 
                itemMap.put('TRAFFIC_LIGHT', '');

                itemMap.put('ATTRIBUTE1', '');
                itemMap.put('ATTRIBUTE2', '');
                itemMap.put('ATTRIBUTE3', '');
                itemMap.put('ATTRIBUTE4', '');
                itemMap.put('ATTRIBUTE5', '');
                itemMap.put('ATTRIBUTE6', '');
                itemMap.put('ATTRIBUTE7', '');
                itemMap.put('ATTRIBUTE8', '');
                itemMap.put('ATTRIBUTE9', '');
                itemMap.put('ATTRIBUTE10', '');
                itemMap.put('ATTRIBUTE11', '');
                itemMap.put('ATTRIBUTE12', '');
                itemMap.put('ATTRIBUTE13', '');
                itemMap.put('ATTRIBUTE14', '');
                itemMap.put('ATTRIBUTE15', '');
                itemMaplist.add(itemMap);
            }
        }
        if(('Repair').equals(headData.Service_Option__c)){
            Map<String, Object> itemMap = new Map<String, Object>();
            //TODO 封装行结构体
            itemMap.put('ORDER_NUMBER_CRM', headData.Name);
            itemMap.put('ORDERLINE_CRM_ID', Datetime.now().format('yyyyMMddHHmmssSSS'));
            itemMap.put('LINEID', Datetime.now().format('yyyyMMddHHmmssSSS'));
            itemMap.put('HEADERID', paramMap.get('HEADERID'));
            itemMap.put('CRM_ORDERLINE_NUMBER', Datetime.now().format('yyyyMMddHHmmssSSS'));
            itemMap.put('PRICE_DATE', paramMap.get('PRICE_DATE'));
            itemMap.put('PRODUCT_MODEL', 'Labor');
            itemMap.put('ORDER_QUANTITY', headData.Labor_Final_Time__c);
            itemMap.put('LIST_PRICE_NAME', headData.Labor_Price_Book__c);
            itemMap.put('SERIAL_NUMBER', '');
            itemMap.put('PRODUCT_RATING_STATUS', '');
            itemMap.put('PROMOTION_CODE', '');
            
            itemMap.put('REQUEST_DATE', String.valueOf(Date.today()));

            //TODO-价格相关字段-start
            itemMap.put('LIST_PRICE', headData.Labor_Rate__c);
            itemMap.put('SUBINVENTORY_CODE', 'EGD01');
            itemMap.put('SALES_PRICE', headData.Labor_Rate__c);
            
            itemMap.put('STANDARD_DISCOUNT_TYPE', '');
            itemMap.put('STANDARD_DISCOUNT', 0);
            itemMap.put('UNIT_NET_PRICE1', headData.Final_Labor_Rate__c);
            itemMap.put('PROMOTION_DISCOUNT1', 0);
            itemMap.put('PROMOTION_DISCOUNT1_TYPE', FLAG_NEW_PRICE);
            //TODO-价格相关字段-end
            //promotion 头折扣
            itemMap.put('PROMOTION_DISCOUNT2', 0);
            //经过Promotion头折扣的计算后的价格
            itemMap.put('UNIT_NET_PRICE2', headData.Final_Labor_Rate__c);
            //最终用户取值价格
            itemMap.put('UNIT_SELLING_PRICE', headData.Final_Labor_Rate__c);
            itemMap.put('EST_REPLENISH_DATE','');
            itemMap.put('UOM', 'Min');
            itemMap.put('TOTAL_NET_PRICE', headData.Labor_Cost_To_Oracle__c);
            String description = '';
            String flag = '';
            if(headData.Is_Mass_Upload__c){
                // if(String.isNotBlank(headData.repair_Work__c) && String.isNotBlank(headData.Failure_Description__c)){
                //     flag = ';';
                // }
                // description = (headData.repair_Work__c == null ? '' : headData.repair_Work__c) + flag +
                // (headData.Failure_Description__c == null ? '' :  headData.Failure_Description__c);
                if(String.isNotBlank(headData.Failure_Description__c)) {
                    description = headData.Failure_Description__c;
                }
            }else{
                description = headData.Description__c;
            }
            itemMap.put('REMARK',description);
            itemMap.put('SCHEDULE_SHIP_DATE',''); 
            itemMap.put('TRAFFIC_LIGHT', '');


            itemMap.put('ATTRIBUTE1', '');
            itemMap.put('ATTRIBUTE2', '');
            itemMap.put('ATTRIBUTE3', '');
            itemMap.put('ATTRIBUTE4', '');
            itemMap.put('ATTRIBUTE5', '');
            itemMap.put('ATTRIBUTE6', '');
            itemMap.put('ATTRIBUTE7', '');
            itemMap.put('ATTRIBUTE8', '');
            itemMap.put('ATTRIBUTE9', '');
            itemMap.put('ATTRIBUTE10', '');
            itemMap.put('ATTRIBUTE11', '');
            itemMap.put('ATTRIBUTE12', '');
            itemMap.put('ATTRIBUTE13', '');
            itemMap.put('ATTRIBUTE14', '');
            itemMap.put('ATTRIBUTE15', '');
            itemMaplist.add(itemMap);
        }
        paramMap.put('OrderLine', itemMaplist);
        return paramMap;
    }   
    
    private static String generateWarrantyRemark(String modelNumber, String serviceOption, String consumerName, String consumerType, String dealerLanguage) {
        String summaryInfo;
        String summaryInfoTemplate = 'Model: {0}({1});{2}({3})';
        Map<String, String> serviceOptionsMap = new Map<String, String>{
            'Replacement' => 'Ersatz',
            'Repair' => 'Reparatur'
        };

        Map<String, String> consumerTypeMap = new Map<String, String>{
            'Residential' => 'Privatnutzung',
            'Commercial' => 'Gewerbekunde'
        };

        if(consumerType == 'Residential Consumer') {
            consumerType = 'Residential';
        }
        else if(consumerType == 'Commercial Consumer') {
            consumerType = 'Commercial';
        }

        if(dealerLanguage.equalsIgnoreCase('DE')) {
            summaryInfo = String.format(summaryInfoTemplate, new List<String>{modelNumber, serviceOptionsMap.get(serviceOption), consumerName, consumerTypeMap.get(consumerType)});
        }
        else {
            summaryInfo = String.format(summaryInfoTemplate, new List<String>{modelNumber, serviceOption, consumerName, consumerType});
        }
        return summaryInfo;
    }

    /**
     * 生成接口序列Id
     */
    public static String getInterfaceId(String autoNo){
        String dateStr = SystemUtils.getCurrentTimeStamp().substring(0,8);
        String resultDateStr = dateStr.substring(2);
        String strResult = resultDateStr + autoNo;
        return strResult;
    }

    public class SyncRes {
        public String Process_Status;
        public String PROCESS_MSG;
    }
    
    
    public class SyncResult {
        public String ReturnCode;
        public String ReturnMessage;
        public String Process_Status;
        public List<ProcessResult> Process_Result; 
    }

    public class ProcessResult {
        
        public String SFDC_Id;
        public String Oracle_Id;
        public String Error_Message;
        public String Error_Detail;
    }
}