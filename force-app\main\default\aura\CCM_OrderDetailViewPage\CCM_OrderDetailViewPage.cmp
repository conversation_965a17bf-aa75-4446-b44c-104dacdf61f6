<aura:component description="CCM_Community_OrderApplicationDetail"
                implements="forceCommunity:availableForAllPageTypes,force:hasRecordId,flexipage:availableForAllPageTypes,force:appHostable"
                controller="CCM_Community_OrderApplicationDetailCtl">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="poRecordId" type="String" default=""/>
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="order" type="Object" default=""/>
    <aura:attribute name="invoiceInfo" type="List" default="[]"/>
    <aura:attribute name="shipmentColumns" type="List" default="[]"/>
    <aura:attribute name="invoiceColumns" type="List" default="[]"/>
    <aura:attribute name="processData" type="List" default="[]"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="paymentTermVal" type="String" default=""/>
    <aura:attribute name="freightTermVal" type="String" default=""/>
    <aura:attribute name="isInnerUser" type="Boolean" default="false"/>
    <aura:attribute name="isShow" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareProducts" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareShipments" type="Boolean" default="false"/>
    <aura:attribute name="isCanReverse" type="Boolean" default="false"/>
    <aura:attribute name="reverseOrderLink" type="String" default=""/>
    <aura:attribute name="attachmentColumns" type="List" default="[]"/>

    <aura:attribute name="basicInformation" type="Object" default=""/>
    <aura:attribute name="shipmentInfo" type="List" default="[]"/>
    <aura:attribute name="attachment" type="Object" default="[]"/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="isInsideSales" type="Boolean" default="false"/>
    <aura:attribute name="isCCA" type="Boolean" default="false"/>
    <aura:attribute name="userType" type="String" default=""/>
    <aura:attribute name="onlyShowDropAddress" type="Boolean" default="false"/>
    <aura:attribute name="isDE" type="Boolean" default="false"/>

    <aura:attribute name="sortedBy" type="String" default="deliveryDate"/>
    <aura:attribute name="sortDirection" type="String" default="desc"/>


    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="slds-box slds-theme_default">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium">
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <c:CCM_PathProcess processData="{!v.processData}" currentStep="{!v.currentStep}"/>
            <!-- Basic Information -->
            <c:CCM_Section title="{!$Label.c.CCM_BasicInformation}" expandable="true">
                <lightning:layout multipleRows="true">
                    <!-- Customer -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_Customer}">
                            {!v.basicInformation.Customer}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Customer Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_CustomerNumber}">
                            {!v.basicInformation.CustomerNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Customer PO -->
                    <!-- <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom-small">
                        <c:CCM_Field label="{!$Label.c.Order_CustomerPO}">
                            {!v.basicInformation.CustomerPO}
                        </c:CCM_Field>
                    </lightning:layoutItem> -->
                    <!-- Purchase Order Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_PurchaseOrderNumber}">
                            {!v.basicInformation.PONumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Payment Term -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_PaymentTerm}">
                            {!v.basicInformation.PaymentTerm}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Inco Term -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_IncoTerm}">
                            {!v.basicInformation.IncoTerm}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Salesperson -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_Salesperson}">
                            {!v.basicInformation.Salesperson}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_OrderDate}">
                            {!v.basicInformation.OrderDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Created Date in EBS -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_OrderCreatedDateinEBS}">
                            {!v.basicInformation.OrderCreatedinEBS}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_OrderNumber}">
                            {!v.basicInformation.OrderNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Status -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_OrderStatus}">
                            {!v.basicInformation.OrderStatus}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Type -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_OrderType}">
                            {!v.basicInformation.OrderType}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Warehouse -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_Warehouse}">
                            {!v.basicInformation.Warehouse}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Expected Delivery Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_ExpectedDeliveryDate}">
                            {!v.basicInformation.ExpectedDeliveryDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Pricing Date -->
                    <aura:if isTrue="{!v.isInsideSales}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.Order_PricingDate}">
                                {!v.basicInformation.PricingDate}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>
                    <!-- Price List -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_PriceList}">
                            {!v.basicInformation.PriceList}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Est Weight -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_EstWeight}">
                            {!v.basicInformation.EstWeight ? v.basicInformation.EstWeight + ' kg' : ''}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Est Volume -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_EstVolume}">
                            {!v.basicInformation.EstVolume ? v.basicInformation.EstVolume + ' m³' : ''}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Is Dropship Order? -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_IsDropshipOrder}">
                            {!v.basicInformation.IsDropshipOrder}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Created By -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_CreatedBy}">
                            {!v.basicInformation.createdBy}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Remark -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_Remark}">
                            {!v.basicInformation.remark}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            
            <!-- Delivery Information -->
            <c:CCM_Section title="{!$Label.c.CCM_DeliveryInformation}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Bill To Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_BillToAddress}">
                            <aura:if isTrue="{!v.basicInformation.BillToAddress.CompanyName}">
                                <span>{!v.basicInformation.BillToAddress.CompanyName}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.BillToAddress.Street}">
                                <span>{!v.basicInformation.BillToAddress.Street}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!(v.basicInformation.BillToAddress.City || v.basicInformation.BillToAddress.PostCode)}">
                                <span>{!v.basicInformation.BillToAddress.PostCode}&nbsp;&nbsp;{!v.basicInformation.BillToAddress.City}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.BillToAddress.Country}">
                                <span>{!v.basicInformation.BillToAddress.Country}</span><br/>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Ship To Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_ShipToAddress}">
                            <aura:if isTrue="{!v.onlyShowDropAddress}">
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!v.basicInformation.ShipToAddress.CompanyName}">
                                        <span>{!v.basicInformation.ShipToAddress.CompanyName}</span><br/>
                                    </aura:if>
                                    <aura:if isTrue="{!v.basicInformation.ShipToAddress.Street}">
                                        <span>{!v.basicInformation.ShipToAddress.Street}</span><br/>
                                    </aura:if>
                                    <aura:if isTrue="{!(v.basicInformation.ShipToAddress.City || v.basicInformation.ShipToAddress.PostCode)}">
                                        <span>{!v.basicInformation.ShipToAddress.PostCode}&nbsp;&nbsp;{!v.basicInformation.ShipToAddress.City}</span><br/>
                                    </aura:if>
                                    <aura:if isTrue="{!v.basicInformation.ShipToAddress.Country}">
                                        <span>{!v.basicInformation.ShipToAddress.Country}</span><br/>
                                    </aura:if>
                                </aura:set>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Dropship Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_DropshipAddress}">
                            <aura:if isTrue="{!v.basicInformation.DropshipAddress.CompanyName}">
                                <span>{!v.basicInformation.DropshipAddress.CompanyName}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.DropshipAddress.Street}">
                                <span>{!v.basicInformation.DropshipAddress.Street}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!(v.basicInformation.DropshipAddress.City || v.basicInformation.DropshipAddress.PostCode)}">
                                <span>{!v.basicInformation.DropshipAddress.PostCode}&nbsp;&nbsp;{!v.basicInformation.DropshipAddress.City}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.DropshipAddress.Country}">
                                <span>{!v.basicInformation.DropshipAddress.Country}</span><br/>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Dropship Type -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_DropshipType}">
                            {!v.basicInformation.DropshipType}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            
            <!-- Order Item Information -->
            <c:CCM_Section title="{!$Label.c.CCM_OrderItemInformation}" expandable="true" >
                <div class="slds-p-left_medium slds-p-right--medium">
                    <div class="table-wrap">
                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productTable" role="grid">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Action}">{!$Label.c.CCM_Action}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Line}">{!$Label.c.CCM_Line}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Product Description -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Model #  -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.Order_Model}">{!$Label.c.Order_Model}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- UOM -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.Order_UOM}">{!$Label.c.Order_UOM}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- haibo: 添加行状态 -->
                                    <!-- Order Line Status -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Order Line Status">Order Line Status</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.Order_OrderDate}">{!$Label.c.Order_OrderDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Request Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.Order_RequestDate}">{!$Label.c.Order_RequestDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Delivery Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" onclick="{!c.handleSort}" data-field="deliveryDate">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_DeliveryDate}">{!$Label.c.CCM_DeliveryDate}</span>
                                                <aura:if isTrue="{!and(v.sortedBy == 'deliveryDate', v.sortDirection == 'desc')}">
                                                    <lightning:icon iconName="utility:arrowdown" size="x-small" class="slds-icon-text-default slds-m-left_x-small"/>
                                                </aura:if>
                                                <aura:if isTrue="{!and(v.sortedBy == 'deliveryDate', v.sortDirection == 'asc')}">
                                                    <lightning:icon iconName="utility:arrowup" size="x-small" class="slds-icon-text-default slds-m-left_x-small"/>
                                                </aura:if>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_OrderQty}">{!$Label.c.CCM_OrderQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Inventory -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.Order_Inventory}">{!$Label.c.Order_Inventory}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Back Order Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_BackOrderQty}">{!$Label.c.Order_BackOrderQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Release to Warehouse Qty -->
                                    <aura:if isTrue="{!v.isInsideSales}">
                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate" title="{!$Label.c.Order_ReleasetowarehouseQty}">{!$Label.c.Order_ReleasetowarehouseQty}</span>
                                                </div>
                                            </a>
                                        </th>
                                    </aura:if>
                                    <!-- Picked & Packed Qty -->
                                    <aura:if isTrue="{!v.isInsideSales}">
                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate" title="{!$Label.c.Order_PickedandPackedqty}">{!$Label.c.Order_PickedandPackedqty}</span>
                                                </div>
                                            </a>
                                        </th>
                                    </aura:if>
                                    <!-- Shipped Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ShippedQty}">{!$Label.c.Order_ShippedQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Invoiced Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_InvoicedQty}">{!$Label.c.Order_InvoicedQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Cancelled Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_CancelledQty}">{!$Label.c.Order_CancelledQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Returned Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_ReturnedQty}">{!$Label.c.CCM_ReturnedQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Est Replenish Date -->
                                    <aura:if isTrue="{!v.isInsideSales}">
                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate" title="{!$Label.c.Order_EstReplenishDate}">{!$Label.c.Order_EstReplenishDate}</span>
                                                </div>
                                            </a>
                                        </th>
                                    </aura:if>
                                    <!-- Schedule Ship Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ScheduleShipDate}">{!$Label.c.Order_ScheduleShipDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Pricing Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_PricingDate}">{!$Label.c.Order_PricingDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- List Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Discount -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_Discount}">{!$Label.c.Order_Discount}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Unit Net Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_UnitNetPrice}">{!$Label.c.Order_UnitNetPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Total Net Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_TotalNetPrice}">{!$Label.c.Order_TotalNetPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Remark -->
                                    <aura:if isTrue="{!v.isInsideSales}">
                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate" title="{!$Label.c.Order_Remark}">{!$Label.c.Order_Remark}</span>
                                                </div>
                                            </a>
                                        </th>
                                    </aura:if>
                                    <!-- Sales Agreement No. -->
                                    <aura:if isTrue="{!v.isInsideSales}">
                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate" title="{!$Label.c.Order_SalesAgreementNo}">{!$Label.c.Order_SalesAgreementNo}</span>
                                                </div>
                                            </a>
                                        </th>
                                    </aura:if>
                                </tr>
                            </thead>
                            <tbody>
                                <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                                    <tr aria-selected="false" id="{!index}" data-expanded="false" class="slds-hint-parent" onclick="{!c.showToolList}">
                                        <!-- Action -->
                                        <td scope="row">
                                            <div class="slds-truncate" title="">
                                                <aura:if isTrue="{!orderItem.ProductList}">
                                                    <lightning:icon iconName="utility:chevrondown" size="xx-small" class="collapse_icn"/>
                                                </aura:if>
                                            </div>
                                        </td>
                                        <!-- line -->
                                        <td scope="row">
                                            <div class="slds-truncate" title="">
                                                {!index + 1}
                                            </div>
                                        </td>
                                        <!-- Product Description -->
                                        <td role="gridcell" title="{!orderItem.ProductDescription}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!orderItem.ProductDescription}
                                            </div>
                                        </td>
                                        <!-- Model #  -->
                                        <td role="gridcell" title="{!orderItem.Model}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.Model}</span>
                                            </div>
                                        </td>
                                        <!-- UOM -->
                                        <td role="gridcell" title="{!orderItem.UOM}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.UOM}</span>
                                            </div>
                                        </td>
                                        <!-- haibo: 添加行状态 -->
                                        <!-- Order Line Status -->
                                        <td role="gridcell" title="{!orderItem.OrderLineStatus}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.OrderLineStatus}</span>
                                            </div>
                                        </td>
                                        <!-- Order Date -->
                                        <td role="gridcell" title="{!orderItem.OrderDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.OrderDate}</span>
                                            </div>
                                        </td>
                                        <!-- Request Date -->
                                        <td role="gridcell" title="{!orderItem.RequestDate}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.RequestDate}</span>
                                            </div>
                                        </td>
                                        <!-- Delivery Date -->
                                        <td role="gridcell" title="{!orderItem.deliveryDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.deliveryDate}</span>
                                            </div>
                                        </td>
                                        <!-- Order Qty -->
                                        <td role="gridcell" title="{!orderItem.OrderQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.OrderQty}</span>
                                            </div>
                                        </td>
                                        <!-- haibo: 根据record type判断kit红绿灯展示 -->
                                        <!-- Inventory -->
                                        <td role="gridcell" title="Inventory">
                                            <aura:if isTrue="{!orderItem.productRecordType != 'TLS_VK'}">
                                                <aura:if isTrue="{!not(and(orderItem.productRecordType == 'MKT', orderItem.ProductList.length > 0))}">
                                                    <div class="slds-truncate icon-position-wrap">
                                                        <aura:if isTrue="{!orderItem.Inventory == 'Red'}">
                                                            <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                            <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                                <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                    <lightning:helptext class="icon-size" content="The order cannot be fulfilled now, and it shall be fulfilled after a period of time​"/>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                            </div>
                                                        </aura:if>
                                                        <aura:if isTrue="{!orderItem.Inventory == 'Yellow'}">
                                                            <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                            <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                                <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                    <aura:if isTrue="{! (v.basicInformation.Warehouse == 'China (DI)')}">
                                                                        <lightning:helptext class="icon-size" content="Availability will be checked by Chervon NJ"/>
                                                                        <aura:set attribute="else">
                                                                            <lightning:helptext class="icon-size" content="Please proceed with order placement ASAP due to insufficient stock inventory / planning tool​"/>
                                                                        </aura:set>
                                                                    </aura:if>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                            </div>
                                                        </aura:if>
                                                        <aura:if isTrue="{!orderItem.Inventory == 'Green'}">
                                                            <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                            <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                                <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                    <lightning:helptext class="icon-size" content="Please proceed with order placement​"/>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="Please proceed with order placement"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                            </div>
                                                        </aura:if>
                                                    </div>
                                                </aura:if>
                                            </aura:if>
                                        </td>
                                        <!-- Back Order Qty -->
                                        <td role="gridcell" title="{!orderItem.BackOrderQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.BackOrderQty}</span>
                                            </div>
                                        </td>
                                        <!-- Release to Warehouse Qty -->
                                        <aura:if isTrue="{!v.isInsideSales}">
                                            <td role="gridcell" title="{!orderItem.ReleasetoWarehouseQty}">
                                                <div class="slds-truncate clear-user-agent-styles">
                                                    <span>{!orderItem.ReleasetoWarehouseQty}</span>
                                                </div>
                                            </td>
                                        </aura:if>
                                        <!-- Picked & Packed Qty -->
                                        <aura:if isTrue="{!v.isInsideSales}">
                                            <td role="gridcell" title="{!orderItem.PickedPackedQty}">
                                                <div class="slds-truncate clear-user-agent-styles">
                                                    <span>{!orderItem.PickedPackedQty}</span>
                                                </div>
                                            </td>
                                        </aura:if>
                                        <!-- Shipped Qty -->
                                        <td role="gridcell" title="{!orderItem.ShippedQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.ShippedQty}</span>
                                            </div>
                                        </td>
                                        <!-- Invoiced Qty -->
                                        <td role="gridcell" title="{!orderItem.InvoicedQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.InvoicedQty}</span>
                                            </div>
                                        </td>
                                        <!-- Cancelled Qty -->
                                        <td role="gridcell" title="{!orderItem.CancelledQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.CancelledQty}</span>
                                            </div>
                                        </td>
                                        <!-- Returned Qty -->
                                        <td role="gridcell" title="{!orderItem.ReturnedQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.ReturnedQty}</span>
                                            </div>
                                        </td>
                                        <!-- Est Replenish Date -->
                                        <aura:if isTrue="{!v.isInsideSales}">
                                            <td role="gridcell" title="{!orderItem.EstReplenishDate}">
                                                <div class="slds-truncate clear-user-agent-styles" >
                                                    <span>{!orderItem.EstReplenishDate}</span>
                                                </div>
                                            </td>
                                        </aura:if>
                                        <!-- Schedule Ship Date​ -->
                                        <td role="gridcell" title="{!orderItem.ScheduleShipDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.ScheduleShipDate}</span>
                                            </div>
                                        </td>
                                        <!-- Pricing Date -->
                                        <td role="gridcell" title="{!orderItem.PricingDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.PricingDate}</span>
                                            </div>
                                        </td>
                                        <!-- List Price -->
                                        <td role="gridcell" title="{!orderItem.ListPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.ListPrice}" style="currency" currencyCode="{!orderItem.currencyCode}"/>
                                            </div>
                                        </td>
                                        <!-- Discount -->
                                        <td role="gridcell" title="{!orderItem.Discount}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.Discount}</span>
                                            </div>
                                        </td>
                                        <!-- Unit Net Price -->
                                        <td role="gridcell" title="{!orderItem.UnitNetPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.UnitNetPrice}" style="currency" currencyCode="{!orderItem.currencyCode}"/>
                                            </div>
                                        </td>
                                        <!-- Total Net Price -->
                                        <td role="gridcell" title="{!orderItem.TotalNetPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.TotalNetPrice}" style="currency" currencyCode="{!orderItem.currencyCode}"/>
                                            </div>
                                        </td>
                                        <!-- Remark -->
                                        <aura:if isTrue="{!v.isInsideSales}">
                                            <td role="gridcell" title="{!orderItem.Remark}">
                                                <div class="slds-truncate clear-user-agent-styles">
                                                    <span>{!orderItem.Remark}</span>
                                                </div>
                                            </td>
                                        </aura:if>
                                        <!-- Sales Agreement No. -->
                                        <aura:if isTrue="{!v.isInsideSales}">
                                            <td role="gridcell" title="{!orderItem.SalesAgreementNo}">
                                                <div class="slds-truncate clear-user-agent-styles">
                                                    <span>{!orderItem.SalesAgreementNo}</span>
                                                </div>
                                            </td>
                                        </aura:if>
                                    </tr>
                                    <aura:if isTrue="{!orderItem.ProductList}">
                                        <tr aria-selected="false" class="slds-hint-parent" id="{!('row' + index)}" style="display: none">
                                            <td colspan="26">
                                                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productToolTable" role="grid">
                                                    <thead>
                                                        <tr class="slds-line-height_reset">
                                                            <!-- No. -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="No.">{!$Label.c.CCM_Line}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Product Description -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Product Description">{!$Label.c.Order_ProductDescription}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Model #  -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Model #">{!$Label.c.Order_Model}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- UOM -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="UOM">{!$Label.c.Order_UOM}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Order Date -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Order Date">{!$Label.c.Order_OrderDate}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Request Date -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Request Date">{!$Label.c.Order_RequestDate}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Order Qty -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Order Qty">{!$Label.c.CCM_OrderQty}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Inventory -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Inventory">{!$Label.c.Order_Inventory}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                             <!-- List Price -->
                                                             <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="List Price">{!$Label.c.Order_ListPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Unit Net Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Unit Net Price">{!$Label.c.Order_UnitNetPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Total Net Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Total Net Price">{!$Label.c.Order_TotalNetPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <aura:iteration items="{!orderItem.ProductList}" var="toolItem" indexVar="toolIndex">
                                                            <tr aria-selected="false" class="slds-hint-parent">
                                                                <!-- line -->
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                        {!index + 1}.{!toolIndex + 1}
                                                                    </div>
                                                                </td>
                                                                <!-- Product Description -->
                                                                <td role="gridcell" title="{!orderItem.ProductDescription}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!toolItem.ProductDescription}
                                                                    </div>
                                                                </td>
                                                                <!-- Model #  -->
                                                                <td role="gridcell" title="{!orderItem.Model}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        <span>{!toolItem.Model}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- UOM -->
                                                                <td role="gridcell" title="{!orderItem.OrderDate}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        <span>{!toolItem.UOM}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- Order Date -->
                                                                <td role="gridcell" title="{!orderItem.RequestDate}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <span>{!toolItem.OrderDate}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- Request Date -->
                                                                <td role="gridcell" title="{!orderItem.RequestDate}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <span>{!toolItem.RequestDate}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- Order Qty -->
                                                                <td role="gridcell" title="{!orderItem.OrderQty}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <span>{!toolItem.OrderQty}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- haibo: 根据record type判断kit红绿灯展示 -->
                                                                <!-- Inventory -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate icon-position-wrap">
                                                                        <aura:if isTrue="{!or(orderItem.productRecordType == 'TLS_VK', and(orderItem.productRecordType == 'MKT', orderItem.ProductList.length>0))}">
                                                                            <aura:if isTrue="{!toolItem.Inventory == 'Red'}">
                                                                                <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                                                <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                        <lightning:helptext class="icon-size" content="The order cannot be fulfilled now, and it shall be fulfilled after a period of time​"/>
                                                                                        <aura:set attribute="else">
                                                                                            <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                                        </aura:set>
                                                                                    </aura:if>
                                                                                </div>
                                                                            </aura:if>
                                                                            <aura:if isTrue="{!toolItem.Inventory == 'Yellow'}">
                                                                                <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                                                <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                        <aura:if isTrue="{! (v.basicInformation.Warehouse == 'China (DI)')}">
                                                                                            <lightning:helptext class="icon-size" content="Availability will be checked by Chervon NJ"/>
                                                                                            <aura:set attribute="else">
                                                                                                <lightning:helptext class="icon-size" content="Please proceed with order placement ASAP due to insufficient stock inventory / planning tool​"/>
                                                                                            </aura:set>
                                                                                        </aura:if>
                                                                                        <aura:set attribute="else">
                                                                                            <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                                        </aura:set>
                                                                                    </aura:if>
                                                                                </div>
                                                                            </aura:if>
                                                                            <aura:if isTrue="{!toolItem.Inventory == 'Green'}">
                                                                                <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                                                <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                        <lightning:helptext class="icon-size" content="Please proceed with order placement​"/>
                                                                                        <aura:set attribute="else">
                                                                                            <lightning:helptext class="icon-size" content="Please proceed with order placement"/>
                                                                                        </aura:set>
                                                                                    </aura:if>
                                                                                </div>
                                                                            </aura:if>
                                                                        </aura:if>
                                                                    </div>
                                                                </td>
                                                                <!-- List Price -->
                                                                <td role="gridcell" title="{!orderItem.ListPrice}">
                                                                    <div class="slds-truncate">
                                                                        <lightning:formattedNumber value="{!toolItem.ListPrice}" style="currency" currencyCode="{!toolItem.currencyCode}"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Unit Net Price -->
                                                                <td role="gridcell" title="{!toolItem.UnitNetPrice}">
                                                                    <div class="slds-truncate">
                                                                        <lightning:formattedNumber value="{!toolItem.UnitNetPrice}" style="currency" currencyCode="{!toolItem.currencyCode}"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Total Net Price -->
                                                                <td role="gridcell" title="{!toolItem.TotalNetPrice}">
                                                                    <div class="slds-truncate">
                                                                        <lightning:formattedNumber value="{!toolItem.TotalNetPrice}" style="currency" currencyCode="{!toolItem.currencyCode}"/>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </aura:iteration>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </aura:if>
                                </aura:iteration>
                            </tbody>
                        </table>
                    </div>
                    <div class="slds-clearfix slds-m-top--medium">
                        <lightning:layout>
                                <div class="c-container" style="padding: 10px">
                                    <lightning:layoutItem alignmentBump="right">
                                        <lightning:button variant="brand" label="Refresh Traffic Light" onclick="{!c.refreshTrafficLight}"/>
                                    </lightning:layoutItem>
                                </div>
                            </lightning:layout>
                        <div class="slds-grid slds-float--right">
                        <div class="slds-text-align--right">
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_TotalValue}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_HeaderDiscount}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_HeaderDiscountAmount}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_TotalValueNet}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_FreightCost}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_InsuranceFee}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_OtherFee}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_VAT}:&nbsp;</div>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.Order_TotalDueAmount}:&nbsp;</strong></div>
                        </div>
                        <div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.TotalValue)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong>{!v.basicInformation.HeaderDiscount}%</strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.HeaderDiscountAmount)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.TotalValueNet)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.FreightCost)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.InsuranceFee)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.OtherFee)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.VAT)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>

                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.basicInformation.TotalDueAmount}" style="currency" currencyCode="{!v.currencySymbol}"/> </strong></div>
                        </div>
                    </div>
                    </div>
                </div>
            </c:CCM_Section>

            <!-- shipmentInfo -->
            <div class="shipmentInfo">
                <c:CCM_Section title="{!$Label.c.CCM_ShipmentInformation}" expandable="true">
                    <div class="slds-p-left_medium slds-p-right--medium">
                        <aura:if isTrue="{!v.shipmentInfo.length > 0}">
                            <aura:iteration items="{!v.shipmentInfo}" var="shipment" indexVar="allIndex">
                                <aura:if isTrue="{!shipment.ShipmentItems.length > 0}">
                                    <lightning:layout multipleRows="true">
                                        <!-- Ship From -->
                                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                            <c:CCM_Field label="{!$Label.c.Order_ShipFrom}">
                                                <span>{!shipment.ShipmentData.ShipFrom.Street}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipFrom.City}&nbsp;&nbsp;{!shipment.ShipmentData.ShipFrom.PostCode}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipFrom.Country}</span>
                                            </c:CCM_Field>
                                        </lightning:layoutItem>
                                        <!-- Ship To -->
                                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                            <c:CCM_Field label="{!$Label.c.Order_ShipTo}">
                                                <span>{!shipment.ShipmentData.ShipTo.Street}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipTo.City}&nbsp;&nbsp;{!shipment.ShipmentData.ShipTo.PostCode}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipTo.Country}</span>
                                            </c:CCM_Field>
                                        </lightning:layoutItem>
                                    </lightning:layout>
                                    <div class="slds-p-left--medium slds-p-right--medium">
                                        <c:CCM_Section title="{!$Label.c.CCM_ShipmentItemInformation}" expandable="true" state="open">
                                            <div class="table-wrap">
                                                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productTable" role="grid">
                                                    <thead>
                                                        <tr class="slds-line-height_reset">
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Action">{!$Label.c.CCM_Action}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Product Description -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Product Description">{!$Label.c.Order_ProductDescription}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Model # -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Model #">{!$Label.c.Order_Model}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Item Qty in Shipment  -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Item Qty in Shipment">{!$Label.c.Order_ItemQtyinShipment}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Ship Set -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Ship Set">{!$Label.c.Order_ShipSet}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Shipped Date -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Shipped Date">{!$Label.c.CCM_ShippedDate}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <aura:iteration items="{!shipment.ShipmentItems}" var="shipmentItem" indexVar="shipmentIndex">
                                                            <tr aria-selected="false" id="{!shipmentIndex}" data-id="{!allIndex}" data-expanded="false" class="slds-hint-parent" onclick="{!c.showShipToolList}">
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                        <aura:if isTrue="{!shipmentItem.ProductList}">
                                                                            <lightning:icon iconName="utility:chevrondown" size="xx-small" class="collapse_icn"/>
                                                                        </aura:if>
                                                                    </div>
                                                                </td>
                                                                <!-- Product Description -->
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                        {!shipmentItem.ProductDescription}
                                                                    </div>
                                                                </td>
                                                                <!-- Model # -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.Model}
                                                                    </div>
                                                                </td>
                                                                <!-- Item Qty in Shipment  -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.ItemQtyInShipment}
                                                                    </div>
                                                                </td>
                                                                <!-- Ship Set -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.ShipSet}
                                                                    </div>
                                                                </td>
                                                                <!-- Shipped Date -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.ShippedDate}
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <aura:if isTrue="{!shipmentItem.ProductList}">
                                                                <tr aria-selected="false" class="slds-hint-parent" id="{!('ship' + shipmentIndex + allIndex)}" style="display: none">
                                                                    <td colspan="6" style="padding: 0;">
                                                                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productToolTable" role="grid">
                                                                            <thead>
                                                                                <tr class="slds-line-height_reset">
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                                                <span class="slds-truncate" title=""></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Product Description -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                                                <span class="slds-truncate" title="Product Description">{!$Label.c.Order_ProductDescription}</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Model #  -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title="Model #">{!$Label.c.Order_Model}</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Item Qty in Shipment -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title="Item Qty in Shipment">{!$Label.c.Order_ItemQtyinShipment}</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Item Qty in Shipment -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title=""></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title=""></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                <aura:iteration items="{!shipmentItem.ProductList}" var="shipmentToolItem" indexVar="shipmentToolIndex">
                                                                                    <tr aria-selected="false" class="slds-hint-parent">
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles" >
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- Product Description -->
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles" >
                                                                                                {!shipmentToolItem.ProductDescription}
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- Model #  -->
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles" >
                                                                                                <span>{!shipmentToolItem.Model}</span>
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- Order Qty -->
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles">
                                                                                                <span>{!shipmentToolItem.OrderQty}</span>
                                                                                            </div>
                                                                                        </td>
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles">
                                                                                                <span></span>
                                                                                            </div>
                                                                                        </td>
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles">
                                                                                                <span></span>
                                                                                            </div>
                                                                                        </td>
                                                                                    </tr>
                                                                                </aura:iteration>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </aura:if>
                                                        </aura:iteration>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </c:CCM_Section>
                                    </div>
                                </aura:if>
                            </aura:iteration>
                            <aura:set attribute="else">
                                <p class="slds-text-align_center slds-p-around--medium">{!$Label.c.CCM_NoItemsToDisplay}</p>
                            </aura:set>
                        </aura:if>
                    </div>
                </c:CCM_Section>
            </div>

            <!-- Attachment -->
            <c:CCM_Section title="{!$Label.c.CCM_Attachment}" expandable="true">
                <aura:if isTrue="{!v.attachment.length > 0}">
                    <c:CCM_DataTable columns="{!v.attachmentColumns}" data="{!v.attachment}"/>
                    <aura:set attribute="else">
                        <p class="slds-text-align_center slds-p-around--medium">{!$Label.c.CCM_NoItemsToDisplay}</p>
                    </aura:set>
                </aura:if>
            </c:CCM_Section>

            <!-- <aura:if isTrue="{!v.isDelegate}">
                <c:CCM_ApprovalHistory objectId="{!v.poRecordId}"/>
            </aura:if> -->

            <div class="CCM_PaddingTop slds-m-bottom_medium">
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Back}" title="Back" onclick="{!c.doBack}"/>
            </div>
        </div>
    </div>
</aura:component>