<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 03-05-2024
  @last modified by  : <EMAIL>
-->
<aura:component controller="CCM_FillPurchaseInfoController" description="CCM_PartsOrder_FillAddress_Cmp" implements="flexipage:availableForAllPageTypes,forceCommunity:availableForAllPageTypes">
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    
    <aura:attribute name="currentStep" type="Integer" />
    <aura:attribute name="purchaseOrderId" type="String"/>
    <aura:attribute name="customerId" type="String"/>
    <aura:attribute name="userType" type="String"/>
    
    <aura:attribute name="billToAddress" type="Object" default=""/>
    <aura:attribute name="shipToAddress" type="Object" default=""/>
    <aura:attribute name="dropshipType" type="String" default=""/>
    <aura:attribute name="dropshipTypeOptions" type="List" default="[]"/>
    <aura:attribute name="dropshipAddress" type="Object" default=""/>
    <aura:attribute name="freightCost" type="Decimal" default="0"/>
    <aura:attribute name="insuranceFee" type="Decimal" default="0"/>
    <aura:attribute name="otherFee" type="Decimal" default="0"/>
    <aura:attribute name="DropShipName" type="String" default=""/>
    <aura:attribute name="DropShipAddress1" type="String" default=""/>
    <aura:attribute name="DropShipAddress2" type="String" default=""/>
    <aura:attribute name="DropShipPhone" type="String" default=""/>
    <aura:attribute name="DropShipCountry" type="String" default=""/>
    <aura:attribute name="DropShipCity" type="String" default=""/>
    <aura:attribute name="DropShipZip" type="String" default=""/>
    <aura:attribute name="DropShipState" type="String" default=""/>
    <aura:attribute name="POId" type="String"/>
    <aura:attribute name="actionType" type="String"/>
    <aura:attribute name="isDropShip" type="String"/>
    <aura:attribute name="disableFlag" type="Boolean" default="false"/>
    <aura:attribute name="dropshipAddressFlag" type="Boolean" default="false"/>
    <aura:attribute name="showDetailfield" type="Boolean" default="true"/>
    <aura:attribute name="returnStep" type="Integer"/>
    <aura:attribute name="maxStep" type="Integer"/>
    <aura:attribute name="customerType" type="String"/>
    <aura:attribute name="tips" type="String" default="trips"/>
    <aura:attribute name="editFreightCost" type="Boolean"/>
    <aura:attribute name="showHelpText" type="Boolean"/>
    <aura:attribute name="currencySymbol" type="String"/>
    <aura:attribute name="isDE" type="String" default="EN"/>
    <aura:attribute name="defaultBillAddressInfo" type="String" default=""/>
    <aura:attribute name="defaultShipAddressInfo" type="String" default=""/>
    <aura:attribute name="pricingDate" type="String"/>
    <aura:attribute name="comments" type="String"/>
    
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="change" value="{!v.dropshipType}" action="{!c.changeDropshipType}"/>
    <!-- <aura:handler name="change" value="{!v.DropShipAddress1}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.DropShipAddress2}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.DropShipPhone}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.DropShipCountry}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.DropShipCity}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.DropShipZip}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.DropShipState}" action="{!c.changeDropshipType}"/> -->
    
    <lightning:card class="mainContent">
        <div>
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <c:CCM_Section title="{!$Label.c.CCM_DeliveryInformation}" expandable="true" >
                <div class="address-wrap">
                    <lightning:layout multipleRows="true" horizontalAlign="space">
                        <!-- Bill To Address -->
                        <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                            <c:CCM_Community_LookUp
                                fieldName="{!$Label.c.CCM_BillToAddress}"
                                selectedValue="{!v.billToAddress}"
                                customerId="{!v.customerId}"
                                aura:id="billToAddress"
                                class="field-required"
                            />
                            <div aura:id="billToAddress-error-required" class="error-text slds-hide">This field is required</div>
                            <div class="address-content">
                                <aura:if isTrue="{!v.billToAddress.address.Street}">
                                    <p>{!v.billToAddress.address.Street}</p>
                                </aura:if>
                                <aura:if isTrue="{! (v.billToAddress.address.City || v.billToAddress.address.PostalCode)}">
                                    <p>
                                        <span>{!v.billToAddress.address.PostalCode}&nbsp;&nbsp;{!v.billToAddress.address.City}</span>
                                    </p>
                                </aura:if>
                                <aura:if isTrue="{!v.billToAddress.address.Country}">
                                    <p>{!v.billToAddress.address.Country}</p>
                                </aura:if>
                            </div>
                        </lightning:layoutItem>
                        <!-- Ship To Address -->
                        <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                            <c:CCM_Community_LookUp
                                fieldName="{!$Label.c.CCM_ShipToAddress}"
                                selectedValue="{!v.shipToAddress}"
                                customerId="{!v.customerId}"
                                aura:id="shipToAddress"
                                class="field-required"
                            />
                            <div aura:id="shipToAddress-error-required" class="error-text slds-hide">This field is required</div>
                            <div class="address-content">
                                <aura:if isTrue="{!v.shipToAddress.address.Street}">
                                    <p>{!v.shipToAddress.address.Street}</p>
                                </aura:if>
                                <aura:if isTrue="{! (v.shipToAddress.address.City || v.shipToAddress.address.PostalCode)}">
                                    <p>
                                        <span>{!v.shipToAddress.address.PostalCode}&nbsp;&nbsp;{!v.shipToAddress.address.City}</span>
                                    </p>
                                </aura:if>
                                <aura:if isTrue="{!v.shipToAddress.address.Country}">
                                    <p>{!v.shipToAddress.address.Country}</p>
                                </aura:if>
                            </div>
                        </lightning:layoutItem>
                        <!-- 详情字段 -->
                        <aura:if isTrue="{!v.showDetailfield}">
                            <!-- DropShipAddress1 -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <lightning:input type="text" name="DropShipAddress1" label="Dropship Address 1" value="{!v.DropShipAddress1}"/>
                            </lightning:layoutItem>
                            <!-- DropShipAddress2 -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <lightning:input type="text" name="DropShipAddress2" label="Dropship Address 2" value="{!v.DropShipAddress2}"/>
                            </lightning:layoutItem>
                            <!-- DropShipPhone -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <lightning:input type="text" name="DropShipPhone" label="Dropshiop PhoneNumber" value="{!v.DropShipPhone}"/>
                            </lightning:layoutItem>
                            <!-- DropShipCountry -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <lightning:input type="text" name="DropShipCountry" label="Dropship Country" value="{!v.DropShipCountry}"/>
                            </lightning:layoutItem>
                            <!-- DropShipCity -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <lightning:input type="text" name="DropShipCity" label="Dropship City" value="{!v.DropShipCity}"/>
                            </lightning:layoutItem>
                            <!-- DropShipZip -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <lightning:input type="text" name="DropShipZip" label="Dropship Postal Code" value="{!v.DropShipZip}"/>
                            </lightning:layoutItem>
                            <!-- DropShipState -->
                            <lightning:layoutItem padding="around-small" size="6">
                                <lightning:input type="text" name="DropShipState" label="Dropship Province/State" value="{!v.DropShipState}"/>
                            </lightning:layoutItem>
                            <!-- 占位 -->
                            <lightning:layoutItem padding="around-small" size="6">
                            </lightning:layoutItem>
                        </aura:if>
                    </lightning:layout>
                </div>
            </c:CCM_Section>
            <c:CCM_Section title="{!$Label.c.CCM_FeeInformation}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Freight Cost -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <aura:if isTrue="{!v.editFreightCost}">
                                <lightning:input type="number" name="freightcost" label="{!$Label.c.CCM_FreightCost}" value="{!v.freightCost}" step="0.01" disabled="{! (v.userType != 'InsideSales')}"/>
                                <aura:set attribute="else">
                                    <lightning:input type="number" name="freightcost" label="{!$Label.c.CCM_FreightCost}" value="{!v.freightCost}" step="0.01" disabled="true"/>
                                </aura:set>
                            </aura:if>
                            <aura:if isTrue="{!v.showHelpText}">
                                <aura:if isTrue="{!(v.customerType == 'DE Customer')}">
                                    <aura:if isTrue="{!(v.isDE == 'EN')}">
                                        <div class="hlep-wrap">
                                            <lightning:helptext content="Order value: 0-150€ -> Freight cost: 7€;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        Order value: 150-500€ -> Freight cost: 13€​​​​;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        Order value: 500-1000€ -> Freight cost: 16.50€​​​​​​​​;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        Order value: over 1000€ -> Freight cost: 0€​;
                                            ​"/>
                                        </div>
                                        <aura:set attribute="else">
                                            <div class="hlep-wrap">
                                                <lightning:helptext content="Auftragswert 0-150€ --> Frachtkosten: 7€;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            Auftragswert 150-500€ --> Frachtkosten: 13€​​​​;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            Auftragswert 500-1.000€ --> Frachtkosten: 16,50€​​​​​​​​;&nbsp;&nbsp;&nbsp;
                                                                            Auftragswert mehr als 1.000€ --> Frachtkosten: 0€​;
                                                ​"/>
                                            </div>
                                        </aura:set>
                                    </aura:if>
                                </aura:if>
                                <aura:if isTrue="{!(v.customerType == 'PBE')}">
                                    <div class="hlep-wrap">
                                        <lightning:helptext content="{!$Label.c.CCM_FreightCostTip1}"/>
                                    </div>
                                </aura:if>
                                <aura:if isTrue="{!(v.customerType == 'HKL')}">
                                    <div class="hlep-wrap">
                                        <lightning:helptext content="{!$Label.c.CCM_FreightCostTip1}"/>
                                    </div>
                                </aura:if>
                            </aura:if>
                        </div>
                    </lightning:layoutItem>

                    <!-- Insurance Fee -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <lightning:input type="number" name="insuranceFee" label="{!$Label.c.CCM_InsuranceFee}" value="{!v.insuranceFee}" step="0.01" disabled="{! (v.userType != 'InsideSales')}"/>
                        </div>
                    </lightning:layoutItem>
                    <!-- Other Fee -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <lightning:input type="number" name="otherFee" label="{!$Label.c.CCM_OtherFee}" value="{!v.otherFee}" step="0.01" disabled="{! (v.userType != 'InsideSales')}"/>
                        </div>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>

            <c:CCM_Section title="{!$Label.c.CCM_Comments}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Comments -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <lightning:input type="text" name="comments" label="{!$Label.c.CCM_Comments}" value="{!v.comments}" />
                        </div>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
        </div>

        <aura:set attribute="footer">
            <div class="footer-wrap">
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SaveAndClose}" onclick="{!c.saveAndClose}" />
                <lightning:button class="previous-btn-left" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancel}" />
                <lightning:button class="previous-btn" variant="brand-outline" label="{!$Label.c.CCM_Previous}" onclick="{!c.previousStep}" />
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Next}" onclick="{!c.nextStep}" />
            </div>
        </aura:set>
    </lightning:card>
</aura:component>