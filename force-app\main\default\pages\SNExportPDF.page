<apex:page applyBodyTag="false" controller="SNExportPDFController" readonly="true" renderAs="pdf" >
	<head> 
		<meta charset="utf-8" />
        <style>
			body { font-family: 'Arial'; font-size: 8px;} 
			@page {
				size: A4 landscape;
				margin: 10mm;
			}
		</style>
	</head>
	<body style="font-family: 'Arial'; font-size: 8px;">
	<h1 style="text-align: center;">Item Serial Number Traceability Report</h1>
		<div  style="border:solid 1px Black;">
			<div>
				<table border="0" width = "100%" cellspacing="0" style="Black"> 
	                <thead style="background: lightgray;">
	                	<tr>
		                    <td align="center" width="5%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_OrderedDate}</td>
		                    <td align="center" width="5%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_ShipmentDate}</td>
		                    <td align="center" width="5%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_ModelNo}</td>
							<td align="center" width="5%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_BareToolModel}</td>
		                    <td align="center" width="15%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_ProductShortDescription}</td>
		                    <td align="center" width="4%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_CustomerCode}</td>	  
		                    <td align="center" width="8%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_BillToName}</td>
		                    <td align="center" width="8%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_ShipToName}</td>
		                    <!-- <td align="center" width="3%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_ShipToCountry}</td> -->
		                    <td align="center" width="4%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_SalesOrderNo}</td>
		                    <td align="center" width="4%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_DeliveryNoteNo}</td>
		                    <!-- <td align="center" width="1%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_Subinventory}</td> -->
		                    <td align="center" width="3%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_OrderedQuantity}</td>
		                    <td align="center" width="4%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_InvoiceNo}</td>
		                    <td align="center" width="4%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_ContainerNo}</td>
		                    <!-- <td align="center" width="4%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_BatchNo}</td> -->
		                    <td align="center" width="6%" style="border-left:solid 1px Black; background: #E8E8E8">{!$Label.CCM_SerialNo}</td>
		                </tr>
	                </thead>
	                <tbody>
		                <apex:repeat var="sn" value="{!snList}" >
			                <tr>
			                    <td width="5%" style = "text-align: center;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.orderedDate}"/></td>
			                    <td width="5%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.shipmentDate}"/></td>
			                    <td width="5%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.modelNo}"/></td>
								<td width="5%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.bareToolModel}"/></td>
			                    <td width="15%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.productShortDescription}"/></td>
			                    <td width="4%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.customerCode}"/></td>
			                    <td width="8%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.billToName}"/></td>
			                    <td width="8%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.shipToName}"/></td>
			                    <!-- <td width="3%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.shipToCountry}"/></td> -->
			                    <td width="4%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.salesOrderNo}"/></td>
			                    <td width="4%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.deliveryNoteNo}"/></td>
			                    <!-- <td width="1%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.subInventory}"/></td> -->
			                    <td width="3%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.orderedQuantity}"/></td>
			                    <td width="4%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.invoiceNo}"/></td>
			                    <td width="4%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.containerNo}"/></td>
			                    <!-- <td width="4%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.batchNo}"/></td> -->
			                    <td width="6%" style = "text-align: center;border-left:solid 1px Black;border-top:solid 1px Black;"><apex:outputLabel value="{!sn.SN}"/></td>

			                </tr>
		            	</apex:repeat>    
	            	</tbody>
	            </table> 
		        	
			</div>
		</div>
	</body>
	
</apex:page>