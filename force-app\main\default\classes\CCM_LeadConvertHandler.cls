/**************************************************************************************************
 * Name: CCM_LeadConvertHandler
 * Object: Lead
 * Purpose: Auto Convert for Lead
 * Author:Aria W Zhong
 * Create Date: 2023-06-12
 * Modify History:
 **************************************************************************************************/
public without sharing class CCM_LeadConvertHandler implements Triggers.Handler{
    static boolean isRun = true;
    public void handle(){
        if (isRun){
            if (Trigger.isUpdate){
                if (Trigger.isAfter){
                    leadConvert((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                }
                if (Trigger.isBefore){
                    changeStatus((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                    defaultOwner((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                }
            }

        }
    }
    private void defaultOwner(List<Lead> newList, Map<Id, Lead> oldMap){
        for (Lead newItem : newList){
            if (newItem.Status == 'Pending Review' && newItem.Approval_Status__c == 'Pending for Approval'){
                Lead queryItem = [select owner.Name
                                  from Lead
                                  where Id = :newItem.Id];
                if (queryItem.Owner.Name == 'Inside Sales'){
                    newItem.OwnerId = UserInfo.getUserId();
                }
            }
        }
    }
    private void changeStatus(List<Lead> newList, Map<Id, Lead> oldMap){
        for (Lead newItem : newList){
            if (newItem.Status == 'Pending Review' && newItem.Approval_Status__c == 'Approved'){
                //判断类型
                if (newItem.Prospect_Type__c == 'End User'){
                    newItem.Status = 'Converted';
                } else if (newItem.Prospect_Type__c == 'Brand Partner'){
                    //生成Address关联集体customer
                    newItem.Status = 'Converted';
                }
            }
        }
    }
    private void leadConvert(List<Lead> newList, Map<Id, Lead> oldMap){
        for (Lead newItem : newList){
            Lead oldItem = oldMap.get(newItem.Id);
            if (newItem.Status == 'Converted' && newItem.Approval_Status__c == 'Approved'){
                //判断类型
                if (newItem.Prospect_Type__c == 'End User'){
                    //关联consumer
                } else if (newItem.Prospect_Type__c == 'Brand Partner'){
                    //生成Address关联集体customer
                    createAddress(newItem);
                }
            }
            if (newItem.Status == 'Pending Review' && newItem.Approval_Status__c == 'Approved'){
                if (newItem.Prospect_Type__c <> 'End User' && newItem.Prospect_Type__c <> 'Brand Partner'){
                    //生成customer
                    customerConvert(newItem);
                }
            }
        }
    }
    private void createAddress(Lead lead){
        //获取要转换的字段
        Map<String, Schema.FieldSetMember> prospectFieldMap = new Map<String, Schema.FieldSetMember>();
        List<Schema.FieldSetMember> fieldSetMemberList = Schema.getGlobalDescribe().get('Lead').getDescribe().FieldSets.getMap().get('Covert_Address').getFields();
        for (FieldSetMember item : fieldSetMemberList){
            prospectFieldMap.put(item.getLabel(), item);
        }
        Map<String, Schema.SObjectField> addrssFieldMap = new Map<String, Schema.SObjectField>();
        Map<String, Schema.SObjectField> mfieldMap = Schema.getGlobalDescribe().get('Account_Address__c').getDescribe().fields.getMap();
        for (String mfield : mfieldMap.keySet()){
            addrssFieldMap.put(mfieldMap.get(mfield).getDescribe().getLabel(), mfieldMap.get(mfield));
        }
        Account_Address__c address = new Account_Address__c();
        address.RecordTypeId = CCM_Constants.DROPSHIP_SHIPPING_ADDRESS_RECORDTYPEID;
        address.OwnerId = lead.OwnerId;
        //根据区块数据分别导入
        //获取哪些国家是欧洲
        Set<String> EuSet = new Set<String>();
        //lead查询不了picklist,用Account代替?
        Schema.DescribeFieldResult fieldResult = Account.Country_EU__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for (Schema.PicklistEntry item : ple){
            EuSet.add(String.valueOf(item.value));
        }
        //获取对应组织的customer
        Map<String, String> customerMap = new Map<String, String>();
        for (Propsect_Address_Customer__mdt item : [select Customer_Type__c, Customer_Id__c
                                                    from Propsect_Address_Customer__mdt]){
            customerMap.put(item.Customer_Type__c, item.Customer_Id__c);
        }

        if (lead.Country__c == 'DE'){
            address.Customer__c = customerMap.get('DE');
        } else if (EuSet.contains(lead.Country__c)){
            address.Customer__c = customerMap.get('EU');
        } else{
            address.Customer__c = customerMap.get('Other');
        }
        address.Prospect__c = lead.Id;
        address.Company_name_1__c = lead.Company;
        String comapny = String.isNotBlank(lead.Company) ? lead.Company : '';
        String street1 = String.isNotBlank(lead.Street_1__c) ? lead.Street_1__c : '';
        String street2 = String.isNotBlank(lead.Street_2__c) ? lead.Street_2__c : '';
        String city = String.isNotBlank(lead.City__c) ? lead.City__c : '';
        String postcode = String.isNotBlank(lead.Postal_Code__c) ? lead.Postal_Code__c : '';
        address.Name = comapny + ' ' + street1 + ' ' + street2 + ' ' + city + ' ' + postcode;
        if (address.Name.length() > 80){
            address.Name = address.Name.substring(0, 80);
        }
        for (String label : prospectFieldMap.keySet()){
            if (addrssFieldMap.containsKey(label)){
                address.put(addrssFieldMap.get(label).getDescribe().getName(), lead.get(prospectFieldMap.get(label).getFieldPath()));
            }
        }
        insert address;
    }
    private void customerConvert(Lead lead){
        Database.LeadConvert leadConvertObj = new database.LeadConvert();
        leadConvertObj.setLeadId(lead.Id);
        leadConvertObj.setDoNotCreateOpportunity(true);
        leadConvertObj.setConvertedStatus('Converted');
        Database.LeadConvertResult leadConvertResult = Database.convertLead(leadConvertObj);
        //update account
        Id accountId = leadConvertResult.getAccountId();
        Account account = new Account(Id = accountId);
        if (lead.RecordTypeId == Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get('Prospect_Channel').getRecordTypeId()){
            account.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        }
        if (lead.RecordTypeId == Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get('Prospect_Assoication_Group').getRecordTypeId()){
            account.RecordTypeId = CCM_Constants.ASSOCIATIONGROP_RECORDTYPEID;
        }
        account.Standard_Edit_Process__c=false;
        update account;
        //related list
        lead = [SELECT Id, ConvertedAccountId, RecordTypeId, 
                (SELECT Id, Customer__c, Status__c, Approval_Status__c FROM Sales_Program__r), 
                (SELECT Id, Customer__c FROM Attachment_Management__r), 
                (SELECT Id, Customer__c FROM Customer_Profile__r), 
                (SELECT Id, Customer__c FROM Account_Address__r),
                (SELECT Id FROM Contacts__r)
                FROM Lead
                WHERE Id = :lead.Id
        ];
        List<Sales_Program__c> spList = new List<Sales_Program__c>();
        List<Attachment_Management__c> amList = new List<Attachment_Management__c>();
        List<Customer_Profile__c> cpList = new List<Customer_Profile__c>();
        List<Account_Address__c> aaList = new List<Account_Address__c>();
        List<Contact> contactList = new List<Contact>();
        for (Sales_Program__c sp : lead.Sales_Program__r){
            //sp.Prospect__c = null;
            sp.Customer__c = accountId;
            sp.Standard_Edit_Process__c=false;
            // 24.4.23: Active的Authorized Brand批准状态更新为Approved
            if(sp.Status__c == 'Active' && sp.Approval_Status__c == 'Draft'){
                sp.Approval_Status__c = 'Approved';
            }
            spList.add(sp);
        }
        for (Attachment_Management__c am : lead.Attachment_Management__r){
            am.Customer__c = accountId;
            amList.add(am);
        }
        for (Customer_Profile__c cp : lead.Customer_Profile__r){
            cp.Customer__c = accountId;
            cpList.add(cp);
        }
        for (Account_Address__c aa : lead.Account_Address__r){
            aa.Customer__c = accountId;
            aaList.add(aa);
        }

        for(Contact con : lead.Contacts__r) {
            con.AccountId = accountId;
            contactList.add(con);
        }

        if (spList.size() > 0){
            update spList;
        }

        if (amList.size() > 0){
            update amList;
        }
        if (cpList.size() > 0){
            update cpList;
        }

        if (aaList.size() > 0){
            update aaList;
        }

        if(contactList.size() > 0) {
            update contactList;
        }
        
        assignModifier(accountId);
    }
    

    public static void assignModifier(String accountId){
        System.debug('after prospect----开始执行 assign modifier =====》');

        List<Customer_Query_Condition__c> lstCondition = new List<Customer_Query_Condition__c>();

        lstCondition = [Select c.Id, c.Is_Assigned__c, c.Modifier_Header_Number__c, c.New_SOQL__c, c.Old_SOQL__c
                        from Customer_Query_Condition__c c];

        System.debug('after prospect---qualfiier规则条数==》' + lstCondition.size());

        if (lstCondition.size() > 0){

            CCM_CustomerQueryConditionBatch b = new CCM_CustomerQueryConditionBatch(lstCondition, new Set<String>{accountId});

            Database.executeBatch(b, 1);

        }
    }
}