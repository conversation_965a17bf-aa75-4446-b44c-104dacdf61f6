({
    // 附件上传
    uploadFile : function(component, fileBase64, name) {
        component.set('v.isBusy', true);
        const self = this;
        console.log('附件上传===========');
        const action = component.get("c.uploadFile");
        action.setParams({
            recordId: component.get('v.recordId'),
            type: 'Attachment',
            fileName: name,
            content: fileBase64
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'state=========');
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, 'res附件上传=========');
                self.searchFileList(component);
            } else { 
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取附件列表
    searchFileList : function(component) {
        component.set('v.isBusy', true);
        console.log('获取附件列表===========');
        const action = component.get("c.searchFile");
        action.setParams({
            recordId: component.get('v.recordId'),
            type: 'Attachment'
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'state=========');
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                console.log(res, 'res获取附件列表=========');
                component.set('v.attachmentList', res);
            } else { 
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 删除附件
    deleteFile : function(component, id) {
        let self = this;
        component.set('v.isBusy', true);
        console.log('删除附件===========');
        const action = component.get("c.deleteFile");
        action.setParams({
            contentDocumentId: id,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'state=========');
            if (state === "SUCCESS") {
                self.searchFileList(component);
            } else { 
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
                component.set('v.isBusy', false);
            }
        });
        $A.enqueueAction(action);
    },

    linkFile: function(component, uploadedFiles) {
        const contentVersionId = uploadedFiles[0].contentVersionId;
        const documentId = uploadedFiles[0].documentId;
        let action = component.get('c.linkFile');
        action.setParams({'recordId': component.get('v.recordId'), 'versionId': contentVersionId, 'documentId': documentId});
        action.setCallback(this, function(response){
            let state = response.getState();
            if(state === 'SUCCESS') {
                this.searchFileList(component);
            }
            else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    }
})