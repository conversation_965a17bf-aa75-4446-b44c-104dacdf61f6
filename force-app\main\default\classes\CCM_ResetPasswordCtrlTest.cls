/**
 * Test class for CCM_ResetPasswordCtrl
 */
@isTest
private class CCM_ResetPasswordCtrlTest {

    @TestSetup
    static void makeData(){
        Account testAccount = new Account(Name = 'Test Account', Standard_Edit_Process__c = true, RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Channel').getRecordTypeId(), Approval_Status__c = 'Approved');
        insert testAccount;

        Contact testContact = new Contact(LastName = 'Test Contact');
        testContact.AccountId = testAccount.Id;
        insert testContact;
    }

    @isTest
    static void resetPasswordTest() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CCM_ResetPasswordCtrl.resetPassword(acc.Id);
    }

    @IsTest
    static void resetPasswordContactTest(){
        Test.startTest();
        Contact con = [SELECT Id FROM Contact LIMIT 1];
        CCM_ResetPasswordCtrl.resetPassword(con.Id);
        Test.stopTest();
    }
}