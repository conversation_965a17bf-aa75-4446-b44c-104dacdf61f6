({
    // 获取customerid
    queryRegisterList : function(component, event, helper) {
        const self = this;
        var action = component.get("c.GetCurrentUserCustomer");
        action.setParams({});
        action.setCallback(this, function (response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                const res = response.getReturnValue();
                console.log(JSON.stringify(res), '获取到的CustomerId');
                component.set('v.customerId',res.CustomerId)
                component.set('v.currencySymbol',res.CurrencyIsoCode)
                self.gettraininglist(component)
                self.getinitNumberInfo(component)
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    // 获取当前用户的training order list
    gettraininglist : function(component, event, helper) {
        var action = component.get("c.getOrderInfo");
        action.setParams({
            "pageNumber": component.get('v.pageNumber'),
            "pageSize": component.get('v.pageCount'),
            "fifter": "",
        });

        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('获取历史课程',response);
            console.log('state--->',state,state == "SUCCESS");
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                console.log(JSON.stringify(results),"获取历史课程");
                results.data.forEach(item =>{
                    item.row = {
                        Id: item.trainingOrderId ? item.trainingOrderId : item.registerId,
                    }
                    item.totalDueAmount = item.totalDueAmount + ' ' + component.get('v.currencySymbol')
                })
                component.set('v.HistoryData',results.data)
                component.set('v.totalRecords',results.size)
            }
        //     helper.loading(component, false);
        });
        $A.enqueueAction(action);
    },
    // 获取当前用户的小眼睛数据
    getinitNumberInfo : function(component, event, helper) {
        var action = component.get("c.initNumberInfo");
        action.setParams({
            customerId : component.get('v.customerId')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('response1111',response);
            console.log('state--->',state,state == "SUCCESS");
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                console.log("gggg",JSON.parse(results).totalRegisterOrderNum);
                console.log(JSON.stringify(results),"results");
                component.set('v.pending',JSON.parse(results).totalRegisterOrderNum)
                component.set('v.issued',JSON.parse(results).invoiceIssued)
                component.set('v.inProgress',JSON.parse(results).invoiceInProcess)
                component.set('v.paid',JSON.parse(results).invoiceCompleted)
                component.set('v.approved',JSON.parse(results).totalorderCompleteNumber)
                component.set('v.rejected',JSON.parse(results).orderreceiptNumber)
                results.forEach(item =>{
                    item.row = {
                        Id: item.trainingOrderId ? item.trainingOrderId : item.registerId,
                    }
                })
                // component.set('v.currentData',results)
            }
        //     helper.loading(component, false);
        });
        $A.enqueueAction(action);
    },
    // 获取当前用户全部可用课程
    getTrainingCrouseList : function(component, event, helper) {
        var action = component.get("c.queryCourseSettingList");
        action.setParams({
            pageNumber: 1,
            pageSize: 10
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->>>>>>>>>',state,state == "SUCCESS");
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                component.set('v.TrainingCourseList',results)
            }
        //     helper.loading(component, false);
        });
        $A.enqueueAction(action);
        // var action = component.get("c.queryCourseSettingList");
        // action.setParams({
        //     pageNumber: 1,
        //     pageSize: 10
        // });
        // console.log("获取可用课程2",action);
        // action.setCallback(this, function (response) {
        //     console.log("response",response);
        //     var state = response.getState();
        //     console.log('state---获取可用课程3',state,state == "SUCCESS");
        //     if (state == "SUCCESS") {
        //         var results = response.getReturnValue();
        //         results[0].id = "a1g7Y00000Hg2dvQAB"
        //         console.log(JSON.stringify(results),"获取可用课程");
        //         component.set('v.TrainingCourseList',results)
        //     }
        // //     helper.loading(component, false);
        // });
        // $A.enqueueAction(action);
    },
    
})