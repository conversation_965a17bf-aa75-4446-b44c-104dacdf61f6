@isTest
public with sharing class CCM_PurchaseBatchUploadController_Test{
    @TestSetup
    static void init(){
        Account acc = new Account(Name = 'test', AccountNUmber = 'CUST001',CurrencyIsoCode = 'EUR');
        insert acc;
        Product2 prod1 = new Product2();
        prod1.Name = 'BH1001';
        prod1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        prod1.ExternalId__c = 'BH1001';
        prod1.Order_Model__c = 'BH1001';
        prod1.Master_Product__c = 'BH1001';
        insert prod1;
        Product2 kit = new Product2(Name = 'Test kit', ExternalId__c = 'kit01', Order_Model__c = 'kit01', IsActive = true, Brand_Name__c = 'EGO', RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByName().get('TLS_KIT').getRecordTypeId());
        insert kit;
        insert new PricebookEntry(Pricebook2Id = Test.getStandardPricebookId(), Product2Id = prod1.Id, UnitPrice = 100, IsActive = true);
        Pricebook2 pricebook = new Pricebook2(Name = 'priceBook');
        insert pricebook;
        Pricebook_Entry__c priceEntry = new Pricebook_Entry__c(PriceBook__c = pricebook.Id);
        priceEntry.start_Date__c = Date.today().addDays(-7);
        priceEntry.End_Date__c = Date.today().addDays(7);
        priceEntry.UnitPrice__c = 10;
        priceEntry.Product__c = kit.Id;
        priceEntry.CurrencyIsoCode = 'EUR';
        priceEntry.PriceBook__c = pricebook.Id;
        insert priceEntry;
        Pricebook_Entry__c priceEntry2 = new Pricebook_Entry__c(PriceBook__c = pricebook.Id);
        priceEntry2.start_Date__c = Date.today().addDays(-7);
        priceEntry2.End_Date__c = Date.today().addDays(7);
        priceEntry2.UnitPrice__c = 10;
        priceEntry2.Product__c = prod1.Id;
        priceEntry2.CurrencyIsoCode = 'EUR';
        priceEntry2.PriceBook__c = pricebook.Id;
        insert priceEntry2;
        Kit_Item__c testKitItem = new Kit_Item__c(Kit__c = kit.Id, VK_Product__c = prod1.Id);
        testKitItem.Quantity__c=12;
        insert testKitItem;
        Account_Address__c objAccountAddress = new Account_Address__c();
        objAccountAddress.Name = 'address1';
        objAccountAddress.Country__c = 'DE';
        objAccountAddress.City__c = 'Steinheim';
        objAccountAddress.Customer_Line_Oracle_ID__c = 'SHIP001';
        objAccountAddress.Address1__c = 'Autenbacherstr. 11';
        objAccountAddress.customer__c = acc.Id;
        objAccountAddress.RecordTypeId = CCM_Constants.ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_ID;
        insert objAccountAddress;
        Account_Address__c objAccountAddress2 = new Account_Address__c();
        objAccountAddress2.Name = 'address2';
        objAccountAddress2.Country__c = 'DE';
        objAccountAddress2.City__c = 'Steinheim';
        objAccountAddress2.Address1__c = 'Autenbacherstr. 121';
        objAccountAddress2.Customer_Line_Oracle_ID__c = 'BILL001';
        objAccountAddress2.customer__c = acc.Id;
        objAccountAddress2.RecordTypeId = CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID;
        insert objAccountAddress2;
        Sales_Program__c salesP1 = new Sales_Program__c(Approval_Status__c = 'Approved', Name = 'Test Sales Program1', Brands__c = 'Test Brand', Status__c = 'Active', Customer__c = acc.Id, Payment_Term__c = 'EEG06', RecordTypeId = CCM_Constants.AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SERVICE_CUSTOMIZED_Id);
        salesP1.Incoterm__c = 'DAP';
        salesP1.List_Price_1__c = pricebook.Id;
        salesP1.List_Price_2__c = pricebook.Id;
        salesP1.List_Price_3__c = pricebook.Id;
        salesP1.Price_Book__c = pricebook.Id;
        insert salesP1;
        Address_With_Program__c objAddressProgram = new Address_With_Program__c();
        objAddressProgram.Customer_Line_Oracle_ID__c = '168111';
        objAddressProgram.Account_Address__c = objAccountAddress.Id;
        objAddressProgram.Program__c = salesP1.Id;
        insert objAddressProgram;

        Address_With_Program__c objAddressProgram2 = new Address_With_Program__c();
        objAddressProgram2.Customer_Line_Oracle_ID__c = '167818';
        objAddressProgram2.Account_Address__c = objAccountAddress.Id;
        insert objAddressProgram2;

        Purchase_Order__c objPurchase = new Purchase_Order__c();
        objPurchase.Customer_PO_Num__c = '2023 Pre-season order DI NJ';
        objPurchase.Dropship_Address__c = objAccountAddress.Id;
        objPurchase.Warehouse__c = 'Germany (DSV)';
        objPurchase.Customer__c = acc.Id;
        objPurchase.Dropship_Address__c = objAccountAddress.Id;
        objPurchase.Authorized_Brand__c = salesP1.Id;
        insert objPurchase;
        Purchase_Order_Item__c objPurchaseOrderItem = new Purchase_Order_Item__c();
        objPurchaseOrderItem.Purchase_Order__c = objPurchase.Id;
        objPurchaseOrderItem.Product__c = kit.Id;
        objPurchaseOrderItem.Quantity__c = 10;
        objPurchaseOrderItem.Request_Date__c = Date.today().addDays(-5);
        insert objPurchaseOrderItem;
        
     
        Modifier__c modifier1 = new Modifier__c(Name = 'Test');
        insert modifier1;
        Modifier_Entry__c mentry = new Modifier_Entry__c();
        mentry.Value__c = 10;
        mentry.Modifier__c = modifier1.Id;
        insert mentry;
        MasterProductPrice__c master = new MasterProductPrice__c();
        master.Name = 'Test Master';
        master.Account__c = acc.Id;
        master.start_Date__c = Date.today().addDays(-7);
        master.End_Date__c = Date.today().addDays(7);
        master.CurrencyIsoCode = 'EUR';
        master.Product__c = kit.Id;
        master.Modifier_Entry__c = mentry.Id;
        master.Has_AllItem__c = false;
        master.Final_Price__c = 12;
        master.List_Price__c = 22;
        insert master;
        
        MasterProductPrice__c master2 = new MasterProductPrice__c();
        master2.Name = 'Test Master';
        master2.Account__c = acc.Id;
        master2.start_Date__c = Date.today().addDays(-7);
        master2.End_Date__c = Date.today().addDays(7);
        master2.CurrencyIsoCode = 'EUR';
        master2.Product__c = kit.Id;
        master2.Modifier_Entry__c = mentry.Id;
        master2.Has_AllItem__c = true;
        master2.Final_Price__c = 12;
        master2.List_Price__c = 22;
 
        insert master2;
        
         LinkProductAndPrice__c link2 = new LinkProductAndPrice__c();
        link2.MasterProductPrice__c = master2.Id;
		link2.Product__c = prod1.Id;
        link2.Modifier_Entry__c = mentry.Id;
        link2.start_Date__c = Date.today().addDays(-7);
        link2.End_Date__c = Date.today().addDays(7);
         link2.Final_Priority__c = 22;
        insert link2;        
        LinkProductAndPrice__c link = new LinkProductAndPrice__c();
        link.MasterProductPrice__c = master.Id;
        
       
        link.Product__c = prod1.Id;
        link.start_Date__c = Date.today().addDays(-7);
        link.End_Date__c = Date.today().addDays(7);
        insert link;
    }
    @IsTest
    static void testQueryAvalibleProduct(){
        CCM_PurchaseBatchUploadController.GetUserType();

        try{
            List<Account> acList = [select id
                                    from Account];
            // 调用测试方
            List<String> result = CCM_PurchaseBatchUploadController.QueryAvalibleProduct(acList.get(0).Id);
            CCM_PurchaseBatchUploadController.GetCurrentUserCustomer();
        } catch (exception e){
        }
    }
    @IsTest
    static void testQueryAddressInfo(){
        // 调用测试方法

        List<Account> acList = [select id
                                from Account];
        try{
            String result = CCM_PurchaseBatchUploadController.QueryAddressInfo(acList[0].Id, false);
        } catch (exception e){
        }
    }
    @IsTest
    static void testCreatePurchaseOrder(){
        try{
            // 创建测试数据
            List<CCM_PurchaseBatchUploadController.PurchaseOrderItemWrapper> purchaseOrderItems = new List<CCM_PurchaseBatchUploadController.PurchaseOrderItemWrapper>();
            // 添加测试数据到purchaseOrderItems列表中
            // 设置具体的属性值
            List<Account> acList = [select id
                                    from Account];
            CCM_PurchaseBatchUploadController.PurchaseOrderItemWrapper objWrapper = new CCM_PurchaseBatchUploadController.PurchaseOrderItemWrapper();
            objWrapper.orderGroupNumber = '123456';
            objWrapper.customerNumber = acList[0].Id;
            objWrapper.customerPO = 'PO001';
            objWrapper.billToAddressCode = 'BILL001';
            objWrapper.shipToAddressCode = 'SHIP001';
            objWrapper.dropshipAddressCode = 'DROP001';
            // objWrapper.insuranceFee = '10.00';
            // objWrapper.otherFee = '5.00';
            objWrapper.instructionToDSV = 'Please handle with care';
            objWrapper.carrierInformation = 'UPS';
            objWrapper.orderType = 'Regular';
            objWrapper.warehouse = 'Germany';
            objWrapper.isDropship = 'false';
            objWrapper.modelNumber = 'BH1001';
            objWrapper.requestDate = '2022-01-01';
            objWrapper.PricingDate = '2022-01-01';
            objWrapper.warehouse = 'Germany(DSV)';
            objWrapper.qty = '10';
            objWrapper.remark = 'This is a test remark';
            purchaseOrderItems.add(objWrapper);
            // 调用测试方法
            Test.startTest();
            Date t = Date.today();
            String priceingDate = String.valueOf(t);
            String strpurchaseOrderItems = JSON.serialize(purchaseOrderItems);
            CCM_PurchaseBatchUploadController.createPurchaseOrder(strpurchaseOrderItems, priceingDate);
            CCM_PurchaseBatchUploadController.CheckPurchaseOrderTableData('InsideSales',purchaseOrderItems);
            CCM_PurchaseBatchUploadController.CheckBatchPurchaseOrderData(strpurchaseOrderItems);
            CCM_PurchaseBatchUploadController.IsNullOrWhiteSpace('');
            Test.stopTest();
        } catch (exception e){
        }
    }
    @IsTest
    static void testAutoFillInfo(){
        // 创建测试数据
        List<Purchase_Order__c> lstPurchaseOrder = [select id, Customer__c, Warehouse__c, Dropship_Address__c, RecordTypeId, Is_DropShip__c, Shipping_Place__c, Shipping_Address__c, Freight_Term__c, Payment_Term__c, Submit_Date__c
                                                    from Purchase_Order__c];
        List<Purchase_Order_Item__c> lstPurchaseOrderItem = [select id, Purchase_Order__c, Product__c, Quantity__c, wearhouse__c, Header_Record_Name__c, Schedule_Ship_Date__c
                                                             from Purchase_Order_Item__c];
        Map<String, String> mapAddressId2City = new Map<String, String>();
        List<Account_Address__c> adList = [select id, Name,Customer_Line_Oracle_ID__c
                                           from Account_Address__c];
        for (Account_Address__c ad : adList){
            mapAddressId2City.put(ad.Id, ad.Name);
        }
        List<Pricebook_Entry__c> lstPriceBookEntryPrice = [SELECT Id, PriceBook__c, UnitPrice__c, End_Date__c, Start_Date__c, Product__c, CurrencyIsoCode
                                                           from Pricebook_Entry__c];
        System.debug('lstPriceBookEntryPrice:' + JSON.serialize(lstPriceBookEntryPrice));
        // 添加测试数据到lstPurchaseOrder、lstPurchaseOrderItem和mapAddressId2City中
        // 调用测试方法
        Test.startTest();
        Date t = Date.today();
        String priceingDate = String.valueOf(t);
        CCM_PurchaseBatchUploadController.AutoFillInfo(lstPurchaseOrder, lstPurchaseOrderItem, mapAddressId2City, priceingDate);
        Test.stopTest();
    }
    @IsTest
    static void testSetDropShipInfo(){
        // 创建测试数据
        try{
            List<Purchase_Order__c> lstPurchaseOrder = [SELECT Id, Dropship_Address__c, Additional_Contact_Name__c, Additional_Shipping_Street__c, Additional_Shipping_Street2__c, Additional_Contact_Phone__c, Additional_Shipping_Country__c, Additional_Shipping_Postal_Code__c, Additional_Shipping_Province__c, Additional_Shipping_City__c
                                                        FROM Purchase_Order__c];
            Set<String> dropShipIds = new Set<String>();
            List<Account_Address__c> adList = [select id
                                               from Account_Address__c];
            for (Account_Address__c ad : adList){
                dropShipIds.add(ad.Id);
            }

            // 调用测试方法
            Test.startTest();
            CCM_PurchaseBatchUploadController.setDropShipInfo(lstPurchaseOrder, dropShipIds);
            Test.stopTest();
        } catch (exception e){
        }
    }
    @IsTest
    static void testCaulateProduct2PriceInfoByDownload(){
        // 创建测试数据
        List<String> lstProductId = new List<String>();
        Map<String, Double> mapProductId2Qty = new Map<String, Double>();
        Product2 prod1 = new Product2();
        prod1.Name = 'BH10011';
        prod1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        prod1.ExternalId__c = 'BH1001';
        prod1.Order_Model__c = 'BH1001';
        prod1.Master_Product__c = 'BH1001';
        insert prod1;
        List<Product2> productList = [select id
                                      from Product2];
        for (Product2 ad : productList){
            lstProductId.add(ad.Id);
            mapProductId2Qty.put(ad.Id, 5);
        }
        lstProductId.add(prod1.Id);
        Account acc = [SELECT Id
                       from Account
                       limit 1];
        List<MasterProductPrice__c> masterProductPriceList = [select id, Account__c, Start_Date__c, End_Date__c, Product__c, Modifier_Entry__C, CurrencyIsoCode
                                                              from MasterProductPrice__c];
        System.debug('MasterProductPriceList:' + JSON.serialize(masterProductPriceList));
        System.debug('Modifier_Entry__C:' + JSON.serialize(masterProductPriceList.get(0).Modifier_Entry__C));
        // 调用测试方法
        Test.startTest();
        CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(String.valueOf(acc.Id), lstProductId, Date.today(), mapProductId2Qty);
        CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfo(String.valueOf(acc.Id), lstProductId, Date.today(), mapProductId2Qty);
        Test.stopTest();
    }
    @IsTest
    static void test_Territory() {
        Territory__c try1 = new Territory__c(Name='try123');
        insert try1;
        try1.Name = 'try234';
        update try1;
        CCM_PurchaseBatchUploadController.forCodeCoverage();
    }
}