/**
 * <AUTHOR>
 * @date 2025-08-04
 * @description Test class for CCM_SalesTarget_SetTargetFlagCtrl
 */
@isTest
public class CCM_SalesTarget_SetTargetFlagCtrlTest {
    
    @TestSetup
    static void makeData() {
        // Create test users for sales persons
        Profile salesProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];
        
        User salesPerson1 = new User(
            FirstName = 'Test',
            LastName = 'SalesPerson1',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'tsp1',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = salesProfile.Id
        );
        
        User salesPerson2 = new User(
            FirstName = 'Test',
            LastName = 'SalesPerson2',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'tsp2',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = salesProfile.Id
        );
        
        insert new List<User>{salesPerson1, salesPerson2};
        
        // Create test Quota Allocation
        Quota_Allocation__c quotaAllocation = new Quota_Allocation__c(
            Name = 'Test Quota Allocation 2025',
            Start_Date__c = Date.newInstance(2025, 1, 1),
            End_Date__c = Date.newInstance(2025, 12, 31),
            Status__c = 'Active'
        );
        insert quotaAllocation;
        
        // Create parent quota allocation details
        List<Quota_Allocation_Detail__c> parentDetails = new List<Quota_Allocation_Detail__c>();
        
        Quota_Allocation_Detail__c parentDetail1 = new Quota_Allocation_Detail__c(
            Quota_Allocation__c = quotaAllocation.Id,
            Sales_Person_Name__c = 'Test SalesPerson1',
            Assigned_Actual__c = 100000,
            Sales_Person_Target_Flag__c = false
        );
        parentDetails.add(parentDetail1);
        
        Quota_Allocation_Detail__c parentDetail2 = new Quota_Allocation_Detail__c(
            Quota_Allocation__c = quotaAllocation.Id,
            Sales_Person_Name__c = 'Test SalesPerson2',
            Assigned_Actual__c = 150000,
            Sales_Person_Target_Flag__c = false
        );
        parentDetails.add(parentDetail2);
        
        insert parentDetails;
        
        // Create child quota allocation details
        List<Quota_Allocation_Detail__c> childDetails = new List<Quota_Allocation_Detail__c>();
        
        Quota_Allocation_Detail__c childDetail1 = new Quota_Allocation_Detail__c(
            Quota_Allocation__c = quotaAllocation.Id,
            Parent_Quota_Detail__c = parentDetail1.Id,
            Sales_Person_Name__c = 'Test SalesPerson1',
            Assigned_Actual__c = 50000,
            Sales_Person_Target_Flag__c = false
        );
        childDetails.add(childDetail1);
        
        Quota_Allocation_Detail__c childDetail2 = new Quota_Allocation_Detail__c(
            Quota_Allocation__c = quotaAllocation.Id,
            Parent_Quota_Detail__c = parentDetail1.Id,
            Sales_Person_Name__c = 'Test SalesPerson2',
            Assigned_Actual__c = 50000,
            Sales_Person_Target_Flag__c = false
        );
        childDetails.add(childDetail2);
        
        Quota_Allocation_Detail__c childDetail3 = new Quota_Allocation_Detail__c(
            Quota_Allocation__c = quotaAllocation.Id,
            Parent_Quota_Detail__c = parentDetail2.Id,
            Sales_Person_Name__c = 'Test SalesPerson2',
            Assigned_Actual__c = 75000,
            Sales_Person_Target_Flag__c = false
        );
        childDetails.add(childDetail3);
        
        insert childDetails;
    }
    
    /**
     * @description Test the main setTargetFlag method with hierarchical data
     */
    @isTest
    static void testSetTargetFlag() {
        // Get test data
        Quota_Allocation__c quotaAllocation = [SELECT Id FROM Quota_Allocation__c LIMIT 1];
        
        Test.startTest();
        
        // Call the method under test
        CCM_SalesTarget_SetTargetFlagCtrl.setTargetFlag(quotaAllocation.Id);
        
        Test.stopTest();
        
        // Verify that target flags were set correctly
        List<Quota_Allocation_Detail__c> updatedDetails = [
            SELECT Id, Sales_Person_Name__c, Sales_Person_Target_Flag__c, Parent_Quota_Detail__c
            FROM Quota_Allocation_Detail__c 
            WHERE Quota_Allocation__c = :quotaAllocation.Id
            ORDER BY Sales_Person_Name__c, Parent_Quota_Detail__c NULLS FIRST
        ];
        
        // Verify that each sales person has only one record with target flag set to true
        Map<String, Integer> salesPersonFlagCount = new Map<String, Integer>();
        for (Quota_Allocation_Detail__c detail : updatedDetails) {
            if (detail.Sales_Person_Target_Flag__c) {
                Integer count = salesPersonFlagCount.get(detail.Sales_Person_Name__c);
                salesPersonFlagCount.put(detail.Sales_Person_Name__c, count == null ? 1 : count + 1);
            }
        }
        
        // Each sales person should have exactly one target flag set to true
        for (String salesPersonName : salesPersonFlagCount.keySet()) {
            System.assertEquals(1, salesPersonFlagCount.get(salesPersonName), 
                'Each sales person should have exactly one target flag set to true: ' + salesPersonName);
        }
        
        // Verify that Sales_Target_Detail_Report__c records were created
        List<Sales_Target_Detail_Report__c> targetReports = [
            SELECT Id, Sales_Person_Name__c, Assigned_Quota__c, Quota_Allocation__c
            FROM Sales_Target_Detail_Report__c 
            WHERE Quota_Allocation__c = :quotaAllocation.Id
        ];
        
        System.assert(!targetReports.isEmpty(), 'Sales Target Detail Reports should be created');
        System.assertEquals(salesPersonFlagCount.size(), targetReports.size(), 
            'Number of target reports should match number of unique sales persons with flags');
    }
    
    /**
     * @description Test with single level hierarchy (no children)
     */
    @isTest
    static void testSetTargetFlagSingleLevel() {
        // Create a new quota allocation with only parent level details
        Quota_Allocation__c singleLevelQuota = new Quota_Allocation__c(
            Name = 'Single Level Quota 2025',
            Start_Date__c = Date.newInstance(2025, 1, 1),
            End_Date__c = Date.newInstance(2025, 12, 31),
            Status__c = 'Active'
        );
        insert singleLevelQuota;
        
        Quota_Allocation_Detail__c singleDetail = new Quota_Allocation_Detail__c(
            Quota_Allocation__c = singleLevelQuota.Id,
            Sales_Person_Name__c = 'Single Level Sales Person',
            Assigned_Actual__c = 200000,
            Sales_Person_Target_Flag__c = false
        );
        insert singleDetail;
        
        Test.startTest();
        
        CCM_SalesTarget_SetTargetFlagCtrl.setTargetFlag(singleLevelQuota.Id);
        
        Test.stopTest();
        
        // Verify the single detail was updated
        Quota_Allocation_Detail__c updatedDetail = [
            SELECT Id, Sales_Person_Target_Flag__c 
            FROM Quota_Allocation_Detail__c 
            WHERE Id = :singleDetail.Id
        ];
        
        System.assertEquals(true, updatedDetail.Sales_Person_Target_Flag__c, 
            'Single level detail should have target flag set to true');
        
        // Verify target report was created
        List<Sales_Target_Detail_Report__c> reports = [
            SELECT Id FROM Sales_Target_Detail_Report__c 
            WHERE Quota_Allocation__c = :singleLevelQuota.Id
        ];
        
        System.assertEquals(1, reports.size(), 'One target report should be created');
    }
    
    /**
     * @description Test with empty quota allocation
     */
    @isTest
    static void testSetTargetFlagEmptyQuota() {
        // Create empty quota allocation
        Quota_Allocation__c emptyQuota = new Quota_Allocation__c(
            Name = 'Empty Quota 2025',
            Start_Date__c = Date.newInstance(2025, 1, 1),
            End_Date__c = Date.newInstance(2025, 12, 31),
            Status__c = 'Active'
        );
        insert emptyQuota;
        
        Test.startTest();
        
        try {
            CCM_SalesTarget_SetTargetFlagCtrl.setTargetFlag(emptyQuota.Id);
            System.assert(true, 'Method should handle empty quota allocation gracefully');
        } catch (Exception e) {
            System.debug('Exception in testSetTargetFlagEmptyQuota: ' + e.getMessage());
        }
        
        Test.stopTest();
        
        // Verify no target reports were created
        List<Sales_Target_Detail_Report__c> reports = [
            SELECT Id FROM Sales_Target_Detail_Report__c 
            WHERE Quota_Allocation__c = :emptyQuota.Id
        ];
        
        System.assertEquals(0, reports.size(), 'No target reports should be created for empty quota');
    }
    
    /**
     * @description Test with duplicate sales person names
     */
    @isTest
    static void testSetTargetFlagDuplicateNames() {
        // Create quota allocation with duplicate sales person names
        Quota_Allocation__c duplicateQuota = new Quota_Allocation__c(
            Name = 'Duplicate Names Quota 2025',
            Start_Date__c = Date.newInstance(2025, 1, 1),
            End_Date__c = Date.newInstance(2025, 12, 31),
            Status__c = 'Active'
        );
        insert duplicateQuota;
        
        List<Quota_Allocation_Detail__c> duplicateDetails = new List<Quota_Allocation_Detail__c>();
        
        // Create multiple details with same sales person name
        for (Integer i = 0; i < 3; i++) {
            Quota_Allocation_Detail__c detail = new Quota_Allocation_Detail__c(
                Quota_Allocation__c = duplicateQuota.Id,
                Sales_Person_Name__c = 'Duplicate Sales Person',
                Assigned_Actual__c = 50000 + (i * 10000),
                Sales_Person_Target_Flag__c = false
            );
            duplicateDetails.add(detail);
        }
        insert duplicateDetails;
        
        Test.startTest();
        
        CCM_SalesTarget_SetTargetFlagCtrl.setTargetFlag(duplicateQuota.Id);
        
        Test.stopTest();
        
        // Verify only one record has target flag set to true
        List<Quota_Allocation_Detail__c> flaggedDetails = [
            SELECT Id, Sales_Person_Target_Flag__c 
            FROM Quota_Allocation_Detail__c 
            WHERE Quota_Allocation__c = :duplicateQuota.Id 
            AND Sales_Person_Target_Flag__c = true
        ];
        
        System.assertEquals(1, flaggedDetails.size(), 
            'Only one record should have target flag set to true for duplicate sales person names');
    }
}
