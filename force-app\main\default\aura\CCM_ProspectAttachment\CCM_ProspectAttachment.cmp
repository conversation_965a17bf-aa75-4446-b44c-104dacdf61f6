<aura:component controller="CCM_LeadController" description="CCM_ProspectAttachment" extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout" access="global">
    
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="filetype" type="List" default=".png, .jpg, .jpeg, .pdf, .xlsx, .csv, .word" />
    <aura:attribute name="recordId" type="String" />
    <aura:attribute name="attachmentList" type="List" default="[]"/>
    <aura:attribute name="contentDocumentId" type="String" default=""/>


    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="slds-theme_default" style="border-radius: 0.25rem;">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
        <div class="attachment-wrap">
            <div class="title-wrap">
                <lightning:icon size="small" iconName="doctype:attachment" alternativeText="attachment file" title="attachment file" />
                <span class="title">Attachment ({!v.attachmentList.length})</span>
            </div>
            <div class="attachment-content">
                <!-- <lightning:input aura:id="upload" name="" type="file" label="Attach receipt" multiple="true" accept="" onchange="{!c.handleUploadFinished}"/> -->
                <lightning:fileUpload label="" name="" onuploadfinished="{!c.handleUploadFinished2}" />

            </div>
        </div>
        <div class="file-list-wrap">
            <aura:iteration items="{!v.attachmentList}" var="attachment">
                <div class="file-item">
                    <div class="file-content" id="{!attachment.ContentDocumentId}" onclick="{!c.handleViewFile}">
                        <aura:if isTrue="{!attachment.FileExtension == 'xlsx'}">
                        <lightning:icon id="{!attachment.ContentDocumentId}" size="small" iconName="doctype:excel" alternativeText="attachment file" title="attachment file" />
                        </aura:if>
                        <aura:if isTrue="{!attachment.FileExtension == 'csv'}">
                            <lightning:icon id="{!attachment.ContentDocumentId}" size="small" iconName="doctype:csv" alternativeText="attachment file" title="attachment file" />
                        </aura:if>
                        <aura:if isTrue="{!attachment.FileExtension == 'pdf'}">
                            <lightning:icon id="{!attachment.ContentDocumentId}" size="small" iconName="doctype:pdf" alternativeText="attachment file" title="attachment file" />
                        </aura:if>
                        <aura:if isTrue="{!attachment.FileExtension == 'word'}">
                            <lightning:icon id="{!attachment.ContentDocumentId}" size="small" iconName="doctype:word" alternativeText="attachment file" title="attachment file" />
                        </aura:if>
                        <aura:if isTrue="{!(attachment.FileExtension == 'png') || (attachment.FileExtension == 'jpg') || (attachment.FileExtension == 'jpeg')}">
                            <lightning:icon id="{!attachment.ContentDocumentId}" size="small" iconName="doctype:image" alternativeText="attachment file" title="attachment file" />
                        </aura:if>
                        <div class="attachment-text" id="{!attachment.ContentDocumentId}">
                            <div class="link" id="{!attachment.ContentDocumentId}">{!attachment.FileName}</div>
                            <div class="info" id="{!attachment.ContentDocumentId}">{!attachment.CredatedDate}</div>
                        </div>
                    </div>
                    <div class="delete-wrap">
                        <lightning:icon id="{!attachment.ContentDocumentId}" iconName="utility:delete" alternativeText="Delete" size="x-small" onclick="{!c.handleDelete}"/>
                    </div>
                </div>
            </aura:iteration>
        </div>
        <!-- 隐藏fileCard，弹出专用 -->
        <div class="slds-hide">
            <lightning:fileCard fileId="{!v.contentDocumentId}" hideDescription="true"/>
        </div>
    </div>
</aura:component>