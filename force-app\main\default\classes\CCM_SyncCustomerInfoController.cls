/**
 * <AUTHOR>
 * @date 2024-07-02
 * @description Add check logic before sync customer
 */
public with sharing class CCM_SyncCustomerInfoController {
    
    @AuraEnabled
    public static string checkRequiredFieldBeforeSync(String recordId){
        Map<String, Object> resultMap = new Map<String, Object>();
        Boolean isSuccess = true;
        String errMsg = null;
        List<Account> accList = [SELECT Planning_Territory_No__c FROM Account WHERE Id = :recordId];
        for(Account acc : accList) {
            if(acc.Planning_Territory_No__c == null) {
                isSuccess = false;
                errMsg = 'Planungsgebiet darf nicht leer sein!';
            }
        }
        resultMap.put('isSuccess', isSuccess);
        resultMap.put('errMsg', errMsg);

        if(isSuccess) {
            return CCM_Service.pushCustInfo(recordId);
        }
        else {
            return JSON.serialize(resultMap);
        }
    }
}