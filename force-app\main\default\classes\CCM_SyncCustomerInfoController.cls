/**
 * <AUTHOR>
 * @date 2024-07-02
 * @description Add check logic before sync customer
 */
public with sharing class CCM_SyncCustomerInfoController {
    
    @AuraEnabled
    public static string checkRequiredFieldBeforeSync(String recordId){
        Map<String, Object> resultMap = new Map<String, Object>();
        Boolean isSuccess = true;
        String errMsg = null;
        List<Account> accList = [SELECT Planning_Territory_No__c FROM Account WHERE Id = :recordId];
        for(Account acc : accList) {
            if(acc.Planning_Territory_No__c == null) {
                isSuccess = false;
                errMsg = 'Planungsgebiet darf nicht leer sein!';
            }
        }
        resultMap.put('isSuccess', isSuccess);
        resultMap.put('errMsg', errMsg);

        if(isSuccess) {
            // return CCM_Service.pushCustInfo(recordId);
            String accountNumber = null;
            for(Account acc : [SELECT AccountNumber FROM Account WHERE Id = :recordId]) {
                accountNumber = acc.AccountNumber;
            }
            List<Alert_Message__c> alertMessages = [SELECT Alert_Message__c, Start_Date__c, End_Date__c, Alert_Mode__c FROM Alert_Message__c 
                                                    WHERE (Customer_Account__c = :accountNumber OR Customer__c = :recordId) 
                                                    AND Alert_Mode__c != null
                                                    Order By Alert_Mode__c, CreatedDate DESC];
            Map<String, Alert_Message__c> alertMap = new Map<String, Alert_Message__c>();
            for(Alert_Message__c alertMessage : alertMessages) {
                if(!alertMap.containsKey(alertMessage.Alert_Mode__c)) {
                    alertMap.put(alertMessage.Alert_Mode__c, alertMessage);
                }
            }

            CCM_SyncCustomerQueue queue = new CCM_SyncCustomerQueue(recordId, alertMap.values(), false, false, null, 0);
            System.enqueueJob(queue);
            Map<String, String> result = new Map<String, String>{
                'PROCESS_STATUS' => 'Success'
            };
            return JSON.serialize(result);
        }
        else {
            return JSON.serialize(resultMap);
        }
    }
}