({
    doInit : function(component, event, helper) {
        component.set('v.columns', [
            // { label: 'Master Reference Number', fieldName: 'masterReferenceNumber', type: 'text' },
            { label: $A.get("$Label.c.CCM_DistributorClaimReferenceNumber"), fieldName: 'distributorClaimReferenceNumber', type: 'text' },
            { label: $A.get("$Label.c.CCM_EmailAddress"), fieldName: 'emailAddress', type: 'text' },
            { label: $A.get("$Label.c.CCM_UserType"), fieldName: 'userType', type: 'text' },
            { label: $A.get("$Label.c.CCM_ModelNumber"), fieldName: 'modelNumber', type: 'text' },
            { label: $A.get("$Label.c.CCM_SerialNumber"), fieldName: 'serialNumber', type: 'text'},
            { label: $A.get("$Label.c.CCM_PartNumber"), fieldName: 'partNumber', type: 'text' },
            { label: $A.get("$Label.c.CCM_Quantity"), fieldName: 'quantity', type: 'text' },
            { label: $A.get("$Label.c.CCM_Currency"), fieldName: 'currency', type: 'text' },
            { label: $A.get("$Label.c.CCM_CustomerUnitPrice"), fieldName: 'customerUnitPrice', type: 'text' },
            { label: $A.get("$Label.c.CCM_StandTotalAmount"), fieldName: 'standTotalAmount', type: 'text' },
            { label: $A.get("$Label.c.CCM_StandRepairTime"), fieldName: 'standRepairTime', type: 'text' },
            { label: $A.get("$Label.c.CCM_ActualTotalNumber"), fieldName: 'actualTotalAmount', type: 'text' },
            { label: $A.get("$Label.c.CCM_ActualRepairTime"), fieldName: 'actualRepairTime', type: 'text' },
            // { label: 'Error', fieldName: 'error', type: 'text' },
            
        ]);
        // 获取customerId
        const params = new URLSearchParams(window.location.search);
        const customerId =  params.get('0.recordId');
        component.set('v.customerId', customerId);

        if(window.location.pathname === '/s/WarrantyClaimUpload') {
            helper.getCustomer(component);
            component.set('v.inPortal', true);
            component.set('v.portalClass', 'width: 90%; margin: 0 auto;');
        }
        else {
            // 获取bill address下拉
            helper.getBillAddress(component);
        }
    },

    // 模板下载
    handleDownloadTemplate : function(component, event, helper) {
        let isDE = $A.get("$Locale.language") === 'de';
        let template = document.createElement('a'); 
        // 创建一个隐藏的a标签
        template.style = 'display: none';
        if(isDE) {
            template.download = 'Massenupload Warranty Claim TemplateÜbersetzung';
            template.href = $A.get('$Resource.Upload_Claim_Template') + '/Upload_Claim_Template_DE.xlsx';
        }
        else {
            template.download = 'Upload Claim Template';
            template.href = $A.get('$Resource.Upload_Claim_Template') + '/Upload_Claim_Template.xlsx';
        }
        document.body.appendChild(template);
        // 触发a标签的click事件
        template.click();
        document.body.removeChild(template);
    },

    // 获取excel解析数据
    getParseData : function(component, event, helper) {
        let isDE = $A.get("$Locale.language") === 'de';
        component.set('v.resetFlag', false);
        // 先置空表格
        component.set('v.tableData', []);
        let parseData = JSON.parse(JSON.stringify(event.getParam('parseData')));
        let newArr = [];
        newArr = JSON.parse(JSON.stringify(parseData));
        // 表格字段处理
        let arr = [];
        if(isDE) {
            newArr.forEach((item)=>{
                arr.push({
                    'distributorClaimReferenceNumber': item[$A.get("$Label.c.CCM_DistributorClaimReferenceNumber")] || '',
                    'claimDate': item[$A.get("$Label.c.CCM_ClaimDate") + '\r\n' + $A.get("$Label.c.CCM_Required") +' ,YYYY-MM-DD'] ? helper.changeDateType(item[$A.get("$Label.c.CCM_ClaimDate") + '\r\n' + $A.get("$Label.c.CCM_Required") +' ,YYYY-MM-DD']) : '',
                    'purchaseDate': item[$A.get("$Label.c.CCM_PurchaseDate") + '\r\n' + $A.get("$Label.c.CCM_Required") +' ,YYYY-MM-DD'] ? helper.changeDateType(item[$A.get("$Label.c.CCM_PurchaseDate") + '\r\n' + $A.get("$Label.c.CCM_Required") +' ,YYYY-MM-DD']) : '',
                    'failureDate': item[$A.get("$Label.c.CCM_FailureDate")] ? helper.changeDateType(item[$A.get("$Label.c.CCM_FailureDate")]) : '',
                    'repairDate': item[$A.get("$Label.c.CCM_RepairDate") + '\r\n' + $A.get("$Label.c.CCM_Required") + ' ,YYYY-MM-DD'] ? helper.changeDateType(item[$A.get("$Label.c.CCM_RepairDate") + '\r\n' + $A.get("$Label.c.CCM_Required") + ' ,YYYY-MM-DD']) : '',
                    'emailAddress': item[$A.get("$Label.c.CCM_EmailAddress")] || '',
                    'userType': item[$A.get("$Label.c.CCM_UserType") + '\r\n\"' + $A.get("$Label.c.CCM_Commercial") + '\" or \"' + $A.get("$Label.c.CCM_Residential") + '\"'] || '',
                    'owner': item[$A.get("$Label.c.CCM_Owner") + '\r\nText'] || '',
                    'modelNumber': item[$A.get("$Label.c.CCM_ModelNumber") + '\r\n' + $A.get("$Label.c.CCM_Required") + ' ,Text'] || '',
                    'serialNumber': item[$A.get("$Label.c.CCM_SerialNumber") + '\r\n' + $A.get("$Label.c.CCM_Required") + ' ,Text'] || '',
                    'failureCode': item[$A.get("$Label.c.CCM_FailureCode") + '\r\nText(' + $A.get("$Label.c.CCM_Reference") + ')'] || '',
                    'failureDescription': item[$A.get("$Label.c.CCM_FailureDescription") + '\r\n' + $A.get("$Label.c.CCM_Required") + ' ,Text'] || '',
                    'partNumber': item[$A.get("$Label.c.CCM_PartNumber") + '\r\n' + $A.get("$Label.c.CCM_Include") + ' \"' + $A.get("$Label.c.CCM_LaborHours") + '\"'] || '',
                    'quantity': item[$A.get("$Label.c.CCM_Quantity")] || '',
                    'currency': item[$A.get("$Label.c.CCM_Currency") + '\r\n\"EUR\" or \"GBP\"'] || '',
                    'customerUnitPrice': item[$A.get("$Label.c.CCM_CustomerUnitPrice") + '\r\n' + $A.get("$Label.c.CCM_ForLaborHourLineorproduct")] || '',
                })
            });
        }
        else {
            newArr.forEach((item)=>{
                arr.push({
                    // 'masterReferenceNumber': item['Master Reference Number'] || '',
                    'distributorClaimReferenceNumber': item['Distributor Claim Reference Number'] || '',
                    'claimDate': item['Claim Date\r\nRequired ,YYYY-MM-DD'] ? helper.changeDateType(item['Claim Date\r\nRequired ,YYYY-MM-DD']) : '',
                    'purchaseDate': item['Purchase Date\r\nRequired ,YYYY-MM-DD'] ? helper.changeDateType(item['Purchase Date\r\nRequired ,YYYY-MM-DD']) : '',
                    'failureDate': item['Failure Date'] ? helper.changeDateType(item['Failure Date']) : '',
                    'repairDate': item['Repair Date\r\nRequired ,YYYY-MM-DD'] ? helper.changeDateType(item['Repair Date\r\nRequired ,YYYY-MM-DD']) : '',
                    'emailAddress': item['Email Address'] || '',
                    'userType': item['User Type\r\n\"Commercial\" or \"Residential\"'] || '',
                    // 'owner': item['Owner\r\nRequired ,Text'] || '',
                    'owner': item['Owner\r\nText'] || '',
                    // 'ownerAddress': item['Owner Address\r\nText'] || '',
                    // 'ownerCity': item['Owner City\r\nText'] || '',
                    // 'ownerPostcode': item['Owner Postcode\r\nText'] || '',
                    'modelNumber': item['Model Number\r\nRequired ,Text'] || '',
                    'serialNumber': item['Serial Number\r\nRequired ,Text'] || '',
                    'failureCode': item['Failure Code\r\nText(Reference)'] || '',
                    'failureDescription': item['Failure Description\r\nRequired ,Text'] || '',
                    // 'repairWork': item['Repair Work\r\nRequired ,Text'] || '',
                    'partNumber': item['Part Number\r\nInclude \"Labor Hour\"'] || '',
                    'quantity': item['Quantity'] || '',
                    'currency': item['Currency\r\n\"EUR\" or \"GBP\"'] || '',
                    'customerUnitPrice': item['Customer Unit Price\r\nFor product only'] || '',
                })
            });
        }
        component.set('v.resetFlag', true);
        // 数据校验
        helper.checkTableData(component, arr);
    },

    // 提交事件
    onClickSave : function(component, event, helper) {
        // 判空
        let tableData = component.get('v.tableData');
        if (!tableData.length) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'Please upload the form data first!',
                "type": "warning"
            }).fire();
            return;
        }
        helper.submitEvent(component);
    },
    
    // 取消事件
    onClickCancel : function(component, event, helper) {
        let customerId = component.get('v.customerId');
        let url = window.location.origin + '/lightning/r/Account/' +  customerId + '/view';
        window.open(url, '_self');
    }
})