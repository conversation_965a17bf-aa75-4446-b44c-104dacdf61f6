<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 03-04-2024
  @last modified by  : <EMAIL>
-->
<aura:component description="CCM_Community_PODetail"
                implements="forceCommunity:availableForAllPageTypes,force:hasRecordId,flexipage:availableForAllPageTypes,force:appHostable"
                controller="CCM_PurchaseOrderPreview"
                access="global">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="poRecordId" type="String" default=""/>
    <aura:attribute name="currencySymbol" type="String" default="€"/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="order" type="Object" default=""/>
    <aura:attribute name="invoiceInfo" type="List" default="[]"/>
    <aura:attribute name="shipmentColumns" type="List" default="[]"/>
    <aura:attribute name="invoiceColumns" type="List" default="[]"/>
    <aura:attribute name="processData" type="List" default="[]"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="paymentTermVal" type="String" default=""/>
    <aura:attribute name="freightTermVal" type="String" default=""/>
    <aura:attribute name="isInnerUser" type="Boolean" default="false"/>
    <aura:attribute name="isShow" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareProducts" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareShipments" type="Boolean" default="false"/>
    <aura:attribute name="isCanReverse" type="Boolean" default="false"/>
    <aura:attribute name="reverseOrderLink" type="String" default=""/>
    <aura:attribute name="attachmentColumns" type="List" default="[]"/>
    <aura:attribute name="attachmentTypeOptions" type="List" default="[]"/>
    <aura:attribute name="contentDocumentId" type="String" default=""/>

    <aura:attribute name="basicInformation" type="Object" default=""/>
    <aura:attribute name="attachment" type="List" default="[]"/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="attachmentItem" type="Map" default="{}"/>
    <aura:attribute name="uploadList" type="List" default="[]"/>
    <aura:attribute name="uploadModalFlag" type="Boolean" default="false"/>
    <aura:attribute name="uploadFinished" type="Boolean" default="false" />
    <aura:attribute name="attachmentName" type="String" default=""/>
    <aura:attribute name="attachmentType" type="String" default=""/>
    <aura:attribute name="TotalDueAmount" type="String" default=""/>
    <aura:attribute name="CountLine" type="String" default=""/>
    <!-- <aura:attribute name="showSync" type="Boolean" default="true"/> -->
    <aura:attribute name="userType" type="String" default=""/>
    <aura:attribute name="poStatus" type="String" default=""/>
    <aura:attribute name="isInsideSales" type="Boolean" default="false"/>
    <aura:attribute name="isCCA" type="Boolean" default="false"/>
    <aura:attribute name="notSync" type="Boolean" default="false" />
    <aura:attribute name="syncStatus" type="String" default=""/>
    <aura:attribute name="syncMessage" type="String" default=""/>
    <aura:attribute name="isSubmitted" type="Boolean" default="false" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="slds-box slds-theme_default">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium">
            <lightning:spinner size="large" style="z-index: 10000;" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <c:CCM_PathProcess processData="{!v.processData}" currentStep="{!v.currentStep}"/>
            <!-- Customer Information -->
            <c:CCM_Section title="{!$Label.c.CCM_CustomerInformation}" expandable="true">
                <lightning:layout multipleRows="true">
                     <!-- Customer PO​ -->
                     <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_CustomerPO}">
                            {!v.basicInformation.CustomerPo}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Customer -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Customer}">
                            {!v.basicInformation.CustomerName}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Customer Number​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                       <c:CCM_Field label="{!$Label.c.CCM_CustomerNumber}">
                           {!v.basicInformation.CustomerNumber}
                       </c:CCM_Field>
                   </lightning:layoutItem>
                   <!-- Sales Rep​ -->
                   <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                      <c:CCM_Field label="{!$Label.c.CCM_SalesRep}">
                          {!v.basicInformation.salesPerson}
                      </c:CCM_Field>
                  </lightning:layoutItem>
                  <!-- Inco Term​ -->
                  <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                     <c:CCM_Field label="{!$Label.c.CCM_Incoterm}">
                         {!v.basicInformation.IncoTerm}
                     </c:CCM_Field>
                 </lightning:layoutItem>
                 <!-- Payment Term​ -->
                 <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.Order_PaymentTerm}">
                        {!v.basicInformation.PaymentTerm}
                    </c:CCM_Field>
                </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            <!-- Order Basic Information -->
            <c:CCM_Section title="{!$Label.c.CCM_OrderBasicInformation}" expandable="true">
                <lightning:layout multipleRows="true">
                    <!-- Warehouse -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Warehouse}">
                            {!v.basicInformation.WareHouse}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_OrderDate}">
                            {!v.basicInformation.orderDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Purchase Order Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_PurchaseOrderNumber}">
                            {!v.basicInformation.purchaseOrderNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Expected Delivery Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_ExpectedDeliveryDate}">
                            {!v.basicInformation.ExpectedDeliveryDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Price List​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_PriceList}">
                            {!v.basicInformation.priceList}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Est Order Volume​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_EstOrderVolume}">
                            {!v.basicInformation.EstOrderVolume ? v.basicInformation.EstOrderVolume + ' m³' : ''}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Est Order Weight​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_EstOrderWeight}">
                            {!v.basicInformation.EstWeight ? v.basicInformation.EstWeight + ' kg' : ''}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Insurance fee -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_InsuranceFee}">
                            <lightning:formattedNumber value="{!v.basicInformation.InsuranceFee}" style="currency" currencyCode="{!v.currencySymbol}"/>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!--Other fee -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_OtherFee}">
                            <lightning:formattedNumber value="{!v.basicInformation.OtherFee}" style="currency" currencyCode="{!v.currencySymbol}"/>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Is Dropship? -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_IsDropship}">
                            {!v.basicInformation.IsDropship ? 'YES' : 'NO'}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Type -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_OrderType}">
                            {!v.basicInformation.recordTypeName}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Status -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_OrderStatus}">
                            {!v.basicInformation.OrderStatus}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            
            <!-- Delivery Information -->
            <c:CCM_Section title="{!$Label.c.CCM_DeliveryInformation}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Bill To Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_BillToAddress}">
                            <aura:if isTrue="{!v.basicInformation.BillToAddressInfo.CompanyName}">
                                <span>{!v.basicInformation.BillToAddressInfo.CompanyName}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.BillToAddressInfo.Street}">
                                <span>{!v.basicInformation.BillToAddressInfo.Street}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!(v.basicInformation.BillToAddressInfo.City) || (v.basicInformation.BillToAddressInfo.PostalCode)}">
                                <span>{!v.basicInformation.BillToAddressInfo.PostalCode}&nbsp;&nbsp;{!v.basicInformation.BillToAddressInfo.City}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.BillToAddressInfo.Country}">
                                <span>{!v.basicInformation.BillToAddressInfo.Country}</span>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Ship To Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_ShipToAddress}">
                            <aura:if isTrue="{!v.basicInformation.ShipToAddressInfo.CompanyName}">
                                <span>{!v.basicInformation.ShipToAddressInfo.CompanyName}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.ShipToAddressInfo.Street}">
                                <span>{!v.basicInformation.ShipToAddressInfo.Street}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!(v.basicInformation.ShipToAddressInfo.City) || (v.basicInformation.ShipToAddressInfo.PostalCode)}">
                                <span>{!v.basicInformation.ShipToAddressInfo.PostalCode}&nbsp;&nbsp;{!v.basicInformation.ShipToAddressInfo.City}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.ShipToAddressInfo.Country}">
                                <span>{!v.basicInformation.ShipToAddressInfo.Country}</span>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Dropship Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_DropshipAddress}">
                            <aura:if isTrue="{!(v.basicInformation.DropshipType == 'End Consumer')}">
                                <aura:if isTrue="{!v.basicInformation.DropShipName}">
                                    <span>{!v.basicInformation.DropShipName}</span><br/>
                                </aura:if>
                                <aura:if isTrue="{!v.basicInformation.DropShipAddress1}">
                                    <span>{!v.basicInformation.DropShipAddress1}</span><br/>
                                </aura:if>
                                <aura:if isTrue="{!(v.basicInformation.DropShipCity) || (v.basicInformation.DropShipZip)}">
                                    <span>{!v.basicInformation.DropShipZip}&nbsp;&nbsp;{!v.basicInformation.DropShipCity}</span><br/>
                                </aura:if>
                                <aura:if isTrue="{!v.basicInformation.DropShipCountry}">
                                    <span>{!v.basicInformation.DropShipCountry}</span><br/>
                                </aura:if>
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!v.basicInformation.DropshipAddressInfo.CompanyName}">
                                        <span>{!v.basicInformation.DropshipAddressInfo.CompanyName}</span><br/>
                                    </aura:if>
                                    <aura:if isTrue="{!v.basicInformation.DropshipAddressInfo.Street}">
                                        <span>{!v.basicInformation.DropshipAddressInfo.Street}</span><br/>
                                    </aura:if>
                                    <aura:if isTrue="{!(v.basicInformation.DropshipAddressInfo.City) || (v.basicInformation.DropshipAddressInfo.PostalCode)}">
                                        <span>{!v.basicInformation.DropshipAddressInfo.PostalCode}&nbsp;&nbsp;{!v.basicInformation.DropshipAddressInfo.City}</span><br/>
                                    </aura:if>
                                    <aura:if isTrue="{!v.basicInformation.DropshipAddressInfo.Country}">
                                        <span>{!v.basicInformation.DropshipAddressInfo.Country}</span>
                                    </aura:if>
                                </aura:set>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Dropship Type -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_DropshipType}">
                            {!v.basicInformation.DropshipType}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            
            <!-- Order Item Information -->
            <c:CCM_Section title="{!$Label.c.CCM_OrderItemInformation}" expandable="true" >
                <div class="slds-p-left_medium slds-p-right--medium">
                    <div class="table-wrap">
                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productTable" role="grid">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Action}">{!$Label.c.CCM_Action}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_No}">{!$Label.c.CCM_No}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Product Description -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Model #  -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Model}">{!$Label.c.CCM_Model}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- UOM -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_UOM}">{!$Label.c.CCM_UOM}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_OrderDate}">{!$Label.c.CCM_OrderDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Request Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_RequestDate}">{!$Label.c.CCM_RequestDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_OrderQty}">{!$Label.c.CCM_OrderQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Inventory -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.Order_Inventory}">{!$Label.c.Order_Inventory}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Schedule Ship Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_ScheduleShipDate}">{!$Label.c.CCM_ScheduleShipDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Pricing Date -->
                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate" title="{!$Label.c.CCM_PricingDate}">{!$Label.c.CCM_PricingDate}</span>
                                                </div>
                                            </a>
                                        </th>
                                    </aura:if>
                                    <!-- List Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Standard Discount -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_StandardDiscount}">{!$Label.c.CCM_StandardDiscount}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Additional Discount -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_AdditionalDiscount}">{!$Label.c.CCM_AdditionalDiscount}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Unit Net Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_UnitNetPrice}">{!$Label.c.CCM_UnitNetPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Total Net Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_TotalNetPrice}">{!$Label.c.CCM_TotalNetPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Remark -->
                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate" title="{!$Label.c.CCM_Remark}">{!$Label.c.CCM_Remark}</span>
                                                </div>
                                            </a>
                                        </th>
                                    </aura:if>
                                </tr>
                            </thead>
                            <tbody>
                                <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                                    <tr aria-selected="false" id="{!index}" data-expanded="false" class="slds-hint-parent" onclick="{!c.showToolList}">
                                        <!-- Action -->
                                        <td scope="row">
                                            <div class="slds-truncate" title="">
                                                <aura:if isTrue="{!orderItem.lstProductTools.length > 0}">
                                                    <lightning:icon iconName="utility:chevrondown" size="xx-small" class="collapse_icn"/>
                                                </aura:if>
                                            </div>
                                        </td>
                                        <!-- line -->
                                        <td scope="row">
                                            <div class="slds-truncate" title="">
                                                {!index + 1}
                                            </div>
                                        </td>
                                        <!-- Product Description -->
                                        <td role="gridcell" title="{!orderItem.ProductDescription}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!orderItem.ProductDescription}
                                            </div>
                                        </td>
                                        <!-- Model #  -->
                                        <td role="gridcell" title="{!orderItem.Model}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.Model}</span>
                                            </div>
                                        </td>
                                        <!-- UOM  -->
                                        <td role="gridcell" title="{!orderItem.UOM}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.UOM}</span>
                                            </div>
                                        </td>
                                        <!-- Order Date -->
                                        <td role="gridcell" title="{!orderItem.orderDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.orderDate}</span>
                                            </div>
                                        </td>
                                        <!-- Request Date -->
                                        <td role="gridcell" title="{!orderItem.RequestDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.RequestDate}</span>
                                            </div>
                                        </td>
                                        <!-- Order Qty -->
                                        <td role="gridcell" title="{!orderItem.Qty}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.Qty}</span>
                                            </div>
                                        </td>
                                        <!-- haibo: 根据record type判断kit红绿灯展示 -->
                                        <!-- Inventory -->
                                        <td role="gridcell" title="{!orderItem.Inventory}">
                                            <aura:if isTrue="{!orderItem.productRecordType != 'TLS_VK'}">
                                                <div class="slds-truncate icon-position-wrap">
                                                    <aura:if isTrue="{!orderItem.Inventory == 'Red'}">
                                                        <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                        <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                            <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips3}"/>
                                                                <aura:set attribute="else">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips1}"/>
                                                                </aura:set>
                                                            </aura:if>
                                                        </div>
                                                    </aura:if>
                                                    <aura:if isTrue="{!orderItem.Inventory == 'Yellow'}">
                                                        <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                        <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                            <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                <aura:if isTrue="{! (v.basicInformation.warehouse == 'China (DI)')}">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips4}"/>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips2}"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                                <aura:set attribute="else">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips5}"/>
                                                                </aura:set>
                                                            </aura:if>
                                                        </div>
                                                    </aura:if>
                                                    <aura:if isTrue="{!orderItem.Inventory == 'Green'}">
                                                        <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                        <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                            <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                <aura:set attribute="else">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                </aura:set>
                                                            </aura:if>
                                                        </div>
                                                    </aura:if>
                                                </div>
                                            </aura:if>
                                        </td>
                                        <!-- Schedule Ship Date -->
                                        <td role="gridcell" title="{!orderItem.scheduShipDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.scheduShipDate}</span>
                                            </div>
                                        </td>
                                        <!-- Pricing Date -->
                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                            <td role="gridcell" title="{!orderItem.Pricingdate}">
                                                <div class="slds-truncate clear-user-agent-styles" >
                                                    <span>{!orderItem.Pricingdate}</span>
                                                </div>
                                            </td>
                                        </aura:if>
                                        <!-- List Price -->
                                        <td role="gridcell" title="{!orderItem.ListPrice}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <lightning:formattedNumber value="{!orderItem.ListPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                            </div>
                                        </td>
                                        <!-- Standard Discount -->
                                        <td role="gridcell" title="{!orderItem.standDiscount ? orderItem.standDiscount + '%' : ''}">
                                            <div class="slds-truncate">
                                                {!orderItem.standDiscount ? orderItem.standDiscount + '%' : ''}
                                            </div>
                                        </td>
                                        <!-- Additional Discount -->
                                        <td role="gridcell" title="{!orderItem.Discount ? orderItem.Discount + '%' : ''}">
                                            <div class="slds-truncate">
                                                {!orderItem.Discount ? orderItem.Discount + '%' : ''}
                                            </div>
                                        </td>
                                        <!-- Unit Net Price -->
                                        <td role="gridcell" title="{!orderItem.UnitNetPrice}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <lightning:formattedNumber value="{!orderItem.UnitNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                            </div>
                                        </td>
                                        <!-- Total Net Price -->
                                        <td role="gridcell" title="{!orderItem.TotalNetPrice}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <lightning:formattedNumber value="{!orderItem.TotalNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                            </div>
                                        </td>
                                        <!-- Remark -->
                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                            <td role="gridcell" title="{!orderItem.Remark}">
                                                <div class="slds-truncate clear-user-agent-styles" >
                                                    <span>{!orderItem.Remark}</span>
                                                </div>
                                            </td>
                                        </aura:if>
                                    </tr>
                                    <!-- tool info -->
                                    <aura:if isTrue="{!orderItem.lstProductTools.length > 0}">
                                        <tr aria-selected="false" class="slds-hint-parent" id="{!('tool' + index)}" style="display: none">
                                            <td colspan="{! (v.userType == 'InsideSales') ? 17 : 15}" style="padding: 0;">
                                                <div class="tool-table-wrap">
                                                    <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped tool-tabel" role="grid">
                                                        <thead>
                                                            <tr class="slds-line-height_reset">
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_Action}">{!$Label.c.CCM_Action}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_No}">{!$Label.c.CCM_No}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Product Description -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                            <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Model #  -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_Model}">{!$Label.c.CCM_Model}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Order Date -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_OrderDate}">{!$Label.c.CCM_OrderDate}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Request Date -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_RequestDate}">{!$Label.c.CCM_RequestDate}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Order Qty -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_OrderQty}">{!$Label.c.CCM_OrderQty}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Inventory -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                            <span class="slds-truncate" title="{!$Label.c.Order_Inventory}">{!$Label.c.Order_Inventory}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- List Price -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                            <span class="slds-truncate" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Unit Net Price -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_UnitNetPrice}">{!$Label.c.CCM_UnitNetPrice}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                                <!-- Total Net Price -->
                                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                            <span class="slds-truncate" title="{!$Label.c.CCM_TotalNetPrice}">{!$Label.c.CCM_TotalNetPrice}</span>
                                                                        </div>
                                                                    </a>
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <aura:iteration items="{!orderItem.lstProductTools}" var="toolItem" indexVar="toolIndex">
                                                                <tr aria-selected="false" class="slds-hint-parent">
                                                                    <!-- Action -->
                                                                    <td scope="row">
                                                                        <div class="slds-truncate" title="">
                                                                        </div>
                                                                    </td>
                                                                    <!-- line -->
                                                                    <td scope="row">
                                                                        <div class="slds-truncate" title="">
                                                                            {!index + 1}.{!toolIndex + 1}
                                                                        </div>
                                                                    </td>
                                                                    <!-- Product Description -->
                                                                    <td role="gridcell" title="{!toolItem.ProductDescription}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            {!toolItem.ProductDescription}
                                                                        </div>
                                                                    </td>
                                                                    <!-- Model #  -->
                                                                    <td role="gridcell" title="{!toolItem.Model}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            <span>{!toolItem.Model}</span>
                                                                        </div>
                                                                    </td>
                                                                    <!-- Order Date -->
                                                                    <td role="gridcell" title="{!toolItem.orderDate}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            <span>{!toolItem.orderDate}</span>
                                                                        </div>
                                                                    </td>
                                                                    <!-- Request Date -->
                                                                    <td role="gridcell" title="{!toolItem.RequestDate}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            <span>{!toolItem.RequestDate}</span>
                                                                        </div>
                                                                    </td>
                                                                    <!-- Order Qty -->
                                                                    <td role="gridcell" title="{!toolItem.Qty}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            <span>{!toolItem.Qty}</span>
                                                                        </div>
                                                                    </td>
                                                                    <!-- haibo: 根据record type判断kit红绿灯展示 -->
                                                                    <!-- Inventory -->
                                                                    <td role="gridcell" title="{!toolItem.Inventory}">
                                                                        <div class="slds-truncate icon-position-wrap">
                                                                            <aura:if isTrue="{!orderItem.productRecordType == 'TLS_VK'}">
                                                                                <aura:if isTrue="{!toolItem.Inventory == 'Red'}">
                                                                                    <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                                                    <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                            <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips3}"/>
                                                                                            <aura:set attribute="else">
                                                                                                <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips1}"/>
                                                                                            </aura:set>
                                                                                        </aura:if>
                                                                                    </div>
                                                                                </aura:if>
                                                                                <aura:if isTrue="{!toolItem.Inventory == 'Yellow'}">
                                                                                    <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                                                    <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                            <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips4}"/>
                                                                                            <aura:set attribute="else">
                                                                                                <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips2}"/>
                                                                                            </aura:set>
                                                                                        </aura:if>
                                                                                    </div>
                                                                                </aura:if>
                                                                                <aura:if isTrue="{!toolItem.Inventory == 'Green'}">
                                                                                    <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                                                    <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                            <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                                            <aura:set attribute="else">
                                                                                                <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                                            </aura:set>
                                                                                        </aura:if>
                                                                                    </div>
                                                                                </aura:if>
                                                                            </aura:if>
                                                                        </div>
                                                                    </td>
                                                                    <!-- List Price -->
                                                                    <td role="gridcell" title="{!toolItem.ListPrice}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            <lightning:formattedNumber value="{!toolItem.ListPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                        </div>
                                                                    </td>
                                                                    <!-- Unit Net Price -->
                                                                    <td role="gridcell" title="{!toolItem.UnitNetPrice}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            <lightning:formattedNumber value="{!toolItem.UnitNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                        </div>
                                                                    </td>
                                                                    <!-- Total Net Price -->
                                                                    <td role="gridcell" title="{!toolItem.TotalNetPrice}">
                                                                        <div class="slds-truncate clear-user-agent-styles" >
                                                                            <lightning:formattedNumber value="{!toolItem.TotalNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </aura:iteration>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                    </aura:if>
                                </aura:iteration>
                            </tbody>
                        </table>
                    </div>
                    <div class="slds-clearfix slds-m-top--medium">
                        <aura:if isTrue="{!v.poStatus == 'Pending Review'}">
                            <lightning:layout>
                                <div class="c-container" style="padding: 10px">
                                    <lightning:layoutItem alignmentBump="right">
                                        <lightning:button variant="brand" label="{!$Label.c.CCM_RefreshTrafficLight}" onclick="{!c.refreshTrafficLight}"/>
                                    </lightning:layoutItem>
                                </div>
                            </lightning:layout> 
                        </aura:if>
                        <div class="slds-grid slds-float--right">
                        <div class="slds-text-align--right">
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_TotalValue}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_HeaderDiscount}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_HeaderDiscountAmount}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_TotalValueNet}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_FreightCost}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_InsuranceFee}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_OtherFee}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_VAT}:&nbsp;</div>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.Order_TotalDueAmount}:&nbsp;</strong></div>
                        </div>
                        <div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.TotalValue)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong>{!v.basicInformation.HeaderDiscount}%</strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.HeaderDiscountAmount)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.TotalValueNet)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.FreightCost)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.InsuranceFee)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.OtherFee)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.VAT)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>

                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.basicInformation.TotalDueAmount}" style="currency" currencyCode="{!v.currencySymbol}"/> </strong></div>
                        </div>
                    </div>
                    </div>
                </div>
            </c:CCM_Section>

            <!-- Attachment -->
            <c:CCM_Section title="{!$Label.c.CCM_Attachment}" expandable="true">
                <aura:if isTrue="{!v.attachment.length > 0}">
                    <c:CCM_DataTable columns="{!v.attachmentColumns}" data="{!v.attachment}"/>
                    <aura:set attribute="else">
                        <p class="slds-text-align_center slds-p-around--medium">{!$Label.c.CCM_NoItemsToDisplay}</p>
                    </aura:set>
                </aura:if>
            </c:CCM_Section>
            
            <div class="CCM_PaddingTop slds-m-bottom_medium">
                <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_UploadAttachmentFile}" onclick="{!c.uploadFileItem}"/>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!(v.poStatus == 'Pending Review')}">
                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_UploadAttachmentFile}" onclick="{!c.uploadFileItem}"/>
                    </aura:if>
                </aura:if>
            </div>

            <div class="footer">
                <div class="footer-information">
                    <span class="slds-p-horizontal_x-large slds-text-body_regular"><strong>Sync Status:</strong>&nbsp;{!v.syncStatus}</span> 
                    <span class="slds-p-horizontal_x-large slds-text-body_regular"><strong>Sync Message:</strong>&nbsp;{!v.syncMessage}</span>
                </div>
                <div class="footer-btn slds-m-bottom_medium">
                    <lightning:button class="slds-p-horizontal_x-large" variant="brand-outline" label="{!$Label.c.CCM_Back}" onclick="{!c.doBack}"/>
                    <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process') || (v.syncStatus == 'Failed')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand-outline" label="{!$Label.c.CCM_Edit}" onclick="{!c.doEdit}"/>
                        </aura:if>
                    </aura:if>
                    <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.doCancelSync}"/>
                    </aura:if>
                    <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SaveAttachment}" onclick="{!c.saveFileLIst}"/>
                        </aura:if>
                    </aura:if>
                    <aura:if isTrue="{!(v.poStatus == 'Pending Review')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SaveAttachment}" onclick="{!c.saveFileLIst}"/>
                        </aura:if>
                    </aura:if>
                    <!-- <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SyncToEBS}" onclick="{!c.doSync}"/>
                        </aura:if>
                    </aura:if> -->
                    <aura:if isTrue="{!and(v.notSync, v.isSubmitted)}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SyncToEBS}" onclick="{!c.doSync}"/>
                    </aura:if>
                </div>
            </div>
        </div>
        <!-- 附件上传 -->
        <aura:if isTrue="{!v.uploadModalFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; height:auto !important; transform: translate(0%, 25%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_UploadAttachmentFile}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <lightning:layout horizontalAlign="space" verticalAlign="center">
                                    <!-- Attachment Name -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                        <lightning:input name="" label="{!$Label.c.CCM_AttachmentName}" value="{!v.attachmentName}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255"/>
                                    </lightning:layoutItem>
                                    <!-- Attachment Type -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                        <span class="combobox-label">{!$Label.c.CCM_AttachmentType}</span>
                                        <lightning:combobox class="ccm_display"
                                            name="Attachment Type" 
                                            value="{!v.attachmentType}" 
                                            options="{!v.attachmentTypeOptions}" 
                                            label=""
                                            variant="label-hidden"
                                        />
                                    </lightning:layoutItem>
                                    <!-- <lightning:layoutItem alignmentBump="center" size="5">
                                        <lightning:input name="" label="Attachment Type" value="{!v.attachmentType}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255"/>
                                    </lightning:layoutItem> -->
                                </lightning:layout>
                                <lightning:layout horizontalAlign="space" verticalAlign="center">
                                    <!-- Attachment file -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                        <lightning:input aura:id="upload" class="upload-wrap" name="" type="file" label="{!$Label.c.CCM_UploadAttachmentFile}" multiple="true" accept="" onchange="{!c.handleFilesChange}"/>
                                        <aura:if isTrue="{!v.uploadFinished}">
                                            <p class="uploadFinished" title="{!v.attachmentItem.name}">
                                                <span class="fileName">{!v.attachmentItem.name}</span>
                                                <a class="delete" onclick="{!c.deleteAttachmentItem}">{!$Label.c.CCM_Delete}</a>
                                            </p>
                                        </aura:if>
                                    </lightning:layoutItem>
                                    <!-- 占位 -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                    </lightning:layoutItem>
                                </lightning:layout>
                            </div>
                        </div>
                        <footer class="slds-modal__footer">
                            <div class="footer-wrap">
                                <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancelEvent}"/>
                                <lightning:button class="" variant="brand" label="{!$Label.c.CCM_Save}"  onclick="{!c.saveFileItem}"/>
                            </div>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
        <!-- 隐藏fileCard，弹出专用 -->
        <div class="slds-hide">
            <lightning:fileCard hideDescription="true"/>
        </div>
    </div>
</aura:component>