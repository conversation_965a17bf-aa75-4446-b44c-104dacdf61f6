@isTest
public with sharing class CCM_LeadController_Test{
    @testSetup
    static void setup(){
        try{
            insert new Account(
                RecordTypeId = CCM_Constants.CommercialConsumer_RECORDTYPEID, 
                Name = 'Test'
            );
            Lead lead1 = new Lead(
                TestFlag__c = true, 
                RecordTypeId = CCM_Constants.PROSPECT_CHANNEL_RECORD_TYPE_ID, 
                Status = 'Open', 
                LastName = 'Test1', 
                Company = 'Test1', 
                Prospect_Type__c = 'Dealer', 
                Sales_Channel__c = 'OPE Dealer', 
                Classification_1__c = 'Dealer_Standard', 
                Re_automatic__c = false, 
                Postal_Code__c = '0123545', 
                Country_All__c = 'DE-Germany', 
                QuestionNaire__c = '123'
            );
            Lead lead3 = new Lead(
                TestFlag__c = true, 
                RecordTypeId = CCM_Constants.PROSPECT_ENDUSER_RECORD_TYPE_ID, 
                Status = 'Open', 
                LastName = 'Test3', 
                Company = 'Test3', 
                Prospect_Type__c = 'End User', 
                Sales_Channel__c = 'End User', 
                Classification_1__c = 'Commercial User', 
                Re_automatic__c = false, 
                Postal_Code__c = '0123545', 
                Country_All__c = 'DE-Germany', 
                QuestionNaire__c = '123'
            );
            Lead lead5 = new Lead(
                TestFlag__c = true, 
                RecordTypeId = CCM_Constants.PROSPECT_BRANDPARTNER_RECORD_TYPE_ID, 
                Status = 'Open', 
                LastName = 'Test5', 
                Company = 'Test5', 
                Prospect_Type__c = 'Brand Partner', 
                Sales_Channel__c = 'Others', 
                Re_automatic__c = false, 
                Postal_Code__c = '0123545', 
                Country_All__c = 'DE-Germany', 
                QuestionNaire__c = '123'
            );
            insert lead1;
            insert lead3;
            insert lead5;
            //convert customer
            insert new Sales_Program__c(
                RecordTypeId = CCM_Constants.AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SALES_CUSTOMIZED_Id, 
                Prospect__c = lead1.Id
            );
            insert new Attachment_Management__c(
                Prospect__c = lead1.Id
            );
            insert new Customer_Profile__c(
                Prospect__c = lead1.Id
            );
            insert new Account_Address__c(
                Prospect__c = lead1.Id
            );
            insert new Contact(
                LastName = 'Test3', 
                Prospect__c = lead1.Id
            );
            //end user
            insert new Sales_Program__c(
                RecordTypeId = CCM_Constants.AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SALES_CUSTOMIZED_Id, 
                Prospect__c = lead3.Id
            );
            insert new Attachment_Management__c(
                Prospect__c = lead3.Id
            );
            insert new Customer_Profile__c(
                End_User_Prospect__c = lead3.Id
            );
            insert new Account_Address__c(
                Prospect__c = lead3.Id
            );
            insert new Sales_Rep_Stock_Lentout__c(
                Prospect__c = lead3.Id
            );
            insert new Contact(
                LastName = 'Test3', 
                Prospect__c = lead3.Id
            );
            CCM_LeadController.uploadFile(String.valueOf(lead3.Id), CCM_Constants.PROSPECT_FILETYPE_TEST, 'Test', '12345');
            //brand partner
            insert new Sales_Program__c(
                RecordTypeId = CCM_Constants.AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SALES_CUSTOMIZED_Id, 
                Prospect__c = lead5.Id
            );
            insert new Attachment_Management__c(
                Prospect__c = lead5.Id
            );
            insert new Customer_Profile__c(
                Brand_Partner_Prospect__c = lead5.Id
            );
            insert new Account_Address__c(
                Prospect__c = lead5.Id
            );
            insert new Contact(
                LastName = 'Test3', 
                Prospect__c = lead5.Id
            );
        } catch (Exception ex){

        }
    }
    @isTest
    static void testFile(){
        try{
            List<Lead> LeadListA = [Select id, ownerId, Status
                                    from Lead
                                    where Company = 'Test3'];
            Lead lead1 = LeadListA.get(0);
            CCM_LeadController.uploadFile(String.valueOf(lead1.Id), CCM_Constants.PROSPECT_FILETYPE_TEST, 'Test', '12345');
            String s = CCM_LeadController.searchFile(String.valueOf(lead1.Id), CCM_Constants.PROSPECT_FILETYPE_TEST);
            List<CCM_LeadController.ContentData> files = (List<CCM_LeadController.ContentData>)JSON.deserialize(s, List<CCM_LeadController.ContentData>.class);
            CCM_LeadController.deleteFile(files.get(0).ContentDocumentId, null);
        } catch (Exception ex){

        }
    }
    @isTest
    static void testQuestion(){
        try{
            Test.startTest();
            List<Lead> LeadListA = [Select id, ownerId, Status
                                    from Lead];
            List<Account> LeadAccount = [Select id, name
                                         from Account];
            Lead lead1 = LeadListA.get(0);
            Account acc1 = LeadAccount.get(0);
            CCM_LeadController.saveQuestionnaire(String.valueOf(lead1.Id), '1234');
            CCM_LeadController.saveQuestionnaire(String.valueOf(acc1.Id), '1234');
            CCM_LeadController.getQuestionnaire(String.valueOf(lead1.Id));
            CCM_LeadController.getQuestionnaire(String.valueOf(acc1.Id));
            CCM_LeadController.getQuestionnaireType(String.valueOf(lead1.Id));
            CCM_LeadController.getQuestionnaireType(String.valueOf(acc1.Id));
            Test.stopTest();
        } catch (Exception ex){

        }
    }
    @isTest
    static void testSubmit(){
        try{
            Test.startTest();
            List<Lead> LeadListA = [Select id, ownerId, Status
                                    from Lead
                                    where Company = 'Test3'];
            Lead lead1 = LeadListA.get(0);
            List<Account> aList = [Select id
                                   from Account];
            Account acc = aList.get(0);
            List<Contact> cList = [Select id
                                   from Contact];
            CCM_LeadController.submitForEndUserApply(String.valueOf(lead1.Id), String.valueOf(acc.Id), 'Test');

            Test.stopTest();

        } catch (Exception e){

        }
    }
    @isTest
    static void testSubmit2(){
        try{
            Test.startTest();
            List<Lead> LeadListA = [Select id, ownerId, Status
                                    from Lead
                                    where Company = 'Test3'];
            Lead lead1 = LeadListA.get(0);
            List<Account> aList = [Select id
                                   from Account];
            Account acc = aList.get(0);
            List<Contact> cList = [Select id
                                   from Contact];
            CCM_LeadController.currentApprover();
            CCM_LeadController.getConsumerInfo(1, 10, '1', '1', String.valueOf(cList.get(0).Id));
            CCM_LeadController.getConsumerInfo(1, 10, '', '', '');
            CCM_LeadController.fleetManageSearch('3');

            Test.stopTest();

        } catch (Exception e){

        }
    }
    @isTest
    static void testAction(){
        try{
            List<Lead> LeadListA = [Select id, ownerId, Status
                                    from Lead];
            Lead lead1 = LeadListA.get(0);
            Lead lead2 = LeadListA.get(1);
            Lead lead3 = LeadListA.get(2);
            CCM_LeadController.doReject(String.valueOf(lead2.Id), '123', 'Others');
            CCM_LeadController.doClose(String.valueOf(lead2.Id), '123', 'Others');
        } catch (Exception ex){

        }
    }
    @isTest
    static void testAction2(){
        try{
            Profile profile2 = [Select Id
                                from Profile
                                where NOT name LIKE '%Administrator%'];
           User userSales = new User(isActive = true, ProfileId = profile2.Id, Username = System.now().millisecond() + '<EMAIL>', Alias = 'batman', Email = '<EMAIL>', EmailEncodingKey = 'UTF-8', Firstname = 'Bruce', Lastname = 'Wayne', LanguageLocaleKey = 'en_US', LocaleSidKey = 'en_US', TimeZoneSidKey = 'America/Chicago');
            insert userSales;
            System.runAs(userSales){
                List<Lead> LeadListA = [Select id, ownerId, Status
                                        from Lead];
                Lead lead1 = LeadListA.get(0);
                Lead lead2 = LeadListA.get(1);
                Lead lead3 = LeadListA.get(2);
                CCM_LeadController.doReject(String.valueOf(lead2.Id), '123', 'Others');
                CCM_LeadController.doClose(String.valueOf(lead2.Id), '123', 'Others');
            }
        } catch (Exception ex){

        }
    }

    @isTest
    static void saveDetailTest(){
        Map<String, Object> saveData = new Map<String, Object>();
        List<Object> datas = new List<Object>();
        Map<String, Object> innerData = new Map<String, Object>();
        innerData.put('question', 'test');
        innerData.put('answer', 'tset');
        List<Object> listDatas = new List<Object>();
        Map<String, Object> listData = new Map<String, Object>();
        listData.put('select', true);
        listData.put('label', 'test');
        listData.put('answer', 'test');
        listDatas.add(listData);

        innerData.put('List', listDatas);
        saveData.put('data', innerData);
        List<Account> aList = [Select id from Account];
        CCM_LeadController.saveQuestionnaireDetail(aList.get(0).Id, JSON.serialize(saveData));
    }
}