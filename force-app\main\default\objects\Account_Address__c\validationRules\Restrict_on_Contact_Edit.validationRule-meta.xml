<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Restrict_on_Contact_Edit</fullName>
    <active>true</active>
    <description>Only Inside Sales can edit Acknowledgement / Dunning / Invoice Credit Contact on Customer</description>
    <errorConditionFormula>AND(
AND($Profile.Name != &apos;Inside Sales Manager&apos;, $Profile.Name != &apos;Inside Sales Rep&apos;, $Profile.Name != &apos;System Administrator&apos;),
NOT( ISBLANK(Customer__c) ),
OR(
	OR(
	ISCHANGED( Acknowledgement_Additional_1_Customer__c ),
	AND( ISNEW(),
	NOT( ISBLANK(Acknowledgement_Additional_1_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Dunning_Letter_Addition_1_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Dunning_Letter_Addition_1_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Dunning_Letter_Additional_2_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Dunning_Letter_Additional_2_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Dunning_Letter_Additional_3_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Dunning_Letter_Additional_3_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Invoice_Credit_Additional_1_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Invoice_Credit_Additional_1_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Invoice_Credit_Additional_2_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Invoice_Credit_Additional_2_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Invoice_Credit_Additional_3_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Invoice_Credit_Additional_3_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Acknowledgement_Additional_2_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Acknowledgement_Additional_2_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Acknowledgement_Additional_3_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Acknowledgement_Additional_3_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Copied_Invoice_Credit_1_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Copied_Invoice_Credit_1_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Copied_Invoice_Credit_2_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Copied_Invoice_Credit_2_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Copied_Invoice_Credit_3_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Copied_Invoice_Credit_3_Customer__c ))
	)
	),
	OR(
	ISCHANGED( Copied_Invoice_Credit_4_Customer__c ), 
	AND( ISNEW(), 
	NOT( ISBLANK(Copied_Invoice_Credit_4_Customer__c ))
	)
	)
)
)</errorConditionFormula>
    <errorMessage>Can not edit Acknowledgement / Dunning / Invoice Credit Contact(Additional / Copied included)</errorMessage>
</ValidationRule>
