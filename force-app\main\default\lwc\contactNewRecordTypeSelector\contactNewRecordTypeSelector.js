import { LightningElement, api, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getAvailableRecordTypes from '@salesforce/apex/ContactNewRecordTypeController.getAvailableRecordTypes';

export default class ContactNewRecordTypeSelector extends NavigationMixin(LightningElement) {
    @api recordId;
    
    entryType;

    recordTypeOptions = [];
    selectedRecordTypeId = '';
    isLoading = true;
    showRecordTypeSelection = true;
    
    isNextDisabled = true;
    
    connectedCallback() {
        this.loadRecordTypes();
    }
    
    loadRecordTypes() {
        this.isLoading = true;

        const searchParams = new URLSearchParams(location.search);
        const paramsObj = Object.fromEntries(searchParams.entries());
        let fromAccount = false;
        if('inContextOfRef' in paramsObj) {
            let startIndex = paramsObj['inContextOfRef'].indexOf('1.');
            if(startIndex !== -1) {
                let objectTypeInfo = JSON.parse(atob(paramsObj['inContextOfRef'].substring(startIndex+2)));
                this.entryType = objectTypeInfo.attributes.objectApiName;
                this.recordId = objectTypeInfo.attributes.recordId;
                if(this.entryType === 'Account') {
                    fromAccount = true;
                }
            }
        }
        // if('backgroundContext' in paramsObj) {
        //     let backgroundContext = paramsObj['backgroundContext'];
        //     if(backgroundContext.includes('Lead')) {
        //         const startindex = paramsObj['backgroundContext'].indexOf('Lead/');
        //         const endindex = paramsObj['backgroundContext'].indexOf('/', startindex+5);
        //         let leadId = paramsObj['backgroundContext'].substring(startindex+5, endindex);
        //         this.recordId = leadId;
        //         this.entryType = 'Lead';
        //     }
        //     else if(backgroundContext.includes('Account')) {
        //         const startindex = paramsObj['backgroundContext'].indexOf('Account/');
        //         const endindex = paramsObj['backgroundContext'].indexOf('/', startindex+8);
        //         let accountId = paramsObj['backgroundContext'].substring(startindex+8, endindex);
        //         this.recordId = accountId;
        //         this.entryType = 'Account';
        //         fromAccount = true;
        //     }
        // }

        getAvailableRecordTypes({ isFromAccount: fromAccount })
            .then(result => {
                this.recordTypeOptions = result.map(rt => ({
                    label: rt.label,
                    value: rt.value,
                    description: rt.description,
                    developerName: rt.developerName
                }));

                // 如果只有一个Record Type，自动选择
                if (this.recordTypeOptions.length === 1) {
                    this.selectedRecordTypeId = this.recordTypeOptions[0].value;
                }

                this.isLoading = false;
            })
            .catch(error => {
                this.isLoading = false;
                console.error('Error loading record types:', error);
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Error',
                        message: 'Failed to load record types: ' + (error.body?.message || error.message),
                        variant: 'error'
                    })
                );
            });
    }
    
    
    handleCancel() {
        // 关闭模态框或返回上一页
        history.back();
    }
    
    handleNext() {
        if (!this.selectedRecordTypeId) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Error',
                    message: 'Please select a Record Type',
                    variant: 'error'
                })
            );
            return;
        }
        this.showRecordTypeSelection = false;
        // 构建新建Contact页面的URL参数
        const defaultFieldValues = this.buildDefaultFieldValues();
        
        // 导航到标准的新建Contact页面
        this[NavigationMixin.Navigate]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Contact',
                actionName: 'new'
            },
            state: {
                recordTypeId: this.selectedRecordTypeId,
                defaultFieldValues: defaultFieldValues,
                nooverride: 1
            }
        });
    }
    
    buildDefaultFieldValues() {
        const urlParams = new URLSearchParams(window.location.search);
        let defaultValues = {};
        
        // 如果从Account创建，设置AccountId
        if (this.recordId) {
            if(this.entryType === 'Lead') {
                defaultValues.Prospect__c = this.recordId;
            }
            else if(this.entryType === 'Account') {
                defaultValues.AccountId = this.recordId;
            }
        }
        
        // 检查URL中是否有其他默认值
        const defaultFieldValuesParam = urlParams.get('defaultFieldValues');
        if (defaultFieldValuesParam) {
            try {
                const decodedValues = decodeURIComponent(defaultFieldValuesParam);
                const parsedValues = this.parseDefaultFieldValues(decodedValues);
                defaultValues = { ...defaultValues, ...parsedValues };
            } catch (error) {
                console.error('Error parsing default field values:', error);
            }
        }
        
        return this.encodeDefaultFieldValues(defaultValues);
    }
    
    parseDefaultFieldValues(encodedValues) {
        const values = {};
        const pairs = encodedValues.split(',');
        
        pairs.forEach(pair => {
            const [key, value] = pair.split('=');
            if (key && value) {
                values[key] = value;
            }
        });
        
        return values;
    }
    
    encodeDefaultFieldValues(values) {
        const pairs = [];
        for (const [key, value] of Object.entries(values)) {
            pairs.push(`${key}=${value}`);
        }
        return pairs.join(',');
    }

    handleRecordTypeSelected(event) {
        this.isNextDisabled = false;
        this.selectedRecordTypeId = event.target.value;
    }
}
