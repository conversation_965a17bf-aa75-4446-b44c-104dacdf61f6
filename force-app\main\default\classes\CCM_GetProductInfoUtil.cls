/**
* Author: Honey
* Description: 根据Customer、Pricing Date获取AuthBrand、然后获取价格册以及其下的产品。以及价格册的计算规则
* Date; 2023/06/11
*/
public without sharing class CCM_GetProductInfoUtil {
    public CCM_GetProductInfoUtil() {
        
    }
    
    public static  List<String> getPriceBookEntry(String customerId, Date pricingDate,String OrderType,String currencyCode) {
        List<String> lstProductIds = new List<String>();
        try{
            String queryOrderType = CCM_Constants.SALES_ORDER_DSV;
            if(OrderType.contains(CCM_Constants.DSV)){
                queryOrderType = CCM_Constants.SALES_ORDER_DSV;
            }else if(OrderType.contains(CCM_Constants.DI)){
                queryOrderType = CCM_Constants.SALES_ORDER_DI;
            }
            
            
            // 根据 CustomerId 查询 Auth Brand 信息
            List<Sales_Program__c> salesPrograms = [
                SELECT Price_Book__c,
                List_Price_1__c,List_Price_2__c,List_Price_3__c FROM Sales_Program__c
                WHERE Customer__c = :customerId AND Order_Type__c = :queryOrderType];
            Set<String> pricebookIds = new Set<String>();
            for(Sales_Program__c objSalesProgram : salesPrograms){
                if(String.isNotBlank(objSalesProgram.Price_Book__c)){
                    pricebookIds.add(objSalesProgram.Price_Book__c);
                }
                if(String.isNotBlank(objSalesProgram.List_Price_1__c)){
                    pricebookIds.add(objSalesProgram.List_Price_1__c);
                }
                if(String.isNotBlank(objSalesProgram.List_Price_2__c)){
                    pricebookIds.add(objSalesProgram.List_Price_2__c);
                }
                if(String.isNotBlank(objSalesProgram.List_Price_3__c)){
                    pricebookIds.add(objSalesProgram.List_Price_3__c);
                }
            }
            //根据PriceBook上的Id查询Entry信息
            List<Pricebook_Entry__c> pricebookEntries = [
                SELECT Product__c
                FROM Pricebook_Entry__c WHERE PriceBook__c IN :pricebookIds AND CurrencyIsoCode = :currencyCode AND (Product__r.Product_Status__c != 'INACT' AND  Product__r.Product_Status__c != 'RTO')];
          
            for(Pricebook_Entry__c objPriceBook : pricebookEntries){
                lstProductIds.add(objPriceBook.Product__c);
            }
            return lstProductIds;
        }catch(Exception e){
            system.debug('报错行数-->'+e.getLineNumber()+'报错信息-->'+e.getMessage());
        }
        return lstProductIds;
    }

    public static List<String> getProductsInPriceBookEntryWithoutFilterProductStatus(String customerId, Date pricingDate, String OrderType, String currencyCode) {
        List<String> lstProductIds = new List<String>();
        try{
            String queryOrderType = CCM_Constants.SALES_ORDER_DSV;
            if(OrderType.contains(CCM_Constants.DSV)){
                queryOrderType = CCM_Constants.SALES_ORDER_DSV;
            }else if(OrderType.contains(CCM_Constants.DI)){
                queryOrderType = CCM_Constants.SALES_ORDER_DI;
            }
            
            
            // 根据 CustomerId 查询 Auth Brand 信息
            List<Sales_Program__c> salesPrograms = [
                SELECT Price_Book__c,
                List_Price_1__c,List_Price_2__c,List_Price_3__c FROM Sales_Program__c
                WHERE Customer__c = :customerId AND Order_Type__c = :queryOrderType];
            Set<String> pricebookIds = new Set<String>();
            for(Sales_Program__c objSalesProgram : salesPrograms){
                if(String.isNotBlank(objSalesProgram.Price_Book__c)){
                    pricebookIds.add(objSalesProgram.Price_Book__c);
                }
                if(String.isNotBlank(objSalesProgram.List_Price_1__c)){
                    pricebookIds.add(objSalesProgram.List_Price_1__c);
                }
                if(String.isNotBlank(objSalesProgram.List_Price_2__c)){
                    pricebookIds.add(objSalesProgram.List_Price_2__c);
                }
                if(String.isNotBlank(objSalesProgram.List_Price_3__c)){
                    pricebookIds.add(objSalesProgram.List_Price_3__c);
                }
            }
            //根据PriceBook上的Id查询Entry信息
            List<Pricebook_Entry__c> pricebookEntries = [
                SELECT Product__c
                FROM Pricebook_Entry__c WHERE PriceBook__c IN :pricebookIds AND CurrencyIsoCode = :currencyCode];
          
            for(Pricebook_Entry__c objPriceBook : pricebookEntries){
                lstProductIds.add(objPriceBook.Product__c);
            }
            return lstProductIds;
        }catch(Exception e){
            system.debug('报错行数-->'+e.getLineNumber()+'报错信息-->'+e.getMessage());
        }
        return lstProductIds;
    }

    public Class ObjPriceInfo{
        //产品的Id
        public String ProductId{get;set;}
        //一级价格册的原价
        public Double ListPrice{get;set;}
        //行折扣比例
        public Double Discount{get;set;}
        //二级价格册计算后的价格
        public Double SalesPrice{get;set;}
        //出售价--->SalesPrice*Descount
        public Double UnitNetPrice{get;set;}
        //最便宜的Percent
        Public Double CheapestPercentagePrice{get;set;}
        //最便宜的一口价
        Public Double CheapestNewPrice{get;set;}
    }
    public static Map<String,Object> CaucaltePrice(String CustomerId, String ProductId, Date PricingDate, String currencyCode){
        Map<String,Object> mapFeild2Price = new Map<String,Object>();
        //直接去中间表查询-->中间表没有再看是否包含AllItem包含则找中间子表下面的Entry计算
        List<MasterProductPrice__c> lstMasterProduct = new List<MasterProductPrice__c>();
        if(PricingDate != null) {
            lstMasterProduct = [
                SELECT Id,Account__c,CurrencyIsoCode,Product__c,Product__r.Order_Model__c, Start_Date__c,End_Date__c,Final_Price__c,List_Price__c,Min_Final_Prority__c,Product__r.Product_Status__c,
                Has_AllItem__c, Modifier_Entry__c, Modifier_Entry__r.Application_Method__c,Modifier_Entry__r.Value__c FROM MasterProductPrice__c
                WHERE Account__c = :customerId  
                AND Start_Date__c <= :PricingDate 
                AND End_Date__c >= :PricingDate 
                AND Modifier_Entry__C != NULL
                AND CurrencyIsoCode = :currencyCode
                AND (Product__r.Product_Status__c != 'INACT' AND  Product__r.Product_Status__c != 'RTO')
                AND Product__c = :ProductId   AND Has_AllItem__c = FALSE   ORDER By min_Final_Prority__c ASC
            ];
        }
        else {
            lstMasterProduct = [
                SELECT Id,Account__c,CurrencyIsoCode,Product__c,Product__r.Order_Model__c, Start_Date__c,End_Date__c,Final_Price__c,List_Price__c,Min_Final_Prority__c,Product__r.Product_Status__c,
                Has_AllItem__c, Modifier_Entry__c, Modifier_Entry__r.Application_Method__c,Modifier_Entry__r.Value__c FROM MasterProductPrice__c
                WHERE Account__c = :customerId
                AND Modifier_Entry__C != NULL
                AND CurrencyIsoCode = :currencyCode
                AND (Product__r.Product_Status__c != 'INACT' AND  Product__r.Product_Status__c != 'RTO')
                AND ((Start_Date__c <= TODAY AND End_Date__c >= TODAY) OR End_Date__c < TODAY)
                AND Product__c = :ProductId AND Has_AllItem__c = FALSE ORDER By End_Date__c DESC, min_Final_Prority__c ASC
            ];
        }
            
        //遍历KeyMap 
        Map<Decimal,MasterProductPrice__c> mapPriority2Product = new Map<Decimal,MasterProductPrice__c>();
        List<Decimal> lstPriority = new List<Decimal>();
       
        //如果能够查到数据则直接返回价格
        if(lstMasterProduct != null && lstMasterProduct.size()>0){
            MasterProductPrice__c objFinalMaster = lstMasterProduct[0];
            Boolean needExclude = CCM_ExculdeDiscountUtil.needExcludeDiscount(objFinalMaster.Modifier_Entry__c, objFinalMaster.Product__r.Order_Model__c);
            mapFeild2Price.put(CCM_Constants.FINAL_PRICE, objFinalMaster.Final_Price__c);
            mapFeild2Price.put(CCM_Constants.LIST_PRICE, objFinalMaster.List_Price__c);            
            mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, objFinalMaster.Modifier_Entry__r.Value__c);
            if(needExclude) {
                mapFeild2Price.put(CCM_Constants.FINAL_PRICE, objFinalMaster.List_Price__c);
                mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, 0);
            }
            mapFeild2Price.put(CCM_Constants.APPLICATION_METHOD, objFinalMaster.Modifier_Entry__r.Application_Method__c);
            system.debug('mapFeild2Price-->'+mapFeild2Price);
            return mapFeild2Price;
            
        }
        //如果为空查询Product下的子表Entry数据---》正常情况下满足条件的只有一条
        List<MasterProductPrice__c> lstMasterProductAllItem = new List<MasterProductPrice__c>();
        if(PricingDate != null) {
            lstMasterProductAllItem = [
                SELECT Id, Account__c, Product__c, Product__r.Product_Status__c, Start_Date__c, End_Date__c, Final_Price__c, Has_AllItem__c, Min_Final_Prority__c, Modifier_Entry__c FROM MasterProductPrice__c
                WHERE Account__c = :customerId AND Start_Date__c <= :PricingDate AND End_Date__c >= :PricingDate AND Has_AllItem__c = TRUE 
                AND CurrencyIsoCode = :currencyCode
                AND (Product__r.Product_Status__c != 'INACT' AND  Product__r.Product_Status__c != 'RTO')
            ];
        }
        else {
            lstMasterProductAllItem = [
                SELECT Id, Account__c, Product__c, Product__r.Product_Status__c, Start_Date__c, End_Date__c, Final_Price__c, Has_AllItem__c, Min_Final_Prority__c, Modifier_Entry__c FROM MasterProductPrice__c
                WHERE Account__c = :customerId AND Has_AllItem__c = TRUE 
                AND CurrencyIsoCode = :currencyCode
                AND (Product__r.Product_Status__c != 'INACT' AND Product__r.Product_Status__c != 'RTO')
                AND ((Start_Date__c <= TODAY AND End_Date__c >= TODAY) OR End_Date__c < TODAY)
                ORDER BY End_Date__c DESC
            ];
        }
        
        system.debug('lstMasterProductAllItem-->'+lstMasterProductAllItem);
        //获取到master数据。根据MasterId查询-->满足条件的只有一条。直接取第一条的Id查询
        if(lstMasterProductAllItem == null || lstMasterProductAllItem.size()==0){
            return null;
        }
        //得到minFinal最小的值。去子表中查询
        List<LinkProductAndPrice__c> lstLinkProduct = [
            SELECT  value__c, Final_Priority__c , Application_Method__c,
            Modifier_Entry__c,
            Modifier_Entry__r.Value__c,Modifier_Entry__r.Application_Method__c
            FROM LinkProductAndPrice__c WHERE MasterProductPrice__c = :lstMasterProductAllItem[0].Id
            AND Final_Priority__c = :lstMasterProductAllItem[0].Min_Final_Prority__c
            AND  Is_Delete__c != TRUE
            AND CurrencyIsoCode = :currencyCode
        ];
        system.debug('lstLinkProduct-->'+lstLinkProduct);
        //根据CustomerID-->查询一级二级价格册获取原价
        List<Sales_Program__c> lstAuth = [
            SELECT Id,Customer__c,Modifier_1__c,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,
            Modifier_2__c FROM Sales_Program__c WHERE Customer__c = :CustomerId
        ];
        Set<String> setPriceBookIds = new Set<String>();
        String PriceBook1st = '';
        for(Sales_Program__c objAuth : lstAuth){
            if(String.isNotBlank(objAuth.List_Price_1__c)){
                setPriceBookIds.add(objAuth.List_Price_1__c);
            }
            if(String.isNotBlank(objAuth.List_Price_2__c)){
                setPriceBookIds.add(objAuth.List_Price_2__c);
            }
            if(String.isNotBlank(objAuth.List_Price_3__c)){
                setPriceBookIds.add(objAuth.List_Price_3__c);
            }
            if(String.isNotBlank(objAuth.Price_Book__c)){
                setPriceBookIds.add(objAuth.Price_Book__c);
                PriceBook1st = objAuth.Price_Book__c;
            }
            
        }
        system.debug('setPriceBookIds-->'+setPriceBookIds);
        system.debug('ProductId-->'+ProductId);
        system.debug('PricingDate-->'+PricingDate);
        //同一时间。同一个Custoemr同一个产品只会有一个Entry
        List<Pricebook_Entry__c>  lstPriceBookEntry = new List<Pricebook_Entry__c>();
        if(PricingDate != null) {
            lstPriceBookEntry = [
                SELECT Id, PriceBook__c, UnitPrice__c, End_Date__c, Start_Date__c, Product__c, Product__r.Order_Model__c
                FROM Pricebook_Entry__c WHERE PriceBook__c IN :setPriceBookIds
                and Product__c = :ProductId AND End_Date__c >= :PricingDate AND Start_Date__c <= :PricingDate
                AND CurrencyIsoCode = :currencyCode
            ];
        }
        else {
            lstPriceBookEntry = [
                SELECT Id, PriceBook__c, UnitPrice__c, End_Date__c, Start_Date__c, Product__c, Product__r.Order_Model__c
                FROM Pricebook_Entry__c WHERE PriceBook__c IN :setPriceBookIds
                AND Product__c = :ProductId AND CurrencyIsoCode = :currencyCode
                AND ((End_Date__c >= TODAY AND Start_Date__c <= TODAY) OR End_Date__c < TODAY)
                ORDER BY End_Date__c DESC
            ];
        }
        
        Boolean needExclude = false;
        if(!lstPriceBookEntry.isEmpty()) {
            needExclude = CCM_ExculdeDiscountUtil.needExcludeDiscount(lstMasterProductAllItem[0].Modifier_Entry__c, lstPriceBookEntry[0].Product__r.Order_Model__c);
        }
        Double ListPrice = 0;
        for(Pricebook_Entry__c objPriceBookEntry : lstPriceBookEntry ){
            //先计算一级价格册的原价
            if(PriceBook1st == objPriceBookEntry.PriceBook__c){
                mapFeild2Price.put(CCM_Constants.LIST_PRICE, objPriceBookEntry.UnitPrice__c);
                ListPrice = objPriceBookEntry.UnitPrice__c;
                break;
            }
            // else{
            //     //一级不存在再存放二级  存放二级
            //     mapFeild2Price.put(CCM_Constants.LIST_PRICE, objPriceBookEntry.UnitPrice__c);
            //     ListPrice = objPriceBookEntry.UnitPrice__c;
            // }
        }
        //一级不存在再存放二级  存放二级
        for(Pricebook_Entry__c objPriceBookEntry : lstPriceBookEntry ){
            if(!mapFeild2Price.containsKey(CCM_Constants.LIST_PRICE)) {
                mapFeild2Price.put(CCM_Constants.LIST_PRICE, objPriceBookEntry.UnitPrice__c);
                ListPrice = objPriceBookEntry.UnitPrice__c;
            }
        }
        system.debug('mapFeild2Price-->'+mapFeild2Price);
        //遍历查询到的LinkProductAndPrice__c---》获取最小价格
        List<Decimal> lstFinalPrice = new List<Decimal>();
        //创建Price到objLink的映射
        Map<Decimal,LinkProductAndPrice__c> mapPrice2Link = new Map<Decimal,LinkProductAndPrice__c>();
        for(LinkProductAndPrice__c objLink : lstLinkProduct){
            Decimal FinalPrice = 0;
            if(objLink.Application_Method__c == 'Percent'){
                //表示是百分比
                if(!needExclude) {
                    FinalPrice = (1-objLink.value__c/100)*ListPrice;
                    Decimal FinalDiscountPrice = ((objLink.value__c * ListPrice)/100);
                    FinalDiscountPrice = FinalDiscountPrice.setScale(2, RoundingMode.HALF_UP);
                    FinalPrice = ListPrice - FinalDiscountPrice;
                    FinalPrice = FinalPrice.setScale(2, RoundingMode.HALF_UP);
                }
                else {
                    FinalPrice = ListPrice;
                }
                
            }else if(objLink.Application_Method__c == 'New Price'){
                FinalPrice = objLink.value__c;
            }
            lstFinalPrice.add(FinalPrice);
            mapPrice2Link.put(FinalPrice, objLink);
        }
        //对List进行排序
        system.debug('排序前--->'+lstFinalPrice);
        lstFinalPrice.sort();
        system.debug('排序后--->'+lstFinalPrice);
        mapFeild2Price.put(CCM_Constants.FINAL_PRICE, lstFinalPrice[0]);
        //通过最终取值获取到唯一的Link
        LinkProductAndPrice__c objFinalLink = mapPrice2Link.get(lstFinalPrice[0]);
        
        mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, objFinalLink.Modifier_Entry__r.Value__c);
        if(needExclude) {
            mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, 0);
        }
        mapFeild2Price.put(CCM_Constants.APPLICATION_METHOD, objFinalLink.Modifier_Entry__r.Application_Method__c);
        system.debug('mapFeild2Price-->'+mapFeild2Price);
        return mapFeild2Price;
    }

    //Honey Added 通过产品和时间获取一级价格册的价格
    public static Map<String,Decimal> getListPrice(List<String> lstProductId,Date PricingDate,String CurrencyCode,String customerId){
        //通过CustomerId查询PriceBookId 
        List<Sales_Program__c> lstAuthBrand = [
                SELECT Id,Customer__c,Modifier_1__c,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,Order_Type__c,
                Modifier_2__c FROM Sales_Program__c WHERE Customer__c = :customerId AND Status__c = 'Active'
                AND Order_Type__c = :Label.SalesPrice_OrderType 
        ];
        Set<String> setPriceBookIds = new Set<String>();
        for(Sales_Program__c objSalesProgram : lstAuthBrand){
            if(String.isNotBlank(objSalesProgram.Price_Book__c)){
                setPriceBookIds.add(objSalesProgram.Price_Book__c);
            }
            if(String.isNotBlank(objSalesProgram.List_Price_1__c)){
                setPriceBookIds.add(objSalesProgram.List_Price_1__c);
            }
            if(String.isNotBlank(objSalesProgram.List_Price_2__c)){
                setPriceBookIds.add(objSalesProgram.List_Price_2__c);
            }
            if(String.isNotBlank(objSalesProgram.List_Price_3__c)){
                setPriceBookIds.add(objSalesProgram.List_Price_3__c);
            }
        }
        //通过时间和价格唯一确认一个产品的价格
        List<Pricebook_Entry__c> lstPricebook = [
            SELECT Id,End_Date__c,Start_Date__c,UnitPrice__c,Product__c FROM Pricebook_Entry__c
            WHERE End_Date__c >= :PricingDate AND Start_Date__c <= :PricingDate AND Product__c IN :lstProductId AND CurrencyIsoCode = :CurrencyCode
            AND PriceBook__c IN : setPriceBookIds
        ];
        Map<String,Decimal> mapProductId2Price = new Map<String,Decimal>();
        for(Pricebook_Entry__c objPriceBookEntry : lstPricebook){
            mapProductId2Price.put(objPriceBookEntry.Product__c, objPriceBookEntry.UnitPrice__c);
        }
        system.debug('mapProductId2Price-->'+mapProductId2Price);
        return mapProductId2Price;
    }
    

    public static Map<String,String> getPriceName(List<String> lstProductId,Date PricingDate,String CurrencyCode,String customerId){
        Sales_Program__c objSalesProgram = new Sales_Program__c();
        Set<String> setPriceBookName = new Set<String>();
        Map<String,String> mapProductId2PriceName = new Map<String,String>();
        //通过AuthBrandID查询PriceBookId信息
        if(String.isNotBlank(customerId)){
            objSalesProgram = [
                SELECT Id,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,Customer__c FROM Sales_Program__c WHERE Customer__c = :customerId
                AND Status__c = 'Active'
                AND Order_Type__c = :Label.SalesPrice_OrderType
                LIMIT 1
            ];        
            setPriceBookName.add(objSalesProgram.List_Price_1__c);
            setPriceBookName.add(objSalesProgram.List_Price_2__c);
            setPriceBookName.add(objSalesProgram.List_Price_3__c);
            setPriceBookName.add(objSalesProgram.Price_Book__c);
        }
        List<Pricebook_Entry__c> lstPriceBookEntry = [
            SELECT Id,Product__c,Start_Date__c,End_Date__c,PriceBook__c, PriceBook__r.Name
            FROM Pricebook_Entry__c WHERE Product__c IN :lstProductId
            AND Start_Date__c <= :PricingDate AND End_Date__c >= :PricingDate
            AND PriceBook__c IN :setPriceBookName
        ];

        Map<String,String> mapProductDate2Name2st = new Map<String,String>();
        Map<String,String> mapProductDate2Name1st = new Map<String,String>();
        
        for(Pricebook_Entry__c objPriceBook : lstPriceBookEntry){
            //优先一级价格册
            if(objPriceBook.PriceBook__c == objSalesProgram.Price_Book__c){
                mapProductDate2Name1st.put(objPriceBook.Product__c,objPriceBook.PriceBook__r.Name);
            }else{
                mapProductDate2Name2st.put(objPriceBook.Product__c,objPriceBook.PriceBook__r.Name);
            }
           
        }

        for(String productId : lstProductId){
            String PriceBookName1st = mapProductDate2Name1st.get(productId);
            String listPriceName = String.isNotBlank(PriceBookName1st)?
               PriceBookName1st :  mapProductDate2Name2st.get(productId);
            mapProductId2PriceName.put(productId, listPriceName);
        }
        return mapProductId2PriceName;
    }

}