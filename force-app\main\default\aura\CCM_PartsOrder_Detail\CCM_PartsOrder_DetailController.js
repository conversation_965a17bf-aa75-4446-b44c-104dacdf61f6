({
    doInit : function(component, event, helper) {
        // 获取基本信息
        helper.getBaseInfo(component);
    },
    // 上一步
    previousStep: function(component){
        var currentStep = component.get("v.currentStep");
        var actionType = component.get("v.actionType");
        if (actionType !== 'edit') {
            component.set('v.actionType', 'draft');
        }
        component.set("v.currentStep", currentStep - 1);
    },
    // 取消事件
    cancel: function(component){
        let url = window.location.origin + '/lightning/n/Purchase_Order_List';
        window.open(url, '_self');
    },
    // 提交事件
    doSubmit: function(component, event, helper){
        // 获取基本信息
        helper.handleSubmitEvent(component);
    },
    showToolList: function(component, event, helper){
        var id = event.currentTarget.getAttribute('id');
        var expanded = event.currentTarget.getAttribute('data-expanded');
        if(expanded == 'true'){
            document.getElementById('tool' + id).style.display = 'none'
            event.currentTarget.setAttribute('data-expanded', false);
        }else{
            document.getElementById('tool' + id).style.display = 'table-row';
            event.currentTarget.setAttribute('data-expanded', true);
        }
    },

    doSync: function(component, event, helper) {
        helper.handleSync(component);
    }
})