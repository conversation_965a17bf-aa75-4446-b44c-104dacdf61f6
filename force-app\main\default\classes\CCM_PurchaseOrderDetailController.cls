/**
 * Author: Honey
 * Description: 查询PurchaseOrder详细信息
 * Date: 2023-06-17
 */
public without sharing class CCM_PurchaseOrderDetailController {
    public static String userType = '';
    public enum userTypeEnums{
        InsideSales, 
        SalesRep
    }

    public enum OrderTypeEnums{
        Regular_Order, 
        PreSeason_Order
    }
    @AuraEnabled
    public static String getData(String recordId){
        Boolean isSuccess = true;
        InitData initD = new InitData();
        userType = GetUserType();
        CCM_PurchaseOrderPreview.PurchaseOrderReview objPurchaseOrderReview = new CCM_PurchaseOrderPreview.PurchaseOrderReview();
        //判断类型
        if (String.isNotBlank(recordId)){
            objPurchaseOrderReview = CCM_PurchaseOrderPreview.QueryDetail(recordId);
        }
        initD.UserType = userType;
        initD.objPurchaseOrderItemReview = objPurchaseOrderReview;
        //判断step
        if (objPurchaseOrderReview.Status == 'Draft'){
            initD.currentStep = 1;
        } else if (objPurchaseOrderReview.Status == CCM_Constants.PENDING_REVIEW ||CCM_Constants.SUBMITTED == objPurchaseOrderReview.Status ){
            initD.currentStep = 2;
        }
         
        system.debug('initD-->'+initD);
        return JSON.serialize(initD);
        
    }
    @AuraEnabled
    public static String GetUserType(){
        String Type = '';
        //判断用户--->根据用户角色判断
        User objUser = [
            SELECT Id, ProfileId, Profile.Name, UserRoleId, UserRole.Name FROM User    WHERE Id = :UserInfo.getUserId()
        ];

        Set<String> SetSalesRepProfile = new Set<String>(Label.Sales_Rep_Profile.split(','));
        Set<String> SetInsideSalesProfile = new Set<String>(Label.Inside_Sales_Profile.split(','));
        system.debug('objUser.Profile.Name--->'+objUser.Profile.Name);
        if (SetInsideSalesProfile.contains(objUser.Profile.Name)){
            system.debug('进入insideSales');
            userType = 'InsideSales';
            Type = 'InsideSales';
        }else if(SetSalesRepProfile.contains(objUser.Profile.Name)){
            system.debug('进入SalesRep');
            Type = 'SalesRep';
        }else{
            system.debug('进入Dealer');
            Type = 'Dealer';
        }
        return Type;
    }
    @AuraEnabled
    public static Map<String,String> GetCurrentUserCustomer(){
        Map<String,String> mapFeild2Value = new Map<String,String>();
        User objUser = [SELECT 
                        ContactId, Contact.Account_Name__c, 
                        Contact.AccountId, CurrencyIsoCode, Contact.Account.CurrencyIsoCode,
                        Contact.Account.Sales_Channel__c
                        FROM User WHERE Id = :UserInfo.getUserId()
                       ];
        mapFeild2Value.put('CustomerId', objUser.Contact.AccountId );
        mapFeild2Value.put('CustomerName', objUser.Contact.Account_Name__c );
        mapFeild2Value.put('CurrencyIsoCode', objUser.Contact.Account.CurrencyIsoCode );
        mapFeild2Value.put('isDealer', 'False');
        if(String.isNotBlank(objUser.Contact.Account.Sales_Channel__c)) {
            if(objUser.Contact.Account.Sales_Channel__c.contains('Dealer')) {
                mapFeild2Value.put('isDealer', 'True');
            }
        }
        return mapFeild2Value;
    }


    public class InitData{
        public CCM_PurchaseOrderPreview.PurchaseOrderReview objPurchaseOrderItemReview{get;set;}
        
        public Integer CurrentStep{get;set;}
        
        public String UserType{get;set;}
        
        public Boolean isSuccess{get;set;}
        
        public String errorMsg{get;set;}
    }
    
    public CCM_PurchaseOrderDetailController() {
        
    }

    // add by haibo
    @AuraEnabled
    public static String QueryPirchaseAndItemInfo(String PurchaseOrderId,Boolean IsProtal){
        return CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(PurchaseOrderId,IsProtal);
    }
}