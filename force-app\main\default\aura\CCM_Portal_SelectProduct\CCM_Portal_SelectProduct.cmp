<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 06-12-2024
  @last modified by  : <EMAIL>
-->
<aura:component controller="CCM_RequestPurchaseOrderController" description="CCM_IN_PartsOrder_SelectProduct" implements="flexipage:availableForAllPageTypes" access="global">
    
    <aura:attribute name="isBusy" type="Boolean" default="false"/>

    <aura:attribute name="currentStep" type="Integer"/>
    <aura:attribute name="pricingDate" type="String"/>
    <aura:attribute name="warehouse" type="String"/>
    <aura:attribute name="recordTypeName" type="String"/>
    <aura:attribute name="purchaseOrderId" type="String"/>
    <aura:attribute name="isDropShip" type="String"/>
    <aura:attribute name="userType" type="String"/>
    <aura:attribute name="contactId" type="String"/>


    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="authorizedBrandOptions" type="List" default="[{'label': 'EGO', 'value': 'EGO'}]"/>
    <aura:attribute name="authorizedBrand" type="String" default="EGO"/>
    <aura:attribute name="authBrandId" type="String" default=""/>
    <aura:attribute name="paymentTerm" type="String" default=""/>
    <aura:attribute name="paymentTerm_copy" type="String" default=""/>
    <aura:attribute name="freightTerm" type="String" default=""/>
    <aura:attribute name="incoTerm" type="String" default=""/>
    <aura:attribute name="isDropShipOpt" type="List" default="[{'label': 'Yes', 'value': 'Y'},{'label': 'No', 'value': 'N'}]"/>
    <aura:attribute name="productList" type="List" default="[]"/>
    <aura:attribute name="currencySymbol" type="String"/>
    <aura:attribute name="operationRow" type="Integer" default="0"/>
    <aura:attribute name="promotionList" type="List" default="[]"/>
    <aura:attribute name="productSelect" type="Object" default=""/>
    <aura:attribute name="promotionobj" type="Object" default=""/>
    <aura:attribute name="expectedDeliveryDate" type="String" default=""/>
    <aura:attribute name="actionType" type="String"/>
    <aura:attribute name="POId" type="String"/>
    <aura:attribute name="termFlag" type="Boolean" default="true"/>
    <aura:attribute name="paymentTermOptions" type="List" default="[]"/>
    <aura:attribute name="incoTermOptions" type="List" default="[]"/>
    <aura:attribute name="customerType" type="String"/>
    <aura:attribute name="PodetailInfo" type="Map"/>
    <aura:attribute name="maxStep" type="Integer"/>
    <aura:attribute name="customerPO" type="String" default=""/>

    <aura:attribute name="prodCondition" type="String" default="" />


    <aura:attribute name="accessoriesColumns" type="List" default="[]"/>
    <aura:attribute name="accessoriesData" type="List" default="[]"/>
    <aura:attribute name="productOptions" type="List" default="[]"/>

    <aura:attribute name="woheaderdiscount" type="List" default="[]"/>
    <aura:attribute name="headerDiscountList" type="List" default="[]"/>
    <aura:attribute name="woPromotionCode" type="String" default=""/>
    <aura:attribute name="editFreightCost" type="Boolean" default="false"/>
    <aura:attribute name="showHelpText" type="Boolean" default="false"/>
    <aura:attribute name="errorinfo" type="String" default="校验全部通过"/>
    <aura:attribute name="relatedSubstituteModalFlag" type="Boolean" default="false"/>
    <aura:attribute name="substituteData" type="List" default="[]"/>
    <aura:attribute name="relatedData" type="List" default="[]"/>

    <!-- for product offering pool combine -->
    <aura:attribute name="hasPoolProductCombine" type="Boolean" default="false" />
    <aura:attribute name="signleProductOffering" type="List" default="[]" />
    
    <!-- 总数 -->
    <aura:attribute name="totalValue" type="Decimal" default="0"/>
    <aura:attribute name="headerDiscount" type="Decimal" default="0"/>
    <aura:attribute name="headerDiscountAmount" type="Decimal" default="0"/>
    <aura:attribute name="totalValueNet" type="Decimal" default="0"/>
    <aura:attribute name="freightCost" type="Decimal" default="0"/>
    <aura:attribute name="insuranceFee" type="Decimal" default="0"/>
    <aura:attribute name="otherFee" type="Decimal" default="0"/>
    <aura:attribute name="VAT" type="Decimal" default="0"/>
    <aura:attribute name="HeaderDiscountAmount__c" type="Decimal" default="0"/>
    <aura:attribute name="totalDueAmount" type="Decimal" default="0"/>

    <!-- 弹框 -->
    <aura:attribute name="promotionModalFlag" type="Boolean" default="false"/>
    <aura:attribute name="bogoFlag" type="Boolean" default="false"/>
    <aura:attribute name="accessoriesModalFlag" type="Boolean" default="false"/>
    <aura:attribute name="unitPerPalletModalFlag" type="Boolean" default="false"/>
    
    <aura:attribute name="palletInfo" type="Map"/>
    <!-- ================================================= 二级弹窗选择product部分 Start ======================================================= -->
    <!-- 配置需要二级弹窗选择Product的type    threshold && offering   threshold部分 -->
    <aura:attribute name="thresholdProductSelect" type="List" default="[
        'BOGO_Multiple_Products',
        'Price_Discount_Multiple_Products',
        'Multiple_Products_By_Amount',
        'Multiple_Products_By_Quantity',
    ]"/>
<!-- 配置需要二级弹窗选择Product的type    threshold && offering   offering部分 -->
    <aura:attribute name="offeringProductSelect" type="List" default="[
                'Pool_of_Free_Goods_of_Customer_Choice',
            ]"/>
    <!-- ================================================= 配置不同的promotion应用的二级弹框信息 Start ======================================================= -->
    <!-- product选择二级弹框填充的数据 弹窗的所有信息 -->
    <aura:attribute name="pupUp_ProductSelect" type="Map" default="{}"/>
<aura:attribute name="cheapestproduct" type="Map" default="{}"/>

    <!-- BOGO类型 -->
    <!-- threshold -->
    <aura:attribute name="pupUp_BOGO_threshold" type="Map" default="{
        'goon' : true,
        'type' : 'threshold',
        'name': 'MixGoods Product',
        'detail': '',
        'tips' : 'Search Mixgoods',
        'thead' : ['No.','Product Description','Model#'],
        'list' : []
    }"/>
    <!-- offering -->
    <aura:attribute name="pupUp_BOGO_Offering" type="Map" default="{
        'goon' : false,
        'type' : 'offering',
        'name': 'Free Goods List',
        'detail': '',
        'tips' : 'Search Free Goods',
        'thead' : ['No.','Product Description','Model#','Qty'],
        'list' : []
    }"/>
    <!-- Price Discount类型 -->
    <!-- threshold -->
    <!-- offering -->

    <!-- Price Break类型 -->
    <!-- threshold -->
    <!-- offering -->
<aura:attribute name="WholeOrder_DO" type="Decimal" default="0"/>  <!-- Price Break 整单折扣 -->


    
    <!-- ======================== Product大类校验部分 Start ===================== -->
    <!-- type大类分类 -->
    <aura:attribute name="category" type="Map" default="{
        'FixedAmount': ['Fixed_Amount_Multiple_Products','Fixed_Amount_Single_Product'],
        'DiscountOff': ['Discount_Off_Cheapest_Among_Products','Discount_Off_Multiple_Products','Discount_Off_Single_Product'],
        'FreeGoods':['Free_Goods_Multiple_Products','Free_Goods_Single_Product','Pool_of_Free_Goods_of_Customer_Choice'],
        'WholeOrder_DO':['Whole_Order_Discount_Off','Whole_Order_Payment_Term']
    }"/>
    <!-- product每种大类装进来 -->
    <!-- <aura:attribute name="Product_TypeVerify" type="Map" default="{}"/>     ==========================666 -->
    <!-- ======================== Product大类校验部分 End ======================== -->

    <!-- threshold和offering都是二次弹框的话,第一次的threshold数据需要暂存一下 Start -->
    <aura:attribute name="thresholdAndoffering" type="List" default="[]"/>
    <!-- 当前行 -->
    <aura:attribute name="Currentrowdata" type="Object" default=""/>
    <!-- ================================================= 配置不同的promotion应用的二级弹框信息 End ========================================================= -->
    <aura:attribute name="isSelectProduct" type="Boolean" default="false"/>

    <!-- ========================================================= Promotion价格计算部分 Start ============================================================== -->
    <!-- 取值有两个地方,single product的在 -->
    <!-- offering的类型需要的参与计算的字段 -->
    <aura:attribute name="PriceCalculation" type="Map" default="{
        'Free_Goods_Single_Product':{
            'Line_L_P':{'isproduct':false,field:'Line_Level_Promot__c'},
            'Compatible_L_P':{'isproduct':false,field:'Compatible_with_Header_Level_Promotion__c'},
            'Free_Goods_Q':{'isproduct':false,field:'Free_Goods_Quantity__c'},
        },
        'Free_Goods_Multiple_Products':{
            'Line_L_P':{'isproduct':false,field:'Line_Level_Promot__c'},
            'Compatible_L_P':{'isproduct':false,field:'Compatible_with_Header_Level_Promotion__c'},
            'PP_Quantity_P':{'isproduct':true,field:'Quantity_Per_Order__c'},
        },
        'Pool_of_Free_Goods_of_Customer_Choice':{
            'Line_L_P':{'isproduct':false,field:'Line_Level_Promot__c'},
            'Compatible_L_P':{'isproduct':false,field:'Compatible_with_Header_Level_Promotion__c'},
            'Free_Goods_T_Q':{'isproduct':false,field:'Free_Goods_Total_Quantity__c'},
            'PP_Max_Q_P':{'isproduct':true,field:'Max_Qty_Per_Order__c'},
            'PP_Quantity_Limit':{'isproduct':true,field:'Quantity_Limit_Whole__c'},
        },



        'Discount_Off_Single_Product':{
            'Line_L_P':{'isproduct':false,field:'Line_Level_Promot__c'},
            'Compatible_L_P':{'isproduct':false,field:'Compatible_with_Header_Level_Promotion__c'},
            'Discount_O':{'isproduct':false,field:'Discount_Off__c'},
            'Maximum_Q_P_O':{'isproduct':false,field:'Maximum_Quantity_Per_order__c'},
        },
        'Discount_Off_Multiple_Products':{
            'Line_L_P':{'isproduct':false,field:'Line_Level_Promot__c'},
            'Compatible_L_P':{'isproduct':false,field:'Compatible_with_Header_Level_Promotion__c'},
            'PP_Max_Q_P':{'isproduct':true,field:'Max_Qty_Per_Order__c'},
            'PP_Discount_O':{'isproduct':true,field:'Discount_Off__c'},
        },
        'Fixed_Amount_Single_Product':{
            'Line_L_P':{'isproduct':false,field:'Line_Level_Promot__c'},
            'Compatible_L_P':{'isproduct':false,field:'Compatible_with_Header_Level_Promotion__c'},
            'Fixed_P':{'isproduct':false,field:'Fixed_Price__c'},
            'Max_Q':{'isproduct':false,field:'Maximum_Quantity_Per_order__c'},
            'Quantity_Limit':{'isproduct':false,field:'Quantity_Limit_Whole__c'},
        },
        'Fixed_Amount_Multiple_Products':{
            'Line_L_P':{'isproduct':false,field:'Line_Level_Promot__c'},
            'Compatible_L_P':{'isproduct':false,field:'Compatible_with_Header_Level_Promotion__c'},
            'PP_Max_Q':{'isproduct':true,field:'Max_Qty_Per_Order__c'},
            'PP_Fixed_P':{'isproduct':true,field:'Fixed_Price__c'},
            'PP_Quantity_Limit':{'isproduct':true,field:'Quantity_Limit_Whole__c'},
        },
    }"/>

    <aura:attribute name="productList_View" type="List" default="[]"/>
    <aura:attribute name="mainProduct" type="Map" default="{}"/>
    <aura:attribute name="promotionGoods" type="List" default="[]"/>

    <!-- ========================================================= Promotion价格计算部分 End ================================================================= -->


    <!-- promotion code header level begin -->
    <aura:attribute name="promotionCodeHeaderLevel" type="String" default=""/>
    <aura:attribute name="promotionCodeHeaderData" type="List" default="[]"/>
    <!-- promotion code header level end -->

    <aura:attribute name="onSelect" type="Aura.action" default="{!c.onSelect}"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <div>
        <lightning:card class="mainContent">
            <div class="c-container" style="padding: 10px">
                <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
                <!-- 筛选条件 -->
                <lightning:layout horizontalAlign="space" verticalAlign="center">
                    <!-- Authorized Brand -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:combobox class="ccm_display"
                            name="authorizedBrand"
                            value="{!v.authorizedBrand}"
                            options="{!v.authorizedBrandOptions}"
                            label="{!$Label.c.CCM_AuthorizedBrand}"
                            disabled="true"
                            onchange="{!c.handleBrandChange}"
                        />
                    </lightning:layoutItem>
                    <!-- Payment Term -->
                    <lightning:layoutItem alignmentBump="right" size="3">
                        <lightning:combobox
                            name="paymentTerm"
                            value="{!v.paymentTerm}"
                            options="{!v.paymentTermOptions}"
                            label="{!$Label.c.Order_PaymentTerm}"
                            disabled="{!v.termFlag}"
                            aura:id="paymentTerm"
                            class="ccm_display field-required"
                        />
                        <div aura:id="paymentTerm-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                    </lightning:layoutItem>
                    <!-- Inco Term -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:combobox
                            name="incoTerm"
                            value="{!v.incoTerm}"
                            options="{!v.incoTermOptions}"
                            label="{!$Label.c.CCM_Incoterm}"
                            disabled="{!v.termFlag}"
                            aura:id="incoTerm"
                            class="ccm_display field-required"
                        />
                        <div aura:id="incoTerm-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                    </lightning:layoutItem>
                    <!-- Expected Delivery Date -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:input type="date" class="date-box-item slds-p-top--xx-small" label="{!$Label.c.CCM_ExpectedDeliveryDate}" value="{!v.expectedDeliveryDate}" onchange="{!c.setExpectedDeliveryDate}"/>
                    </lightning:layoutItem>
                </lightning:layout>
                <!-- 筛选条件 -->
                <lightning:layout horizontalAlign="space" verticalAlign="center">
                    <!-- Customer PO -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:input label="{!$Label.c.CCM_CustomerPO}" value="{!v.customerPO}" maxlength="255" onblur="{!c.checkPo}"/>
                    </lightning:layoutItem>
                    <!-- 占位 -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                    </lightning:layoutItem>
                    <!-- 占位 -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                    </lightning:layoutItem>
                    <!-- 占位 -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                    </lightning:layoutItem>
                </lightning:layout>
            </div>
            <!-- 表格 -->
            <div class="table-wrap">
                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols productTable" role="grid">
                    <thead class="thead-wrap">
                        <tr class="slds-line-height_reset">
                            <!-- Action -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 80px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_Action}">{!$Label.c.CCM_Action}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- No. -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 40px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_No}">{!$Label.c.CCM_No}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Product Description -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 240px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Model # -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 100px;' : 'width: 90px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                        <span class="header-title" title="{!$Label.c.CCM_ModelNo}">{!$Label.c.CCM_ModelNo}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Qty -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 180px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_Qty}">{!$Label.c.CCM_Qty}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Inventory -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 115px;' : 'width: 85px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.Order_Inventory}">{!$Label.c.Order_Inventory}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Request Date -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 150px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_ExpectedDeliveryDate}">{!$Label.c.CCM_ExpectedDeliveryDate}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Available Promotion -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 100px;' : 'width: 90px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_AvailablePromotion}">{!$Label.c.CCM_AvailablePromotion}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- List Price -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 95px;' : 'width: 85px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Standard Discount -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 85px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_StandardDiscount}">{!$Label.c.CCM_StandardDiscount}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Additional Discount -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 105px;' : 'width: 90px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_AdditionalDiscount}">{!$Label.c.CCM_AdditionalDiscount}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Unit Net Price -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 170px;' : 'width: 115px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_UnitNetPrice}">{!$Label.c.CCM_UnitNetPrice}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Total Net Price -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 145px;' : 'width: 120px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_TotalNetPrice}">{!$Label.c.CCM_TotalNetPrice}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Unit Per Pallet -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="{! ($Label.c.CCM_Translate == 'DE') ? 'width: 130px;' : 'width: 115px;'}">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_UnitPerPallet}">{!$Label.c.CCM_UnitPerPallet}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Recommended Accessories -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 125px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                        <span class="header-title" title="{!$Label.c.CCM_RecommendedAccessories}">{!$Label.c.CCM_RecommendedAccessories}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Related & Substitute -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 150px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                        <span class="header-title" title="{!$Label.c.CCM_RelatedSubstitute}">{!$Label.c.CCM_RelatedSubstitute}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- Remark -->
                            <!-- <aura:if isTrue="{! (v.userType == 'InsideSales')}"> -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 120px;">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="header-title" title="{!$Label.c.CCM_Comments}">{!$Label.c.CCM_Comments}</span>
                                        </div>
                                    </a>
                                </th>
                            <!-- </aura:if> -->
                            <!-- Promotion Code -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable w220i title-center" scope="col" style="">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title="{!$Label.c.CCM_PromotionCode}">{!$Label.c.CCM_PromotionCode}</span>
                                    </div>
                                </a>
                            </th>
                            <!-- delete -->
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable title-center" scope="col" style="width: 120px;">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="header-title" title=""></span>
                                    </div>
                                </a>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="tbody-wrap">
                        <aura:iteration items="{!v.productList}" var="productItem" indexVar="productIndex">
                            <aura:if isTrue="{!!productItem.Fallshortofthreshold}">
                                <tr class="{!productItem.type == 'threshold' ? 'slds-hint-parent bg_promotion_th index-wrap' : productItem.type == 'offering' ? 'slds-hint-parent bg_promotion_of index-wrap' : 'slds-hint-parent index-wrap'}" id="{!productIndex}" onmouseover="{!c.rowFocus}">
                                    <td scope="row" aria-selected="{!productItem.expanded}" data-expanded="{!productItem.expanded}" id="{!productIndex}" onclick="{!c.showShipToolList}">
                                        <div class="slds-truncate" title="">
                                            <aura:if isTrue="{!productItem.filteredProducts.length > 0}">
                                                <lightning:icon iconName="utility:chevrondown" size="xx-small" class="collapse_icn"/>
                                            </aura:if>
                                        </div>
                                    </td>
                                    <!-- No. -->
                                    <td scope="row">
                                        <div class="slds-truncate" title="">
                                            {!productIndex + 1}
                                        </div>
                                    </td>
                                    <!-- Product Description -->
                                    <td role="gridcell">
                                        <div class="lookup-parent-wrap">
                                            <!-- {!productItem.productDescription.Name} -->
                                            <aura:if isTrue="{!productItem.isPromotion}">
                                                {!productItem.productDescription}
                                                <aura:set attribute="else">
                                                    <aura:if isTrue="{!productItem.isPromotion}">
                                                        {!productItem.productDescription}
                                                        <aura:set attribute="else">
                                                            <c:CCM_Front_AutoPicklist
                                                                defaultOptions="{!v.productOptions}"
                                                                labelField="Item_Description_EN__c"
                                                                labelField1="Order_Model__c"
                                                                labelId="Id"
                                                                onSelect="{!c.onSelect}"
                                                                selectValue="{!productItem.productDescription}"
                                                            />
                                                        </aura:set>
                                                    </aura:if>
                                                </aura:set>
                                            </aura:if>
                                        </div>
                                    </td>
                                    <!-- Model # -->
                                    <td role="gridcell" title="Brand">
                                        <div class="slds-truncate">
                                            {!productItem.model}
                                        </div>
                                    </td>
                                    <!-- Qty -->
                                    <td role="gridcell" title="Qty">
                                        <aura:if isTrue="{!productItem.showPromotionType == 'FreeGoods'}">
                                            {!productItem.qty}
                                            <aura:set attribute="else">
                                                <div class="qty-wrap">
                                                        <lightning:button class="sub" label="-" onclick="{!c.handleSubQty}" disabled="{!productItem.type == 'offering' ? (productItem.qty == 0 ? true : false) : (productItem.qty == 1 ? true : false)}"></lightning:button>
                                                    <lightning:input type="number" label="" id="{!productIndex}" value="{!productItem.qty}" oncommit="{!c.handleInputQty}"/>
                                                    <lightning:button class="add" label="+" onclick="{!c.handleAddQty}" disabled="{!productItem.Max_Qty ? productItem.qty >= productItem.Max_Qty ? true : false : false}"></lightning:button>
                                                </div>
                                            </aura:set>
                                        </aura:if>
                                    </td>
                                    <!-- haibo: 根据record type判断展示 -->
                                    <!-- Inventory -->
                                    <td role="gridcell" title="Name">
                                        <aura:if isTrue="{!productItem.productRecordType != 'TLS_VK'}">
                                            <aura:if isTrue="{!not(and(productItem.productRecordType == 'MKT', productItem.filteredProducts.length > 0))}">
                                                <div class="slds-truncate icon-position-wrap">
                                                    <aura:if isTrue="{!productItem.inventory == 'Red'}">
                                                        <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                        <div class="slds-truncate clear-user-agent-styles icon-position-kit" style="{! ($Label.c.CCM_Translate == 'DE') ? 'left: 40px;' : ''}">
                                                            <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips3}"/>
                                                                <aura:set attribute="else">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips1}"/>
                                                                </aura:set>
                                                            </aura:if>
                                                        </div>
                                                    </aura:if>
                                                    <aura:if isTrue="{!productItem.inventory == 'Yellow'}">
                                                        <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                        <div class="slds-truncate clear-user-agent-styles icon-position-kit" style="{! ($Label.c.CCM_Translate == 'DE') ? 'left: 40px;' : ''}">
                                                            <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                <aura:if isTrue="{! (v.warehouse == 'China (DI)')}">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips4}"/>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips2}"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                                <aura:set attribute="else">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips5}"/>
                                                                </aura:set>
                                                            </aura:if>
                                                        </div>
                                                    </aura:if>
                                                    <aura:if isTrue="{!productItem.inventory == 'Green'}">
                                                        <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                        <div class="slds-truncate clear-user-agent-styles icon-position-kit" style="{! ($Label.c.CCM_Translate == 'DE') ? 'left: 40px;' : ''}">
                                                            <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                <aura:set attribute="else">
                                                                    <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                </aura:set>
                                                            </aura:if>
                                                        </div>
                                                    </aura:if>
                                                </div>
                                            </aura:if>
                                        </aura:if>
                                    </td>
                                    <!-- Request Date -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate">
                                            <lightning:input type="date" class="date-box-item slds-p-top--xx-small" value="{!productItem.requestDate}" onchange="{!c.handleRequestDate}"/>
                                        </div>
                                    </td>
                                    <!-- Available Promotion -->
                                    <td role="gridcell">
                                        <aura:if isTrue="{!productItem.isPromotion}">
                                            <aura:set attribute="else">
                                                <lightning:icon iconName="utility:info" size="x-small" onclick="{!c.handleAvailablePromotion}"/>
                                            </aura:set>
                                        </aura:if>
                                    </td>
                                    <!-- List Price -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate">
                                            <lightning:formattedNumber value="{!productItem.listPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                        </div>
                                    </td>
                                    <!-- Standard Discount -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate">
                                            {!productItem.standDiscount ? productItem.standDiscount + '%' : ''}
                                        </div>
                                    </td>
                                    <!-- Additional Discount -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate">
                                            {!productItem.discount ? productItem.discount + '%' : ''}
                                        </div>
                                    </td>
                                    <!-- Unit Net Price -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate">
                                            <lightning:formattedNumber value="{!productItem.unitNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                        </div>
                                    </td>
                                    <!-- Total Net Price -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate">
                                            <lightning:formattedNumber value="{!productItem.totalNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                        </div>
                                    </td>
                                    <!-- haibo: 根据record type判断展示 -->
                                    <!-- Unit Per Pallet -->
                                    <td role="gridcell">
                                        <aura:if isTrue="{!productItem.isPromotion || !productItem.productRecordType == 'TLS_VK'}">
                                            <aura:set attribute="else">
                                                <lightning:helptext class="icon-size" content="{!productItem.unitPerPallet ? productItem.unitPerPallet : $Label.c.CCM_CurrentlyNoInformation}"  onclick="{!c.clickUnitPerPallet}"/>
                                            </aura:set>
                                        </aura:if>
                                    </td>
                                    <!-- haibo: 根据record type判断展示 -->
                                    <!-- Recommended Accessories -->
                                    <td role="gridcell">
                                        <aura:if isTrue="{!productItem.isPromotion || !productItem.productRecordType == 'TLS_VK'}">
                                            <aura:set attribute="else">
                                                <lightning:icon iconName="utility:info" size="x-small" id="{!productItem}" onclick="{!c.handleRecommendedAccessories}"/>
                                            </aura:set>
                                        </aura:if>
                                    </td>
                                    <!-- Related & Substitute -->
                                    <td role="gridcell">
                                        <lightning:icon iconName="utility:info" size="x-small" id="{!productItem}" onclick="{!c.handleRelatedSubstitute}"/>
                                    </td>
                                    <!-- Remark -->
                                    <!-- <aura:if isTrue="{! (v.userType == 'InsideSales')}"> -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate">
                                            <lightning:input type="text" label="" value="{!productItem.remark}"/>
                                        </div>
                                    </td>
                                    <!-- </aura:if> -->
                                    <!-- Promotion Code -->
                                    <td role="gridcell" title="Name">
                                        <div class="slds-truncate" style="display: flex;">
                                            <lightning:input type="text" label="" class="{!productItem.type == 'MainProduct' ? '' : w100i}" disabled="{!productItem.promotioncode ? productItem.UsePromotion ? true : false : false}" value="{!productItem.promotioncode}"/>
                                            <aura:if isTrue="{!productItem.type == 'MainProduct'}">
                                                <lightning:button class="ml3" value="{!productIndex}" variant="brand" label="{!productItem.promotioncode ? productItem.UsePromotion ? $Label.c.CCM_Reset : $Label.c.CCM_Search : $Label.c.CCM_Search}" name="{!productItem.promotioncode ? productItem.UsePromotion ? $Label.c.CCM_Reset : $Label.c.CCM_Search : $Label.c.CCM_Search}" onclick="{!c.resetPromotion}"/>
                                            </aura:if>

                                        </div>
                                    </td>
                                    <!-- 删除 -->
                                    <td role="gridcell" style="text-align: center !important; z-index: 2 !important;">
                                        <aura:if isTrue="{!productItem.showPromotionType}">
                                        <span style="color: rgb(141, 141, 141);"><b><em>{!productItem.showPromotionType}</em></b></span>
                                            <aura:set attribute="else">
                                                <lightning:icon iconName="utility:delete" size="x-small" onclick="{!c.handleDelete}"/>
                                            </aura:set>
                                        </aura:if>
                                    </td>
                                </tr>
                            </aura:if>
                            <!-- product info -->
                            <aura:if isTrue="{!productItem.filteredProducts.length > 0}">
                                <tr aria-selected="{!productItem.expanded}" class="{!productItem.expanded ? 'slds-hint-parent show-flag' : 'slds-hint-parent hidden-flag'}" id="{!('tool' + productIndex)}">
                                    <td colspan="{! (v.userType == 'InsideSales') ? 18 : 17}" style="padding: 0;">
                                        <div class="tool-table-wrap">
                                            <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped tool-tabel" role="grid">
                                                <thead>
                                                    <tr class="slds-line-height_reset">
                                                        <!-- No. -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_No}">{!$Label.c.CCM_No}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Product Description -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Model #  -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_Model}">{!$Label.c.CCM_Model}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Recommended Accessories -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_RecommendedAccessories}">{!$Label.c.CCM_RecommendedAccessories}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Request Date -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_RequestDate}">{!$Label.c.CCM_RequestDate}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Qty -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_Qty}">{!$Label.c.CCM_Qty}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                            <!-- Unit Per Pallet -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_UnitPerPallet}">{!$Label.c.CCM_UnitPerPallet}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Inventory -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                    <span class="slds-truncate" title="{!$Label.c.Order_Inventory}">{!$Label.c.Order_Inventory}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- List Price -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Discount -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_Discount}">{!$Label.c.CCM_Discount}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Unit Net Price -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_UnitNetPrice}">{!$Label.c.CCM_UnitNetPrice}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- Total Net Price -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title="{!$Label.c.CCM_TotalNetPrice}">{!$Label.c.CCM_TotalNetPrice}</span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                        <!-- 占位 -->
                                                        <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                            <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                    <span class="slds-truncate" title=""></span>
                                                                </div>
                                                            </a>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <aura:iteration items="{!productItem.filteredProducts}" var="toolItem" indexVar="toolIndex">
                                                        <tr aria-selected="false" class="slds-hint-parent">
                                                            <!-- line -->
                                                            <td scope="row">
                                                                <div class="slds-truncate" title="">
                                                                    {!productIndex + 1}.{!toolIndex + 1}
                                                                </div>
                                                            </td>
                                                            <!-- Product Description -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate clear-user-agent-styles">
                                                                    {!toolItem.productDescription}
                                                                </div>
                                                            </td>
                                                            <!-- Model #  -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate clear-user-agent-styles">
                                                                    <span>{!toolItem.model}</span>
                                                                </div>
                                                            </td>
                                                            <!-- Recommended Accessories -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate clear-user-agent-styles">
                                                                    <aura:if isTrue="{!productItem.productRecordType == 'TLS_VK'}">
                                                                        <lightning:icon iconName="utility:info" size="x-small" id="{!toolItem}" onclick="{!c.handleRecommendedAccessories}"/>
                                                                    </aura:if>
                                                                </div>
                                                            </td>
                                                            <!-- Request Date -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate clear-user-agent-styles">
                                                                    <span>{!toolItem.requestDate}</span>
                                                                </div>
                                                            </td>
                                                            <!-- Qty -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate clear-user-agent-styles">
                                                                    <span>{!toolItem.qty}</span>
                                                                </div>
                                                            </td>
                                                            <!-- Unit Per Pallet -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate clear-user-agent-styles">
                                                                    <aura:if isTrue="{!productItem.productRecordType == 'TLS_VK'}">
                                                                        <lightning:helptext class="icon-size" content="{!toolItem.unitPerPallet ? toolItem.unitPerPallet + ' ' + $Label.c.CCM_PiecesPerPallet : $Label.c.CCM_CurrentlyNoInformation}"/>
                                                                    </aura:if>
                                                                </div>
                                                            </td>
                                                            <!-- Inventory -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate icon-position-wrap" style="text-align: center;">
                                                                    <div class="slds-truncate icon-position-wrap">
                                                                        <aura:if isTrue="{!or(productItem.productRecordType == 'TLS_VK', and(productItem.productRecordType == 'MKT', productItem.filteredProducts.length>0))}">
                                                                            <aura:if isTrue="{!toolItem.inventory == 'Red'}">
                                                                                <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                                                <div class="slds-truncate clear-user-agent-styles icon-position-tool" style="{! ($Label.c.CCM_Translate == 'DE') ? 'left: 62px;' : ''}">
                                                                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                        <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips3}"/>
                                                                                        <aura:set attribute="else">
                                                                                            <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips1}"/>
                                                                                        </aura:set>
                                                                                    </aura:if>
                                                                                </div>
                                                                            </aura:if>
                                                                            <aura:if isTrue="{!toolItem.inventory == 'Yellow'}">
                                                                                <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                                                <div class="slds-truncate clear-user-agent-styles icon-position-tool" style="{! ($Label.c.CCM_Translate == 'DE') ? 'left: 62px;' : ''}">
                                                                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                        <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips4}"/>
                                                                                        <aura:set attribute="else">
                                                                                            <lightning:helptext class="icon-size" content="{!$Label.c.CCM_TrafficLightIndicationTips2}"/>
                                                                                        </aura:set>
                                                                                    </aura:if>
                                                                                </div>
                                                                            </aura:if>
                                                                            <aura:if isTrue="{!toolItem.inventory == 'Green'}">
                                                                                <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                                                <div class="slds-truncate clear-user-agent-styles icon-position-tool" style="{! ($Label.c.CCM_Translate == 'DE') ? 'left: 62px;' : ''}">
                                                                                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                        <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                                        <aura:set attribute="else">
                                                                                            <lightning:helptext class="icon-size" content="{!$Label.c.CCM_CCM_TrafficLightIndicationTips6}"/>
                                                                                        </aura:set>
                                                                                    </aura:if>
                                                                                </div>
                                                                            </aura:if>
                                                                        </aura:if>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <!-- List Price -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate">
                                                                    <lightning:formattedNumber value="{!toolItem.listPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                </div>
                                                            </td>
                                                            <!-- Discount -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate clear-user-agent-styles" >
                                                                    <span>{!toolItem.discount}</span>
                                                                </div>
                                                            </td>
                                                            <!-- Unit Net Price -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate">
                                                                    <lightning:formattedNumber value="{!toolItem.unitNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                </div>
                                                            </td>
                                                            <!-- Total Net Price -->
                                                            <td role="gridcell">
                                                                <div class="slds-truncate">
                                                                <lightning:formattedNumber value="{!toolItem.totalNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                <!-- calvin add -->
                                                                <!-- <lightning:formattedNumber value="{!toolItem.UnitNetPrice * toolItem.OrderQty}" style="currency" currencyCode="{!v.currencySymbol}"/> -->
                                                                <!-- calvin add -->
                                                                </div>
                                                            </td>
                                                            <!-- 占位 -->
                                                            <td role="gridcell">
                                                            </td>
                                                        </tr>
                                                    </aura:iteration>
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                    </td>
                                </tr>
                            </aura:if>
                        </aura:iteration>
                    </tbody>
                </table>
            </div>
            <div class="slds-m-vertical_small slds-m-horizontal_medium slds-border_bottom">
                <lightning:layout>
                    <div class="c-container" style="padding: 10px">
                        <lightning:layoutItem alignmentBump="right">
                            <lightning:button variant="brand-outline" label="{!$Label.c.CCM_AddItem}" iconName="utility:add" iconPosition="left" onclick="{!c.addItem}"/>
                        </lightning:layoutItem>
                    </div>
                </lightning:layout> 
                                <!-- header level promotion code begin-->
                <div class="slds-truncate" style="display: flex;position: relative;overflow: visible;">
                    <lightning:input type="text" label="" onchange="{!c.searchHeaderPromotionByCode}"/>
                    <lightning:button class="apply-btn mrl20" variant="brand" label="{!$Label.c.CCM_ApplyPromotion}" onclick="{!c.applyHeaderPromotionCode}"/>
                    <div style="position: absolute;left:12px;top:45px;">
                        <aura:iteration items="{!v.headerDiscountList}" var="item" indexVar="index">
                            <div style="font-size: 14px; margin-top: 14px;">{!item.promotionCode} : Discount : {!item.discountoff || 0} %
                                <lightning:button class="h27 ml17" disabled="{!item.line_headerDisconunt ? true : false}" variant="brand-outline" label="{!$Label.c.CCM_Remove}" name="{!item.promotionCode}" onclick="{!c.RemoveHeaderDiscount}"/>
                            </div>
                        </aura:iteration>
                    </div>
                </div>
                <!-- header level promotion code end-->
                <div class="slds-grid total-wrap"> 
                    <div class="slds-text-align--right">                     
                        <p><strong>{!$Label.c.CCM_TotalValue}:</strong></p>
                        <p><strong>{!$Label.c.CCM_HeaderDiscount}:</strong></p>
                        <p><strong>{!$Label.c.CCM_HeaderDiscountAmount}:</strong></p>
                        <p><strong>{!$Label.c.CCM_TotalValueNet}:</strong></p>
                        <p><strong>{!$Label.c.CCM_FreightCost}:</strong></p>
                        <p><strong>{!$Label.c.CCM_InsuranceFee}:</strong></p>
                        <p><strong>{!$Label.c.CCM_OtherFee}:</strong></p>
                        <p><strong>{!$Label.c.CCM_VAT}:</strong></p>
                        <hr style="margin: 5px; 0;"/>
                        <p><strong>{!$Label.c.CCM_TotalDueAmount}:</strong></p>
                    </div>
                    <div class="total-value-wrap">
                        <!-- TODO: 字段变量 -->
                        <p><strong><lightning:formattedNumber value="{!v.totalValue}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <p class="slds-truncate"><strong>{!v.headerDiscount}%</strong></p>
                        <p><strong><lightning:formattedNumber value="{!v.headerDiscountAmount}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <p><strong><lightning:formattedNumber value="{!v.totalValueNet}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <p><strong><lightning:formattedNumber value="{!v.freightCost}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <!-- <p><strong><lightning:formattedNumber value="{!v.insuranceFee}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <p><strong><lightning:formattedNumber value="{!v.otherFee}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <p><strong><lightning:formattedNumber value="{!v.VAT}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p> -->
                        <p><strong><lightning:formattedNumber value="" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <p><strong><lightning:formattedNumber value="" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <p><strong><lightning:formattedNumber value="" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                        <hr style="margin: 5px; 0"/>
                        <p><strong><lightning:formattedNumber value="{!v.totalDueAmount}" style="currency" currencyCode="{!v.currencySymbol}"/>&nbsp;</strong></p>
                    </div>
                </div>
            </div>
            <aura:set attribute="footer">
                <div class="footer-wrap">
                    <lightning:button class="previous-btn" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancel}" />
                    <!-- <lightning:button class="previous-btn" variant="brand-outline" label="Previous" title="Previous" onclick="{!c.previousStep}" /> -->
                    <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Next}" onclick="{!c.nextStep}" />
                </div>
            </aura:set>
        </lightning:card>
        <!-- Available Promotion -->
        <aura:if isTrue="{!v.promotionModalFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_AvailablePromotions}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <aura:iteration items="{!v.promotionList}" var="promotionItem" indexVar="promotionIndex">
                                    <div class="promotion-item-wrap">
                                        <div class="apply-btn-wrap">
                                            <lightning:button value="{!promotionIndex}" class="{!promotionItem.disable ? 'apply-btn BtnDisabl' : 'apply-btn'}" disabled="{!promotionItem.disable}" variant="brand" label="Apply" onclick="{!c.applyEvent}"/>
                                            <div class="promotion-type">
                                                {!promotionItem.promotion.Name}
                                            </div>
                                        </div>
                                        <div class="detail-wrap">
                                            <p class="promotion-label">
                                                {!$Label.c.CCM_PromotionDetail}:
                                                <span class="promotion-value">{!promotionItem.promotion.Promotion_short_describe__c}</span>
                                            </p>
                                        </div>
                                        <div class="code-wrap">
                                            <div class="promotion-label">
                                                {!$Label.c.CCM_PromotionCode}:
                                                <a href="#" class="promotion-value">{!promotionItem.promotion.Promotion_Code_For_External__c}</a>
                                            </div>
                                        </div>
                                    </div>
                                </aura:iteration>
                            </div>
                        </div>
                        <footer class="slds-modal__footer">
                            <div class="footer-wrap">
                                <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancelEvent}"/>
                            </div>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
        <!-- 二级选择Product弹框 -->
        <aura:if isTrue="{!v.bogoFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!v.pupUp_ProductSelect.name}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <!-- Promotion Detail -->
                                <c:CCM_Section title="Promotion Detail" expandable="true">
                                    <div class="promotion-detail-wrap">
                                        {!v.pupUp_ProductSelect.detail}
                                    </div>
                                </c:CCM_Section>
                                <!-- Free Goods List -->
                                <c:CCM_Section title="{!v.pupUp_ProductSelect.name}" expandable="true">
                                    <div class="goods-list-wrap">
                                        <div class="good-select-wrap">
                                            <div style="margin-bottom: 10px;">{!v.pupUp_ProductSelect.tips}</div>
                                            <lightning:input
                                                aura:id="enter-search"
                                                name="enter-search"
                                                placeholder="Enter Key Words"
                                                variant="label-hidden"
                                                onblur="{!c.handleFilterGoods}"
                                                onchange="{!c.handleFilterGoods}"
                                            />
                                        </div>
                                    </div>
                                </c:CCM_Section>
                            </div>
                        </div>
                        <!-- 产品选择 -->
                        <div style="background-color: white;">
                        <div style="border:1px solid rgb(121, 121, 121);margin:10px;padding:20px 0;border-radius: 5px;">
                            <p style="padding: 0 20px;">
                                            <aura:iteration items="{!v.pupUp_ProductSelect.thead}" var="ProductSelect_thead">
                                    <span class="{!ProductSelect_thead == 'No.' ? 'table_st w60' : 'table_st'}" style="font-size: 15px;font-weight: bolder;">{!ProductSelect_thead}</span>
                                            </aura:iteration>
                            </p>
                                <aura:iteration items="{!v.pupUp_ProductSelect.list}" var="ProductSelect_List" indexVar="indexProductSelect_List">
<aura:if isTrue="{!!ProductSelect_List.hidden}">
                                    <p class="{!ProductSelect_List.select ? 'clickedProduct' : ''}" onclick="{!c.onSelect_Product}" index="{!indexProductSelect_List}" style="padding:10px 20px">
                                        <span class="table_st w60">{!indexProductSelect_List + 1}</span>
                                        <span class="table_st">{!ProductSelect_List.productDescription}</span>
                                        <span class="table_st">{!ProductSelect_List.model}</span>
                                        <aura:if isTrue="{!ProductSelect_List.type == 'offering'}">
                                            <lightning:input
                                            style="width: 70px;display: inline-block;;"
                                            name="{!indexProductSelect_List}"
                                            variant="label-hidden"
                                            value="{!ProductSelect_List.qty}"/>
                                        </aura:if>
                                        </p>
</aura:if>
                                </aura:iteration>
                            </div>
                        </div>
                        <footer class="slds-modal__footer">
                            <lightning:button class="" variant="brand" label="Finish" onclick="{!c.bogoFinishEvent}"/>
                            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancelEvent}"/>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
        <!-- 配件弹框 -->
        <aura:if isTrue="{!v.accessoriesModalFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 70rem !important; max-width: 80rem !important; max-height: 60rem !important; transform: translate(0%, 0%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.accessoriesCancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_AccessoriesList}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <c:CCM_DataTable columns="{!v.accessoriesColumns}" data="{!v.accessoriesData}"/>
                            </div>
                        </div>
                        <footer class="slds-modal__footer footer-btn-wrap">
                            <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" onclick="{!c.accessoriesCancelEvent}"/>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
        <!-- unit per Pallet弹框 -->
        <aura:if isTrue="{!v.unitPerPalletModalFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 50rem !important; max-width: 80rem !important; max-height: 60rem !important; transform: translate(0%, 0%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.unitPerPalletCancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_UnitPerPallet}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <div class="pallet-table-wrap">
                                    <table class="slds-table slds-table_cell-buffer slds-no-row-hover slds-table_bordered" aria-label="Example table of Opportunities with no row hovers">
                                        <thead>
                                            <tr class="slds-line-height_reset">
                                                <th class="table-header" scope="col">
                                                    <div class="slds-truncate"></div>
                                                </th>
                                                <th class="table-header" scope="col">
                                                    <div class="slds-truncate" title="Unit Per">{!$Label.c.CCM_Unit} (PC)</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="slds-hint-parent">
                                                <td class="table-header" scope="col">
                                                    <div class="slds-truncate" title="Contact">{!$Label.c.CCM_RetailPackage}</div>
                                                </td>
                                                <td>
                                                    <div class="slds-truncate">
                                                        <lightning:formattedNumber value="{!v.palletInfo.retailUnit}"></lightning:formattedNumber>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr class="slds-hint-parent">
                                                <td class="table-header" scope="col">
                                                    <div class="slds-truncate" title="Contact">{!$Label.c.CCM_InnerCarton}</div>
                                                </td>
                                                <td>
                                                    <div class="slds-truncate">
                                                        <lightning:formattedNumber value="{!v.palletInfo.Units_Per_Inner_BOX_EA__c}"></lightning:formattedNumber>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr class="slds-hint-parent">
                                                <td class="table-header" scope="col">
                                                    <div class="slds-truncate" title="Master Carton">{!$Label.c.CCM_MasterCarton}</div>
                                                </td>
                                                <td>
                                                    <div class="slds-truncate">
                                                        <lightning:formattedNumber value="{!v.palletInfo.Units_Per_Master_Carton_EA__c}"></lightning:formattedNumber>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr class="slds-hint-parent">
                                                <td class="table-header" scope="col">
                                                    <div class="slds-truncate" title="Master Carton">{!$Label.c.CCM_Pallet}</div>
                                                </td>
                                                <td>
                                                    <div class="slds-truncate">
                                                        <lightning:formattedNumber value="{!v.palletInfo.Units_Per_Pallet_EA__c}"></lightning:formattedNumber>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <footer class="slds-modal__footer footer-btn-wrap">
                            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.unitPerPalletCancelEvent}"/>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
        <!-- 关联产品弹框 -->
        <aura:if isTrue="{!v.relatedSubstituteModalFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 70rem !important; max-width: 80rem !important; max-height: 60rem !important; transform: translate(0%, 0%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.relatedSubstituteCancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_AccessoriesList}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <lightning:tabset>
                                    <lightning:tab label="Related Product">
                                        <c:CCM_DataTable columns="{!v.accessoriesColumns}" data="{!v.relatedData}"/>
                                    </lightning:tab>
                                    <lightning:tab label="Substitute Product">
                                        <c:CCM_DataTable columns="{!v.accessoriesColumns}" data="{!v.substituteData}"/>
                                    </lightning:tab>
                                </lightning:tabset>
                            </div>
                        </div>
                        <footer class="slds-modal__footer footer-btn-wrap">
                            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.relatedSubstituteCancelEvent}"/>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
    </div>
</aura:component>