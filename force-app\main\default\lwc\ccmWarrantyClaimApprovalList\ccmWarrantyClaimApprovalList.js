import { LightningElement, wire } from 'lwc';
import getAllClaims from '@salesforce/apex/CCM_WarrantyClaimApprovalListCtrl.getWarrantyClaims'
import RedLight from '@salesforce/resourceUrl/RedLight';
import YellowLight from '@salesforce/resourceUrl/YellowLight';
import GreenLight from '@salesforce/resourceUrl/GreenLight';
import OrangeLight from '@salesforce/resourceUrl/OrangeLight';
import Id from '@salesforce/user/Id';
import { getRecord } from 'lightning/uiRecordApi';
import NAME_FIELD from '@salesforce/schema/User.Name';

export default class CcmWarrantyClaimApprovalList extends LightningElement {

    // Current user info
    userId = Id;
    currentUserName = '';

    //Light Status
    RedLight = RedLight;
    YellowLight = YellowLight;
    GreenLight = GreenLight;
    OrangeLight = OrangeLight;

    columns = [
        // {label: 'id', fieldName: 'id', type: 'text', hideLabel: true},
        // {label: 'Warranty Claim Name', fieldName: 'name', type: 'text'},
        {label: 'Warranty Claim Name', fieldName: 'recordLink', type: 'url', typeAttributes:{label: { fieldName: 'name' }, target: '_blank'}},
        {label: 'Light', type: 'customImage', fieldName: 'imageUrl', typeAttributes:{imageUrl: { fieldName: 'imageUrl' }}},
        {label: 'Serial Number', fieldName: 'serialNumber', type: 'text', sortable: true},
        {label: 'Dealer Name', fieldName: 'dealerName', type: 'text', sortable: true},
        // {label: 'Light', fieldName: 'light', type: 'text'},
        {label: 'Model Number', fieldName: 'modelNumber', type: 'text', sortable: true},
        {label: 'Claim Date', fieldName: 'claimDate', type: 'date', sortable: true},
        {label: 'Service Option', fieldName: 'serviceOption', type: 'text', sortable: true},
        {label: 'Replacement Option', fieldName: 'replacementOption', type: 'text', sortable: true},
        {label: 'Repair Type', fieldName: 'repairType', type: 'text', sortable: true},
        {label: 'Create Date', fieldName: 'createdDate', type: 'date', sortable: true},
        {label: 'Approver', fieldName: 'approver', type: 'text', sortable: true},
    ];

    // Data properties
    values = [];
    filteredValues = [];

    // Filter properties
    selectedApprover = '';
    approverOptions = [];

    // Wire to get current user info
    @wire(getRecord, { recordId: '$userId', fields: [NAME_FIELD] })
    wiredUser({ error, data }) {
        if (data) {
            this.currentUserName = data.fields.Name.value;
        } else if (error) {
            console.error('Error retrieving user data:', error);
        }
    }
    
    connectedCallback() {
        this.initApprovalList();
    }

    initApprovalList() {
        getAllClaims().then(res=>{
            if(res) {
                let resJson = JSON.parse(res);
                resJson.forEach(item=>{
                    if(item.light === 'Green') {
                        item.imageUrl = GreenLight;
                    }
                    else if(item.light === 'Red') {
                        item.imageUrl = RedLight;
                    }
                    else if(item.light === 'Orange') {
                        item.imageUrl = OrangeLight;
                    }
                    else if(item.light === 'Yellow') {
                        item.imageUrl = YellowLight;
                    }
                });
                this.values = resJson;
                this.buildApproverOptions();
                this.applyFilter();
                this.template.querySelector('c-ccm-mass-approval-list').hideLoading();
            }
        }).catch(error=>{
            console.log(error);
        });
    }

    // Build approver options for the filter
    buildApproverOptions() {
        // const approvers = new Set();
        // this.values.forEach(item => {
        //     if (item.approver) {
        //         approvers.add(item.approver);
        //     }
        // });

        this.approverOptions = [
            { label: 'All', value: '' },
            { label: 'My Approvals', value: 'MY_APPROVALS' }
        ];

        // Add unique approvers to options
        // approvers.forEach(approver => {
        //     this.approverOptions.push({
        //         label: approver,
        //         value: approver
        //     });
        // });
    }

    // Apply filter based on selected approver
    applyFilter() {
        if (!this.selectedApprover) {
            // Show all records
            this.filteredValues = [...this.values];
        } else if (this.selectedApprover === 'MY_APPROVALS') {
            // Show only current user's approvals
            this.filteredValues = this.values.filter(item =>
                item.approver === this.currentUserName
            );
        } else {
            // Show records for selected approver
            this.filteredValues = this.values.filter(item =>
                item.approver === this.selectedApprover
            );
        }
    }

    // Handle approver filter change
    handleApproverFilterChange(event) {
        this.selectedApprover = event.detail.value;
        this.applyFilter();
    }

    // Handle reset filter
    handleResetFilter() {
        this.selectedApprover = '';
        this.applyFilter();
    }

    handleApprove() {
        this.initApprovalList();
    }

    handleReject() {
        this.initApprovalList();
    }
}