@isTest
public class ContactNewRecordTypeControllerTest {
    
    @TestSetup
    static void setupTestData() {
        // 创建测试Account
        Account testAccount = new Account(
            Name = 'Test Account'
        );
        insert testAccount;
    }
    
    @isTest
    static void testGetAvailableRecordTypesFromAccountWithInsideSalesManager() {
        // 查找Inside Sales Manager Profile，如果不存在则跳过测试
        List<Profile> insideSalesManagerProfiles = [
            SELECT Id FROM Profile
            WHERE Name = 'Inside Sales Manager'
            LIMIT 1
        ];

        if (insideSalesManagerProfiles.isEmpty()) {
            // 如果Profile不存在，跳过此测试
            return;
        }

        User testUser = new User(
            Alias = 'test',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'Test',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = insideSalesManagerProfiles[0].Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserName = 'test' + System.currentTimeMillis() + '@example.com'
        );
        insert testUser;
        
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        System.runAs(testUser) {
            Test.startTest();
            List<ContactNewRecordTypeController.RecordTypeOption> result = 
                ContactNewRecordTypeController.getAvailableRecordTypes(true);
            Test.stopTest();
            
            // 验证返回了Record Types
            System.assertNotEquals(0, result.size(), 'Should return record types');
            
            // 验证包含受限的Record Types（因为用户是Inside Sales Manager）
            Set<String> returnedDeveloperNames = new Set<String>();
            for (ContactNewRecordTypeController.RecordTypeOption option : result) {
                returnedDeveloperNames.add(option.developerName);
            }
            
            // 应该包含受限的Record Types
            System.assert(returnedDeveloperNames.contains('Acknowledgment_Contact') || 
                         returnedDeveloperNames.contains('Invoice_Credit_Contact') || 
                         returnedDeveloperNames.contains('Dunning_Contact'), 
                         'Should include restricted record types for Inside Sales Manager');
        }
    }
    
    @isTest
    static void testGetAvailableRecordTypesFromAccountWithOtherProfile() {
        // 使用系统管理员Profile（这个Profile肯定存在）
        Profile systemAdminProfile = [
            SELECT Id FROM Profile
            WHERE Name = 'System Administrator'
            LIMIT 1
        ];

        User testUser = new User(
            Alias = 'test2',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'Test2',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = systemAdminProfile.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserName = 'test2' + System.currentTimeMillis() + '@example.com'
        );
        insert testUser;
        
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        System.runAs(testUser) {
            Test.startTest();
            List<ContactNewRecordTypeController.RecordTypeOption> result = 
                ContactNewRecordTypeController.getAvailableRecordTypes(true);
            Test.stopTest();
            
            // 验证返回了Record Types
            System.assertNotEquals(0, result.size(), 'Should return record types');
            
            // 验证不包含受限的Record Types（因为用户不是Inside Sales Manager/Rep且从Account创建）
            Set<String> returnedDeveloperNames = new Set<String>();
            for (ContactNewRecordTypeController.RecordTypeOption option : result) {
                returnedDeveloperNames.add(option.developerName);
            }
            
            // 不应该包含受限的Record Types
            System.assert(!returnedDeveloperNames.contains('Acknowledgment_Contact') && 
                         !returnedDeveloperNames.contains('Invoice_Credit_Contact') && 
                         !returnedDeveloperNames.contains('Dunning_Contact'), 
                         'Should not include restricted record types for other profiles when creating from Account');
        }
    }
    
    @isTest
    static void testRecordTypeOptionClass() {
        Test.startTest();
        ContactNewRecordTypeController.RecordTypeOption option = 
            new ContactNewRecordTypeController.RecordTypeOption(
                '012000000000000AAA',
                'Test Record Type',
                'Test_Record_Type',
                'Test Description'
            );
        Test.stopTest();
        
        System.assertEquals('012000000000000AAA', option.value);
        System.assertEquals('Test Record Type', option.label);
        System.assertEquals('Test_Record_Type', option.developerName);
        System.assertEquals('Test Description', option.description);
    }
}
