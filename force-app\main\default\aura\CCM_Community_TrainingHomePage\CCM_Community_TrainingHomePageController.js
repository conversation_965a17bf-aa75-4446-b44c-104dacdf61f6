({
    init:function(component, event, helper){
        // 设置Training Crouse表头
        const userColumns= [
            {label: $A.get("$Label.c.CCM_No"), fieldName: 'no'},
            {label: $A.get("$Label.c.CCM_CourseName"), fieldName:'courseName'},
            {label: $A.get("$Label.c.CCM_CourseDescription"), fieldName:'courseDescription'},
            {
                type: "lightning:button",
                attributes: {
                    label: $A.get("$Label.c.CCM_Register"),
                    variant: "brand",
                    value: "${id}",
                    onclick: component.getReference("c.handleButtonClick")
                }
            },
        ];
        // 设置Training History表头
        const HistoryColumns= [
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '80px',
                tdStyle:'text-align: left',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${row}",
                        variant:"bare",
                        showTitle:false,
                        iconName:"utility:preview",
                        alternativeText:"View",
                        onclick: component.getReference('c.doView')
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${row}",
                        variant:"bare",
                        showTitle:false,
                        iconName:"utility:edit",
                        alternativeText:"Edit",
                        onclick: component.getReference('c.doEdit'),
                        class: "${editStyleCss}",
                    }
                },
            ]
            },
            {label: $A.get("$Label.c.CCM_CourseRequest"), fieldName:'registerNo'},
            {label: $A.get("$Label.c.CCM_OrderNumber"), fieldName:''},
            {label: $A.get("$Label.c.CCM_OrderType"), fieldName:'orderType'},
            {label: $A.get("$Label.c.Order_OrderStatus"), fieldName:'status'},
            {label: $A.get("$Label.c.Order_TotalDueAmount"), fieldName:'totalDueAmount'},
            {label: $A.get("$Label.c.CCM_OrderCreatedBy"), fieldName: 'createdBy'},
        ];
        helper.queryRegisterList(component)
        // 获取课程数据
        helper.getTrainingCrouseList(component)
        component.set('v.userColumns',userColumns);
        component.set('v.HistoryColumns',HistoryColumns);
    },
    handleButtonClick: function(component, event){
        let url = '/s/coursedetail?recordId=' + event.target.name
        window.open(url, '_self');
    },
    doView : function(component, event, helper){
        let rowInfo = event.getSource().get('v.value');
        let url = '';
        console.log(rowInfo,'rowInfo--------------');
        // 判断是否同步后类型
        url = window.location.origin + '/s/training-detail-page?0.recordId=' + rowInfo.Id;
        window.open(url, '_self');
    },
    doEdit : function(component, event, helper){
        let rowInfo = event.getSource().get('v.value');
       
        console.log(rowInfo,'rowInfo--------------');
        // 判断是否同步后类型
        let url = window.location.origin + '/s/coursedetail?recordinfo=' + rowInfo.Id;
       // url = window.location.origin + '/s/training-detail-page?0.recordId=' + rowInfo.Id;
        window.open(url, '_self');
    },
    
    // changepage
    handleChangepage : function(component, event, helper){
        helper.gettraininglist(component)
    },
})