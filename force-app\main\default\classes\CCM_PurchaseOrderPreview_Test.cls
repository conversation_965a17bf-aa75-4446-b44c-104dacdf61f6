@isTest
public with sharing class CCM_PurchaseOrderPreview_Test{
    @testSetup
    static void setup(){
        // Create test data
        Account acc = new Account(Name = 'Test Account', AccountNumber = 'E7123', RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID);
        insert acc;

        List<Product2> lstProducts = new List<Product2>();
        Product2 product1 = new Product2();
        product1.Source__c = 'EBS';
        product1.Name = 'BH1001';
        product1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        product1.ExternalId__c = 'BH1001';
        product1.Order_Model__c = 'BH1001';
        product1.Master_Product__c = 'BH1001';
        product1.Item_Description_DE__c = 'DEDescription';
        product1.Item_Description_EN__c = 'ENDescription';
        product1.Units_Per_Pallet_EA__c = 6;
        product1.Units_Per_Inner_BOX_EA__c = 5;
        product1.Units_Per_Master_Carton_EA__c = 7;
        insert product1;
        Inventory__c testInventory = new Inventory__c(Product__c = product1.Id, Available_QTY__c = 5);
        testInventory.Sub_Inventory_Code__c = 'EGD01';
        testInventory.RecordTypeId = Schema.SObjectType.Inventory__c.getRecordTypeInfosByDeveloperName().get('All_Inventory').getRecordTypeId();
        insert testInventory;
        Product2 prodKit1 = new Product2();
        prodKit1.Source__c = 'EBS';
        prodKit1.Name = 'CS1614E';
        prodKit1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_VK').getRecordTypeId();
        prodKit1.ExternalId__c = 'CS1614E';
        prodKit1.Order_Model__c = 'CS1614E';
        prodKit1.Master_Product__c = 'CS1614E';
        prodKit1.Item_Description_DE__c = 'DEDescription';
        prodKit1.Item_Description_EN__c = 'ENDescription';
        prodKit1.Units_Per_Pallet_EA__c = 6;
        prodKit1.Units_Per_Inner_BOX_EA__c = 5;
        prodKit1.Units_Per_Master_Carton_EA__c = 7;
        insert prodKit1;
        Kit_Item__c kiProductAcc1 = new Kit_Item__c();
        kiProductAcc1.RecordTypeId = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get('Kits_and_Products').getRecordTypeId();
        kiProductAcc1.Kit__c = prodKit1.Id;
        kiProductAcc1.VK_Product__c = product1.Id;
        kiProductAcc1.Quantity__c = 2;
        kiProductAcc1.ReferenceId__c = prodKit1.Order_Model__c + '-' + product1.Order_Model__c;
        insert kiProductAcc1;
        Pricebook2 pricebook = new Pricebook2(Name = 'Test Pricebook');
        insert pricebook;
        Pricebook_Entry__c priceEntry = new Pricebook_Entry__c(PriceBook__c = pricebook.Id);
        priceEntry.start_Date__c = Date.today().addDays(-7);
        priceEntry.End_Date__c = Date.today().addDays(7);
        priceEntry.UnitPrice__c = 10;
        priceEntry.Product__c = prodKit1.Id;
        priceEntry.PriceBook__c = pricebook.Id;
        priceEntry.CurrencyIsoCode = 'EUR';
        priceEntry.Start_Date__c = Date.today().addDays(-7);
        priceEntry.End_Date__c = Date.today().addDays(7);
        insert priceEntry;
        Sales_Program__c salesP = new Sales_Program__c(Name = 'Test Sales Program', Brands__c = 'Test Brand', Customer__c = acc.Id);
        salesP.Price_Book__c = pricebook.Id;
        insert salesP;
        Purchase_Order__c objPurchaseOrder = new Purchase_Order__c();
        objPurchaseOrder.ORG_ID__c = 'EEG';
        objPurchaseOrder.Warehouse__c = 'Germany (DSV)';
        objPurchaseOrder.Customer__c = acc.Id;
        objPurchaseOrder.WearHouse_In_EBS__c = 'EEG';
        objPurchaseOrder.Order_Type__c = CCM_Constants.DSVR;
        objPurchaseOrder.Customer_PO_Num__c = 'P001';
        objPurchaseOrder.Pricing_Date__c = Date.today().addDays(-5);
        objPurchaseOrder.Product__c = product1.Id;
        objPurchaseOrder.RecordTypeId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get('Regular_Order').getRecordTypeId();
        insert objPurchaseOrder;
        Purchase_Order_Item__c objPurchaseOrderItem = new Purchase_Order_Item__c();
        objPurchaseOrderItem.Purchase_Order__c = objPurchaseOrder.Id;
        objPurchaseOrderItem.Product__c = prodKit1.Id;
        objPurchaseOrderItem.Quantity__c = 2;
        insert objPurchaseOrderItem;
        Order order = new Order(Name = 'test order', Customer_EU__c = acc.Id, AccountId = acc.Id, EffectiveDate = Date.today(), Status = 'Draft');
        insert order;
        List<Order_Item__c> testOrderItems = new List<Order_Item__c>();
        for (Integer i = 1; i <= 5; i++){
            Order_Item__c testOrderItem = new Order_Item__c(Order__c = order.Id, Product__c = product1.Id, Pricing_Date__c = Date.today(), Order_Quantity__c = 10, Inventory__c = 'Red');
            testOrderItems.add(testOrderItem);
        }
        insert testOrderItems;
        Recommended_Accessories__c ra = new Recommended_Accessories__c(Access_Model__c = 'BH1001', Masert_Model__c = 'CS1614E');
        insert ra;
        Alert_Message__c am = new Alert_Message__c();
        am.Start_Date__c = Date.today().addDays(-7);
        am.End_Date__c = Date.today().addDays(7);
        am.Customer_Account__c = 'E7123';
        am.Alert_Mode__c = 'Create';
        am.Alert_Message__c = 'test Message';
        insert am;
        Modifier__c modifier1 = new Modifier__c(Name = 'Test');
        insert modifier1;
        Modifier_Entry__c modifierentry = new Modifier_Entry__c(Modifier_Header_Number__c = 'M123', ExternalID__c = 'M123', Modifier__c = modifier1.Id);
        insert modifierentry;
        MasterProductPrice__c testMasterProductPrice = new MasterProductPrice__c(Account__c = acc.Id, Product__c = prodKit1.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today().addDays(7), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = false, Modifier_Entry__c = modifierentry.Id);
        insert testMasterProductPrice;
    }
    
    //SY
    @isTest
    public static void testQueryPirchaseAndItemInfo(){
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(po.Id, false);
        
        po.Status__c = CCM_Constants.SUBMITTED;
        update po;
        CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(po.Id, false);
        
        po.Status__c = 'Cancelled';
        update po;
        /*
        Product_Data__c PD_c new Product_Data__c();
        
        PD_c.Sales_Channel__c=CCM_Constants.DEALER;
        PD_c.Item_Class__c=Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        PD_c.DSV__c='EEG';
        //PD_c.Pallet_Item__c=
        insert PD_c;
*/
        CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(po.Id, false);
        
        po.Status__c = 'review in process';
        update po;
        Purchase_Order_Attachment__c POA_c = new Purchase_Order_Attachment__c(Purchase_Order__c = po.Id, File_Name__c='Test PurchaseOrderAttachment');
		insert POA_c;  
        // CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(po.Id, false);
        
              
        
    }
    @isTest
    public static void testSubmitAndSync(){
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        CCM_PurchaseOrderPreview.SubmitAndSync(String.valueOf(po.Id), 2, 1);
        
        //SY
        CCM_PurchaseOrderPreview.SubmitAndSync(String.valueOf(po.Id), 45000, 1);
    }
    @isTest
    public static void testSyncOrder(){
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        Purchase_Order_Item__c poItem = [select id
                                         from Purchase_Order_Item__c
                                         limit 1];
        Product2 product1 = [select id
                             from Product2
                             limit 1];
        CCM_PurchaseOrderPreview.UpdateOrderItemInfo infoItem = new CCM_PurchaseOrderPreview.UpdateOrderItemInfo();
        infoItem.orderItemId = poItem.Id;
        infoItem.model = 'CS1614E';
        infoItem.quantity = 2;
        infoItem.productId = product1.Id;
        CCM_PurchaseOrderPreview.UpdateOrderInfo info = new CCM_PurchaseOrderPreview.UpdateOrderInfo();
        // 设置测试数据的字段值
        info.purchaseOrderId = po.Id;
        info.customerPO = 'P1220';
        info.OrderItemList = new List<CCM_PurchaseOrderPreview.UpdateOrderItemInfo>{ infoItem };
        CCM_PurchaseOrderPreview.SyncOrder(JSON.serialize(info));
    }
    @IsTest
    static void testQueryProduct(){
        Account acc = [select id
                       from account
                       limit 1];
        CCM_PurchaseOrderPreview.QueryProduct('', acc.Id, Date.today().addDays(-5));
    }
    @IsTest
    static void testuploadFileMidel(){
        Account acc = [select id
                       from account
                       limit 1];
        CCM_PurchaseOrderPreview.uploadFileMidel('1231', '1.txt', '1.txt');
        CCM_PurchaseOrderPreview.uploadFileInfo info = new CCM_PurchaseOrderPreview.uploadFileInfo();
        info.fileType = '';
        info.fileName = '1.txt';
        info.fileDate = Date.today().addDays(5);
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        try{
            CCM_PurchaseOrderPreview.uploadFile(po.Id, new List<CCM_PurchaseOrderPreview.uploadFileInfo>{ info });
        } catch (Exception e){
        }
        try{
            CCM_PurchaseOrderPreview.queryAlertMessage('', '');
        } catch (Exception e){
        }
    }
    @IsTest
    static void testRefreshInventory(){
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        CCM_PurchaseOrderPreview.refreshInventory(po.Id);
        CCM_PurchaseOrderPreview.CancelOrder(po.Id);
    }
    
    @IsTest
    static void testSendNotifyNocation(){
        CCM_PurchaseOrderPreview.SendNotifyNocation(UserInfo.getUserId());
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        
        po.Status__c = CCM_Constants.SUBMITTED;
        update po;
        
        po.Status__c = 'Cancelled';
        update po;
        
        po.Status__c = 'review in process';
        update po;
        Purchase_Order_Attachment__c POA_c = new Purchase_Order_Attachment__c(Purchase_Order__c = po.Id, File_Name__c='Test PurchaseOrderAttachment');
		insert POA_c;  
        CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(po.Id, false);
    }
}