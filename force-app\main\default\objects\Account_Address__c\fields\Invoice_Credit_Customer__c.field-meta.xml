<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Invoice_Credit_Customer__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <externalId>false</externalId>
    <label>Invoice/Credit – Customer</label>
    <lookupFilter>
        <active>true</active>
        <booleanFilter>(1 AND 3) OR (2 AND 4)</booleanFilter>
        <filterItems>
            <field>$Source.Customer__c</field>
            <operation>equals</operation>
            <valueField>Contact.AccountId</valueField>
        </filterItems>
        <filterItems>
            <field>$Source.Prospect__c</field>
            <operation>equals</operation>
            <valueField>Contact.Prospect__c</valueField>
        </filterItems>
        <filterItems>
            <field>$Source.Customer__c</field>
            <operation>notEqual</operation>
            <value></value>
        </filterItems>
        <filterItems>
            <field>$Source.Customer__c</field>
            <operation>equals</operation>
            <value></value>
        </filterItems>
        <infoMessage>Contact and address should belong to same customer Or prospect.</infoMessage>
        <isOptional>false</isOptional>
    </lookupFilter>
    <referenceTo>Contact</referenceTo>
    <relationshipLabel>Address (Invoice/Credit – Customer)</relationshipLabel>
    <relationshipName>AddressdFJ5</relationshipName>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Lookup</type>
</CustomField>
