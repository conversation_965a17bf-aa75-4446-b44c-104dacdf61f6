/**************************************************************************************************
 * Name: CCM_LeadController
 * Object: Lead
 * Purpose: Controller class for Lead relevant page, component
 * Author:Aria W Zhong
 * Create Date: 2023-06-12
 * Modify History:
 **************************************************************************************************/
public without sharing class CCM_LeadController{
    @AuraEnabled
    public static String deleteFile(String contentDocumentId, String Type){
        Map<String, String> result = new Map<String, String>();
        try{
            //同步删除报表数据
            List<ContentDocument> documentList = [select LatestPublishedVersionId, Id
                                                  from ContentDocument
                                                  WHERE Id = :contentDocumentId];
            List<Prospect_ContentDoucument__c> contentList = [select id
                                                              from Prospect_ContentDoucument__c
                                                              WHERE ContentVersion__c =:documentList.get(0).LatestPublishedVersionId];
            if (contentList.size() > 0){
                delete contentList;
            }
            //删除文件
            DELETE new ContentDocument(
                Id = contentDocumentId
            );
            result.put('Status', 'Success');
            result.put('Message', '');
            return JSON.serialize(result);
        } catch (Exception e){
            result.put('Status', 'Error');
            result.put('Message', e.getMessage());
            return JSON.serialize(result);
        }
    }
    @AuraEnabled
    public static String searchFile(String recordId, String type){
        List<ContentDocumentLink> contentList = [SELECT LinkedEntityId, ContentDocumentId, ContentDocument.LatestPublishedVersion.PathOnClient, ContentDocument.LatestPublishedVersion.CreatedDate, ContentDocument.LatestPublishedVersion.OwnerId, ContentDocument.LatestPublishedVersion.Owner.Name, ContentDocument.LatestPublishedVersion.FileExtension, ContentDocument.LatestPublishedVersion.ContentSize
                                                 FROM ContentDocumentLink
                                                 WHERE LinkedEntityId = :recordId And ContentDocument.LatestPublishedVersion.ExternalDocumentInfo1 = :type];
        List<ContentData> listReturn = new List<ContentData>();
        if (contentList.size() > 0){
            for (ContentDocumentLink item : contentList){
                ContentData data = new ContentData();
                data.FileName = item.ContentDocument.LatestPublishedVersion.PathOnClient;
                data.RecordId = item.LinkedEntityId;
                data.CredatedDate = item.ContentDocument.LatestPublishedVersion.CreatedDate.format('yyyy-MM-dd HH:mm:ss');
                data.ContentDocumentId = item.ContentDocumentId;
                data.OwnerId = item.ContentDocument.LatestPublishedVersion.OwnerId;
                data.OwnerName = item.ContentDocument.LatestPublishedVersion.Owner.Name;
                data.ContentSize = String.valueOf(item.ContentDocument.LatestPublishedVersion.ContentSize);
                data.FileExtension = item.ContentDocument.LatestPublishedVersion.FileExtension;
                listReturn.add(data);
            }
        } else{
        }
        return JSON.serialize(listReturn);
    }
    /**上传组件
     * 参数 type:TestReport/Attachment
     */
    @AuraEnabled
    public static String uploadFile(String recordId, String type, String fileName, String content){
        ContentVersion conVer = new ContentVersion();
        conVer.ContentLocation = 'S'; // S specify this document is in SF, use E for external files
        conVer.PathOnClient = fileName; // The files name, extension is very important here which will help the file in preview.
        conVer.Title = fileName; // Display name of the files
        conVer.VersionData = EncodingUtil.base64Decode(content); // converting your binary string to Blog
        conVer.ExternalDocumentInfo1 = type;
        //绑定ContentDocumentLink
        conVer.FirstPublishLocationId = recordId;
        Map<String, String> result = new Map<String, String>();
        try{
            insert conVer;
            result.put('Status', 'Success');
            result.put('Message', '');
            result.put('ContentId', conVer.Id);
            //插入Prospect ContentDoucument
            if (type == 'TestReport'){
                Prospect_ContentDoucument__c contentProspect = new Prospect_ContentDoucument__c();
                contentProspect.Type__c = type;//Attachment,TestReport
                contentProspect.File_Name__c = fileName;
                contentProspect.Created_Date__c = System.now();
                contentProspect.Created_By__c = UserInfo.getUserId();
                contentProspect.Prospect__c = recordId;
                contentProspect.ContentVersion__c = conVer.Id;
                insert contentProspect;
            }
            return JSON.serialize(result);
        } catch (Exception e){
            result.put('Status', 'Error');
            result.put('Message', e.getMessage());
            result.put('ContentId', '');
            return JSON.serialize(result);
        }
    }

    @AuraEnabled
    public static void linkFile(String recordId, String versionId, String documentId) {
        ContentVersion version = new ContentVersion();
        version.Id = versionId;
        version.ExternalDocumentInfo1 = 'Attachment';
        update version;

        ContentDocumentLink link = new ContentDocumentLink();
        link.ContentDocumentId = documentId;
        link.LinkedEntityId = recordId;
        insert link;
    }

    //获取审批人
    @InvocableMethod(label = 'Prospect Approver for report')
    public static void currentApprover(){
        List<ProcessInstanceWorkitem> lstItem = [SELECT id, ActorId, Actor.Name, ProcessInstance.TargetObjectId
                                                 FROM ProcessInstanceWorkitem
                                                 where ProcessInstanceWorkitem.ProcessInstance.ProcessDefinition.TableEnumOrId = 'Lead'];
        if (lstItem.size() > 0){
            Map<String, List<ProcessInstanceWorkitem>> processMap = new Map<String, List<ProcessInstanceWorkitem>>();
            for (ProcessInstanceWorkitem item : lstItem){
                String leadId = item.ProcessInstance.TargetObjectId;
                if (processMap.containsKey(leadId)){
                    List<ProcessInstanceWorkitem> workItemlist = processMap.get(leadId);
                    workItemlist.add(item);
                    processMap.put(leadId, workItemlist);
                } else{
                    List<ProcessInstanceWorkitem> workItemlist = new List<ProcessInstanceWorkitem>();
                    workItemlist.add(item);
                    processMap.put(leadId, workItemlist);
                }
            }
            //保存
            List<Lead> leadList = new List<Lead>();
            for (String itemId : processMap.keySet()){
                Lead lead = new Lead(
                    Id = itemId
                );
                String currentApproverId = '';
                String currentApproverName = '';
                for (ProcessInstanceWorkitem workItem : processMap.get(itemId)){
                    currentApproverId = workItem.ActorId + ',';
                    currentApproverName = workItem.Actor.Name + ',';
                }
                //去掉尾巴
                currentApproverName = currentApproverName.removeEnd(',');
                currentApproverId = currentApproverId.removeEnd(',');
                lead.current_Approver_Id__c = currentApproverId;
                lead.Current_Approver_Name__c = currentApproverName;
                leadList.add(lead);
            }
            update leadList;
            System.debug('leadList:' + leadList);
        }
    }
    //保存问卷
    /**{
     "QuestionData": [{
     "No": "编号",
     "Question": "问题",
     "FiledType":"字段类型",
     "Answer": "回答",
     "Mandatory": "N"
     }]
     } */
    @AuraEnabled
    public static String saveQuestionnaire(String recordId, String saveData){
        try{
            String apiName = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
            if (apiName.equals('Lead')){
                Lead lead = new Lead(
                    Id = recordId, 
                    QuestionNaire__c = saveData
                );
                update lead;
            }
            if (apiName.equals('Account')){
                Account acc = new Account(
                    Id = recordId, 
                    QuestionNaire__c = saveData
                );
                update acc;
            }

            return 'Success';
        } catch (Exception e){
            return 'false';
        }
    }

    @AuraEnabled
    public static void saveQuestionnaireDetail(String recordId, String saveData) {
        String apiName = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
        // delete existing questionnaire detail
        deleteExistingQuestionnaireDetails(recordId);
        
        List<QuestionAnswerWrapper> wrappers = new List<QuestionAnswerWrapper>();
        Map<String, Object> resultMap = (Map<String, Object>)JSON.deserializeUntyped(saveData);
        system.debug(JSON.serialize(resultMap));
        List<Object> results = resultMap.values();
        for(Object result : results) {
            system.debug('*** result:' + JSON.serialize(result));
            Map<String, Object> innerMap = (Map<String, Object>)result;
            String question = '';
            String answer = '';
            for(String key : innerMap.keySet()) {
                if(key == 'question') {
                    question = (String)innerMap.get(key);
                }

                if(key == 'List') {
                    List<Object> selectedValues = (List<Object>)innerMap.get(key);
                    for(Object selectedValue : selectedValues) {
                        Map<String, Object> selectedMap = (Map<String, Object>)selectedValue;
                        Boolean selected = true;
                        if(selectedMap.containsKey('select')) {
                            selected = (Boolean)selectedMap.get('select');
                        }
                        if(selected) {
                            String answerTemp = '';
                            String labelName = (String)selectedMap.get('label');
                            if(selectedMap.containsKey('answer')) {
                                String innerAnswer = (String)selectedMap.get('answer');
                                answerTemp = labelName + ': ' + innerAnswer;
                            }
                            else {
                                answerTemp = labelName;
                            }
                            answer = answer + answerTemp + ';';
                        }
                    }
                }

                if(key == 'answer') {
                    Object value = innerMap.get(key);
                    if(value instanceof String) {
                        String answerTemp = (String)innerMap.get(key);
                        if(String.isNotBlank(answerTemp)) {
                            answer = answerTemp;
                        }
                    }
                    else if(value instanceof Map<String, Object>) {
                        Map<String, Object> answerTemp = (Map<String, Object>)innerMap.get(key);
                        for(String key1 : answerTemp.keySet()) {
                            String temp = key1 + ': ' +  (String)answerTemp.get(key1);
                            answer = answer + temp + ';';
                        }
                    }
                }
            }

            Questionnaire_Question__c q = new Questionnaire_Question__c();
            q.Question__c = question;
            Questionnaire_Answer__c a = new Questionnaire_Answer__c();
            a.Answer__c = answer;
            QuestionAnswerWrapper wrapper = new QuestionAnswerWrapper();
            wrapper.question = q;
            wrapper.answer = a;
            wrappers.add(wrapper);
        }

        if(apiName == 'Lead') {
            for(QuestionAnswerWrapper wrapper : wrappers) {
                wrapper.question.Prospect__c = recordId;
            }
        }
        else if (apiName == 'Account') {
            for(QuestionAnswerWrapper wrapper : wrappers) {
                wrapper.question.Customer__c = recordId;
            }
        }

        List<Questionnaire_Question__c> questionList = new List<Questionnaire_Question__c>();
        for(QuestionAnswerWrapper wrapper : wrappers) {
            questionList.add(wrapper.question);
        }
        system.debug('***questionList: ' + JSON.serialize(questionList));
        insert questionList;

        List<Questionnaire_Answer__c> answerList = new List<Questionnaire_Answer__c>();
        for(QuestionAnswerWrapper wrapper : wrappers) {
            wrapper.answer.Questionnaire_Question__c = wrapper.question.Id;
            answerList.add(wrapper.answer);
        }
        system.debug('***: ' + JSON.serialize(answerList));
        insert answerList;
    }

    private static void deleteExistingQuestionnaireDetails(String recordId) {

        List<Questionnaire_Question__c> questions = [SELECT Customer__c, Prospect__c FROM Questionnaire_Question__c 
                                                     WHERE Customer__c = :recordId OR Prospect__c = :recordId];
        List<Questionnaire_Answer__c> answers = [SELECT Questionnaire_Question__c FROM Questionnaire_Answer__c 
                                                 WHERE Questionnaire_Question__c IN :questions];
        if(!answers.isEmpty()) {
            delete answers;
        }

        if(!questions.isEmpty()) {
            delete questions;
        }
    }

    //针对报表修改:添加已填问卷数量字段
    @AuraEnabled
    public static String saveQuestionnaireAddFilledNum(String recordId, String saveData,String filledNum){
        try{
            String apiName = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
            if (apiName.equals('Lead')){
                Lead lead = new Lead(
                    Id = recordId, 
                    QuestionNaire__c = saveData,
                    Number_of_Questions_Filled__c = Integer.valueOf(filledNum)
                );
                update lead;
            }
            if (apiName.equals('Account')){
                Account acc = new Account(
                    Id = recordId, 
                    QuestionNaire__c = saveData
                );
                update acc;
            }

            return 'Success';
        } catch (Exception e){
            return 'false';
        }
    }
    //获取问卷
    @AuraEnabled
    public static String getQuestionnaire(String recordId){
        String apiName = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
        String questionMsg = '';
        if (apiName.equals('Lead')){
            Lead lead = [select QuestionNaire__c, Prospect_Type__c, RecordType.DeveloperName
                         from Lead
                         where Id = :recordId];
            questionMsg = lead.QuestionNaire__c;
        }
        if (apiName.equals('Account')){
            Account acc = [select QuestionNaire__c, RecordType.DeveloperName
                           from Account
                           where Id = :recordId];
            questionMsg = acc.QuestionNaire__c;
        }

        return questionMsg;
    }
    //获取问卷类型
    @AuraEnabled
    public static String getQuestionnaireType(String recordId){
        String returnmsg = '';
        String apiName = Id.valueOf(recordId).getSObjectType().getDescribe().getName();
        if (apiName.equals('Lead')){
            Lead lead = [select QuestionNaire__c, RecordType.DeveloperName
                         from Lead
                         where Id = :recordId];
            returnmsg = lead.RecordType.DeveloperName;
        }
        if (apiName.equals('Account')){
            Account acc = [select QuestionNaire__c, RecordType.DeveloperName
                           from Account
                           where Id = :recordId];
            returnmsg = acc.RecordType.DeveloperName;

        }
        return returnmsg;
    }
    //提交审批
    @AuraEnabled
    public static String submitForEndUserApply(String recordIdItem, String consumerId, String comments){
        Map<String, String> returnMap = new Map<String, String>();
        //判断是否是新增
        List<ProcessInstance> lstProcessInstance = [SELECT ID
                                                    FROM ProcessInstance
                                                    WHERE TargetObjectId = :recordIdItem And Status = 'Pending'
                                                    ORDER BY CreatedDate DESC];
        if (lstProcessInstance == null || lstProcessInstance.size() <= 0){
            returnMap.put('IsSuccess', 'true');
            // 新建申请
            Approval.ProcessSubmitRequest req = new Approval.ProcessSubmitRequest();
            // 审批记录
            req.setObjectId(recordIdItem);
            // 提交人
            req.setSubmitterId(UserInfo.getUserId());
            req.setProcessDefinitionNameOrId('End_User_Approval');
            req.setComments(comments);
            Approval.process(req);
            //修改字段
            Lead lead = new Lead(
                Id = recordIdItem, 
                Related_Consumer__c = consumerId
            );
            update lead;
        } else{
            returnMap.put('IsSuccess', 'false');
            returnMap.put('ErrorMessage', Label.Data_Under_approval);
        }
        return JSON.serialize(returnMap);
    }
    //获取list清单
    @AuraEnabled
    public static String getConsumerInfo(Integer pageNumber, Integer pageSize, String companyName, String city, String fleetManager){
        String sql = 'Select Id,Name,ShippingCountry,ShippingCity,ShippingStreet,Address_Detail__c,ShippingPostalCode,Consumer_Status__c,Fleet_Manager__r.Name ' + 
                       ' from Account where Status__c <> \'Inactive\' And RecordType.DeveloperName like \'%Consumer%\'';
        if (String.isNotBlank(fleetManager)){
            sql += ' And Fleet_Manager__c =  \'' + fleetManager + '\'';
        }
        if (String.isNotBlank(companyName)){
            sql += ' And name like \'%' + companyName + '%\'';
        }
        if (String.isNotBlank(city)){
            sql += ' And ShippingCity like \'%' + city + '%\'';
        }
        sql += ' LIMIT 5000';
        System.debug('sql:' + sql);
        InitData initD = new InitData();
        List<AccountInfo> wrappers = new List<AccountInfo>();
        List<Account> allList = Database.query(sql);
        if (allList != null && allList.size() > 0){
            for (Account item : allList){
                AccountInfo info = new AccountInfo();
                info.ConsumerId = item.Id;
                info.CompanyName = item.Name;
                info.Country = item.ShippingCountry;
                info.City = item.ShippingCity;
                info.Street = item.ShippingStreet;
                info.Address = item.Address_Detail__c;
                info.Postcode = item.ShippingPostalCode;
                info.Status = item.Consumer_Status__c;
                info.FleetManager = item.Fleet_Manager__r.Name;
                wrappers.add(info);
            }
        }
        initD.totalRecords = allList.size();
        initD.currentData = getCurrentData(wrappers, pageNumber, pageSize);
        return JSON.serialize(initD);
    }
    //分页
    public static List<AccountInfo> getCurrentData(List<AccountInfo> allData, Decimal pageNumber, Integer pageSize){
        List<AccountInfo> currentData = new List<AccountInfo>();
        Integer min = ((Integer) pageNumber - 1) * pageSize;
        Integer max = (Integer) pageNumber * pageSize - 1;
        for (Integer i = min; i <= max; i++){
            if (i < allData.size()){
                currentData.add(allData.get(i));
            }
        }
        return currentData;
    }
    @AuraEnabled
    public static String fleetManageSearch(String fifterString){
        String queryStr = 'Select Id, Name  from Contact where ' + 
                       '  Name like \'%' + fifterString + '%\' and Role__c =\'Fleet Manager\' LIMIT 5000';

        System.debug('queryStr:' + queryStr);
        List<SObject> queryList = Database.query(queryStr);
        if (queryList.isEmpty()){
            return null;
        } else{
            return JSON.serialize(queryList);
        }
    }
    public class InitData{
        public List<AccountInfo> currentData;
        public Integer totalRecords;
        public Boolean isSuccess;
        public String errorMessage;
        public InitData(){
            this.currentData = new List<AccountInfo>();
            this.totalRecords = 0;
            this.isSuccess = true;
            this.errorMessage = '';
        }
    }
    public class ContentData{
        public String RecordId;
        public String ContentDocumentId;
        public String FileName;
        public String CredatedDate;
        public String OwnerName;
        public String OwnerId;
        public String FileExtension;
        public String ContentSize;
    }
    public class AccountInfo{
        public String ConsumerId;
        public String CompanyName;
        public String FleetManager;
        public String Country;
        public String City;
        public String Street;
        public String Address;
        public String Postcode;
        public String Status;
    }
    /**
     * @description: reject assignment with prospect Id, reason and comments
     */
    @AuraEnabled
    public static String doReject(String leadId, String rejectComments, String rejectAssignmentReason){
        //1.判断是否是能拒绝
        Boolean rejectFlag = true;
        Lead queryLead = [select id, OwnerId, Owner.Name
                          from Lead
                          where Id = :leadId];
        User user = [select Profile.Name
                     from User
                     where Id = :UserInfo.getUserId()];
        //判断owner是否是queue
        if (!user.Profile.Name.containsIgnoreCase('Administrator')){
            if (queryLead.OwnerId.getSObjectType().getDescribe().getName() == 'Group'){
                if (!user.Profile.Name.containsIgnoreCase(queryLead.Owner.Name)){
                    rejectFlag = false;
                }
                // List<GroupMember> groupMemberList = [Select UserOrGroupId, Group.Name
                //                                      From GroupMember
                //                                      where GroupId = :queryLead.OwnerId and UserOrGroupId = :UserInfo.getUserId()];
                // if (groupMemberList.size() == 0){
                //     rejectFlag = false;
                // }

            } else{
                if (queryLead.OwnerId <> UserInfo.getUserId()){
                    rejectFlag = false;
                }
            }
        }

        //有权限后
        if (rejectFlag){
            Lead lead = new Lead(
                Id = leadId, 
                Status = 'Open', 
                Reject_Comments__c = rejectComments, 
                Reject_Assignment_Reason__c = rejectAssignmentReason, 
                Rejected_By__c = UserInfo.getUserId(), 
                Rejected_Time__c = Datetime.now(), 
                Is_Rejected__c = true
            );
            // 2. get sales Admin Queue Id, assign value to ownerId
            List<Group> groupList = [select id, Name
                                     from group
                                     where DeveloperName = 'Inside_Sales' And Type = 'Queue'];
            if (groupList.size() > 0){
                lead.OwnerId = groupList.get(0).Id;
            }
            try{
                //check
                // return 'status:'+lead.Status;
                update lead;
            } catch (Exception e){
                System.debug(LoggingLevel.INFO, '*** e.getStackTraceString(): ' + e.getStackTraceString());
                return e.getStackTraceString();
            }
            return 'SUCCESS';
        } else{
            return Label.Prospect_DoReject;
        }
    }
    /**
     * @description: close prospect by Id, and reason
     */
    @AuraEnabled
    public static String doClose(String leadId, String closeReason, String closeComments){
        Lead leadQuery = [select status
                          from Lead
                          where id = :leadId];
        Map<String, String> returnMap = new Map<String, String>();
        if (leadQuery.Status == 'Pending Review'){
            return Label.Prospect_DoClose;
        } else{
            Lead lead = new Lead(
                Id = leadId, 
                Status = 'Closed Lost', 
                IsClosed__c = true, 
                Close_Reason__c = closeReason, 
                Close_Comments__c = closeComments, 
                Closed_By__c = UserInfo.getUserId(), 
                closed_Time__c = Datetime.now()
            );
            Savepoint sp = Database.setSavepoint();
            try{
                update lead;
            } catch (Exception e){
                Database.rollback(sp);
                System.debug(LoggingLevel.INFO, '*** e.getStackTraceString(): ' + e.getMessage() + e.getLineNumber());
                return e.getStackTraceString();
            }
            return 'SUCCESS';
        }
    }

    private class QuestionAnswerWrapper {
        public Questionnaire_Question__c question;
        public Questionnaire_Answer__c answer;
    }
}