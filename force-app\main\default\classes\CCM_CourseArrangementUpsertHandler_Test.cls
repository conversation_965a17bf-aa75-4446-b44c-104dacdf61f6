/**
 * Honey
 * 2023/10/30
 * 测试类
 */
@isTest
public without sharing class CCM_CourseArrangementUpsertHandler_Test {
    @isTest
    public static void testUpdatePriceBook(){
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Traning_PriceBook__c objTraining = new Traning_PriceBook__c();
        objTraining.Start_Date__c = Date.today().addDays(-100);
        objTraining.End_Date__c = Date.today().addDays(100);
        objTraining.IsActive__c = true;
        insert objTraining;
        Traning_PriceBook_Entry__c objTrainingBook = new Traning_PriceBook_Entry__c();
        objTrainingBook.Course_Product__c = objProduct.Id;
        objTrainingBook.Traning_PriceBook__c = objTraining.Id;
        objTrainingBook.Start_Date__c = Date.today().addDays(-100);
        objTrainingBook.End_Date__c = Date.today().addDays(100);
        objTrainingBook.UnitPrice__c = 20;
        insert objTrainingBook;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c objCourseArrangement = new Course_Arrangement__c();
        objCourseArrangement.Course_Date__c = Date.today();

        objCourseArrangement.Training_Course_Setting__c = objSetting.Id;
        objCourseArrangement.Free_Training__c = true;
        insert objCourseArrangement;
        objCourseArrangement.Traning_PriceBook__c = objTraining.Id;
        update objCourseArrangement;

    }

    @isTest
    public static void testFreeTrainingInsert(){
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;

        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;

        Course_Arrangement__c objCourseArrangement = new Course_Arrangement__c();
        objCourseArrangement.Course_Date__c = Date.today();
        objCourseArrangement.Training_Course_Setting__c = objSetting.Id;
        objCourseArrangement.Free_Training__c = true;

        Test.startTest();
        insert objCourseArrangement;
        Test.stopTest();

        // Verify that price is set to 0 for free training
        Course_Arrangement__c insertedArrangement = [SELECT Id, Price__c, Free_Training__c FROM Course_Arrangement__c WHERE Id = :objCourseArrangement.Id];
        System.assertEquals(0, insertedArrangement.Price__c, 'Price should be 0 for free training');
        System.assertEquals(true, insertedArrangement.Free_Training__c, 'Free Training should be true');
    }

    @isTest
    public static void testFreeTrainingUpdate(){
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;

        Traning_PriceBook__c objTraining = new Traning_PriceBook__c();
        objTraining.Start_Date__c = Date.today().addDays(-100);
        objTraining.End_Date__c = Date.today().addDays(100);
        objTraining.IsActive__c = true;
        insert objTraining;

        Traning_PriceBook_Entry__c objTrainingBook = new Traning_PriceBook_Entry__c();
        objTrainingBook.Course_Product__c = objProduct.Id;
        objTrainingBook.Traning_PriceBook__c = objTraining.Id;
        objTrainingBook.Start_Date__c = Date.today().addDays(-100);
        objTrainingBook.End_Date__c = Date.today().addDays(100);
        objTrainingBook.UnitPrice__c = 100;
        insert objTrainingBook;

        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;

        Course_Arrangement__c objCourseArrangement = new Course_Arrangement__c();
        objCourseArrangement.Course_Date__c = Date.today();
        objCourseArrangement.Training_Course_Setting__c = objSetting.Id;
        objCourseArrangement.Traning_PriceBook__c = objTraining.Id;
        objCourseArrangement.Free_Training__c = false;
        insert objCourseArrangement;

        Test.startTest();
        // Update to free training
        objCourseArrangement.Free_Training__c = true;
        update objCourseArrangement;
        Test.stopTest();

        // Verify that price is set to 0 when changed to free training
        Course_Arrangement__c updatedArrangement = [SELECT Id, Price__c, Free_Training__c FROM Course_Arrangement__c WHERE Id = :objCourseArrangement.Id];
        System.assertEquals(0, updatedArrangement.Price__c, 'Price should be 0 when changed to free training');
        System.assertEquals(true, updatedArrangement.Free_Training__c, 'Free Training should be true');
    }

    public CCM_CourseArrangementUpsertHandler_Test() {

    }
}