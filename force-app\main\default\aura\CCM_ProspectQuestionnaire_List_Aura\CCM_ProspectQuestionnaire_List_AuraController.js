({
    // 初始话获取问卷信息回填
    doInit : function(component, event, helper) {
        
        let yesOrNoOptions = [
            {'label': $A.get("$Label.c.CCM_Yes"), 'value': 'Y'},
            {'label': $A.get("$Label.c.CCM_AnswerNo"), 'value': 'N'}
        ];
        component.set('v.options', yesOrNoOptions);

        helper.initQAMap(component);

        var action_1 = component.get('c.getQuestionnaireType')
        action_1.setParams({
            recordId: component.get('v.recordId')
        });

        component.set('v.Dealer_Q_1',$A.get("$Label.c.Questionaire_1_1"))
        component.set('v.Dealer_Q_1_1',$A.get("$Label.c.Questionaire_1_2"))
        component.set('v.Dealer_Q_1_2',$A.get("$Label.c.Questionaire_1_3"))
        component.set('v.Dealer_Q_2',$A.get("$Label.c.Questionaire_2"))
        component.set('v.Dealer_Q_3',$A.get("$Label.c.Questionaire_3_1"))
        component.set('v.Dealer_Q_3_2',$A.get("$Label.c.Questionaire_3_2"))
        component.set('v.Dealer_Q_4',$A.get("$Label.c.Questionaire_4"))
        component.set('v.Dealer_Q_5',$A.get("$Label.c.Questionaire_5"))
        component.set('v.Dealer_Q_6',$A.get("$Label.c.Questionaire_6_1"))
        component.set('v.Dealer_Q_6_2',$A.get("$Label.c.Questionaire_6_4"))
        component.set('v.Dealer_Q_7',$A.get("$Label.c.Questionaire_7"))
        component.set('v.Dealer_Q_8',$A.get("$Label.c.Questionaire_8_1"))
        component.set('v.Dealer_Q_8_2',$A.get("$Label.c.Questionaire_8_2"))



        component.set('v.End_User_1',$A.get("$Label.c.Questionaire_E_1_1"))
        component.set('v.End_User_2',$A.get("$Label.c.Questionaire_E_2"))
        component.set('v.End_User_3',$A.get("$Label.c.Questionaire_E_3_1"))
        component.set('v.End_User_4',$A.get("$Label.c.Questionaire_E_4_1"))
        component.set('v.End_User_5',$A.get("$Label.c.Questionaire_E_5_1"))
        component.set('v.End_User_6',$A.get("$Label.c.Questionaire_E_6_1"))
        component.set('v.End_User_7',$A.get("$Label.c.Questionaire_E_7_1"))
        component.set('v.End_User_8',$A.get("$Label.c.Questionaire_E_8_1"))
        component.set('v.End_User_9',$A.get("$Label.c.Questionaire_E_9_1"))
        component.set('v.End_User_10',$A.get("$Label.c.Questionaire_E_10_1"))
        component.set('v.End_User_11',$A.get("$Label.c.Questionaire_E_11_1"))
        component.set('v.End_User_12',$A.get("$Label.c.Questionaire_E_12_1"))
        component.set('v.End_User_13',$A.get("$Label.c.Questionaire_E_13_1"))
        component.set('v.End_User_14',$A.get("$Label.c.Questionaire_E_14_1"))
        component.set('v.End_User_15',$A.get("$Label.c.Questionaire_E_15_1"))
        component.set('v.End_User_16',$A.get("$Label.c.Questionaire_E_16_1"))
        component.set('v.End_User_17',$A.get("$Label.c.Questionaire_E_17_1"))
        component.set('v.End_User_18',$A.get("$Label.c.Questionaire_E_18_1"))
        component.set('v.End_User_19',$A.get("$Label.c.Questionaire_E_19_1"))
        component.set('v.End_User_20',$A.get("$Label.c.Questionaire_E_20_1"))
        component.set('v.End_User_21',$A.get("$Label.c.Questionaire_E_21_1"))
        component.set('v.End_User_21_2',$A.get("$Label.c.Questionaire_E_21_3"))


        // let DealerAnswer = component.get('v.DealerAnswer')

        let EndUserAnswer = component.get('v.EndUserAnswer')
        
        EndUserAnswer.EndUser_1.options = [
            {'label': $A.get("$Label.c.Questionaire_E_1_2_1"), 'value': $A.get("$Label.c.Questionaire_E_1_2_1")},
            {'label': $A.get("$Label.c.Questionaire_E_1_2_2"), 'value': $A.get("$Label.c.Questionaire_E_1_2_2")},
            {'label': $A.get("$Label.c.Questionaire_E_1_2_3"), 'value': $A.get("$Label.c.Questionaire_E_1_2_3")}
        ]
        EndUserAnswer.EndUser_3.List = [
            {label : $A.get("$Label.c.Questionaire_E_3_2_1"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_2"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_3"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_4"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_5"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_6"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_7"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_8"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_9"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_10"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_11"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_3_2_12"),select : false},
        ]
        EndUserAnswer.EndUser_7.List = [
            {label:$A.get("$Label.c.Questionaire_E_7_2_1"),value:'OpenBedTruck',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_7_2_2"),value:'ClosedPanelTruck',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_7_2_3"),value:'OpenBedTrailer',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_7_2_4"),value:'Closedtrailers',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_7_2_5"),value:'Teamleader',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_7_2_6"),value:'Dispatcher',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_7_2_7"),value:'RepairEngineer',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_7_2_8"),value:'Worker',answer:'',select:''}
        ]
        EndUserAnswer.EndUser_8.List = [
            {label:$A.get("$Label.c.Questionaire_E_8_3_1"),value:'PedestrianMower',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_2"),value:'Ride-onMower',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_3"),value:'GrassTrimmer_BrushCutter',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_4"),value:'HedgeTrimmer',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_5"),value:'LeafBlower',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_6"),value:'ChainSaws',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_7"),value:'Lifestyle',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_8"),value:'Chargers',answer:'',select:''},
            {label:$A.get("$Label.c.Questionaire_E_8_3_9"),value:'RoboticMower',answer:'',select:''}
        ]
        EndUserAnswer.EndUser_11.List = [
            {label : $A.get("$Label.c.Questionaire_E_11_2_1"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_11_2_2"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_11_2_3"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_11_2_4"),select : false}
        ]
        EndUserAnswer.EndUser_13 = 
        {No : '13', Required : '' ,answer : 
        {
            Total:'',
            PerTeam:'',
            PerUser:''
        },child : 
        {
            Total:  {label : $A.get("$Label.c.Questionaire_E_13_2_1")},
            PerTeam:{label : $A.get("$Label.c.Questionaire_E_13_2_2")},
            PerUser:{label : $A.get("$Label.c.Questionaire_E_13_2_3")}
        }},
        EndUserAnswer.EndUser_14 = 
        {No : '14', Required : '' ,answer : 
        {
            Total:'',
            PerTeam:'',
            PerUser:''
        },child : 
        {
            Total:  {label : $A.get("$Label.c.Questionaire_E_14_2_1")},
            PerTeam:{label : $A.get("$Label.c.Questionaire_E_14_2_2")},
            PerUser:{label : $A.get("$Label.c.Questionaire_E_14_2_3")}
        }},

        EndUserAnswer.EndUser_15.List = [
            {label : $A.get("$Label.c.Questionaire_E_15_2_1"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_15_2_2"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_15_2_3"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_15_2_4"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_15_2_5"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_15_2_6"),select : false},
            {label : $A.get("$Label.c.Questionaire_E_15_2_7"),select : false}
        ]
        EndUserAnswer.EndUser_19.List = [
            {'label': $A.get("$Label.c.Questionaire_E_19_2_1"), 'value': $A.get("$Label.c.Questionaire_E_19_2_1")},
            {'label': $A.get("$Label.c.Questionaire_E_19_2_2"), 'value': $A.get("$Label.c.Questionaire_E_19_2_2")},
            {'label': $A.get("$Label.c.Questionaire_E_19_2_3"), 'value': $A.get("$Label.c.Questionaire_E_19_2_3")},
            {'label': $A.get("$Label.c.Questionaire_E_19_2_4"), 'value': $A.get("$Label.c.Questionaire_E_19_2_4")},
        ]
        EndUserAnswer.EndUser_20.List = [
            {'label': $A.get("$Label.c.Questionaire_E_20_2_1"), 'value': $A.get("$Label.c.Questionaire_E_20_2_1")},
            {'label': $A.get("$Label.c.Questionaire_E_20_2_2"), 'value': $A.get("$Label.c.Questionaire_E_20_2_2")},
            {'label': $A.get("$Label.c.Questionaire_E_20_2_3"), 'value': $A.get("$Label.c.Questionaire_E_20_2_3")},
            {'label': $A.get("$Label.c.Questionaire_E_20_2_4"), 'value': $A.get("$Label.c.Questionaire_E_20_2_4")},
            {'label': $A.get("$Label.c.Questionaire_E_20_2_5"), 'value': $A.get("$Label.c.Questionaire_E_20_2_5")},
            {'label': $A.get("$Label.c.Questionaire_E_20_2_6"), 'value': $A.get("$Label.c.Questionaire_E_20_2_6")},
            {'label': $A.get("$Label.c.Questionaire_E_20_2_7"), 'value': $A.get("$Label.c.Questionaire_E_20_2_7")}
        ]
        EndUserAnswer.EndUser_21.List = [
            {'label': $A.get("$Label.c.Questionaire_E_21_2_1"), 'select': false},
            {'label': $A.get("$Label.c.Questionaire_E_21_2_2"), 'select': false},
            {'label': $A.get("$Label.c.Questionaire_E_21_2_3"), 'select': false},
            {'label': $A.get("$Label.c.Questionaire_E_21_2_4"), 'select': false},
            {'label': $A.get("$Label.c.Questionaire_E_21_2_5"), 'select': false},
            {'label': $A.get("$Label.c.Questionaire_E_21_2_6"), 'select': false}
        ]

        component.set('v.Section_1',$A.get("$Label.c.Questionaire_E_S_1"))
        component.set('v.Section_2',$A.get("$Label.c.Questionaire_E_S_2"))
        component.set('v.Section_3',$A.get("$Label.c.Questionaire_E_S_3"))

        // component.set('v.DealerAnswer',DealerAnswer)
        console.log("EndUserAnswer",JSON.parse(JSON.stringify(EndUserAnswer)));
        component.set('v.EndUserAnswer',EndUserAnswer)


        action_1.setCallback(this, function (response) {
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               console.log("result",result);
               if(result == 'Prospect_End_User'){
                component.set('v.getQuestionnaireType','EndUser')
               }else{
                component.set('v.getQuestionnaireType','Dealer')
               }
           } else {
            console.log("error111111");
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": "error",
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action_1);




        var action = component.get('c.getQuestionnaire')
        action.setParams({
            recordId: component.get('v.recordId')
        });

        action.setCallback(this, function (response) {
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               if(result){
                // console.log("获取问卷11",result);
                let _result = result.replace(/&quot;/g, '"').replace(/\\/g, '')
                let res = JSON.parse(_result[0] == "'"|| _result[0] == '"' ? _result.substring(1, _result.length - 1) :_result)
                console.log("获取问卷22",JSON.stringify(res));
                res.QuestionData[0] ? component.set('v.DealerAnswer',res.QuestionData[0]) : ""
                res.QuestionData[1] ? component.set('v.EndUserAnswer',res.QuestionData[1]) : ""
               }
           } else {
            console.log("error22222","recordId",component.get('v.recordId'));
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": "error",
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    // 点击提交按钮
    OnclickNext : function(component, event, helper){

        let DealerAnswer = component.get('v.DealerAnswer')
        let EndUserAnswer = component.get('v.EndUserAnswer')


        let savedata = {
            QuestionData:[
                DealerAnswer,
                EndUserAnswer,
            ]
        }

        let verify = true
        let filledNumCount=0
        // 必填校验 Dealer
        if(component.get('v.getQuestionnaireType') == 'Dealer'){
            // filledNumCount
            filledNumCount=4;
            for (let key in DealerAnswer) {
                if (key == 'Dealer_4' || key == 'Dealer_5'|| key == 'Dealer_6'|| key == 'Dealer_6_2'|| key == 'Dealer_8_1') {
                    if(DealerAnswer[key].answer){
                        filledNumCount++
                        console.log("filledNumCount",DealerAnswer[key].answer+'---------num:'+filledNumCount);
                    }
                }  
            }          
            for (let key in DealerAnswer) {
                if (key == 'Dealer_1_1' || key == 'Dealer_1_2' || key == 'Dealer_2' || key == 'Dealer_3_1' || key == 'Dealer_3_2') {
                    if (key == 'Dealer_3_2' && DealerAnswer.Dealer_3_1.answer == 'Y') {
                        if(!DealerAnswer[key].answer){
                            var toastEvt = $A.get("e.force:showToast");
                               toastEvt.setParams({
                                "title": "Error",
                                   "message": "Please fill in all questions with *",
                                "type": "error"
                            }).fire();
                            verify = false
                            return
                        }
                    } else if (key != 'Dealer_3_2') {
                        if(!DealerAnswer[key].answer){
                            var toastEvt = $A.get("e.force:showToast");
                               toastEvt.setParams({
                                "title": "Error",
                                   "message": "Please fill in all questions with *",
                                "type": "error"
                            }).fire();
                            verify = false
                            return
                        }
                    }
                }else if(key == 'Dealer_7'){
                    console.log("DealerAnswer[key].List",DealerAnswer[key].List);
                    if(DealerAnswer[key].List.filter(item => item.select == true).length == 0){
                        var toastEvt = $A.get("e.force:showToast");
                           toastEvt.setParams({
                            "title": "Error",
                               "message": "Please fill in all questions with *",
                            "type": "error"
                        }).fire();
                        verify = false
                        return
                    }
                }

            }
        }else if(component.get('v.getQuestionnaireType') == 'EndUser'){
            // filledNumCount
            filledNumCount=13;
            for (let key in EndUserAnswer) {
                if (key == 'EndUser_4'||key == 'EndUser_12'||key == 'EndUser_16'||key == 'EndUser_17'||key == 'EndUser_18') {
                    if(EndUserAnswer[key].answer){
                        filledNumCount++
                        console.log("filledNumCount",EndUserAnswer[key].answer+'---------num:'+filledNumCount+'-----key:'+key);
                    }
                }else if(key == 'EndUser_7'||key == 'EndUser_8'){
                     if(EndUserAnswer[key].answer){
                        filledNumCount++
                        console.log("filledNumCount1---"+key+'----'+EndUserAnswer[key].answer+'---------num:'+filledNumCount);

                    }else{
                        if(EndUserAnswer[key].List.filter(item => item.select!='').length > 0||EndUserAnswer[key].List.filter(item => item.answer!='').length > 0){
                            filledNumCount++
                        console.log("filledNumCount3---"+key+'-------------size:'+EndUserAnswer[key].List.filter(item => item.select=true).length);
                        }
                    }
                }else if(key == 'EndUser_13'||key == 'EndUser_14'){
                     if(EndUserAnswer[key].answer.Total!=''||EndUserAnswer[key].answer.PerTeam!=''||EndUserAnswer[key].answer.PerUser!=''){
                        filledNumCount++
                        console.log("filledNumCount4---"+key+'----'+EndUserAnswer[key].answer.Total+'****'+EndUserAnswer[key].answer.PerTeam+'****'+EndUserAnswer[key].answer.PerUser+'---------num:'+filledNumCount);

                    }
                }
            }              
            for (let key in EndUserAnswer) {
                if (key == 'EndUser_1' || key == 'EndUser_2' || key == 'EndUser_5' || key == 'EndUser_6' || key == 'EndUser_9' || key == 'EndUser_10' || key == 'EndUser_19' || key == 'EndUser_20') {
                    if(!EndUserAnswer[key].answer){
                        var toastEvt = $A.get("e.force:showToast");
                           toastEvt.setParams({
                            "title": "Error",
                               "message": "Please fill in all questions with *",
                            "type": "error"
                        }).fire();
                        verify = false
                        return
                    }
                }else if(key == 'EndUser_3' || key == 'EndUser_11' || key == 'EndUser_15' || key == 'EndUser_21'){
                    if(EndUserAnswer[key].List.filter(item => item.select == true).length == 0){
                        var toastEvt = $A.get("e.force:showToast");
                           toastEvt.setParams({
                            "title": "Error",
                               "message": "Please fill in all questions with *",
                            "type": "error"
                        }).fire();
                        verify = false
                        console.log(EndUserAnswer);
                        return
                    }
                }
            }
        }
        // if((verify && (item.No == '1.1'  || item.No == '2.1' || item.No == '3.1' || item.No == '3.2' || item.No == '6.1' || item.No == '7.1' || item.No == '7.2') && (item.Answer == "" || item.Answer == []))){
        //     if(item.Answer == "" || !item.Answer){
        //         var toastEvt = $A.get("e.force:showToast");
        //         toastEvt.setParams({
        //             "title": "Error",
        //             "message": "Answer:" + item.Question,
        //             "type": "error"
        //         }).fire();
        //         verify = false
        //         return
        //     }
        // }


       if(verify){
            console.log("hhh",JSON.stringify(savedata));
            var action = component.get("c.saveQuestionnaireAddFilledNum");
            // console.log("action",action);
            action.setParams({
                recordId: component.get('v.recordId'),
                saveData: JSON.stringify((savedata)),
                filledNum:filledNumCount
            });
            action.setCallback(this, function (response) {
                console.log("response",response);
               var state = response.getState();
               if (state === "SUCCESS") {
                   var result = response.getReturnValue();
                   var toastEvt = $A.get("e.force:showToast");
                   toastEvt.setParams({
                    "title": "Success",
                    "message": 'Success',
                    "type": "Success"
                   }).fire();
                   if(result){
                    console.log("result",result);
                   }
               } else {
                console.log("error111112222");
                   var toastEvt = $A.get("e.force:showToast");
                   toastEvt.setParams({
                       "title": "Error",
                       "message": 'Error',
                       "type": "error"
                   }).fire();
               }
            });
            $A.enqueueAction(action);

            if(component.get('v.getQuestionnaireType') == 'Dealer'){
                let dealerAnswer = helper.constructQAData(component, 'Dealer');
                let action2 = component.get('c.saveQuestionnaireDetail');
                action2.setParams({'recordId': component.get('v.recordId'), saveData: JSON.stringify(dealerAnswer)});
                action2.setCallback(this, function (response){
                    let state = response.getState();
                });
                $A.enqueueAction(action2);
            }
            else if(component.get('v.getQuestionnaireType') == 'EndUser') {
                let endUserAnswer = helper.constructQAData(component, 'EndUser');
                let action2 = component.get('c.saveQuestionnaireDetail');
                action2.setParams({'recordId': component.get('v.recordId'), saveData: JSON.stringify(endUserAnswer)});
                action2.setCallback(this, function (response){
                    let state = response.getState();
                });
                $A.enqueueAction(action2);
            }
       }
    },
    // Dealer07的输入框输入值计算总值
    inputDealer_Q_7 : function(component, event, helper){
        let DealerAnswer = component.get('v.DealerAnswer')
        let sum = 0
        DealerAnswer.Dealer_7.List.forEach(item => sum += item.answer *1)
        DealerAnswer.Dealer_7.sum = sum
        component.set('v.DealerAnswer',DealerAnswer)
        // console.log("DealerAnswer",component.get('v.DealerAnswer'));
    },
    // Dealer07取消勾选某个选择,禁用input && 删除value && 总值减少相应的值
    changeDealer_Q_7 : function(component, event, helper){
        if(event.detail.checked == false){
            let DealerAnswer = component.get('v.DealerAnswer')
            DealerAnswer.Dealer_7.List.forEach((item,index) => {
                if(item.label == event.target.name.label){
                    DealerAnswer.Dealer_7.sum = DealerAnswer.Dealer_7.sum - item.answer
                    DealerAnswer.Dealer_7.List[index].answer = ""
                    item.answer = ""
                    component.set('v.DealerAnswer',DealerAnswer)
                }
            })
            console.log("DealerAnswer",DealerAnswer);
        }
    },
    // EndUser08方格选择逻辑
    select_table : function(component, event, helper){
        let clast = "." + event.target.row
        let End_User_8_table = component.get('v.EndUserAnswer').EndUser_8.List
        // console.log(JSON.parse(JSON.stringify(component.get('v.EndUserAnswer'))));
        End_User_8_table.forEach((item,index)=>{
            if(item.value == event.target.row){
                // console.log("当前的item",JSON.parse(JSON.stringify(item)));
                End_User_8_table[index].select = event.target.name
            }
        })
        console.log("EndUser_8",JSON.parse(JSON.stringify(component.get('v.EndUserAnswer').EndUser_8.List)));
        var target = event.target;
        var selected = target.classList.contains('selected');
        if (selected) {
          target.classList.remove('selected');
        } else {
          document.querySelector(clast).childNodes.forEach(item=>{
            item.classList.remove('selected')
        })
          target.classList.add('selected');
        }
    },
    // EndUser07方格选择逻辑
    select_table07 : function(component, event, helper){
        let _calss = "." + event.target.row
        console.log("name",_calss,document.querySelector(_calss));
        let End_User_7 = component.get('v.EndUserAnswer').EndUser_7.List
 

        End_User_7.forEach((item,index)=>{
            if(item.value == event.target.row){
                console.log("当前的item",event.target.row,event.target.name);
                if(event.target.name == "Petrol" || event.target.name == "Electric"){
                    End_User_7[index].answer = event.target.name
                }else{
                    End_User_7[index].select = event.target.name
                }
            }
        })
        var target = event.target;
        var selected = target.classList.contains('selected');
        // 取消选中
        if (selected) {
            target.classList.remove('selected');
            document.querySelector(_calss).childNodes.forEach(item=>{
                if(event.target.row == "ClosedPanelTruck" || event.target.row == "OpenBedTruck" ){
                    if(event.target.name == "0"){
                        console.log(item,index);
                        item.classList.remove('selected')
                        End_User_7[index].answer = ''
                    }
                }
            })
        // 选中
        } else {
            document.querySelector(_calss).childNodes.forEach((item,index)=>{
                if(event.target.row == "ClosedPanelTruck" || event.target.row == "OpenBedTruck" ){
                    if(event.target.name == "0"){
                        item.classList ? item.classList.remove('selected') : ""
                        console.log("你选了0",JSON.parse(JSON.stringify(item)),item.name);
                    }else if(event.target.name == "Petrol" || event.target.name == "Electric"){
                        // event.target.name == "Petrol" ?
                        // End_User_7[index].select || End_User_7[index].select == "0" ? End_User_7[index].select = "1" : ""
                        console.log("gggg");

                    } else{
                        item.classList && item.name != 'Petrol' && item.name != 'Electric' ? item.classList.remove('selected') : ""
                    }

                }else{
                    item.classList ? item.classList.remove('selected') : ""
                }
            })
            console.log("ok了",component.get('v.EndUserAnswer').EndUser_7.List);
            target.classList.add('selected');

        }
    },
    // 修改 Dealer Answer
    changeDealerAnswer : function(component, event, helper) {
        console.log('123=================');
        let DealerAnswer = component.get('v.DealerAnswer');
        DealerAnswer.Dealer_3_2.answer = null;
        component.set('v.DealerAnswer', DealerAnswer);
    }
})