<aura:component controller="CCM_FillPurchaseInfoController" description="CCM_PartsOrder_FillAddress_Cmp" implements="flexipage:availableForAllPageTypes,forceCommunity:availableForAllPageTypes">
    
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    
    <aura:attribute name="currentStep" type="Integer" />
    <aura:attribute name="purchaseOrderId" type="String"/>
    <aura:attribute name="customerId" type="String"/>
    <aura:attribute name="userType" type="String"/>
    <aura:attribute name="contactId" type="String"/>
    <aura:attribute name="isDropAddressInFirst" type="Boolean"/>

    <aura:attribute name="billToAddress" type="Object" default=""/>
    <aura:attribute name="shipToAddress" type="Object" default=""/>
    <aura:attribute name="dropshipType" type="String" default=""/>
    <aura:attribute name="dropshipTypeOptions" type="List" default="[]"/>
    <aura:attribute name="dropshipAddress" type="Object" default=""/>
    <aura:attribute name="freightCost" type="Decimal"/>
    <aura:attribute name="insuranceFee" type="Decimal" default="0"/>
    <aura:attribute name="otherFee" type="Decimal" default="0"/>
    <aura:attribute name="DropShipName" type="String" default=""/>
    <aura:attribute name="DropShipAddress1" type="String" default=""/>
    <aura:attribute name="DropShipAddress2" type="String" default=""/>
    <aura:attribute name="DropShipPhone" type="String" default=""/>
    <aura:attribute name="DropShipCountry" type="String" default=""/>
    <aura:attribute name="DropShipCity" type="String" default=""/>
    <aura:attribute name="DropShipZip" type="String" default=""/>
    <aura:attribute name="DropShipState" type="String" default=""/>
    <aura:attribute name="POId" type="String"/>
    <aura:attribute name="actionType" type="String"/>
    <aura:attribute name="isDropShip" type="String"/>
    <aura:attribute name="disableFlag" type="Boolean" default="false"/>
    <aura:attribute name="dropshipAddressFlag" type="Boolean" default="false"/>
    <aura:attribute name="showDetailfield" type="Boolean" default="true"/>
    <aura:attribute name="returnStep" type="Integer"/>
    <aura:attribute name="maxStep" type="Integer"/>
    <aura:attribute name="customerType" type="String"/>
    <aura:attribute name="tips" type="String" default="trips"/>
    <aura:attribute name="allAutoAddress" type="Object" default=""/>
    <aura:attribute name="editFreightCost" type="Boolean"/>
    <aura:attribute name="showHelpText" type="Boolean"/>
    <aura:attribute name="currencySymbol" type="String"/>
    <aura:attribute name="showShip" type="Boolean" default="true"/>
    <aura:attribute name="isDE" type="Boolean" default="false"/>
    <aura:attribute name="defaultBillAddressInfo" type="String" default=""/>
    <aura:attribute name="defaultShipAddressInfo" type="String" default=""/>
    <aura:attribute name="defaultDropAddressInfo" type="String" default=""/>
    <aura:attribute name="pricingDate" type="String"/>
    <aura:attribute name="comments" type="String" />
    <aura:attribute name="instructionToDSVOptions" type="List" />
    <aura:attribute name="instructionToDSV" type="String" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler name="change" value="{!v.dropshipType}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.dropshipAddress}" action="{!c.changeDropshipAddress}"/>
    
    <lightning:card class="mainContent">
        <div>
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <c:CCM_Section title="{!$Label.c.CCM_DeliveryInformation}" expandable="true" >
                <div class="address-wrap">
                    <lightning:layout multipleRows="true" horizontalAlign="space">
                        <!-- Bill To Address -->
                        <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                            <c:CCM_Community_LookUp
                                fieldName="{!$Label.c.CCM_BillToAddress}"
                                selectedValue="{!v.billToAddress}"
                                onSelect="{!c.changeBillToAddress}"
                                customerId="{!v.customerId}"
                                contactId="{!v.contactId}"
                                isPortal="false"
                                aura:id="billToAddress"
                                class="field-required"
                            />
                            <div aura:id="billToAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                            <div class="address-content">
                                <aura:if isTrue="{!v.billToAddress.address.Street}">
                                    <p>{!v.billToAddress.address.Street}</p>
                                </aura:if>
                                <aura:if isTrue="{! (v.billToAddress.address.City || v.billToAddress.address.PostalCode)}">
                                    <p>
                                        <span>{!v.billToAddress.address.PostalCode}&nbsp;&nbsp;{!v.billToAddress.address.City}</span>
                                    </p>
                                </aura:if>
                                <aura:if isTrue="{!v.billToAddress.address.Country}">
                                    <p>{!v.billToAddress.address.Country}</p>
                                </aura:if>
                            </div>
                        </lightning:layoutItem>
                        <!-- Ship To Address -->
                        <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                            <c:CCM_Community_LookUp
                                fieldName="{!$Label.c.CCM_ShipToAddress}"
                                selectedValue="{!v.shipToAddress}"
                                onSelect="{!c.changeShipToAddress}"
                                customerId="{!v.customerId}"
                                contactId="{!v.contactId}"
                                isPortal="false"
                                aura:id="shipToAddress"
                                class="{!(v.showShip ? 'field-required' : '')}"
                                isDisabled="{!!(v.showShip)}"
                            />
                            <aura:if isTrue="{!(v.showShip)}">
                                <div aura:id="shipToAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                            </aura:if>
                            <div class="address-content">
                                <aura:if isTrue="{!v.shipToAddress.address.Street}">
                                    <p>{!v.shipToAddress.address.Street}</p>
                                </aura:if>
                                <aura:if isTrue="{! (v.shipToAddress.address.City || v.shipToAddress.address.PostalCode)}">
                                    <p>
                                        <span>{!v.shipToAddress.address.PostalCode}&nbsp;&nbsp;{!v.shipToAddress.address.City}</span>
                                    </p>
                                </aura:if>
                                <aura:if isTrue="{!v.shipToAddress.address.Country}">
                                    <p>{!v.shipToAddress.address.Country}</p>
                                </aura:if>
                            </div>
                        </lightning:layoutItem>
                        
                        <aura:if isTrue="{!(!v.isDropAddressInFirst)}">
                            <!-- Dropship Type -->
                            <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                <span class="combobox-label">{!$Label.c.CCM_DropshipType}</span>
                                <lightning:combobox
                                    label="​"
                                    name="Dropship Type​"
                                    value="{!v.dropshipType}" 
                                    options="{!v.dropshipTypeOptions}"
                                    aura:id="dropshipType"
                                    class="{!(v.isDropShip == 'Y' ? 'field-required' : '')}"
                                    disabled="{!v.disableFlag}"
                                    variant="label-hidden"
                                />
                                <aura:if isTrue="{!(v.isDropShip == 'Y')}">
                                    <div aura:id="dropshipType-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </aura:if>
                            </lightning:layoutItem>
                            <!-- Dropship Address -->
                            <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                <c:CCM_Community_LookUp
                                    fieldName="{!$Label.c.CCM_DropshipAddress}"
                                    selectedValue="{!v.dropshipAddress}"
                                    customerId="{!v.customerId}"
                                    onSelect="{!c.changeDropshipAddress}"
                                    aura:id="dropshipAddress"
                                    class="{!((v.disableFlag || v.dropshipAddressFlag) ? '' : 'field-required')}"
                                    isDisabled="{!v.disableFlag || v.dropshipAddressFlag}"
                                />
                                <aura:if isTrue="{!(!v.dropshipAddressFlag)}">
                                    <div aura:id="dropshipAddress-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </aura:if>
                                <aura:if isTrue="{!v.dropshipAddress.Id}">
                                    <div class="address-content">
                                        <aura:if isTrue="{!v.dropshipAddress.address.Street}">
                                            <p>{!v.dropshipAddress.address.Street}</p>
                                        </aura:if>
                                        <aura:if isTrue="{! (v.dropshipAddress.address.City || v.dropshipAddress.address.PostalCode)}">
                                            <p>
                                                <span>{!v.dropshipAddress.address.PostalCode}&nbsp;&nbsp;{!v.dropshipAddress.address.City}</span>
                                            </p>
                                        </aura:if>
                                        <aura:if isTrue="{!v.dropshipAddress.address.Country}">
                                            <p>{!v.dropshipAddress.address.Country}</p>
                                        </aura:if>
                                    </div>
                                </aura:if>
                            </lightning:layoutItem>
                            <!-- 详情字段 -->
                            <aura:if isTrue="{!v.showDetailfield}">
                                <!-- DropShipName -->
                                <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                    <lightning:input type="text" name="DropShipName" label="{!$Label.c.CCM_DropshipName}" value="{!v.DropShipName}" aura:id="dropShipName" class="field-required" onblur="{!c.changeDropShipName}"/>
                                    <div aura:id="dropShipName-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </lightning:layoutItem>
                                <!-- Dropship Street & Street No. -->
                                <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                    <lightning:input type="text" name="dropshipStreetAndStreetNo" label="{!$Label.c.CCM_DropshipStreetAndStreetNo}" value="{!v.DropShipAddress1}" aura:id="dropshipStreetAndStreetNo" class="field-required" onblur="{!c.changeDropshipStreetAndStreetNo}"/>
                                    <div aura:id="dropshipStreetAndStreetNo-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </lightning:layoutItem>
                                <!-- DropShipCountry -->
                                <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                    <c:CCM_Community_LookUp
                                        fieldName="{!$Label.c.CCM_DropshipCountry}"
                                        selectedValue="{!v.DropShipCountry}"
                                        onSelect="{!c.changeDropShipCountry}"
                                        class="field-required"
                                        aura:id="dropShipCountry"
                                    />
                                    <div aura:id="dropShipCountry-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </lightning:layoutItem>
                                <!-- DropShipZip -->
                                <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                    <lightning:input type="text" name="DropShipZip" label="{!$Label.c.CCM_DropshipPostalCode}" value="{!v.DropShipZip}" aura:id="dropShipZip" class="field-required" onblur="{!c.changeDropShipZip}"/>
                                    <div aura:id="dropShipZip-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </lightning:layoutItem>
                                <!-- DropShipCity -->
                                <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                    <lightning:input type="text" name="DropShipCity" label="{!$Label.c.CCM_DropshipCity}" value="{!v.DropShipCity}" aura:id="dropShipCity" class="field-required" onblur="{!c.changeDropShipCity}"/>
                                    <div aura:id="dropShipCity-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </lightning:layoutItem>
                                
                                <!-- DropShipState -->
                                <!-- <lightning:layoutItem padding="around-small" size="6">
                                    <lightning:input type="text" name="DropShipState" label="Dropship Province/State" value="{!v.DropShipState}"/>
                                </lightning:layoutItem> -->
                                <!-- 占位 -->
                                <lightning:layoutItem padding="around-small" size="6">
                                </lightning:layoutItem>
                            </aura:if>
                        </aura:if>
                    </lightning:layout>
                </div>
            </c:CCM_Section>
            <c:CCM_Section title="Fee Information" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Freight Cost -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <aura:if isTrue="{!v.editFreightCost}">
                                <lightning:input type="number" name="freightcost" label="{!$Label.c.CCM_FreightCost}" value="{!v.freightCost}" step="0.01" disabled="{! (v.userType != 'InsideSales')}"/>
                                <aura:set attribute="else">
                                    <lightning:input type="number" name="freightcost" label="{!$Label.c.CCM_FreightCost}" value="{!v.freightCost}" step="0.01" disabled="true"/>
                                </aura:set>
                            </aura:if>
                            <aura:if isTrue="{!v.showHelpText}">
                                <aura:if isTrue="{!(v.customerType == 'DE Customer')}">
                                    <aura:if isTrue="{!(v.isDE == 'EN')}">
                                        <div class="hlep-wrap">
                                            <lightning:helptext content="Order value: 0-150€ -> Freight cost: 7€;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        Order value: 150-500€ -> Freight cost: 13€​​​​;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        Order value: 500-1000€ -> Freight cost: 16.50€​​​​​​​​;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        Order value: over 1000€ -> Freight cost: 0€​;
                                            ​"/>
                                        </div>
                                        <aura:set attribute="else">
                                            <div class="hlep-wrap">
                                                <lightning:helptext content="Auftragswert 0-150€ --> Frachtkosten: 7€;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            Auftragswert 150-500€ --> Frachtkosten: 13€​​​​;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                            Auftragswert 500-1.000€ --> Frachtkosten: 16,50€​​​​​​​​;&nbsp;&nbsp;&nbsp;
                                                                            Auftragswert mehr als 1.000€ --> Frachtkosten: 0€​;
                                                ​"/>
                                            </div>
                                        </aura:set>
                                    </aura:if>
                                </aura:if>
                                <aura:if isTrue="{!(v.customerType == 'PBE')}">
                                    <div class="hlep-wrap">
                                        <lightning:helptext content="{!$Label.c.CCM_FreightCostTip1}"/>
                                    </div>
                                </aura:if>
                                <aura:if isTrue="{!(v.customerType == 'HKL')}">
                                    <div class="hlep-wrap">
                                        <lightning:helptext content="{!$Label.c.CCM_FreightCostTip1}"/>
                                    </div>
                                </aura:if>
                            </aura:if>
                        </div>
                    </lightning:layoutItem>

                    <!-- Insurance Fee -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <lightning:input type="number" name="insuranceFee" label="{!$Label.c.CCM_InsuranceFee}" value="{!v.insuranceFee}" step="0.01" disabled="{! (v.userType != 'InsideSales')}" onchange="{!c.changeInsuranceFee}"/>
                        </div>
                    </lightning:layoutItem>
                    <!-- Other Fee -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <lightning:input type="number" name="otherFee" label="{!$Label.c.CCM_OtherFee}" value="{!v.otherFee}" step="0.01" disabled="{! (v.userType != 'InsideSales')}" onchange="{!c.changeOtherFee}"/>
                        </div>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>

            <c:CCM_Section title="{!$Label.c.CCM_Comments}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Comments -->
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="freight-cost-wrap">
                            <lightning:input type="text" name="comments" label="{!$Label.c.CCM_Comments}" value="{!v.comments}" />
                        </div>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                        <span class="combobox-label">{!$Label.c.CCM_InstructionToDSV}</span>
                        <lightning:combobox
                            label="​"
                            name="​InstructionToDsv"
                            value="{!v.instructionToDSV}" 
                            options="{!v.instructionToDSVOptions}"
                            aura:id="​InstructionToDsv"
                            variant="label-hidden"
                        />
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
        </div>

        <aura:set attribute="footer">
            <div class="footer-wrap">
                <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancel}" />
                <lightning:button class="previous-btn" variant="brand-outline" label="{!$Label.c.CCM_Previous}" onclick="{!c.previousStep}" />
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Next}" onclick="{!c.nextStep}" />
            </div>
        </aura:set>
    </lightning:card>
</aura:component>