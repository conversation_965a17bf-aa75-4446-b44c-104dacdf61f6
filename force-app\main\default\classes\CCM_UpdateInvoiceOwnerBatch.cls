public with sharing class CCM_UpdateInvoiceOwnerBatch implements Database.Batchable<sObject>{
    private String query;
    public CCM_UpdateInvoiceOwnerBatch() {
        this.query = 'select Invoice__c, Invoice__r.Order__r.Sales_Rep__c from Invoice_Item__c where OwnerId = \'00568000004CBNVAA4\' and Invoice__r.Order__r.RecordType.DeveloperName NOT IN (\'Warranty_Claim\', \'Warranty_Order\') and Invoice__r.Order__r.Sales_Rep__c != null';
    }

    public CCM_UpdateInvoiceOwnerBatch(String query) {
        this.query = query;
    }

    public Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator(this.query);
    }

    public void execute(Database.BatchableContext BC, list<Sobject> scope) {
        List<Invoice_Item__c> lstInvoiceItem = (List<Invoice_Item__c>)scope;
        List<Invoice__c> invoiceList = new List<Invoice__c>();
        for(Invoice_Item__c item : lstInvoiceItem) {
            item.OwnerId = item.Invoice__r.Order__r.Sales_Rep__c;
            Invoice__c invoice = new Invoice__c();
            invoice.Id = item.Invoice__c;
            invoice.OwnerId = item.Invoice__r.Order__r.Sales_Rep__c;
            invoiceList.add(invoice);
        }
        update lstInvoiceItem;
        update invoiceList;
    }

    public void finish(Database.BatchableContext BC) {

    }
}