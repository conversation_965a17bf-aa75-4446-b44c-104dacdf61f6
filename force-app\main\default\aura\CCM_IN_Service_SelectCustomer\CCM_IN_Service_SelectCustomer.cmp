<aura:component controller="CCM_RequestPurchaseOrderController" description="CCM_IN_Service_SelectCustomer" access="global">
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" />
    <aura:attribute name="customerObj" type="Object" default="{}"/>
    <aura:attribute name="customerId" type="String"/>
    <aura:attribute name="contactObj" type="Object" default="{}"/>
    <aura:attribute name="contactId" type="String"/>
    <aura:attribute name="customerName" type="String"/>
    <aura:attribute name="pricingDate" type="String"/>
    <aura:attribute name="purchaseOrderId" type="String"/>
    <aura:attribute name="isDropShip" type="String"/>
    <aura:attribute name="userType" type="String"/>
    <aura:attribute name="actionType" type="String"/>
    <aura:attribute name="PodetailInfo" type="Map"/>
    <aura:attribute name="returnStep" type="Integer"/>
    <aura:attribute name="maxStep" type="Integer"/>

    <aura:attribute name="customerUrl" type="String" default=""/>
    <aura:attribute name="requireFlag" type="Boolean" default="true"/>
    <aura:attribute name="disableFlag" type="Boolean" default="false"/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="hasDropShipAddress" type="Boolean" default="false"/>
    <aura:attribute name="isDropShipOpt" type="List" default="[]"/>
    <aura:attribute name="orderTypeOpt" type="List" default="[{'label': 'Regular Order', 'value': 'Regular_Order'},{'label': 'Pre_season Order', 'value': 'Pre_season_Order'}]"/>
    <aura:attribute name="orderStatus" type="String" default=""/>
    <aura:attribute name="orderStatusOpt" type="List" default="[{'label': 'Review in Process', 'value': 'review in process'},{'label': 'Submitted', 'value': 'Submitted'}]"/>

    <aura:attribute name="warehouseOptions" type="List" default="[]"/>
    <aura:attribute name="warehouse" type="String" default=""/>
    <aura:attribute name="recordTypeName" type="String"/>
    <aura:attribute name="customerPO" type="String" default=""/>
    <aura:attribute name="customerInfo" type="Object" default="{}"/>
    <aura:attribute name="editDropShipOrder" type="Boolean" default="false"/>
    <aura:attribute name="currencySymbol" type="String" default="€"/>

    <aura:attribute name="isCollectiveCustomer" type="Boolean" default="false"/>
    <aura:attribute name="dropshipTypeOptions" type="List" default="[]"/>
    <!-- <aura:attribute name="disableFlag" type="Boolean" default="false"/> -->

    <aura:attribute name="billToAddressId" type="String" default=""/>
    <aura:attribute name="shipToAddressId" type="String" default=""/>
    <aura:attribute name="dropshipAddress" type="Map" default="{}"/>
    <aura:attribute name="dropshipAddressId" type="String" default=""/>
    <aura:attribute name="dropshipType" type="String" default=""/>
    <aura:attribute name="dropshipAddressFlag" type="Boolean" default="false"/>
    <aura:attribute name="showDetailfield" type="Boolean" default="false"/>
    <aura:attribute name="showDropAddress" type="Boolean" default="false"/>
    <aura:attribute name="DropShipName" type="String" default=""/>
    <aura:attribute name="DropShipAddress1" type="String" default=""/>
    <aura:attribute name="DropShipAddress2" type="String" default=""/>
    <aura:attribute name="DropShipPhone" type="String" default=""/>
    <aura:attribute name="DropShipCountry" type="String" default=""/>
    <aura:attribute name="DropShipCity" type="String" default=""/>
    <aura:attribute name="DropShipZip" type="String" default=""/>
    <aura:attribute name="DropShipState" type="String" default=""/>
    <aura:attribute name="freightCost" type="String" default=""/>
    <aura:attribute name="insuranceFee" type="String" default=""/>
    <aura:attribute name="otherFee" type="String" default=""/>
    <aura:attribute name="isDropAddressInFirst" type="Boolean" default="false"/>
    <aura:attribute name="defaultDropAddressInfo" type="String" default=""/>

    
    <!-- <aura:handler name="change" value="{!v.customerObj}" action="{!c.selectCustomer}"/> -->
    
    <aura:handler name="change" value="{!v.dropshipType}" action="{!c.changeDropshipType}"/>
    <aura:handler name="change" value="{!v.isCollectiveCustomer}" action="{!c.validCollectiveCustomer}"/>
    <aura:handler name="change" value="{!v.isDropShip}" action="{!c.validCollectiveCustomer}"/>
    <aura:handler name="change" value="{!v.contactObj}" action="{!c.checkAutoDropAddress}"/>
    <aura:handler name="change" value="{!v.customerObj}" action="{!c.checkAutoDropAddress}"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <lightning:card class="mainContent">
        <div class="c-container" style="padding: 10px">
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <!-- 筛选条件 -->
            <lightning:layout horizontalAlign="space" verticalAlign="center" class="search-wrap">
                <!-- Select Customer -->
                <lightning:layoutItem alignmentBump="right" size="2" class="required-wrap">
                    <aura:if isTrue="{!v.actionType == 'edit'}">
                        <lightning:input label="{!$Label.c.CCM_SelectCustomer}" value="{!v.PodetailInfo.CustomerName}" disabled="true"/>
                        <aura:set attribute="else">
                            <c:CCM_Community_LookUp 
                                fieldName="{!$Label.c.CCM_SelectCustomer}"
                                selectedValue="{!v.customerObj}"
                                onSelect="{!c.selectCustomer}"
                                aura:id="customer"
                                class="field-required"
                            />
                            <div aura:id="customer-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                        </aura:set>
                    </aura:if>
                </lightning:layoutItem>
                <!-- Select Contact -->
                <!-- <lightning:layoutItem alignmentBump="right" size="2" class="required-wrap">
                    <aura:if isTrue="{!v.actionType == 'edit'}">
                        <lightning:input label="Select Contact" value="{!v.PodetailInfo.contactName}" disabled="true"/>
                        <aura:set attribute="else">
                            <c:CCM_Community_LookUp 
                                fieldName="Select Contact"
                                selectedValue="{!v.contactObj}"
                                onSelect="{!c.selectContact}"
                                accId="{!v.customerId}"
                                aura:id="contact"
                                class="field-required"
                            />
                            <div aura:id="contact-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                        </aura:set>
                    </aura:if>
                </lightning:layoutItem> -->
                <!-- Warehouse -->
                <lightning:layoutItem alignmentBump="right" size="2">
                    <lightning:combobox class="ccm_display"
                        name="warehouse" 
                        value="{!v.warehouse}" 
                        options="{!v.warehouseOptions}"
                        label="{!$Label.c.CCM_Warehouse}"
                        disabled="{! (v.userType != 'InsideSales')}"
                    />
                </lightning:layoutItem>
                <!-- Customer PO -->
                <lightning:layoutItem alignmentBump="right" size="2">
                    <lightning:input label="{!$Label.c.CCM_CustomerPO}" value="{!v.customerPO}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255" onblur="{!c.checkCustomerPo}"/>
                </lightning:layoutItem>
                <!-- Is DropShip Order? -->
                <lightning:layoutItem alignmentBump="right" size="2">
                    <lightning:combobox class="ccm_display"
                        name="isDropShip" 
                        value="{!v.isDropShip}" 
                        options="{!v.isDropShipOpt}" 
                        label="{!$Label.c.Order_IsDropship}"
                        disabled="{!v.editDropShipOrder}"
                    />
                </lightning:layoutItem>
                <!-- Pricing Date -->
                <aura:if isTrue="{!v.userType == 'InsideSales'}">
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:input disabled="{! (v.userType != 'InsideSales')}" type="date" class="date-box-item slds-p-top--xx-small" label="{!$Label.c.CCM_PricingDate}" value="{!v.pricingDate}"/>
                    </lightning:layoutItem>
                </aura:if>
            </lightning:layout>
            <lightning:layout horizontalAlign="space" verticalAlign="center" class="search-wrap">
                <!-- Pricing Date -->
                <!-- <aura:if isTrue="{!v.userType == 'InsideSales'}">
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:input disabled="{! (v.userType != 'InsideSales')}" type="date" class="date-box-item slds-p-top_xx-small" label="{!$Label.c.CCM_PricingDate}" value="{!v.pricingDate}"/>
                    </lightning:layoutItem>
                </aura:if> -->
                <aura:if isTrue="{!v.actionType == 'edit'}">
                    <!-- Order Type -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:combobox
                            name="orderType"
                            value="{!v.recordTypeName}" 
                            options="{!v.orderTypeOpt}" 
                            label="{!$Label.c.CCM_OrderType}"
                        />
                    </lightning:layoutItem>
                    <!-- Order Status -->
                    <lightning:layoutItem alignmentBump="right" size="2">
                        <lightning:combobox
                            name="OrderStatus"
                            value="{!v.orderStatus}" 
                            options="{!v.orderStatusOpt}" 
                            label="Order Status"
                        />
                    </lightning:layoutItem>
                    <lightning:layoutItem alignmentBump="right" size="2">
                    </lightning:layoutItem>
                    <lightning:layoutItem alignmentBump="right" size="2">
                    </lightning:layoutItem>
                </aura:if>
            </lightning:layout>
            <div>
                <c:CCM_Section title="Customer Detail" expandable="true">
                    <div class="label-wrap">
                        <lightning:layout horizontalAlign="space" multipleRows="true">
                            <lightning:layoutItem size="6">
                                <!-- Customer Name -->
                                <c:CCM_Field label="{!$Label.c.CCM_CustomerName}">
                                    <a href="{!v.customerUrl}" target="_blank">{!v.customerName}</a>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Customer Owner -->
                                <c:CCM_Field label="{!$Label.c.CCM_CustomerOwner}">
                                    {!v.customerInfo.Owner.Name}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Account Number -->
                                <c:CCM_Field label="{!$Label.c.CCM_AccountNumber}">
                                    {!v.customerInfo.AccountNumber}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Customer Type -->
                                <c:CCM_Field label="{!$Label.c.CCM_CustomerType}">
                                    {!v.customerInfo.Distributor_or_Dealer__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Tier -->
                                <c:CCM_Field label="{!$Label.c.CCM_Tier}">
                                    {!v.customerInfo.Tier__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Sales Channel -->
                                <c:CCM_Field label="{!$Label.c.CCM_SalesChannel}">
                                    {!v.customerInfo.Sales_Channel__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Classification 1 -->
                                <c:CCM_Field label="{!$Label.c.CCM_Classification1}">
                                    {!v.customerInfo.Classification1__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Classification 2 -->
                                <c:CCM_Field label="{!$Label.c.CCM_Classification2}">
                                    {!v.customerInfo.Classification2__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Customer Business Type -->
                                <c:CCM_Field label="{!$Label.c.CCM_CustomerBusinessType}">
                                    {!v.customerInfo.Customer_Business_Type__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Intended Brand -->
                                <c:CCM_Field label="{!$Label.c.CCM_IntendedBrand}">
                                    {!v.customerInfo.Intended_Brand__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Association Type -->
                                <c:CCM_Field label="{!$Label.c.CCM_AssociationType}">
                                    {!v.customerInfo.Association_Type__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Association Name -->
                                <c:CCM_Field label="{!$Label.c.CCM_AssociationName}">
                                    {!v.customerInfo.Association_Group__r.Name}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- Association Number -->
                                <c:CCM_Field label="{!$Label.c.CCM_AssoicationMemberNo}">
                                    {!v.customerInfo.Association_Membership_No__c}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="6">
                                <!-- 占位 -->
                            </lightning:layoutItem>
                        </lightning:layout>
                    </div>
                </c:CCM_Section>
                <aura:if isTrue="{!v.showDropAddress}">
                    <!-- Dropship Address -->
                    <c:CCM_Section title="{!$Label.c.CCM_DeliveryInformation}" expandable="true" >
                        <div class="address-wrap">
                            <lightning:layout multipleRows="true" horizontalAlign="space">
                                <!-- Dropship Type -->
                                <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                    <span class="combobox-label">{!$Label.c.CCM_DropshipType}</span>
                                    <lightning:combobox
                                        label="​"
                                        name="Dropship Type​"
                                        value="{!v.dropshipType}" 
                                        options="{!v.dropshipTypeOptions}"
                                        aura:id="dropshipType"
                                        class="field-required"
                                        variant="label-hidden"
                                    />
                                    <div aura:id="dropshipType-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </lightning:layoutItem>
                                <!-- Dropship Address -->
                                <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                    <c:CCM_Community_LookUp
                                        fieldName="{!$Label.c.CCM_DropshipAddress}"
                                        selectedValue="{!v.dropshipAddress}"
                                        customerId="{!v.customerId}"
                                        onSelect="{!c.changeDropshipAddress}"
                                        aura:id="dropshipAddress"
                                        class="{!v.dropshipAddressFlag ? '' : 'field-required'}"
                                        isDisabled="{!v.dropshipAddressFlag}"
                                    />
                                    <aura:if isTrue="{!(!v.dropshipAddressFlag)}">
                                        <div aura:id="dropshipAddress-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </aura:if>
                                    <aura:if isTrue="{!v.dropshipAddress.Id}">
                                        <div class="address-content">
                                            <aura:if isTrue="{!v.dropshipAddress.address.Street}">
                                                <p>{!v.dropshipAddress.address.Street}</p>
                                            </aura:if>
                                            <aura:if isTrue="{! (v.dropshipAddress.address.City || v.dropshipAddress.address.PostalCode)}">
                                                <p>
                                                    <span>{!v.dropshipAddress.address.PostalCode}&nbsp;&nbsp;{!v.dropshipAddress.address.City}</span>
                                                </p>
                                            </aura:if>
                                            <aura:if isTrue="{!v.dropshipAddress.address.Country}">
                                                <p>{!v.dropshipAddress.address.Country}</p>
                                            </aura:if>
                                        </div>
                                    </aura:if>
                                </lightning:layoutItem>
                                <!-- 详情字段 -->
                                <aura:if isTrue="{!v.showDetailfield}">
                                    <!-- DropShipName -->
                                    <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                        <lightning:input type="text" name="DropShipName" label="{!$Label.c.CCM_DropshipName}" value="{!v.DropShipName}" aura:id="dropShipName" class="field-required" onblur="{!c.changeDropShipName}"/>
                                        <div aura:id="dropShipName-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </lightning:layoutItem>
                                    <!-- Dropship Street & Street No. -->
                                    <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                        <lightning:input type="text" name="dropshipStreetAndStreetNo" label="{!$Label.c.CCM_DropshipStreetAndStreetNo}" value="{!v.DropShipAddress1}" aura:id="dropshipStreetAndStreetNo" class="field-required" onblur="{!c.changeDropshipStreetAndStreetNo}"/>
                                        <div aura:id="dropshipStreetAndStreetNo-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </lightning:layoutItem>
                                    <!-- DropShipCountry -->
                                    <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                        <c:CCM_Community_LookUp
                                            fieldName="{!$Label.c.CCM_DropshipCountry}"
                                            selectedValue="{!v.DropShipCountry}"
                                            onSelect="{!c.changeDropShipCountry}"
                                            class="field-required"
                                            aura:id="dropShipCountry"
                                        />
                                        <div aura:id="dropShipCountry-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </lightning:layoutItem>
                                    <!-- DropShipZip -->
                                    <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                        <lightning:input type="text" name="DropShipZip" label="{!$Label.c.CCM_DropshipPostalCode}" value="{!v.DropShipZip}" aura:id="dropShipZip" class="field-required" onblur="{!c.changeDropShipZip}"/>
                                        <div aura:id="dropShipZip-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </lightning:layoutItem>
                                    <!-- DropShipCity -->
                                    <lightning:layoutItem padding="around-small" size="6" class="required-wrap">
                                        <lightning:input type="text" name="DropShipCity" label="{!$Label.c.CCM_DropshipCity}" value="{!v.DropShipCity}" aura:id="dropShipCity" class="field-required" onblur="{!c.changeDropShipCity}"/>
                                        <div aura:id="dropShipCity-error-required" class="drop-error error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </lightning:layoutItem>
                                    
                                    <!-- DropShipState -->
                                    <!-- <lightning:layoutItem padding="around-small" size="6">
                                        <lightning:input type="text" name="DropShipState" label="Dropship Province/State" value="{!v.DropShipState}"/>
                                    </lightning:layoutItem> -->
                                    <!-- 占位 -->
                                    <lightning:layoutItem padding="around-small" size="6">
                                    </lightning:layoutItem>
                                </aura:if>
                            </lightning:layout>
                        </div>
                    </c:CCM_Section>
                </aura:if>
            </div>
        </div>
        <aura:set attribute="footer">
            <div>
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.cancel}" />
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Next}" title="{!$Label.c.CCM_Next}" onclick="{!c.nextStep}" />
            </div>
        </aura:set>
    </lightning:card>
</aura:component>