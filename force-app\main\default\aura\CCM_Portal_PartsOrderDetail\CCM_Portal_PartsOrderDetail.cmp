<aura:component controller="CCM_PurchaseOrderPreview" description="CCM_PartsOrder_Detail"  implements="forceCommunity:availableForAllPageTypes,flexipage:availableForAllPageTypes,force:hasRecordId" access="global">
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    
    <aura:attribute name="currentStep" type="Integer" />
    <aura:attribute name="purchaseOrderId" type="Integer"/>
    <aura:attribute name="customerId" type="Integer"/>
    <aura:attribute name="currencySymbol" type="String"/>
    <aura:attribute name="baseInfo" type="Object" default="{}"/>
    <aura:attribute name="TotalDueAmount" type="Decimal" default="0"/>
    <aura:attribute name="CountLine" type="Integer" default=""/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="actionType" type="String"/>
    <aura:attribute name="userType" type="String"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <lightning:card class="mainContent slds-p-horizontal_x-large">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
        <!-- Basic Information -->
        <c:CCM_Section title="{!$Label.c.CCM_BasicInformation}" expandable="true">
            <lightning:layout multipleRows="true">
                <!-- Your Purchase Order# -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.CCM_YourPurchaseOrder}">
                        {!v.baseInfo.purchaseOrderNumber}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Customer Number -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.CCM_CustomerName}">
                        {!v.baseInfo.CustomerName}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Customer PO -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.CCM_CustomerPO}">
                        {!v.baseInfo.CustomerPo}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Is Dropship Order? -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.Order_IsDropship}">
                        {!v.baseInfo.IsDropship ? 'YES' : 'NO'}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Authorized Brand -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.CCM_AuthorizedBrand}">
                        {!v.baseInfo.AuthBrand}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Payment Term -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.Order_PaymentTerm}">
                        {!v.baseInfo.PaymentTerm}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Inco Term -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom-small">
                    <c:CCM_Field label="{!$Label.c.CCM_Incoterm}">
                        {!v.baseInfo.IncoTerm}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Expected Delivery Date -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.CCM_ExpectedDeliveryDate}">
                        {!v.baseInfo.ExpectedDeliveryDate}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Pricing Date -->
                <!-- <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom-small">
                    <c:CCM_Field label="Pricing Date">
                        {!v.baseInfo.PricingDate}
                    </c:CCM_Field>
                </lightning:layoutItem> -->
                <!-- Bill To Address -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom-small">
                    <c:CCM_Field label="{!$Label.c.CCM_BillToAddress}">
                        <aura:if isTrue="{!v.baseInfo.BillToAddressInfo.CompanyName}">
                            <span>{!v.baseInfo.BillToAddressInfo.CompanyName}</span><br/>
                        </aura:if>
                        <aura:if isTrue="{!v.baseInfo.BillToAddressInfo.Street}">
                            <span>{!v.baseInfo.BillToAddressInfo.Street}</span><br/>
                        </aura:if>
                        <aura:if isTrue="{!(v.baseInfo.BillToAddressInfo.City) || (v.baseInfo.BillToAddressInfo.PostalCode)}">
                            <span>{!v.baseInfo.BillToAddressInfo.PostalCode}&nbsp;&nbsp;{!v.baseInfo.BillToAddressInfo.City}</span><br/>
                        </aura:if>
                        <aura:if isTrue="{!v.baseInfo.BillToAddressInfo.Country}">
                            <span>{!v.baseInfo.BillToAddressInfo.Country}</span>
                        </aura:if>
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Ship To Address -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom-small">
                    <c:CCM_Field label="{!$Label.c.CCM_ShipToAddress}">
                        <aura:if isTrue="{!v.baseInfo.ShipToAddressInfo.CompanyName}">
                            <span>{!v.baseInfo.ShipToAddressInfo.CompanyName}</span><br/>
                        </aura:if>
                        <aura:if isTrue="{!v.baseInfo.ShipToAddressInfo.Street}">
                            <span>{!v.baseInfo.ShipToAddressInfo.Street}</span><br/>
                        </aura:if>
                        <aura:if isTrue="{!(v.baseInfo.ShipToAddressInfo.City) || (v.baseInfo.ShipToAddressInfo.PostalCode)}">
                            <span>{!v.baseInfo.ShipToAddressInfo.PostalCode}&nbsp;&nbsp;{!v.baseInfo.ShipToAddressInfo.City}</span><br/>
                        </aura:if>
                        <aura:if isTrue="{!v.baseInfo.ShipToAddressInfo.Country}">
                            <span>{!v.baseInfo.ShipToAddressInfo.Country}</span>
                        </aura:if>
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Est Order Volume -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.CCM_EstOrderVolume}">
                        {!v.baseInfo.EstOrderVolume ? v.baseInfo.EstOrderVolume + ' m³' : ''}
                    </c:CCM_Field>
                </lightning:layoutItem>
                <!-- Est Weight -->
                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="{!$Label.c.CCM_EstOrderWeight}">
                        {!v.baseInfo.EstWeight ? v.baseInfo.EstWeight + ' kg' : ''}
                    </c:CCM_Field>
                </lightning:layoutItem>
            </lightning:layout>
        </c:CCM_Section>
        
        <!-- Order Item Information -->
        <c:CCM_Section title="{!$Label.c.CCM_OrderItemInformation}" expandable="true" >
            <div class="slds-p-left_medium slds-p-right--medium">
                <div class="table-wrap">
                    <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productTable" role="grid">
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="width: 80px;">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Action}">{!$Label.c.CCM_Action}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- No. -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_No}">{!$Label.c.CCM_No}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Product Description -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Model #  -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Model}">{!$Label.c.CCM_Model}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- UOM -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_UOM}">{!$Label.c.CCM_UOM}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- haibo: 添加行状态 -->
                                <!-- Order Line Status -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="Order Line Status">Order Line Status</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Request Date -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_RequestDate}">{!$Label.c.CCM_RequestDate}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Schedule Ship Date -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_ScheduleShipDate}">{!$Label.c.CCM_ScheduleShipDate}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Qty -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Qty}">{!$Label.c.CCM_Qty}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- List Price -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Standard Discount -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_StandardDiscount}">{!$Label.c.CCM_StandardDiscount}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Additional Discount -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_AdditionalDiscount}">{!$Label.c.CCM_AdditionalDiscount}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Unit Net Price -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_UnitNetPrice}">{!$Label.c.CCM_UnitNetPrice}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- Total Net Price -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_TotalNetPrice}">{!$Label.c.CCM_TotalNetPrice}</span>
                                        </div>
                                    </a>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                                <tr aria-selected="false" id="{!index}" data-expanded="false" class="slds-hint-parent" onclick="{!c.showToolList}">
                                    <!-- Action -->
                                    <td scope="row">
                                        <div class="slds-truncate" title="">
                                            <aura:if isTrue="{!orderItem.lstProductTools.length > 0}">
                                                <lightning:icon iconName="utility:chevrondown" size="xx-small" class="collapse_icn"/>
                                            </aura:if>
                                        </div>
                                    </td>
                                    <!-- No. -->
                                    <td scope="row">
                                        <div class="slds-truncate" title="">
                                            {!index + 1}
                                        </div>
                                    </td>
                                    <!-- Product Description -->
                                    <td role="gridcell" title="{!orderItem.ProductDescription}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            {!orderItem.ProductDescription}
                                        </div>
                                    </td>
                                    <!-- Model #  -->
                                    <td role="gridcell" title="{!orderItem.Model}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            {!orderItem.Model}
                                        </div>
                                    </td>
                                    <!-- UOM -->
                                    <td role="gridcell" title="{!orderItem.UOM}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>{!orderItem.UOM}</span>
                                        </div>
                                    </td>
                                    <!-- haibo: 添加行状态 -->
                                    <!-- Order Line Status -->
                                    <td role="gridcell" title="{!orderItem.OrderLineStatus}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>{!orderItem.OrderLineStatus}</span>
                                        </div>
                                    </td>
                                    <!-- Request Date -->
                                    <td role="gridcell" title="{!orderItem.RequestDate}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            {!orderItem.RequestDate}
                                        </div>
                                    </td>
                                    <!-- Schedule Ship Date -->
                                    <td role="gridcell" title="{!orderItem.scheduShipDate}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            {!orderItem.scheduShipDate}
                                        </div>
                                    </td>
                                    <!-- Qty -->
                                    <td role="gridcell" title="{!orderItem.Qty}">
                                        <div class="slds-truncate clear-user-agent-styles">
                                            {!orderItem.Qty}
                                        </div>
                                    </td>
                                    <!-- List Price -->
                                    <td role="gridcell" title="{!orderItem.ListPrice}">
                                        <div class="slds-truncate clear-user-agent-styles">
                                            <lightning:formattedNumber value="{!orderItem.ListPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                        </div>
                                    </td>
                                    <!-- Standard Discount -->
                                    <td role="gridcell" title="{!orderItem.standDiscount}">
                                        <div class="slds-truncate">
                                            {!orderItem.standDiscount ? orderItem.standDiscount + '%' : ''}
                                        </div>
                                    </td>
                                    <!-- Additional Discount -->
                                    <td role="gridcell" title="{!orderItem.Discount}">
                                        <div class="slds-truncate">
                                            {!orderItem.Discount ? orderItem.Discount + '%' : ''}
                                        </div>
                                    </td>
                                    <!-- Unit Net Price -->
                                    <td role="gridcell" title="{!orderItem.UnitNetPrice}">
                                        <div class="slds-truncate clear-user-agent-styles">
                                            <lightning:formattedNumber value="{!orderItem.UnitNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                        </div>
                                    </td>
                                    <!-- Total Net Price -->
                                    <td role="gridcell" title="{!orderItem.TotalNetPrice}">
                                        <div class="slds-truncate clear-user-agent-styles">
                                            <lightning:formattedNumber value="{!orderItem.TotalNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                        </div>
                                    </td>
                                </tr>
                                <!-- product info -->
                                <aura:if isTrue="{!orderItem.lstProductTools.length > 0}">
                                    <tr aria-selected="false" class="slds-hint-parent" id="{!('tool' + index)}" style="display: none">
                                        <td colspan="14" style="padding: 0;">
                                            <div class="tool-table-wrap">
                                                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productToolTable" role="grid">
                                                    <thead>
                                                        <tr class="slds-line-height_reset">
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="width: 80px;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_Action}">{!$Label.c.CCM_Action}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- No. -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_No}">{!$Label.c.CCM_No}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Product Description -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Model #  -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_Model}">{!$Label.c.CCM_Model}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Request Date -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_RequestDate}">{!$Label.c.CCM_RequestDate}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Qty -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_Qty}">{!$Label.c.CCM_Qty}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- List Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Discount -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_Discount}">{!$Label.c.CCM_Discount}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Unit Net Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_UnitNetPrice}">{!$Label.c.CCM_UnitNetPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Total Net Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_TotalNetPrice}">{!$Label.c.CCM_TotalNetPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <aura:iteration items="{!orderItem.lstProductTools}" var="toolItem" indexVar="toolIndex">
                                                            <tr aria-selected="false" class="slds-hint-parent">
                                                                <!-- Action -->
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                    </div>
                                                                </td>
                                                                <!-- No. -->
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                        {!toolIndex + 1}
                                                                    </div>
                                                                </td>
                                                                <!-- Product Description -->
                                                                <td role="gridcell" title="{!toolItem.ProductDescription}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!toolItem.ProductDescription}
                                                                    </div>
                                                                </td>
                                                                <!-- Model #  -->
                                                                <td role="gridcell" title="{!toolItem.Model}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!toolItem.Model}
                                                                    </div>
                                                                </td>
                                                                <!-- Request Date -->
                                                                <td role="gridcell" title="{!toolItem.RequestDate}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!toolItem.RequestDate}
                                                                    </div>
                                                                </td>
                                                                <!-- Pricing date -->
                                                                <!-- <td role="gridcell" title="Pricing date">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!toolItem.Pricingdate}
                                                                    </div>
                                                                </td> -->
                                                                <!-- Qty -->
                                                                <td role="gridcell" title="{!toolItem.Qty}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        {!toolItem.Qty}
                                                                    </div>
                                                                </td>
                                                                <!-- List Price -->
                                                                <td role="gridcell" title="{!toolItem.ListPrice}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <lightning:formattedNumber value="{!toolItem.ListPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Discount -->
                                                                <td role="gridcell" title="{!toolItem.Discount}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!toolItem.Discount}
                                                                    </div>
                                                                </td>
                                                                <!-- Unit Net Price -->
                                                                <td role="gridcell" title="{!toolItem.UnitNetPrice}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <lightning:formattedNumber value="{!toolItem.UnitNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Total Net Price -->
                                                                <td role="gridcell" title="{!toolItem.TotalNetPrice}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <lightning:formattedNumber value="{!toolItem.TotalNetPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Remark -->
                                                                <!-- <td role="gridcell" title="Remark">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        {!toolItem.Remark}
                                                                    </div>
                                                                </td> -->
                                                            </tr>
                                                        </aura:iteration>
                                                    </tbody>
                                                </table>
                                            </div>
                                            
                                        </td>
                                    </tr>
                                </aura:if>
                            </aura:iteration>
                        </tbody>
                    </table>
                </div>
                <div class="slds-clearfix slds-m-top--medium">
                    <div class="slds-grid slds-float--right">
                    <div class="slds-text-align--right">
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_TotalValue}:&nbsp;</div>
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_HeaderDiscount}:&nbsp;</div>
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_HeaderDiscountAmount}:&nbsp;</div>
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_TotalValueNet}:&nbsp;</div>
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_FreightCost}:&nbsp;</div>
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_InsuranceFee}:&nbsp;</div>
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_OtherFee}:&nbsp;</div>
                        <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_VAT}:&nbsp;</div>
                        <div class="slds-border_bottom ccm_paddingTop" />
                        <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.CCM_TotalDueAmount}:&nbsp;</strong></div>
                    </div>
                    <div>
                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.baseInfo.TotalValue)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                        <div class="slds-truncate" title=""><strong>{!v.baseInfo.HeaderDiscount}%</strong></div>
                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.baseInfo.HeaderDiscountAmount)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.baseInfo.TotalValueNet)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.baseInfo.FreightCost)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.baseInfo.InsuranceFee)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.baseInfo.OtherFee)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                        <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.baseInfo.VAT)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                        <div class="slds-border_bottom ccm_paddingTop" />
                        <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.baseInfo.TotalDueAmount}" style="currency" currencyCode="{!v.currencySymbol}"/> </strong></div>
                    </div>
                </div>
                </div>
            </div>
        </c:CCM_Section>

        <div class="btn-wrap slds-m-bottom_medium footer-wrap">
            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancel}" />
            <lightning:button class="previous-btn" variant="brand-outline" label="{!$Label.c.CCM_Previous}" onclick="{!c.previousStep}" />
            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Submit}" onclick="{!c.doSubmit}" />
        </div>
    </lightning:card>
</aura:component>