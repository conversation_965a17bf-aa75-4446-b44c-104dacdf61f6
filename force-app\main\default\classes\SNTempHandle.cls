public without sharing class <PERSON><PERSON>empH<PERSON>le implements Triggers.Handler{
    public static Boolean isRun = true;
    public void handle() {
        if(isRun){
            if(Trigger.isAfter && Trigger.isInsert){
                //TODO  检查当前批次是否传输完成
                List<SN_Temp__c> lstTemp = (List<SN_Temp__c>)Trigger.new;
                if(lstTemp.size() > 0 ){
                    //获取当前批次requestId、记录总数
                    String requestId  = lstTemp[0].Union_Batch__c;
                    Integer totalCount = Integer.valueOf(lstTemp[0].Total_Num__c);

                    List<SN_Temp__c> exists = [SELECT Id,Union_Batch__c,Total_Num__c FROM SN_Temp__c WHERE Union_Batch__c =:requestId];
                    Integer existCount = exists.size();
                    if(existCount >= totalCount){
                        if(!requestId.contains('SN')){
                            List<SN_Export__c>  objExport = new List<SN_Export__c>();
                            objExport =  [SELECT id,CreatedById,Request_User__c,Request_Id__c,Status__c ,Request_Value__c,Type__c,Org_Type__c,Error_Msg__c, Usage__c FROM SN_Export__c WHERE Request_Id__c =:requestId limit 1];
                            if(!objExport.isEmpty() && objExport.size() >0){
                                objExport[0].Status__c = 'Finished';
                                objExport[0].Total_Num__c = lstTemp[0].Total_Num__c;
                                String processMsg = lstTemp[0].Process_Message__c;
                                if(objExport[0].Total_Num__c == '0'){
                                    if(processMsg == 'no results'){
                                        //The query parameters are no results.   no results
                                        objExport[0].Error_Msg__c = Label.CCM_SN_Response_Error_Msg_Blank;
                                    }else if(processMsg == 'incorrect'){
                                        //The query parameters are incorrect. incorrect
                                        objExport[0].Error_Msg__c =  Label.CCM_SN_Response_Error_Msg_Incorrect_Parameter;
                                    }
                                }
                                update objExport[0];
                                if(objExport[0].Usage__c != 'WarrantyClaimQuery') {
                                    sendEmail(objExport[0].Type__c,objExport[0].Request_Value__c,objExport[0].Org_Type__c,objExport[0].CreatedById,processMsg,objExport[0].Error_Msg__c);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    //发送SN导出请求结果邮件
    @future(callout=true)
    private static void sendEmail(String requestType,String requestValue,String orgType,String userId,String processMsg,String errorMsg) {
        Messaging.SingleEmailMessage objEmail = new Messaging.SingleEmailMessage();
        User userInfo = getUserById(userId);
        String emailAddress = userInfo.Email;
        List<String> lstAddress = new List<String>();
        List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
        List<String> bccAddress = new List<String>();
        lstAddress.add(emailAddress);
        bccAddress.add('<EMAIL>');
        bccAddress.add('<EMAIL>');
        String strDealerName = userInfo.Name;
        String baseUrl = '';
        String linkHtml = '';
        String strSubject = '';
        String strBody = '';
        if(String.isNotBlank(processMsg)){
            //导出请求失败邮件内容
            strSubject = Label.Serial_Number_Dowload_Email_Subject_Error;
            strBody = Label.Serial_Number_Dowload_Email_Body_Error;
            strBody = strBody.replace('##ERRORMSG##', errorMsg);
            
        }else{
            //导出请求成功邮件内容
            strSubject = Label.Serial_Number_Dowload_Email_Subject;
            strBody = Label.Serial_Number_Dowload_Email_Body;
            
        }
        strBody = strBody.replace('##REQUEST_TYPE##', requestType);
        strBody = strBody.replace('##REQUEST_VALUE##', requestValue);
        //组装超链接
        if(orgType.equalsIgnoreCase('CRM')){
            baseUrl = Label.CRM_SN_Export_Link;
            linkHtml = '<a href="'+ baseUrl +'">EGO CRM</a>';
        }else if (orgType.equalsIgnoreCase('Portal')) {
            baseUrl = Label.Portal_SN_Export_Link;
            linkHtml = '<a href="'+ baseUrl +'">DEALER PORTAL</a>';
        }

        strBody = strBody.replace('##DEALER_NAME##', strDealerName);
        strBody = strBody.replace('##DEALER_PORTAL##', linkHtml);


        objEmail.setSubject(strSubject);
        objEmail.toaddresses =lstAddress;
        if (listAddresses.size() > 0) {
            objEmail.setOrgWideEmailAddressId(listAddresses[0].Id);
        }
        objEmail.bccaddresses = bccAddress;
        system.debug('toaddresses--》'+lstAddress);
        objEmail.htmlBody = strBody;
        objEmail.setCharset('UTF-8');
        objEmail.setSaveAsActivity(false);
        // prettier-ignore
        if (!Test.isRunningTest()) Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{ objEmail });
        system.debug('发送邮件结束');
    }

    private static User getUserById(String  Id){
        User user = new User();
        user = [SELECT Id,Name,Email FROM User WHERE Id = :Id limit 1];
        return user;
    }
}