({
    submitEvent : function(component) {
        var action = component.get('c.createPurchaseOrder');
        // 传参处理
        const arr = component.get('v.tableData');
        const itemArr = [];
        arr.forEach((item)=>{
            itemArr.push({
                orderGroupNumber: item.OrderGroupNumber,
                customerNumber: item.CustomerNumber,
                customerPO: item.CustomerPO,
                billToAddressCode: item.BillToAddressCode,
                shipToAddressCode: item.ShipToAddressCode,
                dropshipAddressCode: item.DropshipAddressCode,
                insuranceFee: item.InsuranceFee,
                otherFee: item.OtherFee,
                orderType: item.OrderType,
                warehouse: item.Warehouse,
                isDropship: item.IsDropship,
                modelNumber: item.ModelNumber,
                requestDate: item.RequestDate,
                ScheduleShipDate: item.ScheduleShipDate,
                qty: item.Qty,
                remark: item.Remark,
                customerNumber: component.get('v.customerId'),
            })
        })
        console.log(itemArr, 'itemArr=========');
        action.setParams({
            'strpurchaseOrderItems': JSON.stringify(itemArr),
        })
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, 'submitEvent result=========');
            if (state === 'SUCCESS') {
                console.log(result, 'result----------------');
                if (result) {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": result,
                        "type": "error"
                    }).fire();
                } else {
                    let url = window.location.origin + '/s/orderinformation';
                    window.open(url, '_self');
                }
            }else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取当前用户身份
    getUserType : function(component) {
        var action = component.get('c.GetUserType');
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, '获取当前用户身份-----------------');
            if (state === 'SUCCESS' ) {
                component.set('v.userType', result);
                let insideSalesColumns = [
                    { label: $A.get("$Label.c.CCM_OrderGroupNumber"), fieldName: 'OrderGroupNumber', type: 'text', editable: true },
                    // { label: 'Customer Number', fieldName: 'CustomerNumber', type: 'text' },
                    { label: $A.get("$Label.c.CCM_CustomerPO"), fieldName: 'CustomerPO', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_BillToAddressCode"), fieldName: 'BillToAddressCode', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_ShipToAddressCode"), fieldName: 'ShipToAddressCode', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_DropshipAddressCode"), fieldName: 'DropshipAddressCode', type: 'text', editable: true},
                    { label: $A.get("$Label.c.CCM_InsuranceFee"), fieldName: 'InsuranceFee', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_OtherFee"), fieldName: 'OtherFee', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_OrderType"), fieldName: 'OrderType', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Warehouse"), fieldName: 'Warehouse', type: 'text', editable: true },
                    { label: $A.get("$Label.c.Order_IsDropship"), fieldName: 'IsDropship', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Model"), fieldName: 'ModelNumber', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Qty"), fieldName: 'Qty', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_RequestDate"), fieldName: 'RequestDate', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Remark"), fieldName: 'Remark', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Action"), type: 'lightning:buttonIcon', name: 'delete', 'attributes': {iconName: 'utility:delete', name: 'delete', iconClass: 'slds-icon-text-error'}},
                ];
                let dealerColumns = [
                    { label: $A.get("$Label.c.CCM_OrderGroupNumber"), fieldName: 'OrderGroupNumber', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_CustomerPO"), fieldName: 'CustomerPO', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_BillToAddressCode"), fieldName: 'BillToAddressCode', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_ShipToAddressCode"), fieldName: 'ShipToAddressCode', type: 'text', editable: true },
                    // { label: $A.get("$Label.c.CCM_DropshipAddressCode"), fieldName: 'DropshipAddressCode', type: 'text', editable: true},
                    // { label: $A.get("$Label.c.Order_IsDropship"), fieldName: 'IsDropship', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Model"), fieldName: 'ModelNumber', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Qty"), fieldName: 'Qty', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_RequestDate"), fieldName: 'RequestDate', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Action"), type: 'lightning:buttonIcon', name: 'delete', 'attributes': {iconName: 'utility:delete', name: 'delete', iconClass: 'slds-icon-text-error'}},
                ];
                let salesRepColumns = [
                    { label: $A.get("$Label.c.CCM_OrderGroupNumber"), fieldName: 'OrderGroupNumber', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_CustomerPO"), fieldName: 'CustomerPO', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_BillToAddressCode"), fieldName: 'BillToAddressCode', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_ShipToAddressCode"), fieldName: 'ShipToAddressCode', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_DropshipAddressCode"), fieldName: 'DropshipAddressCode', type: 'text', editable: true},
                    { label: $A.get("$Label.c.CCM_OrderType"), fieldName: 'OrderType', type: 'text', editable: true },
                    { label: $A.get("$Label.c.Order_IsDropship"), fieldName: 'IsDropship', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Model"), fieldName: 'ModelNumber', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Qty"), fieldName: 'Qty', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_RequestDate"), fieldName: 'RequestDate', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Remark"), fieldName: 'Remark', type: 'text', editable: true },
                    { label: $A.get("$Label.c.CCM_Action"), type: 'lightning:buttonIcon', name: 'delete', 'attributes': {iconName: 'utility:delete', name: 'delete', iconClass: 'slds-icon-text-error'}},
                ];
                // 初始化配置表格参数
                switch(component.get('v.userType')) {
                    case 'InsideSales':
                        component.set('v.columns', insideSalesColumns);
                        break;
                    case 'SalesRep':
                        component.set('v.columns', salesRepColumns);
                        break;
                    case 'Dealer':
                        component.set('v.columns', dealerColumns);
                        break;
                }
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取当前customer
    getCustomerInfo : function(component) {
        let self = this;
        var action = component.get('c.GetCurrentUserCustomer');
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, '获取当前customer-----------------');
            if (state === 'SUCCESS') {
                component.set('v.customerName', result.CustomerName);
                component.set('v.customerId', result.CustomerId);
            } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    checkUploadData : function(component, data) {
        var action = component.get('c.CheckBatchPurchaseOrderData');
        action.setParams({
            'strpurchaseOrderItems': JSON.stringify(data),
        })
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, 'submitEvent result=========');
            if (state === 'SUCCESS') {
                console.log(result, '上传校验result----------------');
                let returnData = JSON.parse(result);
                if (returnData && returnData.length) {
                    component.set('v.errortip', true);
                    component.set('v.errorList', returnData);
                } else {
                    component.set('v.errortip', false);
                    component.set('v.errorList', []);
                    component.set('v.tableData', JSON.parse(JSON.stringify(data)));
                }
            }else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.resetFlag', true);
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
})