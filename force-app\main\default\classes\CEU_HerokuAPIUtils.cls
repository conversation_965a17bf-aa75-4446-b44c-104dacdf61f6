/**
 * Cloned new interface for EU Web site
 */
public without sharing class CEU_HerokuAPIUtils{

    private static Map<String, String> siteOriginMap = new Map<String, String>{
        'Austria' => 'AT', 'Belgium' => 'BE', 'Belgium-French' => 'BEFR', 'Switzerland-German' => 'CH', 'Switzerland-French' => 'CHFR', 'Switzerland-Italian' => 'CHIT', 'Germany' => 'DE', 'Denmark' => 'DK', 'Estonia' => 'EE', 'Spain' => 'ES', 'Europe' => 'EU', 'Finland' => 'FI', 'France' => 'FR', 'Greece' => 'GR', 'Hungary' => 'HU', 'Ireland' => 'lE', 'Italy' => 'IT', 'Lithunia' => 'LT', 'Netherlands' => 'NL', 'United Kingdom' => 'UK', 'Zimbabwe' => 'ZA'
    };

    /**
     * @Function: 返回错误信息
     * @Param: [errorCode, Integer, 错误代码]
     * @Param: [errorMsg, String, 错误信息]
     */
    public static CEU_HerokuEntity.ResponseEntity getErrorResponse(Integer errorCode, String errorMsg){
        return new CEU_HerokuEntity.ResponseEntity(errorCode, errorMsg);
    }
    /**
     * @Function: 返回错误信息
     * @Param: [errorCode, Integer, 错误代码]
     * @Param: [errorMsg, String, 错误信息]
     */
    public static CEU_HerokuEntity.ResponseEntity upsertFailedResponse(String errorMsg){
        return new CEU_HerokuEntity.ResponseEntity(5003, errorMsg);
    }
    /**
     * @Function: 根据用户邮箱查询用户
     * @Param: [emailAddress, String, 用户邮箱]
     */
    /*public static Account getAccountByEmail(String emailAddress) {
     for (Account acc : [SELECT Id, PersonEmail, Password__c FROM Account where PersonEmail =: emailAddress]) {
     return acc;
     }
     return null;
     }*/
    /**
     * @Function: 根据用户邮箱查询用户
     * @Param: [emailAddress, String, 用户邮箱]
     */
    public static Account getUserByEmailAndBrandName(String emailAddress, String brandName){
        /*if (brandName != null && !brandName.endsWith('Customer')) {
         brandName = brandName + ' Customer';
         }*/
        // Add by yujie for service migration, not include merge logic
        //for (Account acc : [SELECT Id, FirstName, LastName, PersonEmail, Password__c, Brand_Name__c,

        for (Account acc : [SELECT Id, Language_For_Consumer__c, FirstName, LastName, PersonEmail, EGO_password__c, Password__c, PersonMobilePhone, Send_marketing_emails__c, Phone, Email__c, Brand_Name__c, ShippingStreet, ShippingPostalCode, ShippingCity, ShippingState, ShippingCountry, Lawn_Size__c, Country__c, Postal_Code__c, Description, Username__c, Type, RecordTypeId, State__c, City__c, PersonContactId, DealerView__c, Status__c, Street__c, UserCode__c, Address_Detail__c, Product_Type__c, EGO_username__c, Site_Origin__pc, Company__pc, MarketingOptIn__pc
                            FROM Account
                            WHERE PersonEmail = :emailAddress and Record_Type_Name__c = :CCM_Constants.PERSONACCOUNT]){
            //(Brand_Name__c =: brandName OR Brand_Name__c =: (brandName + ' Customer'))]) {
            return acc;
        }
        return null;
    }
    /**
     * @Function: 根据用户邮箱查询用户
     * @Param: [emailAddress, String, 用户邮箱]
     */
    /*public static Account getAccountByEmail(String emailAddress) {
     for (Account acc : [SELECT Id, PersonEmail, Password__c FROM Account where PersonEmail =: emailAddress]) {
     return acc;
     }
     return null;
     }*/
    /**
     * @Function: 根据用户邮箱查询用户
     * @Param: [emailAddress, String, 用户邮箱]
     */
    public static Account getResidentialByEmailAndBrandName(String emailAddress, String brandName, String userCode){

        String sqlStr = ' SELECT Id,FirstName,LastName,PersonEmail,EGO_password__c,Password__c,PersonMobilePhone,Send_marketing_emails__c,Phone,Email__c,Brand_Name__c, ';
        sqlStr += ' ShippingStreet,ShippingPostalCode,ShippingCity,ShippingState,ShippingCountry,Lawn_Size__c,Country__c,Postal_Code__c, Consumer_Status__c,';
        sqlStr += ' Description,Username__c,Type,RecordTypeId,State__c,City__c,PersonContactId,DealerView__c,Status__c,Street__c,UserCode__c,';
        sqlStr += ' Address_Detail__c,Product_Type__c,EGO_username__c,Site_Origin__pc,Company__pc,MarketingOptIn__pc ';
        sqlStr += ' FROM Account ';
        sqlStr += ' WHERE Record_Type_Name__c =\'' + CCM_Constants.PERSONACCOUNT + '\'';
        if (!String.isBlank(emailAddress)){
            if (String.isBlank(userCode)){
                sqlStr += ' and PersonEmail =: emailAddress ';

            } else{
                sqlStr += ' and (PersonEmail =: emailAddress OR UserCode__c =: userCode) ';
            }
        } else{
            sqlStr += ' and UserCode__c =: userCode ';
        }
        System.debug(LoggingLevel.INFO, '*** sqlStr: ' + sqlStr);
        List<Account> accList = Database.query(sqlStr);
        for (Account acc : accList){
            return acc;
        }
        return null;
    }
    public static Contact getCommercialByEmailAndBrandName(String emailAddress, String brandName){

        for (Contact acc : [SELECT Email, FirstName, LastName, MobilePhone, Password__c, Phone, account.ShippingStreet, account.ShippingPostalCode, account.ShippingCity, account.ShippingState, account.ShippingCountry, account.Lawn_Size__c, account.Country__c, account.Postal_Code__c, account.Consumer_Status__c, account.Username__c, account.Type, account.RecordTypeId, account.State__c, account.City__c, account.DealerView__c, account.Status__c, account.Street__c, account.UserCode__c, account.Address_Detail__c
                            FROM Contact
                            WHERE Email = :emailAddress and account.Record_Type_Name__c = :CCM_Constants.CommercialConsumerName]){
            return acc;
        }
        return null;
    }
    /**
     * @Description:
     * @Function: 通过Account id获取Account
     * @Param: [accountId]
     *
     */
    public static Account getResidentialUserByID(String accountId){
        // Add by yujie for service migration, not include merge logic
        //for(Account acc : [SELECT Id, FirstName, LastName, PersonEmail, Password__c,
        for (Account acc : [SELECT Id, Language_For_Consumer__c, FirstName, LastName, PersonEmail, EGO_password__c, Password__c, PersonMobilePhone, Send_marketing_emails__c, Phone, Email__c, Brand_Name__c, ShippingStreet, ShippingPostalCode, ShippingCity, ShippingState, ShippingCountry, Lawn_Size__c, Country__c, Postal_Code__c, Description, Username__c, Type, RecordTypeId, State__c, City__c, PersonContactId, DealerView__c, Status__c, Street__c, UserCode__c, Address_Detail__c, Product_Type__c, EGO_username__c, Site_Origin__pc, Company__pc, MarketingOptIn__pc, Consumer_Status__c, Name
                            FROM Account
                            WHERE Id = :accountId]){
            return acc;
        }
        return null;
    }
    public static Account getResidentialUserByIDwithoutName(String accountId){
        // Add by yujie for service migration, not include merge logic
        //for(Account acc : [SELECT Id, FirstName, LastName, PersonEmail, Password__c,
        for (Account acc : [SELECT Id, Language_For_Consumer__c, FirstName, LastName, PersonEmail, EGO_password__c, Password__c, PersonMobilePhone, Send_marketing_emails__c, Phone, Email__c, Brand_Name__c, ShippingStreet, ShippingPostalCode, ShippingCity, ShippingState, ShippingCountry, Lawn_Size__c, Country__c, Postal_Code__c, Description, Username__c, Type, RecordTypeId, State__c, City__c, PersonContactId, DealerView__c, Status__c, Street__c, UserCode__c, Address_Detail__c, Product_Type__c, EGO_username__c, Site_Origin__pc, Company__pc, MarketingOptIn__pc, Consumer_Status__c
                            FROM Account
                            WHERE Id = :accountId]){
            return acc;
        }
        return null;
    }
    //获取 commercial company
    public static Account getCommercialCompanyByID(String accountId){
        // Add by yujie for service migration, not include merge logic
        //for(Account acc : [SELECT Id, FirstName, LastName, PersonEmail, Password__c,
        for (Account acc : [SELECT Id, Language_For_Consumer__c, FirstName, LastName, PersonEmail, EGO_password__c, Password__c, PersonMobilePhone, Send_marketing_emails__c, Phone, Email__c, Brand_Name__c, MarketingOptIn__c, ShippingStreet, ShippingPostalCode, ShippingCity, ShippingState, ShippingCountry, Lawn_Size__c, Country__c, Postal_Code__c, Site_Origin__c, Description, Username__c, Type, RecordTypeId, State__c, City__c, PersonContactId, DealerView__c, Status__c, Street__c, UserCode__c, Address_Detail__c, Product_Type__c, EGO_username__c, Business_Area__c, name, Consumer_Status__c
                            FROM Account
                            WHERE Id = :accountId]){
            return acc;
        }
        return null;
    }
    //获取  contcat
    public static Contact getContactByID(String contactId){
        // Add by yujie for service migration, not include merge logic
        for (Contact con : [SELECT Email, FirstName, LastName, MobilePhone, Password__c, Phone, accountId, Role__c, account.ShippingCountry, account.Name, account.Site_Origin__c, account.Site_Origin__pc, account.Language_For_Consumer__c
                            FROM Contact
                            WHERE Id = :contactId]){
            return con;
        }
        return null;
    }
    public static Contact getContactByEmail(String contactEmail){
        // Add by yujie for service migration, not include merge logic
        for (Contact con : [SELECT Email, FirstName, LastName, MobilePhone, Password__c, Phone, accountId, Role__c, account.Language_For_Consumer__c, account.ShippingCountry, account.Name, Account.Site_Origin__c, Account.Site_Origin__pc
                            FROM Contact
                            WHERE Email = :contactEmail]){
            return con;
        }
        return null;
    }
    //获取 commercial company
    public static Account getContactListByID(String accountId){
        // Add by yujie for service migration, not include merge logic
        //for(Account acc : [SELECT Id, FirstName, LastName, PersonEmail, Password__c,
        for (Account acc : [SELECT Id, Language_For_Consumer__c, FirstName, LastName, PersonEmail, EGO_password__c, PersonMobilePhone, Send_marketing_emails__c, Phone, Email__c, Brand_Name__c, Site_Origin__c, ShippingStreet, ShippingPostalCode, ShippingCity, ShippingState, ShippingCountry, Lawn_Size__c, Country__c, Postal_Code__c, Account_Code__c, Description, Username__c, Type, RecordTypeId, State__c, City__c, PersonContactId, DealerView__c, Status__c, Street__c, UserCode__c, Address_Detail__c, Product_Type__c, EGO_username__c, Site_Origin__pc, Company__pc, MarketingOptIn__c, Business_Area__c, Name, (SELECT Email, FirstName, LastName, MobilePhone, Password__c, Phone, accountId, Role__c, DealerView__c, Status__c, MarketingOptIn__c, Site_Origin__c
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 FROM Contacts)
                            FROM Account
                            WHERE Id = :accountId]){
            return acc;
        }
        return null;
    }
    /**
     *
     * @Function: Get the Reset_Password_EmailTemplate
     * @Param:
     *
     */
    // update by roger for EU 2019/05/20, add param 'acc'
    // 2022-06-22(update): Avoid sending failure due to missing email templates
    //public static EmailTemplate getResetPasswordEmailTemplate() {
    public static EmailTemplate getResetPasswordEmailTemplate(String siteOrigin, String language, String contactLanguage){
        // 2022-06-22(update)
        List<String> etDevNameList = new List<String>{ 'Reset_Password_Email_EU' };
        //Add by yujie for EU
        String emailDeveloperName = 'Reset_Password_Email';
        //add by yujie for EU
        if (siteOrigin != 'United States'){
            String a = siteOrigin;
            a = newCountryMap.get(a) != null ? newCountryMap.get(a) : a;
            String emailDeveloperNameTemp = emailDeveloperName + '_' + a.replace(' ', '_').replace('-', '_');
            //【Start Add】Vince 2023-12-06

            // if (String.isNotEmpty(contactLanguage)){
            //     emailDeveloperName += contactLanguage;
            // } else if (String.isNotEmpty(language)){
            //     emailDeveloperName += language;
            // }
            //【End Add】Vince 2023-12-06
            // 2022-06-22(update)
            etDevNameList.add(emailDeveloperNameTemp);
        }

        if(String.isNotBlank(contactLanguage)) {
            String emailDeveloperNameTemp = emailDeveloperName + '_' + contactLanguage;
            etDevNameList.add(emailDeveloperNameTemp);
        }
        else if(String.isNotBlank(language)) {
            String emailDeveloperNameTemp = emailDeveloperName + '_' + language;
            etDevNameList.add(emailDeveloperNameTemp);
        }

        EmailTemplate et = null;
        for (EmailTemplate etTemp : [SELECT Id, Body, HtmlValue, Subject, DeveloperName
                                     FROM EmailTemplate
                                     WHERE DeveloperName IN:etDevNameList AND IsActive = true]){
            // 2022-06-22(update)
            if (et == null){
                et = etTemp;
            } else if (etTemp.DeveloperName <> 'Reset_Password_Email_EU'){
                et = etTemp;
            }
        }
        System.debug(LoggingLevel.INFO, '*** et: ' + et.DeveloperName);

        return et;
    }
    /**
     *
     * @Function: Get the IoT_Reset_Password_EmailTemplate
     * @Param:
     *
     */
    public static EmailTemplate getIoTResetPasswordEmailTemplate(){
        for (EmailTemplate etTemp : [SELECT Id, Body, HtmlValue, Subject
                                     FROM EmailTemplate
                                     WHERE DeveloperName = 'Reset_Password_Email_IoT' AND IsActive = true
                                     LIMIT 1]){
            return etTemp;
        }
        return null;
    }
    /**
     *
     * @Function: Get the Migrate_Account_EmailTemplate
     * @Param:
     *
     */
    public static EmailTemplate getMigrateAccountEmailTemplate(){
        for (EmailTemplate etTemp : [SELECT Id, Body, HtmlValue, Subject
                                     FROM EmailTemplate
                                     WHERE DeveloperName = 'Migrate_Account_Email' AND IsActive = true
                                     LIMIT 1]){
            return etTemp;
        }
        return null;
    }
    /**
     * @author:   Zack
     * @date:     2018-08-09
     * @function: 发送重置密码的邮件
     * @param:
     * @return:
     */
    public static void sendResetPasswordEmail(Account acc, String jwtToken, String brandName){
        Log__c logInfo = new Log__c(
            Name = 'EUHerokuAPI ' + DateTime.now()
        );
        logInfo.ApexName__c = 'CEU_HerokuAPIUtils';
        logInfo.Method__c = 'sendResetPasswordEmail';
        System.debug(LoggingLevel.INFO, '*** sendResetPasswordEmail1111111: ');
        if (acc == null || String.isEmpty(acc.PersonEmail) || String.isEmpty(jwtToken)){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'Error Params: Account(' + JSON.serialize(acc) + '); ' + 'jwtToken(' + jwtToken + ').';
            insert logInfo;

            return;
        }

        //查找模板
        // Add by yujie for EU
        EmailTemplate emailTem = getResetPasswordEmailTemplate(acc.Site_Origin__pc, acc.Language_For_Consumer__c, '');
        if (emailTem == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'No email template searched. Account(' + JSON.serialize(acc) + ').';
            insert logInfo;

            return;
        }
        // OrgWideEmailAddress orgEmailAddress = getOrgWideEmailAddressBySystemConfigurationName('EGO CRM', brandName);
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'OrgWideEmailAddress is Null.';
            insert logInfo;

            return;
        }

        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        if (String.isNotBlank(acc.ShippingCountry)){
            String a = '_' + acc.ShippingCountry;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(acc.Site_Origin__pc)) {
            String site = acc.Site_Origin__pc;
            if(siteOriginMap.containsKey(acc.Site_Origin__pc)) {
                site = siteOriginMap.get(acc.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        System.debug(LoggingLevel.INFO, '*** keySet: ' + keySet);
        String urlStr = '';

        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        System.debug(LoggingLevel.INFO, '*** mdtList: ' + mdtList);
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }

        emailTem.HtmlValue = emailTem.HtmlValue.replace('[ACCOUNT_NAME]', acc.LastName);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.Body = emailTem.Body.replace('[ACCOUNT_NAME]', acc.LastName);
        emailTem.Body = emailTem.Body.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[SITE_URL]', urlStr);
        emailTem.Body = emailTem.Body.replace('[SITE_URL]', urlStr);

        sendEmail(orgEmailAddress, acc.PersonEmail, emailTem);
    }
    public static void sendResetPasswordContactEmail(Contact con, String jwtToken, String brandName, String contactLanguage){

        Log__c logInfo = new Log__c(
            Name = 'EUHerokuAPI ' + DateTime.now()
        );
        logInfo.ApexName__c = 'CEU_HerokuAPIUtils';
        logInfo.Method__c = 'sendResetPasswordContactEmail';
        System.debug(LoggingLevel.INFO, '*** con: ' + con);
        if (con == null || String.isEmpty(con.Email) || String.isEmpty(jwtToken)){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'Error Params: Commerical User (' + JSON.serialize(con) + '); ' + 'jwtToken(' + jwtToken + ').';
            insert logInfo;

            return;
        }

        //查找模板
        // Add by yujie for EU
        EmailTemplate emailTem = getResetPasswordEmailTemplate(con.account.Site_Origin__c, con.account.Language_For_Consumer__c, contactLanguage);
        if (emailTem == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'No email template searched. Commerical User (' + JSON.serialize(con) + ').';
            insert logInfo;

            return;
        }
        // OrgWideEmailAddress orgEmailAddress = getOrgWideEmailAddressBySystemConfigurationName('EGO CRM', brandName);
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'OrgWideEmailAddress is Null.';
            insert logInfo;

            return;
        }

        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        if (String.isNotBlank(con.Account.ShippingCountry)){
            String a = '_' + con.Account.ShippingCountry;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if (String.isNotBlank(con.Account.Site_Origin__pc)){
            String site = con.Account.Site_Origin__pc;
            if(siteOriginMap.containsKey(con.Account.Site_Origin__pc)) {
                site = siteOriginMap.get(con.Account.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        System.debug(LoggingLevel.INFO, '*** keySet: ' + keySet);
        String urlStr = '';

        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        System.debug(LoggingLevel.INFO, '*** mdtList: ' + mdtList);
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }

        emailTem.HtmlValue = emailTem.HtmlValue.replace('[ACCOUNT_NAME]', con.LastName);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.Body = emailTem.Body.replace('[ACCOUNT_NAME]', con.LastName);
        emailTem.Body = emailTem.Body.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[SITE_URL]', urlStr);
        emailTem.Body = emailTem.Body.replace('[SITE_URL]', urlStr);

        sendEmail(orgEmailAddress, con.Email, emailTem);
    }
    /**
     * @author:   Zack
     * @date:     2018-08-14
     * @function: 发送IoT重置密码的邮件
     * @param:
     * @return:
     */
    public static void sendIoTResetPasswordEmail(Account acc, String jwtToken){
        if (acc == null || String.isEmpty(acc.PersonEmail) || String.isEmpty(jwtToken)){
            return;
        }
        //查找模板
        EmailTemplate emailTem = getIoTResetPasswordEmailTemplate();
        if (emailTem == null){
            return;
        }
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            System.debug('----OrgWideEmailAddress为Null');
            return;
        }

        emailTem.HtmlValue = emailTem.HtmlValue.replace('#token_value#', jwtToken);

        sendEmail(orgEmailAddress, acc.PersonEmail, emailTem);
    }
    /**
     * @author:   Zack
     * @date:     2018-08-09
     * @function: 发送合并账户的邮件
     * @param:
     * @return:
     */
    public static void sendMigrateAccountEmail(Account acc, String jwtToken){
        if (acc == null || String.isEmpty(acc.PersonEmail) || String.isEmpty(jwtToken)){
            return;
        }
        //查找模板
        EmailTemplate emailTem = getMigrateAccountEmailTemplate();
        if (emailTem == null){
            return;
        }
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            System.debug('----OrgWideEmailAddress为Null');
            return;
        }

        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        if (String.isNotBlank(acc.ShippingCountry)){
            String a = '_' + acc.ShippingCountry;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if (String.isNotBlank(acc.Site_Origin__pc)){
            String site = acc.Site_Origin__pc;
            if(siteOriginMap.containsKey(acc.Site_Origin__pc)) {
                site = siteOriginMap.get(acc.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        System.debug(LoggingLevel.INFO, '*** keySet: ' + keySet);
        String urlStr = '';

        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        System.debug(LoggingLevel.INFO, '*** mdtList: ' + mdtList);
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }

        emailTem.HtmlValue = emailTem.HtmlValue.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.Body = emailTem.Body.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[SITE_URL]', urlStr);
        emailTem.Body = emailTem.Body.replace('[SITE_URL]', urlStr);

        sendEmail(orgEmailAddress, acc.PersonEmail, emailTem);
    }
    public static void sendMigrateContactEmail(contact con, String jwtToken){
        if (con == null || String.isEmpty(con.email) || String.isEmpty(jwtToken)){
            return;
        }
        //查找模板
        EmailTemplate emailTem = getMigrateAccountEmailTemplate();
        if (emailTem == null){
            return;
        }
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];

        if (orgEmailAddress == null){
            System.debug('----OrgWideEmailAddress为Null');
            return;
        }

        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        if (String.isNotBlank(con.Account.ShippingCountry)){
            String a = '_' + con.Account.ShippingCountry;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if (String.isNotBlank(con.Account.Site_Origin__pc)){
            String site = con.Account.Site_Origin__pc;
            if(siteOriginMap.containsKey(con.Account.Site_Origin__pc)) {
                site = siteOriginMap.get(con.Account.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        System.debug(LoggingLevel.INFO, '*** keySet: ' + keySet);
        String urlStr = '';

        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        System.debug(LoggingLevel.INFO, '*** mdtList: ' + mdtList);
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }

        emailTem.HtmlValue = emailTem.HtmlValue.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.Body = emailTem.Body.replace('[TOKEN_VALUE]', jwtToken);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[SITE_URL]', urlStr);
        emailTem.Body = emailTem.Body.replace('[SITE_URL]', urlStr);

        sendEmail(orgEmailAddress, con.Email, emailTem);
    }
    //residential用户注册，commercial用户第一次注册
    public static void sendNewConsumerNoticeEmail(String accId, Boolean personFlag){
        System.debug('run sendNewConsumer NoticeEmail');
        Log__c logInfo = new Log__c(
            Name = 'EUHerokuAPI ' + DateTime.now()
        );
        logInfo.ApexName__c = 'CEU_HerokuAPIUtils';
        logInfo.Method__c = 'sendNewConsumerNoticeEmail';
        Account consumerRecord = new Account();
        String personUserCodeDisplay = '';
        String RegistrationEmailAddress = '';
        String display30 = '';
        String tokenParam = '';
        if (personFlag){
            consumerRecord = getResidentialUserByID(accId);
            tokenParam = consumerRecord.Id;
            RegistrationEmailAddress = consumerRecord.PersonEmail;
            if (consumerRecord.EGO_password__c != null){
                display30 = 'none';//不显示设置密码部分
            }
        } else{
            consumerRecord = getContactListByID(accId);
            Contact fleetM = new Contact();
            for (contact con : consumerRecord.Contacts){
                if (con.Role__c == 'Fleet Manager'){
                    fleetM = con;
                }
            }
            personUserCodeDisplay = 'none';
            RegistrationEmailAddress = fleetM.Email;
            tokenParam = fleetM.Id;
            if (fleetM.password__c != null){
                display30 = 'none';//不显示设置密码部分
            }
        }
        if (System.Label.Email_UserCode == '0'){
            personUserCodeDisplay = 'none';
        }

        if (consumerRecord == null || String.isEmpty(RegistrationEmailAddress)){
            logInfo.Error_Message__c = 'Account Error. Account(' + JSON.serialize(consumerRecord) + ').';
            insert logInfo;
            return;
        }
        System.debug('run consumerRecord == null');
        //查找模板
        List<String> etDevNameList = new List<String>{ 'Set_Password_Email_EU' };
        String emailDeveloperName = 'Set_Password_Email';
        //add by yujie for EU
        if (String.isNotBlank(consumerRecord.ShippingCountry)){
            String a = consumerRecord.ShippingCountry;
            String emailDeveloperName1 = emailDeveloperName + '_' + a;
            etDevNameList.add(emailDeveloperName1);
        }
        //【Start-Add】 By Vince 2023-12-06  Language_For_Consumer__c
        if (String.isNotBlank(consumerRecord.Language_For_Consumer__c)){
            String a = consumerRecord.Language_For_Consumer__c;
            String emailDeveloperName1 = emailDeveloperName + '_' + a;
            etDevNameList.add(emailDeveloperName1);
        }
        //【End-Add】 By Vince 2023-12-06
        EmailTemplate emailTem = null;
        List<EmailTemplate> templatelist = [SELECT Id, Body, HtmlValue, Subject, DeveloperName
                                            FROM EmailTemplate
                                            WHERE DeveloperName IN:etDevNameList AND IsActive = true];
        System.debug(LoggingLevel.INFO, '*** etDevNameList: ' + etDevNameList);
        System.debug(LoggingLevel.INFO, '*** templatelist.size(): ' + templatelist.size());
        if (templatelist.size() > 1){
            for (EmailTemplate etTemp : templatelist){
                if (etTemp.DeveloperName <> 'Set_Password_Email_EU'){
                    emailTem = etTemp;
                }
            }
        } else if (templatelist.size() > 0){
            emailTem = templatelist[0];
        }
        System.debug(LoggingLevel.INFO, '*** emailTem: ' + emailTem);
        if (emailTem == null){
            logInfo.Error_Message__c = 'No email template searched. Account(' + JSON.serialize(consumerRecord) + ').';
            insert logInfo;
            return;
        }
        System.debug('run emailTem == null');
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'OrgWideEmailAddress is Null.';
            insert logInfo;

            return;
        }
        System.debug('run orgEmailAddress == null');

        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        // if (consumerRecord.ShippingCountry != NULL){
        //     String a = '_' + consumerRecord.ShippingCountry;
        //     //【Start-Add】 By Vince 2023-12-06  Language_For_Consumer__c
        //     if (consumerRecord.Language_For_Consumer__c != null){
        //         a += consumerRecord.Language_For_Consumer__c;
        //     }
        //     //【End-Add】 By Vince 2023-12-06
        //     String key2 = '';
        //     key2 = key.replace('_EU', a);
        //     keySet.add(key2);
        // }
        if(String.isNotBlank(consumerRecord.ShippingCountry)) {
            String a = '_' + consumerRecord.ShippingCountry;
            String key2 = '';
            key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(consumerRecord.Language_For_Consumer__c)) {
            String a = '_' + consumerRecord.Language_For_Consumer__c;
            String key2 = '';
            key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(consumerRecord.Site_Origin__pc)) {
            String site = consumerRecord.Site_Origin__pc;
            if(siteOriginMap.containsKey(consumerRecord.Site_Origin__pc)) {
                site = siteOriginMap.get(consumerRecord.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        System.debug(LoggingLevel.INFO, '*** keySet: ' + keySet);
        String urlStr = '';

        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        System.debug(LoggingLevel.INFO, '*** mdtList: ' + mdtList);
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }
        try{
            System.debug(LoggingLevel.INFO, '*** emailTem: ' + emailTem);
            emailTem.Subject = emailTem.Subject.replace('[USER_NAME]', consumerRecord.Name);
            if (personFlag){
                emailTem.HtmlValue = emailTem.HtmlValue.replace('[USER_CODE]', consumerRecord.UserCode__c);
            } else{
                emailTem.HtmlValue = emailTem.HtmlValue.replace('[USER_CODE]', consumerRecord.Account_Code__c);

            }
            emailTem.HtmlValue = emailTem.HtmlValue.replace('[TOKEN_VALUE]', tokenParam);
            emailTem.HtmlValue = emailTem.HtmlValue.replace('[SITE_URL]', urlStr);
            emailTem.HtmlValue = emailTem.HtmlValue.replace('[SHOW_FLAG]', display30);
            emailTem.HtmlValue = emailTem.HtmlValue.replace('[PERSONAL_FLAG]', personUserCodeDisplay);

            // sendEmail(orgEmailAddress, consumerRecord.PersonEmail, emailTem);
            sendEmail(orgEmailAddress, RegistrationEmailAddress, emailTem);
        } catch (Exception e){
            system.debug('Error Line1 : ' + e.getLineNumber());
            system.debug('Error Message : ' + e.getMessage());
        }
    }
    //官网commercial的fleet manager邀请新用户
    public static void sendNewConsumerCommerNoticeEmail(String conId){
        Log__c logInfo = new Log__c(
            Name = 'EUHerokuAPI ' + DateTime.now()
        );
        logInfo.ApexName__c = 'CEU_HerokuAPIUtils';
        logInfo.Method__c = 'sendNewConsumerCommerNoticeEmail';
        Contact consumerRecord = getContactByID(conId);
        if (consumerRecord == null || String.isEmpty(consumerRecord.Email)){
            logInfo.Error_Message__c = 'Account Error. Account(' + JSON.serialize(consumerRecord) + ').';
            insert logInfo;
            return;
        }
        //查找模板
        List<String> etDevNameList = new List<String>{ 'Set_Comericial_User_Password_Email_EU' };
        String emailDeveloperName = 'Set_Comericial_User_Password_Email';
        //add by yujie for EU
        if (String.isNotBlank(consumerRecord.Account.ShippingCountry)){
            String a = consumerRecord.Account.ShippingCountry;
            String emailDeveloperName1 = emailDeveloperName + '_' + a;
            etDevNameList.add(emailDeveloperName1);
        }
        //【Start-Add】 By Vince 2023-12-06  Language_For_Consumer__c
        if (String.isNotBlank(consumerRecord.Account.Language_For_Consumer__c)){
            String a = consumerRecord.Account.Language_For_Consumer__c;
            String emailDeveloperName1 = emailDeveloperName + '_' + a;
            etDevNameList.add(emailDeveloperName1);
        }
        //【End-Add】 By Vince 2023-12-06
        EmailTemplate emailTem = null;
        List<EmailTemplate> templatelist = [SELECT Id, Body, HtmlValue, Subject, DeveloperName
                                            FROM EmailTemplate
                                            WHERE DeveloperName IN:etDevNameList AND IsActive = true];
        System.debug(LoggingLevel.INFO, '*** etDevNameList: ' + etDevNameList);
        System.debug(LoggingLevel.INFO, '*** templatelist.size(): ' + templatelist.size());
        if (templatelist.size() == 2){
            for (EmailTemplate etTemp : templatelist){
                if (etTemp.DeveloperName <> 'Set_Password_Email_EU'){
                    emailTem = etTemp;
                }
            }
        } else if (templatelist.size() > 0){
            emailTem = templatelist[0];
        }
        System.debug(LoggingLevel.INFO, '*** emailTem: ' + emailTem);
        if (emailTem == null){
            logInfo.Error_Message__c = 'No email template searched. Account(' + JSON.serialize(consumerRecord) + ').';
            insert logInfo;
            return;
        }
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'OrgWideEmailAddress is Null.';
            insert logInfo;

            return;
        }

        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        // if (consumerRecord.Account.ShippingCountry != NULL){
        //     String a = '_' + consumerRecord.Account.ShippingCountry;
        //     //【Start-Add】 By Vince 2023-12-06  Language_For_Consumer__c
        //     if (consumerRecord.Account.Language_For_Consumer__c != null){
        //         a += consumerRecord.Account.Language_For_Consumer__c;
        //     }
        //     //【End-Add】 By Vince 2023-12-06
        //     String key2 = '';
        //     key2 = key.replace('_EU', a);
        //     keySet.add(key2);
        // }
        if(String.isNotBlank(consumerRecord.Account.ShippingCountry)) {
            String a = '_' + consumerRecord.Account.ShippingCountry;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(consumerRecord.Account.Language_For_Consumer__c)) {
            String a = '_' + consumerRecord.Account.Language_For_Consumer__c;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(consumerRecord.Account.Site_Origin__pc)) {
            String site = consumerRecord.Account.Site_Origin__pc;
            if(siteOriginMap.containsKey(consumerRecord.Account.Site_Origin__pc)) {
                site = siteOriginMap.get(consumerRecord.Account.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        System.debug(LoggingLevel.INFO, '*** keySet: ' + keySet);
        String urlStr = '';

        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        System.debug(LoggingLevel.INFO, '*** mdtList: ' + mdtList);
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }
        System.debug(LoggingLevel.INFO, '*** emailTem: ' + emailTem);
        emailTem.Subject = emailTem.Subject.replace('[USER_NAME]', consumerRecord.Account.Name);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[COMPANY_NAME]', consumerRecord.Account.Name);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[TOKEN_VALUE]', consumerRecord.Id);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[SITE_URL]', urlStr);

        sendEmail(orgEmailAddress, consumerRecord.Email, emailTem);
    }
    //residential和commercial用户注册产品
    public static void sendNewWarrantyNoticeEmail(String accId, Boolean personFlag){
        Log__c logInfo = new Log__c(
            Name = 'EUHerokuAPI ' + DateTime.now()
        );
        logInfo.ApexName__c = 'CEU_HerokuAPIUtils';
        logInfo.Method__c = 'sendNewWarrantyNoticeEmail';
        Account consumerRecord = new Account();
        String personUserCodeDisplay = '';
        String tokenParam = '';
        String emailAddress = '';
        if (personFlag){
            consumerRecord = getResidentialUserByID(accId);
            if (consumerRecord != null){
                tokenParam = consumerRecord.Id;
                emailAddress = consumerRecord.PersonEmail;
            }
        } else{
            consumerRecord = getContactListByID(accId);
            if (consumerRecord != null){
                for (contact con : consumerRecord.Contacts){
                    if (con.Role__c == 'Fleet Manager'){
                        tokenParam = con.Id;
                        emailAddress = con.Email;
                    }
                }
            }
            personUserCodeDisplay = 'none';
        }
        if (System.Label.Email_UserCode == '0'){
            personUserCodeDisplay = 'none';
        }
        // String display30 = '';
        // if(consumerRecord.Consumer_Status__c == 'Active') {
        //     display30 = 'none';
        // }
        /**
        if (consumerRecord == null || String.isEmpty(consumerRecord.PersonEmail)){
            logInfo.Error_Message__c = 'Account Error. Account(' + JSON.serialize(consumerRecord) + ').';
            insert logInfo;
            return;
        } */

        if(consumerRecord == null){
            logInfo.Error_Message__c = 'Can not find the account by '+ accId;
            insert logInfo;
            return;
        }else{
            if(String.isEmpty(emailAddress)){
                logInfo.Error_Message__c = 'Email address is empty.Customer id is :'+accId;
                insert logInfo;
                return;
            }
        }

        //查找模板
        List<String> etDevNameList = new List<String>{ 'Warranty_Email_EU' };
        String emailDeveloperName = 'Warranty_Email';
        //add by yujie for EU
        if (String.isNotBlank(consumerRecord.ShippingCountry)){
            String a = consumerRecord.ShippingCountry;
            String emailDeveloperName1 = emailDeveloperName + '_' + a;
            etDevNameList.add(emailDeveloperName1);
        }
        //【Start-Add】 By Vince 2023-12-06  Language_For_Consumer__c
        if (String.isNotBlank(consumerRecord.Language_For_Consumer__c)){
            String a = consumerRecord.Language_For_Consumer__c;
            String emailDeveloperName1 = emailDeveloperName + '_' + a;
            etDevNameList.add(emailDeveloperName1);
        }
        //【End-Add】 By Vince 2023-12-06
        EmailTemplate emailTem = null;
        List<EmailTemplate> templatelist = [SELECT Id, Body, HtmlValue, Subject, DeveloperName
                                            FROM EmailTemplate
                                            WHERE DeveloperName IN:etDevNameList AND IsActive = true];
        System.debug(LoggingLevel.INFO, '*** etDevNameList: ' + etDevNameList);
        System.debug(LoggingLevel.INFO, '*** templatelist.size(): ' + templatelist.size());
        if (templatelist.size() > 1){
            for (EmailTemplate etTemp : templatelist){
                if (etTemp.DeveloperName <> 'Warranty_Email_EU'){
                    emailTem = etTemp;
                }
            }
        } else if (templatelist.size() > 0){
            emailTem = templatelist[0];
        }
        System.debug(LoggingLevel.INFO, '*** emailTem: ' + emailTem);
        if (emailTem == null){
            logInfo.Error_Message__c = 'No email template searched. Account(' + JSON.serialize(consumerRecord) + ').';
            insert logInfo;
            return;
        }
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'OrgWideEmailAddress is Null.';
            insert logInfo;

            return;
        }

        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        if(String.isNotBlank(consumerRecord.ShippingCountry)) {
            String a = '_' + consumerRecord.ShippingCountry;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(consumerRecord.Language_For_Consumer__c)) {
            String a = '_' + consumerRecord.Language_For_Consumer__c;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(consumerRecord.Site_Origin__pc)) {
            String site = consumerRecord.Site_Origin__pc;
            if(siteOriginMap.containsKey(consumerRecord.Site_Origin__pc)) {
                site = siteOriginMap.get(consumerRecord.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        System.debug(LoggingLevel.INFO, '*** keySet: ' + keySet);
        String urlStr = '';

        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        System.debug(LoggingLevel.INFO, '*** mdtList: ' + mdtList);
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }
        System.debug(LoggingLevel.INFO, '*** emailTem: ' + emailTem);
        emailTem.Subject = emailTem.Subject.replace('[USER_NAME]', consumerRecord.Name);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[USER_CODE]', consumerRecord.UserCode__c);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[TOKEN_VALUE]', tokenParam);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[SITE_URL]', urlStr);
        // emailTem.HtmlValue = emailTem.HtmlValue.replace('[SHOW_FLAG]',display30);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[PERSONAL_FLAG]', personUserCodeDisplay);

        sendEmail(orgEmailAddress, emailAddress, emailTem);
    }

    private static void sendEmail(OrgWideEmailAddress orgEmailAddress, String toAddress, EmailTemplate emailTem){
        System.debug('====toAddress:' + toAddress);
        //创建邮件
        Messaging.SingleEmailMessage singleEmail = new Messaging.SingleEmailMessage();
        //设置发件人在Org中配置的Id
        singleEmail.setOrgWideEmailAddressId(orgEmailAddress.Id);
        //设置主题
        singleEmail.setSubject(emailTem.Subject);
        //设置收件人地址
        singleEmail.setToAddresses(new String[]{ toAddress });
        // List<String> bssAddresss = new List<String>{ '<EMAIL>' };
        // singleEmail.bccaddresses = bssAddresss;
        //设置邮件HTML内容
        singleEmail.htmlbody = emailTem.HtmlValue;
        //设置邮件TEXT内容
        singleEmail.plaintextbody = emailTem.Body;
        //是否存为活动
        singleEmail.setSaveAsActivity(false);
        //设置展示在邮件上的发件人名字
        //singleEmail.setSenderDisplayName(orgEmailAddress.DisplayName);
        //发送邮件
        Messaging.sendEmail(new Messaging.SingleEmailMessage[]{ singleEmail });
    }
    public static Map<String, String> newCountryMap = new Map<String, String>{ 'Latin America' => 'Spain', 'Lithuania' => 'Lithuania', 'Hungary' => 'Hungary', 'Estonia' => 'United_Kingdom', 'Italy' => 'Italy', 'Austria' => 'Austria', 'Ireland' => 'Ireland', 'Australia' => 'Australia', 'Netherlands' => 'Netherlands', 'Belgium-Dutch' => 'Belgium-Dutch', 'Belgium-French' => 'Belgium-French', 'Finland' => 'Finland', 'France' => 'France', 'Germany' => 'Germany', 'Zealand' => 'Zealand', 'Switzerland' => 'Switzerland', 'Switzerland-French' => 'Switzerland-French', 'Switzerland-German' => 'Switzerland-German', 'Switzerland-Italian' => 'Switzerland-Italian' };
    /**
     * add by vince 0926
     * 判断commercial是否重复
     */
    public static Boolean isCommercialCompanyDuplicated(CEU_HerokuEntity.ComercialUserRequestEntity userRequest){
        String strAccId = userRequest.customerId;
        String strPostCode = userRequest.postcode;
        String strCountry = userRequest.country;
        String strCompany = userRequest.company;

        List<Account> lstCustomer = new List<Account>();
        Boolean isDuplicated = false;

        //根据id判断是新增/更新，执行不同的查询语句校验重复记录
        lstCustomer = getCommercialAccount(strPostCode, strCountry, strCompany, strAccId);

        //查询结果不为空，则有重复记录
        if (lstCustomer.size() > 0){
            isDuplicated = true;
        }

        return isDuplicated;
    }
    public static List<Account> getCommercialAccount(String strPostCode, String strCountry, String strCompany, String strAccId){

        List<Account> lstCustomer = new List<Account>();
        String comercialRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Commercial_Consumer').getRecordTypeId();
        String strSoqlAcc = '';
        if (strAccId == null){
            strSoqlAcc += 'select id,ShippingPostalCode,ShippingCountry,Company__c from Account ' + 
                       ' where ShippingPostalCode =:strPostCode and ShippingCountry =:strCountry and Company__c =:strCompany and RecordTypeId =:comercialRecordTypeId ';
        } else{
            strSoqlAcc += 'select id,ShippingPostalCode,ShippingCountry,Company__c from Account ' + 
                       ' where id !=:strAccId and ShippingPostalCode =:strPostCode and ShippingCountry =:strCountry and Company__c =:strCompany and RecordTypeId =:comercialRecordTypeId ';
        }
        lstCustomer = Database.query(strSoqlAcc);

        return lstCustomer;
    }
    /**
     * add by vince 0926
     * 判断commercial是否重复
     */
    public static Boolean isCommercialContactDuplicated(CEU_HerokuEntity.ComercialUserRequestEntity userRequest){
        String strConId = userRequest.userId;
        String strEmail = userRequest.emailAddress;
        String strSoqlAcc = '';
        List<Contact> lstContact = new List<Contact>();
        Boolean isDuplicated = false;


        //根据id判断是新增/更新，执行不同的查询语句校验重复记录

        lstContact = getCommercialContact(strEmail, strConId);

        //查询结果不为空，则有重复记录
        if (lstContact.size() > 0){
            isDuplicated = true;
        }

        return isDuplicated;
    }
    public static List<Contact> getCommercialContact(String strEmail, String strConId){

        List<Contact> lstContact = new List<Contact>();
        String comercialRecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByDeveloperName().get('Commercial_Contact').getRecordTypeId();
        String strSoqlAcc = '';
        if (strConId == null){
            strSoqlAcc += 'Select Email, FirstName, LastName from Contact  ' + 
                       ' where Email =:strEmail and RecordTypeId =:comercialRecordTypeId ';
        } else{
            strSoqlAcc += 'Select Email, FirstName, LastName from Contact  ' + 
                       ' where id !=:strConId and Email =:strEmail and RecordTypeId =:comercialRecordTypeId ';
        }
        lstContact = Database.query(strSoqlAcc);

        return lstContact;
    }
    /**
     * add by vince 0926
     * 判断commercial是否重复
     */
    public static Boolean isInvitedCommercialContactDuplicated(CEU_HerokuEntity.InvitedComercialUserRequestEntity userRequest){
        String strConId = userRequest.userId;
        String strEmail = userRequest.emailAddress;
        String strSoqlAcc = '';
        List<Contact> lstContact = new List<Contact>();
        Boolean isDuplicated = false;
        String comercialRecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByDeveloperName().get('Commercial_Contact').getRecordTypeId();


        //根据id判断是新增/更新，执行不同的查询语句校验重复记录
        if (strConId == null){
            strSoqlAcc += 'Select Email, FirstName, LastName from Contact  ' + 
                       ' where Email =:strEmail and RecordTypeId =:comercialRecordTypeId ';
        } else{
            strSoqlAcc += 'Select Email, FirstName, LastName from Contact  ' + 
                       ' where id !=:strConId and Email =:strEmail and RecordTypeId =:comercialRecordTypeId ';
        }
        lstContact = Database.query(strSoqlAcc);

        //查询结果不为空，则有重复记录
        if (lstContact.size() > 0){
            isDuplicated = true;
        }

        return isDuplicated;
    }

    public static void sendNewsLetterRegistrationEmail(String accId, String email, String name, String firstName) {
        Log__c logInfo = new Log__c(
            Name = 'EUHerokuAPI ' + DateTime.now()
        );
        logInfo.ApexName__c = 'CEU_HerokuAPIUtils';
        logInfo.Method__c = 'sendNewsLetterNoticeEmail';
        EmailTemplate emailTem = null;
        List<String> etDevNameList = new List<String>{ 'NewsLetter_Email_EU' };
        String emailDeveloperName = 'NewsLetter_Email';
        Account consumerRecord;
        if(String.isNotBlank(accId)) {
            consumerRecord = getResidentialUserByID(accId);
        }
        if(consumerRecord != null) {
            name = consumerRecord.Name;
            firstName = consumerRecord.FirstName;
            if (String.isNotBlank(consumerRecord.ShippingCountry)){
                String a = consumerRecord.ShippingCountry;
                String emailDeveloperName1 = emailDeveloperName + '_' + a;
                etDevNameList.add(emailDeveloperName1);
            }
            if (String.isNotBlank(consumerRecord.Language_For_Consumer__c)){
                String a = consumerRecord.Language_For_Consumer__c;
                String emailDeveloperName1 = emailDeveloperName + '_' + a;
                etDevNameList.add(emailDeveloperName1);
            }
        }
        List<EmailTemplate> templatelist = [SELECT Id, Body, HtmlValue, Subject, DeveloperName
                                            FROM EmailTemplate
                                            WHERE DeveloperName IN :etDevNameList AND IsActive = true];
        if (templatelist.size() > 1){
            for (EmailTemplate etTemp : templatelist){
                if (etTemp.DeveloperName != 'NewsLetter_Email_EU'){
                    emailTem = etTemp;
                }
            }
        } else if (templatelist.size() > 0){
            emailTem = templatelist[0];
        }
    
        if (emailTem == null){
            logInfo.Error_Message__c = 'No email template searched. Account(' + JSON.serialize(consumerRecord) + ').';
            insert logInfo;
            return;
        }
    
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'OrgWideEmailAddress is Null.';
            insert logInfo;
            return;
        }
    
        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
    
        if(consumerRecord != null) {
            if(String.isNotBlank(consumerRecord.ShippingCountry)) {
                String a = '_' + consumerRecord.ShippingCountry;
                String key2 = key.replace('_EU', a);
                keySet.add(key2);
            }
            if(String.isNotBlank(consumerRecord.Language_For_Consumer__c)) {
                String a = '_' + consumerRecord.Language_For_Consumer__c;
                String key2 = key.replace('_EU', a);
                keySet.add(key2);
            }
            if(String.isNotBlank(consumerRecord.Site_Origin__pc)) {
                String site = consumerRecord.Site_Origin__pc;
                if(siteOriginMap.containsKey(consumerRecord.Site_Origin__pc)) {
                    site = siteOriginMap.get(consumerRecord.Site_Origin__pc);
                }
                String a = '_' + site;
                String key2 = key.replace('_EU', a);
                keySet.add(key2);
            }
        }
    
        String urlStr = '';
        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN :keySet];
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }
    
        emailTem.Subject = emailTem.Subject.replace('[USER_NAME]', name);
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[FIRST_NAME]', firstName);
        sendEmail(orgEmailAddress, email, emailTem);
    }
    
    public static void sendNewsLetterRegistrationEmail(Account acc) {
        Log__c logInfo = new Log__c(
            Name = 'EUHerokuAPI ' + DateTime.now()
        );
        logInfo.ApexName__c = 'CEU_HerokuAPIUtils';
        logInfo.Method__c = 'sendNewsLetterRegisterEmail';
        //查找模板
        // Add by yujie for EU
        EmailTemplate emailTem = getNewsLetterRegisterEmailTemplate(acc.Site_Origin__pc, acc.Language_For_Consumer__c);
        if (emailTem == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'No email template searched. Account(' + JSON.serialize(acc) + ').';
            insert logInfo;
    
            return;
        }
        // OrgWideEmailAddress orgEmailAddress = getOrgWideEmailAddressBySystemConfigurationName('EGO CRM', brandName);
        List<OrgWideEmailAddress> listAddresses = [SELECT Id
                                                   FROM OrgWideEmailAddress
                                                   WHERE DisplayName = 'EGO CRM'];
        OrgWideEmailAddress orgEmailAddress = new OrgWideEmailAddress();
        orgEmailAddress = listAddresses.isEmpty() ? null : listAddresses[0];
        if (orgEmailAddress == null){
            // 2022-06-29: 新增记录Log
            logInfo.Error_Message__c = 'OrgWideEmailAddress is Null.';
            insert logInfo;
    
            return;
        }
    
        String key = '';
        Set<String> keySet = new Set<String>();
        if (CCM_Service.IsSandboxOrg()){
            key = 'Web_Sandbox_EU';
        } else{
            key = 'Web_Production_EU';
        }
        keySet.add(key);
        if (String.isNotBlank(acc.ShippingCountry)){
            String a = '_' + acc.ShippingCountry;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        if(String.isNotBlank(acc.Site_Origin__pc)) {
            String site = acc.Site_Origin__pc;
            if(siteOriginMap.containsKey(acc.Site_Origin__pc)) {
                site = siteOriginMap.get(acc.Site_Origin__pc);
            }
            String a = '_' + site;
            String key2 = key.replace('_EU', a);
            keySet.add(key2);
        }
        String urlStr = '';
    
        List<MeIntegration_Setting__mdt> mdtList = [SELECT DeveloperName, End_Point__c, Key__c, Label, password__c, userName__c
                                                    FROM MeIntegration_Setting__mdt
                                                    WHERE DeveloperName IN:keySet];
        if (mdtList.size() == 2){
            for (MeIntegration_Setting__mdt mdt : mdtList){
                if (mdt.DeveloperName <> key){
                    urlStr = mdt.End_Point__c;
                }
            }
        } else if (mdtList.size() > 0){
            urlStr = mdtList[0].End_Point__c;
        }
    
        emailTem.HtmlValue = emailTem.HtmlValue.replace('[FIRST_NAME]', acc.FirstName);
        emailTem.Body = emailTem.Body.replace('[FIRST_NAME]', acc.FirstName);
        sendEmail(orgEmailAddress, acc.PersonEmail, emailTem);
    }
    
    public static EmailTemplate getNewsLetterRegisterEmailTemplate(String siteOrigin, String language) {
        // 2022-06-22(update)
        List<String> etDevNameList = new List<String>{ 'NewsLetter_Email_EU' };
        //Add by yujie for EU
        String emailDeveloperName = 'NewsLetter_Email';
        //add by yujie for EU
        if (siteOrigin != 'United States'){
            String a = siteOrigin;
            a = newCountryMap.get(a) != null ? newCountryMap.get(a) : a;
            String emailDeveloperNameTemp = emailDeveloperName + '_' + a.replace(' ', '_').replace('-', '_');
            //【Start Add】Vince 2023-12-06
    
            // if (String.isNotEmpty(contactLanguage)){
            //     emailDeveloperName += contactLanguage;
            // } else if (String.isNotEmpty(language)){
            //     emailDeveloperName += language;
            // }
            //【End Add】Vince 2023-12-06
            // 2022-06-22(update)
            etDevNameList.add(emailDeveloperNameTemp);
        }
    
        if(String.isNotBlank(language)) {
            String emailDeveloperNameTemp = emailDeveloperName + '_' + language;
            etDevNameList.add(emailDeveloperNameTemp);
        }
    
        EmailTemplate et = null;
        for (EmailTemplate etTemp : [SELECT Id, Body, HtmlValue, Subject, DeveloperName
                                     FROM EmailTemplate
                                     WHERE DeveloperName IN:etDevNameList AND IsActive = true]){
            // 2022-06-22(update)
            if (et == null){
                et = etTemp;
            } else if (etTemp.DeveloperName <> 'NewsLetter_Email_EU'){
                et = etTemp;
            }
        }
        System.debug(LoggingLevel.INFO, '*** et: ' + et.DeveloperName);
        return et;
    }
}