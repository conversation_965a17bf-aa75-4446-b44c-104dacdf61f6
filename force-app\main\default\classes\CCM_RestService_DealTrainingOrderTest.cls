@isTest
public class CCM_RestService_DealTrainingOrderTest {
	@IsTest
    static void testTrainingOrder(){
        RestRequest req = new RestRequest(); 
        RestResponse res = new RestResponse();             
        req.requestURI = '/services/apexrest/CCM_RestService_DealOrderInfoEU';
        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response= res;
        String str = '[{"ATTRIBUTE4": "ORDER", "TOTAL_AMOUNT": "499.8", "PRICE_DATE": "2023-11-15 00:00:00", "ORDER_NUMBER_ORACLE": "1038584", "ORDER_STATUS": "BOOKED", "HEADER_DISCOUNT_AMOUNT": "0", "DROPSHIP_ZIP": "", "HEADERID": "177509", "TOTAL_VALUE": "420", "EXPECTED_DELIVERY_DATE": "", "DROPSHIP_ADDRESS2": "", "DROPSHIP_ADDRESS1": "", "SHPPING_PLACE": "Hamburg", "HEADER_DISCOUNT": "0", "SCENE_TYPE": "Training Request", "ORDER_ORACLE_ID": "591807", "CARRIER_INFORMATION": "", "FREIGHT_TERM": "", "INCO_TERM": "DAP", "DROPSHIP_CITY": "", "IS_DROPSHIP": "N", "VAT": "79.8", "DROPSHIP_STATE": "", "PAYMENT_TERMS": "EEG-EDE", "TOTAL_VALUE_NET": "420", "BILLTO": "180634", "OrderLine": [{"SCHEDULE_SHIP_DATE": "2023-11-15 23:59:00", "INVOICED_QTY": "0", "UNIT_SELLING_PRICE": "140", "HEADERID": "177509", "REQUEST_DATE": "2023-11-14 00:00:00", "BACK_ORDER_QTY": "0", "RELEASE_TO_WAREHOUSE_QTY": "0", "UOM": "EA", "CANCELLED_QTY": "0", "RESOURCE_ORDERNUMBER": "", "RESOURCE_HEADERID": "", "SALES_AGREEMENT_NO": "", "LIST_PRICE": "140", "EST_REPLENISH_DATE": null, "PRODUCT_MODEL": "Training Z6", "RESOURCE_LINEID": "", "SHIPPED_QTY": "0", "SUB_INVENTORY_CODE": "", "ORDER_QUANTITY": "1", "SERIAL_NUMBER": "", "PICK_AND_PACKED_QTY": "0", "ORDER_LINE_STATUS": "FULFILLED", "ORDERLINE_NUMBER": "1", "ORDERLINE_CRM_ID": "a1tQI000001DkviYAC", "RESOURCE_LINENUMBER": "", "LINEID": "760618", "REMARK": "mingqian", "PRODUCT_DESCRIPTION": "Teilnahmegeb\u00fchr Z6 Schulung", "ORDERLINE_ORACLE_ID": "2317342", "SALES_PRICE": "0", "PRODUCT_RATING_STATUS": "", "TOTAL_NET_PRICE": "140"}], "CURRENCY_CODE": "EUR", "ORDER_HEADERID": null, "CUSTOMER": "E01041", "SHIPTO": "180654", "DROPSHIP_COUNTRY": "", "FERIGHT_FEE": "0", "WAREHOUSE": "EEG", "SALES_REP_EMAIL": "<EMAIL>", "PRICE_LIST": "EEG IMP SSP EUR - ab 2020", "DATE_ORDER": "2023-11-14 00:00:00", "ORDER_NUMBER_CRM": "a1kQI000009s9fRYAQ", "DROPSHIP_NAME": "", "SALES_REP_CODE": "11343", "ORDER_TYPE": "EEG Training Domestic", "INSTRUCTION_TO_DSV": "", "COMMENT_TO_CUSTOMER": "", "PO_NUMBER": "2023-11-15 00:30:00-12:30:00"}]';
        req.requestBody = Blob.valueOf(str);
        Test.startTest();
        CCM_UpsertOrder.ResultObj Result = CCM_UpsertOrder.doPost();
        Test.stopTest();
    }
    
    @IsTest
    static void testTrainingOrderException(){
        RestRequest req = new RestRequest(); 
        RestResponse res = new RestResponse();             
        req.requestURI = '/services/apexrest/CCM_RestService_DealOrderInfoEU';
        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response= res;
        String str = '[{"ATTRIBUTE4": "ORDER", "TOTAL_AMOUNT": "499.8", "PRICE_DATE": "2023-11 00:00:00", "ORDER_NUMBER_ORACLE": "1038584", "ORDER_STATUS": "BOOKED", "HEADER_DISCOUNT_AMOUNT": "0", "DROPSHIP_ZIP": "", "HEADERID": "177509", "TOTAL_VALUE": "420", "EXPECTED_DELIVERY_DATE": "", "DROPSHIP_ADDRESS2": "", "DROPSHIP_ADDRESS1": "", "SHPPING_PLACE": "Hamburg", "HEADER_DISCOUNT": "0", "SCENE_TYPE": "Training Request", "ORDER_ORACLE_ID": "591807", "CARRIER_INFORMATION": "", "FREIGHT_TERM": "", "INCO_TERM": "DAP", "DROPSHIP_CITY": "", "IS_DROPSHIP": "N", "VAT": "79.8", "DROPSHIP_STATE": "", "PAYMENT_TERMS": "EEG-EDES", "TOTAL_VALUE_NET": "420", "BILLTO": "180634", "OrderLine": [{"SCHEDULE_SHIP_DATE": "2023-11-15 23:59:00", "INVOICED_QTY": 0, "UNIT_SELLING_PRICE": "140", "HEADERID": "177509", "REQUEST_DATE": "2023-11-14 00:00:00", "BACK_ORDER_QTY": "0", "RELEASE_TO_WAREHOUSE_QTY": "0", "UOM": "EA", "CANCELLED_QTY": "0", "RESOURCE_ORDERNUMBER": "", "RESOURCE_HEADERID": "", "SALES_AGREEMENT_NO": "", "LIST_PRICE": "140", "EST_REPLENISH_DATE": null, "PRODUCT_MODEL": "Training Z6", "RESOURCE_LINEID": "", "SHIPPED_QTY": "0", "SUB_INVENTORY_CODE": "", "ORDER_QUANTITY": "1", "SERIAL_NUMBER": "", "PICK_AND_PACKED_QTY": "0", "ORDER_LINE_STATUS": "FULFILLED", "ORDERLINE_NUMBER": "1", "ORDERLINE_CRM_ID": "a1tQI000001DkviYAC", "RESOURCE_LINENUMBER": "", "LINEID": "760618", "REMARK": "mingqian", "PRODUCT_DESCRIPTION": "Teilnahmegeb\u00fchr Z6 Schulung", "ORDERLINE_ORACLE_ID": "2317342", "SALES_PRICE": "0", "PRODUCT_RATING_STATUS": "", "TOTAL_NET_PRICE": "140"}], "CURRENCY_CODE": "EUR", "ORDER_HEADERID": null, "CUSTOMER": "E01041", "SHIPTO": "180654", "DROPSHIP_COUNTRY": "", "FERIGHT_FEE": "0", "WAREHOUSE": "EEG", "SALES_REP_EMAIL": "<EMAIL>", "PRICE_LIST": "EEG IMP SSP EUR - ab 2020", "DATE_ORDER": "2023-11-14 00:00:00", "ORDER_NUMBER_CRM": "a1kQI000009s9fRYAQ", "DROPSHIP_NAME": "", "SALES_REP_CODE": "11343", "ORDER_TYPE": "EEG Training Domestic", "INSTRUCTION_TO_DSV": "", "COMMENT_TO_CUSTOMER": "", "PO_NUMBER": "2023-11-15 00:30:00-12:30:00"}]';
        req.requestBody = Blob.valueOf(str);
        Test.startTest();
        CCM_UpsertOrder.ResultObj Result = CCM_UpsertOrder.doPost();
        Test.stopTest();
    }
}