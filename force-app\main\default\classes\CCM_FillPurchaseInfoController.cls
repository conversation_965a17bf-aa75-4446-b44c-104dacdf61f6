/**
 * Authoer: Honey
 * Description: 下单页面第三步填写Purchase Order详细信息
 * Date : 2023-06-15
 */
public without sharing class CCM_FillPurchaseInfoController {
    //进入页面初始化--->根据PurchaseOrder信息查询Is DropShip。以及Address信息的下拉框
    @AuraEnabled
    public static String InitDate(String PurchaseId){
        //-->根据PurchaseId查询CustomerId以及是否DropShip
        Purchase_Order__c objPurchaseOrder = [
            SELECT Id,Is_DropShip__c,Customer__c,DropShip_Type__c FROM Purchase_Order__c WHERE Id = :PurchaseId LIMIT 1
        ];
        //返回是否Dropship用于校验某个字段是否必填
        return JSON.serialize(objPurchaseOrder) ;
    }
    // TODO,部署重载，需要使用下面的方法
    public static string QueryAddressByName(String AddressName){
       return '';
    }
    @AuraEnabled
    public static String  QueryAddressByName(String AddressName,String customerId,String recordTypeName){
        List<AddressInfo> lstAddressInfo = new List<AddressInfo>();
        if(String.isNotBlank(AddressName)){
            AddressName = '%'+AddressName+'%';
        }
        
        try {
            List<Account_Address__c> lstAddress = new List<Account_Address__c>();
            if(String.isNotBlank(AddressName)){
                lstAddress =    [
                    SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                    RecordType.Id, RecordType.Name,Company_name_1__c,Primary__c
                    FROM Account_Address__c WHERE Final_Address__c LIKE :AddressName AND 
                    RecordType.Name = :recordTypeName AND StatusNew__c ='Active' AND (Customer__c = :customerId OR Prospect__c = :customerId)
                ];
            }else{
                lstAddress = [
                    SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                    RecordType.Id, RecordType.Name,Company_name_1__c,Primary__c
                    FROM Account_Address__c WHERE RecordType.Name = :recordTypeName 
                    AND StatusNew__c ='Active' AND (Customer__c = :customerId OR Prospect__c = :customerId)
                ];

            }
             
            system.debug('lstAddress-->'+lstAddress.size());
            for(Account_Address__c objAccountAddress : lstAddress){
                AddressInfo objAddressInfo = new AddressInfo();
                AddressDetail objAddressDetail = new AddressDetail();
                objAddressInfo.Id = objAccountAddress.Id;
                objAddressInfo.Name = objAccountAddress.Final_Address__c;
                objAddressDetail.Country = objAccountAddress.Country__c;
                objAddressDetail.City = objAccountAddress.City__c;
                objAddressDetail.Street = objAccountAddress.Street_1__c;
                objAddressDetail.PostalCode = objAccountAddress.Postal_Code__c;
                objAddressDetail.CompanyName = objAccountAddress.Company_name_1__c;
                objAddressDetail.Primary = objAccountAddress.Primary__c;
                objAddressInfo.address = objAddressDetail;
                lstAddressInfo.add(objAddressInfo);
            }
            
        } catch (Exception e) {
           system.debug('报错信息--->'+e.getMessage()+'报错行数--->'+e.getLineNumber());
        }
        system.debug('JSON.serialize(lstAddressInfo)-->'+JSON.serialize(lstAddressInfo));
        return JSON.serialize(lstAddressInfo);
    }
    @AuraEnabled
    public static String UpdatePoInfo(String StringRequestBody, String pricingDate){
        Map<String, Object> resultMap = new Map<String, Object>();
        RequestBody objRequestBody = (RequestBody)JSON.deserialize(StringRequestBody, RequestBody.class);
        //根据PurchaseOrderId查询PurchaseOrder信息
        Purchase_Order__c objUpdatePurchaseOrder = [
            SELECT Id,Billing_Address__c,Dropship_Address__c,Shipping_Address__c,Authorized_Brand__c,Invetory__c,Current_Step__c,
            Additional_Contact_Name__c,Additional_Shipping_Street__c,Additional_Shipping_Street2__c,Is_DropShip__c,Freight_Fee__c,
            WearHouse_In_EBS__c,ORG_ID__c,CurrencyIsoCode,Customer__c,Customer__r.CurrencyIsoCode,Shipping_Address__r.City__c,Dropship_Address__r.City__c,
            Additional_Contact_Phone__c,Additional_Shipping_Country__c,Additional_Shipping_Postal_Code__c,
            Additional_Shipping_Province__c,Additional_Shipping_City__c,Inco_Term__c,
            Insurance_Fee__c,Other_Fees__c,DropShip_Type__c FROM Purchase_Order__c WHERE Id = :objRequestBody.PurchaseOrderId
        ];

        List<String> errorMsgList = checkFeeInPriceBook(objUpdatePurchaseOrder.Customer__c, objRequestBody, pricingDate);
        if(!errorMsgList.isEmpty()) {
            resultMap.put('isSuccess', false);
            resultMap.put('errorMsg', String.join(errorMsgList, '\n'));
            return JSON.serialize(resultMap);
        }

        //存储Type以及地址信息
        objUpdatePurchaseOrder.DropShip_Type__c = objRequestBody.DropShipType;
        objUpdatePurchaseOrder.Freight_Fee__c = objRequestBody.FreightCost;
        objUpdatePurchaseOrder.Billing_Address__c = objRequestBody.BillAddressId;
        if(String.isNotBlank(objRequestBody.ShipAddressId)){
            objUpdatePurchaseOrder.Shipping_Address__c = objRequestBody.ShipAddressId;
        }
        
        objUpdatePurchaseOrder.WearHouse_In_EBS__c = 'EEG';
        objUpdatePurchaseOrder.ORG_ID__c = 'EEG';
        objUpdatePurchaseOrder.CurrencyIsoCode = objUpdatePurchaseOrder.Customer__r.CurrencyIsoCode;
        if(String.isNotBlank(objRequestBody.DropShipAddressId)){
            objUpdatePurchaseOrder.Dropship_Address__c = objRequestBody.DropShipAddressId;
        }
        
        //根据BillAddress和ShipAddress的Id 和Auth Brand 唯一确认一个
        Set<String> setAddressId = new Set<String>();
        setAddressId.add(objRequestBody.BillAddressId);
        setAddressId.add(objRequestBody.ShipAddressId);
        if(String.isNotBlank(objRequestBody.DropShipAddressId)){
            setAddressId.add(objRequestBody.DropShipAddressId);
        }
        
        Map<String,String> mapAddressId2ShippingCity = new Map<String,String>();
        //Address_With_Program__c
        list<Address_With_Program__c> lstAddress = [
            SELECT Id,Account_Address__c,Program__c ,Account_Address__r.City__c
            FROM Address_With_Program__c 
            WHERE Account_Address__c IN:setAddressId 
            
        ];
        //查询Address 
        List<Account_Address__c> lstAccountAddress = [
            SELECT Id,City__c FROM Account_Address__c WHERE Id IN :setAddressId  AND StatusNew__c ='Active'];
        for(Account_Address__c objAccountAddress  :lstAccountAddress){
            mapAddressId2ShippingCity.put(objAccountAddress.Id,objAccountAddress.City__c);
        }
        Map<String,String> mapAddressId2Id = new Map<String,String>();
        for(Address_With_Program__c objAddress : lstAddress){
          mapAddressId2Id.put(objAddress.Account_Address__c,objAddress.Id);
          
        }
        objUpdatePurchaseOrder.ShipTo__c = mapAddressId2Id.get(objRequestBody.ShipAddressId);
        objUpdatePurchaseOrder.BillTo__c = mapAddressId2Id.get(objRequestBody.BillAddressId);
        objUpdatePurchaseOrder.Insurance_Fee__c = objRequestBody.InsuranceFee;
        
        objUpdatePurchaseOrder.Other_Fees__c = objRequestBody.OtherFee;
        objUpdatePurchaseOrder.Comments__c = objRequestBody.Comments;
        objUpdatePurchaseOrder.Instruction_To_Dsv__c = objRequestBody.InstructionToDSV;
        if(CCM_Constants.END_CONSUMER.equals(objRequestBody.DropShipType)){
            //如果是C端客户。则需要用户手动填写DropShip地址
            objUpdatePurchaseOrder.Additional_Contact_Name__c =  objRequestBody.DropShipName;
            objUpdatePurchaseOrder.Additional_Shipping_Street__c =  objRequestBody.DropShipAddress1;
            objUpdatePurchaseOrder.Additional_Shipping_Street2__c =  objRequestBody.DropShipAddress2;
            objUpdatePurchaseOrder.Additional_Contact_Phone__c =  objRequestBody.DropShipPhone;
            objUpdatePurchaseOrder.Additional_Shipping_Country__c =  objRequestBody.DropShipCountry;
            objUpdatePurchaseOrder.Additional_Shipping_Postal_Code__c = objRequestBody.DropShipZip;
            objUpdatePurchaseOrder.Additional_Shipping_Province__c  =  objRequestBody.DropShipState;
            objUpdatePurchaseOrder.Additional_Shipping_City__c =  objRequestBody.DropShipCity;
        
        }
        //Shipping Place根据Icon Term联动
        if(objUpdatePurchaseOrder.Inco_Term__c == 'FCA'|| objUpdatePurchaseOrder.Inco_Term__c == 'EXW'){
            objUpdatePurchaseOrder.Shipping_Place__c = 'Möckmühl';
        }else{
            //校验是否为DropSHip 
            if(objUpdatePurchaseOrder.Is_DropShip__c == true){
                //如果是DropShip就要取Dropship的City
                objUpdatePurchaseOrder.Shipping_Place__c = objRequestBody.DropShipCity == null ? mapAddressId2ShippingCity.get(objRequestBody.DropshipAddressId) : objRequestBody.DropShipCity;
            }else if(objUpdatePurchaseOrder.Is_DropShip__c == false || objUpdatePurchaseOrder.Is_DropShip__c == null){
                objUpdatePurchaseOrder.Shipping_Place__c = mapAddressId2ShippingCity.get(objRequestBody.ShipAddressId);
            }
        }
        //存入SalesRep-->根据Customer的Owner
        //根据CustomerId查询Customer信息
        Account objAccount = [
            SELECT OwnerId,Id FROM Account WHERE Id = :objRequestBody.CustomerId
        ];
        objUpdatePurchaseOrder.Salesperson__c = objAccount.OwnerId;
        //更新当前步骤
        objUpdatePurchaseOrder.Current_Step__c = objUpdatePurchaseOrder.Current_Step__c >  CCM_Constants.FILL_IN_PO ? 
            objUpdatePurchaseOrder.Current_Step__c : CCM_Constants.FILL_IN_PO;
        update objUpdatePurchaseOrder;
        resultMap.put('isSuccess', true);
        return JSON.serialize(resultMap);
    }

    private static List<String> checkFeeInPriceBook(String customerId, RequestBody objRequestBody, String pricingDate) {
        List<String> errorMsgList = new List<String>();
        Set<String> customerIds = new Set<String>{customerId};
        Map<String, Set<String>> customerFeeTypesMap = CCM_PurchaseOrder_FeeCheckUtil.checkFeeInPriceBook(customerIds, pricingDate);
        if(customerFeeTypesMap.containsKey(customerId)) {
            if(objRequestBody.FreightCost != null && objRequestBody.FreightCost != 0) {
                String feeType = 'freight';
                Boolean freightFeeInPriceBook = customerFeeTypesMap.get(customerId).contains(feeType);
                if(!freightFeeInPriceBook) {
                    errorMsgList.add(Label.CCM_Freight_Fee_Not_In_PriceBook);
                }
            }
            if(objRequestBody.InsuranceFee != null && objRequestBody.InsuranceFee != 0) {
                String feeType = 'insurance';
                Boolean insuranceFeeInPriceBook = customerFeeTypesMap.get(customerId).contains(feeType);
                if(!insuranceFeeInPriceBook) {
                    errorMsgList.add(Label.CCM_Insurance_Fee_Not_In_PriceBook);
                }
            }
            if(objRequestBody.OtherFee != null && objRequestBody.OtherFee != 0) {
                String feeType = 'otherfee';
                Boolean otherFeeInPriceBook = customerFeeTypesMap.get(customerId).contains(feeType);
                if(!otherFeeInPriceBook) {
                    errorMsgList.add(Label.CCM_Other_Fee_Not_In_PriceBook);
                }
            }
        }
        return errorMsgList;
    }

    @AuraEnabled
    public static Map<String,Object> checkAddressInfo(String customerId){
        system.debug('customerId---->'+customerId);
        Map<String,Object> mapType2Address = new Map<String,Object>();
        try {
            //通过CustomerId查询AddressInfo 
            List<Account_Address__c> lstAddress = [
                SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                RecordType.Id, RecordType.DeveloperName,Company_name_1__c
                FROM Account_Address__c WHERE  Customer__c = :customerId  AND StatusNew__c = 'Active'
            ];
            List<Account_Address__c> lstShipAddress = new List<Account_Address__c>();
            List<Account_Address__c> lstBillAddress = new List<Account_Address__c>();
            List<Account_Address__c> lstDropAddress = new List<Account_Address__c>();
            for(Account_Address__c objAcocuntAddress : lstAddress){
                system.debug('objAcocuntAddress.RecordType.DeveloperName-->'+objAcocuntAddress.RecordType.DeveloperName);
                if('Shipping_Address'.equals(objAcocuntAddress.RecordType.DeveloperName)){
                    lstShipAddress.add(objAcocuntAddress);

                }else if('Billing_Address'.equals(objAcocuntAddress.RecordType.DeveloperName)){
                    lstBillAddress.add(objAcocuntAddress);

                }else if('Dropship_Shipping_Address'.equals(objAcocuntAddress.RecordType.DeveloperName)){
                    lstDropAddress.add(objAcocuntAddress);

                }

            }
            //只有一个数据的时候。返回到前端
            system.debug('lstShipAddress--->'+lstShipAddress.size());
            system.debug('lstBillAddress--->'+lstBillAddress.size());
            system.debug('lstDropAddress--->'+lstDropAddress.size());
            if(lstShipAddress.size() ==1 ){
                AddressInfo objShipAddressInfo = new AddressInfo();
                objShipAddressInfo.Id = lstShipAddress[0].Id;
                objShipAddressInfo.Name = lstShipAddress[0].Final_Address__c;
                AddressDetail objShipAddressDetail = new AddressDetail();
                objShipAddressDetail.City = lstShipAddress[0].City__c;
                objShipAddressDetail.Country = lstShipAddress[0].Country__c;
                objShipAddressDetail.Street = lstShipAddress[0].Street_1__c;
                objShipAddressDetail.PostalCode = lstShipAddress[0].Postal_Code__c;
                objShipAddressDetail.CompanyName = lstShipAddress[0].Company_name_1__c;
                objShipAddressInfo.address = objShipAddressDetail;
                system.debug('objShipAddressInfo-->'+objShipAddressInfo);
                mapType2Address.put('shippingAddress', Json.serialize(objShipAddressInfo)   );
            }

            if(lstBillAddress.size() ==1 ){
                AddressInfo objBillAddressInfo = new AddressInfo();
                objBillAddressInfo.Id = lstBillAddress[0].Id;
                objBillAddressInfo.Name = lstBillAddress[0].Final_Address__c;
                AddressDetail objBillAddressDetail = new AddressDetail();
                objBillAddressDetail.City = lstBillAddress[0].City__c;
                objBillAddressDetail.Country = lstBillAddress[0].Country__c;
                objBillAddressDetail.Street = lstBillAddress[0].Street_1__c;
                objBillAddressDetail.PostalCode = lstBillAddress[0].Postal_Code__c;
                objBillAddressInfo.address = objBillAddressDetail;
                system.debug('objBillAddressInfo-->'+objBillAddressInfo);
                mapType2Address.put('billingAddress', Json.serialize(objBillAddressInfo) );
            }
            if(lstDropAddress.size() ==1 ){
                AddressInfo objDropAddressInfo = new AddressInfo();
                objDropAddressInfo.Id = lstDropAddress[0].Id;
                objDropAddressInfo.Name = lstDropAddress[0].Final_Address__c;
                AddressDetail objDropAddressDetail = new AddressDetail();
                objDropAddressDetail.City = lstDropAddress[0].City__c;
                objDropAddressDetail.Country = lstDropAddress[0].Country__c;
                objDropAddressDetail.Street = lstDropAddress[0].Street_1__c;
                objDropAddressDetail.PostalCode = lstDropAddress[0].Postal_Code__c;
                objDropAddressInfo.address = objDropAddressDetail;
                system.debug('objDropAddressInfo-->'+objDropAddressInfo);
                mapType2Address.put('dropshipShippingAddress', Json.serialize(objDropAddressInfo) );
            }
            return mapType2Address;
            
        } catch (Exception e) {
            system.debug('报错行数--->'+e.getLineNumber()+'报错信息---->'+e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }
    
    public Class AddressInfo{
        public String Id {get;set;}
        public String Name {get;set;}
        public AddressDetail address {get;set;}
    }
    public Class AddressDetail{
        public String Country {get;set;}
        public String City {get;set;}
        public String Street {get;set;}
        public String PostalCode {get;set;}
        public String CompanyName {get;set;}
        public Boolean Primary {get;set;}
    }
    public Class RequestBody{
        @AuraEnabled public String BillAddressId {get;set;}
        @AuraEnabled public Decimal FreightCost {get;set;}
        @AuraEnabled public String CustomerId {get;set;}
        @AuraEnabled public String ShipAddressId {get;set;}
        @AuraEnabled public String DropShipType {get;set;}
        @AuraEnabled public String DropShipAddressId {get;set;}
        @AuraEnabled public String PurchaseOrderId {get;set;}
        @AuraEnabled public Boolean IsProtal {get;set;}
        @AuraEnabled public Boolean isFirst {get;set;}
        
        @AuraEnabled public String DropShipName {get;set;}
        @AuraEnabled public String DropShipAddress1 {get;set;}
        @AuraEnabled public String DropShipAddress2 {get;set;}
        @AuraEnabled public String DropShipPhone {get;set;}
        @AuraEnabled public String DropShipCountry {get;set;}
        @AuraEnabled public String DropShipCity{get;set;}
        @AuraEnabled public String DropShipZip {get;set;}
        @AuraEnabled public String DropShipState {get;set;}
        
        @AuraEnabled public Double InsuranceFee {get;set;}
        @AuraEnabled public Double OtherFee {get;set;}
        @AuraEnabled public String Comments {get;set;}
        @AuraEnabled public String InstructionToDSV {get;set;}
    }
    public CCM_FillPurchaseInfoController() {

    }
    // add by haibo
    @AuraEnabled
    public static String QueryPirchaseAndItemInfo(String PurchaseOrderId,Boolean IsProtal){
        return CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(PurchaseOrderId,IsProtal);
    }
}