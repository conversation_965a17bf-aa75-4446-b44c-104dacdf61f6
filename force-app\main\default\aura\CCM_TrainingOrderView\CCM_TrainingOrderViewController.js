({
    doInit: function(component, event, helper){
        // 进度条
        var pathDataForInsideSales = [
            {label: $A.get("$Label.c.CCM_NewRequest"), icon: 'edit_form'},
            {label: $A.get("$Label.c.Order_PendingReview"), icon: 'description'},
            {label: $A.get("$Label.c.Confirmed"), icon: 'entitlement'},
            {label: $A.get("$Label.c.CCM_OrderProcessing"), icon: 'edit_form'},
            {label: $A.get("$Label.c.CCM_Invoiced"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.CCM_Cancelled"), icon:'cancel_file_request'}
        ];

        // attachment table
        component.set('v.attachmentColumns', [
            {   
                label: $A.get("$Label.c.CCM_Action"),
                width: '60px',
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${contentId}",
                            variant:"bare",
                            iconName:"utility:preview",
                            alternativeText:"download",
                            onclick: component.getReference('c.doView')
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${contentId}",
                            variant:"bare",
                            iconName:"utility:delete",
                            alternativeText:"delete",
                            onclick: component.getReference('c.doDelete')
                        }
                    },
                ]
            },
            {label: $A.get("$Label.c.CCM_AttachmentName"), fieldName: 'attachmentName'},
            {label: $A.get("$Label.c.CCM_AttachmentType"), fieldName: 'attachmentType'},
            {label: $A.get("$Label.c.CCM_Date"), fieldName:'attachmentDate'},
        ]);
        // attachmentTypeOptions
        component.set('v.attachmentTypeOptions', [
            {
                label: $A.get("$Label.c.CCM_ProfomaInvoice"),
                value: 'Profoma Invoice',
            },
            {
                label: $A.get("$Label.c.CCM_PackingList"),
                value: 'Packing List',
            },
            {
                label: $A.get("$Label.c.CCM_PurchaseOrder"),
                value: 'Purchase Order',
            },
            {
                label: $A.get("$Label.c.CCM_Others"),
                value: 'Others',
            },
        ]);
        component.set('v.processData', pathDataForInsideSales); // aaa

        // var recordId = helper.getUrlParameter('recordId');
        // if(recordId){ // 点眼睛进来的,仅作展示,不可编辑
        //     console.log("有recordId",JSON.stringify(recordId));
        //     component.set('v.recordId', recordId);
        //     helper.queryRegisterInfo(component) // aaa
        // }
        var recordId = helper.getUrlParameter('recordId');
        component.set('v.isBusy',true)
        if(component.get('v.recordId')){
            helper.queryRegisterInfo(component) // aaa
        }else if(recordId){ // 点眼睛进来的,仅作展示,不可编辑
            console.log("有recordId",JSON.stringify(recordId));
            component.set('v.recordId', recordId);
            helper.queryRegisterInfo(component) // aaa
        }
    },
    // 点击Cancel按钮
    onclickCancel: function(component) {
        component.set('v.cancelFlag', true);
    },
    // cancel event
    cancelCourseEvent: function(component, event, helper) {
        let valid = helper.getValidation(component);
        if (!valid) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_FillinRequiredFieldsWarning"),
                "type": "warning",
            }).fire();
            return;
        }
        component.set('v.isBusy',true);
        var action = component.get("c.CancelCourse");
        action.setParams({
            "courseRegisterId": component.get('v.recordId'),
            "reason": component.get('v.cancellationReason'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state == "SUCCESS") {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Success"),
                    "message": $A.get("$Label.c.CCM_CancelSuccessfully"),
                    "type": "success"
                }).fire();
                let url = window.location.origin + '/s/Training';
                window.open(url, '_self'); 
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 点击返回回到list页面
    doBack: function(component) {
        let url = window.location.origin + '/s/Training';
        window.open(url, '_self');
    },
    // 点击返回回到list页面
    Submit: function(component) {
        component.set('v.isBusy',true)
        var action = component.get("c.RegisterSubmit");
        action.setParams({
            JsonApprovalInfoString: JSON.stringify({
                "recordId":component.get('v.recordId'),
                "comments":""
                })
        });
        action.setCallback(this, function (response) {
            console.log(JSON.parse(JSON.stringify(response)),"cccc");
            var state = response.getState();
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                if(results == 'SUCCESS'){
                    console.log(JSON.parse(JSON.stringify(results)),"最终提交");
                    
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Success"),
                        "message": $A.get("$Label.c.CCM_RequestSubmitSuccess"),
                        "type": "success",
                    }).fire();
                    setTimeout(() => {
                        let url = window.location.origin + '/s/training-detail-page?0.recordId=' + component.get('v.recordId');
                        window.open(url, '_self'); 
                    }, 1000);
                }else{
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": results,
                        "type": "error",
                    }).fire();

                }
                
            }
        });
        component.set('v.isBusy',false)
        $A.enqueueAction(action);
    },
    // 点击Edit点击按钮
    onclickEdit: function(component) {
        console.log("我点击了编辑按钮");
        let url = window.location.origin + '/s/coursedetail?0.recordId=' + component.get('v.recordId');
        window.open(url, '_self');
    },
    // 点击同步按钮
    onclickConfirm: function(component) {
        component.set('v.isBusy',true)
        var action = component.get("c.ReceiptCourse");
        action.setParams({
            "courseRegisterId":component.get('v.recordId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                console.log(JSON.parse(JSON.stringify(results)),"点击了Confirm");
                component.set('v.isBusy',false)
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Success"),
                    "message": $A.get("$Label.c.CCM_RequestSubmitSuccess"),
                    "type": "success",
                }).fire();
                setTimeout(() => {
                    let url = window.location.origin + '/s/training-detail-page?0.recordId=' + component.get('v.recordId');
                    window.open(url, '_self'); 
                }, 1000);
            }
        });
        $A.enqueueAction(action);
    },
    doView: function(component, event, helper){
        var contentId = event.getSource().get('v.value');
        console.log(contentId, 'rowData--------------');
        let url = "";
        url = "/apex/TrainingPDFView?recordId=" + contentId;
        window.open(url, "_blank");
        // 打开预览模式
        // var openPreview = $A.get('e.lightning:openFiles');
        // openPreview.fire({
            // recordIds: ['0697Y000004hXuAQAU']
            // recordIds: [contentId]
        // });
    },
    // 从列表上删除附件
    doDelete : function(component, event, helper) {
        let contentId = event.getSource().get('v.value');
        let attachment = component.get('v.attachment');
        console.log(contentId, 'contentId----------');
        attachment = attachment.filter((item)=>{
            return item.contentId !== contentId;
        });
        component.set('v.attachment', attachment);
    },
    showToolList: function(component, event, helper){
        var id = event.currentTarget.getAttribute('id');
        var expanded = event.currentTarget.getAttribute('data-expanded');
        if(expanded == 'true'){
            document.getElementById('tool' + id).style.display = 'none'
            event.currentTarget.setAttribute('data-expanded', false);
        }else{
            document.getElementById('tool' + id).style.display = 'table-row';
            event.currentTarget.setAttribute('data-expanded', true);
        }
    },
    // 同步操作
    doSync: function(component, event, helper){
        helper.syncEvent(component);
    },
    // inside 用户编辑操作
    doEdit: function(component, event, helper){
        var recordId = component.get('v.recordId');
        let url = window.location.origin + '/s/coursedetail?recordinfo=' + recordId;
        window.open(url, '_self');
    },
    Syncbefore : function(component, event, helper) {
        console.log("点击了同步前的按钮",component.get('v.beforeSyncId'));
        let url = window.location.origin + '/s/training-detail-page?0.recordId=' + component.get('v.beforeSyncId');
        window.open(url, '_self'); 
    },
    Syncafter : function(component, event, helper) {
        console.log("点击了同步前的按钮",component.get('v.beforeSyncId'));
        let url = window.location.origin + '/s/training-detail-page?0.recordId=' + component.get('v.afterSyncId');
        window.open(url, '_self');
    },
    // 打开附件弹框
    uploadFileItem: function(component, event, helper){
        console.log('打开附件弹框-----------');
        component.set('v.uploadModalFlag', true);
    },
    // 关闭附件弹框
    cancelEvent: function(component, event, helper){
        component.set('v.uploadModalFlag', false);
        component.set('v.uploadFinished', false);
        component.set('v.cancelFlag', false);
        component.set('v.cancellationReason', '');
        component.set('v.attachmentName', '');
        component.set('v.attachmentType', '');
    },
    // 上传附件
    handleFilesChange : function(component, event, helper) {
        component.set('v.isBusy', true);
        var files = event.getSource().get("v.files");
        let uploadItem = component.get('v.attachmentItem');
        let attachmentName = component.get('v.attachmentName');
        console.log(files, 'file=======');
        // 显示附件信息
        if (files.length > 0) {
            component.set('v.uploadFinished', true);
        }
        // 附件赋值
        if (!attachmentName) {
            component.set('v.attachmentName', files[0].name);
        }
        uploadItem.name = files[0].name;
        component.set('v.attachmentItem', uploadItem);
        // 转base64
        helper.fileByBase64(files, component, attachmentName || files[0].name );
    },
    // 删除附件
    deleteAttachmentItem : function(component, event, helper) {
        component.set('v.uploadFinished', false);
        component.set('v.attachmentItem', {});
    },
    // 保存当前附件
    saveFileItem : function(component, event, helper) {
        let attachment = component.get('v.attachment');
        let attachmentItem = component.get('v.attachmentItem');
        let attachmentName = component.get('v.attachmentName');
        let attachmentType = component.get('v.attachmentType');
        console.log(JSON.parse(JSON.stringify(attachmentItem)), 'attachmentItem-------------');
        // 获取当前时间
        const year = new Date().getFullYear().toString();
        const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
        const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
        attachment.push({
            attachmentName: attachmentName,
            attachmentType: attachmentType,
            attachmentDate: `${year}-${month}-${day}`,
            contentId: attachmentItem.contentId,
        });
        // 关闭弹框
        component.set('v.attachment', attachment);
        component.set('v.uploadModalFlag', false);
        component.set('v.uploadFinished', false);
        component.set('v.attachmentName', '');
        component.set('v.attachmentType', '');
    },
    // 保存附件
    saveFileLIst : function(component, event, helper) {
        helper.uplaodFileEvent(component);
    },
    // 刷新红绿灯
    refreshTrafficLight : function(component, event, helper) {
        console.log('刷新红绿灯---------------');
        helper.refreshLight(component);
    },
    // 取消同步
    doCancelSync : function(component, event, helper) {
        helper.cancelSyncEvent(component);
    },
    // 点击Approve:开启弹窗
    ApproveBtn : function(component, event, helper) {
        component.set('v.ratifyPup',true)
        component.set('v.ratifyPuplabel',"Approve")
    },
    // 点击Reject:开启弹窗
    RejectBtn : function(component, event, helper) {
        component.set('v.ratifyPup',true)
        component.set('v.ratifyPuplabel',"Reject")
    },
    // 点击弹窗上的Approve
    onclickApprove : function(component, event, helper) {
        helper.approvalOrRejectRegister(component,"Approve")
        component.set('v.ratifyPuplabel',"Approve")
    },
    // 点击弹窗上的Reject
    onclickReject : function(component, event, helper) {
        helper.approvalOrRejectRegister(component,"Reject")
        component.set('v.ratifyPuplabel',"Reject")
    },
    // 修改cancel原因
    changeCancellationReason : function(component, event, helper) {
        helper.getElementRequiredError(component, 'cancellationReason');
    },
})