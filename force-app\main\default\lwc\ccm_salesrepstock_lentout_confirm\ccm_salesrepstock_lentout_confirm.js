import { LightningElement, track, api } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

import getAgreementInfoOnConfirmPage from "@salesforce/apex/CCM_SalesRepStock_LentOut.getAgreementInfoOnConfirmPage";
import confirmLentoutRecord from "@salesforce/apex/CCM_SalesRepStock_LentOut.confirmLentoutRecord";

import LentoutAgreement from "@salesforce/label/c.CCM_LentoutAgreement";
import LentoutNumber from "@salesforce/label/c.CCM_LentoutNumber";
import LentOutAgreementName from "@salesforce/label/c.CCM_LentOutAgreementName";
import Preview from "@salesforce/label/c.CCM_Preview";
import Download from "@salesforce/label/c.CCM_Download";
import BackToOverView from "@salesforce/label/c.CCM_BackToOverView";
import Previous from "@salesforce/label/c.CCM_Previous";
import Confirm from "@salesforce/label/c.CCM_Confirm";

export default class Ccm_salesrepstock_lentout_confirm extends NavigationMixin(LightningElement) {

    label = {
        LentoutAgreement,
        LentoutNumber,
        LentOutAgreementName,
        Preview,
        Download,
        BackToOverView,
        Previous,
        Confirm
    }

    // 这个 id 应该是 lentout return 的 record id
    @api
    recordId;

    // 页面遮罩
    @track
    showSpinner = false;

    @track
    viewPDFUrl = "";
    // 是否预览 pdf
    blPdfPreview = false;
    // pdf file info
    @track
    agreementInfo = {
        no: "",
        name: "",
        url: "",
        contentdocumentid: ""
    };


    // attachment pdf
    getAgreementPdfInfo() {
        this.showSpinner = true;
        getAgreementInfoOnConfirmPage({ recordId: this.recordId }).then(res => {
            console.log("getAgreementInfoOnConfirmPageres:", JSON.stringify(res));
            if (res.state === "SUCCESS") {
                this.showSpinner = false;
                this.agreementInfo.no = res.data.no;
                this.agreementInfo.name = res.data.name;
                this.agreementInfo.url = res.data.url;
                this.agreementInfo.contentdocumentid = res.data.contentdocumentid;
            }
        }).catch(error => {
            console.log("getAgreementInfoOnConfirmPageerror:", JSON.stringify(error));
        });
    }

    handleDownload() {
        let url = this.agreementInfo.url;
        window.open(url, "_blank");
    }
    handlePreview() {
        this[NavigationMixin.Navigate]({
            type: 'standard__namedPage',
            attributes: {
                pageName: 'filePreview'
            },
            state: {
                selectedRecordId: this.agreementInfo.contentdocumentid
            }
        });
    }
    clickBackToSelectProduct() {
        this.dispatchEvent(
            CustomEvent("previous", {
                detail: {
                    recordId: this.recordId
                }
            })
        )
    }
    handleAgreementConfirm() {
        this.showSpinner = true;
        // confirmed lentout
        confirmLentoutRecord({ recordId: this.recordId }).then(res => {
            if (res.state === "SUCCESS") {
                this.showSpinner = false;
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: "SUCCESS",
                        message: "Lentout confirmed!",
                        variant: 'success'
                    })
                );
                setTimeout(() => {
                    this.dispatchEvent(
                        CustomEvent("next", {
                            detail: {
                                recordId: this.recordId,
                                DownloadPDFurl: this.agreementInfo.url,
                                ViewPDFurl: this.viewPDFUrl,
                            }
                        })
                    )
                }, 100);
            } else {
                this.showSpinner = false;
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: "ERROR",
                        message: "Lentout confirm failed!",
                        variant: 'error'
                    })
                );
            }
        }).catch(error => {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: "ERROR",
                    message: "Lentout confirm failed!",
                    variant: 'error'
                })
            );
        })
    }

    // 回到 overview 页面
    handleLentoutCancel() {
        console.log("Cancel then back to overview page!");
        // 跳转到 overview 页面去
        let url = window.location.origin + "/lightning/n/Sales_Rep_Stock_Overview";
        window.open(url, '_self');
    }

    connectedCallback() {
        // 测试场景 
        // this.recordId = "a1P7Y000002awUH";

        this.getAgreementPdfInfo();
    }
}