<!--
 - Created by gluo006 on 8/28/2019.
 -->
 <aura:component implements="lightning:actionOverride,flexipage:availableForRecordHome,force:hasRecordId"
 access="global" controller="CCM_ProductRegistration"
 description="CCM_Community_LookUp">

<aura:attribute name="inputVal" type="String" default=""/>
<aura:attribute name="inputValList" type="List" default="['']"/>
<aura:attribute name="modelNumberIndex" type="Integer" default="0"/>
<aura:attribute name="dropDownOpen" type="Boolean" default="false"/>
<!--product registration-->
<aura:attribute name="brandName" type="String" default=""/>
<aura:attribute name="index" type="String" default=""/>
<aura:attribute name="fieldName" type="String" default=""/>
<aura:attribute name="fieldNameLabel" type="String" default="" />
<aura:attribute name="productId" type="String" default=""/>
<aura:attribute name="majorIssue" type="String" default=""/>
<aura:attribute name="pageName" type="String" default=""/>
<aura:attribute name="serialNum" type="String" default=""/>
<aura:attribute name="customerId" type="String" default=""/>
<aura:attribute name="scrollBarDown" type="Boolean" default="false"/>
<aura:attribute name="productList" type="List" default="[]"/>
<aura:attribute name="isProductCodeShow" type="Boolean" default="false"/>
<aura:attribute name="pricingDate" type="Date" default=""/>


<aura:attribute name="selectedValue" type="Map" access="global"/>
<aura:attribute name="isSelected" type="Boolean" default="true"/>
<aura:attribute name="isSearching" type="Boolean" default="false"/>
<aura:attribute name="class" type="String" default=""/>
<aura:attribute name="isDisabled" type="Boolean" default="false"/>

<!-- resales fill -->
<aura:attribute name="entityId" type="String" default="" />
<aura:attribute name="isProspect" type="Boolean" default="" />
<aura:attribute name="isResalesFill" type="Boolean" default="false" />
<aura:attribute name="isInvoice" type="Boolean" default="false" />

<!-- claim -->
<aura:attribute name="isClaim" type="Boolean" default="false" />
<aura:attribute name="accId" type="String" default="" />
<aura:attribute name="claimAddressType" type="String" default="" />
<aura:attribute name="isClaimInfo" type="Boolean" default="false" />
<aura:attribute name="contactId" type="String" default="" />
<aura:attribute name="addressId" type="String" default="" />
<aura:attribute name="isPortal" type="Boolean" default="false" />

<!-- 防抖 -->
<aura:attribute name="searchCustomerFlag" type="Boolean" default="true"/>

<!-- <aura:attribute name="isModelNumber" type="Boolean" default="{!v.fieldName == 'Master Model number:'}"/> -->
<!--<aura:handler name="change" value="{!v.warranty}" action="{!c.onParamsChanged}"/>-->
<!--<aura:handler name="change" value="{!v.brandName}" action="{!c.onParamsChanged}"/>-->
<!--<aura:handler name="change" value="{!v.caseType}" action="{!c.onParamsChanged}"/>-->

<aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

<!--new order product change get the price-->
<aura:method name="clearDropdownOptions" action="{!c.clearDropdownOptions}" access="public" />
<aura:method name="setInputVal" action="{!c.setInputVal}" access="public" />
<aura:handler event="c:CCM_ProductLookUpChange" action="{!c.handleProductClear}"/>
<!-- <aura:handler event="c:CCM_ProductLookUpChange" name="delModelNumber" action="{!c.delMasterProductByProductCode}"/> -->
<aura:registerEvent name="getProductChange" type="c:CCM_ProductLookUpChange"/>
<aura:registerEvent name="delModelNumber" type="c:CCM_ProductLookUpChange"/>
<aura:handler name="change" value="{!v.selectedValue}" action="{!c.onSelectedValueChange}"/>

<!-- 父子传值 -->
<aura:attribute name="orderMethod" type="Aura.Action"/>
<aura:attribute name="invoiceMethod" type="Aura.Action"/>
<aura:registerEvent name="onSelect" type="c:CCM_CommonEvent"/>
<aura:attribute name="indexnum" type="Integer" default="0"/>

<aura:attribute name="requestValue" type="String" default=""/>
<aura:attribute name="requestType" type="String" default=""/>

<aura:attribute name="userId" type="String" default=""/>
<aura:attribute name="fromProductRegistration" type="Boolean" default="false"/>


<div aura:id="lookup-product" class="{!'slds-form-element '+ v.class}">
<label class="slds-form-element__label" for="combobox-id-20">
{!v.fieldNameLabel}
</label>
<div class="slds-form-element__control">
<div class="slds-combobox_container">
 <div class="{!'slds-combobox slds-dropdown-trigger slds-dropdown-trigger_click '+ (v.dropDownOpen
         ? 'slds-is-open' :'')}" aria-expanded="true" aria-haspopup="listbox" role="combobox">
     <div class="slds-combobox__form-element slds-input-has-icon slds-input-has-icon_right" role="none">

         <div class="slds-form-element">
             <div class="slds-form-element__control slds-input-has-icon slds-input-has-icon_right">

                 <aura:iteration items="{!v.inputValList}" var="item" indexVar="index">
                     
                     <div class="slds-combobox__form-element slds-input-has-icon slds-input-has-icon_right"
                         role="none" style="margin-top: 5px;">
                         <aura:if isTrue="{!v.isDisabled}">
                             <input type="text" class="slds-input slds-combobox__input" id="productLookUpInput combobox-id-19"
                                 aria-autocomplete="list" aria-controls="listbox-id-9" autoComplete="off"
                                 role="textbox" value="{!item}" data-id="{!index}"
                                 onkeyup="{!c.onInputChange}" placeholder="" onfocus="{!c.onInputChange}" onblur="{!c.hideProductList}" disabled="disabled" html-autocomplete="off"/>
                             <aura:set attribute="else">
                             <!-- <aura:iteration items="1,2,3,4,5" var="item"> -->
                                 <input type="text" class="slds-input slds-combobox__input" id="productLookUpInput combobox-id-19"
                                     aria-autocomplete="list" aria-controls="listbox-id-9" autoComplete="off"
                                     role="textbox" value="{!item}" data-id="{!index}"
                                     onkeyup="{!c.onInputChange}" placeholder="" onfocus="{!c.onInputChange}" onblur="{!c.hideProductList}" html-autocomplete="off"
                                     
                                     />
                             </aura:set>
                         </aura:if>
                         <span class="slds-icon_container slds-icon-utility-search slds-input__icon slds-input__icon_right">
                             <aura:if isTrue="{!v.isSearching}">
                                 <div role="status"
                                     class="slds-spinner slds-spinner_brand slds-spinner_x-small slds-input__spinner">
                                     <span class="slds-assistive-text">Loading</span>
                                     <div class="slds-spinner__dot-a"></div>
                                     <div class="slds-spinner__dot-b"></div>
                                 </div>
                                 <aura:set attribute="else">
                                     <lightning:icon size="x-small" iconName="utility:search"
                                                     alternativeText="search"/>
                                 </aura:set>

                             </aura:if>
                         </span>
                     </div>

                 </aura:iteration>

             </div>
         </div>
     </div>
     <div class="slds-dropdown slds-dropdown_length-5 slds-dropdown_fluid"
          role="listbox" onmousedown="{!c.avoidBlur}">
         <ul class="{!v.isProductCodeShow ? 'doubleLineList' : 'slds-listbox slds-listbox_vertical'}" role="presentation" >
             <aura:if isTrue="{!v.productList.length > 0}">
                 <aura:iteration items="{!v.productList}" var="item" indexVar="index">
                     <li role="presentation" class="slds-listbox__item" onmousedown="{!c.onSelectItem}"
                         data-index="{!index}">
                         <aura:if isTrue="{!v.isProductCodeShow}">
                             <p onmousedown="{!c.onSelectItem}" data-index="{!index}"><span onmousedown="{!c.onSelectItem}" data-index="{!index}">
                                 <aura:if isTrue="{! !empty(item.modelNumber)}"> {!item.modelNumber}
                                     <aura:set attribute="else">
                                         {!item.ProductCode}
                                     </aura:set>
                                 </aura:if>
                             </span><br />{!item.ProductDescription}</p>
                             <aura:set attribute="else">
                                 <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small"
                                      role="option">
                                     <span class="slds-media__figure slds-listbox__option-icon">
                                         <lightning:icon iconName="standard:product" size="small"/>
                                     </span>
                                     <span class="slds-media__body">
                                     <span class="slds-truncate"
                                           title="{!item.Name}">
                                         <aura:if isTrue="{!v.fieldName == ''}">
                                             {!item.Name}
                                             <aura:set attribute="else">
                                                 {!item.Name}
                                             </aura:set>
                                         </aura:if>
                                     </span>
                                 </span>
                                 </div>
                             </aura:set>
                         </aura:if>
                     </li>
                 </aura:iteration>
                 <aura:set attribute="else">
                     <li role="presentation" class="slds-listbox__item"
                         data-index="{!index}">
                         <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small"
                              role="option">
                             <span class="slds-media__figure slds-listbox__option-icon"></span>
                             <span class="slds-media__body">
                                 <span class="slds-truncate" title="No record found">
                                     {!$Label.c.CCM_LookupIsEmptyTips}
                                 </span>
                             </span>
                         </div>
                     </li>
                 </aura:set>
             </aura:if>
         </ul>
     </div>
 </div>
</div>
</div>
<button id="orderBtn" style="display: none" label="call parent method" onclick="{!v.orderMethod}"></button>
<button id="invoiceBtn" style="display: none" label="call parent method" onclick="{!v.invoiceMethod}"></button>
</div>
</aura:component>
