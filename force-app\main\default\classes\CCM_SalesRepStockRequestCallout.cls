/**
 * 同步Stock Request/Invoice Damage/Return Warehouse/Return Disposal/Resales Request到EBS
 * Vince 0806 add 
 */
public without sharing class CCM_SalesRepStockRequestCallout {
    public static final String OBJ_API_STOCK = 'Sub_inventory_Request__c';
    public static final String SCENE_TYPE_STOCK = 'Stock Request';
    public static final String OBJ_API_INVOICE_DAMAGE = 'Invoice_Damage__c';
    public static final String SCENE_TYPE_INVOICE_DAMAGE = 'Invoice Damage';
    public static final String OBJ_API_RETURN = 'Sales_Rep_Stock_Return__c';
    public static final String SCENE_TYPE_RETURN_WAREHOUSE = 'Return Warehouse';
    public static final String SCENE_TYPE_RETURN_DISPOSAL = 'Return Disposal';
    public static final String SCENE_TYPE_RETURN = 'Return';
    public static final String OBJ_API_RESALES = 'Sales_Rep_Stock_Re_Sale__c';
    public static final String SCENE_TYPE_RESALES = 'Resales Request';
    public static final String ENDPOINT_NAME_SEEBURGER_UAT = 'chervon_seeburger_uat';
    public static final String ENDPOINT_NAME_SEEBURGER_PROD = 'chervon_seeburger_prod';
    public static final String ORG_EEG = 'EEG';
    public static final String ALL_INVENTORY_CODE_EGD01 = 'EGD01';
    public static final String CSTOCK_CODE_EGD01 = 'EGS02';
    public static final String FLAG_YES = 'YES';
    public static final String FLAG_NO = 'NO';
    public static final String DISPOSAL_DEFAULT_PAYMENT_TERM = 'EEG01';


    public static final String ORDER_TYPE_INV = 'EEG Sample Disposal';
    public static final String ORDER_TYPE_RESALE_ORDER = 'EEG Sales Domestic';
    public static final String ORDER_TYPE_DISPOSAL = 'EEG Sample Disposal';
    public static final String PROMOTION_TYPE_NEW_PRICE = 'New Price';

    //Disposal 场景 固定传Customer：E01111， billto:182717、shipto：180852
    public static final String PUBLIC_SHIP_CODE = '180852';
    public static final String PUBLIC_BILL_CODE = '182717';
    public static final String INVOICE_DAMAGE_MODEL = 'WEARCHARGE';
    

    

    
    public CCM_SalesRepStockRequestCallout() {

    }

    public static String pushRequestInfo(String recordId){
        
        String logPreName = '';
        String paramStr = '';
        String logId = '';
        try{
            //根据记录Id获取Object Apiname
            String objApiName = Util.findObjectNameFromRecordIdPrefix(recordId);
            Map<String, Object> mapRequest = new Map<String, Object>();
            //根据所属Apiname判断场景：Stock Request/Invoice Damage/Return/Resales Request
            if(objApiName.equals(OBJ_API_STOCK)){
                mapRequest = getStockRequestInfo(recordId);
                logPreName = SCENE_TYPE_STOCK;
            }else if(objApiName.equals(OBJ_API_INVOICE_DAMAGE)){
                mapRequest = getInvoiceDamageRequestInfo(recordId);
                logPreName = SCENE_TYPE_INVOICE_DAMAGE;
            }else if(objApiName.equals(OBJ_API_RETURN)){
                mapRequest = getReturnRequestInfo(recordId);
                logPreName = SCENE_TYPE_RETURN;
            }else if(objApiName.equals(OBJ_API_RESALES)){
                mapRequest = getResalesRequestInfo(recordId);
                logPreName = SCENE_TYPE_RESALES;
            }

            paramStr = Json.serialize(mapRequest);
            SyncRes res = requestCallOut(paramStr);
            if(String.isNotBlank(res.PROCESS_MSG)){
                res.PROCESS_MSG = res.PROCESS_MSG.replace('"','');
            }
            if(res.Process_Status == 'Failed'){
                
                logId = Util.logIntegration(logPreName + ' Sync Exception','CCM_SalesRepStockRequestCallout','POST','',paramStr, JSON.serialize(res));
            }else{
                logId = Util.logIntegration(logPreName + ' Sync Log','CCM_SalesRepStockRequestCallout','POST','',paramStr, JSON.serialize(res));
            }
            
            return res.Process_Status;
        }catch(Exception e){
            SyncRes res = new SyncRes();
            res.Process_Status = 'Failed';
            system.debug('报错行数---->'+e.getLineNumber()+'报错信息---->'+e.getMessage());
            logId = Util.logIntegration(logPreName + ' Sync Exception','CCM_SalesRepStockRequestCallout','POST','',paramStr, e.getMessage());
            return res.Process_Status;
        }
    }

    public static SyncRes requestCallOut(String param){
        SyncRes objRes = new SyncRes();
        String endPointName = '';
        if(CCM_Service.IsSandboxOrg()){
            endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
        }else {
            endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
        }
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'order';
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        system.debug('请求参数为-->'+param);
        if (!Test.isRunningTest()) {
            HttpResponse res = new HttpResponse();
            res = CCM_ServiceCallout.getDataViaHttp(param, endPoint, 'POST', HeaderKey);
            objRes = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
        }
        return objRes;
    }

    //获取库存申请参数
    public static Map<String, Object> getStockRequestInfo(String recordId){
        String soqlStr = 'SELECT id,Name,Request_For__c,Request_For__r.FederationIdentifier,Request_For__r.Sub_Inventory_Code__c, '+
        '(SELECT Id,Name, Model__c,Qty__c FROM sub_Invotory_Request_Product__r) FROM Sub_inventory_Request__c '+
        ' WHERE id = :recordId';
        System.debug('soqlStr=======>'+soqlStr);
        String strDataTime = String.valueOf(system.now().getTime());
        SObject obj = Database.query(soqlStr);
        Map<String, Object> paramMap = new Map<String, Object>();
        Sub_inventory_Request__c subinv = (Sub_inventory_Request__c) obj;
        // paramMap.put('ORDER_NUMBER_CRM', subinv.Id);
        paramMap.put('ORDER_NUMBER_CRM', subinv.Name);
        //paramMap.put('ORDER_NUMBER_CRM', '121313');
        system.debug('ORDER_NUMBER_CRM-->'+subinv.Id);
        paramMap.put('HEADERID', getInterfaceId(subinv.Name));
        //paramMap.put('HEADERID', '121313');
        paramMap.put('PROMOTION_CODE',  '');
        paramMap.put('RETURN_TO_SUBINVENTORY',  '');
        paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
        
        paramMap.put('REQUEST_TO_SUBINVENTORY',subinv.Request_For__r.Sub_Inventory_Code__c);
        paramMap.put('REQUEST_FROM_SUBINVENTORY',  ALL_INVENTORY_CODE_EGD01);

        paramMap.put('CURRENCY_CODE','');
        paramMap.put('ORDER_FLAG',  FLAG_NO);
        paramMap.put('SCENE_TYPE',  SCENE_TYPE_STOCK);
        paramMap.put('VAT',  '');
        system.debug('HEADERID-->'+paramMap.get('HEADERID'));
        paramMap.put('CUSTOMER',Label.Public_Customer_For_Sync_Order);
        //TO DO ，缺字段
        paramMap.put('ORG_CODE', ORG_EEG);

        system.debug('OrgCode-->'+paramMap.get('ORG_CODE'));
        paramMap.put('PO_NUMBER', '');
        // paramMap.put('PO_NUMBER', '121313');
        paramMap.put('SHIPTO', '');
        paramMap.put('BILLTO', '');
        paramMap.put('ORDER_TYPE', '');
        paramMap.put('PRICE_LIST', '');
        system.debug('ORDER_TYPE-->'+paramMap.get('ORDER_TYPE'));
        system.debug('PRICE_LIST-->'+paramMap.get('PRICE_LIST'));
        paramMap.put('WARE_HOUSE_ORACLE', ORG_EEG);
        paramMap.put('SALES_REP_Code', subinv.Request_For__r.FederationIdentifier);
        system.debug('SALES_REP_Code-->'+paramMap.get('SALES_REP_Code'));
        paramMap.put('DATE_ORDER', '');
        paramMap.put('PAYMENT_TERM', '');
        paramMap.put('FREIGHT_TERM', '');
        paramMap.put('INCO_TERM', '');
        paramMap.put('SHPPING_PLACE', '');
        paramMap.put('PRICE_DATE', '');
        paramMap.put('FERIGHT_FEE', '');
        paramMap.put('INSURANCE_FEE', '');
        paramMap.put('OTHER_FEE', '');
        paramMap.put('CHARGE_FORWEAR_FEE', '');
        paramMap.put('ORDER_STATUS','');
        paramMap.put('DROPSHIP_NAME','');
        paramMap.put('DROPSHIP_ADDRESS1','');
        paramMap.put('DROPSHIP_ADDRESS2', '');
        paramMap.put('DROPSHIP_PHONE', '');
        paramMap.put('DROPSHIP_COUNTRY', '');
        paramMap.put('DROPSHIP_ZIP', '');
        paramMap.put('DROPSHIP_STATE', '');
        paramMap.put('DROPSHIP_CITY', '');
        paramMap.put('INSTRUCTION_TO_DSV','');
        paramMap.put('CARRIER_INFORMATION', '');
        paramMap.put('TOTAL_VALUE', '');
        paramMap.put('HEADER_DISCOUNT', '');
        paramMap.put('HEADER_DISCOUNT_AMOUNT', '');
        paramMap.put('TOTAL_VALUE_NET', '');
        paramMap.put('TOTAL_AMOUNT', '');
        paramMap.put('IS_DROPSHIP', 'N');
        //TO DO：0815新增字段（AUTO_BOOK），暂不处理  
        // paramMap.put('AUTO_BOOK', 'NO');
        
        paramMap.put('LABOR_FEE', '');
        // paramMap.put('PURCHASE_DATE', '');
        
        paramMap.put('ATTRIBUTE1', '');
        paramMap.put('ATTRIBUTE2', '');
        paramMap.put('ATTRIBUTE3', '');
        paramMap.put('ATTRIBUTE4', '');
        paramMap.put('ATTRIBUTE5', '');
        paramMap.put('ATTRIBUTE6', '');
        paramMap.put('ATTRIBUTE7', '');
        paramMap.put('ATTRIBUTE8', '');
        paramMap.put('ATTRIBUTE9', '');
        paramMap.put('ATTRIBUTE10', '');
        paramMap.put('ATTRIBUTE11', '');
        paramMap.put('ATTRIBUTE12', '');
        paramMap.put('ATTRIBUTE13', '');
        paramMap.put('ATTRIBUTE14', '');
        paramMap.put('ATTRIBUTE15', '');
        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        List<sub_inventory_Request_Product__c> itemlist = subinv.sub_Invotory_Request_Product__r;
        if (itemlist != null && itemlist.size() > 0) {
            for (sub_inventory_Request_Product__c item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                // itemMap.put('ORDER_NUMBER_CRM', subinv.Id);
                itemMap.put('ORDER_NUMBER_CRM', subinv.Name);
                //itemMap.put('ORDER_NUMBER_CRM', '121313');
                system.debug('ORDER_NUMBER_CRM-->'+itemMap.get('ORDER_NUMBER_CRM'));
                itemMap.put('ORDERLINE_CRM_ID', item.Id);
                // itemMap.put('ORDERLINE_CRM_ID', '121313');
                itemMap.put('LINEID', getInterfaceId(item.Name));
                // itemMap.put('LINEID', '121313');
                system.debug('ORDERLINE_CRM_ID-->'+itemMap.get('ORDERLINE_CRM_ID'));
                itemMap.put('HEADERID', paramMap.get('HEADERID'));
                // itemMap.put('HEADERID', '121313');
                system.debug('HEADERID-->'+itemMap.get('HEADERID'));
                itemMap.put('CRM_ORDERLINE_NUMBER', item.Id);
                // itemMap.put('CRM_ORDERLINE_NUMBER', '121313');
                system.debug('CRM_ORDERLINE_NUMBER-->'+itemMap.get('CRM_ORDERLINE_NUMBER'));
                itemMap.put('PRICE_DATE', '');
                itemMap.put('PRODUCT_MODEL', item.Model__c);
                itemMap.put('ORDER_QUANTITY',item.Qty__c);
                itemMap.put('LIST_PRICE_NAME', '');
                itemMap.put('SERIAL_NUMBER', '');
                itemMap.put('PRODUCT_RATING_STATUS', '');
                itemMap.put('PROMOTION_CODE', '');
                itemMap.put('LIST_PRICE', '');
                
                itemMap.put('REQUEST_DATE',String.valueOf(Date.today()));
                //request - > Request_For__c
                itemMap.put('SUBINVENTORY_CODE',subinv.Request_For__r.Sub_Inventory_Code__c);
                itemMap.put('SALES_PRICE', '');
                //Promotion行折扣
                itemMap.put('PROMOTION_DISCOUNT1', '');
                //promotion 头折扣
                itemMap.put('PROMOTION_DISCOUNT2','');
                //Modifire的折扣
                itemMap.put('STANDARD_DISCOUNT', '');
                //经过promotion行折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE1', '');
                //经过Promotion头折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE2', '');
                //最终用户取值价格
                itemMap.put('UNIT_SELLING_PRICE', '');
                itemMap.put('EST_REPLENISH_DATE','');
                itemMap.put('UOM', 'EA');
                itemMap.put('TOTAL_NET_PRICE', '');
                itemMap.put('REMARK','');
                itemMap.put('SCHEDULE_SHIP_DATE','');
                //0821新增
                itemMap.put('STANDARD_DISCOUNT_TYPE','');
                itemMap.put('PROMOTION_DISCOUNT1_TYPE','');
                itemMap.put('TRAFFIC_LIGHT', '');
                itemMap.put('ATTRIBUTE1', '');
                itemMap.put('ATTRIBUTE2', '');
                itemMap.put('ATTRIBUTE3', '');
                itemMap.put('ATTRIBUTE4', '');
                itemMap.put('ATTRIBUTE5', '');
                itemMap.put('ATTRIBUTE6', '');
                itemMap.put('ATTRIBUTE7', '');
                itemMap.put('ATTRIBUTE8', '');
                itemMap.put('ATTRIBUTE9', '');
                itemMap.put('ATTRIBUTE10', '');
                itemMap.put('ATTRIBUTE11', '');
                itemMap.put('ATTRIBUTE12', '');
                itemMap.put('ATTRIBUTE13', '');
                itemMap.put('ATTRIBUTE14', '');
                itemMap.put('ATTRIBUTE15', '');
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        return paramMap;
    }
    //获取Invoice Damage申请参数
    public static Map<String, Object> getInvoiceDamageRequestInfo(String recordId){
        String soqlStr = 'SELECT Name,Authorized_Brand__c, Authorized_Brand__r.Incoterm__c, Authorized_Brand__r.Payment_Term__c, Billing_Address__c,Billing_Address__r.Country__c, '+
        'Billing_Address__r.Customer_Line_Oracle_ID__c, CurrencyIsoCode, Customer__c, Customer__r.Name, Prospect__c,Customer__r.AccountNumber,Shipping_Address__c, Shipping_Address__r.Customer_Line_Oracle_ID__c, '+
        'Total_Value__c,Total_Due_Amount__c, VAT__c , Customer__r.List_Price__r.Name, Customer__r.List_Price__c,CreatedById, CreatedBy.Sub_Inventory_Code__c,CreatedBy.FederationIdentifier, '+
        'Shipping_Address__r.Name,Shipping_Address__r.Street_1__c,Shipping_Address__r.Street_2__c,Shipping_Address__r.DropShip_Phone__c,'+
        'Shipping_Address__r.Country__c,Shipping_Address__r.Postal_Code__c,Shipping_Address__r.Province__c,Shipping_Address__r.City__c,'+
        '(SELECT id,name,Product__c,Product__r.Order_Model__c,Sales_Rep_Stock__c, Sales_Rep_Stock__r.SerialNumber__c,Unit_Price__c,Total_Net_Price__c FROM Invoice_Damage_Items__r) '+
        'FROM Invoice_Damage__c '+
        'WHERE id = :recordId';
        SObject obj = Database.query(soqlStr);
        String strDataTime = String.valueOf(system.now().getTime());
        Map<String, Object> paramMap = new Map<String, Object>();
        
        Invoice_Damage__c headerData = (Invoice_Damage__c) obj;
        paramMap.put('ORDER_NUMBER_CRM', headerData.Id);
        //paramMap.put('ORDER_NUMBER_CRM', '121313');
        system.debug('ORDER_NUMBER_CRM-->'+headerData.Id);
        paramMap.put('HEADERID',  getInterfaceId(headerData.Name));
        System.debug('HEADERID======>'+getInterfaceId(headerData.Name));
        //paramMap.put('HEADERID', '121313');
        paramMap.put('PROMOTION_CODE',  '');
        paramMap.put('RETURN_TO_SUBINVENTORY',  '');
        paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
        paramMap.put('REQUEST_TO_SUBINVENTORY',  '');
        paramMap.put('REQUEST_FROM_SUBINVENTORY',  '');

        paramMap.put('CURRENCY_CODE',headerData.CurrencyIsoCode);
        paramMap.put('ORDER_FLAG',  FLAG_YES);
        paramMap.put('SCENE_TYPE',  SCENE_TYPE_INVOICE_DAMAGE);
        paramMap.put('VAT',  headerData.VAT__c);
        system.debug('HEADERID-->'+paramMap.get('HEADERID'));
        //根据Customer/Prospect判断获取价格册、Customer Code
        String customerCode = '';
        String listPriceName = '';
        String accId = '';
        Account acc = new Account();
        if(String.isNotBlank(headerData.Prospect__c)){
            acc = getCustomerOfProspect(headerData.Prospect__c);
            customerCode = acc.AccountNumber;
            listPriceName = acc.List_Price__r.Name;
            if(acc != null){
                accId = acc.Id;
            }
            //若为Prospect场景，ship to 给默认值，实际地址通过dropship地址段传输
            paramMap.put('IS_DROPSHIP', 'Y');
            paramMap.put('SHIPTO', PUBLIC_SHIP_CODE);
            paramMap.put('DROPSHIP_NAME',headerData.Shipping_Address__r.Name);
            paramMap.put('DROPSHIP_ADDRESS1',headerData.Shipping_Address__r.Street_1__c);
            paramMap.put('DROPSHIP_ADDRESS2', headerData.Shipping_Address__r.Street_2__c);
            paramMap.put('DROPSHIP_PHONE', headerData.Shipping_Address__r.DropShip_Phone__c);
            paramMap.put('DROPSHIP_COUNTRY', headerData.Shipping_Address__r.Country__c);
            paramMap.put('DROPSHIP_ZIP', headerData.Shipping_Address__r.Postal_Code__c);
            paramMap.put('DROPSHIP_STATE', headerData.Shipping_Address__r.Province__c);
            paramMap.put('DROPSHIP_CITY', headerData.Shipping_Address__r.City__c);
            paramMap.put('BILLTO', PUBLIC_BILL_CODE);
        }else if(String.isNotBlank(headerData.Customer__c)){
            customerCode = headerData.Customer__r.AccountNumber;
            listPriceName = headerData.Customer__r.List_Price__r.Name;
            accId = headerData.Customer__c;
            paramMap.put('IS_DROPSHIP', 'N');
            paramMap.put('SHIPTO', headerData.Shipping_Address__r.Customer_Line_Oracle_ID__c);
            paramMap.put('BILLTO', headerData.Billing_Address__r.Customer_Line_Oracle_ID__c);
        }

        if(String.isBlank(listPriceName)){
            //查询Auth上的二级价格册
            List<Sales_Program__c> lstSalesProgram = [
                SELECT Id , List_Price_1__c,List_Price_1__r.Name,List_Price_2__c,List_Price_2__r.Name,List_Price_3__c,List_Price_3__r.Name FROM  Sales_Program__c WHERE Customer__r.AccountNumber = : customerCode
            ];
            if(lstSalesProgram != null && lstSalesProgram.size() > 0 ){
                if(String.isNotBlank(lstSalesProgram[0].List_Price_1__r.Name)){
                    listPriceName = lstSalesProgram[0].List_Price_1__r.Name;
                }else if(String.isNotBlank(lstSalesProgram[0].List_Price_2__r.Name)){
                    listPriceName = lstSalesProgram[0].List_Price_2__r.Name;
                }
                else if(String.isNotBlank(lstSalesProgram[0].List_Price_3__r.Name)){
                    listPriceName = lstSalesProgram[0].List_Price_3__r.Name;
                }
               
            }
        }
        paramMap.put('CUSTOMER',customerCode);
        paramMap.put('ORG_CODE', ORG_EEG);
        system.debug('OrgCode-->'+paramMap.get('ORG_CODE'));
        paramMap.put('PO_NUMBER', '');
        
        system.debug('SHIPTO-->'+paramMap.get('SHIPTO'));
        
        system.debug('BILLTO-->'+paramMap.get('BILLTO'));
        // paramMap.put('ORDER_TYPE', ORDER_TYPE_INV);
        //change 0207
        String resOrderType = CCM_CovertOrderTypeUtil.covertOther2OracleType('Sales rep stock', headerData.Billing_Address__r.Country__c);
        paramMap.put('ORDER_TYPE', resOrderType);
        
        paramMap.put('PRICE_LIST', listPriceName);
        System.debug('PRICE_LIST===》'+listPriceName);
        paramMap.put('WARE_HOUSE_ORACLE', ORG_EEG);
        paramMap.put('SALES_REP_Code', headerData.CreatedBy.FederationIdentifier);
        //取Sync当天
        paramMap.put('DATE_ORDER', String.valueOf(Date.today()));

        paramMap.put('PAYMENT_TERM', headerData.Authorized_Brand__r.Payment_Term__c);
        paramMap.put('FREIGHT_TERM', '');
        paramMap.put('INCO_TERM', headerData.Authorized_Brand__r.Incoterm__c);
        paramMap.put('SHPPING_PLACE', '');
        paramMap.put('PRICE_DATE', String.valueOf(Date.today()));
        paramMap.put('FERIGHT_FEE', '');
        paramMap.put('INSURANCE_FEE', '');
        paramMap.put('OTHER_FEE', '');
        paramMap.put('CHARGE_FORWEAR_FEE', '');
        paramMap.put('ORDER_STATUS','');
        
        paramMap.put('INSTRUCTION_TO_DSV','');
        paramMap.put('CARRIER_INFORMATION', '');
        paramMap.put('TOTAL_VALUE', headerData.Total_Value__c);
        paramMap.put('HEADER_DISCOUNT', '');
        paramMap.put('HEADER_DISCOUNT_AMOUNT', '');
        paramMap.put('TOTAL_VALUE_NET', headerData.Total_Value__c);
        paramMap.put('TOTAL_AMOUNT', headerData.Total_Due_Amount__c);
        
        //TO DO：0815新增字段（AUTO_BOOK），暂不处理  
        // paramMap.put('AUTO_BOOK', 'NO');
        
        paramMap.put('LABOR_FEE', '');
        // paramMap.put('PURCHASE_DATE', '');

        paramMap.put('ATTRIBUTE1', '');
        paramMap.put('ATTRIBUTE2', '');
        paramMap.put('ATTRIBUTE3', '');
        paramMap.put('ATTRIBUTE4', '');
        paramMap.put('ATTRIBUTE5', '');
        paramMap.put('ATTRIBUTE6', '');
        paramMap.put('ATTRIBUTE7', '');
        paramMap.put('ATTRIBUTE8', '');
        paramMap.put('ATTRIBUTE9', '');
        paramMap.put('ATTRIBUTE10', '');
        paramMap.put('ATTRIBUTE11', '');
        paramMap.put('ATTRIBUTE12', '');
        paramMap.put('ATTRIBUTE13', '');
        paramMap.put('ATTRIBUTE14', '');
        paramMap.put('ATTRIBUTE15', '');
        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();

        //获取行上的ProductId
        List<String> lstProductId = new List<String>();
        List<Invoice_Damage_Item__c> itemlist = headerData.Invoice_Damage_Items__r;
        List<product2> lstProductInfo = [
            SELECT Id FROM  product2 WHERE Order_Model__c = : INVOICE_DAMAGE_MODEL
        ];
        if (lstProductInfo != null && lstProductInfo.size() > 0) {
            for (product2 objProduct : lstProductInfo) {
                lstProductId.add(objProduct.Id);
            }
        }
        //获取map：product id ->List price name
        Map<String,String> mapProductToPBName = new Map<String,String>();
        System.debug('lstProductId===>'+lstProductId);
        System.debug('Date.today()===>'+Date.today());
        System.debug('headerData.CurrencyIsoCode===>'+headerData.CurrencyIsoCode);
        System.debug('AccountId===>'+accId);
        //Invoice Damage传指定的Model的价格册
        //mapProductToPBName = CCM_GetProductInfoUtil.getPriceName(lstProductId,Date.today(),headerData.CurrencyIsoCode,accId);
        //根据productId 获取List price
       // Map<String,Decimal> mapProductToListPrice = new Map<String,Decimal>();
       // mapProductToListPrice = CCM_GetProductInfoUtil.getListPrice(lstProductId,Date.today(),headerData.CurrencyIsoCode,accId);
       //通过Model 以及 AB获取价格册
       List<Sales_Program__c> lstSalesProgram = [
        SELECT Id,Customer__c,Modifier_1__c,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,Order_Type__c,
        Modifier_2__c FROM Sales_Program__c WHERE Customer__c =  :accId  AND Status__c = 'Active'
       ];
       List<String> setPriceBookIds = new List<String>();
       for(Sales_Program__c objSalesProgram : lstSalesProgram){
        if(String.isNotBlank(objSalesProgram.Price_Book__c)){
            setPriceBookIds.add(objSalesProgram.Price_Book__c);
        }
        if(String.isNotBlank(objSalesProgram.List_Price_1__c)){
            setPriceBookIds.add(objSalesProgram.List_Price_1__c);
        }
        if(String.isNotBlank(objSalesProgram.List_Price_2__c)){
            setPriceBookIds.add(objSalesProgram.List_Price_2__c);
        }
        if(String.isNotBlank(objSalesProgram.List_Price_3__c)){
            setPriceBookIds.add(objSalesProgram.List_Price_3__c);
        }
       }

        //通过时间和价格唯一确认一个产品的价格
        List<Pricebook_Entry__c> lstPricebook = [
            SELECT Id,End_Date__c,Start_Date__c,UnitPrice__c,Product__c,PriceBook__r.Name FROM Pricebook_Entry__c
            WHERE End_Date__c >= :Date.today() AND Start_Date__c <= :Date.today() 
            AND Product__r.Order_Model__c = :INVOICE_DAMAGE_MODEL AND CurrencyIsoCode = :headerData.CurrencyIsoCode
            AND PriceBook__c IN : setPriceBookIds
        ];
        System.debug('mapProductToPBName=====>'+mapProductToPBName);
        if (itemlist != null && itemlist.size() > 0) {
            for (Invoice_Damage_Item__c item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                itemMap.put('ORDER_NUMBER_CRM', headerData.Id);
                //itemMap.put('ORDER_NUMBER_CRM', '121313');
                system.debug('ORDER_NUMBER_CRM-->'+itemMap.get('ORDER_NUMBER_CRM'));
                itemMap.put('ORDERLINE_CRM_ID', item.Id);
                // itemMap.put('ORDERLINE_CRM_ID', '121313');
                itemMap.put('LINEID', getInterfaceId(item.Name));
                // itemMap.put('LINEID', '121313');
                system.debug('ORDERLINE_CRM_ID-->'+itemMap.get('ORDERLINE_CRM_ID'));
                itemMap.put('HEADERID', paramMap.get('HEADERID'));
                // itemMap.put('HEADERID', '121313');
                system.debug('HEADERID-->'+itemMap.get('HEADERID'));
                itemMap.put('CRM_ORDERLINE_NUMBER', item.Id);
                // itemMap.put('CRM_ORDERLINE_NUMBER', '121313');
                system.debug('CRM_ORDERLINE_NUMBER-->'+itemMap.get('CRM_ORDERLINE_NUMBER'));
                //itemMap.put('price date', item.Submit_Date__c.format('yyyy-MM-dd'));
                //暂取Sync当天日期
                itemMap.put('PRICE_DATE', String.valueOf(Date.today()));
                itemMap.put('PRODUCT_MODEL', INVOICE_DAMAGE_MODEL);
                itemMap.put('ORDER_QUANTITY','1');
                
                itemMap.put('SERIAL_NUMBER', item.Sales_Rep_Stock__r.SerialNumber__c);
                itemMap.put('PRODUCT_RATING_STATUS', '');
                //行上的List price name 先从customer身上找一级，一级不符合则找Authorized brand 上的二级
                IF(lstPricebook != NULL && lstPricebook.size()>0 ){
                    itemMap.put('LIST_PRICE_NAME', lstPricebook[0].PriceBook__r.Name);
                    itemMap.put('LIST_PRICE', lstPricebook[0].UnitPrice__c);
                    itemMap.put('SALES_PRICE', item.Unit_Price__c);
                    itemMap.put('UNIT_NET_PRICE1', item.Unit_Price__c);

                }else{
                    itemMap.put('LIST_PRICE_NAME', '');
                    itemMap.put('LIST_PRICE','');
                    itemMap.put('SALES_PRICE', '');
                    itemMap.put('UNIT_NET_PRICE1', '');
                }
               
                
                itemMap.put('REQUEST_DATE',String.valueOf(Date.today()));
                //Modifire的折扣
                //创建人取
                itemMap.put('SUBINVENTORY_CODE', headerData.CreatedBy.Sub_Inventory_Code__c);
                itemMap.put('STANDARD_DISCOUNT', 0);
               
                //0821新增
                itemMap.put('PROMOTION_CODE', '');
                itemMap.put('PROMOTION_DISCOUNT1', '');
                //经过promotion行折扣的计算后的价格
                
                //promotion 头折扣
                itemMap.put('PROMOTION_DISCOUNT2','');
                //经过Promotion头折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE2', item.Unit_Price__c);
                itemMap.put('STANDARD_DISCOUNT_TYPE','');
                itemMap.put('PROMOTION_DISCOUNT1_TYPE','New Price');
                itemMap.put('EST_REPLENISH_DATE','');
                itemMap.put('UOM', 'EA');
                itemMap.put('TOTAL_NET_PRICE', item.Total_Net_Price__c);
                //最终用户取值价格
                itemMap.put('UNIT_SELLING_PRICE', item.Unit_Price__c);
                itemMap.put('REMARK','');
                itemMap.put('SCHEDULE_SHIP_DATE','');
                 
                itemMap.put('TRAFFIC_LIGHT', '');

                itemMap.put('ATTRIBUTE1', '');
                itemMap.put('ATTRIBUTE2', '');
                itemMap.put('ATTRIBUTE3', '');
                itemMap.put('ATTRIBUTE4', '');
                itemMap.put('ATTRIBUTE5', '');
                itemMap.put('ATTRIBUTE6', '');
                itemMap.put('ATTRIBUTE7', '');
                itemMap.put('ATTRIBUTE8', '');
                itemMap.put('ATTRIBUTE9', '');
                itemMap.put('ATTRIBUTE10', '');
                itemMap.put('ATTRIBUTE11', '');
                itemMap.put('ATTRIBUTE12', '');
                itemMap.put('ATTRIBUTE13', '');
                itemMap.put('ATTRIBUTE14', '');
                itemMap.put('ATTRIBUTE15', '');
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        return paramMap;
    }   
    //获取Return申请参数
    public static Map<String, Object> getReturnRequestInfo(String recordId){
        String soqlStr = 'Select id,Name ,CurrencyIsoCode,Return_Type__c,Returned_Warehouse__c,CreatedById,CreatedBy.Sub_Inventory_Code__c, CreatedBy.FederationIdentifier,Sub_Total__c, '+
        '(Select id,name,Product__c, Product__r.Order_Model__c,Sales_Rep_Stock__c,Sales_Rep_Stock__r.SerialNumber__c,Product_Status_Rating__c, '+
        'Customer__c, Customer__r.List_Price__c, Customer__r.List_Price__r.name,Price__c '+
        'from Sales_Rep_Stock_Return_Items__r ) '+
        'from Sales_Rep_Stock_Return__c  '+
        'WHERE id = :recordId';
        System.debug('Scene Return SOQL=====>'+soqlStr);
        SObject obj = Database.query(soqlStr);
        String strDataTime = String.valueOf(system.now().getTime());
        Map<String, Object> paramMap = new Map<String, Object>();
        Sales_Rep_Stock_Return__c headerData = (Sales_Rep_Stock_Return__c) obj;
        List<Sales_Rep_Stock_Return_Item__c> itemlist = headerData.Sales_Rep_Stock_Return_Items__r;
        //获取行上的ProductId
        List<String> lstProductId = new List<String>();
        //获取公共客户
        Account acc = new Account();
        acc = getAccByNum(Label.Public_Customer_For_Sync_Order);

        Map<String, String> addressOracleIdMap = new Map<String, String>();
        for(Account_Address__c address : [SELECT RecordType.Name, Customer_Line_Oracle_ID__c FROM Account_Address__c WHERE Customer__c = :acc.Id AND Primary__c = true]) {
            addressOracleIdMap.put(address.RecordType.Name, address.Customer_Line_Oracle_ID__c);
        }

        //封装接口参数
        paramMap.put('ORDER_NUMBER_CRM', headerData.Id);
        system.debug('ORDER_NUMBER_CRM-->'+headerData.Id);
        paramMap.put('HEADERID',  getInterfaceId(headerData.Name));
        paramMap.put('PROMOTION_CODE',  '');
        paramMap.put('REQUEST_TO_SUBINVENTORY',  '');
        //disposal场景获取list pricebook name
        if (itemlist != null && itemlist.size() > 0) {
            for (Sales_Rep_Stock_Return_Item__c disposal : itemlist) {
                lstProductId.add(disposal.Product__c);
            }
        }
        if(headerData.Return_Type__c.equals('Disposal')){
            paramMap.put('ORDER_FLAG',  FLAG_YES);
            paramMap.put('SCENE_TYPE',  SCENE_TYPE_RETURN_DISPOSAL);
            paramMap.put('RETURN_TO_SUBINVENTORY',  '');
            paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
            paramMap.put('REQUEST_FROM_SUBINVENTORY',  headerData.CreatedBy.Sub_Inventory_Code__c);
            paramMap.put('ORDER_TYPE', ORDER_TYPE_DISPOSAL);
            paramMap.put('PRICE_LIST', acc.List_Price__r.Name);
            paramMap.put('PAYMENT_TERM', DISPOSAL_DEFAULT_PAYMENT_TERM);

        }else if(headerData.Return_Type__c.equals('Return To Warehouse')){
            paramMap.put('ORDER_FLAG',  FLAG_NO);
            paramMap.put('SCENE_TYPE',  SCENE_TYPE_RETURN_WAREHOUSE);
            paramMap.put('RETURN_TO_SUBINVENTORY',  headerData.Returned_Warehouse__c);
            paramMap.put('RETURN_FROM_SUBINVENTORY',  headerData.CreatedBy.Sub_Inventory_Code__c);
            paramMap.put('REQUEST_FROM_SUBINVENTORY',  '');
            paramMap.put('ORDER_TYPE', '');
            paramMap.put('PAYMENT_TERM', '');
        }
        //取Sync当天
        paramMap.put('DATE_ORDER', String.valueOf(Date.today()));
        paramMap.put('PRICE_LIST', acc.List_Price__r.Name);
        paramMap.put('VAT', '');
        if(headerData.Return_Type__c.equals('Disposal')){
            // for DE customer, fixed tax rate: 0.19
            Decimal vat = (headerData.Sub_Total__c *  0.19).setScale(2, System.RoundingMode.HALF_UP);
            paramMap.put('VAT', vat);
        }
        paramMap.put('TOTAL_VALUE', headerData.Sub_Total__c);
        paramMap.put('TOTAL_AMOUNT', headerData.Sub_Total__c);
        paramMap.put('CURRENCY_CODE',headerData.CurrencyIsoCode);
        system.debug('HEADERID-->'+paramMap.get('HEADERID'));
        paramMap.put('CUSTOMER',Label.Public_Customer_For_Sync_Order);
        paramMap.put('ORG_CODE', ORG_EEG);
        system.debug('OrgCode-->'+paramMap.get('ORG_CODE'));
        paramMap.put('PO_NUMBER', '');
        // paramMap.put('PO_NUMBER', '121313');
        // paramMap.put('SHIPTO', PUBLIC_SHIP_CODE);
        // paramMap.put('BILLTO', PUBLIC_BILL_CODE);
        String shipTo = '';
        if(addressOracleIdMap.containsKey(CCM_Constants.SHIPPING_ADDRESS)) {
            shipTo = addressOracleIdMap.get(CCM_Constants.SHIPPING_ADDRESS);
        }
        paramMap.put('SHIPTO', shipTo);
        String billTo = '';
        if(addressOracleIdMap.containsKey(CCM_Constants.BILLING_ADDRESS)) {
            billTo = addressOracleIdMap.get(CCM_Constants.BILLING_ADDRESS);
        }
        paramMap.put('BILLTO', billTo);
        paramMap.put('WARE_HOUSE_ORACLE', ORG_EEG);
        paramMap.put('SALES_REP_Code', headerData.CreatedBy.FederationIdentifier);
        

        
        paramMap.put('FREIGHT_TERM', '');
        paramMap.put('INCO_TERM', '');
        paramMap.put('SHPPING_PLACE', '');
        paramMap.put('PRICE_DATE', String.valueOf(Date.today()));
        paramMap.put('FERIGHT_FEE', '');
        paramMap.put('INSURANCE_FEE', '');
        paramMap.put('OTHER_FEE', '');
        paramMap.put('CHARGE_FORWEAR_FEE', '');
        paramMap.put('ORDER_STATUS','');
        paramMap.put('DROPSHIP_NAME','');
        paramMap.put('DROPSHIP_ADDRESS1','');
        paramMap.put('DROPSHIP_ADDRESS2', '');
        paramMap.put('DROPSHIP_PHONE', '');
        paramMap.put('DROPSHIP_COUNTRY', '');
        paramMap.put('DROPSHIP_ZIP', '');
        paramMap.put('DROPSHIP_STATE', '');
        paramMap.put('DROPSHIP_CITY', '');
        paramMap.put('INSTRUCTION_TO_DSV','');
        paramMap.put('CARRIER_INFORMATION', '');
        
        paramMap.put('HEADER_DISCOUNT', '');
        paramMap.put('HEADER_DISCOUNT_AMOUNT', '');
        paramMap.put('TOTAL_VALUE_NET', '');
        
        paramMap.put('IS_DROPSHIP', 'N');

        //TO DO：0815新增字段（AUTO_BOOK），暂不处理  
        // paramMap.put('AUTO_BOOK', 'NO');
        
        paramMap.put('LABOR_FEE', '');
        // paramMap.put('PURCHASE_DATE', '');
    

        paramMap.put('ATTRIBUTE1', '');
        paramMap.put('ATTRIBUTE2', '');
        paramMap.put('ATTRIBUTE3', '');
        paramMap.put('ATTRIBUTE4', '');
        paramMap.put('ATTRIBUTE5', '');
        paramMap.put('ATTRIBUTE6', '');
        paramMap.put('ATTRIBUTE7', '');
        paramMap.put('ATTRIBUTE8', '');
        paramMap.put('ATTRIBUTE9', '');
        paramMap.put('ATTRIBUTE10', '');
        paramMap.put('ATTRIBUTE11', '');
        paramMap.put('ATTRIBUTE12', '');
        paramMap.put('ATTRIBUTE13', '');
        paramMap.put('ATTRIBUTE14', '');
        paramMap.put('ATTRIBUTE15', '');
        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        
        //获取map：product id ->List price name
        Map<String,String> mapProductToPBName = new Map<String,String>();
        System.debug('Return 场景公共AccId===》'+acc.Id);
        mapProductToPBName = CCM_GetProductInfoUtil.getPriceName(lstProductId,Date.today(),headerData.CurrencyIsoCode,acc.Id);
        //根据productId 获取List price
        Map<String,Decimal> mapProductToListPrice = new Map<String,Decimal>();
        mapProductToListPrice = CCM_GetProductInfoUtil.getListPrice(lstProductId,Date.today(),headerData.CurrencyIsoCode,acc.Id);

        if (itemlist != null && itemlist.size() > 0) {
            for (Sales_Rep_Stock_Return_Item__c item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                itemMap.put('ORDER_NUMBER_CRM', headerData.Id);
                //itemMap.put('ORDER_NUMBER_CRM', '121313');
                system.debug('ORDER_NUMBER_CRM-->'+itemMap.get('ORDER_NUMBER_CRM'));
                itemMap.put('ORDERLINE_CRM_ID', item.Id);
                // itemMap.put('ORDERLINE_CRM_ID', '121313');
                itemMap.put('LINEID', getInterfaceId(item.Name));
                // itemMap.put('LINEID', '121313');
                system.debug('ORDERLINE_CRM_ID-->'+itemMap.get('ORDERLINE_CRM_ID'));
                itemMap.put('HEADERID', paramMap.get('HEADERID'));
                // itemMap.put('HEADERID', '121313');
                system.debug('HEADERID-->'+itemMap.get('HEADERID'));
                itemMap.put('CRM_ORDERLINE_NUMBER', item.Id);
                // itemMap.put('CRM_ORDERLINE_NUMBER', '121313');
                system.debug('CRM_ORDERLINE_NUMBER-->'+itemMap.get('CRM_ORDERLINE_NUMBER'));
                //itemMap.put('price date', item.Submit_Date__c.format('yyyy-MM-dd'));
                
                //行上的List price name 先从customer身上找一级，一级不符合则找Authorized brand 上的二级
                String childPbName = mapProductToPBName.containsKey(item.Product__c) ? mapProductToPBName.get(item.Product__c) : '';
                System.debug('item.Product__c===>'+item.Product__c);
                itemMap.put('LIST_PRICE_NAME', childPbName);
                Decimal dclListPrice ;
                dclListPrice = mapProductToListPrice.containsKey(item.Product__c) ? mapProductToListPrice.get(item.Product__c) : null;
                itemMap.put('LIST_PRICE', dclListPrice);
                // if(headerData.Return_Type__c.equals('Disposal')){
                //     itemMap.put('PRODUCT_RATING_STATUS', item.Product_Status_Rating__c);
                    
                // }else if(headerData.Return_Type__c.equals('Return To Warehouse')){
                //     itemMap.put('PRODUCT_RATING_STATUS', item.Product_Status_Rating__c);
                // }
                
                itemMap.put('PRODUCT_RATING_STATUS', item.Product_Status_Rating__c);
                //最终用户取值价格
                itemMap.put('UNIT_SELLING_PRICE', item.Price__c);
                itemMap.put('TOTAL_NET_PRICE', item.Price__c);
                itemMap.put('PRICE_DATE', String.valueOf(Date.today()));
                itemMap.put('PRODUCT_MODEL', item.Product__r.Order_Model__c);
                itemMap.put('ORDER_QUANTITY','1');
                itemMap.put('SERIAL_NUMBER', item.Sales_Rep_Stock__r.SerialNumber__c);
                
                itemMap.put('PROMOTION_CODE', item.Product_Status_Rating__c);
                itemMap.put('REQUEST_DATE',String.valueOf(Date.today()));
                //创建人取
                itemMap.put('SUBINVENTORY_CODE', headerData.CreatedBy.Sub_Inventory_Code__c);
                itemMap.put('SALES_PRICE', item.Price__c);
                itemMap.put('PROMOTION_DISCOUNT1', '');
                //promotion 头折扣
                itemMap.put('PROMOTION_DISCOUNT2','');
                //Modifire的折扣
                itemMap.put('STANDARD_DISCOUNT', 0);
                //经过promotion行折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE1',item.Price__c );
                //经过Promotion头折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE2', item.Price__c);
                
                itemMap.put('EST_REPLENISH_DATE','');
                itemMap.put('UOM', 'EA');
                itemMap.put('REMARK','');
                itemMap.put('SCHEDULE_SHIP_DATE','');

                //0821新增
                itemMap.put('STANDARD_DISCOUNT_TYPE','None');
                itemMap.put('PROMOTION_DISCOUNT1_TYPE','None');

                itemMap.put('TRAFFIC_LIGHT', '');

                itemMap.put('ATTRIBUTE1', '');
                itemMap.put('ATTRIBUTE2', '');
                itemMap.put('ATTRIBUTE3', '');
                itemMap.put('ATTRIBUTE4', '');
                itemMap.put('ATTRIBUTE5', '');
                itemMap.put('ATTRIBUTE6', '');
                itemMap.put('ATTRIBUTE7', '');
                itemMap.put('ATTRIBUTE8', '');
                itemMap.put('ATTRIBUTE9', '');
                itemMap.put('ATTRIBUTE10', '');
                itemMap.put('ATTRIBUTE11', '');
                itemMap.put('ATTRIBUTE12', '');
                itemMap.put('ATTRIBUTE13', '');
                itemMap.put('ATTRIBUTE14', '');
                itemMap.put('ATTRIBUTE15', '');
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        return paramMap;
    }
    //获取Resales申请参数
    public static Map<String, Object> getResalesRequestInfo(String recordId){
        Map<String, Object> paramMap = new Map<String, Object>();
        String soqlStr = 'Select id, Bill_To_Address__c, Bill_To_Address__r.Country__c,Bill_To_Address__r.Customer_Line_Oracle_ID__c, CurrencyIsoCode, customer_Po__c,Customer__c, '+
        'Customer__r.AccountNumber,Prospect__c,Customer__r.Owner.FederationIdentifier,is_DropShip_Order__c,Customer__r.List_Price__r.Name, '+
        'Dropship_Address__c, Dropship_Address__r.City__c, Dropship_Address__r.Country__c, Dropship_Address__r.Postal_Code__c,Vat__c, '+
        'Dropship_Address__r.Province__c, Dropship_Address__r.Street_1__c, Dropship_Address__r.Street_2__c,CreatedById, CreatedBy.Sub_Inventory_Code__c,'+
        'Dropship_Address_Phone__c, Dropship_Address_Province__c, DropShipAddresCountry__c, DropShipAddress2__c, DropShipAddressAddress__c, '+
        'Ship_To_Address__r.Name,Ship_To_Address__r.Street_1__c,Ship_To_Address__r.Street_2__c,Ship_To_Address__r.DropShip_Phone__c,'+
        'Ship_To_Address__r.Country__c,Ship_To_Address__r.Postal_Code__c,Ship_To_Address__r.Province__c,Ship_To_Address__r.City__c,'+
        'DropShipAddressCity__c, DropShipAddressCode__c, DropShipName__c, Freight_Cost__c, Inco_Term__c, Insurance_Fee__c, Name, Order_Date__c,Total_Due_Amount__c, '+
        'Other_Fee__c, payment_term__c, pricing_Date__c, Ship_To_Address__c, Ship_To_Address__r.Customer_Line_Oracle_ID__c, Total_Value_Formal__c,Total_Value_Net__c, warehouse__c, '+
        '(Select name,List_Price__c, Model__c,Product__c, Product_Status_Rating__c, Request_Date__c, Sales_Price__c, Serial_Number__c,Remark__c,Product_Discount__c, Unit_Net_Price__c from Sales_RepStock_ReSale_SN__r) '+
        'from Sales_Rep_Stock_Re_Sale__c where id =:recordId ';
        SObject obj = Database.query(soqlStr);
        String strDataTime = String.valueOf(system.now().getTime());
        Sales_Rep_Stock_Re_Sale__c po = (Sales_Rep_Stock_Re_Sale__c) obj;
        List<Sales_RepStock_ReSale_SN__c> itemlist = po.Sales_RepStock_ReSale_SN__r;
        String wareHouse = po.warehouse__c;
        paramMap.put('ORDER_NUMBER_CRM', po.Id);
        //paramMap.put('ORDER_NUMBER_CRM', '121313');
        system.debug('ORDER_NUMBER_CRM-->'+po.Id);
        paramMap.put('HEADERID',  getInterfaceId(po.Name));
        //paramMap.put('HEADERID', '121313');
        paramMap.put('PROMOTION_CODE',  '');
        paramMap.put('RETURN_TO_SUBINVENTORY',  '');
        paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
        paramMap.put('REQUEST_TO_SUBINVENTORY',  '');
        if(wareHouse.equals('C-stock')){
            paramMap.put('REQUEST_FROM_SUBINVENTORY',  'EGS02');
        }else if(wareHouse.equals('Sales Rep Stock')){
            //TO DO 根据request的stock request code查询销售人员子库(createBy)code
            paramMap.put('REQUEST_FROM_SUBINVENTORY', po.CreatedBy.Sub_Inventory_Code__c);
        }
        paramMap.put('CURRENCY_CODE',  po.CurrencyIsoCode);
        paramMap.put('ORDER_FLAG',  FLAG_YES);
        paramMap.put('SCENE_TYPE',  SCENE_TYPE_RESALES);
        paramMap.put('VAT',  po.Vat__c);

        system.debug('HEADERID-->'+paramMap.get('HEADERID'));

        String isDropship = 'N';
        if('true' == po.is_DropShip_Order__c){
            isDropship = 'Y';
        }else{
            isDropship = 'N';
        }
        //根据Customer/Prospect判断获取价格册、Customer Code
        String customerCode = '';
        String listPriceName = '';
        Account acc = new Account();
        if(String.isNotBlank(po.Prospect__c)){
            acc = getCustomerOfProspect(po.Prospect__c);
            customerCode = acc.AccountNumber;
            listPriceName = acc.List_Price__r.Name;
            isDropship = 'Y';
            if(String.isNotBlank(po.Ship_To_Address__c)){
                //若为prospect场景，ship address 按drop address段传输
                paramMap.put('SHIPTO', PUBLIC_SHIP_CODE);
                paramMap.put('BILLTO', PUBLIC_BILL_CODE);
                paramMap.put('DROPSHIP_NAME',po.Ship_To_Address__r.Name);
                paramMap.put('DROPSHIP_ADDRESS1',po.Ship_To_Address__r.Street_1__c);
                paramMap.put('DROPSHIP_ADDRESS2', po.Ship_To_Address__r.Street_2__c);
                paramMap.put('DROPSHIP_PHONE', po.Ship_To_Address__r.DropShip_Phone__c);
                paramMap.put('DROPSHIP_COUNTRY', po.Ship_To_Address__r.Country__c);
                paramMap.put('DROPSHIP_ZIP', po.Ship_To_Address__r.Postal_Code__c);
                paramMap.put('DROPSHIP_STATE', po.Ship_To_Address__r.Province__c);
                paramMap.put('DROPSHIP_CITY', po.Ship_To_Address__r.City__c);
            }else{
                paramMap.put('SHIPTO', PUBLIC_SHIP_CODE);
                paramMap.put('BILLTO', PUBLIC_BILL_CODE);
                paramMap.put('DROPSHIP_NAME',po.DropShipName__c == null ? po.Dropship_Address__r.Name : po.DropShipName__c );
                paramMap.put('DROPSHIP_ADDRESS1', po.DropShipAddressAddress__c == null ? po.Dropship_Address__r.Street_1__c : po.DropShipAddressAddress__c );
                paramMap.put('DROPSHIP_ADDRESS2', po.DropShipAddress2__c == null ? po.Dropship_Address__r.Street_2__c : po.DropShipAddress2__c );
                paramMap.put('DROPSHIP_PHONE', po.Dropship_Address_Phone__c);
                paramMap.put('DROPSHIP_COUNTRY', po.DropShipAddresCountry__c == null  ? po.Dropship_Address__r.Country__c : po.DropShipAddresCountry__c );
                paramMap.put('DROPSHIP_ZIP', po.DropShipAddressCode__c == null  ? po.Dropship_Address__r.Postal_Code__c : po.DropShipAddressCode__c );
                paramMap.put('DROPSHIP_STATE', po.Dropship_Address_Province__c == null  ? po.Dropship_Address__r.Province__c : po.Dropship_Address_Province__c );
                paramMap.put('DROPSHIP_CITY', po.DropShipAddressCity__c == null  ? po.Dropship_Address__r.City__c : po.DropShipAddressCity__c );
            }
        }else if(String.isNotBlank(po.Customer__c)){
            customerCode = po.Customer__r.AccountNumber;
            listPriceName = po.Customer__r.List_Price__r.Name;
            paramMap.put('SHIPTO', po.Ship_To_Address__r.Customer_Line_Oracle_ID__c);
            paramMap.put('BILLTO', po.Bill_To_Address__r.Customer_Line_Oracle_ID__c);
            paramMap.put('DROPSHIP_NAME',po.DropShipName__c == null ? po.Dropship_Address__r.Name : po.DropShipName__c );
            paramMap.put('DROPSHIP_ADDRESS1', po.DropShipAddressAddress__c == null ? po.Dropship_Address__r.Street_1__c : po.DropShipAddressAddress__c );
            paramMap.put('DROPSHIP_ADDRESS2', po.DropShipAddress2__c == null ? po.Dropship_Address__r.Street_2__c : po.DropShipAddress2__c );
            paramMap.put('DROPSHIP_PHONE', po.Dropship_Address_Phone__c);
            paramMap.put('DROPSHIP_COUNTRY', po.DropShipAddresCountry__c == null  ? po.Dropship_Address__r.Country__c : po.DropShipAddresCountry__c );
            paramMap.put('DROPSHIP_ZIP', po.DropShipAddressCode__c == null  ? po.Dropship_Address__r.Postal_Code__c : po.DropShipAddressCode__c );
            paramMap.put('DROPSHIP_STATE', po.Dropship_Address_Province__c == null  ? po.Dropship_Address__r.Province__c : po.Dropship_Address_Province__c );
            paramMap.put('DROPSHIP_CITY', po.DropShipAddressCity__c == null  ? po.Dropship_Address__r.City__c : po.DropShipAddressCity__c );
        }
        paramMap.put('CUSTOMER',customerCode);
        paramMap.put('ORG_CODE', ORG_EEG);
        system.debug('OrgCode-->'+paramMap.get('ORG_CODE'));
        paramMap.put('PO_NUMBER', po.customer_Po__c);
        
        // paramMap.put('PO_NUMBER', '121313');
    
        
        
        system.debug('SHIPTO-->'+paramMap.get('SHIPTO'));
        system.debug('BILLTO-->'+paramMap.get('BILLTO'));
        //change 0207
        // paramMap.put('ORDER_TYPE', ORDER_TYPE_RESALE_ORDER);
        String resOrderType = CCM_CovertOrderTypeUtil.covertOther2OracleType('SalesRepStock-Resale', po.Bill_To_Address__r.Country__c);
        paramMap.put('ORDER_TYPE', resOrderType);

        system.debug('ORDER_TYPE-->'+paramMap.get('ORDER_TYPE'));
        paramMap.put('PRICE_LIST', listPriceName);
        system.debug('PRICE_LIST-->'+paramMap.get('PRICE_LIST'));

        paramMap.put('WARE_HOUSE_ORACLE', ORG_EEG);
        system.debug('WARE_HOUSE_ORACLE-->'+paramMap.get('WARE_HOUSE_ORACLE'));
        if(po.Customer__c != null){
            paramMap.put('SALES_REP_Code', po.Customer__r.Owner.FederationIdentifier);
        }else if(po.Prospect__c != null){
            paramMap.put('SALES_REP_Code', acc.Owner.FederationIdentifier);
        }
        
        system.debug('SALES_REP_Code-->'+paramMap.get('SALES_REP_Code'));
        
        paramMap.put('DATE_ORDER', String.valueOf(po.Order_Date__c) );
        system.debug('DATE_ORDER-->'+paramMap.get('DATE_ORDER'));
        paramMap.put('CURRENCY_CODE', po.CurrencyIsoCode);
        paramMap.put('PAYMENT_TERM', po.payment_term__c);
        paramMap.put('FREIGHT_TERM', '');
        paramMap.put('INCO_TERM', po.Inco_Term__c);
        paramMap.put('SHPPING_PLACE', '');
        paramMap.put('PRICE_DATE', String.valueOf(po.pricing_Date__c) );
        system.debug('PRICE_DATE-->'+paramMap.get('PRICE_DATE'));
        paramMap.put('FERIGHT_FEE', po.Freight_Cost__c > 0 ? String.valueOf(po.Freight_Cost__c) : '');
        paramMap.put('INSURANCE_FEE', po.Insurance_Fee__c > 0 ? String.valueOf(po.Insurance_Fee__c) : '');
        paramMap.put('OTHER_FEE', po.Other_Fee__c > 0 ? String.valueOf(po.Other_Fee__c) : '');
        
        paramMap.put('CHARGE_FORWEAR_FEE', '');
        paramMap.put('ORDER_STATUS','');
        
        paramMap.put('INSTRUCTION_TO_DSV', '');
        paramMap.put('CARRIER_INFORMATION', '');
        paramMap.put('TOTAL_VALUE', po.Total_Value_Formal__c);
        paramMap.put('HEADER_DISCOUNT', '');
        paramMap.put('HEADER_DISCOUNT_AMOUNT', '');
        paramMap.put('TOTAL_VALUE_NET', po.Total_Value_Net__c);
        //TO DO 
        paramMap.put('TOTAL_AMOUNT', po.Total_Due_Amount__c);

        
        paramMap.put('IS_DROPSHIP', isDropship);
        
        //TO DO：0815新增字段（AUTO_BOOK），暂不处理  
        // paramMap.put('AUTO_BOOK', 'NO');
        
        // paramMap.put('LABOR_FEE', '');
        // paramMap.put('PURCHASE_DATE', '');

        paramMap.put('ATTRIBUTE1', '');
        paramMap.put('ATTRIBUTE2', '');
        paramMap.put('ATTRIBUTE3', '');
        paramMap.put('ATTRIBUTE4', '');
        paramMap.put('ATTRIBUTE5', '');
        paramMap.put('ATTRIBUTE6', '');
        paramMap.put('ATTRIBUTE7', '');
        paramMap.put('ATTRIBUTE8', '');
        paramMap.put('ATTRIBUTE9', '');
        paramMap.put('ATTRIBUTE10', '');
        paramMap.put('ATTRIBUTE11', '');
        paramMap.put('ATTRIBUTE12', '');
        paramMap.put('ATTRIBUTE13', '');
        paramMap.put('ATTRIBUTE14', '');
        paramMap.put('ATTRIBUTE15', '');
        
        //获取行上的ProductId
        List<String> lstProductId = new List<String>();
        if (itemlist != null && itemlist.size() > 0) {
            for (Sales_RepStock_ReSale_SN__c resale : itemlist) {
                lstProductId.add(resale.Product__c);
            }
        }
        //获取map：product id ->List price name
        Map<String,String> mapProductToPBName = new Map<String,String>();
        if(String.isNotBlank(po.Customer__c)){
            mapProductToPBName = CCM_GetProductInfoUtil.getPriceName(lstProductId,Date.valueOf(po.pricing_Date__c),po.CurrencyIsoCode,po.Customer__c);
        }else{
            mapProductToPBName = CCM_GetProductInfoUtil.getPriceName(lstProductId,Date.valueOf(po.pricing_Date__c),po.CurrencyIsoCode,acc.Id);
        }
        
        //根据productId 获取List price
        Map<String,Decimal> mapProductToListPrice = new Map<String,Decimal>();
        mapProductToListPrice = CCM_GetProductInfoUtil.getListPrice(lstProductId,Date.today(),po.CurrencyIsoCode,acc.Id);

        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        if (itemlist != null && itemlist.size() > 0) {
            for (Sales_RepStock_ReSale_SN__c item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                itemMap.put('ORDER_NUMBER_CRM', po.Id);
                //itemMap.put('ORDER_NUMBER_CRM', '121313');
                
                system.debug('ORDER_NUMBER_CRM-->'+itemMap.get('ORDER_NUMBER_CRM'));
                itemMap.put('ORDERLINE_CRM_ID', item.Id);
                // itemMap.put('ORDERLINE_CRM_ID', '121313');
        
                itemMap.put('LINEID', getInterfaceId(item.Name));
                // itemMap.put('LINEID', '121313');
                system.debug('ORDERLINE_CRM_ID-->'+itemMap.get('ORDERLINE_CRM_ID'));
                itemMap.put('HEADERID', paramMap.get('HEADERID'));
                // itemMap.put('HEADERID', '121313');
                
                system.debug('HEADERID-->'+itemMap.get('HEADERID'));
                itemMap.put('CRM_ORDERLINE_NUMBER', item.Id);
                // itemMap.put('CRM_ORDERLINE_NUMBER', '121313');
                
                
                system.debug('CRM_ORDERLINE_NUMBER-->'+itemMap.get('CRM_ORDERLINE_NUMBER'));
                //itemMap.put('price date', item.Submit_Date__c.format('yyyy-MM-dd'));
                itemMap.put('PRICE_DATE', String.valueOf(Date.today()));
                itemMap.put('PRODUCT_MODEL', item.Model__c);
                itemMap.put('ORDER_QUANTITY','1');

                //行上的List price name 先从customer身上找一级，一级不符合则找Authorized brand 上的二级
                String childPbName = mapProductToPBName.containsKey(item.Product__c) ? mapProductToPBName.get(item.Product__c) : '';
                itemMap.put('LIST_PRICE_NAME', childPbName);

                itemMap.put('SERIAL_NUMBER', item.Serial_Number__c);
                itemMap.put('PRODUCT_RATING_STATUS', item.Product_Status_Rating__c);
                //取磨损程度C1、C2、C3
                itemMap.put('PROMOTION_CODE', item.Product_Status_Rating__c);

                Decimal dclListPrice ;
                dclListPrice = mapProductToListPrice.containsKey(item.Product__c) ? mapProductToListPrice.get(item.Product__c) : null;
                // itemMap.put('LIST_PRICE', dclListPrice);
                itemMap.put('LIST_PRICE', item.List_Price__c);
                
                itemMap.put('REQUEST_DATE', String.valueOf(item.Request_Date__c));
                system.debug('REQUEST_DATE-->'+itemMap.get('REQUEST_DATE'));
                //sales rep  stock ->  创建人取；C-stock  固定（EGS02）
                if(wareHouse.equals('C-stock')){
                    itemMap.put('SUBINVENTORY_CODE',  'EGS02');
                }else if(wareHouse.equals('Sales Rep Stock')){
                    //TO DO 根据request的stock request code查询销售人员子库(createBy)code
                    itemMap.put('SUBINVENTORY_CODE', po.CreatedBy.Sub_Inventory_Code__c);
                }
                //Todo-->待前端传了sales Price计算-->经过Modifire打折后的计算
                // itemMap.put('SALES_PRICE', dclListPrice);
                itemMap.put('SALES_PRICE', item.List_Price__c);
                //Promotion行折扣   取磨损程度C1、C2、C3对应Discount
                itemMap.put('PROMOTION_DISCOUNT1', item.Product_Discount__c);
                //promotion 头折扣
                itemMap.put('PROMOTION_DISCOUNT2', 0);
                //Modifire的折扣
                itemMap.put('STANDARD_DISCOUNT', 0);
                //经过promotion行折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE1', item.Unit_Net_Price__c == null ? 0 : item.Unit_Net_Price__c.setScale(2, RoundingMode.HALF_UP));
                //经过Promotion头折扣的计算后的价格
                itemMap.put('UNIT_NET_PRICE2', item.Unit_Net_Price__c == null ? 0 : item.Unit_Net_Price__c.setScale(2, RoundingMode.HALF_UP));
                //最终用户取值价格
                itemMap.put('UNIT_SELLING_PRICE', item.Unit_Net_Price__c == null ? 0 : item.Unit_Net_Price__c.setScale(2, RoundingMode.HALF_UP));
                //0821新增
                itemMap.put('STANDARD_DISCOUNT_TYPE','None');
                itemMap.put('PROMOTION_DISCOUNT1_TYPE',PROMOTION_TYPE_NEW_PRICE);
                
                itemMap.put('EST_REPLENISH_DATE','');
                itemMap.put('UOM', 'EA');
                itemMap.put('TOTAL_NET_PRICE', item.Unit_Net_Price__c == null ? 0 : item.Unit_Net_Price__c.setScale(2, RoundingMode.HALF_UP));
                itemMap.put('REMARK', item.Remark__c);
                itemMap.put('SCHEDULE_SHIP_DATE',''); 

                itemMap.put('TRAFFIC_LIGHT', '');

                itemMap.put('ATTRIBUTE1', '');
                itemMap.put('ATTRIBUTE2', '');
                itemMap.put('ATTRIBUTE3', '');
                itemMap.put('ATTRIBUTE4', '');
                itemMap.put('ATTRIBUTE5', '');
                itemMap.put('ATTRIBUTE6', '');
                itemMap.put('ATTRIBUTE7', '');
                itemMap.put('ATTRIBUTE8', '');
                itemMap.put('ATTRIBUTE9', '');
                itemMap.put('ATTRIBUTE10', '');
                itemMap.put('ATTRIBUTE11', '');
                itemMap.put('ATTRIBUTE12', '');
                itemMap.put('ATTRIBUTE13', '');
                itemMap.put('ATTRIBUTE14', '');
                itemMap.put('ATTRIBUTE15', '');
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        return paramMap;
    }
    public static Account getAccByNum(String accNum){
        List<Account> lstAcc = new List<Account>();
        lstAcc=[SELECT 
                Id,AccountNumber, CreatedById, CreatedBy.Name, CreatedDate, Customer_Oracle_ID__c,List_Price__r.Name 
                FROM Account  
                WHERE AccountNumber =:accNum and Customer_Oracle_ID__c != null 
                ORDER BY CreatedDate Desc];
        Account acc = new Account();
        if(lstAcc.size() > 0){
            acc = lstAcc[0];
        }
        return acc;
    }

    //根据Prospect 获取customer
    public static Account getCustomerOfProspect(String prospectId){
        Account objAccount = new Account();
        Lead objLead = [SELECT Id,Country__c FROM Lead WHERE Id = :prospectId];
        if(objLead != NULL){
            system.debug('Country__c-->'+objLead.Country__c);
            objAccount = CCM_SalesRepStockUtils.getCollectiveCustomerOfProspect(objLead);
        }
        return objAccount;
    }   

    /**
     * 生成接口序列Id
     */
    public static String getInterfaceId(String autoNo){
        String dateStr = SystemUtils.getCurrentTimeStamp().substring(0,8);
        String resultDateStr = dateStr.substring(2);
        String strResult = resultDateStr + autoNo;
        return strResult;
    }

    public class SyncRes {
        public String Process_Status;
        public String PROCESS_MSG;
    }
    
    
    public class SyncResult {
        public String ReturnCode;
        public String ReturnMessage;
        public String Process_Status;
        public List<ProcessResult> Process_Result; 
    }

    public class ProcessResult {
        
        public String SFDC_Id;
        public String Oracle_Id;
        public String Error_Message;
        public String Error_Detail;
    }
}