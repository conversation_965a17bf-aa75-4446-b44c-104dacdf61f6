({
    doInit : function(component, event, helper) {
        component.set('v.isDE', $A.get("$Label.c.CCM_Translate"));
        let freightCost = component.get('v.freightCost');
        let freightCostNum = Number(freightCost);
        console.log(freightCostNum > 0 && freightCostNum <= 150, 'freightCostNum--------------');
        if (freightCostNum > 0 && freightCostNum <= 150) {
            component.set('v.othersTips', 1);
            // component.set('v.othersTips', 'Order value: 0-150€ -> Freight cost: 7€​​');
        } else if (freightCostNum > 150 && freightCostNum <= 500) {
            component.set('v.othersTips', 2);
            // component.set('v.othersTips', 'Order value: 150–500€ -> Freight cost: 13€​​​​');
        } else if (freightCostNum > 500 && freightCostNum <= 1000) {
            component.set('v.othersTips', 3);
            // component.set('v.othersTips', 'Order value: 500–1000€ -> Freight cost: 16.50€​​​​');
        } else if (freightCostNum > 1000) {
            component.set('v.othersTips', 4);
            // component.set('v.othersTips', 'Order value: over 1000€ -> Freight cost: 0€​​');
        }
        if (component.get('v.isDropShip') === 'N') {
            component.set('v.disableFlag', true);
        } else {
            component.set('v.disableFlag', false);
        }
        component.set('v.showDetailfield', false);
        helper.getInstructionToDSVOptions(component);
        // 判断是否edit
        let actionType = component.get('v.actionType');
        if (actionType === 'edit' || actionType === 'draft') {
            // 获取旧数据
            console.log('获取旧数据-----------');
            helper.getPODetailInfo(component);
        } else {
            // 自动填充address
            helper.getBillAddressList(component);
            helper.getShipAddressList(component);
        }
        // 获取picklist
        helper.getDropshipList(component);
        console.log(component.get('v.userType'), 'userType----------------');
    },
    // 下一步
    nextStep: function(component, event, helper){
        // 校验必填项
        let shipToAddressValid = false;
        let billToAddressValid = false;
        let shipToAddress = JSON.parse(JSON.stringify(component.get('v.shipToAddress')));
        const shipToAddressElement = component.find('shipToAddress');
        const shipToAddressRequiredText = component.find('shipToAddress-error-required');
        if (shipToAddress.Id) {
            shipToAddressValid = true;
            $A.util.removeClass(shipToAddressElement, 'field-error');
            $A.util.addClass(shipToAddressRequiredText, 'slds-hide');
        } else {
            shipToAddressValid = false;
            $A.util.addClass(shipToAddressElement, 'field-error');
            $A.util.removeClass(shipToAddressRequiredText, 'slds-hide');
        }
        let billToAddress = JSON.parse(JSON.stringify(component.get('v.billToAddress')));
        const billToAddressElement = component.find('billToAddress');
        const billToAddressRequiredText = component.find('billToAddress-error-required');
        if (billToAddress.Id) {
            billToAddressValid = true;
            $A.util.removeClass(billToAddressElement, 'field-error');
            $A.util.addClass(billToAddressRequiredText, 'slds-hide');
        } else {
            billToAddressValid = false;
            $A.util.addClass(billToAddressElement, 'field-error');
            $A.util.removeClass(billToAddressRequiredText, 'slds-hide');
        };
        if (billToAddressValid && shipToAddressValid) {
            helper.handleNextStep(component, true, false);
        } else {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_FillinRequiredFieldsWarning"),
                "type": "warning",
                "duration": "dismissible"
            }).fire();
        }
    },
    // 上一步
    previousStep: function(component, event, helper){
        // var currentStep = component.get("v.currentStep");
        // var actionType = component.get("v.actionType");
        // if (actionType !== 'edit') {
        //     component.set('v.actionType', 'draft');
        // }
        // component.set("v.currentStep", currentStep - 1);
        helper.handleNextStep(component, false, false);
    },
    // 取消事件
    cancel: function(component){
        let url = window.location.origin + '/s/orderinformation';
        window.open(url, '_self');
    },
    // 监听DropshipType
    changeDropshipType: function(component, event, helper){
        let dropshipTypeValue = component.get('v.dropshipType');
        console.log(dropshipTypeValue, 'dropshipTypeValue------------');
        if (dropshipTypeValue === 'End Consumer') {
            component.set('v.dropshipAddressFlag', true);
            component.set('v.showDetailfield', true);
            component.set('v.dropshipAddress', null);
        } else {
            component.set('v.dropshipAddressFlag', false);
            component.set('v.showDetailfield', false);
            // 清空详细信息
            component.set('v.DropshipName', '');
            component.set('v.DropShipAddress1', '');
            component.set('v.DropShipAddress2', '');
            component.set('v.DropShipPhone', '');
            component.set('v.DropShipCountry', null);
            component.set('v.DropShipCity', '');
            component.set('v.DropShipZip', '');
            component.set('v.DropShipState', '');
        }
    },

    saveAndClose: function(component, event, helper) {
        helper.handleNextStep(component, true, true);
    }
})