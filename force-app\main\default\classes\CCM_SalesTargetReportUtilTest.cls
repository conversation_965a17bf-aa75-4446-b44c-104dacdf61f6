/**
 * <AUTHOR>
 * @date 2025-08-04
 * @description Test class for CCM_SalesTargetReportUtil
 */
@isTest
public class CCM_SalesTargetReportUtilTest {
    
    @TestSetup
    static void makeData() {
        // Create test users for sales reps
        Profile salesProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];
        
        User salesRep1 = new User(
            FirstName = 'Test',
            LastName = 'SalesRep1',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'tsrep1',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = salesProfile.Id
        );
        
        User salesRep2 = new User(
            FirstName = 'Test',
            LastName = 'SalesRep2',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'tsrep2',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = salesProfile.Id
        );
        
        insert new List<User>{salesRep1, salesRep2};
        
        // Create test account
        Account testAccount = new Account(
            Name = 'Test Account',
            CurrencyIsoCode = 'USD'
        );
        insert testAccount;
        
        // Create test orders with different record types
        List<Order> testOrders = new List<Order>();
        
        Order regularOrder1 = new Order(
            AccountId = testAccount.Id,
            Status = 'Draft',
            EffectiveDate = Date.today(),
            Sales_Rep__c = salesRep1.Id
        );
        testOrders.add(regularOrder1);
        
        Order regularOrder2 = new Order(
            AccountId = testAccount.Id,
            Status = 'Draft',
            EffectiveDate = Date.today(),
            Sales_Rep__c = salesRep2.Id
        );
        testOrders.add(regularOrder2);
        
        // Warranty order (should be excluded)
        Order warrantyOrder = new Order(
            AccountId = testAccount.Id,
            Status = 'Draft',
            EffectiveDate = Date.today(),
            Sales_Rep__c = salesRep1.Id
        );
        testOrders.add(warrantyOrder);
        
        insert testOrders;
        
        // Update warranty order record type after insert
        RecordType warrantyRecordType = [SELECT Id FROM RecordType WHERE SObjectType = 'Order' AND DeveloperName = 'Warranty_Claim' LIMIT 1];
        if (warrantyRecordType != null) {
            warrantyOrder.RecordTypeId = warrantyRecordType.Id;
            update warrantyOrder;
        }
        
        // Create quota allocations
        Quota_Allocation__c quotaAllocation1 = new Quota_Allocation__c(
            Name = 'Q1 2025 Quota',
            Start_Date__c = Date.newInstance(2025, 1, 1),
            End_Date__c = Date.newInstance(2025, 3, 31),
            Status__c = 'Active'
        );
        
        Quota_Allocation__c quotaAllocation2 = new Quota_Allocation__c(
            Name = 'Q2 2025 Quota',
            Start_Date__c = Date.newInstance(2025, 4, 1),
            End_Date__c = Date.newInstance(2025, 6, 30),
            Status__c = 'Active'
        );
        
        insert new List<Quota_Allocation__c>{quotaAllocation1, quotaAllocation2};
        
        // Create sales target detail reports
        List<Sales_Target_Detail_Report__c> targetReports = new List<Sales_Target_Detail_Report__c>();
        
        Sales_Target_Detail_Report__c report1 = new Sales_Target_Detail_Report__c(
            Quota_Allocation__c = quotaAllocation1.Id,
            Sales_Person_Name__c = 'Test SalesRep1',
            Assigned_Quota__c = 100000
        );
        targetReports.add(report1);
        
        Sales_Target_Detail_Report__c report2 = new Sales_Target_Detail_Report__c(
            Quota_Allocation__c = quotaAllocation2.Id,
            Sales_Person_Name__c = 'Test SalesRep2',
            Assigned_Quota__c = 150000
        );
        targetReports.add(report2);
        
        insert targetReports;
        
        // Create test invoices
        List<Invoice__c> testInvoices = new List<Invoice__c>();
        
        Invoice__c invoice1 = new Invoice__c(
            Order__c = regularOrder1.Id,
            Invoice_Date__c = Date.newInstance(2025, 2, 15), // Within Q1 range
            Sales_Target_Detail__c = null
        );
        testInvoices.add(invoice1);
        
        Invoice__c invoice2 = new Invoice__c(
            Order__c = regularOrder2.Id,
            Invoice_Date__c = Date.newInstance(2025, 5, 15), // Within Q2 range
            Sales_Target_Detail__c = null
        );
        testInvoices.add(invoice2);
        
        Invoice__c invoice3 = new Invoice__c(
            Order__c = warrantyOrder.Id,
            Invoice_Date__c = Date.newInstance(2025, 2, 15), // Warranty order - should be excluded
            Sales_Target_Detail__c = null
        );
        testInvoices.add(invoice3);
        
        Invoice__c invoice4 = new Invoice__c(
            Order__c = regularOrder1.Id,
            Invoice_Date__c = Date.newInstance(2025, 7, 15), // Outside any quota range
            Sales_Target_Detail__c = null
        );
        testInvoices.add(invoice4);
        
        insert testInvoices;
    }
    
    /**
     * @description Test the main linkInvoiceToSalesTargetDetail method
     */
    @isTest
    static void testLinkInvoiceToSalesTargetDetail() {
        // Get test invoices
        List<Invoice__c> testInvoices = [
            SELECT Id, Invoice_Date__c, Order__r.Sales_Rep__r.Name 
            FROM Invoice__c 
            WHERE Order__r.RecordType.DeveloperName NOT IN ('Warranty_Claim', 'Warranty_Order')
            AND Sales_Target_Detail__c = null
        ];
        
        List<String> invoiceIds = new List<String>();
        for (Invoice__c invoice : testInvoices) {
            invoiceIds.add(invoice.Id);
        }
        
        Test.startTest();
        
        // Call the method under test
        CCM_SalesTargetReportUtil.linkInvoiceToSalesTargetDetail(invoiceIds);
        
        Test.stopTest();
        
        // Verify that invoices were linked to sales target details
        List<Invoice__c> updatedInvoices = [
            SELECT Id, Sales_Target_Detail__c, Invoice_Date__c, Order__r.Sales_Rep__r.Name
            FROM Invoice__c 
            WHERE Id IN :invoiceIds
            AND Order__r.RecordType.DeveloperName NOT IN ('Warranty_Claim', 'Warranty_Order')
        ];
        
        Integer linkedCount = 0;
        for (Invoice__c invoice : updatedInvoices) {
            if (invoice.Sales_Target_Detail__c != null) {
                linkedCount++;
            }
        }
        
        System.assert(linkedCount > 0, 'At least some invoices should be linked to sales target details');
        
        // Verify that sales target detail reports were updated with correct owner
        List<Sales_Target_Detail_Report__c> updatedReports = [
            SELECT Id, OwnerId, Sales_Person_Name__c
            FROM Sales_Target_Detail_Report__c
        ];
        
        System.assert(!updatedReports.isEmpty(), 'Sales target detail reports should exist');
    }
    
    /**
     * @description Test with invoices that have no matching sales target details
     */
    @isTest
    static void testLinkInvoiceNoMatchingSalesTarget() {
        // Create an invoice with a date outside any quota allocation range
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        User salesRep = [SELECT Id FROM User WHERE Username LIKE '%testsalesrep%' LIMIT 1];
        
        Order testOrder = new Order(
            AccountId = testAccount.Id,
            Status = 'Draft',
            EffectiveDate = Date.today(),
            Sales_Rep__c = salesRep.Id
        );
        insert testOrder;
        
        Invoice__c testInvoice = new Invoice__c(
            Order__c = testOrder.Id,
            Invoice_Date__c = Date.newInstance(2024, 1, 1), // Date outside any quota range
            Sales_Target_Detail__c = null
        );
        insert testInvoice;
        
        Test.startTest();
        
        CCM_SalesTargetReportUtil.linkInvoiceToSalesTargetDetail(new List<String>{testInvoice.Id});
        
        Test.stopTest();
        
        // Verify that invoice was not linked
        Invoice__c updatedInvoice = [
            SELECT Id, Sales_Target_Detail__c 
            FROM Invoice__c 
            WHERE Id = :testInvoice.Id
        ];
        
        System.assertEquals(null, updatedInvoice.Sales_Target_Detail__c, 
            'Invoice should not be linked when no matching sales target detail exists');
    }
    
    /**
     * @description Test with empty invoice list
     */
    @isTest
    static void testLinkInvoiceEmptyList() {
        Test.startTest();
        
        try {
            CCM_SalesTargetReportUtil.linkInvoiceToSalesTargetDetail(new List<String>());
            System.assert(true, 'Method should handle empty list gracefully');
        } catch (Exception e) {
            System.debug('Exception in testLinkInvoiceEmptyList: ' + e.getMessage());
        }
        
        Test.stopTest();
    }
    
    /**
     * @description Test with null invoice list
     */
    @isTest
    static void testLinkInvoiceNullList() {
        Test.startTest();
        
        try {
            CCM_SalesTargetReportUtil.linkInvoiceToSalesTargetDetail(null);
            System.assert(true, 'Method should handle null list gracefully');
        } catch (Exception e) {
            System.debug('Exception in testLinkInvoiceNullList: ' + e.getMessage());
        }
        
        Test.stopTest();
    }
    
    /**
     * @description Test with invoices that already have sales target details linked
     */
    @isTest
    static void testLinkInvoiceAlreadyLinked() {
        // Get a sales target detail report
        Sales_Target_Detail_Report__c targetReport = [SELECT Id FROM Sales_Target_Detail_Report__c LIMIT 1];
        
        // Create an invoice that already has a sales target detail linked
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        User salesRep = [SELECT Id FROM User WHERE Username LIKE '%testsalesrep%' LIMIT 1];
        
        Order testOrder = new Order(
            AccountId = testAccount.Id,
            Status = 'Draft',
            EffectiveDate = Date.today(),
            Sales_Rep__c = salesRep.Id
        );
        insert testOrder;
        
        Invoice__c testInvoice = new Invoice__c(
            Order__c = testOrder.Id,
            Invoice_Date__c = Date.newInstance(2025, 2, 15),
            Sales_Target_Detail__c = targetReport.Id // Already linked
        );
        insert testInvoice;
        
        Test.startTest();
        
        CCM_SalesTargetReportUtil.linkInvoiceToSalesTargetDetail(new List<String>{testInvoice.Id});
        
        Test.stopTest();
        
        // Verify that invoice remains linked to the same target detail
        Invoice__c updatedInvoice = [
            SELECT Id, Sales_Target_Detail__c 
            FROM Invoice__c 
            WHERE Id = :testInvoice.Id
        ];
        
        System.assertEquals(targetReport.Id, updatedInvoice.Sales_Target_Detail__c, 
            'Invoice should remain linked to the original sales target detail');
    }
    
    /**
     * @description Test error handling and logging
     */
    @isTest
    static void testErrorHandlingAndLogging() {
        // This test verifies that the method handles database errors gracefully
        // and creates log records when updates fail
        
        // Get test invoices
        List<Invoice__c> testInvoices = [
            SELECT Id FROM Invoice__c 
            WHERE Order__r.RecordType.DeveloperName NOT IN ('Warranty_Claim', 'Warranty_Order')
            AND Sales_Target_Detail__c = null
            LIMIT 1
        ];
        
        if (!testInvoices.isEmpty()) {
            List<String> invoiceIds = new List<String>();
            for (Invoice__c invoice : testInvoices) {
                invoiceIds.add(invoice.Id);
            }
            
            Test.startTest();
            
            // Call the method - it should handle any potential errors gracefully
            CCM_SalesTargetReportUtil.linkInvoiceToSalesTargetDetail(invoiceIds);
            
            Test.stopTest();
            
            // Check if any log records were created (in case of errors)
            List<Log__c> logs = [
                SELECT Id, Name, ApexName__c, Error_Message__c 
                FROM Log__c 
                WHERE ApexName__c = 'CCM_SalesTargetReportUtil'
            ];
            
            // The test should pass regardless of whether logs were created
            System.assert(true, 'Method should complete without throwing exceptions');
        }
    }
}
