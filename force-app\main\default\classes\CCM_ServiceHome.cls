/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 04-08-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_ServiceHome{
    @AuraEnabled
    public static String allUserHistory(String userId, Integer pageNumber, Integer allPageSize, String companyName, String name, String email){
        String accId = CCM_PortalPageUtil.getCustomerByUser(userId);

        String salesChannel = 'Dealer';
        String country = '';
        for(Account acc : [SELECT Sales_Channel__c, Country__c FROM Account WHERE Id = :accId]) {
            if(String.isNotBlank(acc.Sales_Channel__c) && acc.Sales_Channel__c.containsIgnoreCase('Distributor')) {
                salesChannel = 'Distributor';
                country = acc.Country__c;
            }
        }

        List<UserHistoryClass> userHistoryClassList = new List<UserHistoryClass>();
        List<UserHistoryClass> allConsumersRegistration = new List<UserHistoryClass>();
        if(salesChannel == 'Dealer') {
            String sql = 'SELECT Id, RelatedConsumer__r.Consumer_Company_Formula__c,RelatedConsumer__r.Fleet_Manager__c, RelatedConsumer__c, RelatedConsumer__r.RecordType.Name, RelatedConsumer__r.Company__c, RelatedConsumer__r.Name, RelatedConsumer__r.PersonEmail, RelatedConsumer__r.CreatedDate, RelatedConsumer__r.ConfirmeDate__c, RelatedConsumer__r.Consumer_Status__c, RelatedConsumer__r.Contact_Name__c, RelatedConsumer__r.Email__c' + 
                 ' FROM Dealer_Share__c' + 
                 ' WHERE  RelatedConsumer__r.Consumer_Status__c <> \'Inactive\' AND RelatedConsumer__r.RecordType.Name in (\'Residential Consumer\', \'Commercial Consumer\')';
            if (String.isNotBlank(accId)){
                sql += ' And RelatedDealer__c =\'' + accId + '\'';
            }
            if (String.isNotBlank(companyName)){
                companyName = '%' + companyName + '%';
                sql += ' And RelatedConsumer__r.Consumer_Company_Formula__c like \'' + companyName + '\'';
            }
            if (String.isNotBlank(name) ){
                name = '%' + name + '%';
                sql += ' And RelatedConsumer__r.name like \'' + name + '\'';
            }
            if (String.isNotBlank(email)){
                email = '%' + email + '%';
                sql += ' And RelatedConsumer__r.Consumer_Email_Formula__c  like \'' + email + '\'';
            }
            sql += ' ORDER BY RelatedConsumer__r.CreatedDate DESC LIMIT 4000';
            system.debug('sql:' + sql);
            List<Dealer_Share__c> accountList = new List<Dealer_Share__c>();
            accountList = Database.query(sql);
            for(Dealer_Share__c dealerShare : accountList) {
                UserHistoryClass consumerRegistration = new UserHistoryClass();
                consumerRegistration.Id = dealerShare.RelatedConsumer__c;
                consumerRegistration.UserType = 'Residential Consumer'.equals(dealerShare.RelatedConsumer__r.RecordType.Name) ? 'Residential' : 'Commercial';
                consumerRegistration.Company = dealerShare.RelatedConsumer__r.Consumer_Company_Formula__c;
                if (consumerRegistration.UserType.equals('Residential')){
                    consumerRegistration.Name = dealerShare.RelatedConsumer__r.Name;
                    consumerRegistration.ConsumerOrContactID = dealerShare.RelatedConsumer__c;
                    consumerRegistration.Email = dealerShare.RelatedConsumer__r.PersonEmail;
                } else{
                    consumerRegistration.ConsumerOrContactID = dealerShare.RelatedConsumer__r.Fleet_Manager__c;
                    consumerRegistration.Name = dealerShare.RelatedConsumer__r.Contact_Name__c;
                    consumerRegistration.Email = dealerShare.RelatedConsumer__r.Email__c;
                }
                consumerRegistration.ApplyDate = dealerShare.RelatedConsumer__r.CreatedDate.date();
                consumerRegistration.ConfirmeDate = dealerShare.RelatedConsumer__r.ConfirmeDate__c;
                consumerRegistration.Status = dealerShare.RelatedConsumer__r.Consumer_Status__c;
                Date today = Date.today();
                Date dueDate = dealerShare.RelatedConsumer__r.CreatedDate.date().addDays(30);
                consumerRegistration.DueDate = dueDate;
                consumerRegistration.isEdit = 'Waiting Customer Approval'.equals(dealerShare.RelatedConsumer__r.Consumer_Status__c) && today < dueDate;
                allConsumersRegistration.add(consumerRegistration);
            }
        }
        else if(salesChannel == 'Distributor') {
            List<String> accRecordTypes = new List<String> {
                CCM_Constants.PERSONACCOUNT,
                CCM_Constants.CommercialConsumerName
            };
            String distributorUserRegistrationSql = 'SELECT Consumer_Company_Formula__c, Fleet_Manager__c, RecordType.Name, Company__c, Name, PersonEmail, CreatedDate, ConfirmeDate__c, Consumer_Status__c, Contact_Name__c, Email__c FROM Account WHERE RecordType.DeveloperName IN :accRecordTypes';
            if(String.isNotBlank(country)) {
                distributorUserRegistrationSql += ' AND ShippingCountry = :country';
            }
            if (String.isNotBlank(companyName)){
                companyName = '%' + companyName + '%';
                distributorUserRegistrationSql += ' And Consumer_Company_Formula__c like \'' + companyName + '\'';
            }
            if (String.isNotBlank(name) ){
                name = '%' + name + '%';
                distributorUserRegistrationSql += ' And Name like \'' + name + '\'';
            }
            if (String.isNotBlank(email)){
                email = '%' + email + '%';
                distributorUserRegistrationSql += ' And Consumer_Email_Formula__c like \'' + email + '\'';
            }
            distributorUserRegistrationSql += ' ORDER BY CreatedDate DESC LIMIT 4000';
            List<Account> consumerList = Database.query(distributorUserRegistrationSql);
            for(Account consumer : consumerList) {
                UserHistoryClass consumerRegistration = new UserHistoryClass();
                consumerRegistration.Id = consumer.Id;
                consumerRegistration.UserType = 'Residential Consumer'.equals(consumer.RecordType.Name) ? 'Residential' : 'Commercial';
                consumerRegistration.Company = consumer.Consumer_Company_Formula__c;
                if (consumerRegistration.UserType.equals('Residential')){
                    consumerRegistration.Name = consumer.Name;
                    consumerRegistration.ConsumerOrContactID = consumer.Id;
                    consumerRegistration.Email = consumer.PersonEmail;
                } else{
                    consumerRegistration.ConsumerOrContactID = consumer.Fleet_Manager__c;
                    consumerRegistration.Name = consumer.Contact_Name__c;
                    consumerRegistration.Email = consumer.Email__c;
                }
                consumerRegistration.ApplyDate = consumer.CreatedDate.date();
                consumerRegistration.ConfirmeDate = consumer.ConfirmeDate__c;
                consumerRegistration.Status = consumer.Consumer_Status__c;
                Date today = Date.today();
                Date dueDate = consumer.CreatedDate.date().addDays(30);
                consumerRegistration.DueDate = dueDate;
                consumerRegistration.isEdit = 'Waiting Customer Approval'.equals(consumer.Consumer_Status__c) && today < dueDate;
                allConsumersRegistration.add(consumerRegistration);
            }
        }

        // user registration from website
        // String websiteUserRegistrationSql = 'SELECT Consumer_Company_Formula__c, Fleet_Manager__c, RecordType.Name, Company__c, Name, PersonEmail, CreatedDate, ConfirmeDate__c, Consumer_Status__c, Contact_Name__c, Email__c FROM Account WHERE DealerView__c = true';
        // if (String.isNotBlank(companyName)){
        //     companyName = '%' + companyName + '%';
        //     websiteUserRegistrationSql += ' And Consumer_Company_Formula__c like \'' + companyName + '\'';
        // }
        // if (String.isNotBlank(name) ){
        //     name = '%' + name + '%';
        //     websiteUserRegistrationSql += ' And Name like \'' + name + '\'';
        // }
        // if (String.isNotBlank(email)){
        //     email = '%' + email + '%';
        //     websiteUserRegistrationSql += ' And Consumer_Email_Formula__c like \'' + email + '\'';
        // }
        // websiteUserRegistrationSql += ' ORDER BY CreatedDate DESC LIMIT 4000';
        // List<Account> consumerList = Database.query(websiteUserRegistrationSql);
        // for(Account consumer : consumerList) {
        //     UserHistoryClass consumerRegistration = new UserHistoryClass();
        //     consumerRegistration.Id = consumer.Id;
        //     consumerRegistration.UserType = 'Residential Consumer'.equals(consumer.RecordType.Name) ? 'Residential' : 'Commercial';
        //     consumerRegistration.Company = consumer.Consumer_Company_Formula__c;
        //     if (consumerRegistration.UserType.equals('Residential')){
        //         consumerRegistration.Name = consumer.Name;
        //         consumerRegistration.ConsumerOrContactID = consumer.Id;
        //         consumerRegistration.Email = consumer.PersonEmail;
        //     } else{
        //         consumerRegistration.ConsumerOrContactID = consumer.Fleet_Manager__c;
        //         consumerRegistration.Name = consumer.Contact_Name__c;
        //         consumerRegistration.Email = consumer.Email__c;
        //     }
        //     consumerRegistration.ApplyDate = consumer.CreatedDate.date();
        //     consumerRegistration.ConfirmeDate = consumer.ConfirmeDate__c;
        //     consumerRegistration.Status = consumer.Consumer_Status__c;
        //     Date today = Date.today();
        //     Date dueDate = consumer.CreatedDate.date().addDays(30);
        //     consumerRegistration.DueDate = dueDate;
        //     consumerRegistration.isEdit = 'Waiting Customer Approval'.equals(consumer.Consumer_Status__c) && today < dueDate;
        //     allConsumersRegistration.add(consumerRegistration);
        // }

        if (allConsumersRegistration.size() > 0){
            Integer totalSize = allConsumersRegistration.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                UserHistoryClass userHistoryClass = new UserHistoryClass();
                userHistoryClass.Id = allConsumersRegistration[i].Id;
                userHistoryClass.UserType = allConsumersRegistration[i].UserType;
                userHistoryClass.Company = allConsumersRegistration[i].Company;
                userHistoryClass.ConsumerOrContactID = allConsumersRegistration[i].ConsumerOrContactID;
                userHistoryClass.Name = allConsumersRegistration[i].Name;
                userHistoryClass.Email = allConsumersRegistration[i].Email;
                userHistoryClass.ApplyDate = allConsumersRegistration[i].ApplyDate;
                userHistoryClass.ConfirmeDate = allConsumersRegistration[i].ConfirmeDate;
                userHistoryClass.Status = allConsumersRegistration[i].Status;
                userHistoryClass.DueDate = allConsumersRegistration[i].DueDate;
                userHistoryClass.isEdit = allConsumersRegistration[i].isEdit;
                userHistoryClassList.add(userHistoryClass);
            }
        }

        Map<String, Object> userHistoryrMap = new Map<String, Object>();
        userHistoryrMap.put('UserHistory', userHistoryClassList);
        userHistoryrMap.put('TotalSize', allConsumersRegistration.size());

        return JSON.serialize(userHistoryrMap);
    }
    //add Yanko receipt URL
    @AuraEnabled
    public static String getAWSSignedURL(String url){
        String signedUrl = '';
        if (url != null){
            String[] fileName = url.split('/');
            system.debug(fileName);
            signedUrl = CCM_WarrantyDetailReceiptPageCtl.getAWSSignedURL(fileName[3], 'files-to-pim-********', '********************', 'mTngUk97N7E9xrrsU0+DGdhBqjBjMI8fvA9e1g4n', 'us-east-1');
        }
        return signedUrl;
    }
    //Yanko add new getUserList logic for CRM
    @AuraEnabled
    public static String allUserHistoryCRM(Integer pageNumber, Integer allPageSize, String companyName, String name, String email){
        List<UserHistoryClass> userHistoryClassList = new List<UserHistoryClass>();
        // Date threeMonthsAgo = Date.today().addMonths(-3);
        List<Account> accountList = new List<Account>();
        User currentUser = [SELECT Profile.Name
                            FROM User 
                            WHERE Id = :UserInfo.getUserId()
                            LIMIT 1];
        String sql = 'select Id, RecordType.Name, Company__c, Consumer_Company_Formula__c,Name, PersonEmail, CreatedDate, ConfirmeDate__c, Consumer_Status__c, Contact_Name__c, Email__c FROM Account ';
        if (currentUser.Profile.Name.Contains('NJ ')){
            sql += ' WHERE RecordType.Name = \'Commercial Consumer\' AND Consumer_Status__c <> \'Inactive\' ';
            // accountList = [SELECT Id, RecordType.Name, Company__c, Name, PersonEmail, CreatedDate, ConfirmeDate__c, Consumer_Status__c, Contact_Name__c, Email__c
            //                FROM Account
            //                WHERE RecordType.Name = 'Commercial Consumer' AND Consumer_Status__c <> 'Inactive' AND LastModifiedDate >= :threeMonthsAgo
            //                ORDER BY CreatedDate DESC
            //                LIMIT 5000];
        } else{
            // accountList = [SELECT Id, RecordType.Name, Company__c, Name, PersonEmail, CreatedDate, ConfirmeDate__c, Consumer_Status__c, Contact_Name__c, Email__c
            //                FROM Account
            //                WHERE RecordType.Name in ('Residential Consumer', 'Commercial Consumer') AND Consumer_Status__c <> 'Inactive' AND LastModifiedDate >= :threeMonthsAgo
            //                ORDER BY CreatedDate DESC
            //                LIMIT 5000];
            sql += ' WHERE RecordType.Name in (\'Residential Consumer\', \'Commercial Consumer\') AND Consumer_Status__c <> \'Inactive\' ';
        }
        //条件查询
        if (String.isNotBlank(companyName)){
            companyName = '%' + companyName + '%';
            sql += ' And Consumer_Company_Formula__c like \'' + companyName + '\'';
        }
        if (String.isNotBlank(name) ){
            name = '%' + name + '%';
            sql += ' And Name like \'' + name + '\'';
        }
        if (String.isNotBlank(email)){
            email = '%' + email + '%';
            sql += ' And Consumer_Email_Formula__c  like \'' + email + '\'';
        }
        sql += ' ORDER BY CreatedDate DESC  LIMIT 5000';
        System.debug('sql:' + sql);
        accountList = Database.query(sql);
        if (accountList.size() > 0){
            Integer totalSize = accountList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                UserHistoryClass userHistoryClass = new UserHistoryClass();
                userHistoryClass.Id = accountList[i].Id;
                userHistoryClass.UserType = 'Residential Consumer'.equals(accountList[i].RecordType.Name) ? 'Residential' : 'Commercial';
                userHistoryClass.Company = accountList[i].Consumer_Company_Formula__c;
                if (userHistoryClass.UserType.equals('Residential')){
                    userHistoryClass.Name = accountList[i].Name;
                    userHistoryClass.ConsumerOrContactID = accountList[i].Id;
                    userHistoryClass.Email = accountList[i].PersonEmail;
                } else{
                    List<Contact> contactList = [SELECT Id
                                                 FROM Contact
                                                 WHERE AccountId = :accountList[i].Id AND Role__c = 'Fleet Manager'];
                    if (contactList.size() > 0){
                        userHistoryClass.ConsumerOrContactID = contactList[0].Id;
                    }
                    userHistoryClass.Name = accountList[i].Contact_Name__c;
                    userHistoryClass.Email = accountList[i].Email__c;
                }
                userHistoryClass.ApplyDate = accountList[i].CreatedDate.date();
                userHistoryClass.ConfirmeDate = accountList[i].ConfirmeDate__c;
                userHistoryClass.Status = accountList[i].Consumer_Status__c;
                Date today = Date.today();
                Date dueDate = accountList[i].CreatedDate.date().addDays(30);
                userHistoryClass.DueDate = dueDate;
                userHistoryClass.isEdit = 'Waiting Customer Approval'.equals(accountList[i].Consumer_Status__c) && today < dueDate;
                //新增
                userHistoryClass.NameId = accountList[i].Id;
                userHistoryClass.CompanyId = accountList[i].Id;
                userHistoryClassList.add(userHistoryClass);
            }
        }

        Map<String, Object> userHistoryrMap = new Map<String, Object>();
        userHistoryrMap.put('UserHistory', userHistoryClassList);
        userHistoryrMap.put('TotalSize', accountList.size());

        return JSON.serialize(userHistoryrMap);
    }
    //Yanko add find currentUser Profile Name for CRM
    @AuraEnabled
    public static String getUserProfileName(){
        Id userId = UserInfo.getUserId();
        User currentUser = [SELECT Profile.Name
                            FROM User 
                            WHERE Id = :userId
                            LIMIT 1];
        return currentUser.Profile.Name;
    }
    @AuraEnabled
    public static string deleteUserHistory(Id RecordId){
        Map<String, Object> result = new Map<String, Object>();
        try{
            Account account = [SELECT Id, Consumer_Status__c
                               FROM Account
                               WHERE Id = :RecordId];
            // account.Consumer_Status__c = 'Inactive';
            delete account;
            result.put('result', 'Success');
        } catch (Exception e){
            result.put('result', 'Error');
            result.put('ErrorMsg', e.getMessage());
        }
        return JSON.serialize(result);
    }
    // yanko add to judge whether there are duplicate email in the salesforce when edit userhistory 2023/10/02
    @AuraEnabled
    public static Boolean CheckDuplicateEmail(String emailAddress){
        list<Account> accList = [SELECT Id, PersonEmail
                                 FROM Account
                                 WHERE PersonEmail = 'emailAddress'];
        if (accList.size() > 0){
            return true;
        } else{
            return false;
        }
    }
    @AuraEnabled
    public static string updateUserHistory(Id RecordId, String Email){
        System.debug('RecordId ===== ' + RecordId);
        System.debug('Email ===== ' + Email);
        Map<String, Object> result = new Map<String, Object>();
        List<Contact> contactList = [SELECT Id, Account.RecordType.Name, Account.Consumer_Status__c, Account.CreatedDate, Email, Role__c
                                     FROM Contact
                                     WHERE AccountId = :RecordId AND Role__c = 'Fleet Manager'];
        if (contactList != null && contactList.size() > 0){
            Contact contact = contactList.get(0);
            System.debug('contact ===== ' + contact);
            Date duedate = contact.Account.CreatedDate.date().addDays(30);
            Date today = Date.today();
            if (!contact.Account.Consumer_Status__c.equals('Waiting Customer Approval')){
                result.put('result', 'False');
                result.put('ErrorMsg', 'Status is not Pending can not change');
            } else if (today > duedate){
                result.put('result', 'False');
                result.put('ErrorMsg', 'Cannot modify Email if current time is greater than 30 days due date');
            } else{
                contact.Email = Email;
                update contact;
                Account account = [SELECT Id, Email__c, RecordType.Name
                                   FROM Account 
                                   where Id = :RecordId];
                if ('Residential Consumer'.equals(account.RecordType.Name)){
                    account.PersonEmail = Email;
                } else{
                    account.Email__c = Email;
                }
                update account;
                result.put('result', 'Success');
            }
        }

        return JSON.serialize(result);
    }
    @AuraEnabled
    public static String getUserHistoryById(Id RecordId){
        UserHistoryClass userHistoryClass = new UserHistoryClass();
        Account account = [SELECT Id, RecordType.Name, Consumer_Status__c, Company__c, Consumer_Company_Formula__c, Address_Detail__c, City__c, Country__c, Business_Area__c, Street__c, State__c, Postal_Code__c, CreatedDate, PersonEmail, Phone, FirstName, LastName, Name, ShippingCity, ShippingCountry, ShippingStreet, ShippingState, ShippingPostalCode
                           FROM Account
                           WHERE Id = :RecordId];
        System.debug('DataAccount ===== ' + account);
        userHistoryClass.Id = account.Id;
        userHistoryClass.UserType = 'Residential Consumer'.equals(account.RecordType.Name) ? 'Residential' : 'Commercial';
        if ('Commercial Consumer'.equals(account.RecordType.Name)){
            List<Contact> contactList = [SELECT Id, Email, Name, FirstName, LastName, Phone
                                         FROM Contact
                                         WHERE AccountId = :RecordId AND Role__c = 'Fleet Manager'
                                         limit 1];
            CompanyInformation companyInformation = new CompanyInformation();
            // companyInformation.Company = account.Company__c;
            // companyInformation.Address = account.Address_Detail__c;
            // companyInformation.City = account.City__c;
            // companyInformation.Country = account.Country__c;
            // companyInformation.Status = account.Consumer_Status__c;
            // companyInformation.BusinessArea = account.Business_Area__c;
            // companyInformation.StreetNo = account.Street__c;
            // companyInformation.State = account.State__c;
            // companyInformation.Postcode = account.Postal_Code__c;
            // companyInformation.DueDate = account.CreatedDate.date().addDays(30);
            companyInformation.Company = account.Company__c;
            companyInformation.Address = account.Address_Detail__c;
            companyInformation.City = account.ShippingCity;
            companyInformation.Country = account.ShippingCountry;
            companyInformation.Status = account.Consumer_Status__c;
            companyInformation.BusinessArea = account.Business_Area__c;
            companyInformation.StreetNo = account.ShippingStreet;
            companyInformation.State = account.ShippingState;
            companyInformation.Postcode = account.ShippingPostalCode;
            companyInformation.DueDate = account.CreatedDate.date().addDays(30);
            userHistoryClass.CompanyInformation = companyInformation;
            if (contactList != null && !contactList.isEmpty()){
                Contact firstContact = contactList[0];
                companyInformation.FleetManager = firstContact.Name;
                companyInformation.FleetManagerEmail = firstContact.Email;

                if (account.Consumer_Status__c.equals('Waiting Customer Approval')){
                    PersonalInformation personalInformation = new PersonalInformation();
                    personalInformation.EmailAddress = firstContact.Email;
                    personalInformation.Phone = firstContact.Phone;
                    personalInformation.FirstName = firstContact.FirstName;
                    personalInformation.LastName = firstContact.LastName;
                    userHistoryClass.PersonalInformation = personalInformation;
                } else{
                    userHistoryClass.ProductInformationList = getProductInformationById(RecordId);
                }
            } else{
                userHistoryClass.ProductInformationList = getProductInformationById(RecordId);
            }
        } else{
            OwnerInformation ownerInformation = new OwnerInformation();
            ownerInformation.Address = account.Address_Detail__c;
            // ownerInformation.City = account.City__c;
            // ownerInformation.Country = account.Country__c;
            // ownerInformation.Status = account.Consumer_Status__c;
            // ownerInformation.StreetNo = account.Street__c;
            // ownerInformation.State = account.State__c;
            // ownerInformation.Postcode = account.Postal_Code__c;
            ownerInformation.City = account.ShippingCity;
            ownerInformation.Country = account.ShippingCountry;
            ownerInformation.Status = account.Consumer_Status__c;
            ownerInformation.StreetNo = account.ShippingStreet;
            ownerInformation.State = account.ShippingState;
            ownerInformation.Postcode = account.ShippingPostalCode;
            ownerInformation.DueDate = account.CreatedDate.date().addDays(30);
            ownerInformation.EmailAddress = account.PersonEmail;
            ownerInformation.Phone = account.Phone;
            ownerInformation.FirstName = account.FirstName;
            ownerInformation.LastName = account.LastName;
            userHistoryClass.ownerInformation = ownerInformation;
            userHistoryClass.ProductInformationList = getProductInformationById(RecordId);

        }
        Map<String, Object> userHistoryrMap = new Map<String, Object>();
        userHistoryrMap.put('UserHistory', userHistoryClass);
        userHistoryrMap.put('result', 'success');
        System.debug('userHistoryrMap ===== ' + userHistoryrMap);

        return JSON.serialize(userHistoryrMap);
    }
    public static List<ProductInformation> getProductInformationById(Id RecordId){
        List<ProductInformation> productInformationList = new List<ProductInformation>();
        List<String> warrantyItemIdList = new List<String>();
        List<Warranty_Item__c> warrantyList = [SELECT Id, Product__c, Product__r.Order_Model__c, Receipt_Link__c, Receipt_Name__c, Receipt_Status__c, Purchase_Date__c, Brand__c, Purchase_Place__c, Master_Product__c, Master_Product__r.Order_Model__C, Serial_Number__c, Warranty_Status__c, Expiration_Date_New__c
                                               FROM Warranty_Item__c
                                               WHERE Consumer__c = :RecordId
                                               ORDER BY CreatedDate DESC];
        for (Warranty_Item__c item : warrantyList){
            ProductInformation productInformation = new ProductInformation();
            productInformation.Id = item.Id;
            productInformation.PurchaseDate = item.Purchase_Date__c;
            productInformation.Brand = item.Brand__c;
            productInformation.PurchasePlace = item.Purchase_Place__c;
            productInformation.WarrantyStatus = item.Warranty_Status__c;
            productInformation.ExpirationDate = item.Expiration_Date_New__c;
            productInformation.receiptStatus = item.Receipt_Status__c;
            productInformation.receipt = item.Receipt_Link__c;
            productInformation.receiptName = item.Receipt_Name__c;
            productInformation.SerialNumber = item.Serial_Number__c;
            productInformation.MasterProduct = item.Master_Product__r.Order_Model__C;
            productInformation.ModelNumber = item.Product__r.Order_Model__C;
            productInformation.MasterProductId = item.Master_Product__c;
            productInformation.PartId = item.Product__c;
            productInformationList.add(productInformation);
            warrantyItemIdList.add(item.Id);
        }
        // if (warrantyList != null && warrantyList.size() > 0){
        //     //save receipt
        //     Map<String, ContentDocumentLink> receiptMap = new Map<String, ContentDocumentLink>();
        //     //get receipt
        //     for (ContentDocumentLink linkItem : [SELECT LinkedEntityId, ContentDocumentId, ContentDocument.LatestPublishedVersion.PathOnClient, ContentDocument.LatestPublishedVersion.VersionNumber
        //                                          FROM ContentDocumentLink
        //                                          WHERE LinkedEntityId in:warrantyItemIdList]){
        //         if (receiptMap.containsKey(linkItem.LinkedEntityId)){
        //             if (Decimal.valueOf(linkItem.ContentDocument.LatestPublishedVersion.VersionNumber) > Decimal.valueOf(receiptMap.get(linkItem.LinkedEntityId).ContentDocument.LatestPublishedVersion.VersionNumber)){
        //                 receiptMap.put(linkItem.LinkedEntityId, linkItem);
        //             }
        //         } else{
        //             receiptMap.put(linkItem.LinkedEntityId, linkItem);

        //         }
        //     }
        //     List<ProductInformation> productInformationReturnList = new List<ProductInformation>();

        //     for (ProductInformation item : productInformationList){
        //         if (receiptMap.containsKey(item.Id)){
        //             item.Receipt = receiptMap.get(item.Id).ContentDocument.LatestPublishedVersion.PathOnClient;
        //             item.ReceiptDocumentID = receiptMap.get(item.Id).ContentDocumentId;
        //         }
        //         productInformationReturnList.add(item);
        //     }
        //     return productInformationReturnList;

        // } else{
        //     return productInformationList;

        // }
        return productInformationList;
    }
    @AuraEnabled
    public static String allRegistrationHistory(String userId, Boolean isPortal, Integer pageNumber, Integer allPageSize, String companyName, String name, String model, String sn){
        Map<String, Object> userHistoryrMap = new Map<String, Object>();
        String accId = '';
        try{
            List<RegistrationHistoryClass> registrationHistoryList = new List<RegistrationHistoryClass>();
            List<Warranty_Item__c> warrantyList = new List<Warranty_Item__c>();
            String sql = 'SELECT Id, Name, Consumer__r.RecordType.Name, Consumer__r.Consumer_Email_Formula__c, Consumer__r.Consumer_Company_Formula__c, Master_Product__c,Product__c, Receipt_Link__c, Receipt_Name__c, Receipt_Status__c, Consumer__r.Company__c, Consumer__r.Name,Product__r.Order_Model__C, Master_Product__r.Order_Model__C, Serial_Number__c, Warranty_Status__c, Expiration_Date_New__c, CreatedDate' + 
                     ' FROM Warranty_Item__c where Id <> null';
            if (isPortal){
                User user = [select id, Contact.AccountId, Profile.Name
                             from user 
                             where Id = :UserInfo.getUserId()];
                accId = user.Contact.AccountId;
                String salesChannel = 'Dealer';
                String country = '';
                for(Account acc : [SELECT Sales_Channel__c, Country__c FROM Account WHERE Id = :accId]) {
                    if(String.isNotBlank(acc.Sales_Channel__c) && acc.Sales_Channel__c.containsIgnoreCase('Distributor')) {
                        salesChannel = 'Distributor';
                    }
                    country = acc.Country__c;
                }

                if(salesChannel == 'Dealer') {
                    //获取相关的consumer
                    Set<String> consumerIds = new Set<String>();
                    consumerIds.add(accId);
                    List<Dealer_Share__c> desList = [SELECT Id, RelatedConsumer__c
                                                    FROM Dealer_Share__c
                                                    WHERE RelatedConsumer__r.Consumer_Status__c <> 'Inactive' AND RelatedConsumer__r.RecordType.Name in ('Residential Consumer', 'Commercial Consumer') And RelatedDealer__c = :accId];
                    for (Dealer_Share__c shareItem : desList){
                        consumerIds.add(shareItem.RelatedConsumer__c);
                    }
                    sql += ' AND Consumer__c in :consumerIds';
                }
                else {
                    if(String.isNotBlank(country)) {
                        sql += ' AND Consumer__r.ShippingCountry = :country';
                    }
                }
            }
            //条件查询
            if (String.isNotBlank(companyName)){
                companyName = '%' + companyName + '%';
                sql += ' And Consumer__r.Name like \'' + companyName + '\'';
            }
            if (String.isNotBlank(name) ){
                name = '%' + name + '%';
                sql += ' And  Name like \'' + name + '\'';
            }
            if (String.isNotBlank(model)){
                model = '%' + model + '%';
                sql += ' And Product__r.Order_Model__C  like \'' + model + '\'';
            }
            if (String.isNotBlank(sn)){
                sn = '%' + sn + '%';
                sql += ' And Serial_Number__c  like \'' + sn + '\'';
            }
            sql += ' ORDER BY CreatedDate DESC LIMIT 5000';
            System.debug('sql:' + sql);
            warrantyList = Database.query(sql);
            Integer totalSize = warrantyList.size();
            System.debug('*** warrantyList.size(): ' + warrantyList.size());
            System.debug('*** totalSize: ' + totalSize);
            if (totalSize > 0){
                System.debug('*** run totalSize: ');
                Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
                Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
                System.debug('initPageNumber:' + initPageNumber + 'pageLength:' + pageLength);
                for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                    RegistrationHistoryClass registrationHistory = new RegistrationHistoryClass();
                    registrationHistory.Id = warrantyList[i].Id;
                    registrationHistory.receipt = warrantyList[i].Receipt_Link__c;
                    registrationHistory.receiptName = warrantyList[i].Receipt_Name__c;
                    registrationHistory.receiptStatus = warrantyList[i].Receipt_Status__c;
                    registrationHistory.UserType = 'Residential Consumer'.equals(warrantyList[i].Consumer__r.RecordType.Name) ? 'Residential' : 'Commercial';
                    registrationHistory.Company = 'Residential Consumer'.equals(warrantyList[i].Consumer__r.RecordType.Name) ? warrantyList[i].Consumer__r.Name : warrantyList[i].Consumer__r.Company__c;
                    registrationHistory.Name = warrantyList[i].Name;
                    registrationHistory.MasterProduct = warrantyList[i].Master_Product__r.Order_Model__C;
                    registrationHistory.ModelNumber = warrantyList[i].Product__r.Order_Model__C;
                    registrationHistory.SerialNumber = warrantyList[i].Serial_Number__c;
                    registrationHistory.WarrantyStatus = warrantyList[i].Warranty_Status__c;
                    registrationHistory.ExpirationDate = warrantyList[i].Expiration_Date_New__c;
                    registrationHistory.CreatedDate = warrantyList[i].CreatedDate.date();
                    registrationHistory.NameId = warrantyList[i].Id;
                    registrationHistory.CompanyId = warrantyList[i].Consumer__c;
                    registrationHistory.MasterProductId = warrantyList[i].Master_Product__c;
                    registrationHistory.PartId = warrantyList[i].Product__c;
                    registrationHistory.Email = warrantyList[i].Consumer__r.Consumer_Email_Formula__c;
                    registrationHistoryList.add(registrationHistory);
                }
                System.debug('*** registrationHistoryList: ' + registrationHistoryList);
            }
            userHistoryrMap.put('RegistrationHistory', registrationHistoryList);
            userHistoryrMap.put('TotalSize', warrantyList.size());
            userHistoryrMap.put('isSuccess', 'True');

        } catch (Exception objE){
            System.debug(LoggingLevel.INFO, '*** objE.getMessage(): ' + objE.getMessage());
            System.debug('*** objE.getLineNumber(): ' + objE.getLineNumber());
            userHistoryrMap.put('isSuccess', 'FALSE');
            userHistoryrMap.put('message', objE.getLineNumber() + '=>' + objE.getMessage());

        }
        userHistoryrMap.put('accid', accId);
        System.debug('*** userHistoryrMap: ' + userHistoryrMap);
        return JSON.serialize(userHistoryrMap);
    }
    @AuraEnabled
    public static String allOrder(String userId, Integer pageNumber, Integer allPageSize){
        String accId = CCM_PortalPageUtil.getCustomerByUser(userId);

        List<Order> orderList = [SELECT Id, OrderNumber, PO_No__c, Submit_Date__c, Status, TotalAmount
                                 FROM Order
                                 WHERE AccountId = :accId
                                 ORDER BY CreatedDate DESC];

        //Integer totalPage = orderList.size()/allPageSize==0?(orderList.size()/allPageSize):(orderList.size()/allPageSize+1);
        Integer totalPage = orderList.size();

        List<Order> orderListInOnePage = new List<Order>();

        for (Integer i = (pageNumber - 1) * allPageSize; i <= (pageNumber - 1) * allPageSize + allPageSize - 1; i++){
            if (orderList.size() > i){
                orderListInOnePage.add(orderList[i]);
            }
        }


        Map<String, Object> orderMap = new Map<String, Object>();
        orderMap.put('Order', orderListInOnePage);
        orderMap.put('TotalPageNumber', totalPage);

        return JSON.serialize(orderMap);
    }
    @AuraEnabled
    public static string getUserInfo(){
        return CCM_Community_HeaderCtl.getUserInfo();
    }
    // add haibo
    @AuraEnabled
    public static Boolean IsPortal(){
        return WarrantyClaimUtil.IsPortal();
    }
    // add haibo
    /**list查询 */
    @AuraEnabled
    public static String queryClaimList(String accountId, Integer pageNumber, Integer allPageSize, String modelNumber, String status, String invoiceStatus, String serialNumber, String name, String companyName, String claimReferenceNumber){
        return WarrantyClaimSaveHandler.queryClaimList(accountId, pageNumber, allPageSize, modelNumber, status, invoiceStatus, serialNumber, name, companyName, claimReferenceNumber);
    }
    // add haibo
    /**删除数据 */
    @AuraEnabled
    public static String deleteClaim(String claimId){
        return WarrantyClaimSaveHandler.deleteClaim(claimId);
    }
    // add haibo
    /**获取customer信息:判断是 dealer还是distributor,获取labor Price */
    @AuraEnabled
    public static String getCustomerInformation(String accId){
        return WarrantyClaimUtil.getCustomerInformation(accId);
    }
    // add haibo
    /** 获取claim information,claim payment information*/
    @AuraEnabled
    public static String queryClaimInfoMation(String accountId){
        return WarrantyClaimSaveHandler.queryClaimInfoMation(accountId);
    }
    public class ContentInfo{
        public Date createdate;
        public String name;
        public String Id;
    }
    public class UserHistoryClass{
        public Id Id;
        public String UserType;
        public String Company;
        public String Name;
        public String Email;
        public Date ApplyDate;
        public Date ConfirmeDate;
        public Date DueDate;
        public String Status;
        public Boolean isEdit;
        public String ConsumerOrContactID;
        public CompanyInformation CompanyInformation;
        public PersonalInformation PersonalInformation;
        public OwnerInformation OwnerInformation;
        public List<ProductInformation> ProductInformationList;
        //新增
        public String NameId;
        public String CompanyId;
    }
    public class CompanyInformation{
        public String Company;
        public String Address;
        public String City;
        public String Country;
        public String Status;
        public String BusinessArea;
        public String StreetNo;
        public String State;
        public String Postcode;
        public Date DueDate;
        public String FleetManager;
        public String FleetManagerEmail;
    }
    public class PersonalInformation{
        public String EmailAddress;
        public String Phone;
        public String FirstName;
        public String LastName;
    }
    public class OwnerInformation{
        public String EmailAddress;
        public String Phone;
        public String FirstName;
        public String LastName;
        public String Address;
        public String City;
        public String Country;
        public String Status;
        public String StreetNo;
        public String State;
        public String Postcode;
        public Date DueDate;
    }
    public class ProductInformation{
        public String Id;
        public String Brand;
        public Date PurchaseDate;
        public String PurchasePlace;
        public String MasterProduct;
        public String ModelNumber;
        public String SerialNumber;
        public String receipt;
        public String receiptName;
        public String receiptStatus;
        public String ReceiptDocumentID;
        public String WarrantyStatus;
        public Date ExpirationDate;
        //对应MasterProduct
        public String MasterProductId;
        //对应partId
        public String PartId;
    }
    public class RegistrationHistoryClass{
        public Id Id;
        public String UserType;
        public String Company;
        public String Name;
        public String MasterProduct;
        public String ModelNumber;
        public String SerialNumber;
        public String WarrantyStatus;
        public Date ExpirationDate;
        public Date CreatedDate;
        public String receipt;
        public String receiptName;
        public String receiptStatus;
        public String NameId;
        public String CompanyId;
        //对应MasterProduct
        public String MasterProductId;
        //对应partId
        public String PartId;
        public String Email;
    }
}