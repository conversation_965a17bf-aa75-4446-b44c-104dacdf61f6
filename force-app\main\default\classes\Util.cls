public without sharing class Util {

    /**
     * isNullOrBlank
     * @description
     * @param integer, decimal, string, date, datetime, time, blob, double, id, long
     * @return boolean true if input null or blank
     */
    public static boolean isNullOrBlank (integer input) {return (input==null)?true:false;}
    public static boolean isNullOrBlank (decimal input) {return (input==null)?true:false;}
    public static boolean isNullOrBlank (string input)  {return (input==null || input.trim().length()==0)?true:false;}
    public static boolean isNullOrBlank (date input)    {return (input==null)?true:false;}
    public static boolean isNullOrBlank (datetime input){return (input==null)?true:false;}
    public static boolean isNullOrBlank (time input)    {return (input==null)?true:false;}
    public static boolean isNullOrBlank (blob input)    {return (input==null || input.size()==0)?true:false;}
    public static boolean isNullOrBlank (double input)  {return (input==null)?true:false;}
    public static boolean isNullOrBlank (id input)      {return (input==null)?true:false;}
    public static boolean isNullOrBlank (long input)    {return (input==null)?true:false;}
    public static boolean isNullOrBlank (boolean input) {return (input==null)?true:false;}

    /**
     * fixNull
     * @description
     * @param integer, decimal, string, date, datetime, time, blob, double, id, long
     * @return integer, decimal, double, long inputs return 0
     * @return string input returns '' (empty string)
     * @return date returns system.today()
     * @return datetime returns system.now()
     * @return id inputs returns null
     * @return time input returns 0:00:00.0
     * @return blob input returns blob.valueof('') (empty string)
     */
    public static integer fixNull (integer input)   {return (isNullOrBlank(input))?0:input;}
    public static decimal fixNull (decimal input)   {return (isNullOrBlank(input))?0:input;}
    public static string fixNull (string input)     {return (isNullOrBlank(input))?'':input;}
    public static date fixNull (date input)         {return (isNullOrBlank(input))?system.today():input;}
    public static datetime fixNull (datetime input) {return (isNullOrBlank(input))?system.now():input;}
    public static time fixNull (time input)         {return (isNullOrBlank(input))?time.newinstance(0,0,0,0):input;}
    public static blob fixNull (blob input)         {return (isNullOrBlank(input))?blob.valueof(''):input;}
    public static double fixNull (double input)     {return (isNullOrBlank(input))?0:input;}
    public static id fixNull (id input)             {return (isNullOrBlank(input))?null:input;} //can't fix a null id
    public static long fixNull (long input)         {return (isNullOrBlank(input))?0:input;}
    public static boolean fixNull (boolean input)   {return (isNullOrBlank(input))?false:input;}

    /**
     * validateEmail
     * @description
     * @param string
     * @return true if valid email, false if not
     */
   // Fetch the current user information
    public static User getUserInfo(String userId){
        List<User> usr = [SELECT Id,
                                 Contact.AccountId,
                                 ContactId,
                                 Name,
                                 Phone,
                                 Email,
                                 Profile.Name,
                                 UserRoleId,
                                 UserRole.DeveloperName,
                                 SmallPhotoUrl,
                                 FullPhotoUrl,
                                 LanguageLocaleKey FROM User WHERE Id = :userId];
        if (usr.size() > 0 && usr != null){
            return usr[0];
        }
        return null;
    }
    
    public static List<SelectItem> getSelectOptions(sObject objObject, String fld) {
        List<SelectItem> allOpts = new List<SelectItem>();
        Schema.SObjectType objType = objObject.getSObjectType();
        Schema.DescribeSObjectResult objDescribe = objType.getDescribe();
        Map<String,Schema.SObjectField > fieldMap = objDescribe.fields.getMap();
        List<Schema.PicklistEntry> entries =
        fieldMap.get(fld).getDescribe().getPickListValues();

        for (Schema.PicklistEntry entry : entries) {
            SelectItem item = new SelectItem();
            item.value = entry.getValue();
            item.label = entry.getLabel();
            allOpts.add(item);
        }
        system.debug('allOpts ---->' + allOpts);
        return allOpts;
    }

    public static Map<String, SelectItem> getSelectOptMap(sObject objObject, String fld) {
       Map<String, SelectItem> allOptMap = new Map<String, SelectItem>();
       List<SelectItem> allOpts = new List<SelectItem>();
       Schema.SObjectType objType = objObject.getSObjectType();
       Schema.DescribeSObjectResult objDescribe = objType.getDescribe();
       Map<String,Schema.SObjectField > fieldMap = objDescribe.fields.getMap();
       List<Schema.PicklistEntry> entries =
       fieldMap.get(fld).getDescribe().getPickListValues();

       for (Schema.PicklistEntry entry : entries) {
           SelectItem item = new SelectItem();
           item.value = entry.getValue();
           item.label = entry.getLabel();
           allOptMap.put(item.value, item);
       }
       system.debug('allOptMap ---->' + allOptMap);
       return allOptMap;
    }

    public static Map<String, String> getPickListMap(sObject objObject, String fld) {
        Map<String, String> pickListMap = new Map<String, String>();
        Schema.SObjectType objType = objObject.getSObjectType();
        Schema.DescribeSObjectResult objDescribe = objType.getDescribe();
        Map<String,Schema.SObjectField > fieldMap = objDescribe.fields.getMap();
        List<Schema.PicklistEntry> entries =
        fieldMap.get(fld).getDescribe().getPickListValues();

        for (Schema.PicklistEntry entry : entries) {
            pickListMap.put(entry.getLabel(), entry.getValue());
        }
        return pickListMap;
    }

    public class SelectItem {
        @AuraEnabled public String label {get; set;}
        @AuraEnabled public String value {get; set;}
    }
    public static String findObjectNameFromRecordIdPrefix(String recordIdOrPrefix){
        String objectName = '';
        try{
            //Get prefix from record ID
            //This assumes that you have passed at least 3 characters
            String myIdPrefix = String.valueOf(recordIdOrPrefix).substring(0,3);
             
            //Get schema information
            Map<String, Schema.SObjectType> gd =  Schema.getGlobalDescribe(); 
             
            //Loop through all the sObject types returned by Schema
            for(Schema.SObjectType stype : gd.values()){
                Schema.DescribeSObjectResult r = stype.getDescribe();
                String prefix = r.getKeyPrefix();
                 
                //Check if the prefix matches with requested prefix
                if(prefix!=null && prefix.equals(myIdPrefix)){
                    objectName = r.getName();
                    System.debug('Object Name! ' + objectName);
                    break;
                }
            }
        }catch(Exception e){
            System.debug(e);
        }
        return objectName;
    }

//    //根据当前用户选的地址信息
    public static Address_With_Program__c getAddressInfo(String addressId){
        Address_With_Program__c address = new Address_With_Program__c();
        if(String.isNotBlank(addressId)){
            List<Address_With_Program__c> addressInfos = [
                SELECT Id, Account_Address__c, 
                       Account_Address__r.Address1__c,
                       Account_Address__r.Address2__c,
                       Account_Address__r.State__c,
                       Account_Address__r.Country__c,
                       Account_Address__r.City__c,
                       Account_Address__r.Postal_Code__c,
                       Account_Address__r.Contact__r.Name
                FROM Address_With_Program__c
                WHERE Id = :addressId];
            address = addressInfos[0];
        }
        return address;
    }

    //根据RecordId 获取对象的Object Name和RecordType
    public static ObjectInfo getObjectInfo(String recordId){
        ObjectInfo objInfo = new ObjectInfo();
        if (String.isNotBlank(recordId)){
            objInfo.objectName = findObjectNameFromRecordIdPrefix(recordId);
            String recordType = '';
            if (objInfo.objectName == 'Purchase_Order__c'){
                Purchase_Order__c po = [SELECT Id, RecordType.DeveloperName FROM Purchase_Order__c WHERE Id =: recordId];
                recordType = po.RecordType.DeveloperName;
            }else if (objInfo.objectName == 'Order'){
                Order order = [SELECT Id, RecordType.DeveloperName FROM Order WHERE Id =: recordId];
                recordType = order.RecordType.DeveloperName;
            }
            objInfo.isPlaceOrder = recordType == 'Place_Order' ? true : false;
        }

        return objInfo;
    }

    public class ObjectInfo {
        @AuraEnabled public String objectName {get; set;}
        @AuraEnabled public Boolean isPlaceOrder {get; set;}

        public ObjectInfo(){
            this.isPlaceOrder = true;
        }
    }

    //异常日志记录
    public static String logIntegration(String logName,String apexClassName, String method, 
                                      String errMsg, String reqJsonBody,String resJsonBody){
        Log__c expLog = new Log__c();
        expLog.Name = logName;
        expLog.ApexName__c = apexClassName;
        expLog.Method__c = method;
        expLog.Error_Message__c = errMsg.left(100000);
        expLog.ReqParam__c = reqJsonBody.left(100000);
        expLog.ResParam__c = resJsonBody.left(100000);
        Database.insert(expLog, false);
        if(String.isNotBlank(reqJsonBody)){
            createNotes('JSON Payload:'+expLog.Id,reqJsonBody,expLog.Id);
        }
        return expLog.Id;
    }

    public static String logIntegration(String logName,String apexClassName, String method, 
                                      String errMsg, String reqJsonBody, String resJsonBody, String objType, String recordId){
        Log__c expLog = new Log__c();
        expLog.Name = logName;
        expLog.ApexName__c = apexClassName;
        expLog.Method__c = method;
        expLog.ObjType__c = objType;
        expLog.RecordId__c = recordId;
        expLog.Error_Message__c = errMsg.left(100000);
        expLog.ReqParam__c = reqJsonBody.left(100000);
        expLog.ResParam__c = resJsonBody.left(100000);
        Database.insert(expLog, false);
        return expLog.Id;
    }

//    //异常日志附件
    public static void createNotes(String title, String notes, Id exceptionLogId) {
            notes = String.isEmpty(notes) ? '<br/>' : notes.escapeXML().replace('\r\n', '<br>').replace('\r', '<br>').replace('\n', '<br>').replace('&apos;', '&#39;');
            ContentVersion newContent = new ContentVersion(Title = title,VersionData = Blob.ValueOf(notes),PathOnClient = title+'.snote',FirstPublishLocationId = exceptionLogId);
            system.debug('@@newContent'+newContent);
            Insert newContent;
     
   }

//    //是否当前用户是Inner User
    public static Boolean isInnerUser(){
        String userType = UserInfo.getUserType();
        if(userType == 'Standard'){
            return true;
        }else{
            return false;
        }
    }

    //获取Brands Options （Brand Label）
    public static SelectItem getBrandOption(List<String> brandOpts){
       SelectItem brandScopeItem = new SelectItem();
       if (brandOpts != null && brandOpts.size() > 0){
           Map<String, SelectItem> brandOptMap = getSelectOptMap(new Sales_Program__c(),'Brands__c');
           String finalStrLabel = '';
           String finalStrValue = '';
           for (String str : brandOpts){
               SelectItem item = new SelectItem();
               item = brandOptMap.get(str);
               if (item != null){
                   finalStrLabel = finalStrLabel + ' & ' + item.label;
                   finalStrValue = finalStrValue + '&' + item.value;
               }
           }

           finalStrLabel = finalStrLabel.removeStart(' & ');
           finalStrValue = finalStrValue.removeStart('&');
           brandScopeItem.label = finalStrLabel;
           brandScopeItem.value = finalStrValue;
       }

       return brandScopeItem;
    }

    public static void pushExceptionEmail(String IntegrationName,Id logId,String failResult){
        String sendToLabel = Label.CCM_adminEmail;
        String[] sendToArr = sendToLabel.split(';');
        List<User> userlist = [select id from User where Email in:sendToArr];
        List<Id> sendTolist = new List<Id>();
        for(User u : userlist){
            if(!sendTolist.contains(u.Id)){
                sendTolist.add(u.Id);
            }         
        }
        /*if(!sendTolist.contains(UserInfo.getUserId())){
            sendTolist.add(UserInfo.getUserId());
        } */       
        String mailContent = '';
        String sfUrl = 'https://'+URL.getSalesforceBaseUrl().getHost();
        List<Log__c> loglist = [select id,ResParam__c
                                from Log__c where id =:logId];
        if(loglist.size() > 0){
           mailContent += 'CCM Integration Exception:' + IntegrationName + 'exception.<br/>';
            mailContent += 'Exception Log:' + sfUrl +'/'+ logId + '<br/>';
            mailContent += 'Failure Result:<br/>';
            mailContent += failResult;                         
            mailContent += 'This is an automatically generated notification by salesforce system, please do not reply to this email.<br/>';
            //mailContent += '失败数据: ' + failResult;
        }
        if (!Test.isRunningTest()){
            CCM_ServiceCallout.callsfEmail(sendTolist, new List<Id>(),'CCM Integration Exception' ,mailContent , false);
        }
    }

    //get Payment Term Option for ego
    public static List<PTSelectItem> getPaymentTermOptions(String customerType){
        List<PTSelectItem> ptOptions = new List<PTSelectItem>();
        for(Payment_Term_For_EGO__mdt ptSetting : [SELECT MasterLabel, Description__c,
                                                    Name__c, Payment_Discount__c,
                                                   Payment_Leadtime__c,Criteria_From__c,
                                                   Criteria_To__c,Customer_Type__c
                                             FROM Payment_Term_For_EGO__mdt
                                             WHERE Customer_Type__c =: customerType]) {
            PTSelectItem item = new PTSelectItem();
            item.label = ptSetting.Description__c;
            item.value = ptSetting.Name__c;
            item.criteriaFrom = ptSetting.Criteria_From__c;
            item.criteriaTo = ptSetting.Criteria_To__c;
            ptOptions.add(item);
        }

        return ptOptions;
    }

    public class PTSelectItem {
        @AuraEnabled public String label {get; set;}
        @AuraEnabled public String value {get; set;}
        @AuraEnabled public Decimal criteriaFrom {get; set;}
        @AuraEnabled public Decimal criteriaTo {get; set;}
    }
}