/**
* Cloned new interface for EU Web site
*/
public abstract class CEU_HerokuAPIHandler {
    // 空参构造
    public CEU_HerokuAPIHandler(){}
    // 处理Post请求
    public abstract CEU_HerokuEntity.ResponseEntity handlePost(String requestBody);
    // 处理Get请求
    public abstract CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap);

    // 2022-06-29: 新增记录Log
    private static Log__c logInfo = new Log__c(Name = 'EUHerokuAPI ' + DateTime.now());

    public static final String RECORDTYPEID_COMMERCIAL_ACC = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Commercial_Consumer').getRecordTypeId();
    public static final String RECORDTYPEID_COMMERCIAL_CON = Schema.SObjectType.Contact.getRecordTypeInfosByDeveloperName().get('Commercial_Contact').getRecordTypeId();

    // 创建或更新用户处理类 - 欧洲非德国区域
    public class UpsertUserHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'UpsertUserHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            Account changedUser;
            try{
                CEU_HerokuEntity.RCUserRequestEntity userRequest = (CEU_HerokuEntity.RCUserRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.RCUserRequestEntity.class);
                //if(userRequest.userType =='Residential'){//Residential 类型客户维持原始代码
                    Boolean emailFlag = false;
                    if(String.isNotEmpty(userRequest.customerId)){
                        changedUser = CEU_HerokuAPIUtils.getResidentialUserByID(userRequest.customerId);
                        System.debug('----User:' + changedUser);
                        if (changedUser == null) {
                            // 2022-06-29: 记录log
                            logInfo.Error_Message__c = 'Can not find the account.';
                            insert logInfo;
                            return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                        }
                        if (userRequest.firstName != null) {
                            changedUser.FirstName = userRequest.firstName;
                        }
                        if (userRequest.lastName != null) {
                            changedUser.LastName  = userRequest.lastName;
                        }
                        if (userRequest.emailAddress != null) {
                            changedUser.PersonEmail = userRequest.emailAddress;
                            changedUser.EGO_username__c = userRequest.emailAddress;
                        }
                        if (userRequest.password != null) {
                            changedUser.EGO_password__c = userRequest.password;
                        }

                        if (userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                            changedUser.Site_Origin__pc = userRequest.siteOrigin;
                            changedUser.Site_Origin__c = userRequest.siteOrigin;
                        }

                        if (userRequest.mobilePhone != null) {
                            changedUser.PersonMobilePhone = userRequest.mobilePhone;
                            changedUser.Phone = userRequest.mobilePhone;
                        }
                        if (userRequest.sendMarketingEmails != null) {
                            changedUser.send_marketing_emails__c = userRequest.sendMarketingEmails;
                        }
                        if (userRequest.address1 != null) {
                            changedUser.ShippingStreet = userRequest.address1;
                        }
                        if (userRequest.address2 != null) {
                            changedUser.ShippingCity = userRequest.address2;
                        }
                        if (userRequest.address3 != null) {
                            changedUser.ShippingState = userRequest.address3;
                        }
                        if (userRequest.postcode != null) {
                            changedUser.ShippingPostalCode = userRequest.postcode;
                        }
                        if (userRequest.country != null) {
                            changedUser.ShippingCountry = userRequest.country;
                        }
                        if (userRequest.company != null) {
                            changedUser.Company__pc = userRequest.company;
                        }
                        if (userRequest.marketingOptIn != null) {
                            // changedUser.MarketingOptIn__pc = userRequest.marketingOptIn =='Y' ? TRUE : FALSE;
                            changedUser.MarketingOptIn__pc = userRequest.marketingOptIn;
                        }
                    } else {
                        String recTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
                        if (recTypeId == null) {
                            // 2022-06-29: 记录log
                            logInfo.Error_Message__c = 'Can not find the record type according to the request information.';
                            insert logInfo;

                            return new CEU_HerokuEntity.ResponseEntity(422, 'Can not find the record type according to the request information.');
                        }
                        Account acc = CEU_HerokuAPIUtils.getUserByEmailAndBrandName(userRequest.emailAddress, userRequest.brandName);
                        if (acc != null) {
                            logInfo.Error_Message__c = 'This email has been registered before.';
                            insert logInfo;
                            return CEU_HerokuAPIUtils.getErrorResponse(403, 'This email has been registered before.');
                        }

                        changedUser = new Account();
                        
                        changedUser.RecordTypeId             = recTypeId;
                        changedUser.Product_Type__c          = userRequest.brandName;//是否需要
                        changedUser.FirstName                = userRequest.firstName;
                        changedUser.LastName                 = userRequest.lastName;
                        changedUser.PersonEmail              = userRequest.emailAddress;
                        changedUser.EGO_username__c          = userRequest.emailAddress;
                        changeduser.EGO_password__c          = userRequest.password;
                
                        if( userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                            changedUser.Site_Origin__pc      = userRequest.siteOrigin;
                            changedUser.Site_Origin__c       = userRequest.siteOrigin;
                        } else {
                            changedUser.Site_Origin__pc      = 'United Kingdom';
                            changedUser.Site_Origin__c       = 'United Kingdom';
                        }

                        changedUser.PersonMobilePhone        = userRequest.mobilePhone;
                        changedUser.Phone                    = userRequest.mobilePhone;
                        changedUser.send_marketing_emails__c = userRequest.sendMarketingEmails;
                        if (userRequest.address1 != null) {
                            changedUser.ShippingStreet = userRequest.address1;
                        }
                        if (userRequest.address2 != null) {
                            changedUser.ShippingCity = userRequest.address2;
                        }
                        if (userRequest.address3 != null) {
                            changedUser.ShippingState = userRequest.address3;
                        }
                        if (userRequest.postcode != null) {
                            changedUser.ShippingPostalCode = userRequest.postcode;
                        }
                        if (userRequest.country != null) {
                            changedUser.ShippingCountry = userRequest.country;
                        }
                        if (userRequest.marketingOptIn != null) {
                            // changedUser.MarketingOptIn__pc = userRequest.marketingOptIn=='Y' ? TRUE : FALSE;
                            changedUser.MarketingOptIn__pc = userRequest.marketingOptIn;
                        }
                        changedUser.Consumer_Status__c = 'Active';
                        changedUser.Tier__c = null;
                        // changedUser.AccountSource = 'Website';
                        // emailFlag = true;
                    }    
                    System.debug(LoggingLevel.INFO, '*** changedUser: ' + changedUser);        
                // }
                upsert changedUser;
                // if(emailFlag){
                //    CEU_HerokuAPIUtils.sendNewConsumerNoticeEmail(changedUser);
                // }
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;

                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.RCUserResponseEntity(200, '', changedUser.Id);
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    // 创建或更新用户处理类 - 欧洲德国 Residential User
    public class UpsertResidentialUserHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'UpsertResidentialUserHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            Account changedUser;
            try{
                CEU_HerokuEntity.RecidentialUserRequestEntity userRequest = (CEU_HerokuEntity.RecidentialUserRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.RecidentialUserRequestEntity.class);
                System.debug(LoggingLevel.INFO, '*** userRequest: ' + userRequest);
                Boolean emailFlag = false;
                if(String.isNotEmpty(userRequest.customerId)){
                    changedUser = CEU_HerokuAPIUtils.getResidentialUserByIDwithoutName(userRequest.customerId);
                    System.debug('----User:' + changedUser);
                    if (changedUser == null) {
                        // 2022-06-29: 记录log
                        logInfo.Error_Message__c = 'Can not find the account.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                    }
                    Account oldUser = CEU_HerokuAPIUtils.getResidentialByEmailAndBrandName(userRequest.emailAddress,'','');
                    if(oldUser != null && oldUser.id != changedUser.id){
                        logInfo.Error_Message__c = 'This email has been registered before.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(403, 'This email has been registered before.');
                    }
                    if (userRequest.firstName != null) {
                        changedUser.FirstName = userRequest.firstName;
                    }
                    if (userRequest.lastName != null) {
                        changedUser.LastName  = userRequest.lastName;
                    }
                    if (userRequest.emailAddress != null) {
                        changedUser.PersonEmail = userRequest.emailAddress;
                        changedUser.EGO_username__c = userRequest.emailAddress;
                    }
                    if (userRequest.password != null) {
                        changedUser.EGO_password__c = userRequest.password;
                        changedUser.Status__c = 'Active';
                    }

                    if (userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                        changedUser.Site_Origin__pc = userRequest.siteOrigin;
                        changedUser.Site_Origin__c = userRequest.siteOrigin;
                    }

                    if (userRequest.mobilePhone != null) {
                        changedUser.PersonMobilePhone = userRequest.mobilePhone;
                        changedUser.Phone = userRequest.mobilePhone;
                    }
                    // if (userRequest.sendMarketingEmails != null) {
                    //     changedUser.send_marketing_emails__c = userRequest.sendMarketingEmails;
                    // }
                    if (userRequest.address != null) {
                        changedUser.Address_Detail__c = userRequest.address;
                    }
                    if (userRequest.street != null) {
                        changedUser.ShippingStreet = userRequest.street;
                    }
                    if (userRequest.city != null) {
                        changedUser.ShippingCity = userRequest.city;
                    }
                    if (userRequest.state != null) {
                        changedUser.ShippingState = userRequest.state;
                    }
                    if (userRequest.postcode != null) {
                        changedUser.ShippingPostalCode = userRequest.postcode;
                    }
                    if (userRequest.country != null) {
                        changedUser.ShippingCountry = userRequest.country;
                    }
                    // if (userRequest.company != null) {
                    //     changedUser.Company__pc = userRequest.company;
                    // }
                    if (userRequest.marketingOptIn != null) {
                        // changedUser.MarketingOptIn__pc = userRequest.marketingOptIn =='Y' ? TRUE : FALSE;
                        changedUser.MarketingOptIn__c = userRequest.marketingOptIn;
                        changedUser.MarketingOptIn__pc = userRequest.marketingOptIn;
                    }
                    if (userRequest.dealerView != null) {
                        // changedUser.MarketingOptIn__pc = userRequest.marketingOptIn =='Y' ? TRUE : FALSE;
                        changedUser.DealerView__c = userRequest.dealerView;
                    }
                    //【Start-Add】： By Vince 2023-12-06 
                    //Add Language For Residential
                    if(userRequest.language != null){
                        changedUser.Language_For_Consumer__c = userRequest.language;
                    }
                    //【End-Add】： By Vince 2023-12-06 

                } else {
                    String recTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
                    if (recTypeId == null) {
                        // 2022-06-29: 记录log
                        logInfo.Error_Message__c = 'Can not find the record type according to the request information.';
                        insert logInfo;

                        return new CEU_HerokuEntity.ResponseEntity(422, 'Can not find the record type according to the request information.');
                    }
                    Account acc = CEU_HerokuAPIUtils.getUserByEmailAndBrandName(userRequest.emailAddress, 'EGO');
                    if (acc != null) {
                        logInfo.Error_Message__c = 'This email has been registered before.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(403, 'This email has been registered before.');
                    }

                    changedUser = new Account();
                    changedUser.RecordTypeId             = recTypeId;
                    // changedUser.Product_Type__c          = userRequest.brandName;//是否需要
                    changedUser.FirstName                = userRequest.firstName;
                    changedUser.LastName                 = userRequest.lastName;
                    changedUser.PersonEmail              = userRequest.emailAddress;
                    changedUser.EGO_username__c          = userRequest.emailAddress;
                    changeduser.EGO_password__c          = userRequest.password;
            
                    if( userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                        changedUser.Site_Origin__pc      = userRequest.siteOrigin;
                        changedUser.Site_Origin__c       = userRequest.siteOrigin;
                    } else {
                        changedUser.Site_Origin__pc      = 'United Kingdom';
                        changedUser.Site_Origin__c       = 'United Kingdom';
                    }

                    changedUser.PersonMobilePhone        = userRequest.mobilePhone;
                    changedUser.Phone                    = userRequest.mobilePhone;
                    // changedUser.send_marketing_emails__c = userRequest.sendMarketingEmails;
                    if (userRequest.address != null) {
                        changedUser.Address_Detail__c = userRequest.address;
                    }
                    if (userRequest.street != null) {
                        changedUser.ShippingStreet = userRequest.street;
                    }
                    if (userRequest.city != null) {
                        changedUser.ShippingCity = userRequest.city;
                    }
                    if (userRequest.state != null) {
                        changedUser.ShippingState = userRequest.state;
                    }
                    if (userRequest.postcode != null) {
                        changedUser.ShippingPostalCode = userRequest.postcode;
                    }
                    if (userRequest.country != null) {
                        changedUser.ShippingCountry = userRequest.country;
                    }
                    if (userRequest.marketingOptIn != null) {
                        // changedUser.MarketingOptIn__pc = userRequest.marketingOptIn=='Y' ? TRUE : FALSE;
                        changedUser.MarketingOptIn__pc = userRequest.marketingOptIn;
                    }
                    if (userRequest.dealerView != null) {
                        changedUser.DealerView__c = userRequest.dealerView;
                    }
                    //【Start-Add】： By Vince 2023-12-06 
                    //Add Language For Residential
                    if(userRequest.language != null){
                        changedUser.Language_For_Consumer__c = userRequest.language;
                    }
                    //【End-Add】： By Vince 2023-12-06 
                    
                    changedUser.Consumer_Status__c = 'Active';
                    changedUser.Tier__c = null;
                    emailFlag = true;
                    changedUser.AccountSource = 'Website';
                    changedUser.IsStandard__c = false;
                }    
                
                System.debug(LoggingLevel.INFO, '*** changedUser: ' + changedUser);        

                upsert changedUser;
                if(emailFlag){
                   CEU_HerokuAPIUtils.sendNewConsumerNoticeEmail(changedUser.Id,true);
                }
                insert logInfo;
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;

                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.RCUserResponseEntity(200, '', changedUser.Id);
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }


     // 创建或更新用户处理类 - 欧洲德国 2.3Commercial User - fleet manager
    public class UpsertCommercialUserHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            System.debug('start create Commercial User------->');
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'UpsertCommercialUserHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            Boolean exsitFlag = false;
            Account changedCompany;
            Contact changedContact;
            String strAccId = '';
            Savepoint sp = null;
            try{
                sp = Database.setSavepoint();
                Boolean emailFlag = false;
                CEU_HerokuEntity.ComercialUserRequestEntity userRequest = (CEU_HerokuEntity.ComercialUserRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.ComercialUserRequestEntity.class);
                //考虑company为空，userid不为空，这种情况有问题，要么都是空，要么都不空才对
                if(!String.isNotEmpty(userRequest.customerId)){
                    //判断company重复
                    exsitFlag = CEU_HerokuAPIUtils.isCommercialCompanyDuplicated(userRequest);
                    if(exsitFlag){
                        logInfo.Error_Message__c = 'This company is exist.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'This company is exist.');
                    }
                }
                if(!String.isNotEmpty(userRequest.userId)){
                    //判断contact重复
                    exsitFlag = CEU_HerokuAPIUtils.isCommercialContactDuplicated(userRequest);
                    if(exsitFlag){
                        logInfo.Error_Message__c = 'This user is exist.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'This user is exist.');
                    }
                }
                
                //进行company的upsert
                if(String.isNotEmpty(userRequest.customerId)){
                    changedCompany = CEU_HerokuAPIUtils.getCommercialCompanyByID(userRequest.customerId);
                    System.debug('----changedCompany:' + changedCompany);
                    if (changedCompany == null) {
                        logInfo.Error_Message__c = 'Can not find the account.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                    }
                    List<Account> oldUser = CEU_HerokuAPIUtils.getCommercialAccount(userRequest.postcode,userRequest.country,userRequest.company,'');
                    if(oldUser != null && oldUser.size() > 0  && oldUser[0].id != changedCompany.id){
                        return CEU_HerokuAPIUtils.getErrorResponse(403, 'This company is exist.');
                    }
                    System.debug('userRequest.siteOrigin===========>'+userRequest.siteOrigin);
                    if (userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                       
                        // changedCompany.Site_Origin__pc = userRequest.siteOrigin;
                        changedCompany.Site_Origin__c = userRequest.siteOrigin;
                    }

                    if (userRequest.sendMarketingEmails != null) {
                        changedCompany.send_marketing_emails__c = userRequest.sendMarketingEmails;
                    }
                    if (userRequest.address1 != null) {
                        changedCompany.ShippingStreet = userRequest.address1;
                    }
                    if (userRequest.address2 != null) {
                        changedCompany.ShippingCity = userRequest.address2;
                    }
                    if (userRequest.address3 != null) {
                        changedCompany.ShippingState = userRequest.address3;
                    }
                    if (userRequest.postcode != null) {
                        changedCompany.ShippingPostalCode = userRequest.postcode;
                    }else{
                        return CEU_HerokuAPIUtils.getErrorResponse(422, 'postcode is missing.');
                    }
                    if (userRequest.country != null) {
                        changedCompany.ShippingCountry = userRequest.country;
                    }else{
                        return CEU_HerokuAPIUtils.getErrorResponse(422, 'country is missing.');
                    }
                    if (userRequest.company != null) {
                        // changedCompany.Company__pc = userRequest.company;
                        changedCompany.Company__c = userRequest.company;
                        changedCompany.Name = userRequest.company;
                    }else{
                        return CEU_HerokuAPIUtils.getErrorResponse(422, 'company is missing.');
                    }
                    System.debug('userRequest.marketingOptIn===========>'+userRequest.marketingOptIn);
                    if (userRequest.marketingOptIn != null) {
                        changedCompany.MarketingOptIn__c = userRequest.marketingOptIn;
                    }
                    if (userRequest.businessArea != null) {
                        changedCompany.Business_Area__c = userRequest.businessArea;
                    }

                    //【Start-Add】： By Vince 2023-12-06 
                    //Add Language For Commercial
                    if(userRequest.language != null){
                        changedCompany.Language_For_Consumer__c = userRequest.language;
                    }
                    //【End-Add】： By Vince 2023-12-06 
                } else {
                    String recTypeId = RECORDTYPEID_COMMERCIAL_ACC;
                    if (recTypeId == null) {
                        logInfo.Error_Message__c = 'Can not find the company record type according to the request information.';
                        insert logInfo;

                        return new CEU_HerokuEntity.ResponseEntity(422, 'Can not find the company record type according to the request information.');
                    }
                    changedCompany = new Account();
                    changedCompany.RecordTypeId             = recTypeId;
                    changedCompany.Product_Type__c          = userRequest.brandName;//是否需要
                    System.debug('userRequest.siteOrigin===========>'+userRequest.siteOrigin);
                    if( userRequest.siteOrigin != null && userRequest.siteOrigin != '' ) {
                        // changedCompany.Site_Origin__pc      = userRequest.siteOrigin;
                        changedCompany.Site_Origin__c       = userRequest.siteOrigin;
                        // changedCompany.Site_Origin__pc       = userRequest.siteOrigin;
                    } else {
                        // changedCompany.Site_Origin__pc      = 'United Kingdom';
                        changedCompany.Site_Origin__c       = 'United Kingdom';
                        // changedCompany.Site_Origin__pc       = 'United Kingdom';
                    }
                    changedCompany.send_marketing_emails__c = userRequest.sendMarketingEmails;
                    if (userRequest.address1 != null) {
                        changedCompany.ShippingStreet = userRequest.address1;
                    }
                    if (userRequest.address2 != null) {
                        changedCompany.ShippingCity = userRequest.address2;
                    }
                    if (userRequest.address3 != null) {
                        changedCompany.ShippingState = userRequest.address3;
                    }
                    if (userRequest.postcode != null) {
                        changedCompany.ShippingPostalCode = userRequest.postcode;
                    }else{
                        return CEU_HerokuAPIUtils.getErrorResponse(422, 'postcode is missing.');
                    }
                    if (userRequest.country != null) {
                        changedCompany.ShippingCountry = userRequest.country;
                    }else{
                        return CEU_HerokuAPIUtils.getErrorResponse(422, 'country is missing.');
                    }

                    if (userRequest.company != null) {
                        changedCompany.Name = userRequest.company;
                        changedCompany.Company__c = userRequest.company;
                        // changedCompany.Company__pc = userRequest.company;
                    }else{
                        return CEU_HerokuAPIUtils.getErrorResponse(422, 'company is missing.');
                    }
                    //【Start-Add】： By Vince 2023-12-06 
                    //Add Language For Commercial
                    if(userRequest.language != null){
                        changedCompany.Language_For_Consumer__c = userRequest.language;
                    }
                    //【End-Add】： By Vince 2023-12-06 

                    System.debug('userRequest.marketingOptIn===========>'+userRequest.marketingOptIn);
                    // if (userRequest.marketingOptIn != null) {
                    //     changedCompany.MarketingOptIn__c = userRequest.marketingOptIn;
                        
                    // }
                    if (userRequest.businessArea != null) {
                        changedCompany.Business_Area__c = userRequest.businessArea;
                    }
                    changedCompany.Consumer_Status__c = 'Active';
                    changedCompany.Tier__c = null;
                    changedCompany.IsStandard__c = false;
                    emailFlag = true;
                }    
                System.debug(LoggingLevel.INFO, '*** changedCompany: ' + changedCompany);      
                // upsert changedCompany;
                Database.DMLOptions insertDML = new Database.DMLOptions(); 
                insertDML.DuplicateRuleHeader.AllowSave=true;
                if(String.isNotEmpty(userRequest.customerId)){
                    Database.upsert(changedCompany);
                }else{
                    Database.insert(changedCompany,insertDML);
                }

                // Database.upsert(changedCompany,insertDML);
                //进行fleet contact的upsert
                strAccId = changedCompany.Id;
                if(String.isNotEmpty(userRequest.userId)){
                    changedContact = CEU_HerokuAPIUtils.getContactByID(userRequest.userId);
                    System.debug('----changedContact:' + changedContact);
                    if (changedContact == null) {
                        logInfo.Error_Message__c = 'Can not find the user.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the user.');
                    }

                    List<Contact> oldUser = CEU_HerokuAPIUtils.getCommercialContact(userRequest.emailAddress,'');
                    if(oldUser != null && oldUser.size() > 0  && oldUser[0].id != changedContact.id){
                        return CEU_HerokuAPIUtils.getErrorResponse(403, 'This user is exist.');
                    }

                    changedContact.AccountId = strAccId;
                    if (userRequest.firstName != null) {
                        changedContact.FirstName = userRequest.firstName;
                    }
                    if (userRequest.lastName != null) {
                        changedContact.LastName  = userRequest.lastName;
                    }
                    if (userRequest.emailAddress != null) {
                        changedContact.Email = userRequest.emailAddress;
                    }
                    if (userRequest.password != null) {
                        changedContact.Password__c = userRequest.password;
                        changedCompany.Consumer_Status__c = 'Active';
                        changedContact.Status__c = 'Active';
                    }
                    if (userRequest.mobilePhone != null) {
                        changedContact.MobilePhone = userRequest.mobilePhone;
                        changedContact.Phone = userRequest.mobilePhone;
                    }
                } else {
                    String recTypeId = RECORDTYPEID_COMMERCIAL_CON;
                    if (recTypeId == null) {
                        logInfo.Error_Message__c = 'Can not find the user record type according to the request information.';
                        insert logInfo;

                        return new CEU_HerokuEntity.ResponseEntity(422, 'Can not find the user record type according to the request information.');
                    }
                    // Account acc = CEU_HerokuAPIUtils.getUserByEmailAndBrandName(userRequest.emailAddress, userRequest.brandName);
                    // if (acc != null) {
                    //     return CEU_HerokuAPIUtils.getErrorResponse(403, 'This email has been registered before.');
                    // }

                    changedContact = new Contact();
                    changedContact.RecordTypeId             = recTypeId;
                    changedContact.AccountId                = strAccId;
                    changedContact.FirstName = userRequest.firstName;
                    changedContact.LastName  = userRequest.lastName;
                    changedContact.Email = userRequest.emailAddress;
                    changedContact.Password__c = userRequest.password;
                    changedContact.MobilePhone = userRequest.mobilePhone;
                    changedContact.Phone = userRequest.mobilePhone;
                    changedContact.Username__c = userRequest.emailAddress;
                    changedContact.Role__c = 'Fleet Manager';
                    changedContact.Status__c = 'Active';
                    if (userRequest.marketingOptIn != null) {
                        changedContact.MarketingOptIn__c = userRequest.marketingOptIn;
                    }
                }   

               

                System.debug(LoggingLevel.INFO, '*** changedContact: ' + changedContact);        
                if(String.isNotEmpty(userRequest.userId)){
                    Database.upsert(changedContact);
                    if(userRequest.password != null) {
                        Database.upsert(changedCompany);
                    }
                }else{
                    Database.insert(changedContact,insertDML);
                }
                // Database.upsert(changedContact,insertDML);
                if(emailFlag){
                   CEU_HerokuAPIUtils.sendNewConsumerNoticeEmail(changedCompany.Id,false);
                }
                insert logInfo;
            }catch (Exception ex) {
                Database.rollback(sp);
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;
                
                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.RCUserResponseEntity(200,'', changedContact.Id);
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    // 创建或更新用户处理类 - 欧洲德国 2.4Invited Commercial User
    public class upsertInvitedCommercialUser extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            System.debug('start create Commercial User------->');
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'upsertInvitedCommercialUser';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            Boolean exsitFlag = false;
            Account changedCompany;
            Contact changedContact;
            String strAccId = '';
            Savepoint sp = null;
            try{
                sp = Database.setSavepoint();
                CEU_HerokuEntity.InvitedComercialUserRequestEntity userRequest = (CEU_HerokuEntity.InvitedComercialUserRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.InvitedComercialUserRequestEntity.class);
                
                //判断contact重复
                exsitFlag = CEU_HerokuAPIUtils.isInvitedCommercialContactDuplicated(userRequest);
                if(exsitFlag){
                    logInfo.Error_Message__c = 'This email has been registered a commercial user.';
                    insert logInfo;
                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'This email has been registered a commercial user.');
                }
                
                //进行company的是否存在校验
                if(String.isNotEmpty(userRequest.customerId)){
                    changedCompany = CEU_HerokuAPIUtils.getCommercialCompanyByID(userRequest.customerId);
                    System.debug('----changedCompany:' + changedCompany);
                    if (changedCompany == null) {
                        logInfo.Error_Message__c = 'Can not find the company.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the company.');
                    }
                }else{
                    logInfo.Error_Message__c = 'The customer id can not be empty.';
                    insert logInfo;
                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'The customer id can not be empty.');
                }
                strAccId = userRequest.customerId;
                Database.DMLOptions insertDML = new Database.DMLOptions(); 
                insertDML.DuplicateRuleHeader.AllowSave=true;
                //进行invited contact的upsert
                boolean emailFlag = false;
                if(String.isNotEmpty(userRequest.userId)){

                    changedContact = CEU_HerokuAPIUtils.getContactByID(userRequest.userId);
                    System.debug('----changedContact:' + changedContact);
                    if (changedContact == null) {
                        logInfo.Error_Message__c = 'Can not find the user.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the user.');
                    }
                    
                    changedContact.AccountId = strAccId;
                    if (userRequest.firstName != null) {
                        changedContact.FirstName = userRequest.firstName;
                    }
                    if (userRequest.lastName != null) {
                        changedContact.LastName  = userRequest.lastName;
                    }
                    if (userRequest.emailAddress != null) {
                        changedContact.Email = userRequest.emailAddress;
                    }
                    if (userRequest.password != null) {
                        changedContact.Password__c = userRequest.password;
                    }
                    if (userRequest.mobilePhone != null) {
                        changedContact.MobilePhone = userRequest.mobilePhone;
                        changedContact.Phone = userRequest.mobilePhone;
                    }
                } else {
                    String recTypeId = RECORDTYPEID_COMMERCIAL_CON;
                    if (recTypeId == null) {
                        logInfo.Error_Message__c = 'Can not find the user record type according to the request information.';
                        insert logInfo;

                        return new CEU_HerokuEntity.ResponseEntity(422, 'Can not find the user record type according to the request information.');
                    }
                    emailFlag = true;
                    changedContact = new Contact();
                    changedContact.RecordTypeId             = recTypeId;
                    changedContact.AccountId                = strAccId;
                    changedContact.FirstName = userRequest.firstName;
                    changedContact.LastName  = userRequest.lastName;
                    changedContact.Email = userRequest.emailAddress;
                    changedContact.Username__c = userRequest.emailAddress;
                    changedContact.Password__c = userRequest.password;
                    changedContact.MobilePhone = userRequest.mobilePhone;
                    changedContact.Phone = userRequest.mobilePhone;
                    changedContact.Role__c = 'Staff';
                    changedContact.Status__c = 'Waiting Customer Approval';
                    changedContact.IsStandard__c = false;

                }    
                System.debug(LoggingLevel.INFO, '*** changedContact: ' + changedContact);        
                if(String.isNotEmpty(userRequest.userId)){
                    Database.upsert(changedContact);
                }else{
                    Database.insert(changedContact);
                }
                System.debug('changedContact.Id===>'+changedContact.Id);
                if(emailFlag){
                   CEU_HerokuAPIUtils.sendNewConsumerCommerNoticeEmail(changedContact.Id);
                }
            }catch (Exception ex) {
                Database.rollback(sp);
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;
                
                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.RCUserResponseEntity(200,'', changedContact.Id);
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }
    // 权限角色转移 - 欧洲德国 2.5 permissionTrasferHandler
    public class PermissionTransferHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            System.debug('start create Commercial User------->');
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'upsertInvitedCommercialUser';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            // Account changedCompany;
            Contact origialContact;
            Contact changedContact;
            Savepoint sp = null;
            try{
                sp = Database.setSavepoint();
                CEU_HerokuEntity.transferComercialUserRequestEntity userRequest = (CEU_HerokuEntity.transferComercialUserRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.transferComercialUserRequestEntity.class);
                
                //判断contact重复
                System.debug('origialContact===>'+userRequest.originalUserId);
                origialContact = CEU_HerokuAPIUtils.getContactByID(userRequest.originalUserId);
                changedContact = CEU_HerokuAPIUtils.getContactByID(userRequest.changeUserId);
                if (origialContact == null) {
                    logInfo.Error_Message__c = 'Can not find the original user.';
                    insert logInfo;
                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the original user.');
                }
                if (changedContact == null) {
                    logInfo.Error_Message__c = 'Can not find the changed user.';
                    insert logInfo;
                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the changed user.');
                }
            
                origialContact.IsStandard__c = false;
                changedContact.IsStandard__c = false;
                origialContact.Role__c = userRequest.origialUserUpdateRole;
                changedContact.Role__c = userRequest.changeUserUpdateRole;
     
                List<Contact> updateList = new List<Contact>();
                updateList.add(origialContact);
                updateList.add(changedContact);
                Database.upsert(updateList);
                
            }catch (Exception ex) {
                Database.rollback(sp);
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;
                
                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.RCUserResponseEntity(200,'', origialContact.Id);
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    public class SignupForNewsletterHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            logInfo.ApexName__c = 'SignupForNewsletterHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;
    
            Savepoint sp = null;
            try {
                sp = Database.setSavepoint();
                CEU_HerokuEntity.SignupForNewsletterRequestEntity userRequest = (CEU_HerokuEntity.SignupForNewsletterRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.SignupForNewsletterRequestEntity.class);
    
                // check customer exist if not null
                if(String.isNotBlank(userRequest.customerId)) {
                    Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(userRequest.customerId);
                    if(acc == null) {
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                    }
                }
    
                if(String.isBlank(userRequest.emailAddress)) {
                    return CEU_HerokuAPIUtils.getErrorResponse(422, 'Email is missing.');
                }
                List<Website_Signup_For_Newsletter_Customers__c> customerList = [SELECT Id, Email__c FROM Website_Signup_For_Newsletter_Customers__c 
                                                                                 WHERE Email__c = :userRequest.emailAddress];
                Boolean isRegisterNewsLetter = false;
                if(!customerList.isEmpty()) {
                    // update
                    for(Website_Signup_For_Newsletter_Customers__c signUpCustomer : customerList) {
                        if(String.isNotBlank(userRequest.customerId)) {
                            signUpCustomer.Customer__c = userRequest.customerId;
                        }
    
                        if(String.isNotBlank(userRequest.firstName)) {
                            signUpCustomer.First_Name__c = userRequest.firstName;
                        }
    
                        if(String.isNotBlank(userRequest.lastName)) {
                            signUpCustomer.Last_Name__c = userRequest.lastName;
                        }
    
                        if(userRequest.signupForNewsletter != null) {
                            signUpCustomer.Signup_For_Newsletter__c = userRequest.signupForNewsletter;
                        }
    
                        if(userRequest.signupForNewsletter) {
                            isRegisterNewsLetter = true;
                        }
                    }
                    update customerList;
                }
                else {
                    // new
                    Website_Signup_For_Newsletter_Customers__c signUpCustomer = new Website_Signup_For_Newsletter_Customers__c();
                    if(String.isNotBlank(userRequest.customerId)) {
                        signUpCustomer.Customer__c = userRequest.customerId;
                    }
    
                    if(String.isNotBlank(userRequest.emailAddress)) {
                        signUpCustomer.Email__c = userRequest.emailAddress;
                    }
    
                    if(String.isNotBlank(userRequest.firstName)) {
                        signUpCustomer.First_Name__c = userRequest.firstName;
                    }
    
                    if(String.isNotBlank(userRequest.lastName)) {
                        signUpCustomer.Last_Name__c = userRequest.lastName;
                    }
    
                    if(userRequest.signupForNewsletter != null) {
                        signUpCustomer.Signup_For_Newsletter__c = userRequest.signupForNewsletter;
                    }
    
                    if(userRequest.signupForNewsletter) {
                        isRegisterNewsLetter = true;
                    }
                    insert signUpCustomer;
                }
    
                if(isRegisterNewsLetter) {
                    String name = userRequest.firstName + ' ' + userRequest.lastName;
                    CEU_HerokuAPIUtils.sendNewsLetterRegistrationEmail(userRequest.customerId, userRequest.emailAddress, name, userRequest.firstName);
                }
            }
            catch(Exception ex) {
                Database.rollback(sp);
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;
                
                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }
            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.RCUserResponseEntity(200, '', '');
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            List<CEU_HerokuEntity.SignupForNewsletterRequestEntity> signupCustomers = new List<CEU_HerokuEntity.SignupForNewsletterRequestEntity>();

            for(Website_Signup_For_Newsletter_Customers__c signupCustomer : [SELECT First_Name__c, Last_Name__c, Email__c FROM Website_Signup_For_Newsletter_Customers__c 
                                                                             WHERE Signup_For_Newsletter__c = true]) {
                CEU_HerokuEntity.SignupForNewsletterRequestEntity entity = new CEU_HerokuEntity.SignupForNewsletterRequestEntity();
                entity.firstName = signupCustomer.First_Name__c;
                entity.lastName = signupCustomer.Last_Name__c;
                entity.emailAddress = signupCustomer.Email__c;
                signupCustomers.add(entity);
            }

            return new CEU_HerokuEntity.SignupCustomerResponseEntity(200, '', signupCustomers);
        }
    }

    public class InactiveResidentialUserHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'InactiveResidentialUserHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            try{
                CEU_HerokuEntity.InactiveUserRequestEntity userRequest = (CEU_HerokuEntity.InactiveUserRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.InactiveUserRequestEntity.class);
                if(String.isNotEmpty(userRequest.customerId)){
                    Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(userRequest.customerId);
                    if (acc == null) {
                        logInfo.Error_Message__c = 'Can not find the account.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                    }else{
                        delete acc;
                    }
                }
                
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;

                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.ResponseEntity(200, '');
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }
    public class InactiveCommercialUserHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'InactiveCommercialUserHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            try{
                CEU_HerokuEntity.InactiveUserRequestEntity userRequest = (CEU_HerokuEntity.InactiveUserRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.InactiveUserRequestEntity.class);
                System.debug(LoggingLevel.INFO, '*** userRequest: ' + userRequest);
                if(String.isNotEmpty(userRequest.customerId)){
                    Contact con = CEU_HerokuAPIUtils.getContactByID(userRequest.customerId);
                    if (con == null) {
                        logInfo.Error_Message__c = 'Can not find the account.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                    }else{
                        delete con;
                    }
                }
                
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;

                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.ResponseEntity(200, '');
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }
    
    // 查询用户处理类
    public class SelectUserByEmailHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String emailAddress = headerMap.get('emailAddress');
            String brandName = headerMap.get('brandName');
            if (String.isEmpty(emailAddress) || String.isEmpty(brandName)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'Email or brand_name is missing.');
            }

            Account acc = CEU_HerokuAPIUtils.getUserByEmailAndBrandName(emailAddress, brandName);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            // Add by yujie for service migration, not include merge logic 
            System.debug('----:' + (acc.Id + ';' + acc.EGO_password__c));
            return new CEU_HerokuEntity.RCUserResponseEntity(200, '', (acc.Id + ';' + acc.EGO_password__c));
        }
    }

     // 查询用户处理类
    public class SelectResidentialUserByEmailHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String emailAddress = headerMap.get('emailAddress');
            String brandName = headerMap.get('brandName');
            String userCode = headerMap.get('userCode');
            System.debug(LoggingLevel.INFO, '*** emailAddress: ' + emailAddress);
            System.debug(LoggingLevel.INFO, '*** userCode: ' + userCode);
            if (String.isEmpty(emailAddress) && String.isEmpty(userCode)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'Email and User Code is missing.');
            }

            Account acc = CEU_HerokuAPIUtils.getResidentialByEmailAndBrandName(emailAddress, brandName, userCode);
            System.debug(LoggingLevel.INFO, '*** acc: ' + acc);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            // Add by yujie for service migration, not include merge logic 
            System.debug('----:' + (acc.Id + ';' + acc.EGO_password__c));
            return new CEU_HerokuEntity.RCUserResponseEntity(200, '', (acc.Id + ';' + acc.EGO_password__c));
        }
    }

    public class SelectCommercialUserByEmailHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String emailAddress = headerMap.get('emailAddress');
            String brandName = headerMap.get('brandName');
            if (String.isEmpty(emailAddress) || String.isEmpty(brandName)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'Email or brand_name is missing.');
            }

            Contact acc = CEU_HerokuAPIUtils.getCommercialByEmailAndBrandName(emailAddress, brandName);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the Contact.');
            }

            //返回正确结果，用户的sfid
            // Add by yujie for service migration, not include merge logic 
            System.debug('----:' + (acc.Id + ';' + acc.Password__c));
            return new CEU_HerokuEntity.RCUserResponseEntity(200, '', (acc.Id + ';' + acc.Password__c));
        }
    }

    //获取Residential用户信息
    public class GetUserInfoByIdHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(customerId)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'customerId is missing.');
            }

            Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(customerId);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }
            //返回正确结果，用户的sfid
            System.debug('----:' + (acc.Id));
            return new CEU_HerokuEntity.RCUserInfoResponseEntity(200, '', acc);
        }
    }
    //获取Residential用户信息 -- 这个过程需要重新结构化
    public class GetResidentialUserInfoByIdHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(customerId)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'customerId is missing.');
            }

            Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(customerId);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }
            //返回正确结果，用户的sfid
            System.debug('----:' + (acc.Id));
            return new CEU_HerokuEntity.ResidentailUserInfoResponseEntity(200, '', acc);
        }
    }

    //获取Commercial用户信息，待更新逻辑 -- 重新结构化
    public class GetCommercialUserInfoByIdHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String customerId = headerMap.get('userId');
            if (String.isEmpty(customerId)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'user id is missing.');
            }

            Contact con = CEU_HerokuAPIUtils.getContactByID(customerId);
            if (con == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the commercial user.');
            }
            String accId = con.AccountId;
            Account acc = CEU_HerokuAPIUtils.getContactListByID(accId);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the commercial company.');
            }
            //返回正确结果，用户的sfid
            System.debug('----:' + (acc.Id));
            return new CEU_HerokuEntity.CommercialUserInfoResponseEntity(200, '', acc, con);
        }
    }

    // 查询用户处理类
    public class SelectUserByIdHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            String customerId = headerMap.get('customerId');
            if (String.isEmpty(customerId)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }

            Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(customerId);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.RCUserResponseEntity(200, '', acc.Id);
        }
    }

    // 注册warranty处理类，考虑commercial用户如何调整
    public class RegisterWarrantyHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody) {
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'RegisterWarrantyHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            try{
                CEU_HerokuEntity.RCRegisterWarrantyRequestEntity regWarRequest = (CEU_HerokuEntity.RCRegisterWarrantyRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.RCRegisterWarrantyRequestEntity.class);
                System.debug(LoggingLevel.INFO, '*** regWarRequest: ' + regWarRequest);
                Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(regWarRequest.customerId);
                if (String.isEmpty(regWarRequest.customerId) || acc == null) {
                    // 2022-06-29: 记录log
                    logInfo.Error_Message__c = 'Can not find the account.';
                    insert logInfo;

                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                }
                //********-vince 校验regWarRequest.purchaseDate是否为空
                if(String.isEmpty(regWarRequest.purchaseDate)){
                    logInfo.Error_Message__c = 'Please fill in your purchase date.';
                    insert logInfo;
                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'Please fill in your purchase date.');
                }

                List<CEU_HerokuEntity.RCRegisterWarrantyItemLine> itemlinelist = new List<CEU_HerokuEntity.RCRegisterWarrantyItemLine>();
                for(CEU_HerokuEntity.WarrantyItemEntity line: regWarRequest.warrantyItems){
                    CEU_HerokuEntity.RCRegisterWarrantyItemLine lineWarpper = new CEU_HerokuEntity.RCRegisterWarrantyItemLine();
                    lineWarpper.customerId = regWarRequest.customerId;
                    lineWarpper.receiptStatus = regWarRequest.receiptStatus;
                    lineWarpper.receiptUrl = regWarRequest.receiptUrl;
                    lineWarpper.purchaseDate = regWarRequest.purchaseDate;
                    lineWarpper.productUseType = regWarRequest.productUseType;
                    lineWarpper.placeOfPurchase = regWarRequest.placeOfPurchase;
                    lineWarpper.masterModelNumber = regWarRequest.masterModelNumber;
                    lineWarpper.serialNumber = line.serialNumber;
                    lineWarpper.productModelNumber = line.productModelNumber;
                    itemlinelist.add(lineWarpper);
                }
                System.debug(LoggingLevel.INFO, '*** itemlinelist: ' + itemlinelist);

                Set<String> productCodeSet = new Set<String>();
                for (CEU_HerokuEntity.RCRegisterWarrantyItemLine wi : itemlinelist) {
                    productCodeSet.add(wi.productModelNumber);
                }
                System.debug(LoggingLevel.INFO, '*** productCodeSet: ' + productCodeSet);
                Map<String,Product2> id2ProductMap = new Map<String,Product2>();
                id2ProductMap = getproductMap(productCodeSet);
                System.debug(LoggingLevel.INFO, '*** id2ProductMap: ' + id2ProductMap);
                List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>();
                
                Boolean isKit = false ; 
                if(itemlinelist.size() > 1){
                    isKit = TRUE;
                }
                List<Warranty_Item__c> warrantyItemHeaderList = new List<Warranty_Item__c>();
                for (CEU_HerokuEntity.RCRegisterWarrantyItemLine wi : itemlinelist) {
                    if(id2ProductMap.containsKey(wi.productModelNumber)){
                        Warranty_Item__c wt   = new Warranty_Item__c();
                        wt.Product__c = id2ProductMap.get(wi.productModelNumber).Id;
                        wt.Consumer__c = wi.customerId;
                        wt.Purchase_Date__c   = (String.isNotEmpty(wi.purchaseDate) && wi.purchaseDate.length() == 6)
                                   ? Date.valueOf('20' + wi.purchaseDate.substring(4, 6) + '-' +
                                   wi.purchaseDate.substring(0, 2) + '-' +
                                   wi.purchaseDate.substring(2, 4)) : null;
                        if(wi != null && String.isNotEmpty(wi.serialNumber) && wi.serialNumber != 'null') {
                            wt.Serial_Number__c = wi.serialNumber;
                        }
                        wt.Product_Use_Type__c = wi.productUseType;
                        wt.Product_Use_Type2__c = ((wi.productUseType.contains('mmercial') || 
                                                    wi.productUseType.contains('rofessional'))
                                                ? 'Industrial/Professional/Commercial'
                                                : 'Residential');
                        wt.Purchase_Place__c = wi.placeOfPurchase == 'Online Dealer'? 'Online Dealers': wi.placeOfPurchase;
                        if(wi.productModelNumber == wi.masterModelNumber && isKit){
                            wt.Is_Kit__c = true;
                            warrantyItemHeaderList.add(wt);
                        }else{
                            warrantyItemList.add(wt);
                        }
                        
                    }                   
                }
                String masterModelItemId = '';
                if(warrantyItemHeaderList.size()>0){

                    Database.insert(warrantyItemHeaderList);
                    masterModelItemId = warrantyItemHeaderList[0].Id;

                    for(Warranty_Item__c wi :warrantyItemList){
                        wi.Parent_Warranty__c = masterModelItemId;
                    }
                }

                System.debug(LoggingLevel.INFO, '*** warrantyItemList: ' + warrantyItemList);
                Database.DMLOptions insertDML = new Database.DMLOptions(); 
                insertDML.DuplicateRuleHeader.AllowSave=true;
                Database.insert(warrantyItemList,insertDML);
                return new CEU_HerokuEntity.ResponseEntity(200, '');
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;
                return new CEU_HerokuEntity.ResponseEntity(404, logInfo.Error_Message__c);
            }
            // 返回正确结果，用户的sfid

        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    // // 注册warranty处理类，考虑commercial用户如何调整（含master model结构版）
    // public class RegisterWarrantyWithReceiptHandler extends CEU_HerokuAPIHandler {
    //     public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody) {
    //         // 2022-06-29: 新增记录Log
    //         logInfo.ApexName__c = 'RegisterWarrantyWithReceiptHandler';
    //         logInfo.Method__c = 'handlePost';
    //         logInfo.ReqParam__c = requestBody;

    //         try{
    //             CEU_HerokuEntity.NewRegisterWarrantyRequestEntity regWarRequest = (CEU_HerokuEntity.NewRegisterWarrantyRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.NewRegisterWarrantyRequestEntity.class);
    //             System.debug(LoggingLevel.INFO, '*** regWarRequest: ' + regWarRequest);
    //             Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(regWarRequest.customerId);
    //             Boolean residentialFlag = true;
    //             if(acc == null){
    //                 acc = CEU_HerokuAPIUtils.getCommercialCompanyByID(regWarRequest.customerId);
    //                 residentialFlag = false;
    //             }
    //             if (String.isEmpty(regWarRequest.customerId) || acc == null) {
    //                 // 2022-06-29: 记录log
    //                 logInfo.Error_Message__c = 'Can not find the account.';
    //                 insert logInfo;

    //                 return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
    //             }

    //             List<CEU_HerokuEntity.NewRegisterWarrantyItemLine> itemlinelist = new List<CEU_HerokuEntity.NewRegisterWarrantyItemLine>();
    //             Set<String> snSet = new Set<String>();
    //             Map<String,String> model2SNMap = new Map<String,String>();
    //             Map<String,String> sn2RecordIdMap = new Map<String,String>();
    //             for(CEU_HerokuEntity.WarrantyItemEntityNew line: regWarRequest.warrantyItems){
    //                 snSet.add(line.serialNumber);
    //                 if(line.recordId != '') {
    //                     sn2RecordIdMap.put(line.serialNumber,line.recordId);
    //                 }
    //                 model2SNMap.put(line.productModelNumber,line.serialNumber);
    //             }
    //             System.debug(LoggingLevel.INFO, '*** sn2RecordIdMap: ' + sn2RecordIdMap);
    //             List<Warranty_Item__c> duplicateWarrantyList = [SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,Is_Kit__c,
    //                                             Parent_Warranty__c, Expiration_Date_New__c,Receipt_Link__c,Update_Flag__c,
    //                                             Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c,
    //                                             (SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,
    //                                                 Product_Use_Type2__c,Expiration_Date_New__c,Receipt_Link__c,Update_Flag__c,
    //                                             Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c FROM Child_Warranties__r)
    //                                         FROM Warranty_Item__c
    //                                         WHERE Serial_Number__c in:snSet];

    //             if(!duplicateWarrantyList.isEmpty()){
    //                 String errorSN = '';
    //                 Set<String> removeDupicateRecord = new Set<String>();
    //                 for(Warranty_Item__c wi:duplicateWarrantyList){
    //                     System.debug(LoggingLevel.INFO, '*** wi.Id: ' + wi.Id);
    //                     System.debug(LoggingLevel.INFO, '*** sn: ' + sn2RecordIdMap.get(wi.Serial_Number__c) );
    //                     if(sn2RecordIdMap.containsKey(wi.Serial_Number__c) && (sn2RecordIdMap.get(wi.Serial_Number__c) != wi.Id)){
    //                         if(!removeDupicateRecord.contains(wi.Serial_Number__c) ){
    //                             errorSN += wi.Serial_Number__c +',';
    //                             removeDupicateRecord.add(wi.Serial_Number__c);
    //                         }
    //                     }else if(snSet.contains(wi.Serial_Number__c) && !sn2RecordIdMap.containsKey(wi.Serial_Number__c)){
    //                         if(!removeDupicateRecord.contains(wi.Serial_Number__c) ){
    //                             errorSN += wi.Serial_Number__c +',';
    //                             removeDupicateRecord.add(wi.Serial_Number__c);
    //                         }
    //                     }
    //                 }
    //                 if(errorSN.endsWith(',')){
    //                     errorSN = errorSN.removeEnd(',');
    //                 }
    //                 if(errorSN != ''){
    //                     logInfo.Error_Message__c = 'The Serial Number has been registered before ' + errorSN ;
    //                     insert logInfo;
    //                     return CEU_HerokuAPIUtils.getErrorResponse(404, logInfo.Error_Message__c);
    //                 }
    //             }
    //             //需要校验SN Rule
    //             String snError = snVerify(model2SNMap,transferPurchaseDate(regWarRequest.purchaseDate));
    //             System.debug(LoggingLevel.INFO, '*** snError: ' + snError);
    //             if(snError != ''){
    //                 if(snError.endsWith(',')){
    //                     snError = snError.removeEnd(',');
    //                 }
    //                 snError = snError + ' is not correct.';
    //                 logInfo.Error_Message__c = snError ;
    //                 insert logInfo;
    //                 return CEU_HerokuAPIUtils.getErrorResponse(404, logInfo.Error_Message__c);
    //             }
                

    //             for(CEU_HerokuEntity.WarrantyItemEntityNew line: regWarRequest.warrantyItems){
    //                 CEU_HerokuEntity.NewRegisterWarrantyItemLine lineWarpper = new CEU_HerokuEntity.NewRegisterWarrantyItemLine();
    //                 // lineWarpper.productName;
    //                 lineWarpper.serialNumber = line.serialNumber;
    //                 lineWarpper.productModelNumber = line.productModelNumber;
    //                 lineWarpper.userId = regWarRequest.userId;
    //                 lineWarpper.customerId = regWarRequest.customerId;
    //                 lineWarpper.receiptStatus = regWarRequest.receiptStatus;
    //                 lineWarpper.receiptUrl = regWarRequest.receiptUrl;
    //                 lineWarpper.purchaseDate = regWarRequest.purchaseDate;
    //                 lineWarpper.placeOfPurchase = regWarRequest.placeOfPurchase;
    //                 lineWarpper.masterModelNumber = regWarRequest.masterModelNumber;
    //                 lineWarpper.recordId = line.recordId;
    //                 itemlinelist.add(lineWarpper);
    //             }

    //             Set<String> productCodeSet = new Set<String>();
    //             for (CEU_HerokuEntity.NewRegisterWarrantyItemLine wi : itemlinelist) {
    //                 productCodeSet.add(wi.productModelNumber);
    //             }

    //             Map<String,Product2> id2ProductMap = new Map<String,Product2>();
    //             id2ProductMap = getproductMap(productCodeSet);

    //             List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>();
                
    //             Boolean isKit = false ; 
    //             if(itemlinelist.size() > 1){
    //                 isKit = TRUE;
    //             }
    //             List<Warranty_Item__c> warrantyItemHeaderList = new List<Warranty_Item__c>();
    //             Boolean emailFlag = false;
    //             for (CEU_HerokuEntity.NewRegisterWarrantyItemLine wi : itemlinelist) {
    //                 if(id2ProductMap.containsKey(wi.productModelNumber)){
    //                     Warranty_Item__c wt = new Warranty_Item__c();
    //                     if(!String.isEmpty(wi.recordId)){
    //                         wt.Id = wi.recordId;
    //                     }else{
    //                         wt.Consumer__c = wi.customerId;
    //                         wt.Registration_Date__c = System.today();
    //                         emailFlag = true;
    //                     }
    //                     wt.Product__c = id2ProductMap.get(wi.productModelNumber).Id;
                        
    //                     wt.Purchase_Date__c = transferPurchaseDate(wi.purchaseDate);
    //                     if(wi != null && String.isNotEmpty(wi.serialNumber) && wi.serialNumber != 'null') {
    //                         wt.Serial_Number__c = wi.serialNumber;
    //                     }
    //                     wt.Receipt_Link__c = wi.receiptUrl;
    //                     wt.Receipt_Status__c = wi.receiptStatus;
    //                     wt.Purchase_Place__c = wi.placeOfPurchase;//看下情况
    //                     if(wi.productModelNumber == wi.masterModelNumber && isKit){
    //                         wt.Is_Kit__c = true;
    //                         warrantyItemHeaderList.add(wt);
    //                     }else{
    //                         warrantyItemList.add(wt);
    //                     }
    //                     wt.Register_Source__c = 'Brand Website';
    //                     wt.Warranty_Status__c = 'In process';
    //                 }                   
    //             }
    //             String masterModelItemId = '';
    //             if(warrantyItemHeaderList.size()>0){

    //                 Database.upsert(warrantyItemHeaderList);
    //                 masterModelItemId = warrantyItemHeaderList[0].Id;

    //                 for(Warranty_Item__c wi :warrantyItemList){
    //                     wi.Parent_Warranty__c = masterModelItemId;
    //                 }
    //             }

    //             System.debug(LoggingLevel.INFO, '*** warrantyItemList: ' + warrantyItemList);
    //             Database.upsert(warrantyItemList);

    //             if(emailFlag){
    //                CEU_HerokuAPIUtils.sendNewWarrantyNoticeEmail(regWarRequest.customerId,residentialFlag);
    //             }
    //             return new CEU_HerokuEntity.ResponseEntity(200, '');
    //         }catch (Exception ex) {
    //             // 2022-06-29: 记录log
    //             logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
    //             insert logInfo;
    //             return new CEU_HerokuEntity.ResponseEntity(404, logInfo.Error_Message__c);
    //         }
    //         // 返回正确结果，用户的sfid
            
    //     }

    //     public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
    //         return null;
    //     }
    // }
    


    // 注册warranty处理类，考虑commercial用户如何调整（去除master model，铺平版20240509）
    public class RegisterWarrantyWithReceiptHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody) {
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'RegisterWarrantyWithReceiptHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            try{
                CEU_HerokuEntity.NewRegisterWarrantyRequestEntity regWarRequest = (CEU_HerokuEntity.NewRegisterWarrantyRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.NewRegisterWarrantyRequestEntity.class);
                System.debug(LoggingLevel.INFO, '*** regWarRequest: ' + regWarRequest);
                Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(regWarRequest.customerId);
                Boolean residentialFlag = true;
                if(acc == null){
                    acc = CEU_HerokuAPIUtils.getCommercialCompanyByID(regWarRequest.customerId);
                    residentialFlag = false;
                }
                if (String.isEmpty(regWarRequest.customerId) || acc == null) {
                    // 2022-06-29: 记录log
                    logInfo.Error_Message__c = 'Can not find the account.';
                    insert logInfo;

                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                }

                List<CEU_HerokuEntity.NewRegisterWarrantyItemLine> itemlinelist = new List<CEU_HerokuEntity.NewRegisterWarrantyItemLine>();
                Set<String> snSet = new Set<String>();
                Map<String,String> model2SNMap = new Map<String,String>();
                Map<String,String> sn2RecordIdMap = new Map<String,String>();

                //********-vince 校验regWarRequest.purchaseDate是否为空
                if(String.isEmpty(regWarRequest.purchaseDate)){
                    logInfo.Error_Message__c = 'Please fill in your purchase date.';
                    insert logInfo;
                    return CEU_HerokuAPIUtils.getErrorResponse(404, 'Please fill in your purchase date.');
                }

                for(CEU_HerokuEntity.WarrantyItemEntityNew line: regWarRequest.warrantyItems){
                    snSet.add(line.serialNumber);
                    if(line.recordId != '') {
                        sn2RecordIdMap.put(line.serialNumber,line.recordId);
                    }
                    model2SNMap.put(line.productModelNumber,line.serialNumber);
                }
                System.debug(LoggingLevel.INFO, '*** sn2RecordIdMap: ' + sn2RecordIdMap);
                List<Warranty_Item__c> duplicateWarrantyList = [SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,Is_Kit__c,
                                                Parent_Warranty__c, Expiration_Date_New__c,Receipt_Link__c,Update_Flag__c,
                                                Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c
                                            FROM Warranty_Item__c
                                            WHERE Serial_Number__c in:snSet];

                if(!duplicateWarrantyList.isEmpty()){
                    String errorSN = '';
                    Set<String> removeDupicateRecord = new Set<String>();
                    for(Warranty_Item__c wi:duplicateWarrantyList){
                        System.debug(LoggingLevel.INFO, '*** wi.Id: ' + wi.Id);
                        System.debug(LoggingLevel.INFO, '*** sn: ' + sn2RecordIdMap.get(wi.Serial_Number__c) );
                        if(sn2RecordIdMap.containsKey(wi.Serial_Number__c) && (sn2RecordIdMap.get(wi.Serial_Number__c) != wi.Id)){
                            if(!removeDupicateRecord.contains(wi.Serial_Number__c) ){
                                errorSN += wi.Serial_Number__c +',';
                                removeDupicateRecord.add(wi.Serial_Number__c);
                            }
                        }else if(snSet.contains(wi.Serial_Number__c) && !sn2RecordIdMap.containsKey(wi.Serial_Number__c)){
                            if(!removeDupicateRecord.contains(wi.Serial_Number__c) ){
                                errorSN += wi.Serial_Number__c +',';
                                removeDupicateRecord.add(wi.Serial_Number__c);
                            }
                        }
                    }
                    if(errorSN.endsWith(',')){
                        errorSN = errorSN.removeEnd(',');
                    }
                    if(errorSN != ''){
                        logInfo.Error_Message__c = 'The Serial Number has been registered before ' + errorSN ;
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, logInfo.Error_Message__c);
                    }
                }
                //需要校验SN Rule
                String snError = snVerify(model2SNMap,transferPurchaseDate(regWarRequest.purchaseDate));
                System.debug(LoggingLevel.INFO, '*** snError: ' + snError);
                if(snError != ''){
                    if(snError.endsWith(',')){
                        snError = snError.removeEnd(',');
                    }
                    snError = snError + ' is not correct.';
                    logInfo.Error_Message__c = snError ;
                    insert logInfo;
                    return CEU_HerokuAPIUtils.getErrorResponse(404, logInfo.Error_Message__c);
                }
                

                for(CEU_HerokuEntity.WarrantyItemEntityNew line: regWarRequest.warrantyItems){
                    CEU_HerokuEntity.NewRegisterWarrantyItemLine lineWarpper = new CEU_HerokuEntity.NewRegisterWarrantyItemLine();
                    // lineWarpper.productName;
                    lineWarpper.serialNumber = line.serialNumber;
                    lineWarpper.productModelNumber = line.productModelNumber;
                    lineWarpper.userId = regWarRequest.userId;
                    lineWarpper.customerId = regWarRequest.customerId;
                    lineWarpper.receiptStatus = regWarRequest.receiptStatus;
                    lineWarpper.receiptUrl = regWarRequest.receiptUrl;
                    lineWarpper.purchaseDate = regWarRequest.purchaseDate;
                    lineWarpper.placeOfPurchase = regWarRequest.placeOfPurchase;
                    // lineWarpper.masterModelNumber = regWarRequest.masterModelNumber;
                    lineWarpper.recordId = line.recordId;
                    itemlinelist.add(lineWarpper);
                }

                Set<String> productCodeSet = new Set<String>();
                for (CEU_HerokuEntity.NewRegisterWarrantyItemLine wi : itemlinelist) {
                    productCodeSet.add(wi.productModelNumber);
                }

                Map<String,Product2> id2ProductMap = new Map<String,Product2>();
                id2ProductMap = getproductMap(productCodeSet);

                List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>();
                List<Warranty_Item__c> warrantyItemHeaderList = new List<Warranty_Item__c>();
                Boolean emailFlag = false;
                for (CEU_HerokuEntity.NewRegisterWarrantyItemLine wi : itemlinelist) {
                    if(id2ProductMap.containsKey(wi.productModelNumber)){
                        Warranty_Item__c wt = new Warranty_Item__c();
                        if(!String.isEmpty(wi.recordId)){
                            wt.Id = wi.recordId;
                        }else{
                            wt.Consumer__c = wi.customerId;
                            wt.Registration_Date__c = System.today();
                            emailFlag = true;
                        }
                        wt.Product__c = id2ProductMap.get(wi.productModelNumber).Id;
                        
                        wt.Purchase_Date__c = transferPurchaseDate(wi.purchaseDate);
                        if(wi != null && String.isNotEmpty(wi.serialNumber) && wi.serialNumber != 'null') {
                            wt.Serial_Number__c = wi.serialNumber;
                        }
                        wt.Receipt_Link__c = wi.receiptUrl;
                        wt.Receipt_Status__c = wi.receiptStatus;
                        wt.Purchase_Place__c = wi.placeOfPurchase;
                        wt.Register_Source__c = 'Brand Website';
                        wt.Warranty_Status__c = 'In process';
                        warrantyItemList.add(wt);
                    }                   
                }
                System.debug(LoggingLevel.INFO, '*** warrantyItemList: ' + warrantyItemList);
                Database.upsert(warrantyItemList);
               
                if(emailFlag){
                   CEU_HerokuAPIUtils.sendNewWarrantyNoticeEmail(regWarRequest.customerId,residentialFlag);
                }
                logInfo.ResParam__c = '200';
                insert logInfo;
                return new CEU_HerokuEntity.ResponseEntity(200, '');
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;
                return new CEU_HerokuEntity.ResponseEntity(404, logInfo.Error_Message__c);
            }
            // 返回正确结果，用户的sfid
            
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    public Date  transferPurchaseDate(String purdate){
        Date purchaseDate = (String.isNotEmpty(purdate) && purdate.length() == 6)
                                   ? Date.valueOf('20' + purdate.substring(4, 6) + '-' +
                                   purdate.substring(0, 2) + '-' +
                                   purdate.substring(2, 4)) : null;
        return purchaseDate;

    }

    public string snVerify(Map<String,String> model2SNMap, Date purchaseDate){
        Boolean flag = true;
        List<String> errList = new List<String>();
        //2-5位校验
        String snError = '';
        Map<String,String> model2CodeMap = new Map<String,String>();
        for (Warranty_Rules__c warrantyRule : [SELECT Id, Product_Model__c, Code_in_Serial__c
                                               FROM Warranty_Rules__c
                                               WHERE Product_Model__c in: model2SNMap.keySet() AND RecordType.developerName = 'Model_Code' AND Product_Model__c != null AND Code_in_Serial__c != null]){
            model2CodeMap.put(warrantyRule.Product_Model__c,warrantyRule.Code_in_Serial__c);
        }
        for(String key : model2SNMap.keySet()){
            if(model2CodeMap.containsKey(key)){
                String sn = model2SNMap.get(key);
                //校验2-5位
                String modelCodeSN = String.isNotBlank(sn) ? sn.substring(1, 5) : '';
                if(modelCodeSN != model2CodeMap.get(key)){
                    snError += sn + ',';
                }
                //校验购买日期6-9位
                String getDate = String.isNotBlank(sn) ? sn.substring(5, 9) : '';
                String year = '20' + getDate.substring(0, 2);
                Integer days = Integer.valueOf(getDate.substring(2, 4)) * 7;
                Date firstMonday = Date.newInstance(Integer.valueOf(year), 1, 1).toStartOfWeek().addDays(7);
                Date trueDate = firstMonday.addDays(days).addDays(6);
                if (trueDate > purchaseDate){
                    snError += sn + ',';
                }
            }else{
                snError += model2SNMap.get(key) + ',';
            }
        }
        return snError;
    }

    public class RemoveWarrantyHandler extends CEU_HerokuAPIHandler{
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'RemoveWarrantyHandler';
            logInfo.Method__c = 'handlePost';
            logInfo.ReqParam__c = requestBody;

            try{
                CEU_HerokuEntity.RemoveWarrantyRequestEntity regWarRequest = (CEU_HerokuEntity.RemoveWarrantyRequestEntity) JSON.deserialize(requestBody, CEU_HerokuEntity.RemoveWarrantyRequestEntity.class);
                List<Warranty_Item__c> wiDelList = new List<Warranty_Item__c>();
                if(String.isNotEmpty(regWarRequest.recordId)){
                    List<Warranty_Item__c> wiList = [SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,Is_Kit__c,
                                                Parent_Warranty__c,Update_Flag__c,
                                                Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c,(SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,
                                                Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c FROM Child_Warranties__r)
                                                FROM Warranty_Item__c
                                                WHERE Id =: regWarRequest.recordId];
                    if (wiList == null) {
                        logInfo.Error_Message__c = 'Can not find the warranty.';
                        insert logInfo;
                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the warranty.');
                    }else{
                        for(Warranty_Item__c wi: wiList){
                            // if(wi.Update_Flag__c) {//冗余字段20240517移除
                                wiDelList.add(wi);
                            // }
                            // for(Warranty_Item__c ww : wi.Child_Warranties__r){
                            //     wiDelList.add(ww);
                            // }
                        }
                        if(wiDelList.size() > 0 ){
                            Database.delete(wiDelList);
                        }else{
                            logInfo.Error_Message__c = 'Can not remove this warranty.';
                            insert logInfo;
                            return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not remove this warranty.');
                        }
                        
                    }
                }
                
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;

                return CEU_HerokuAPIUtils.upsertFailedResponse(ex.getMessage());
            }

            //返回正确结果，用户的sfid
            return new CEU_HerokuEntity.ResponseEntity(200, '');
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap){
            return null;
        }
    }

    public static Map<String,Product2> getproductMap (Set<String> productCodeSet){
        List<Product2> prodList = [SELECT Id,Order_Model__c
                                    FROM Product2 
                                    WHERE Order_Model__c in: productCodeSet];
        Map<String,Product2> id2ProductMap = new Map<String,Product2>();
        for(Product2 pro: prodList){
            id2ProductMap.put(pro.Order_Model__c,pro);
        }
        return id2ProductMap;
    }

    // 批量查询warranty处理类，非德国用户
    public class SelectWarrantyHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String customerId = headerMap.get('customerId');

            // 如果CustomerId为空，返回错误信息
            if (String.isEmpty(customerId)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }
            // 如果Account为null，返回错误信息
            Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(customerId);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }

            //获取用户名下注册的warranty信息
            List<CEU_HerokuEntity.RCWarrantyEntity> warrantyList = new List<CEU_HerokuEntity.RCWarrantyEntity>();
            for(Warranty_Item__c warranty : [SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,Is_Kit__c,
                                                Parent_Warranty__c,
                                                Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c,(SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,
                                                Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c FROM Child_Warranties__r)
                                            FROM Warranty_Item__c
                                            WHERE Consumer__c =: customerId]){

                CEU_HerokuEntity.RCWarrantyEntity tempWarranty = new CEU_HerokuEntity.RCWarrantyEntity();
                tempWarranty.receipt         = 'FALSE';
                tempWarranty.PurchaseDate    = (warranty.Purchase_Date__c == null) ? '': DateTime.newInstance(warranty.Purchase_Date__c, Time.newInstance(0,0,0,0)).format('MMddyy');
                tempWarranty.ProductUseType  = (warranty.Product_Use_Type__c == null) ? warranty.Product_Use_Type2__c:warranty.Product_Use_Type__c;
                tempWarranty.PlaceOfPurchase =  warranty.Purchase_Place__c;
                tempWarranty.Kit             =  warranty.Is_Kit__c ?'TRUE':'FALSE';
                tempWarranty.masterModelNumber = warranty.Product__r.Master_Product__c;
                tempWarranty.masterProductName = warranty.Product__r.Item_Description_EN__c;
                // 封装warrantyItem信息
                List<CEU_HerokuEntity.RCWarrantyItemEntity> warrantyItemList = new List<CEU_HerokuEntity.RCWarrantyItemEntity>();
                //儿子组装
                for (Warranty_Item__c wiItem : warranty.Child_Warranties__r) {
                    CEU_HerokuEntity.RCWarrantyItemEntity warrantyItem = new CEU_HerokuEntity.RCWarrantyItemEntity();
                    warrantyItem.productType        = wiItem.Product_Type__c;
                    warrantyItem.productName        = wiItem.Product__r.Item_Description_EN__c;
                    warrantyItem.serialNumber       = wiItem.Serial_Number__c;
                    warrantyItem.productModelNumber = wiItem.Product__r.Master_Product__c;
                    warrantyItemList.add(warrantyItem);
                }
                //父亲自己
                CEU_HerokuEntity.RCWarrantyItemEntity warrantyItem1 = new CEU_HerokuEntity.RCWarrantyItemEntity();
                warrantyItem1.productType        = warranty.Product_Type__c;
                warrantyItem1.productName        = warranty.Product__r.Item_Description_EN__c;
                warrantyItem1.serialNumber       = warranty.Serial_Number__c;
                warrantyItem1.productModelNumber = warranty.Product__r.Master_Product__c;
                warrantyItemList.add(warrantyItem1);
                // 将warrantyItem封装入warranty中
                tempWarranty.warrantyItems = warrantyItemList;
                if(warranty.Parent_Warranty__c == null ){
                    warrantyList.add(tempWarranty);
                }
            }
            System.debug(LoggingLevel.INFO, '*** warrantyList: ' + warrantyList);
            //返回正确结果，warranty的List集合
            return new CEU_HerokuEntity.RCWarrantyListResponseEntity(200, '', warrantyList);
        }
    }
    // // 批量查询warranty处理类，德国Residential 用户 -- 待改
    // public class SelectWarrantyREHandler extends CEU_HerokuAPIHandler {
    //     public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
    //         return null;
    //     }

    //     public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
    //         String customerId = headerMap.get('customerId');

    //         // 如果CustomerId为空，返回错误信息
    //         if (String.isEmpty(customerId)) {
    //             return CEU_HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
    //         }
    //         // 如果Account为null，返回错误信息
    //         Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(customerId);
    //         if (acc == null) {
    //             return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
    //         }

    //         //获取用户名下注册的warranty信息
    //         List<CEU_HerokuEntity.DEWarrantyEntity> warrantyList = new List<CEU_HerokuEntity.DEWarrantyEntity>();
    //         for(Warranty_Item__c warranty : [SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,Is_Kit__c,
    //                                             Parent_Warranty__c, Expiration_Date_New__c,Receipt_Link__c,Update_Flag__c,
    //                                             Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c,
    //                                             (SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,
    //                                                 Product_Use_Type2__c,Expiration_Date_New__c,Receipt_Link__c,Update_Flag__c,
    //                                             Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c FROM Child_Warranties__r)
    //                                         FROM Warranty_Item__c
    //                                         WHERE Consumer__c =: customerId]){

    //             CEU_HerokuEntity.DEWarrantyEntity tempWarranty = new CEU_HerokuEntity.DEWarrantyEntity();
    //             tempWarranty.receipt         = warranty.Receipt_Link__c ;
    //             tempWarranty.PurchaseDate    = (warranty.Purchase_Date__c == null) ? '': DateTime.newInstance(warranty.Purchase_Date__c, Time.newInstance(0,0,0,0)).format('MMddyy');
    //             tempWarranty.updateFlag  = warranty.Update_Flag__c;
    //             tempWarranty.Kit             =  warranty.Is_Kit__c ?'TRUE':'FALSE';
    //             tempWarranty.masterModelNumber = warranty.Product__r.Master_Product__c;
    //             tempWarranty.masterProductName = warranty.Product__r.Item_Description_EN__c;
    //             tempWarranty.placeOfPurchase = warranty.Purchase_Place__c;
    //             // 封装warrantyItem信息
    //             List<CEU_HerokuEntity.DEWarrantyItemEntity> warrantyItemList = new List<CEU_HerokuEntity.DEWarrantyItemEntity>();
    //             //儿子组装
    //             for (Warranty_Item__c wiItem : warranty.Child_Warranties__r) {
    //                 CEU_HerokuEntity.DEWarrantyItemEntity warrantyItem = new CEU_HerokuEntity.DEWarrantyItemEntity();
    //                 // warrantyItem.productType        = wiItem.Product_Type__c;
    //                 warrantyItem.productName        = wiItem.Product__r.Item_Description_EN__c;
    //                 warrantyItem.serialNumber       = wiItem.Serial_Number__c;
    //                 warrantyItem.productModelNumber = wiItem.Product__r.Master_Product__c;
    //                 warrantyItem.expiredDate        = dateTransfer(wiItem.Expiration_Date_New__c);
    //                 // warrantyItem.placeOfPurchase    = wiItem.Purchase_Place__c;
    //                 warrantyItem.recordId           = wiItem.Id;
    //                 warrantyItemList.add(warrantyItem);
    //             }
    //             //父亲自己
    //             CEU_HerokuEntity.DEWarrantyItemEntity warrantyItem1 = new CEU_HerokuEntity.DEWarrantyItemEntity();
    //             // warrantyItem1.productType        = warranty.Product_Type__c;
    //             warrantyItem1.productName        = warranty.Product__r.Item_Description_EN__c;
    //             warrantyItem1.serialNumber       = warranty.Serial_Number__c;
    //             warrantyItem1.productModelNumber = warranty.Product__r.Master_Product__c;
    //             warrantyItem1.expiredDate        = dateTransfer(warranty.Expiration_Date_New__c);
    //             warrantyItem1.recordId           = warranty.Id;
    //             // warrantyItem1.placeOfPurchase    = warranty.Purchase_Place__c;
    //             warrantyItemList.add(warrantyItem1);
    //             // 将warrantyItem封装入warranty中
    //             tempWarranty.warrantyItems = warrantyItemList;
    //             if(warranty.Parent_Warranty__c == null ){
    //                 warrantyList.add(tempWarranty);
    //             }
    //         }
    //         System.debug(LoggingLevel.INFO, '*** warrantyList: ' + warrantyList);
    //         //返回正确结果，warranty的List集合
    //         return new CEU_HerokuEntity.DEWarrantyListResponseEntity(200, '', warrantyList);
    //     }
    // }

    // 批量查询warranty处理类，德国Residential 用户 -- 20240509改为平铺结构
    public class SelectWarrantyREHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String customerId = headerMap.get('customerId');

            // 如果CustomerId为空，返回错误信息
            if (String.isEmpty(customerId)) {
                return CEU_HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
            }
            // 如果Account为null，返回错误信息
            Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(customerId);
            if (acc == null) {
                return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
            }
            CEU_HerokuEntity.DEWarrantyEntity tempWarranty = new CEU_HerokuEntity.DEWarrantyEntity();
             // 封装warrantyItem信息
            List<CEU_HerokuEntity.DEWarrantyItemEntity> warrantyItemList = new List<CEU_HerokuEntity.DEWarrantyItemEntity>();
            //获取用户名下注册的warranty信息
            List<CEU_HerokuEntity.DEWarrantyEntity> warrantyList = new List<CEU_HerokuEntity.DEWarrantyEntity>();
            for(Warranty_Item__c warranty : [SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,Is_Kit__c,
                                                Parent_Warranty__c, Expiration_Date_New__c,Receipt_Link__c,Update_Flag__c,
                                                Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c
                                            FROM Warranty_Item__c
                                            WHERE Consumer__c =: customerId]){
               
                //儿子组装
                CEU_HerokuEntity.DEWarrantyItemEntity warrantyItem = new CEU_HerokuEntity.DEWarrantyItemEntity();
                // warrantyItem.productType        = wiItem.Product_Type__c;
                warrantyItem.productName        = warranty.Product__r.Item_Description_EN__c;
                warrantyItem.serialNumber       = warranty.Serial_Number__c;
                warrantyItem.productModelNumber = warranty.Product__r.Master_Product__c;
                warrantyItem.expiredDate        = dateTransfer(warranty.Expiration_Date_New__c);
                // warrantyItem.placeOfPurchase    = wiItem.Purchase_Place__c;
                warrantyItem.recordId           = warranty.Id;
                warrantyItem.receipt         = warranty.Receipt_Link__c ;
                warrantyItem.PurchaseDate    = (warranty.Purchase_Date__c == null) ? '': DateTime.newInstance(warranty.Purchase_Date__c, Time.newInstance(0,0,0,0)).format('MMddyy');
                warrantyItem.updateFlag  = warranty.Update_Flag__c;
                warrantyItem.placeOfPurchase = warranty.Purchase_Place__c;
                warrantyItemList.add(warrantyItem);
                // 将warrantyItem封装入warranty中
               
                
            }
            tempWarranty.warrantyItems = warrantyItemList;
            warrantyList.add(tempWarranty);
            System.debug(LoggingLevel.INFO, '*** warrantyList: ' + tempWarranty);
            //返回正确结果，warranty的List集合
            return new CEU_HerokuEntity.DEWarrantyListResponseEntity(200, '', warrantyList);
        }
    }


    // 批量查询warranty处理类，commercial用户 -- 待改
    // public class SelectWarrantyCOHandler extends CEU_HerokuAPIHandler {
    //     public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
    //         return null;
    //     }

    //     public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
    //         String customerId = headerMap.get('customerId');

    //         // 如果CustomerId为空，返回错误信息
    //         if (String.isEmpty(customerId)) {
    //             return CEU_HerokuAPIUtils.getErrorResponse(422, 'CustomerID cannot be empty.');          
    //         }
    //         // 如果Account为null，返回错误信息
    //         Account acc = CEU_HerokuAPIUtils.getResidentialUserByID(customerId);
    //         if (acc == null) {
    //             return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
    //         }

    //         //获取用户名下注册的warranty信息
    //         List<CEU_HerokuEntity.RCWarrantyEntity> warrantyList = new List<CEU_HerokuEntity.RCWarrantyEntity>();
    //         for(Warranty_Item__c warranty : [SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,Is_Kit__c,
    //                                             Parent_Warranty__c,
    //                                             Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c,(SELECT Id, Purchase_Date__c,Purchase_Place__c ,Product__r.Master_Product__c,Product_Use_Type__c,Product_Use_Type2__c,
    //                                             Product__r.Name,Serial_Number__c, Product_Type__c, Product__r.Item_Description_EN__c FROM Child_Warranties__r)
    //                                         FROM Warranty_Item__c
    //                                         WHERE Consumer__c =: customerId]){

    //             CEU_HerokuEntity.RCWarrantyEntity tempWarranty = new CEU_HerokuEntity.RCWarrantyEntity();
    //             tempWarranty.receipt         = 'FALSE';
    //             tempWarranty.PurchaseDate    = (warranty.Purchase_Date__c == null) ? '': DateTime.newInstance(warranty.Purchase_Date__c, Time.newInstance(0,0,0,0)).format('MMddyy');
    //             tempWarranty.ProductUseType  = (warranty.Product_Use_Type__c == null) ? warranty.Product_Use_Type2__c:warranty.Product_Use_Type__c;
    //             tempWarranty.PlaceOfPurchase =  warranty.Purchase_Place__c;
    //             tempWarranty.Kit             =  warranty.Is_Kit__c ?'TRUE':'FALSE';
    //             tempWarranty.masterModelNumber = warranty.Product__r.Master_Product__c;
    //             tempWarranty.masterProductName = warranty.Product__r.Item_Description_EN__c;
    //             // 封装warrantyItem信息
    //             List<CEU_HerokuEntity.RCWarrantyItemEntity> warrantyItemList = new List<CEU_HerokuEntity.RCWarrantyItemEntity>();
    //             //儿子组装
    //             for (Warranty_Item__c wiItem : warranty.Child_Warranties__r) {
    //                 CEU_HerokuEntity.RCWarrantyItemEntity warrantyItem = new CEU_HerokuEntity.RCWarrantyItemEntity();
    //                 warrantyItem.productType        = wiItem.Product_Type__c;
    //                 warrantyItem.productName        = wiItem.Product__r.Item_Description_EN__c;
    //                 warrantyItem.serialNumber       = wiItem.Serial_Number__c;
    //                 warrantyItem.productModelNumber = wiItem.Product__r.Master_Product__c;
    //                 warrantyItemList.add(warrantyItem);
    //             }
    //             //父亲自己
    //             CEU_HerokuEntity.RCWarrantyItemEntity warrantyItem1 = new CEU_HerokuEntity.RCWarrantyItemEntity();
    //             warrantyItem1.productType        = warranty.Product_Type__c;
    //             warrantyItem1.productName        = warranty.Product__r.Item_Description_EN__c;
    //             warrantyItem1.serialNumber       = warranty.Serial_Number__c;
    //             warrantyItem1.productModelNumber = warranty.Product__r.Master_Product__c;
    //             warrantyItemList.add(warrantyItem1);
    //             // 将warrantyItem封装入warranty中
    //             tempWarranty.warrantyItems = warrantyItemList;
    //             if(warranty.Parent_Warranty__c == null ){
    //                 warrantyList.add(tempWarranty);
    //             }
    //         }
    //         System.debug(LoggingLevel.INFO, '*** warrantyList: ' + warrantyList);
    //         //返回正确结果，warranty的List集合
    //         return new CEU_HerokuEntity.RCWarrantyListResponseEntity(200, '', warrantyList);
    //     }
    // }

    // 发送邮件处理类
    public class SendEmailHandler extends CEU_HerokuAPIHandler {
        public override CEU_HerokuEntity.ResponseEntity handlePost(String requestBody){
            return null;
        }

        public override CEU_HerokuEntity.ResponseEntity handleGet(Map<String, String> headerMap) {
            String customerEmail = headerMap.get('customerEmail');
            // 2022-06-29: 新增记录Log
            logInfo.ApexName__c = 'SendEmailHandler';
            logInfo.Method__c = 'handleGet';
            logInfo.ReqParam__c = JSON.serialize(headerMap);

            try{
                String emailType = headerMap.get('emailType');
                String jwtToken = headerMap.get('jwtToken');
                //String brandName = headerMap.get('brandName');
                String brandName = 'EGO';//欧洲只有EGO类型
                String contactLanguage = headerMap.get('language');

                // 如果CustomerId为空，返回错误信息
                if (String.isEmpty(customerEmail)) {
                    // 2022-06-29: 记录log
                    logInfo.Error_Message__c = 'customerEmail cannot be empty.';
                    insert logInfo;

                    return CEU_HerokuAPIUtils.getErrorResponse(422, 'customerEmail cannot be empty.');          
                }
                // 如果Account为null，返回错误信息
                Account acc = new Account();
                if (emailType.equalsIgnoreCase('reset')){
                    acc = CEU_HerokuAPIUtils.getUserByEmailAndBrandName(customerEmail,brandName);//考虑commercial用户
                    if(acc == null){
                        logInfo.Error_Message__c = 'Can not find the account.';
                        insert logInfo;

                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                    }
                }
                Contact con = new Contact();
                 if (emailType.equalsIgnoreCase('resetCommercial')){
                    con = CEU_HerokuAPIUtils.getContactByEmail(customerEmail);//考虑commercial用户
                    if(con == null){
                        logInfo.Error_Message__c = 'Can not find the account.';
                        insert logInfo;

                        return CEU_HerokuAPIUtils.getErrorResponse(404, 'Can not find the account.');
                    }
                }
                
                if (emailType.equalsIgnoreCase('reset')) {
                    CEU_HerokuAPIUtils.sendResetPasswordEmail(acc, jwtToken, brandName);
                } else if (emailType.equalsIgnoreCase('resetCommercial')) {
                    CEU_HerokuAPIUtils.sendResetPasswordContactEmail(con, jwtToken, brandName,contactLanguage);
                } else if (emailType.equalsIgnoreCase('migrate')) {
                    if(acc == null){
                        CEU_HerokuAPIUtils.sendMigrateAccountEmail(acc, jwtToken);
                    }else if(con != null){
                        CEU_HerokuAPIUtils.sendMigrateContactEmail(con, jwtToken);
                    }
                } else if (emailType.equalsIgnoreCase('resetIoT')) {
                    CEU_HerokuAPIUtils.sendIoTResetPasswordEmail(acc, jwtToken);
                } else {
                    // 2022-06-29: 记录log
                    logInfo.Error_Message__c = 'Can\'t detect the email_type from the request';
                    insert logInfo;

                    return CEU_HerokuAPIUtils.getErrorResponse(422, 'Can\'t detect the email_type from the request');
                }
            }catch (Exception ex) {
                // 2022-06-29: 记录log
                logInfo.Error_Message__c = ex.getMessage() + '------' + ex.getStackTraceString();
                insert logInfo;
            }
            //返回正确结果，warranty的List集合
            return new CEU_HerokuEntity.ResponseEntity(200, '');
        }
    }

    public String dateTransfer(Date expiredDate){
        if(expiredDate != null){
            String year = String.valueOf(expiredDate.year());
            year = year.substring(2,4);
            String month = String.valueOf(expiredDate.month());
            if(month.length() <2 ){
                month = '0' + month;
            }
            String day = String.valueOf(expiredDate.day());
             if(day.length() <2 ){
                day = '0' + day;
            }
            return month + day +year ;
        }else{
            return '000000';//MMDDYY
        }
    }
}