/**********************************************************************
 *
 *
 * @url: /services/apexrest/CCM_RestService_DealStockOrderReturnInfo
 * @data:
 *
 *
 *************************************************************************/
@RestResource(urlMapping = '/CCM_RestService_DealStockOrderReturnInfo')
global without sharing class CCM_RestService_DealStockOrderReturnInfo {
    global static final String SCENE_TYPE_STOCK = 'Stock Request';
    global static final String SCENE_TYPE_RETURN_WAREHOUSE = 'Return Warehouse';

    public static final Id RecordType_C_STOCK = Schema.SObjectType.Sales_Rep_Stock__c.getRecordTypeInfosByDeveloperName().get('Chervon_Stock').getRecordTypeId();
    public static final Id RecordType_SALES_REP_STOCK = Schema.SObjectType.Sales_Rep_Stock__c.getRecordTypeInfosByDeveloperName().get('Sales_Rep_Stock').getRecordTypeId();

    global CCM_RestService_DealStockOrderReturnInfo() {
        
    }
    @HttpPost
    global static ResultObj doPost(){
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        resObj.Process_Result = new List<ReturnItem>();
        String reqParam = req.requestBody.toString();
        String sceneType = '';
        List<Sales_Rep_Stock_Header__c> lstStockHead = new List<Sales_Rep_Stock_Header__c>();
        try{
            System.debug(LoggingLevel.INFO, '*** req.requestBody.toString(): ' + req.requestBody.toString());
            if (!reqParam.startsWith('[')){
                reqParam = '[' + reqParam + ']';
            }
            reqObjList = parse(reqParam);

            System.debug(LoggingLevel.INFO, '*** req.requestBody()3：' + reqObjList);

            if (reqObjList != null && reqObjList.size() > 0){
                //获取订单场景：Stock Request、Return Warehouse
                sceneType = reqObjList[0].SCENE_TYPE;
                resObj =  upsertStockOrder(reqObjList,sceneType);
                if (resObj.Process_Result.size() == 0){
                    resObj.Process_Status = 'Success';
                    String logId = Util.logIntegration(sceneType + ' Log', 'CCM_RestService_DealStockOrderReturnInfo', 'POST', JSON.serialize(resObj.Process_Result), reqParam, JSON.serialize(resObj));
                } else{
                    resObj.Process_Status = 'Fail';
                    String logId = Util.logIntegration(sceneType+ ' Exception', 'CCM_RestService_DealStockOrderReturnInfo', 'POST', JSON.serialize(resObj.Process_Result), reqParam, JSON.serialize(resObj));
                    Util.pushExceptionEmail('Accept Modifier Info', logId, getMailErrorMessage(resObj));

                }

                System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));
            } else{
                resObj.Process_Status = 'Fail';
                ReturnItem empty = new ReturnItem();
                empty.Error_Message = 'Empty JSON';
                empty.Error_Detail = 'Empty JSON';
                resObj.Process_Result.add(empty);

                String logId = Util.logIntegration(sceneType + ' Exception', 'CCM_RestService_DealStockOrderReturnInfo', 'POST', JSON.serialize(resObj.Process_Result), JSON.serialize(reqObjList), JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept ' + sceneType+ ' Info', logId, getMailErrorMessage(resObj));

            }
        } catch (Exception e){

            resObj.Process_Status = 'Fail';
            ReturnItem returnItem = new ReturnItem();
            returnItem.Type = sceneType;
            returnItem.Error_Message = 'This '+sceneType+' was failed saving in Salesforce';
            returnItem.Error_Detail = '*** e.getMessage(): ' + e.getLineNumber() + ' line e.getMessage(): ' + e.getMessage();
            resObj.Process_Result.add(returnItem);
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): ' + e.getLineNumber() + ' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration(sceneType + ' Exception', 'CCM_RestService_DealStockOrderReturnInfo', 'POST', JSON.serialize(resObj.Process_Result), reqParam, e.getMessage());
            Util.pushExceptionEmail('Accept ' + sceneType + ' Info', logId, getMailErrorMessage(resObj));
            return resObj;

        }
        System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
        return resObj;
    }

    global static String getMailErrorMessage(ResultObj res){
        String errContent = '';
        errContent += 'Process Status : Fail<br/><br/>';
        if (res.Process_Result.size() > 0){
            for (ReturnItem Item : res.Process_Result){
                errContent += 'External ID : ' + Item.ExternalID + '<br/>';
                errContent += 'Error Message : ' + Item.Error_Message + '<br/>';
                errContent += 'Error Detail : ' + Item.Error_Detail + '<br/><br/>';
            }
        }
        return errContent;
    }

    global static ResultObj upsertStockOrder(List<ReqestObj> reqObjList,String sceneType){
        ResultObj resObj = new ResultObj();
        resObj.Process_Result = new List<ReturnItem>();
        List<Sales_Rep_Stock_Header__c> lstHeader = new List<Sales_Rep_Stock_Header__c>();
        List<Sales_Rep_Stock__c> lstLine = new List<Sales_Rep_Stock__c>();
        Set<String> setWareHouseItem  = new Set<String>();
        Set<String> setStockItem  = new Set<String>();
        //获取已存在的Product和kit&item
        List<Product2> lstProduct = new List<Product2>();
        Map<String,String> mapModelToId = new Map<String,String>();
        lstProduct = [Select Id,Order_Model__c From Product2];
        if(lstProduct.size() > 0){
            for(Product2 p : lstProduct){
                mapModelToId.put(p.Order_Model__c,p.Id);
            }
        }
        //映射Kit Model 到Tools
        List<Kit_Item__c> lstKitItem = new List<Kit_Item__c>();
        Map<String,List<Kit_Item__c>> mapKitModelToKI = new Map<String,List<Kit_Item__c>>();
        lstKitItem = [Select Kit__c,Kit__r.Order_Model__c, VK_Product__c ,VK_Product__r.Order_Model__c,Quantity__c  from Kit_Item__c where Kit__c != null ];
        if(lstKitItem.size() > 0){
            for(Kit_Item__c ki : lstKitItem){
                if(!mapKitModelToKI.containsKey(ki.Kit__r.Order_Model__c)){
                    List<Kit_Item__c> lstKi = new List<Kit_Item__c>();
                    lstKi.add(ki);
                    mapKitModelToKI.put(ki.Kit__r.Order_Model__c,lstKi);
                }else{
                    List<Kit_Item__c> lstExsitKi =  mapKitModelToKI.get(ki.Kit__r.Order_Model__c);
                    lstExsitKi.add(ki);
                    mapKitModelToKI.put(ki.Kit__r.Order_Model__c,lstExsitKi);
                }
            }
        }
        
        Map<String, String> stockRequestSalesRepMap = new Map<String, String>();
        Map<String, String> stockRequestIdMap = new Map<String, String>();
        getStockRequestSalesRepMap(reqObjList, stockRequestSalesRepMap, stockRequestIdMap);

        //遍历父对象记录
        for(ReqestObj reqObj : reqObjList){
            Sales_Rep_Stock_Header__c headerData = new Sales_Rep_Stock_Header__c();
            headerData.Move_Order_OracleID__c = reqObj.MOVE_ORDER_ORACLE_ID;
            System.debug('headerData.Move_Order_OracleID__c===>'+headerData.Move_Order_OracleID__c);
            headerData.Move_Order_OracleNumber__c = reqObj.MOVE_ORDER_ORACLE_NUMBER;
            headerData.Request_Number_CRM__c = reqObj.REQUEST_NUMBER_CRM;
            headerData.Scene_Type__c = reqObj.SCENE_TYPE;
            //判断场景,绑定lookup 记录
            if(headerData.Scene_Type__c == SCENE_TYPE_STOCK){
                // headerData.Stock_Request__c = reqObj.REQUEST_NUMBER_CRM;
                if(stockRequestIdMap.containsKey(reqObj.REQUEST_NUMBER_CRM)) {
                    headerData.Stock_Request__c = stockRequestIdMap.get(reqObj.REQUEST_NUMBER_CRM);
                }
            }else if(headerData.Scene_Type__c == SCENE_TYPE_RETURN_WAREHOUSE){
                headerData.Return_Warehouse__c = reqObj.REQUEST_NUMBER_CRM;
            }
            headerData.Move_Order_Date__c = String.isNotBlank(reqObj.MOVE_ORDER_DATE) ? Date.valueOf(reqObj.MOVE_ORDER_DATE) : null;
            lstHeader.add(headerData);
            String requestFor = '';
            if(stockRequestSalesRepMap.containsKey(reqObj.REQUEST_NUMBER_CRM)) {
                requestFor = stockRequestSalesRepMap.get(reqObj.REQUEST_NUMBER_CRM);
            }
            //遍历子对象记录
            System.debug('reqObj.OrderLine.size()=======>'+reqObj.OrderLine.size());
            if(reqObj.OrderLine != null && reqObj.OrderLine.size() > 0){
                List<OrderLine> lstLineData = reqObj.OrderLine;
                for(OrderLine lineData : lstLineData){
                    //若不为Kit，直接创建
                    if(!mapKitModelToKI.containsKey(lineData.PRODUCT_MODEL)){
                        Sales_Rep_Stock__c ol = new Sales_Rep_Stock__c();
                        ol.Transaction_Id__c = lineData.TRANSACTION_ID;
                        ol.Move_Order_Oracle_Id__c =  lineData.MOVE_ORDER_ORACLE_ID;
                        ol.Move_OrderLine_Number__c = lineData.MOVE_ORDERLINE_NUMBER;
                        ol.Move_OrderLine_OracleId__c = lineData.MOVE_ORDERLINE_ORACLE_ID;
                        //判断场景,行上绑定lookup 记录
                        if(headerData.Scene_Type__c == SCENE_TYPE_STOCK){
                            //stock request -> Sales rep stock
                            ol.Stock_Request_Item__c = lineData.MOVE_ORDERLINE_CRM_ID;
                            ol.RecordTypeId = RecordType_SALES_REP_STOCK;
                        }else if(headerData.Scene_Type__c == SCENE_TYPE_RETURN_WAREHOUSE){
                            //warehouse -> C-Stock
                            ol.Return_Warehouse_Item__c = lineData.MOVE_ORDERLINE_CRM_ID;
                            ol.RecordTypeId = RecordType_C_STOCK;
                            setWareHouseItem.add(lineData.MOVE_ORDERLINE_CRM_ID);
                        }
                        ol.Move_Ship_Date__c = String.isNotBlank(lineData.SHIPPED_DATE) ? Date.valueOf(lineData.SHIPPED_DATE) : null;
                        ol.Model__c = lineData.PRODUCT_MODEL;
                        System.debug('lineData.PRODUCT_MODE======>'+lineData.PRODUCT_MODEL);
                        ol.Product_Description__c = mapModelToId.containsKey(lineData.PRODUCT_MODEL) ? mapModelToId.get(lineData.PRODUCT_MODEL) : '';
                        if(lineData.SERIAL_NUMBER_FLAG == 'N'){
                            ol.Serial_Number_Flag__c = 'NO';
                        }else if(lineData.SERIAL_NUMBER_FLAG == 'Y'){
                            ol.Serial_Number_Flag__c = 'YES';
                        }
                        ol.SerialNumber__c = lineData.SERIAL_NUMBER;
                        if(String.isNotBlank(requestFor)) {
                            ol.Request_For__c = requestFor;
                        }
                        lstLine.add(ol);
                    }else {
                        //若为Kit，查询tools 及 Qty，拆成SN维度
                        List<Kit_Item__c> lstItem = mapKitModelToKI.get(lineData.PRODUCT_MODEL);
                        for(Kit_Item__c item : lstItem){
                            for(Integer i = 0; i < item.Quantity__c ; i++){
                                Sales_Rep_Stock__c ol = new Sales_Rep_Stock__c();
                                ol.Transaction_Id__c = lineData.TRANSACTION_ID + '-' +item.VK_Product__r.Order_Model__c;
                                ol.Move_Order_Oracle_Id__c =  lineData.MOVE_ORDER_ORACLE_ID;
                                ol.Move_OrderLine_Number__c = lineData.MOVE_ORDERLINE_NUMBER;
                                ol.Move_OrderLine_OracleId__c = lineData.MOVE_ORDERLINE_ORACLE_ID;
                                //判断场景,行上绑定lookup 记录
                                if(headerData.Scene_Type__c == SCENE_TYPE_STOCK){
                                    //stock request -> Sales rep stock
                                    ol.Stock_Request_Item__c = lineData.MOVE_ORDERLINE_CRM_ID;
                                    ol.RecordTypeId = RecordType_SALES_REP_STOCK;
                                }else if(headerData.Scene_Type__c == SCENE_TYPE_RETURN_WAREHOUSE){
                                    //warehouse -> C-Stock
                                    ol.Return_Warehouse_Item__c = lineData.MOVE_ORDERLINE_CRM_ID;
                                    ol.RecordTypeId = RecordType_C_STOCK;
                                    setWareHouseItem.add(lineData.MOVE_ORDERLINE_CRM_ID);
                                }
                                ol.Move_Ship_Date__c = String.isNotBlank(lineData.SHIPPED_DATE) ? Date.valueOf(lineData.SHIPPED_DATE) : null;
                                //Kit转化Tool，生成记录
                                System.debug('item.VK_Product__c===>'+item.VK_Product__c);
                                ol.Model__c = item.VK_Product__r.Order_Model__c;
                                ol.Product_Description__c = mapModelToId.containsKey(item.VK_Product__r.Order_Model__c) ? mapModelToId.get(item.VK_Product__r.Order_Model__c) : '';
                                if(lineData.SERIAL_NUMBER_FLAG == 'N'){
                                    ol.Serial_Number_Flag__c = 'NO';
                                }else if(lineData.SERIAL_NUMBER_FLAG == 'Y'){
                                    ol.Serial_Number_Flag__c = 'YES';
                                }
                                ol.SerialNumber__c = lineData.SERIAL_NUMBER;
                                if(String.isNotBlank(requestFor)) {
                                    ol.Request_For__c = requestFor;
                                }
                                lstLine.add(ol);
                            }
                        }
                    }
                }
            }
        }
        Schema.SObjectField externalFiled = Sales_Rep_Stock_Header__c.Fields.Move_Order_OracleID__c;
        Database.UpsertResult[] resList = Database.upsert(lstHeader, externalFiled, false);

        for (Integer i = 0; i < resList.size(); i++){
            if (!resList.get(i).isSuccess()){
                Database.Error[] err = resList.get(i).getErrors();
                ReturnItem request = new ReturnItem();
                System.debug('lstHeader.get(i)===>'+lstHeader.get(i));
                System.debug('lstHeader.get(i).Move_Order_OracleID__c===>'+lstHeader.get(i).Move_Order_OracleID__c);

                request.ExternalID = lstHeader.get(i).Move_Order_OracleID__c;
                request.Type = lstHeader.get(i).Scene_Type__c;
                request.Error_Message = 'This ' + lstHeader.get(i).Scene_Type__c + ' was failed saving in Salesforce';
                request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' + err.get(0).getMessage();
                resObj.Process_Result.add(request);
            }
        } 
        //获取父对象id
        Map<String,String> mapNumToId = new Map<String,String>();
        for(Sales_Rep_Stock_Header__c head: lstHeader){
            mapNumToId.put(head.Move_Order_OracleID__c,head.Id);
        } 
        //绑定父子对象关系
        for(Sales_Rep_Stock__c line : lstLine){
            if(mapNumToId.containsKey(line.Move_Order_Oracle_Id__c)){
                line.Sales_Rep_Stock_Header__c = mapNumToId.get(line.Move_Order_Oracle_Id__c);
            }
        }
        System.debug('lstLine记录数====》'+lstLine.size());
        Schema.SObjectField externalLineFiled = Sales_Rep_Stock__c.Fields.Transaction_Id__c;
        Database.UpsertResult[] lineResList = Database.upsert(lstLine,externalLineFiled,false);
        for (Integer i = 0; i < lineResList.size(); i++){
            if (!lineResList.get(i).isSuccess()){
                Database.Error[] err = lineResList.get(i).getErrors();
                ReturnItem request = new ReturnItem();
                request.Type =  sceneType + 'Line';
                request.Error_Message = 'This ' + sceneType + 'Line was failed saving in Salesforce';
                request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' + err.get(0).getMessage();
                resObj.Process_Result.add(request);
            }
        }

        List<Sales_Rep_Stock_Return_Item__c> lstWareHouseLine = new List<Sales_Rep_Stock_Return_Item__c>();
        if(setWareHouseItem.size() > 0){
            lstWareHouseLine = [SELECT Id, Sales_Rep_Stock__c FROM Sales_Rep_Stock_Return_Item__c WHERE Id in :setWareHouseItem];
            if(lstWareHouseLine.size() > 0){
                for(Sales_Rep_Stock_Return_Item__c srsri : lstWareHouseLine){
                    if(srsri.Sales_Rep_Stock__c != null){
                        setStockItem.add(srsri.Sales_Rep_Stock__c);
                    }
                }
            }
        }
        List<Sales_Rep_Stock__c> lstStockLine = new List<Sales_Rep_Stock__c>();
        if(setStockItem.size() > 0){
            lstStockLine = [SELECT id,isDelete__c FROM Sales_Rep_Stock__c WHERE Id in :setStockItem and isDelete__c = false];
            if(lstStockLine.size() > 0){
                for(Sales_Rep_Stock__c srs : lstStockLine){
                    srs.isDelete__c = true;
                }
                update lstStockLine;
            }
        }

        return resObj;
    }

    private static void getStockRequestSalesRepMap(List<ReqestObj> reqObjList, Map<String, String> stockRequestSalesRepMap, Map<String, String> stockRequestIdMap) {
        for(ReqestObj reqObj : reqObjList){
            if(reqObj.SCENE_TYPE == SCENE_TYPE_STOCK) {
                if(String.isNotBlank(reqObj.REQUEST_NUMBER_CRM)) {
                    stockRequestSalesRepMap.put(reqObj.REQUEST_NUMBER_CRM, '');
                }
            }
        }
        for(Sub_inventory_Request__c inventoryRequest : [SELECT Name, Request_For__c FROM Sub_inventory_Request__c WHERE Name IN :stockRequestSalesRepMap.keySet()]){
            stockRequestSalesRepMap.put(inventoryRequest.Name, inventoryRequest.Request_For__c);
            stockRequestIdMap.put(inventoryRequest.Name, inventoryRequest.Id);
        }
    }



    global class ReqestObj{
        global String HEADERID;
        global String MOVE_ORDER_ORACLE_ID;
        global String MOVE_ORDER_ORACLE_NUMBER;
        global String REQUEST_NUMBER_CRM;
        global String SCENE_TYPE;
        global String MOVE_ORDER_DATE;
        global List<OrderLine> OrderLine;
        global String ATTRIBUTE1;
        global String ATTRIBUTE2;
        global String ATTRIBUTE3;
        global String ATTRIBUTE4;
        global String ATTRIBUTE5;
        global String ATTRIBUTE6;
        global String ATTRIBUTE7;
        global String ATTRIBUTE8;
        global String ATTRIBUTE9;
        global String ATTRIBUTE10;
        global String ATTRIBUTE11;
        global String ATTRIBUTE12;
        global String ATTRIBUTE13;
        global String ATTRIBUTE14;
        global String ATTRIBUTE15;
    }

    global class OrderLine{
        global String LINEID;
        global String TRANSACTION_ID;
        global String HEADERID;
        global String MOVE_ORDER_ORACLE_ID;
        global String MOVE_ORDERLINE_NUMBER;
        global String MOVE_ORDERLINE_ORACLE_ID;
        global String MOVE_ORDERLINE_CRM_ID;
        global String SHIPPED_DATE;
        global String PRODUCT_MODEL;
        global String SERIAL_NUMBER_FLAG;
        global String SERIAL_NUMBER;
        global String ATTRIBUTE1;
        global String ATTRIBUTE2;
        global String ATTRIBUTE3;
        global String ATTRIBUTE4;
        global String ATTRIBUTE5;
        global String ATTRIBUTE6;
        global String ATTRIBUTE7;
        global String ATTRIBUTE8;
        global String ATTRIBUTE9;
        global String ATTRIBUTE10;
        global String ATTRIBUTE11;
        global String ATTRIBUTE12;
        global String ATTRIBUTE13;
        global String ATTRIBUTE14;
        global String ATTRIBUTE15;
    }

    global static List<ReqestObj> parse(String jsonStr){
        return (List<ReqestObj>)JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }

    global class ResultObj{
        global String Process_Status;
        global List<ReturnItem> Process_Result;

    }

    global class ReturnItem{
        global String Type;
        global String ExternalID;
        global String Error_Message;
        global String Error_Detail;

    }
}