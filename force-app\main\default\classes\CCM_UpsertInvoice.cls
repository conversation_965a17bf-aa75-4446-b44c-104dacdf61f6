@RestResource(urlMapping='/CCM_RestService_DealInvoiceInfoEU')
global without sharing class CCM_UpsertInvoice {
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        resObj.Process_Result = new List<ReturnItem>();
        String resStr = req.requestBody.toString();
        try {
            resStr = resStr.replace('\'', '\\\'');
            if(!resStr.startsWith('[')){
                resStr = '[' + resStr + ']';
            }
            resStr = resStr.replace('\'', '\"');
            reqObjList = parse(resStr);
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObjList);
            //遍历reqObj用于创建Order\OrderLine\OderLineAdjustment
            List<Invoice__c> lstUpsertInvoice = new List<Invoice__c>();
            List<Invoice_Item__c> lstUpsertInvoiceItem = new List<Invoice_Item__c>();
            //创建唯一标识到具体对象的映射
            Map<String,Invoice__c> mapId2Invoice = new Map<String,Invoice__c>();
            Map<String,Invoice_Item__c> mapId2InvoiceItem = new Map<String,Invoice_Item__c>();
            Set<String> setAddressCode = new Set<String>();
            Map<String,Address_With_Program__c> mapCode2Address = new Map<String,Address_With_Program__c>();
            //校验是否需要插入或更新通过各种Id
            Set<String> setInvoiceId = new Set<String>();
            Set<String> setInvoiceItemId = new Set<String>();
        
            Set<String> setOrderOracleId = new Set<String>();
            Set<String> setShipmentNo = new Set<String>();
            Set<String> setCustomerNumber = new Set<String>();
            //产品Model查询产品Id
            //honey added---2023/10/25-->根据Screen Type查询Training
            List<String> lstOrderOracleid= new List<String>();
            //honey added end
            
            Set<String> setProductModel = new Set<String>();
            for(ReqestObj objInvoice : reqObjList){
                setAddressCode.add(objInvoice.BILLTO);
                setAddressCode.add(objInvoice.SHIPTO);
                //唯一标识待定
                setInvoiceId.add(objInvoice.CUSTOMER_TRX_ID);
                setCustomerNumber.add(objInvoice.CUSTOMER);
                setShipmentNo.add(objInvoice.SHIP_NO);
                Invoice__c objUpsertInvoice = new Invoice__c();
                SetInvoiceValue(objInvoice,objUpsertInvoice);
                //honey added---2023/10/25-->根据Screen Type查询Training
                
                if('Training Request'.equals(objInvoice.SCENE_TYPE)){
                    //表示是Training Order类型的。需要根据Order Oracle Id查询对应的Training Order 
                    lstOrderOracleid.add(objInvoice.ORDER_ORACLE_ID);
                }
                //honey added end

                mapId2Invoice.put(objInvoice.CUSTOMER_TRX_ID,objUpsertInvoice);
                system.debug('objUpsertInvoice-->'+ objUpsertInvoice);
                //设置完Invoice后遍历Item
                for(InvoiceLine objInvoiceItem : objInvoice.InvoiceLine){
                    objUpsertInvoice.Order_OracleID__c = objInvoiceItem.ORDER_ORACLE_ID;
                    setOrderOracleId.add(objInvoiceItem.ORDER_ORACLE_ID);
                    setInvoiceItemId.add(objInvoice.CUSTOMER_TRX_ID+'-'+ objInvoiceItem.INVOICE_LINE_NUMBER);
                    setProductModel.add(objInvoiceItem.PRODUCT_MODEL);
                    Invoice_Item__c objUpsertInvoiceItem = new Invoice_Item__c();
                    SetInvoiceItemValue(objInvoiceItem,objUpsertInvoiceItem,objInvoice);
                    system.debug('objUpsertInvoiceItem-->'+objUpsertInvoiceItem);
                    mapId2InvoiceItem.put(objInvoice.CUSTOMER_TRX_ID+'-'+ objInvoiceItem.INVOICE_LINE_NUMBER,objUpsertInvoiceItem);
                  
                }
            }
            //honey added---2023/10/25-->根据Screen Type查询Training
            Map<String,String> mapTrainingOracleId2Id = new Map<String,String>();
            //根据OrderOracleId查询Training Order 
            if(lstOrderOracleid != null && lstOrderOracleid.size()>0){
                List<Training_Order__c> lstTraining = new List<Training_Order__c>();
                lstTraining = [
                    SELECT Id, Order_Oralce_Id__c FROM Training_Order__c WHERE Order_Oralce_Id__c IN :lstOrderOracleid 
                ] ;
                
                for(Training_Order__c objTraining :  lstTraining){
                    mapTrainingOracleId2Id.put(objTraining.Order_Oralce_Id__c, objTraining.Id);
                }
                
            }
            //通过OrderOracleId 查询Order信息
            List<Order> lstOrder = [
                SELECT Id,Order_OracleID__c FROM Order WHERE Order_OracleID__c IN :setOrderOracleId
            ];
            
            map<String,String> mapOracleId2Id = new Map<String,String>();
            for(Order objOrder : lstOrder){
                mapOracleId2Id.put(objOrder.Order_OracleID__c,objOrder.Id);
            }
            system.debug('mapOracleId2Id-->'+mapOracleId2Id);
            //通过AddressCode查询BAB
            List<Address_With_Program__c> lstAddress = [
                SELECT Account_Address__c,Id,Customer_Line_Oracle_ID__c FROM Address_With_Program__c WHERE Customer_Line_Oracle_ID__c IN :setAddressCode
            ];
            for(Address_With_Program__c objAddress : lstAddress){
                mapCode2Address.put(objAddress.Customer_Line_Oracle_ID__c,objAddress);
            }
            //通过CustomerNumber去Customer表中查询Customer Id
            List<Account> lstAccount = [
                SELECT Id ,AccountNumber FROM Account WHERE AccountNumber IN : setCustomerNumber
            ];
            Map<String,String> mapAccountNumber2Id = new Map<String,String>();
            for(Account objAccount : lstAccount){
                mapAccountNumber2Id.put(objAccount.AccountNumber,objAccount.Id);
            }
            system.debug('mapAccountNumber2Id-->'+mapAccountNumber2Id);
            //通过ShipmentOracleId去Shipment表中查询ShipmentId
            List<Shipment__c> lstShipment = [
                SELECT Id ,Ship_OracleID__c,Order__c FROM Shipment__c WHERE Ship_OracleID__c IN : setShipmentNo
            ];
            Map<String,String> mapShipmentOracle2Id = new Map<String,String>();
            Map<String,String> mapShipmentOracle2OrderId = new Map<String,String>();
            for(Shipment__c objShipment : lstShipment){
                mapShipmentOracle2Id.put(objShipment.Ship_OracleID__c,objShipment.Id);
                mapShipmentOracle2OrderId.put(objShipment.Ship_OracleID__c,objShipment.Order__c);
            }
            system.debug('mapShipmentOracle2Id-->'+mapShipmentOracle2Id);
            //通过Invoice的Oracle Id 查询Invoicet信息--->用于校验是否重复
            List<Invoice__c> lstExitInvoice = [
                SELECT Id ,Invoice_OracleID__c FROM Invoice__c WHERE  Invoice_OracleID__c IN : setInvoiceId
            ];
            List<Invoice_Item__c> lstExitInvoiceLine = [
                SELECT Id ,Invoice_Item_OracleID__c ,Invoice_OracleId__c,External_Id__c FROM Invoice_Item__c WHERE  External_Id__c IN : setInvoiceItemId
            ];
          
            CheckUpdateOrInsertInvoice(lstExitInvoice,mapId2Invoice,lstUpsertInvoice,mapShipmentOracle2Id,mapAccountNumber2Id,mapShipmentOracle2OrderId);
            for(Invoice__c objUpsertInvoice : lstUpsertInvoice){
                if(mapOracleId2Id.containsKey(objUpsertInvoice.Order_OracleID__c)){
                    objUpsertInvoice.Order__c =  mapOracleId2Id.get(objUpsertInvoice.Order_OracleID__c);

                }
                
                Address_With_Program__c objBillAddress = mapCode2Address.get(objUpsertInvoice.BillTo_Text__c);
                Address_With_Program__c objShipAddress = mapCode2Address.get(objUpsertInvoice.ShipTo_Text__c);
                String strTrainingOrderId = mapTrainingOracleId2Id.get(objUpsertInvoice.Order_OracleID__c);
                if(String.isNotBlank(strTrainingOrderId)){
                    objUpsertInvoice.Training_Order__c = strTrainingOrderId;
                }
                if(objBillAddress!= null){
                    objUpsertInvoice.BillTo__c = objBillAddress.Id;
                 
                }
                if(objShipAddress!= null){
                    objUpsertInvoice.ShipTo__c = objShipAddress.Id;
                    
                }
                
            }
            
            system.debug('需要插入的数据lstUpsertInvoice-->'+lstUpsertInvoice);
            
            
            Map<String,String> mapInvoiceOracleId2Id = new Map<String,String>();
            if(lstUpsertInvoice.size()>0){
                Database.UpsertResult[] resShipmentList= Database.upsert(lstUpsertInvoice,false);
                for (Integer i = 0 ; i < resShipmentList.size() ; i++) {
                    if (!resShipmentList.get(i).isSuccess()) {
                        Database.Error[] err = resShipmentList.get(i).getErrors();
                        ReturnItem request = new ReturnItem();
                        request.External_Id = String.valueOf(lstUpsertInvoice.get(i).Invoice_OracleID__c);
                        request.Error_Message = 'This Invoice was failed saving in Salesforce';
                        request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                        resObj.Process_Result.add(request);
                    }else{
                        //表示更新或插入成功
                        mapInvoiceOracleId2Id.put(String.valueOf(lstUpsertInvoice.get(i).Invoice_OracleID__c),lstUpsertInvoice.get(i).Id);
                    }
                }
            }
            //根据ProduceModel查询产品信息
            List<Product2> lstProduct = [
                SELECT Id,Order_Model__c FROM Product2 WHERE Order_Model__c IN :setProductModel
            ];
            Map<String,String> mapModel2Id = new Map<String,String>();
            for(Product2 objProduct : lstProduct){
                mapModel2Id.put(objProduct.Order_Model__c,objProduct.Id);
            }
            // 插入Shipment后获取Shipment的Id ,用于关联Shipment Item
            CheckUpdateOrInsertInvoiceLine(lstExitInvoiceLine,mapId2InvoiceItem,lstUpsertInvoiceItem,mapInvoiceOracleId2Id,mapModel2Id);
            system.debug('需要插入的数据OlstUpsertInvoiceItem-->'+lstUpsertInvoiceItem.size());
          
            if(lstUpsertInvoiceItem.size()>0){
                Database.UpsertResult[] resOrderLineList= Database.upsert(lstUpsertInvoiceItem,false);
                for (Integer i = 0 ; i < resOrderLineList.size() ; i++) {
                    if (!resOrderLineList.get(i).isSuccess()) {
                        Database.Error[] err = lstUpsertInvoiceItem.get(i).getErrors();
                        ReturnItem request = new ReturnItem();
                        request.External_Id = String.valueOf(lstUpsertInvoiceItem.get(i).Invoice_Item_OracleID__c);
                        request.Error_Message = 'This Invoice Item was failed saving in Salesforce';
                        request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                        resObj.Process_Result.add(request);
                    }
                }
                
            }
            if (resObj.Process_Result.size() == 0) {
                resObj.Process_Status = 'Success';
                String logId = Util.logIntegration('InvoiceService Log','CCM_UpsertInvoice','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            }else {
                resObj.Process_Status = 'Fail';
                String logId = Util.logIntegration('InvoiceService Log','CCM_UpsertInvoice','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept Invoice Info',logId,getMailErrorMessage(resObj));
            }

            // link to sales target detail
            linkToSalesTargetDetail(lstUpsertInvoice);

            
        } catch (Exception e) {
           
            resObj.Process_Status = 'Fail';
            ReturnItem request = new ReturnItem();
            request.Error_Message = 'This Invoice was failed saving in Salesforce';
            request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
            resObj.Process_Result.add(request);
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration('InvoiceService Exception','CCM_UpsertInvoice','POST',
                                               JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept Invoice Info',logId,getMailErrorMessage(resObj));
            system.debug('错误的resObj--->'+resObj);
            return resObj;
        }
        system.debug('resObj--->'+resObj);
        return resObj;
    }

    global static void linkToSalesTargetDetail(List<Invoice__c> lstUpsertInvoice) {
        List<String> invoiceIds = new List<String>();
        for(Invoice__c invoice : lstUpsertInvoice) {
            if(String.isNotBlank(invoice.Id)) {
                invoiceIds.add(invoice.Id);
            }
        }
        CCM_SalesTargetReportUtil.linkInvoiceToSalesTargetDetail(invoiceIds);
    }

    global static void CheckUpdateOrInsertInvoice(List<Invoice__c> lstExitInvoice , Map<String,Invoice__c> mapId2Invoice, List<Invoice__c> lstUpsertInvoice,Map<String,String> mapShipmentOracle2Id,Map<String,String> mapAccountNumber2Id,Map<String,String> mapShipmentOracle2OrderId) {
        for(Invoice__c objExitInvoice : lstExitInvoice){
            if(mapId2Invoice.containsKey(objExitInvoice.Invoice_OracleID__c)){
                //如果存在的列表中的OrderId包含传入的则是修改。将Id赋值
                Invoice__c objInvoice = mapId2Invoice.get(objExitInvoice.Invoice_OracleID__c);
                objInvoice.Id = objExitInvoice.Id;
                mapId2Invoice.put(objExitInvoice.Invoice_OracleID__c,objInvoice);
            }
        }
        system.debug('传入的Account --> '+mapAccountNumber2Id);
        system.debug('传入的mapShipmentOracle2Id --> '+mapShipmentOracle2Id);
        for(String key : mapId2Invoice.keySet()){
            Invoice__c objInvoice = mapId2Invoice.get(key);
            objInvoice.Customer__c = mapAccountNumber2Id.get(objInvoice.Customer_Text__c);
            objInvoice.Shipment__c = mapShipmentOracle2Id.get(objInvoice.Delivery_Number__c);
            objInvoice.Order__c = mapShipmentOracle2OrderId.get(objInvoice.Delivery_Number__c);
            //根据 Credit_note_for_Invoice__c 是否有值判断是Invoice还是Credit
            if(objInvoice.Credit_note_for_Invoice__c == 'INV' || String.isBlank(objInvoice.Credit_note_for_Invoice__c)){
                objInvoice.RecordTypeId = Schema.SObjectType.Invoice__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.INVOICE).getRecordTypeId();
            }else if(objInvoice.Credit_note_for_Invoice__c == 'CREDIT'){
                objInvoice.RecordTypeId = Schema.SObjectType.Invoice__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.CREDIT).getRecordTypeId();
            }
            lstUpsertInvoice.add(objInvoice);
        }
    }

    global static void CheckUpdateOrInsertInvoiceLine(List<Invoice_Item__c> lstExitInvoiceLine , Map<String,Invoice_Item__c> mapId2InvoiceLine ,  List<Invoice_Item__c> lstUpsertInvoiceItem,Map<String,String> mapInvoiceOracleId2Id,Map<String,String> mapModel2Id) {
        for(Invoice_Item__c objExitInvoiceLine : lstExitInvoiceLine){
            if(mapId2InvoiceLine.containsKey(objExitInvoiceLine.Invoice_OracleId__c+'-'+objExitInvoiceLine.Invoice_Item_OracleID__c)){
                //如果存在的列表中的OrderId包含传入的则是修改。将Id赋值
                Invoice_Item__c objInvoiceLine = mapId2InvoiceLine.get(objExitInvoiceLine.Invoice_OracleId__c+'-'+objExitInvoiceLine.Invoice_Item_OracleID__c);
                objInvoiceLine.Id = objExitInvoiceLine.Id;
                mapId2InvoiceLine.put(objExitInvoiceLine.Invoice_OracleId__c+'-'+objExitInvoiceLine.Invoice_Item_OracleID__c,objInvoiceLine);
            }
        }
        for(String key : mapId2InvoiceLine.keySet()){
            Invoice_Item__c objInvoiceItem = mapId2InvoiceLine.get(key);
            system.debug('mapInvoiceOracleId2Id-->'+mapInvoiceOracleId2Id);
            system.debug('objInvoiceItem-->'+objInvoiceItem.Invoice_OracleId__c);
            objInvoiceItem.Invoice__c = mapInvoiceOracleId2Id.get(objInvoiceItem.Invoice_OracleId__c);
            system.debug('objInvoiceItem.Invoice__c--->'+objInvoiceItem.Invoice__c);
            objInvoiceItem.Product__c = mapModel2Id.get(objInvoiceItem.Catalog_Item_Text__c);
            lstUpsertInvoiceItem.add(objInvoiceItem);
        }
    }

    global static void SetInvoiceValue(ReqestObj objInvoice,Invoice__c objUpsertInvoice){
        objUpsertInvoice.Invoice_OracleID__c = objInvoice.CUSTOMER_TRX_ID;
        objUpsertInvoice.Invoice_Number__c = objInvoice.INVOICE_NUMBER;
        objUpsertInvoice.Customer_Text__c = objInvoice.CUSTOMER;
        objUpsertInvoice.BillTo_Text__c = objInvoice.BILLTO;
        objUpsertInvoice.BillTo_Customer__c = objInvoice.BILLTO_CUSTOMER;
        objUpsertInvoice.BillTo_Address1__c = objInvoice.BILLTO_ADDRESS1;
        objUpsertInvoice.BillTo_Address2__c = objInvoice.BILLTO_ADDRESS2;
        objUpsertInvoice.BillTo_Address3__c = objInvoice.BILLTO_ADDRESS3;
        objUpsertInvoice.BillTo_Location__c = objInvoice.BILLTO_LOCATION;
        objUpsertInvoice.ShipTo_Text__c = objInvoice.SHIPTO;
        objUpsertInvoice.ShipTo_Customer__c = objInvoice.SHIPTO_CUSTOMER;
        objUpsertInvoice.ShipTo_Address1__c = objInvoice.SHIPTO_ADDRESS1;
        objUpsertInvoice.ShipTo_Address2__c = objInvoice.SHIPTO_ADDRESS2;
        objUpsertInvoice.ShipTo_Address3__c = objInvoice.SHIPTO_ADDRESS3;
        objUpsertInvoice.ShipTo_Location__c = objInvoice.SHIPTO_LOCATION;
        objUpsertInvoice.Delivery_Number__c = objInvoice.SHIP_NO;
        objUpsertInvoice.Your_VAT_Re_No__c = objInvoice.YOUR_VAT_RE_NO;
        
        objUpsertInvoice.Invoice_Date__c = String.isBlank(objInvoice.INVOICE_DATE)? null : Date.valueOf(objInvoice.INVOICE_DATE) ;
        
      
        objUpsertInvoice.Remarks__c = objInvoice.REMARKS;
        objUpsertInvoice.Fracht_Freight__c = objInvoice.FRACHT_FREIGHT;
        objUpsertInvoice.Credit_note_for_Invoice__c = objInvoice.CREDIT_NOTE_FOR_INVOICE;
        
        //Honey Added-->2023-07-10 -->新增币种信息
        objUpsertInvoice.CurrencyIsoCode = objInvoice.CURRENCY_CODE;

        //add start -vince 2023-10-11
        objUpsertInvoice.Claim_CRM_Id__c = objInvoice.CRM_ID;
        objUpsertInvoice.Order_OracleID__c = objInvoice.ORDER_ORACLE_ID;
        objUpsertInvoice.Scene_Type__c = objInvoice.SCENE_TYPE;
        //add end -vince 2023-10-11
    }

    global static void SetInvoiceItemValue(InvoiceLine objInvoiceLine,Invoice_Item__c objUpsertInvoiceLine,ReqestObj objInvoice){
        objUpsertInvoiceLine.Invoice_OracleId__c = objInvoice.CUSTOMER_TRX_ID;
        objUpsertInvoiceLine.External_Id__c = objInvoice.CUSTOMER_TRX_ID+'-'+ objInvoiceLine.INVOICE_LINE_NUMBER;
        objUpsertInvoiceLine.Invoice_Item_OracleID__c = objInvoiceLine.INVOICE_LINE_NUMBER;
        objUpsertInvoiceLine.Qty_Extended__c = String.isBlank(objInvoiceLine.QUANTITY) ? null  :  Double.valueOf(objInvoiceLine.QUANTITY) ;
        objUpsertInvoiceLine.Catalog_Item_Text__c = objInvoiceLine.PRODUCT_MODEL;
        objUpsertInvoiceLine.Description__c = objInvoiceLine.DESCRIPTION;
        objUpsertInvoiceLine.Uom__c = objInvoiceLine.UOM;
        objUpsertInvoiceLine.Price__c = String.isBlank(objInvoiceLine.PRICE)? null :  Double.valueOf(objInvoiceLine.PRICE);
        objUpsertInvoiceLine.Amount__c = String.isBlank(objInvoiceLine.AMOUNT)? null :  Double.valueOf(objInvoiceLine.AMOUNT);
        objUpsertInvoiceLine.Tax__c = String.isBlank(objInvoiceLine.TAX)? null :  Double.valueOf(objInvoiceLine.TAX);
        objUpsertInvoiceLine.Order_OracleId__c = objInvoiceLine.ORDER_ORACLE_ID;
        objUpsertInvoiceLine.Order_Oracle_Number__c = objInvoiceLine.ORDER_ORACLE_NUMBER;
        objUpsertInvoiceLine.Order_Line_Oracle_ID__c = objInvoiceLine.ORDER_LINE_ORACLEID;
        objUpsertInvoiceLine.Order_Line_Oracle_Number__c = objInvoiceLine.ORDER_LINE_ORACLE_NUMBER;
        //objUpsertInvoiceLine.Pos__c
        objUpsertInvoiceLine.CurrencyIsoCode = objInvoice.CURRENCY_CODE;
    }
  
    global static List<ReqestObj> parse(String jsonStr) {
        return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }
   
    global class ResultObj {
        global String Process_Status;
        global List<ReturnItem> Process_Result;
    }

    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
        errContent += 'Process Status : Fail<br/><br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
                errContent += 'External ID : ' + Item.External_Id + '<br/>';
                errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
                errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>'; 
            }
        }
        return errContent;
    }
    global class ReqestObj {
        global String CUSTOMER_TRX_ID;
        global String INVOICE_NUMBER;
        global String CUSTOMER;
        global String BILLTO;
        global String BILLTO_CUSTOMER;
        global String BILLTO_ADDRESS1;
        global String BILLTO_ADDRESS2;
        global String BILLTO_ADDRESS3;
        global String BILLTO_LOCATION;
        global String SHIPTO;
        global String SHIPTO_CUSTOMER;
        global String SHIPTO_ADDRESS1;
        global String SHIPTO_ADDRESS2;
        global String SHIPTO_ADDRESS3;
        global String SHIPTO_LOCATION;
        global String SHIP_NO;
        
        global String YOUR_VAT_RE_NO;
        global String INVOICE_DATE;
        global String REMARKS;
        global Double FRACHT_FREIGHT;
        global String CREDIT_NOTE_FOR_INVOICE;
        
        //Honey Added-->2023-07-10 -->新增币种信息
        global String CURRENCY_CODE;

        //add start -Vince 2023-10-11
        global String SCENE_TYPE;
        global String ORDER_ORACLE_ID;
        global String CRM_ID;
        //add end -Vince 2023-10-11
        global List<InvoiceLine> InvoiceLine;
    }
    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
    global class InvoiceLine {
        global String INVOICE_LINE_NUMBER;
        global String QUANTITY;
        global String PRODUCT_MODEL;
        global String DESCRIPTION;
        global String UOM;
        global String PRICE;
        global String AMOUNT;
        global String TAX;
        global String ORDER_ORACLE_ID;
        global String ORDER_ORACLE_NUMBER;
        global String ORDER_LINE_ORACLEID;
        global String ORDER_LINE_ORACLE_NUMBER;
    }
}