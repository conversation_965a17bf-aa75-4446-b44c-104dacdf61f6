/**
 * Honey
 * 2023/11/07
 * 测试类
 */
@isTest
public without sharing class CCM_CourseArrangementUpdateHandler_Test {
    @isTest
    static void testHandleMethod() {
        Account objAccount = new Account (Name = 'pwc Test',AccountNumber = 'E1023');
        insert objAccount;
        Account_Address__c objAccountAddress = new Account_Address__c();
        objAccountAddress.Country__c = 'DE';
        objAccountAddress.City__c = 'Steinheim';
        objAccountAddress.Address1__c = 'Autenbacherstr. 11';
        insert objAccountAddress;
        Account_Address__c objAccountAddress2 = new Account_Address__c();
        objAccountAddress2.Country__c = 'DE';
        objAccountAddress2.City__c = 'Steinheim';
        objAccountAddress2.Address1__c = 'Autenbacherstr. 121';
        insert objAccountAddress2;
        Address_With_Program__c objAddressProgram = new Address_With_Program__c();
        objAddressProgram.Customer_Line_Oracle_ID__c = '168111';
        objAddressProgram.Account_Address__c = objAccountAddress.Id;
        Sales_Program__c authBrand = new Sales_Program__c();
        authBrand.Customer__c = objAccount.Id;
        authBrand.Status__c = 'Active';
        authBrand.Approval_Status__c = 'Approved';
        insert authBrand;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;

        Training_Location__c training_Location = new Training_Location__c(Name='Test',Country_All__c='DE-Germany');
        insert training_Location;
        // ContentDocument cd = new ContentDocument();
        // insert cd;
        // ContentDocumentLink link = new ContentDocumentLink(ContentDocumentId=cd.Id,LinkedEntityId=training_Location.Id);
        // insert link;

        // 创建一个测试ContentVersion记录
        ContentVersion cv = new ContentVersion(
        Title = 'Test Document',
        PathOnClient = 'TestDocument.txt',
        VersionData = Blob.valueOf('Test document content')
        );
        insert cv;

        // 尝试查询ContentDocument记录
        ContentDocumentLink cdl;
        try {
            ContentDocument cd = [SELECT Id FROM ContentDocument WHERE Id IN (SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id) LIMIT 1];
            system.debug('cd-->'+cd);
            // 创建ContentDocumentLink记录将ContentDocument链接到测试对象上
            cdl = new ContentDocumentLink(
                LinkedEntityId = training_Location.Id,
                ContentDocumentId = cd.Id,
                ShareType = 'V'
            );
            insert cdl;
        } catch (Exception e) {
            System.debug('Exception occurred: ' + e.getMessage());
        }

        // 检查ContentDocumentLink是否成功创建
        if (cdl != null) {
            List<ContentDocumentLink> cdlList = [SELECT Id FROM ContentDocumentLink WHERE LinkedEntityId = :training_Location.Id];
            System.assertEquals(1, cdlList.size(), 'ContentDocumentLink should be created successfully.');
        } else {
            System.assert(false, 'ContentDocumentLink creation failed.');
        }
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today().addDays(4),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Training_Location__c=training_Location.Id,
            Free_Training__c = true
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = objAccountAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = objAccount.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 100,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]',
            Course_Arrangement__r=courseArrangement
        );
        insert courseRegister;
        Course_Register_Item__c cri = new Course_Register_Item__c(Course_Register__c=courseRegister.Id,Trainee__c='Trainee');
        insert cri;

        // Call the handle method
        Test.startTest();
        courseRegister.Status__c = 'Confirmed';
        courseRegister.Trainee__c = '["test"]';
        update courseRegister;
        Test.stopTest();


    }

    @isTest
    static void testPushRegisterToEBSMethod() {
        // Create test data
        Account objAccount = new Account (Name = 'pwc Test');
        insert objAccount;
        Account_Address__c objAccountAddress = new Account_Address__c();
        objAccountAddress.Country__c = 'DE';
        objAccountAddress.City__c = 'Steinheim';
        objAccountAddress.Address1__c = 'Autenbacherstr. 11';
        insert objAccountAddress;
        Account_Address__c objAccountAddress2 = new Account_Address__c();
        objAccountAddress2.Country__c = 'DE';
        objAccountAddress2.City__c = 'Steinheim';
        objAccountAddress2.Address1__c = 'Autenbacherstr. 121';
        insert objAccountAddress2;
        Address_With_Program__c objAddressProgram = new Address_With_Program__c();
        objAddressProgram.Customer_Line_Oracle_ID__c = '168111';
        objAddressProgram.Account_Address__c = objAccountAddress.Id;
        Sales_Program__c authBrand = new Sales_Program__c();
        authBrand.Customer__c = objAccount.Id;
        authBrand.Status__c = 'Active';
        authBrand.Approval_Status__c = 'Approved';
        insert authBrand;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;

        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = objAccountAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = objAccount.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 100,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]'
        );
        insert courseRegister;

        // Call the pushRegisterToEBS method
        Test.startTest();
        CCM_CourseArrangementUpdateHandler.pushRegisterToEBS(courseRegister.Id);
        Test.stopTest();

        // Verify the expected behavior
        // Add your assertions here
    }

    @isTest
    static void testFreeTrainingNoERPSync() {
        // Create test data
        Account objAccount = new Account (Name = 'pwc Test',AccountNumber = 'E1023');
        insert objAccount;
        Account_Address__c objAccountAddress = new Account_Address__c();
        objAccountAddress.Country__c = 'DE';
        objAccountAddress.City__c = 'Steinheim';
        objAccountAddress.Address1__c = 'Autenbacherstr. 11';
        insert objAccountAddress;

        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;

        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;

        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 0,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;

        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = objAccountAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = objAccount.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 0,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]'
        );
        insert courseRegister;

        Course_Register_Item__c cri = new Course_Register_Item__c(Course_Register__c=courseRegister.Id,Trainee__c='Trainee');
        insert cri;

        // Test that free training does not trigger ERP sync
        Test.startTest();
        courseRegister.Status__c = 'Confirmed';
        update courseRegister;
        Test.stopTest();

        // Verify that the status was updated but no ERP sync occurred
        Course_Register__c updatedRegister = [SELECT Id, Status__c FROM Course_Register__c WHERE Id = :courseRegister.Id];
        System.assertEquals('Confirmed', updatedRegister.Status__c, 'Status should be updated to Confirmed');
        // Note: In a real test, you would verify that the ERP callout was not made
        // This could be done by checking integration logs or using mock callouts
    }

    @isTest
    static void testNonFreeTrainingERPSync() {
        // Create test data
        Account objAccount = new Account (Name = 'pwc Test',AccountNumber = 'E1023');
        insert objAccount;
        Account_Address__c objAccountAddress = new Account_Address__c();
        objAccountAddress.Country__c = 'DE';
        objAccountAddress.City__c = 'Steinheim';
        objAccountAddress.Address1__c = 'Autenbacherstr. 11';
        insert objAccountAddress;

        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;

        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;

        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;

        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = objAccountAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = objAccount.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 100,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]'
        );
        insert courseRegister;

        Course_Register_Item__c cri = new Course_Register_Item__c(Course_Register__c=courseRegister.Id,Trainee__c='Trainee');
        insert cri;

        // Test that non-free training triggers ERP sync
        Test.startTest();
        courseRegister.Status__c = 'Confirmed';
        update courseRegister;
        Test.stopTest();

        // Verify that the status was updated and ERP sync was triggered
        Course_Register__c updatedRegister = [SELECT Id, Status__c FROM Course_Register__c WHERE Id = :courseRegister.Id];
        System.assertEquals('Confirmed', updatedRegister.Status__c, 'Status should be updated to Confirmed');
        // Note: In a real test, you would verify that the ERP callout was made
        // This could be done by checking integration logs or using mock callouts
    }

    public CCM_CourseArrangementUpdateHandler_Test() {

    }
}