/**************************************************************************************************
 * Name: CCM_CustomerSalesTargetHandler
 * Object: Account
 * Purpose:
 *  1.如果customer 关联territory,把customer上的authorand brand也关联territory
 *  2.增加一个推数据到Oracle的接口
 * Author:  Aria w Zhong
 * Create Date:
 * Modify History:
 *2023/08/02:增加一个推数据到Oracle的接口
 **************************************************************************************************/
public without sharing class CCM_CustomerSalesTargetHandler implements Triggers.Handler{
    public static Boolean isRun = true;
    public static final String ASSOCIATIONGROP = 'Association_Group';
    public static final String ASSOCIATIONGROP_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(ASSOCIATIONGROP).getRecordTypeId();
    public void handle(){
        if (isRun){
            if (Trigger.isAfter){
                if (Trigger.isInsert){
                    pushCustInfo((List<Account>)Trigger.new, (Map<Id, Account>)Trigger.oldMap);
                }
                if (Trigger.isUpdate){

                    upsertTerritoryWithAB((List<Account>)Trigger.new, (Map<Id, Account>)Trigger.oldMap);
                    pushCustInfo((List<Account>)Trigger.new, (Map<Id, Account>)Trigger.oldMap);

                }
            }
        }
    }
    // *  1.如果customer 关联territory,把customer上的authorand brand也关联territory
    public static void upsertTerritoryWithAB(List<Account> newItemList, Map<Id, Account> oldItemMap){
        List<Sales_Program__c> abChangeList = new List<Sales_Program__c>();
        Map<Id, Id> CustomerIdsMap = new Map<Id, Id>();
        for (Account newItem : newItemList){
            if(newItem.RecordTypeId != CCM_Constants.PERSONACCOUNT_RECORDTYPEID &&  newItem.RecordTypeId != CCM_Constants.CommercialConsumer_RECORDTYPEID){//Add by Zoe on 23-09-13  by pass consumer
                Account old = new Account();
                //Honey Updated --->跳过后端逻辑字段触发
                old = oldItemMap.get(newItem.Id);
                if (newItem.RecordTypeId == CCM_Constants.CHANNEL_RECORDTYPEID || newItem.RecordTypeId == CCM_Constants.ASSOCIATIONGROP_RECORDTYPEID){
                    if (newItem.Sales_Territory__c <> null && newItem.Sales_Territory__c <> old.Sales_Territory__c){
                        CustomerIdsMap.put(newItem.Id, newItem.Sales_Territory__c);
                    }
                }
            }

        }
        if (CustomerIdsMap.size() > 0){
            List<Sales_Program__c> abList = [SELECT Id, Customer__c, Sales_Territory__c
                                             from Sales_Program__c
                                             where Customer__c in:CustomerIdsMap.keySet()];
            for (Sales_Program__c ab : abList){
                ab.Sales_Territory__c = CustomerIdsMap.get(ab.Customer__c);
                abChangeList.add(ab);
            }
        }

        update abChangeList;
    }
    //如果修改数据会触发把数据推到Oracle的逻辑
    public static void pushCustInfo(List<Account> newItemList, Map<Id, Account> oldItemMap){
        Map<String, String> agAccountNumberMap = new Map<String, String>();
        if(oldItemMap != null) {
            Set<String> agIds = new Set<String>();
            for(Account oldItem : oldItemMap.values()) {
                if(String.isNotBlank(oldItem.Association_Group__c)) {
                    agIds.add(oldItem.Association_Group__c);
                }
            }
            for(Account acc : [SELECT Id, AccountNumber FROM Account WHERE Id IN :agIds]) {
                agAccountNumberMap.put(acc.Id, acc.AccountNumber);
            }
        }
        
        for (Account newItem : newItemList){
            if(newItem.RecordTypeId != CCM_Constants.PERSONACCOUNT_RECORDTYPEID &&  newItem.RecordTypeId != CCM_Constants.CommercialConsumer_RECORDTYPEID){//Add by Zoe on 23-09-13  by pass consumer
                if (oldItemMap != null){
                    //Honey Updated --->跳过后端逻辑字段触发
                    Account old = oldItemMap.get(newItem.Id);
                    if (newItem.Last_PriceBook_Update_Date__c != old.Last_PriceBook_Update_Date__c || newItem.Is_2st_Caculate__c != old.Is_2st_Caculate__c || newItem.Is_2st_Caculate__c != old.Is_2st_Caculate__c || newItem.Caculate_Product_Price__c != old.Caculate_Product_Price__c || newItem.Customer_Oracle_ID__c != old.Customer_Oracle_ID__c || newItem.AccountNumber != old.AccountNumber){
                        continue;
                    } else{
                        if (old.Approval_Status__c != 'Approved' && newItem.Approval_Status__c == 'Approved'){
                            if (newItem.RecordTypeId == CCM_Constants.CHANNEL_RECORDTYPEID || newItem.RecordTypeId == ASSOCIATIONGROP_RECORDTYPEID){
                                Boolean agRemoved = false;
                                String oldAGAccountNumber = '';
                                if(newItem.Association_Group__c == null && old.Association_Group__c != null) {
                                    agRemoved = true;
                                    if(agAccountNumberMap.containsKey(old.Association_Group__c)) {
                                        oldAGAccountNumber = agAccountNumberMap.get(old.Association_Group__c);
                                    }
                                }
                                pushCustomerToEBS(newItem.Id, agRemoved, oldAGAccountNumber);
                            }
                        }
                    }
                }
            }

        }
    }

    @future(callout = true)
    public static void pushCustomerToEBS(String recordId, Boolean agRemoved, String oldAGAccountNumber){
        CCM_PushCustomerInfoService.pushCustInfo(recordId, agRemoved, oldAGAccountNumber);
    }
}