<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Issue_Point__c</fullName>
    <externalId>false</externalId>
    <formula>IF(Amount_limit__c,$Label.Amount_Limit+&apos;;&apos;,&apos;&apos;)+
IF(Labor_Time_limit__c,$Label.Labor_Time_limit+&apos;;&apos;,&apos;&apos;)+
IF(SN_Repeat__c,$Label.SN_Repeat+&apos;;&apos;,&apos;&apos;)+
IF(Replacement__c,$Label.Replacement+&apos;;&apos;,&apos;&apos;)+
IF(Warranty_Expired__c,$Label.Warranty_Expired+&apos;;&apos;,&apos;&apos;)+
IF(Wearable_Parts__c,$Label.Wearable_Parts+&apos;;&apos;,&apos;&apos;)+
IF(Receipt_Missing__c,$Label.Receipt_Missing+&apos;;&apos;,&apos;&apos;)+
IF(Email_Missing__c,$Label.Email_Missing+&apos;;&apos;,&apos;&apos;)+
IF(SN_Missing__c,$Label.SN_Missing+&apos;;&apos;,&apos;&apos;)+
IF(SN_not_related__c,$Label.SN_not_related+&apos;;&apos;,&apos;&apos;)+
IF(SN_not_found__c,$Label.SN_not_found+&apos;;&apos;,&apos;&apos;) + 
IF(Amount_Difference__c,$Label.CCM_InconsistentOfPrice+&apos;;&apos;,&apos;&apos;)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>Issue Point</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
