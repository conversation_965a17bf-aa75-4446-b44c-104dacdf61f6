/**
 * Author: <PERSON> 北美
 * 2023/10/09
 * Warratny Check功能
 */
public without sharing class CCM_EligibilityCheck {
    @AuraEnabled
    public static String GenerateVersionList(String productId){
        system.debug(productId);
        Map<String,Object> result = new Map<String,Object>();
        Set<String> versionList = new  Set<String>();
        if(String.isNotBlank(ProductId)){
            Product2 originProduct = [SELECT Id,ProductCode FROM Product2 WHERE id =: productId LIMIT 1];
            if(originProduct.ProductCode != null){
                Product2 pimProduct = [SELECT Id FROM Product2 WHERE ProductCode =: originProduct.ProductCode  AND Recordtype.Name = 'TLS_Product' LIMIT 1];
                 for(Kit_Item__c ki : [SELECT Id,Diagram_Version__c
                                    FROM Kit_Item__c
                                    WHERE Product__c =: pimProduct.Id
                                    AND Recordtype.Name = 'Products and Diagram'
                                    AND Status__c = 'A']){
                    versionList.add(ki.Diagram_Version__c);
                }
                result.put('Version', versionList);
            }
        }

        return JSON.serialize(result);
    }

    @AuraEnabled
    public static String GenerateBrandList(){
    	String accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        Set<String> brandList = new Set<String>();
        for(Sales_Program__c sp : [SELECT Id, Brands__c FROM Sales_Program__c WHERE RecordTypeId IN :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS AND Approval_Status__c = 'Approved' AND Customer__c =: accId]){
            brandList.add(sp.Brands__c);
        }
		return JSON.serialize(brandList);
    }
    /**
     * 返回product关联parts和带版本的爆炸图id
     */
    @AuraEnabled
    public static String SetupBomList(String productId, String version,String customerId){
        try{
            Map<String,Object> result = new Map<String,Object>();

            if(String.isNotEmpty(productId) ){
                Product2 originProduct = [SELECT Id,Order_Model__c FROM Product2 WHERE id =: productId LIMIT 1];
                if(originProduct.Order_Model__c != null){
                    //Product2 pimProduct = [SELECT Id FROM Product2 WHERE ProductCode =: originProduct.Order_Model__c   LIMIT 1];
                    List<String> kiList = new List<String>();
                    List<VersionInfo> versionList = new List<VersionInfo>();
                    Map<String,Kit_Item__c> mapItem = new Map<String,Kit_Item__c>();
                    Map<String,List<String>> mapRefrence = new Map<String,List<String>>();
                    for(Kit_Item__c ki : [SELECT Id,Diagram_Version__c FROM Kit_Item__c WHERE Product_Diag__c =: productId AND Recordtype.DeveloperName = 'Products_and_Diagram'  AND Diagram_Version__c != null ORDER BY Diagram_Page__c ASC]){
                        kiList.add(ki.Id);
                        mapItem.put(ki.Id,ki);
                    }
                    system.debug('kiList--->'+kiList);

                    if(kiList.size() > 0){
                        List<ContentDocumentLink> cdlList = [SELECT Id, LinkedEntityId, ContentDocumentId, ContentDocument.Title FROM ContentDocumentLink where LinkedEntityId IN: kiList ];

                        
                        for(ContentDocumentLink cdl : cdlList){
                            if(!mapRefrence.containsKey(mapItem.get(cdl.LinkedEntityId).Diagram_Version__c)){
                                List<String> contentIdList = new List<String>();
                                contentIdList.add(cdl.ContentDocumentId);
                                mapRefrence.put(mapItem.get(cdl.LinkedEntityId).Diagram_Version__c, contentIdList);
                            }else{
                                mapRefrence.get(mapItem.get(cdl.LinkedEntityId).Diagram_Version__c).add(cdl.ContentDocumentId);
                            }

                        }

                        for (String key : mapRefrence.keySet()) {
                            String label = key;
                            List<String> value = mapRefrence.get(key);
                            VersionInfo versionDetail = new VersionInfo();
                            versionDetail.label = label;
                            versionDetail.value = value;
                            versionList.add(versionDetail);
                            System.debug('Key: ' + key + ', Value: ' + value);
                        }


                        if(version == 'A'){
                            version = 'Spare part';
                        }else if(version == 'B'){
                            version = 'Spare part Version 1';
                        }else if(version == 'C'){
                            version = 'Spare part Version 2';
                        }
                        //ToDO Honey====> 目前产品上没有Current_Price__c 取值是什么。是ListPrice吗。那么Customer如何取呢 
                        //--->计算modifire的价格 时间是当前时间 customer是当前登录用户的customer 会存在价格册上没有这个产品的情况需要做筛选
                        //获取当前登录用户对应的customer 
                        Map<String,String> mapCustomerInfo  =  CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
                        if(String.isBlank(customerId)){
                            customerId = mapCustomerInfo.get('CustomerId');
                        }
                    List<Kit_Item__c> itemList = [SELECT Id,ExplosionID__c,Product__r.ProductCode,Parts__r.ProductCode,Parts__r.Item_Description_DE__c, Parts__r.Item_Description_EN__c, Parts__r.Brand_Name__c,Parts__r.Name ,Parts__r.Base_Cost__c,
                                                         EstimatedRepairTime__c
                                                    FROM Kit_Item__c
                                                    WHERE Product__c =: productId
                                                   // AND Version__c =: version
                                                    AND Recordtype.Name = 'Products and Parts'
                                                    AND Status__c = 'A'
                                                    ORDER BY ExplosionID__c ASC];
                        
                        List<Kit_Item__c> uniqueItemList = new List<Kit_Item__c>();
                        Set<String> partsCodeUniqueSet = new Set<String>();
                        for(Kit_Item__c item : itemList) {
                            if(partsCodeUniqueSet.contains(item.Parts__r.ProductCode)) {
                                continue;
                            }
                            partsCodeUniqueSet.add(item.Parts__r.ProductCode);
                            uniqueItemList.add(item);
                        }
                        Integer i =0;
                        List<String> ProductIdList = new List<String>();
                        Map<String,Double> mapProductId2Qty = new Map<String,Double>();
                        for(Kit_Item__c ki : uniqueItemList){
                            ProductIdList.add(ki.Parts__c);
                            mapProductId2Qty.put(ki.Parts__c, 1);
                        }

                        
                        //根据product获取产品价格
                        Map<String,Map<String,Object>> mapProductId2PriceInfo = new Map<String,Map<String,Object>>();
                        mapProductId2PriceInfo = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfo(CustomerId,ProductIdList,Date.today(),mapProductId2Qty);
                        for(Kit_Item__c ki : uniqueItemList){
                            if (mapProductId2PriceInfo.size() > 0){
                                Map<String,Object> PartsIdMapToPrice = mapProductId2PriceInfo.containsKey(ki.Parts__c) ? 
                                    mapProductId2PriceInfo.get(ki.Parts__c)  : new Map<String,Object>();
                                ki.Parts__r.Base_Cost__c = (Decimal)PartsIdMapToPrice.get(CCM_Constants.FINAL_PRICE);
                            }
                        }


                        result.put('contentIdList', versionList);
                        result.put('ProductList', uniqueItemList);
                    }
                }
            }

            return JSON.serialize(result);

        }catch(Exception e){
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
        }
        
    }

    /**  缺失前端版本选择-弃用 vince-20240624
    @AuraEnabled 
    public static String SetupBomList(String productId, String version,String customerId){
        try{
            Map<String,Object> result = new Map<String,Object>();

            if(String.isNotEmpty(productId) ){
                Product2 originProduct = [SELECT Id,Order_Model__c FROM Product2 WHERE id =: productId LIMIT 1];
                if(originProduct.Order_Model__c != null){
                    //Product2 pimProduct = [SELECT Id FROM Product2 WHERE ProductCode =: originProduct.Order_Model__c   LIMIT 1];
                    List<String> kiList = new List<String>();
                    //for(Kit_Item__c ki : [SELECT Id FROM Kit_Item__c WHERE Product_Diag__c =: productId AND Recordtype.DeveloperName = 'Products_and_Diagram'  AND Diagram_Version__c =: version ORDER BY Diagram_Page__c ASC]){
                    for(Kit_Item__c ki : [SELECT Id FROM Kit_Item__c WHERE Product_Diag__c =: productId AND Recordtype.DeveloperName = 'Products_and_Diagram'  ORDER BY Diagram_Page__c ASC]){
                        kiList.add(ki.Id);
                    }
                    system.debug('kiList--->'+kiList);

                    if(kiList.size() > 0){
                        List<ContentDocumentLink> cdlList = [SELECT Id, LinkedEntityId, ContentDocumentId, ContentDocument.Title FROM ContentDocumentLink where LinkedEntityId IN: kiList ];

                        List<String> contentIdList = new List<String>();
                        for(ContentDocumentLink cdl : cdlList){
                            contentIdList.add(cdl.ContentDocumentId);

                        }
                        system.debug('contentIdList--->'+contentIdList);

                        if(version == 'A'){
                            version = 'Spare part';
                        }else if(version == 'B'){
                            version = 'Spare part Version 1';
                        }else if(version == 'C'){
                            version = 'Spare part Version 2';
                        }
                        //ToDO Honey====> 目前产品上没有Current_Price__c 取值是什么。是ListPrice吗。那么Customer如何取呢 
                        //--->计算modifire的价格 时间是当前时间 customer是当前登录用户的customer 会存在价格册上没有这个产品的情况需要做筛选
                        //获取当前登录用户对应的customer 
                        Map<String,String> mapCustomerInfo  =  CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
                        if(String.isBlank(customerId)){
                            customerId = mapCustomerInfo.get('CustomerId');
                        }
                        List<Kit_Item__c> itemList = [SELECT Id,ExplosionID__c,Product__r.ProductCode,Parts__r.ProductCode,Parts__r.Item_Description_DE__c,Parts__r.Brand_Name__c,Parts__r.Name ,Parts__r.Base_Cost__c
                                                    FROM Kit_Item__c
                                                    WHERE Product__c =: productId
                                                   // AND Version__c =: version
                                                    AND Recordtype.Name = 'Products and Parts'
                                                    AND Status__c = 'A'
                                                    ORDER BY ExplosionID__c ASC];
                        Integer i =0;

                    
                        List<String> ProductIdList = new List<String>();
                        Map<String,Double> mapProductId2Qty = new Map<String,Double>();
                        for(Kit_Item__c ki : itemList){
                            ProductIdList.add(ki.Parts__c);
                            mapProductId2Qty.put(ki.Parts__c, 1);
                        }

                        
                        //根据product获取产品价格
                        Map<String,Map<String,Object>> mapProductId2PriceInfo = new Map<String,Map<String,Object>>();
                        mapProductId2PriceInfo = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfo(CustomerId,ProductIdList,Date.today(),mapProductId2Qty);
                        for(Kit_Item__c ki : itemList){
                            if (mapProductId2PriceInfo.size() > 0){
                                Map<String,Object> PartsIdMapToPrice = mapProductId2PriceInfo.containsKey(ki.Parts__c) ? 
                                    mapProductId2PriceInfo.get(ki.Parts__c)  : new Map<String,Object>();
                                ki.Parts__r.Base_Cost__c = (Decimal)PartsIdMapToPrice.get(CCM_Constants.FINAL_PRICE);
                            }
                        }


                        result.put('contentIdList', contentIdList);
                        result.put('ProductList', itemList);
                    }
                }
            }

            return JSON.serialize(result);

        }catch(Exception e){
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
        }
        
    }**/


    @AuraEnabled
    public static Boolean IsBatteryOrCharger(String productId){
        //honey修改。与北美逻辑不同需要查看是否存在Parts而不是查看电池类型
        List<Kit_Item__c> productList = [
            SELECT id, Parts__c, Parts__r.Order_Model__c, Accessory_SP__c, Product__c, Accessory_SP__r.Order_Model__c, Product__r.Order_Model__c
            FROM Kit_Item__c
            WHERE RecordType.DeveloperName in ('Products_and_Parts', 'Accessories_and_Parts') 
            AND (Accessory_SP__C = :productId Or Product__C = :productId)];
    	
    	if(productList == null || productList.size() > 0 ){
    		return false;
    	}else{
    		return true;
    	}
    }

    @AuraEnabled
    public static String eligibilityCheck(String partsList, String productId, String brand, String customerId){
		Map<String,Object> result = new Map<String,Object>();
		Decimal productPrice = 0.00;
		Decimal partsPrice = 0.00;
        List<Object> piList = (List<Object>)JSON.deserializeUntyped(partsList);
    	system.debug(partsList);
        Decimal productCost = 0.00;
	    if(piList.size() > 0){	
            List<String> partsCodeList = new List<String>();
            Map<String,Decimal> partCodeMapToQuantity = new Map<String,Decimal>();
            for(Object pi : piList){
                Map<String, Object> piMap = (Map<String, Object>)pi;
                partsCodeList.add((String)piMap.get('ProductCode'));
                partCodeMapToQuantity.put((String)piMap.get('ProductCode'),Decimal.valueOf((String)piMap.get('quantity')) );
            }
            //根据productCode获取产品Id 
            List<Product2> lstProduct = [
                SELECT Id,Order_Model__c FROM Product2 WHERE Order_Model__c IN : partsCodeList OR Id = :productId
            ];
            Map<String,String> MapId2OrderModel = new Map<String,String>();
            List<String> lstProductIds = new List<String>();
            lstProductIds.add(productId);
            Map<String,Decimal> mapProductId2Qty = new Map<String,Decimal>();
            mapProductId2Qty.put(productId, 1);
            for(Product2 objProduct : lstProduct){
                MapId2OrderModel.put(objProduct.Id, objProduct.Order_Model__c);
                lstProductIds.add(objProduct.Id);
                if(partCodeMapToQuantity.containsKey(objProduct.Order_Model__c)){
                    mapProductId2Qty.put(objProduct.Id, partCodeMapToQuantity.get(objProduct.Order_Model__c));
                }
               
                
            }
            system.debug('mapProductId2Qty--->'+mapProductId2Qty);
            //根据当前用户当前时间计算价格Map
            Map<String,String> mapCustomerInfo  =  CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
            if(String.isBlank(customerId)){
                //标识是protal端。自己获取customer信息
                customerId =  mapCustomerInfo.get('CustomerId');
            }
            
            //根据product获取产品价格
            Map<String,Map<String,Object>> mapProductId2PriceInfo = new Map<String,Map<String,Object>>();
            mapProductId2PriceInfo = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfo(CustomerId,lstProductIds,Date.today(),mapProductId2Qty);

            
            List<Kit_Item__c> kitItemsList = [SELECT Id,Parts__c,Parts__r.ProductCode,Parts__r.Order_Model__c,EstimatedRepairTime__c FROM Kit_Item__c WHERE Product__c =: productId AND RecordType.Name = 'Products and Parts' AND Parts__r.ProductCode IN: partsCodeList AND Status__c = 'A'  AND ReferenceId__c LIKE '%Spare part'];
            
            Decimal partsCostHours = 0.00;
            Set<String> setErrorModel = new Set<String>();
            if(kitItemsList.size() > 0){
                for(Kit_Item__c kit : kitItemsList){
                    if(mapProductId2PriceInfo ==null || !mapProductId2PriceInfo.containsKey(kit.Parts__c)){
                        setErrorModel.add(kit.Parts__r.Order_Model__c);
                    }
                    if(mapProductId2PriceInfo.containsKey(kit.Parts__c)){

                        Decimal SalesPrice =  (Decimal)mapProductId2PriceInfo.get(kit.Parts__c).get(CCM_Constants.SALES_PRICE);
                        productCost += (SalesPrice == null ? 0 :SalesPrice) ;
                        //ToDo若没有Hourse 取值是什么。目前环境中没有该字段 ---->EstimatedRepairTime__c min分钟数
                       if(partsCostHours < kit.EstimatedRepairTime__c || partsCostHours == 0.00){
                            partsCostHours = kit.EstimatedRepairTime__c;
                       }
                    }
                }
            }
            system.debug('productCost---->'+productCost);

            //partsCostHours = partsCostHours/60;
            List<Account> lstAccount = [
                SELECT Id,Labor_Rate__c,ORG_Code__c FROM Account WHERE Id  = :customerId
            ];
           
            IF(lstAccount.size() > 0){
                //已确认    不需要计算运费
                //Decimal shippingCost = getFreightFeeByProdPriceAndOrgCode(lstauthBrand[0].ORG_Code__c,productCost);
                if(mapProductId2PriceInfo == null || !mapProductId2PriceInfo.containsKey(productId)){
                    setErrorModel.add(MapId2OrderModel.get(productId));
                    
                }else{
                    productPrice = (Decimal)mapProductId2PriceInfo.get(productId).get(CCM_Constants.FINAL_PRICE);
                    //todo 改为配置   后面的  hourse *客户身上的Rate + Parts的价格后面的计算不需要
                    // Decimal Laber = lstAccount[0].Labor_Rate__c == null ? 0 : lstAccount[0].Labor_Rate__c;
                    Decimal laberRate = 0;
                    CCM_LaborUtil.LaborWrapper wrapper = CCM_LaborUtil.getLabor(customerId);
                    if(wrapper != null) {
                        laberRate = wrapper.laborRate;
                    }
                    Decimal price1 = partsCostHours * laberRate + productCost;
                    Decimal price2 = productPrice * Decimal.valueOf(Label.Check_Rate);
                    if( price2 >= price1 ){
                        result.put('Status', 'Success');
                        result.put('result', 'repair');
                        result.put('Message', System.Label.CCM_ProcessWithRepair);
                    }else{
                        result.put('Status', 'Success');
                        result.put('result', 'replacement');
                        result.put('Message', System.Label.CCM_ProcessWithReplacement);
                    }

                }
                
            }ELSE{
                result.put('Status', 'Fail');
                result.put('Message', System.Label.CCM_CostCannotCalculateError);

            }
            if(setErrorModel != null && setErrorModel.size()>0 ){
                result.put('Status', 'Fail');
                result.put('Message', System.Label.CCM_NoValidPriceError.replace('{0}', setErrorModel.toString()));
                // 'The current product Model ' + setErrorModel.toString() + ' No valid price, contact service team.'
                return JSON.serialize(result);
            }
    	}
    	
    	return JSON.serialize(result);
    }

    /*public static Map<String,Map<String,Object>> CaulateProduct2PriceInfo(String CustomerId,List<String> lstProductId, Date PricingDate, Map<String,Double> mapProductId2Qty){
        //先获取当前价格
        Map<String,Map<String,Object>> mapFeild2Price = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfo(CustomerId,lstProductId,PricingDate,mapProductId2Qty);
        //遍历产品。获取当前日期不存在的产品Id
        Set<String> setProductIds = new Set<String>();
        for(String productId : lstProductId){
            //判断map中是否存在价格
            if(!mapFeild2Price.containsKey(productId)){
                //如果不存在则需要后续计算。
                setProductIds.add(productId);
            }
        }
        if(setProductIds != null && setProductIds.size()>0){
            //表示存在没有查到价格的产品。需要进行后续计算
            //查询Order_Item数据
            List<Order_Item__c> lstOrderItem = new List<Order_Item__c>();
            lstOrderItem = [
                SELECT  o.List_Price__c, o.Pricing_Date__c, Order__c, o.Order__r.Customer__c,o.Product__c, o.Unit_Selling_Price__c FROM Order_Item__c o
                WHERE Product__c IN : setProductIds AND Order__r.Customer__c = :CustomerId  ORDER BY Pricing_Date__c ASC
            ];
            
            //查询所有Modifire 名字
            List<Modifier__c> lstModifire = [
                Select m.Id, m.Name from Modifier__c m
            ];
            Set<String> setModifireName = new Set<String>();
            for(Modifier__c objModifire : lstModifire){
                setModifireName.add(objModifire.Name);
            }
            Set<String> setOrderItemIds = new Set<String>();
            Map<String,Order_Item__c> mapProduct2Item = new Map<String,Order_Item__c>();
            for(Order_Item__c objItem : lstOrderItem){
                
                if(!mapProduct2Item.ContainsKey(objItem.product__c)){
                    //不包含表示没有相关价格。直接存入
                    mapProduct2Item.put(objItem.product__c,objItem);
                    setOrderItemIds.add(objItem.Id);
                }
                //只会存一个。存的最近的一个Date
            }
            List<Order_Line_Adjustment__c> lstAdjustomet = [
                SELECT Id,Amount__c, Name__c, Rate__c, Type__c ,
                Order_Item__c, Order_Item__r.Product__c
                FROM Order_Line_Adjustment__c  WHERE Order_Item__c IN : setOrderItemIds AND Name__c  IN :setModifireName AND Type__c = 'DIscount'
            ];
            Map<String,Order_Line_Adjustment__c> mapOrderItem2Adjustment = new Map<String,Order_Line_Adjustment__c>();
            for(Order_Line_Adjustment__c objOrderLine : lstAdjustomet){
                mapOrderItem2Adjustment.put(objOrderLine.Order_Item__c,objOrderLine);
            }

            //遍历Map为Map赋值
            for(String productId :mapProduct2Item.keySet()){
                Order_Item__c objOrderItem = mapProduct2Item.get(productId);
                Order_Line_Adjustment__c objOrderLine = mapOrderItem2Adjustment.get(objOrderItem.Id);
                Map<String,Object> mapOrderPrice = new Map<String,Object>();
                Decimal listPrice = objOrderItem.List_Price__c == null ? 0 :  objOrderItem.List_Price__c.setScale(2,RoundingMode.HALF_UP);
                if(objOrderLine != null){
                    Decimal Amount = objOrderLine.Amount__c == null ? 0 :  objOrderLine.Amount__c.setScale(2,RoundingMode.HALF_UP);
                    Decimal salesPrice = listPrice+Amount;
                    mapOrderPrice.put(CCM_Constants.FINAL_PRICE,salesPrice);
                    mapOrderPrice.put(CCM_Constants.LIST_PRICE,listPrice);
                     //计算折扣比例。List Price - unit Price / list Price
                    Decimal Discount = objOrderLine.Rate__c;
                    Decimal totalPrice =  salesPrice *  (mapProductId2Qty.get(productId) == null ? 0 : mapProductId2Qty.get(productId));
                    mapOrderPrice.put(CCM_Constants.STAND_DISCOUNT,Discount);
                    mapOrderPrice.put(CCM_Constants.SALES_PRICE,totalPrice);
                }else{
                    //为空表示没有Modifire
                    mapOrderPrice.put(CCM_Constants.FINAL_PRICE,listPrice);
                    mapOrderPrice.put(CCM_Constants.LIST_PRICE,listPrice);
                    Decimal totalPrice =  listPrice *  (mapProductId2Qty.get(productId) == null ? 0 : mapProductId2Qty.get(productId));
                    mapOrderPrice.put(CCM_Constants.STAND_DISCOUNT,0);
                }
               
                mapFeild2Price.put(productId, mapOrderPrice);

            }
            

        }
        return mapFeild2Price;

    }*/

    public class PartsItem {
        public String itemNumber;
        public String Name;
        public String partsId;
        public Integer quantity;
        public String brand;
        public String productCode;
        public Decimal validHours;
        public Decimal price;

    }

    public class PartsItems {
        public String Name;
        public String ProductCode;
        public Integer itemNumber;
        public String quantity;

    }

    public class VersionInfo {
        public String label;
        public List<String> value;
    }


    @AuraEnabled
    public static String searchPartsItem(String kitItemId){
        //Honey ToDo Valid Hourse 目前Kit Item数据库中没有这个字段 
        Kit_Item__c ki = [SELECT Id,Parts__r.Item_Number__c,Wearable_Parts__c,
                                Parts__r.ProductCode,Parts__r.Name,Parts__c,EstimatedRepairTime__c,
                                Parts__r.Brand__c
                             FROM Kit_Item__c 
                             WHERE Id =: kitItemId
                             AND RecordType.Name = 'Products and Parts'];
                             PartsItem pi = new PartsItem();                 
            if(ki != null){
                pi.itemNumber = ki.Parts__r.Item_Number__c;
	            pi.Name = ki.Parts__r.Name;
	            pi.partsId = ki.Parts__c;
	            pi.quantity = 1;
	            pi.brand = ki.Parts__r.Brand__c;
	            pi.productCode = ki.Parts__r.ProductCode;
                //字段来源是什么
	            pi.validHours = ki.EstimatedRepairTime__c;
            }    
        Map<String,Object> result = new Map<String,Object>();
        result.put('PartsList', ki);
        return JSON.serialize(result);
    }
   
    public CCM_EligibilityCheck() {

    }
}