/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-14-2024
 * @last modified by  : <EMAIL>
**/
@isTest
public with sharing class CCM_PushCustomerInfoService_Test{
    @TestSetup
    static void makeData(){
        Account channelAcc = new Account();
        channelAcc.Name = 'TestAcc1';
        channelAcc.Country_All__c = 'AD-Andorra';
        channelAcc.Postal_Code__c = '621044';
        channelAcc.Customer_Oracle_ID__c = '123552';
        channelAcc.AccountNumber = 'E01234';
        channelAcc.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        channelAcc.Approval_Status__c = 'Approved';
        channelAcc.Status__c = 'DisContinue';
        channelAcc.Sales_Channel__c = 'Internal';
        insert channelAcc;
        Contact contact1 = new Contact(FirstName = 'Test', Role__c = 'Fleet Manager', Lastname = 'McTesty', AccountId = channelAcc.Id, Email = System.now().millisecond() + '<EMAIL>');
        insert contact1;
        Account_Address__c objAccountAddress = new Account_Address__c();
        objAccountAddress.Name = 'address1';
        objAccountAddress.Country__c = 'DE';
        objAccountAddress.City__c = 'Steinheim';
        objAccountAddress.Address1__c = 'Autenbacherstr. 11';
        objAccountAddress.customer__c = channelAcc.Id;
        objAccountAddress.RecordTypeId = CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID;
        insert objAccountAddress;
        // Create test sales programs
        Sales_Program__c salesP = new Sales_Program__c(Name = 'Test Sales Program', Brands__c = 'Test Brand', Customer__c = channelAcc.Id, Payment_Term__c = 'EEG06', RecordTypeId = CCM_Constants.AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SERVICE_CUSTOMIZED_Id);
        insert salesP;
    }
    @isTest
    static void testPushCustInfo(){
        Test.startTest();
        Account acc1 = [select id
                        from Account
                        where name = 'TestAcc1'
                        limit 1];
        CCM_PushCustomerInfoService.pushCustInfo(acc1.Id, false, '');

        Territory__c testRegion = new Territory__c(Name = 'testRegion', Territory_Name__c = 'testRegion', Type__c = 'Region');
        insert testRegion;
        Territory__c testplannig = new Territory__c(Name = 'testplannig', Parent_Territory__c = testRegion.Id, Territory_Name__c = 'testplannig', Type__c = 'Planning Territory');
        insert testplannig;
        acc1.Account_Description__c = 'test';
        acc1.Planning_Territory_No__c = testplannig.Id;
        acc1.Allow_Backorder__c = 'Yes';
        acc1.Standard_Edit_Process__c = false;
        update acc1;
        CCM_PushCustomerInfoService.pushCustInfo(acc1.Id, false, '');
        Account channelAcc = new Account();
        channelAcc.Name = 'TestAcc1';
        channelAcc.Country_All__c = 'AD-Andorra';
        channelAcc.Postal_Code__c = '621044';
        channelAcc.Customer_Oracle_ID__c = '24550';
        channelAcc.AccountNumber = 'E2334';
        channelAcc.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        channelAcc.Allow_Backorder__c = 'No';
        channelAcc.Send_dunning_letters__c = '';
        channelAcc.Approval_Status__c = 'Approved';
        insert channelAcc;
        CCM_PushCustomerInfoService.pushCustInfo(channelAcc.Id, false, '');

        Test.stopTest();
    }

    @IsTest
    static void testSyncAssociationGroup(){
        
        Test.startTest();
        Account associationGroup = new Account();
        associationGroup.Name = 'AssociationGroup';
        associationGroup.Country_All__c = 'AD-Andorra';
        associationGroup.Postal_Code__c = '621044';
        associationGroup.Customer_Oracle_ID__c = '1235521';
        associationGroup.AccountNumber = 'E012345';
        associationGroup.RecordTypeId = CCM_Constants.ASSOCIATIONGROP_RECORDTYPEID;
        associationGroup.Approval_Status__c = 'Approved';
        associationGroup.Status__c = 'DisContinue';
        associationGroup.Sales_Channel__c = 'Internal';
        insert associationGroup;

        Account channelCustomer = [SELECT Id FROM Account WHERE Name = 'TestAcc1' LIMIT 1];
        channelCustomer.Association_Group__c = associationGroup.Id;
        update channelCustomer;
        
        CCM_PushCustomerInfoService.pushCustInfo(channelCustomer.Id, false, '');
        CCM_PushCustomerInfoService.pushCustInfo(channelCustomer.Id, true, 'removeag');
        Test.stopTest();
        
    }
}