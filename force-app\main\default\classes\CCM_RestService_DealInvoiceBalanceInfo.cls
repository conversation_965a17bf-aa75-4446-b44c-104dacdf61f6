/**********************************************************************
 * 
 *
 * @url: /services/apexrest/CCM_RestService_DealInvoiceBalanceInfo
 * 
 * @description:Sync Invoice Balance from EBS to Salesforce
 * 
 * @author:<PERSON>
 * 
 * @date:2023-06-14
 * 
 * */
@RestResource(urlMapping='/CCM_RestService_DealInvoiceBalanceInfo')
global without sharing class CCM_RestService_DealInvoiceBalanceInfo {
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        String resStr = req.requestBody.toString();
        resObj.Process_Result = new List<ReturnItem>();
        List<Accounting_Balance__c> lstToUpsert = new List<Accounting_Balance__c>();

        //获取Customer Oracle Id、Customer SF ID
        Set<String> setCustOracleId = new Set<String>();
        Map<String,String> mapCustOIdToSFId = new Map<String,String>();
        try{
            if(!resStr.startsWith('[')){
                resStr = '[' + resStr + ']';
            }
            resStr = resStr.replace('\'','\"');
            reqObjList = parse(resStr);
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObjList);
            //获取请求体中的bill to code
            Set<String> setBillToCode = new Set<String>();
            Set<String> setInvoiceOracleId = new Set<String>();
            Set<String> invoiceNumbers = new Set<String>();
            Set<Date> dueDates = new Set<Date>();
            Map<String,String> mapCodeToAddress = new Map<String,String>();
            Map<String,String> mapInoiceId = new Map<String,String>();
            if (reqObjList != null && reqObjList.size() > 0) {
                for (ReqestObj reqObj : reqObjList) {
                    setBillToCode.add(reqObj.BILL_TO);
                    setInvoiceOracleId.add(reqObj.CUSTOMER_TRX_ID);
                    setCustOracleId.add(reqObj.CUSTOMER_SF_NUMBER);
                    if(String.isNotBlank(reqObj.INVOICE_NUMBER)) {
                        invoiceNumbers.add(reqObj.INVOICE_NUMBER);
                    }
                    if(String.isNotBlank(reqObj.DUE_DATE)) {
                        Date dueDate = Date.valueOf(reqObj.DUE_DATE);
                        dueDates.add(dueDate);
                    }
                }
            }
            //获取Customer SF Id
            List<Account> lstAccount = new List<Account>();
            lstAccount = [SELECT Id,Customer_Oracle_ID__c FROM Account WHERE Customer_Oracle_ID__c in :setCustOracleId]; 
            for(Account acc : lstAccount){
                mapCustOIdToSFId.put(acc.Customer_Oracle_ID__c, acc.Id);
            }
            //获取 invoice oracle id 对应的Invoice sf Id
            List<Invoice__c> lstInvoice = new List<Invoice__c>();
            lstInvoice = [SELECT Id ,Invoice_OracleID__c FROM Invoice__c where Invoice_OracleID__c in :setInvoiceOracleId];
            for(Invoice__c i : lstInvoice){
                mapInoiceId.put(i.Invoice_OracleID__c,i.Id);
            }
            //获取bill to 对应的Address
            List<Account_Address__c> lstAddress = new List<Account_Address__c>(); 
            lstAddress = [SELECT Id,Customer_Line_Oracle_ID__c FROM Account_Address__c   WHERE Customer_Line_Oracle_ID__c in :setBillToCode];
            if(lstAddress.size() > 0){
                for(Account_Address__c data : lstAddress) {
                    mapCodeToAddress.put(data.Customer_Line_Oracle_ID__c,data.Id);
                }  
            }

            // query existing accounting balance
            Map<String, String> existingAccountingBalanceMap = new Map<String, String>();
            for(Accounting_Balance__c existingAccountingBalance : [SELECT Id, Due_Date__c, Invoice_Number__c 
                                                                   FROM Accounting_Balance__c WHERE Due_Date__c IN :dueDates AND Invoice_Number__c IN :invoiceNumbers]) {
                String key = existingAccountingBalance.Invoice_Number__c + String.valueOf(existingAccountingBalance.Due_Date__c);
                existingAccountingBalanceMap.put(key, existingAccountingBalance.Id);
            }

            //遍历请求体中的Invoice Balance
            if (reqObjList != null && reqObjList.size() > 0) {
                for (ReqestObj reqObj : reqObjList) {
                    //解析Json
                    Accounting_Balance__c objBalance = new Accounting_Balance__c();
                    objBalance.Balance_Oracle_Id__c = reqObj.BALANCE_ORACLE_ID;
                    objBalance.Customer_SF_Number__c = reqObj.CUSTOMER_SF_NUMBER;
                    if(mapCustOIdToSFId.containsKey(reqObj.CUSTOMER_SF_NUMBER)){
                        objBalance.Customer__c = mapCustOIdToSFId.get(reqObj.CUSTOMER_SF_NUMBER);
                    }
                    
                    objBalance.BillTo_Text__c = reqObj.BILL_TO;
                    if(mapCodeToAddress.containsKey(reqObj.BILL_TO)){
                        objBalance.BillTo__c = mapCodeToAddress.get(reqObj.BILL_TO);
                    }
                    
                    objBalance.Invoice_Oracle_Id__c = reqObj.CUSTOMER_TRX_ID;
                    if(mapInoiceId.containsKey(reqObj.CUSTOMER_TRX_ID)){
                        objBalance.invoice__c =  mapInoiceId.get(reqObj.CUSTOMER_TRX_ID);
                    }
                    objBalance.Invoice_Number__c = reqObj.INVOICE_NUMBER;
                    objBalance.Invoice_Date__c = String.isNotBlank(reqObj.INVOICE_DATE) ? Date.valueOf(reqObj.INVOICE_DATE) : null;
                    objBalance.Gl_Date__c =  String.isNotBlank(reqObj.GL_DATE) ? Date.valueOf(reqObj.GL_DATE) : null;
                    objBalance.Due_Date__c = String.isNotBlank(reqObj.DUE_DATE) ? Date.valueOf(reqObj.DUE_DATE) : null;
                    objBalance.CurrencyIsoCode = reqObj.CURRENCY_CODE;
                    objBalance.Payment_Term__c = reqObj.PAYMENT_TERM;
                    objBalance.Amt_Original__c = SystemUtils.convertToDigit(reqObj.AMT_ORIGINAL);
                    objBalance.Amt_Due_Remaining__c = SystemUtils.convertToDigit(reqObj.AMT_DUE_REMAINING);
                    objBalance.Commentes__c = reqObj.COMMENTES;
                    objBalance.ORG_Code__c = reqObj.ORG_CODE;
                    //add start vince2023-10-13
                    objBalance.Credit_note_for_Invoice__c = reqObj.CREDIT_NOTE_FOR_INVOICE;
                    //add end vince2023-10-13

                    String key = objBalance.Invoice_Number__c;
                    if(objBalance.Due_Date__c != null) {
                        key += String.valueOf(objBalance.Due_Date__c);
                    }
                    if(existingAccountingBalanceMap.containsKey(key)) {
                        objBalance.Id = existingAccountingBalanceMap.get(key);
                    }
                    lstToUpsert.add(objBalance);
                }
                System.debug(LoggingLevel.INFO,'***lstToUpsert : ' + lstToUpsert);
                Database.UpsertResult[] resList = Database.upsert(lstToUpsert, false);
                for (Integer i = 0 ; i < resList.size() ; i++) {
                    if (!resList.get(i).isSuccess()) {
                        Database.Error[] err = resList.get(i).getErrors();
                        ReturnItem request = new ReturnItem();
                        request.External_Id = reqObjList.get(i).CUSTOMER_TRX_ID;
                        request.Error_Message = 'This InvoiceBalance was failed saving in Salesforce';
                        request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                        resObj.Process_Result.add(request);
                    }
                }

                if (resObj.Process_Result.size() == 0) {
                    resObj.Process_Status = 'Success';
                    String logId = Util.logIntegration('InvoiceBalance Log','CCM_RestService_DealInvoiceBalanceInfo','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(reqObjList), JSON.serialize(resObj));
                }else {
                    resObj.Process_Status = 'Fail';
                    String logId = Util.logIntegration('InvoiceBalance Exception','CCM_RestService_DealInvoiceBalanceInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                    Util.pushExceptionEmail('Accept InvoiceBalance Info',logId,getMailErrorMessage(resObj));
                }

                System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));
            }else{
                resObj.Process_Status = 'Fail';
                ReturnItem empty = new ReturnItem();
                empty.Error_Message = 'Empty JSON';
                empty.Error_Detail = 'Empty JSON';
                resObj.Process_Result.add(empty);

                String logId = Util.logIntegration('InvoiceBalance Exception','CCM_RestService_DealInvoiceBalanceInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept InvoiceBalance Info',logId,getMailErrorMessage(resObj));
            }
        }catch (Exception e) {
            resObj.Process_Status = 'Fail';
            ReturnItem request = new ReturnItem();
            request.Error_Message = 'This InvoiceBalance was failed saving in Salesforce';
            request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
            resObj.Process_Result.add(request);
            String logId = Util.logIntegration('InvoiceBalance Exception','CCM_RestService_DealInvoiceBalanceInfo','POST',
                                               JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept InvoiceBalance Info',logId,getMailErrorMessage(resObj));
            return resObj;
        }
        System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
        return resObj;
    }

    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
       	errContent += 'Process Status : Fail<br/><br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
            	errContent += 'External ID : ' + Item.External_Id + '<br/>';
            	errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
            	errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>';
            }
        }
        return errContent;
    }

    global class ReqestObj {
        global String BALANCE_ORACLE_ID;
        global String CUSTOMER_SF_NUMBER;
        global String BILL_TO;
        global String CUSTOMER_TRX_ID;
        global String INVOICE_NUMBER;
        global String INVOICE_DATE;
        global String GL_DATE;
        global String DUE_DATE;
        global String CURRENCY_CODE;
        global String PAYMENT_TERM;
        global String AMT_ORIGINAL;
        global String AMT_DUE_REMAINING;
        global String COMMENTES;
        global String ORG_CODE;

        //add start vince2023-10-13
        global String CREDIT_NOTE_FOR_INVOICE;
        //add end vince 2023-10-13

        global String Attribute1;
        global String Attribute2;
        global String Attribute3;
        global String Attribute4;
        global String Attribute5;
        global String Attribute6;
        global String Attribute7;
        global String Attribute8;
        global String Attribute9;
        global String Attribute10;


    }

	global static List<ReqestObj> parse(String jsonStr) {
		return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }

    global class ResultObj {
        global String Process_Status;
        //global Integer IgnoreSize;
        global List<ReturnItem> Process_Result;
    }

    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
}