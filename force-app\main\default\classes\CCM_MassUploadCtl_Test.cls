/**
 * Honey
 * 测试类
 */
@isTest
public without sharing class CCM_MassUploadCtl_Test {
    @isTest
    public static void testCheck(){
        Account account = new Account(
            Name = 'Sample Account',
            Company__c = 'Sample Company',
            AccountNumber = 'Sample Account Number',
            CurrencyIsoCode = 'EUR',
            Sales_Channel__c = 'OPE Dealer',
            RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName = 'Association_Group']
        );
        insert account;
   
    Pricebook2 testPricebook = new Pricebook2(Name = 'Test Pricebook');
    insert testPricebook;
    Modifier__c modifier1 = new Modifier__c(Name = 'Test');
    insert modifier1;
    Modifier__c modifier2 = new Modifier__c(Name = 'Test2');
    insert modifier2;
   
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName IN ('Billing_Address')],
            Status__c = true
        );
        insert billingAddress;
        Product2 product1 = new Product2();
        product1.Source__c = 'EBS';
        product1.Name = 'BH1001';
        product1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        product1.ExternalId__c = 'BH1001';
        product1.Order_Model__c = 'BH1001';
        product1.Master_Product__c = 'BH1001';
        product1.Item_Description_DE__c = 'DEDescription';
        product1.Item_Description_EN__c = 'ENDescription';
        product1.Units_Per_Pallet_EA__c = 6;
        product1.Units_Per_Inner_BOX_EA__c = 5;
        product1.Units_Per_Master_Carton_EA__c = 7;
        insert product1;
        Product2 product3 = new Product2();
        product3.Source__c = 'EBS';
        product3.Name = 'E1213';
        product3.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        product3.ExternalId__c = 'E1213';
        product3.Order_Model__c = 'E1213';
        product3.Master_Product__c = 'E1213';
        product3.Item_Description_DE__c = 'DEDescription';
        product3.Item_Description_EN__c = 'ENDescription';
        product3.Units_Per_Pallet_EA__c = 6;
        product3.Units_Per_Inner_BOX_EA__c = 5;
        product3.Units_Per_Master_Carton_EA__c = 7;
        insert product3;
         Modifier_Entry__c modifierentry = new Modifier_Entry__c(Modifier_Header_Number__c = 'M123', ExternalID__c = 'M123', Modifier__c = modifier1.Id);
    insert modifierentry;
    Pricebook_Entry__c testPricebookEntry = new Pricebook_Entry__c(PriceBook__c = testPricebook.Id, Product__c = product1.Id, End_Date__c = Date.today(), Start_Date__c = Date.today().addDays(-7), IsActive__c = true, UnitPrice__c = 100);
    insert testPricebookEntry;
    Pricebook_Entry__c testPricebookEntry2 = new Pricebook_Entry__c(PriceBook__c = testPricebook.Id, Product__c = product3.Id, End_Date__c = Date.today(), Start_Date__c = Date.today().addDays(-7), UnitPrice__c = 200);
    insert testPricebookEntry2;
    Sales_Program__c testSalesProgram = new Sales_Program__c(List_Price_1__c = testPricebook.Id, List_Price_2__c = testPricebook.Id, List_Price_3__c = testPricebook.Id, Customer__c = account.Id, Price_Book__c = testPricebook.Id, Modifier_1__c = modifier1.Id, Modifier_2__c = modifier2.Id, Order_Type__c = 'Sales Order - DSV');
    insert testSalesProgram;
    MasterProductPrice__c testMasterProductPrice = new MasterProductPrice__c(Account__c = account.Id, Product__c = product1.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today(), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = false, Modifier_Entry__c = modifierentry.Id,CurrencyIsoCode = 'EUR');
    insert testMasterProductPrice;
        List<CCM_MassUploadCtl.MassUploadInfo> lstmapcheckValue = new List<CCM_MassUploadCtl.MassUploadInfo> ();
        CCM_MassUploadCtl.MassUploadInfo mapfeild2Value = new CCM_MassUploadCtl.MassUploadInfo();
        mapfeild2Value.claimReferenceNumber = '001';
        mapfeild2Value.masterReferenceNumber = 'claim-001';
        mapfeild2Value.claimDate = '2023-01-01';
        mapfeild2Value.billAddressId = billingAddress.Id;
        mapfeild2Value.purchaseDate = '2023-01-01';
        mapfeild2Value.repairDate = '2023-01-01';
        mapfeild2Value.failureDate = '2023-01-01';
        mapfeild2Value.emailAddress = '<EMAIL>';
        mapfeild2Value.userType = 'Commercial';
        mapfeild2Value.owner = 'test';
        mapfeild2Value.ownerAddress = 'testaddress';
        mapfeild2Value.ownerCity = 'testCity';
        mapfeild2Value.ownerPostalCode = '12312';
        mapfeild2Value.modelNumber = 'BH1001';
        mapfeild2Value.serialNumber = 'SN123212';
        mapfeild2Value.failureCode = '0 - Battery/Charger';
        mapfeild2Value.failureDescription = 'testDescription';
        mapfeild2Value.repairWork = '132123';
        mapfeild2Value.partNumber = 'BH1001';
        mapfeild2Value.quantity = '1';
        mapfeild2Value.currencyCode = 'EUR';
        mapfeild2Value.customerUnitPrice = '20';
        lstmapcheckValue.add(mapfeild2Value);
        CCM_MassUploadCtl.MassUploadInfo mapfeild2Value2 = new CCM_MassUploadCtl.MassUploadInfo();
        mapfeild2Value2.claimReferenceNumber = '001';
        mapfeild2Value2.masterReferenceNumber = 'claim-001';
        mapfeild2Value2.claimDate = '2023-01-01';
        mapfeild2Value2.billAddressId = billingAddress.Id;
        mapfeild2Value2.purchaseDate = '2023-01-01';
        mapfeild2Value2.repairDate = '2023-01-01';
        mapfeild2Value2.failureDate = '2023-01-01';
        mapfeild2Value2.emailAddress = '<EMAIL>';
        mapfeild2Value2.userType = 'Commercial';
        mapfeild2Value2.owner = 'test';
        mapfeild2Value2.ownerAddress = 'testaddress';
        mapfeild2Value2.ownerCity = 'testCity';
        mapfeild2Value2.ownerPostalCode = '12312';
        mapfeild2Value2.modelNumber = 'BH1001';
        mapfeild2Value2.serialNumber = 'SN123212';
        mapfeild2Value2.failureCode = '0 - Battery/Charger';
        mapfeild2Value2.failureDescription = 'testDescription';
        mapfeild2Value2.repairWork = '132123';
        mapfeild2Value2.partNumber = Label.Laber_Hourse;
        mapfeild2Value2.quantity = '1';
        mapfeild2Value2.currencyCode = 'EUR';
        mapfeild2Value2.customerUnitPrice = '20';
        lstmapcheckValue.add(mapfeild2Value2);
        Map<String,String> mapresult = CCM_MassUploadCtl.checkWarrantyInfo(JSON.serialize(lstmapcheckValue) ,account.Id);

        if(mapresult.get('Status') == CCM_Constants.SUCCESS){
            String uploadString = CCM_MassUploadCtl.uploadWarrantyClaim(mapresult.get('Data'),account.Id);
        }
       
        

    }
    @isTest
    public static void testCheck2(){
        Account account = new Account(
            Name = 'Sample Account',
            Company__c = 'Sample Company',
            AccountNumber = 'Sample Account Number',
            CurrencyIsoCode = 'EUR',
            RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName = 'Association_Group']
        );
        insert account;
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName IN ('Billing_Address')],
            Status__c = true
        );
        insert billingAddress;
        Product2 product1 = new Product2();
        product1.Source__c = 'EBS';
        product1.Name = 'BH1001';
        product1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        product1.ExternalId__c = 'BH1001';
        product1.Order_Model__c = 'BH1001';
        product1.Master_Product__c = 'BH1001';
        product1.Item_Description_DE__c = 'DEDescription';
        product1.Item_Description_EN__c = 'ENDescription';
        product1.Units_Per_Pallet_EA__c = 6;
        product1.Units_Per_Inner_BOX_EA__c = 5;
        product1.Units_Per_Master_Carton_EA__c = 7;
        insert product1;
        Product2 product3 = new Product2();
        product3.Source__c = 'EBS';
        product3.Name = 'E1213';
        product3.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        product3.ExternalId__c = 'E1213';
        product3.Order_Model__c = 'E1213';
        product3.Master_Product__c = 'E1213';
        product3.Item_Description_DE__c = 'DEDescription';
        product3.Item_Description_EN__c = 'ENDescription';
        product3.Units_Per_Pallet_EA__c = 6;
        product3.Units_Per_Inner_BOX_EA__c = 5;
        product3.Units_Per_Master_Carton_EA__c = 7;
        insert product3;
        List<CCM_MassUploadCtl.MassUploadInfo> lstmapcheckValue = new List<CCM_MassUploadCtl.MassUploadInfo> ();
        CCM_MassUploadCtl.MassUploadInfo mapfeild2Value = new CCM_MassUploadCtl.MassUploadInfo();
        mapfeild2Value.claimReferenceNumber = '001';
        mapfeild2Value.masterReferenceNumber = 'claim-001';
        mapfeild2Value.claimDate = '';
        mapfeild2Value.billAddressId = billingAddress.Id;
        mapfeild2Value.purchaseDate = '2023-01-011';
        mapfeild2Value.repairDate = '';
        mapfeild2Value.failureDate = '';
        mapfeild2Value.emailAddress = '<EMAIL>';
        mapfeild2Value.userType = 'Commercial';
        mapfeild2Value.owner = 'test';
        mapfeild2Value.ownerAddress = 'testaddress';
        mapfeild2Value.ownerCity = 'testCity';
        mapfeild2Value.ownerPostalCode = '12312';
        mapfeild2Value.modelNumber = 'BH1001';
        mapfeild2Value.serialNumber = '';
        mapfeild2Value.failureCode = '0 - Battery/Charger';
        mapfeild2Value.failureDescription = 'testDescription';
        mapfeild2Value.repairWork = '132123';
        mapfeild2Value.partNumber = 'BH1001';
        mapfeild2Value.quantity = '1';
        mapfeild2Value.currencyCode = 'EUR';
        mapfeild2Value.customerUnitPrice = '20';
        lstmapcheckValue.add(mapfeild2Value);
        CCM_MassUploadCtl.MassUploadInfo mapfeild2Value2 = new CCM_MassUploadCtl.MassUploadInfo();
        mapfeild2Value2.claimReferenceNumber = '001';
        mapfeild2Value2.masterReferenceNumber = 'claim-001';
        mapfeild2Value2.claimDate = '2023-01-01';
        mapfeild2Value2.billAddressId = billingAddress.Id;
        mapfeild2Value2.purchaseDate = '2023-01-01';
        mapfeild2Value2.repairDate = '2023-01-01';
        mapfeild2Value2.failureDate = '2023-01-01';
        mapfeild2Value2.emailAddress = '<EMAIL>';
        mapfeild2Value2.userType = 'Commercial';
        mapfeild2Value2.owner = 'test';
        mapfeild2Value2.ownerAddress = 'testaddress';
        mapfeild2Value2.ownerCity = 'testCity';
        mapfeild2Value2.ownerPostalCode = '12312';
        mapfeild2Value2.modelNumber = 'BH1001';
        mapfeild2Value2.serialNumber = 'EBA06171102571X';
        mapfeild2Value2.failureCode = '0 - Battery/Charger';
        mapfeild2Value2.failureDescription = 'testDescription';
        mapfeild2Value2.repairWork = '132123';
        mapfeild2Value2.partNumber = '12311';
        mapfeild2Value2.quantity = '1';
        mapfeild2Value2.currencyCode = 'EUR1';
        mapfeild2Value2.customerUnitPrice = '20';
        lstmapcheckValue.add(mapfeild2Value2);
        Map<String,String> mapresult = CCM_MassUploadCtl.checkWarrantyInfo(JSON.serialize(lstmapcheckValue) ,account.Id);
        Set<String> setpartsModel = new Set<String>();
        setpartsModel.add('12311');
        set<String> setActualExistModels = new Set<String>();
        setActualExistModels.add('12311');
        set<String> setSnCheck = new Set<String>();
        setSnCheck.add('EBA06171102571X');
        Set<String> seterror = CCM_MassUploadCtl.checkPartModel(setpartsModel,setActualExistModels,lstmapcheckValue);
        Set<String> seterror1 = CCM_MassUploadCtl.checkSnInfo(setSnCheck,lstmapcheckValue);
        if(mapresult.get('Status') == CCM_Constants.SUCCESS){
            String uploadString = CCM_MassUploadCtl.uploadWarrantyClaim(mapresult.get('Data'),account.Id);
        }
       
        

    }
    public CCM_MassUploadCtl_Test() {

    }
}