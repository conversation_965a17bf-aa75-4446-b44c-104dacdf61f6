@IsTest
public without sharing class CCM_Community_InvoiceTabCtlTest{
    @isTest
    static void testSearchInvoice(){
        // 创建测试数据
        Account acc = new Account();
        acc.Name = 'Test Company';
        acc.Country_All__c = 'AD-Andorra';
        acc.Postal_Code__c = '621044';
        acc.Consumer_Status__c = 'Active';
        acc.RecordTypeId = CCM_Constants.CommercialConsumer_RECORDTYPEID;
        acc.Consumer_Status__c = 'Waiting Customer Approval';
        insert acc;
        Contact testContact = new Contact(FirstName = 'Test', Lastname = 'McTesty', AccountId = acc.Id, Email = System.now().millisecond() + '<EMAIL>');
        insert testContact;
        User thisUser = [select Id
                         from User
                         where Id = :UserInfo.getUserId()];
        User testUser = new User();
        System.runAs(thisUser){
            testUser = new User(ProfileId = UserInfo.getProfileId(), Username = System.now().millisecond() + '<EMAIL>', Alias = 'batman', Email = '<EMAIL>', EmailEncodingKey = 'UTF-8', Firstname = 'Bruce', Lastname = 'Wayne', LanguageLocaleKey = 'en_US', LocaleSidKey = 'en_US', TimeZoneSidKey = 'America/Chicago');
            insert testUser;

        }
        String orderRecordId1 = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('PreSeason_Order').getRecordTypeId();
        String orderRecordId2 = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Warranty_Claim').getRecordTypeId();

        Order testOrder1 = new Order(AccountId = acc.Id, RecordTypeId = orderRecordId1, EffectiveDate = Date.today(), Status = 'Draft');
        insert testOrder1;
        Order testOrder2 = new Order(AccountId = acc.Id, RecordTypeId = orderRecordId1, EffectiveDate = Date.today(), Status = 'Draft');
        insert testOrder2;
        Order testOrder3 = new Order(AccountId = acc.Id, RecordTypeId =orderRecordId2, EffectiveDate = Date.today(), Status = 'Draft');
        insert testOrder3;
        Training_Order__c to= new Training_Order__c(Customer__c=acc.Id);
        insert to;
        Invoice__c testInvoice1 = new Invoice__c(Scene_Type__c = 'Sales Order', Customer__c = acc.Id, Order__c = testOrder1.Id, Invoice_Number__c = 'INV-001', Your_VAT_Re_No__c = 'VAT-001', Invoice_Date__c = Date.valueOf('2023-10-10'), Order_Date__c = testOrder1.EffectiveDate, Credit_note_for_Invoice__c = 'INV');
        insert testInvoice1;
        Invoice__c testInvoice2 = new Invoice__c(Scene_Type__c = 'Training Request', Customer__c = acc.Id, Training_Order__c = to.Id, Order__c = testOrder2.Id, Invoice_Number__c = 'INV-001', Your_VAT_Re_No__c = 'VAT-001', Invoice_Date__c = Date.valueOf('2023-10-10'), Order_Date__c = testOrder2.EffectiveDate, Credit_note_for_Invoice__c = 'INV');
        insert testInvoice2;
        Invoice__c testInvoice3 = new Invoice__c(Scene_Type__c = 'Warranty Claim', Customer__c = acc.Id, Order__c = testOrder3.Id, Invoice_Number__c = 'INV-001', Your_VAT_Re_No__c = 'VAT-001', Invoice_Date__c = Date.valueOf('2023-10-10'), Order_Date__c = testOrder3.EffectiveDate, Credit_note_for_Invoice__c = 'INV');
        insert testInvoice3;
        Invoice__c testInvoice4 = new Invoice__c(Scene_Type__c = 'Sales Order', Order__c = testOrder3.Id, Invoice_Number__c = 'INV-001', Your_VAT_Re_No__c = 'VAT-001', Invoice_Date__c = Date.valueOf('2023-10-10'), Order_Date__c = testOrder3.EffectiveDate, Credit_note_for_Invoice__c = 'INV');
        insert testInvoice4;

        Invoice_Item__c  invItem = new Invoice_Item__c(Invoice__c = testInvoice3.id);
        insert invItem;

        List<Invoice__c> testparam = [select id from Invoice__c];

        // 调用Apex方法进行测试
        Test.startTest();
        CCM_Community_InvoiceTabCtl.searchInvoice(1, 10, 'INV-001', 'VAT-001', 'INV', '2022-01-01', '2023-12-12', 'salesorder');
        CCM_Community_InvoiceTabCtl.searchInvoice(1, 10, 'INV-001', 'VAT-001', 'INV', '2022-01-01', '2023-12-12', 'trainingorder');
        CCM_Community_InvoiceTabCtl.searchInvoice(1, 10, 'INV-001', 'VAT-001', 'INV', '2022-01-01', '2023-12-12', 'warrantyclaim');
        CCM_Community_InvoiceTabCtl.searchInvoice(1, 10, 'INV-001', 'VAT-001', 'INV', '2022-01-01', '2023-12-12', 'warrantyorder');
        CCM_Community_InvoiceTabCtl.searchInvoice(1, 10, null, null, null, null, null, null);
        CCM_Community_InvoiceTabCtl.getTotalAmountMap(testparam);
        CCM_Community_InvoiceTabCtl.getCustomerCurrency();
        Test.stopTest();
    }
}