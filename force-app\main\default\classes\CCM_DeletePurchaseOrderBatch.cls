/**
 * Author : Honey Added 
 * Description : 每天删除30天前为草稿状态的PO订单
 */
public with sharing class CCM_DeletePurchaseOrderBatch implements Database.Batchable<sObject>,Database.Stateful{
    public Database.QueryLocator start(Database.BatchableContext BC){
        String status= CCM_Constants.DRAFT_STOCK;
        String query = 'SELECT Id FROM Purchase_Order__c where CreatedDate < LAST_N_DAYS:10 AND Status__c = :status AND CreatedBy.Profile.UserLicense.Name = \'Partner Community\''; 

        return Database.getQueryLocator(query);
    }
    public void execute(Database.BatchableContext BC, List<Purchase_Order__c> lstMasterProduct){
       List<Purchase_Order__c> poHasNoItems = new List<Purchase_Order__c>();
       for(Purchase_Order__c objPo : [SELECT Id, (SELECT Id FROM Purchase_Order_Items__r) 
                                      FROM Purchase_Order__c WHERE Id IN :lstMasterProduct]){
            if(objPo.Purchase_Order_Items__r == null || objPo.Purchase_Order_Items__r.size() == 0){
                poHasNoItems.add(objPo);
            }
       }
       Database.delete(poHasNoItems, false);
    }
    public void finish(Database.BatchableContext BC){
       
    }
    public CCM_DeletePurchaseOrderBatch() {

    }
}