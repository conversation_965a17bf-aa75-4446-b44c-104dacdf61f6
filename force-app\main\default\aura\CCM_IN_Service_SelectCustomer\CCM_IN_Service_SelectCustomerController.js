({
    // 初始化
    doInit : function(component, event, helper) {
        // 设置下拉翻译
        component.set('v.isDropShipOpt', [
            {
                'label': $A.get("$Label.c.CCM_Yes"), 
                'value': 'Y'
            },
            {
                'label': $A.get("$Label.c.CCM_No"),
                'value': 'N'
            }
        ]);
        const params = new URLSearchParams((window === globalThis || window === document ? location : window.location).search);
        const recordType =  params.get('0.recordType');
        if(recordType === 'EEG_Influencer_EU') {
            component.set('v.warehouseOptions', [
                {
                    'label': $A.get("$Label.c.CCM_GermanDSV"), 
                    'value': 'Germany (DSV)'
                }
            ]);
            component.set('v.warehouse', 'Germany (DSV)');
        }
        else {
            component.set('v.warehouseOptions', [
                {
                    'label': $A.get("$Label.c.CCM_GermanDSV"), 
                    'value': 'Germany (DSV)'
                },
                {
                    'label': $A.get("$Label.c.CCM_ChinaDI"),
                    'value': 'China (DI)'
                }
            ]);
        }
        component.set('v.dropshipTypeOptions', [
            {
                label: 'Affiliate', 
                value: 'Affiliate',
            },
            {
                label: 'Warehouse', 
                value: 'Warehouse',
            },
            {
                label: 'End Consumer', 
                value: 'End Consumer',
            },
        ]);
        console.log(component.get('v.purchaseOrderId'), component.get('v.actionType'), component.get('v.userType'), '初始化------------customer');
        let actionType = component.get('v.actionType');
        console.log(actionType, 'actionType-----------customer');
        if (actionType === 'edit' || actionType === 'draft') {
            // 获取旧数据
            helper.getPODetailInfo(component);

        //     // let PodetailInfo = JSON.parse(JSON.stringify(component.get('v.PodetailInfo')));
        //     // // 回填参数
        //     // component.set('v.warehouse', PodetailInfo.WareHouse);
        //     // component.set('v.customerPO', PodetailInfo.CustomerPo);
        //     // component.set('v.isDropShip', PodetailInfo.IsDropship);
        //     // component.set('v.pricingDate', PodetailInfo.PricingDate);
        //     // component.set('v.recordTypeName', PodetailInfo.recordTypeName),
        //     // component.set('v.purchaseOrderId', component.get('v.POId')),
        //     // // 获取customer detail
        //     // component.set('v.customerId', PodetailInfo.CustomerId);
        //     // component.set('v.customerName', PodetailInfo.CustomerName);
        //     // component.set('v.customerObj', {
        //     //     Id: PodetailInfo.CustomerId,
        //     //     Name: PodetailInfo.CustomerName,
        //     // });
        //     // helper.getCustomerInfo(component);
        } else {
            const params = new URLSearchParams(window.location.search);
            const recordType =  params.get('0.recordType');
            component.set('v.recordTypeName', recordType);
            component.set('v.isDropShip', 'N');
            component.set('v.warehouse', 'Germany (DSV)');
            // 获取当前时间
            const year = new Date().getFullYear().toString();
            const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
            const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
            component.set('v.pricingDate', `${year}-${month}-${day}`);
        }
        const userType =  component.get('v.userType');
        if (userType === 'InsideSales' || userType === 'SalesRep') {
            component.set('v.editDropShipOrder', false);
        } else {
            component.set('v.editDropShipOrder', true);
        }
    },
    // 下一步
    nextStep: function(component, event, helper){
        // 校验必填项
        let customerValid = false;
        const customerElement = component.find('customer');
        const customerRequiredText = component.find('customer-error-required');
        // customer校验
        const customerObj = JSON.parse(JSON.stringify(component.get('v.customerObj')));
        if (customerObj.Id) {
            customerValid = true;
            $A.util.removeClass(customerElement, 'field-error');
            $A.util.addClass(customerRequiredText, 'slds-hide');
        } else {
            customerValid = false;
            $A.util.addClass(customerElement, 'field-error');
            $A.util.removeClass(customerRequiredText, 'slds-hide');
        };
        // contact校验
        let contactValid = true;
        // const contactElement = component.find('contact');
        // const contactRequiredText = component.find('contact-error-required');
        // const contactObj = JSON.parse(JSON.stringify(component.get('v.contactObj')));
        // if (contactObj.Id) {
        //     contactValid = true;
        //     $A.util.removeClass(contactElement, 'field-error');
        //     $A.util.addClass(contactRequiredText, 'slds-hide');
        // } else {
        //     contactValid = false;
        //     $A.util.addClass(contactElement, 'field-error');
        //     $A.util.removeClass(contactRequiredText, 'slds-hide');
        // };
        // 判断是否存在drop address
        let isCollectiveCustomer = component.get('v.isCollectiveCustomer');
        let isDropShip = component.get('v.isDropShip');
        if (isCollectiveCustomer && isDropShip == 'Y') {
            // 判断type是否为End Consumer
            let valid = true;
            let dropshipAddressValid = false;
            let dropShipCountryValid = false;

            if (component.get('v.dropshipType') == 'End Consumer') {
                valid = helper.getValidation(component, 'detail');
                // 校验country
                let DropShipCountry = JSON.parse(JSON.stringify(component.get('v.DropShipCountry')));
                console.log(DropShipCountry, '校验country------------test');
                const dropShipCountryElement = component.find('dropShipCountry');
                const dropShipCountryRequiredText = component.find('dropShipCountry-error-required');
                if (DropShipCountry.Id) {
                    dropShipCountryValid = true;
                    $A.util.removeClass(dropShipCountryElement, 'field-error');
                    $A.util.addClass(dropShipCountryRequiredText, 'slds-hide');
                } else {
                    dropShipCountryValid = false;
                    $A.util.addClass(dropShipCountryElement, 'field-error');
                    $A.util.removeClass(dropShipCountryRequiredText, 'slds-hide');
                };
                // 下一步
                if (customerValid && contactValid && valid && dropShipCountryValid) {
                    console.log('下一步校验通过-----------');
                    helper.createPo(component);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": $A.get("$Label.c.CCM_FillRequiredFields"),
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
            } else {
                valid = helper.getValidation(component, '');
                // 校验dropshipAddress
                let dropshipAddress = JSON.parse(JSON.stringify(component.get('v.dropshipAddress')));
                const dropshipAddressElement = component.find('dropshipAddress');
                const dropshipAddressRequiredText = component.find('dropshipAddress-error-required');
                if (dropshipAddress.Id) {
                    dropshipAddressValid = true;
                    $A.util.removeClass(dropshipAddressElement, 'field-error');
                    $A.util.addClass(dropshipAddressRequiredText, 'slds-hide');
                } else {
                    dropshipAddressValid = false;
                    $A.util.addClass(dropshipAddressElement, 'field-error');
                    $A.util.removeClass(dropshipAddressRequiredText, 'slds-hide');
                };
                console.log(valid, 'valid------------');
                // 下一步
                if (customerValid && contactValid && valid && dropshipAddressValid) {
                    console.log('下一步校验通过-----------');
                    helper.createPo(component);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": $A.get("$Label.c.CCM_FillRequiredFields"),
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
            }
        } else {
            if (customerValid && contactValid) {
                helper.createPo(component);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Warning",
                    "message": $A.get("$Label.c.CCM_FillRequiredFields"),
                    "type": "warning",
                    "duration": "dismissible"
                }).fire();
            }
        }
    },
    // 取消
    cancel: function(component){
        let url = window.location.origin + '/lightning/n/Purchase_Order_List';
        window.open(url, '_self');
    },
    // customer 选择
    selectCustomer: function(component, event, helper){
        let customerObj = JSON.parse(JSON.stringify(component.get('v.customerObj')));
        let customerId = component.get('v.customerId');
        console.log(customerObj.Id, 'customerId========');
        // 置空contact & drop info
        if (customerId != customerObj.Id) {
            component.set('v.contactObj', {Id: '', Name: ''});
            component.set('v.contactId', '');
            component.set('v.showDropAddress', false);
            component.set('v.isCollectiveCustomer', false);
            component.set('v.dropshipType', '');
            component.set('v.dropshipAddress', {Id: null, Name: null});
            component.set('v.DropShipName', '');
            component.set('v.DropShipAddress1', '');
            component.set('v.DropShipAddress2', '');
            component.set('v.DropShipPhone', '');
            component.set('v.DropShipCountry', {Id: null, Name: null});
            component.set('v.DropShipCity', '');
            component.set('v.DropShipZip', '');
            component.set('v.DropShipState', '');
            component.set('v.defaultDropAddressInfo', {Id: '', Name: ''});
            console.log(JSON.stringify(component.get('v.defaultDropAddressInfo')), JSON.stringify(component.get('v.contactObj')), 'defaultDropAddressInfo-----------------------111');
        }
        const customerElement = component.find('customer');
        const customerRequiredText = component.find('customer-error-required');
        if (customerObj.Id) {
            component.set('v.customerId', customerObj.Id);
            component.set('v.customerName', customerObj.Name);
            $A.util.removeClass(customerElement, 'field-error');
            $A.util.addClass(customerRequiredText, 'slds-hide');
            helper.getCustomerInfo(component);
            // 判断是否Collective Customer
            helper.checkCollectiveCustomer(component, customerObj.Id);
        } else {
            component.set('v.customerId', null);
            component.set('v.customerName', null);
            component.set('v.customerInfo', {});
            $A.util.addClass(customerElement, 'field-error');
            $A.util.removeClass(customerRequiredText, 'slds-hide');
            // 判断是否Collective Customer
            component.set('v.isCollectiveCustomer', false);
        }
    },
    // select Contact
    selectContact: function(component, event, helper){
        let customerObj = component.get('v.customerObj');
        let contactObj = component.get('v.contactObj');
        let contactId = component.get('v.contactId');
        // 置空contact & drop info
        if (contactId !== contactObj.Id) {
            component.set('v.dropshipType', '');
            component.set('v.dropshipAddress', {Id: null, Name: null});
            component.set('v.DropShipName', '');
            component.set('v.DropShipAddress1', '');
            component.set('v.DropShipAddress2', '');
            component.set('v.DropShipPhone', '');
            component.set('v.DropShipCountry', {Id: null, Name: null});
            component.set('v.DropShipCity', '');
            component.set('v.DropShipZip', '');
            component.set('v.DropShipState', '');
            component.set('v.defaultDropAddressInfo', {Id: '', Name: ''})
        }
        const contactElement = component.find('contact');
        const contactRequiredText = component.find('contact-error-required');
        // 先判断是否选择customer
        if (!customerObj.Id) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'Please select Customer first!',
                "type": "warning",
                "duration": "dismissible"
            }).fire();
        } else {
            // 判断是否选择contact
            // if (contactObj.Id) {
            //     component.set('v.contactId', contactObj.Id);
            //     $A.util.removeClass(contactElement, 'field-error');
            //     $A.util.addClass(contactRequiredText, 'slds-hide');
            // } else {
            //     component.set('v.contactId', null);
            //     $A.util.addClass(contactElement, 'field-error');
            //     $A.util.removeClass(contactRequiredText, 'slds-hide'); 
            // }
        }
    },
    // customerPO 重复校验
    checkCustomerPo: function(component, event, helper){
        console.log('重复校验--------');
        helper.checkPO(component);
    },
    // 判断是否显示dropAddress
    validCollectiveCustomer: function(component, event, helper){
        let isCollectiveCustomer = component.get('v.isCollectiveCustomer');
        let isDropShip = component.get('v.isDropShip');
        let dropshipAddress = component.get('v.dropshipAddress');
        let dropshipTypeValue = component.get('v.dropshipType');
        console.log(isCollectiveCustomer, isDropShip, '判断是否显示dropAddress--------');
        if (isCollectiveCustomer && isDropShip == 'Y') {
            console.log('显示dropaddress===============');
            component.set('v.showDropAddress', true);
            component.set('v.isDropAddressInFirst', true);
            // 判断是否自动带入address
            if (!dropshipAddress.Id && dropshipTypeValue != 'End Consumer') {
                component.set('v.dropshipAddress', component.get('v.defaultDropAddressInfo'))
            }

        } else {
            component.set('v.dropshipType', '');
            component.set('v.dropshipAddress', {Id: null, Name: null});
            component.set('v.showDropAddress', false);
            component.set('v.isDropAddressInFirst', false);
            component.set('v.DropShipName', '');
            component.set('v.DropShipAddress1', '');
            component.set('v.DropShipAddress2', '');
            component.set('v.DropShipPhone', '');
            component.set('v.DropShipCountry', {Id: null, Name: null});
            component.set('v.DropShipCity', '');
            component.set('v.DropShipZip', '');
            component.set('v.DropShipState', '');
        }
    },
    changeDropshipAddress: function(component, event, helper){
        let customerObj = component.get('v.customerObj');
        let contactObj = component.get('v.contactObj');
        if (!customerObj.Id || !contactObj.Id) {
            component.set('v.dropshipAddress', {Id: null, Name: null});
            component.set('v.dropshipAddressId', '');
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'Please enter the customer and contact information first!',
                "type": "warning",
                "duration": "dismissible"
            }).fire();
        } else {
            if (component.get('v.dropshipType') != 'End Consumer' && component.get('v.isDropShip') == 'Y') {
                let dropshipAddress = JSON.parse(JSON.stringify(component.get('v.dropshipAddress')));
                console.log(JSON.stringify(dropshipAddress), 'dropshipAddress========');
                const dropshipAddressElement = component.find('dropshipAddress');
                const dropshipAddressRequiredText = component.find('dropshipAddress-error-required');
                if (dropshipAddress.Id) {
                    component.set('v.dropshipAddressId', dropshipAddress.Id);
                    $A.util.removeClass(dropshipAddressElement, 'field-error');
                    $A.util.addClass(dropshipAddressRequiredText, 'slds-hide');
                } else {
                    component.set('v.dropshipAddressId', '');
                    $A.util.addClass(dropshipAddressElement, 'field-error');
                    $A.util.removeClass(dropshipAddressRequiredText, 'slds-hide');
                }
            }
        }
    },
    // 监听DropshipType
    changeDropshipType: function(component, event, helper){
        let dropshipTypeValue = component.get('v.dropshipType');
        let dropshipAddress = component.get('v.dropshipAddress');
        console.log(dropshipTypeValue, dropshipAddress,'dropshipTypeValue------------test');
        if (dropshipTypeValue == 'End Consumer') {
            component.set('v.dropshipAddressFlag', true);
            component.set('v.showDetailfield', true);
            component.set('v.dropshipAddress', {Id: '', Name: ''});
            component.set('v.dropshipAddressId', '');
        } else if (dropshipTypeValue && dropshipTypeValue != 'End Consumer') {
            component.set('v.dropshipAddressFlag', false);
            component.set('v.showDetailfield', false);
            if (!dropshipAddress.Id) {
                let defaultDropAddressInfo = component.get('v.defaultDropAddressInfo')
                component.set('v.dropshipAddress', defaultDropAddressInfo);
                component.set('v.dropshipAddressId', defaultDropAddressInfo.Id);
            } else {
                component.set('v.dropshipAddress', dropshipAddress);
                component.set('v.dropshipAddressId', dropshipAddress.Id);
            }
            // 清空详细信息
            component.set('v.DropShipName', '');
            component.set('v.DropShipAddress1', '');
            component.set('v.DropShipAddress2', '');
            component.set('v.DropShipPhone', '');
            component.set('v.DropShipCountry', {Id: null, Name: null});
            component.set('v.DropShipCity', '');
            component.set('v.DropShipZip', '');
            component.set('v.DropShipState', '');
        }
    },
    changeDropShipCountry: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            let DropShipCountry = JSON.parse(JSON.stringify(component.get('v.DropShipCountry')));
            console.log(DropShipCountry, 'DropShipCountry========');
            const dropShipCountryElement = component.find('dropShipCountry');
            const dropShipCountryRequiredText = component.find('dropShipCountry-error-required');
            if (DropShipCountry.Id) {
                $A.util.removeClass(dropShipCountryElement, 'field-error');
                $A.util.addClass(dropShipCountryRequiredText, 'slds-hide');
            } else {
                $A.util.addClass(dropShipCountryElement, 'field-error');
                $A.util.removeClass(dropShipCountryRequiredText, 'slds-hide');
            }
        }
    },
    changeDropShipName: function(component, event, helper){
        console.log('blur------------');
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropShipName');
        }
    },
    changeDropshipStreetAndStreetNo: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropshipStreetAndStreetNo');
        }
    },
    changeDropshipStreetAndStreetNo: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropshipStreetAndStreetNo');
        }
    },
    changeDropShipZip: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropShipZip');
        }
    },
    changeDropShipCity: function(component, event, helper){
        if (component.get('v.dropshipType') == 'End Consumer' && component.get('v.isDropShip') == 'Y') {
            // 校验必填
            helper.getElementRequiredError(component, 'dropShipCity');
        }
    },
    // 当customer & contact有值时获取drop address list
    checkAutoDropAddress: function(component, event, helper){
        // 监听变更这个会先触发
        setTimeout(()=>{
            let customerObj = component.get('v.customerObj');
            let contactObj = component.get('v.contactObj');
            if (customerObj.Id && contactObj.Id) {
                console.log(customerObj.Id, contactObj.Id,  '当customer & contact有值时获取drop address list=======================');
                helper.getDropAddressList(component);
            }
        }, 10)
    },
})