/**
 * Author: Honey
 * Description: 创建purchase Order的时候查询Customer、产品、价格等数据
 *
 */
public without sharing class CCM_RequestPurchaseOrderController{
    public Static Map<String, Map<String, List<Product2>>> mapFeil2Products = new Map<String, Map<String, List<Product2>>>();
    //delete
    @AuraEnabled
    public static string QueryCustomer(String CustomerId){
        system.debug('CustomerId-->' + CustomerId);
        Account objAccount = [Select Id, Name, Customer_Name__c, Company__c, OwnerId, CurrencyIsoCode, Owner.Name, AccountNumber, Distributor_or_Dealer__c, Tier__c, Sales_Channel__c, Classification1__c, Classification2__c, Association_Type__c, Discount_Type__c, Customer_Business_Type__c, Intended_Brand__c, Association_Group__r.Association_Type__c, Association_Membership_No__c, Association_Group__r.Name
                              from Account
                              where Id = :CustomerId
                              LIMIT 1];
        system.debug('queryList-->' + objAccount);
        return JSON.serialize(objAccount);
    }
        @AuraEnabled
    public static String QueryAuthBrand(String CustomerId){
        system.debug('CustomerId--->' + CustomerId);
        List<Sales_Program__c> lstSalesProgram = QueryAuthBrandInfo(CustomerId);

        system.debug('objSalesProgram-->' + lstSalesProgram);
        if (lstSalesProgram != null && lstSalesProgram.size() > 0){
            system.debug('lstSalesProgram[0]--->' + lstSalesProgram[0].Id);
            return JSON.serialize(lstSalesProgram[0]);
        } else{
            return null;
        }
    }
    @AuraEnabled
    public static String createPO(String selectCustomer, String warehouse, Boolean isDropship, Date pricingDate, String CustomerPo, String RecordTypeName, String PurchaseOrderId, String status, String contactId){
        try{
            Purchase_Order__c po = new Purchase_Order__c();
            if (String.isNotBlank(PurchaseOrderId)){
                po.Id = PurchaseOrderId;
            } else{
                po.Current_Step__c = CCM_Constants.SELECT_CUSTOMER;
            }
            system.debug('selectCustomer-->' + selectCustomer);
            Account objAccount = [SELECT OwnerId, Id, CurrencyIsoCode, Country__c, Country_EU__c
                                  FROM Account
                                  WHERE Id = :selectCustomer];
            if (String.isNotBlank(status)){
                po.Status__c = status;
            } else{
                po.Status__c = CCM_Constants.DRAFT_STOCK;
            }
            po.Salesperson__c = objAccount.OwnerId;
            //通过CustomerId查询Customer的Currency字段

            po.CurrencyIsoCode = objAccount.CurrencyIsoCode;
            po.Customer__c = selectCustomer;
            po.Warehouse__c = warehouse;
            po.Is_DropShip__c = isDropship;
            po.Pricing_Date__c = pricingDate;

            po.Customer_PO_Num__c = CustomerPo;

            system.debug('RecordTypeName-->' + RecordTypeName);
            if (RecordTypeName == 'Regular Order'){
                RecordTypeName = 'Regular_Order';
            } else if (RecordTypeName == 'Pre_season_Order'){
                RecordTypeName = 'Pre_season_Order';
            }
            po.RecordTypeId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(RecordTypeName).getRecordTypeId();
            //根据record Type和 WareHouse确定Order Type
            if(CCM_Constants.REGULAR_ORDER.equals(RecordTypeName)){
                //regular类型
                //WareHouse为Germany (DSV)
                if(CCM_Constants.GERMANY.equals(warehouse)){
                    po.Order_Type__c = CCM_Constants.DSVR;
                }
                if(CCM_Constants.CHINA.equals(warehouse)){
                    po.Order_Type__c = CCM_Constants.DIR;
                }
            }
            if(CCM_Constants.PRE_SEASON_ORDER_DEV_NAME.equals(RecordTypeName)){
                //Pre_season_Order
                //WareHouse为Germany (DSV)
                if(CCM_Constants.GERMANY.equals(warehouse)){
                    po.Order_Type__c = CCM_Constants.DSVP;
                }
                if(CCM_Constants.CHINA.equals(warehouse)){
                    po.Order_Type__c = CCM_Constants.DIP;
                }
            }
            if(CCM_Constants.EEG_Influencer_EU_ORDER.equals(RecordTypeName)){
                if(CCM_Constants.GERMANY.equals(warehouse)){
                    po.Order_Type__c = CCM_Constants.DSVR;
                }
                if(CCM_Constants.CHINA.equals(warehouse)){
                    po.Order_Type__c = CCM_Constants.DIR;
                }
            }
        upsert po;
            return po.Id;
            
        }catch(Exception e){
            system.debug('报错信息--->'+e.getMessage()+'报错行数--->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
                
    }

    @AuraEnabled
    public static String GetAccessProductInfo(String ProductModel, String CustomerId, String RecordTypeName, String WareHouse, Date PricingDate, String PurchaseOrderId){
        //根据传过来的Model号获取相关联的Model信息
        // List<Recommended_Accessories__c> lstRecommended = [SELECT Id, Access_Model__c, Masert_Model__c
        //                                                    FROM Recommended_Accessories__c
        //                                                    where Masert_Model__c = :ProductModel];
        List<kit_item__c> lstRecommended = new List<kit_item__c >();

        lstRecommended = [SELECT id,Kit_Acc__c,Accessories__c ,Product_Acc__c,recordType.developerName,Accessories__r.Order_Model__c,Kit_Acc__r.Order_Model__c,Product_Acc__r.Order_Model__c
                          FROM kit_item__c 
                          WHERE recordType.developerName in ('Kits_and_Accessories','Products_and_Accessories') AND Accessories__c != null
                                AND (Kit_Acc__r.Order_Model__c = :ProductModel or  Product_Acc__r.Order_Model__c = :ProductModel)];

        if (lstRecommended == null || lstRecommended.size() == 0){
            return null;
        }
        //遍历Accessary
        Set<String> setProductModel = new Set<String>();
        for (kit_item__c objRecommended : lstRecommended){
            setProductModel.add(objRecommended.Accessories__r.Order_Model__c);
        }
        if (setProductModel.size() == 0){
            return null;
        }else{
            //对比可售清单，不在可售清单的产品过滤掉 -Vince add ********
            List<MasterProductPrice__c> salesPriceList = getSalesPriceListByCustomerAndDate(setProductModel,CustomerId,Date.valueOf(PricingDate));
            if(salesPriceList != null && salesPriceList.size() > 0){
                for(MasterProductPrice__c salesPrice : salesPriceList){
                    if(!setProductModel.contains(salesPrice.Product__r.Order_Model__c)){
                        //移除不满足可售清单的产品
                        setProductModel.remove(salesPrice.Product__r.Order_Model__c);
                    }
                }
            }else {
                return null;
            }
            
        }
        system.debug('setProductModel-->' + setProductModel);
        //根据AccessModel去Product种查询关联的产品信息
        List<Product2> lstProduct = [SELECT Id, Item_Description_DE__c, RecordType_Name__c, Item_Description_EN__c, Order_Model__c, Units_Per_Inner_BOX_EA__c, Units_Per_Pallet_EA__c, Units_Per_Master_Carton_EA__c, RecordTypeId, RecordType.Name
                                     FROM Product2
                                     WHERE Order_Model__c IN:setProductModel];

        if(lstProduct == null || lstProduct.size() == 0){
            return null;
        }                              

        // for 调用 GetProductInfoUtil 方法前先进行查询操作
        // 拿到 objAccountQuery
        Account objAccountQuery = [SELECT Sales_Channel__c, CurrencyIsoCode 
				                    from Account
                               	    where Id = :CustomerId];

        List<Id> lstProductId = new List<Id>();
        for(Product2 product : lstProduct){
            lstProductId.add(product.Id);
        }

        // 拿到 ProductId 和 List<Kit_Item__c> 映射关系 matchingKitItemsQueryMap
        // todo 优化代码能否直接进行分组
        Map<Id, List<Kit_Item__c>> matchingKitItemsQueryMap = new Map<Id, List<Kit_Item__c>>();
        List<Kit_Item__c> matchingKitItemsQueryList = [SELECT Product__c, Kit__c, Quantity__c, VK_Product__c
                                        	            FROM Kit_Item__c
                                            	        WHERE Kit__c in :lstProductId  AND VK_Product__c != null];

        // 根据 matchingKitItems.Kit__c(ProductId) 进行分组
        for(Kit_Item__c matchingKitItems : matchingKitItemsQueryList){
            if(!matchingKitItemsQueryMap.containsKey(matchingKitItems.Kit__c)){
                matchingKitItemsQueryMap.put(matchingKitItems.Kit__c, new List<Kit_Item__c>{matchingKitItems});
            }else{
                matchingKitItemsQueryMap.get(matchingKitItems.Kit__c).add(matchingKitItems);
            }
        }

        // 拿到 ProductId 和 Set<VK_Product__c> 映射关系 setVKProductMap
        Map<Id, Set<String>> setVKProductMap = new Map<Id, Set<String>>();
        Set<String> VKProductSet= new Set<String>();
        for(Id key : matchingKitItemsQueryMap.keySet()){
            List<Kit_Item__c> matchingKitItemsQuery = matchingKitItemsQueryMap.get(key);
            Set<String> setVKProduct = new Set<String>();
            for(Kit_Item__c matchingKitItems : matchingKitItemsQuery){
                setVKProduct.add(matchingKitItems.VK_Product__c);
                VKProductSet.add(matchingKitItems.VK_Product__c);
            }
            
            setVKProductMap.put(key, setVKProduct);
        }

        // 拿到 ProductId 和 List<Product2> 映射关系 filteredProductsQueryMap, Product2 是 matchingKitItemsQueryMap 相关联和 VK_Product__c
        Map<Id, List<Product2>> filteredProductsQueryMap = new Map<Id, List<Product2>>();
        Map<Id, Product2> VKProductMap = new Map<Id, Product2>([
            SELECT Id, Item_Description_DE__c, InvotoryInfo__c, Item_Description_EN__c, Full_Pallet_Quantity__c, Order_Model__c, Units_Per_Master_Carton_EA__c
            FROM Product2
            WHERE Id IN:VKProductSet
        ]);
        
        for(Id key: setVKProductMap.keySet()){
            Set<String> setVKProduct = setVKProductMap.get(key);
            for(String VKProductId : setVKProduct){
                if(!filteredProductsQueryMap.containsKey(key)){
                    filteredProductsQueryMap.put(key, new List<Product2>{VKProductMap.get(VKProductId)});
                }else{
                    filteredProductsQueryMap.get(key).add(VKProductMap.get(VKProductId));
                }
            }
        }
        
        // 查询 productTmpQueryMap
        Map<Id, Product2> productTmpQueryMap = new Map<Id, Product2>([SELECT Id, Pallet_Item__c, MPQ__c
                                   FROM Product2
                                   WHERE Id in :lstProductId]);
        
        
        system.debug('lstProduct-->' + lstProduct);
        List<DetailProductInfoAndAll> lstDetailINfo = new List<DetailProductInfoAndAll>();
        String userLanguage = UserInfo.getLanguage();
        for (Product2 objProject : lstProduct){
            DetailProductInfoAndAll objInfo = GetProductInfoUtil(String.valueOf(objProject.Id), objProject.Units_Per_Pallet_EA__c, CustomerId, objProject.Units_Per_Master_Carton_EA__c, objProject.Units_Per_Inner_BOX_EA__c, objProject.RecordType.Name, WareHouse, PricingDate, PurchaseOrderId
                                                                , objAccountQuery, matchingKitItemsQueryMap.get(objProject.Id), filteredProductsQueryMap.get(objProject.Id), productTmpQueryMap.get(objProject.Id));
            
            if(objInfo.applicationMethod == null){
                continue;
            }
            if(CCM_Constants.EN == userLanguage){
                objInfo.ProductDescription = objProject.Item_Description_EN__c;
            }else if(CCM_Constants.DE == userLanguage){
                objInfo.ProductDescription = objProject.Item_Description_DE__c;
            }
            objInfo.Model = objProject.Order_Model__c;
            objInfo.productId = objProject.Id;
            objInfo.RecordTypeName = objProject.RecordType_Name__c;

            lstDetailINfo.add(objInfo);
        }
        system.debug('lstDetailINfo-->' + lstDetailINfo);
        return JSON.serialize(lstDetailINfo);
    }

    @AuraEnabled
    public static String getRelatedAndSubstituteProducts(String ProductModel, String CustomerId, String WareHouse, String PricingDate, String PurchaseOrderId) {
        Map<String, String> productsMap = new Map<String, String>();
        String relatedProductInfo = GetRelatedProductInfo(ProductModel, CustomerId, WareHouse, PricingDate, PurchaseOrderId);
        if(String.isNotBlank(relatedProductInfo)) {
            productsMap.put('relatedProducts', relatedProductInfo);
        }
        String substituteProductInfo = GetSubstituteProductInfo(ProductModel, CustomerId, WareHouse, PricingDate, PurchaseOrderId);
        if(String.isNotBlank(substituteProductInfo)) {
            productsMap.put('substituteProducts', substituteProductInfo);
        }
        return JSON.serialize(productsMap);
    }

    //aria 新增:查询替代品
    @AuraEnabled
    public static String GetSubstituteProductInfo(String ProductModel, String CustomerId, String WareHouse, String PricingDate, String PurchaseOrderId){
        //根据传过来的Model号获取相关联的Model信息
        List<Kit_Item__c> lstRecommended = [SELECT Id, Product_Substituted__c, Substituted__c, Product_Substituted__r.order_Model__c, Substituted__r.order_Model__c
                                            FROM Kit_Item__c
                                            where recordType.developerName = 'Product_And_Substituted' and Product_Substituted__r.order_Model__c = :ProductModel];

        if (lstRecommended == null || lstRecommended.size() == 0){
            return null;
        }
        //遍历kit item
        Set<String> setProductModel = new Set<String>();
        for (Kit_Item__c objRecommended : lstRecommended){
            setProductModel.add(objRecommended.Substituted__r.order_Model__c);
        }

        Date priceDate = Date.today();
        if(String.isNotBlank(PricingDate)) {
            priceDate = Date.valueOf(PricingDate);
        }
        if (setProductModel.size() == 0){
            return null;
        }else{
            //对比可售清单，不在可售清单的产品过滤掉 -Vince add ********
            List<MasterProductPrice__c> salesPriceList = getSalesPriceListByCustomerAndDate(setProductModel,CustomerId,priceDate);
            if(salesPriceList != null && salesPriceList.size() > 0){
                for(MasterProductPrice__c salesPrice : salesPriceList){
                    if(!setProductModel.contains(salesPrice.Product__r.Order_Model__c)){
                        //移除不满足可售清单的产品
                        setProductModel.remove(salesPrice.Product__r.Order_Model__c);
                    }
                }
            }else {
                return null;
            }
            
        }
        system.debug('setProductModel-->' + setProductModel);
        //根据AccessModel去Product种查询关联的产品信息
        List<Product2> lstProduct = [SELECT Id, Item_Description_DE__c, Item_Description_EN__c, Order_Model__c, Units_Per_Inner_BOX_EA__c, Units_Per_Pallet_EA__c, Units_Per_Master_Carton_EA__c, RecordTypeId, RecordType.Name
                                     FROM Product2
                                     WHERE Order_Model__c IN:setProductModel];
        system.debug('lstProduct-->' + lstProduct);
        List<DetailProductInfoAndAll> lstDetailINfo = new List<DetailProductInfoAndAll>();
        for (Product2 objProject : lstProduct){
            DetailProductInfoAndAll objInfo = GetProductInfoUtil(String.valueOf(objProject.Id), objProject.Units_Per_Pallet_EA__c, CustomerId, objProject.Units_Per_Master_Carton_EA__c, objProject.Units_Per_Inner_BOX_EA__c, objProject.RecordType.Name, WareHouse, priceDate, PurchaseOrderId);
            objInfo.Model = objProject.Order_Model__c;
            objInfo.ProductDescription = objProject.Item_Description_DE__c;
            objInfo.productId = objProject.Id;
            lstDetailINfo.add(objInfo);
        }
        if(!lstDetailINfo.isEmpty()) {
            return JSON.serialize(lstDetailINfo);
        }
        return '';
    }
    //aria 新增:查询related
    @AuraEnabled
    public static String GetRelatedProductInfo(String ProductModel, String CustomerId, String WareHouse, String PricingDate, String PurchaseOrderId){
        //根据传过来的Model号获取相关联的Model信息
        List<Kit_Item__c> lstRecommended = [SELECT Id, Product_Related__c, Related__c, Product_Related__r.order_Model__c, Related__r.order_Model__c
                                            FROM Kit_Item__c
                                            where recordType.developerName = 'Product_And_Related' and (Product_Related__r.order_Model__c = :ProductModel OR Related__c = :ProductModel)];

        if (lstRecommended == null || lstRecommended.size() == 0){
            return null;
        }
        //遍历kit item
        Date priceDate = Date.today();
        if(String.isNotBlank(PricingDate)) {
            priceDate = Date.valueOf(PricingDate);
        }
        Set<String> setProductModel = new Set<String>();
        for (Kit_Item__c objRecommended : lstRecommended){
            if (objRecommended.Product_Related__r.order_Model__c == ProductModel){
                setProductModel.add(objRecommended.Related__r.order_Model__c);
            } else if (objRecommended.Related__r.order_Model__c == ProductModel){
                setProductModel.add(objRecommended.Product_Related__r.order_Model__c);
            }
        }
        if (setProductModel.size() == 0){
            return null;
        }else{
            //对比可售清单，不在可售清单的产品过滤掉 -Vince add ********
            List<MasterProductPrice__c> salesPriceList = getSalesPriceListByCustomerAndDate(setProductModel,CustomerId,priceDate);
            if(salesPriceList != null && salesPriceList.size() > 0){
                for(MasterProductPrice__c salesPrice : salesPriceList){
                    if(!setProductModel.contains(salesPrice.Product__r.Order_Model__c)){
                        //移除不满足可售清单的产品
                        setProductModel.remove(salesPrice.Product__r.Order_Model__c);
                    }
                }
            }else {
                return null;
            }
            
        }
        system.debug('setProductModel-->' + setProductModel);
        //根据AccessModel去Product种查询关联的产品信息
        List<Product2> lstProduct = [SELECT Id, Item_Description_DE__c, Item_Description_EN__c, Order_Model__c, Units_Per_Inner_BOX_EA__c, Units_Per_Pallet_EA__c, Units_Per_Master_Carton_EA__c, RecordTypeId, RecordType.Name
                                     FROM Product2
                                     WHERE Order_Model__c IN:setProductModel];
        system.debug('lstProduct-->' + lstProduct);
        List<DetailProductInfoAndAll> lstDetailINfo = new List<DetailProductInfoAndAll>();
        for (Product2 objProject : lstProduct){
            DetailProductInfoAndAll objInfo = GetProductInfoUtil(String.valueOf(objProject.Id), objProject.Units_Per_Pallet_EA__c, CustomerId, objProject.Units_Per_Master_Carton_EA__c, objProject.Units_Per_Inner_BOX_EA__c, objProject.RecordType.Name, WareHouse, priceDate, PurchaseOrderId);
            objInfo.Model = objProject.Order_Model__c;
            objInfo.ProductDescription = objProject.Item_Description_DE__c;
            objInfo.productId = objProject.Id;
            lstDetailINfo.add(objInfo);
        }
        if(!lstDetailINfo.isEmpty()) {
            return JSON.serialize(lstDetailINfo);
        }
        return '';
    }

    /**
     * vince ******** 根据产品model、客户id、下单日期查询可售清单
     */

    public static List<MasterProductPrice__c> getSalesPriceListByCustomerAndDate(Set<String> setProductModel,String CustomerId, Date PricingDate){
        List<MasterProductPrice__c> lstMasterProduct = new List<MasterProductPrice__c>();
        lstMasterProduct = [SELECT Id, Account__c, End_Date__c, Start_Date__c, Product__c, Has_AllItem__c, CurrencyIsoCode,Product__r.Product_Status__c,Product__r.RecordType_Name__c,Product__r.Order_Model__c
        FROM MasterProductPrice__c
        WHERE Account__c = :CustomerId AND End_Date__c >= :PricingDate AND Start_Date__c <= :PricingDate AND Modifier_Entry__C != NULL AND Is_Delete__c != TRUE AND Product__r.Order_model__c in :setProductModel
        AND (Product__r.Product_Status__c != 'INACT' AND  Product__r.Product_Status__c != 'RTO')
        ORDER By min_Final_Prority__c ASC];
        return lstMasterProduct;
    }

    @AuraEnabled
    public static string queryAlertMessage(String CustomerNumber, String AlertMode){
        system.debug('CustomerNumber--->' + CustomerNumber);
        system.debug('AlertMode--->' + AlertMode);
        Date searchDate = Date.today();

        try{
            //通过Customer查询AlertMessage
            List<Alert_Message__c> lstAlert = new List<Alert_Message__c>();
            lstAlert = [SELECT Id, Alert_Message__c, Start_Date__c, End_Date__c, Customer_Account__c, Alert_Mode__c
                        FROM Alert_Message__c
                        WHERE Start_Date__c < :searchDate AND (End_Date__c = NULL OR End_Date__c > :searchDate) AND Customer_Account__c = :CustomerNumber AND Alert_Mode__c = :AlertMode];
            if (lstAlert.size() > 0){
                return lstAlert[0].Alert_Message__c;
            } else{
                return null;
            }

        } catch (Exception e){
            system.debug('报错行数--->' + e.getLineNumber() + '报错信息---->' + e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static String GetProductInfo(String FilterString, String CustomerId, Date PricingDate){
        system.debug('CustomerId-->' + CustomerId);
        system.debug('FilterString-->' + FilterString);
        system.debug('PricingDate-->' + PricingDate);
        List<Product2> filteredProducts = new List<Product2>();
        Account objAccount = [SELECT OwnerId, Id, CurrencyIsoCode
                              FROM Account
                              WHERE Id = :CustomerId];
        //根据用户Id查询中间表-->校验是否包含All Item
        // add haibo (获取product record type -- Product__r.RecordType_Name__c)
        List<MasterProductPrice__c> lstMasterProduct = [SELECT Product__c, Has_AllItem__c
                                                        FROM MasterProductPrice__c
                                                        WHERE Account__c = :CustomerId AND End_Date__c >= :PricingDate AND Start_Date__c <= :PricingDate AND Modifier_Entry__C != NULL AND CurrencyIsoCode = :objAccount.CurrencyIsoCode AND Is_Delete__c != TRUE
                                                        AND (Product__r.Product_Status__c != 'INACT' AND  Product__r.Product_Status__c != 'RTO')
                                                        ORDER By min_Final_Prority__c ASC];
        //只要有一个包含ALL Item就直接取所有的Product-->获取该Customer下所有的Product
        Boolean hasAllItem = false;
        List<String> lstSearchProductIds = new List<String>();
        List<String> lstmasterProductIds = new List<String>();
        for (MasterProductPrice__c objMasterProduct : lstMasterProduct){
            if (objMasterProduct.Has_AllItem__c){
                //有一个为AllItem则为AllItem
                hasAllItem = true;
            }
            lstmasterProductIds.add(objMasterProduct.Product__c);
        }
        if (hasAllItem){
            //标识全部的PriceBook绑定的Customer信息
            List<String> lstProductIds = CCM_GetProductInfoUtil.getPriceBookEntry(CustomerId, PricingDate, '', objAccount.CurrencyIsoCode);
            lstSearchProductIds.addAll(lstProductIds);
        } else{
            //只有满足Modifire的才在里面
            lstSearchProductIds.addAll(lstmasterProductIds);
        }
        //默认为英文
        String userLanguage = UserInfo.getLanguage();
        System.debug('userLanguage---->' + userLanguage);
        //判断当前

        String QueryStr = 'SELECT Id,Order_Model__c,Name,Units_Per_Inner_BOX_EA__c,Item_Description_DE__c,Item_Description_EN__c,Units_Per_Pallet_EA__c,Product_Status__c, ';
        QueryStr += ' Units_Per_Master_Carton_EA__c ,RecordTypeId, RecordType.Name,Master_Product__c,RecordType.DeveloperName ';
        QueryStr += ' FROM Product2 WHERE Id IN :lstSearchProductIds AND (Product_Status__c != \'INACT\' AND  Product_Status__c != \'RTO\')';

        system.debug('QueryStr-->' + QueryStr);
        //根据ProductId查询产品信息
        List<Product2> lstProduct = Database.query(QueryStr);
        List<Product2> lstFinalProduct = new List<Product2>();
        for (Product2 objProduct : lstProduct){
            objProduct.Units_Per_Inner_BOX_EA__c = objProduct.Units_Per_Inner_BOX_EA__c == null ? 0 : objProduct.Units_Per_Inner_BOX_EA__c;
            objProduct.Units_Per_Pallet_EA__c = objProduct.Units_Per_Pallet_EA__c == null ? 0 : objProduct.Units_Per_Pallet_EA__c;
            objProduct.Units_Per_Master_Carton_EA__c = objProduct.Units_Per_Master_Carton_EA__c == null ? 0 : objProduct.Units_Per_Master_Carton_EA__c;
            objProduct.RecordType.Name = objProduct.RecordType.Name == null ? '' : objProduct.RecordType.Name;
            // system.debug('CCM_Constants.EN--->'+CCM_Constants.EN);
            // system.debug('FilterString--->'+FilterString);
            if (CCM_Constants.EN == userLanguage){
                //英文环境--->英语筛选
                // system.debug('英文描述----->'+objProduct.Item_Description_EN__c);
                // system.debug('英文描述----->'+FilterString);
                if (objProduct.Order_Model__c.containsIgnoreCase(FilterString) || objProduct.Name.containsIgnoreCase(FilterString) || objProduct.Item_Description_EN__c.containsIgnoreCase(FilterString)){
                    objProduct.Item_Description_EN__c = objProduct.Item_Description_EN__c == null ? '' : objProduct.Item_Description_EN__c;
                    // system.debug('添加final');
                    lstFinalProduct.add(objProduct);
                }
            } else if (CCM_Constants.DE == userLanguage){
                //德文环境--->德语筛选
                if (objProduct.Order_Model__c.containsIgnoreCase(FilterString) || objProduct.Name.containsIgnoreCase(FilterString) || objProduct.Item_Description_DE__c.containsIgnoreCase(FilterString)){
                    objProduct.Item_Description_EN__c = objProduct.Item_Description_DE__c == null ? '' : objProduct.Item_Description_DE__c;
                    lstFinalProduct.add(objProduct);
                }
            } else{
                if (objProduct.Order_Model__c.containsIgnoreCase(FilterString) || objProduct.Name.containsIgnoreCase(FilterString)){
                    //其他环境只对Model筛选
                    lstFinalProduct.add(objProduct);
                }
            }

        }
        return JSON.serialize(lstFinalProduct);
    }
    //选择产品后，取Kit对象中查询关联的子对象
    @AuraEnabled
    public static String GetProductListInfo(String ProductId, Decimal UnitsPerPallet, String CustomerId, Decimal UnitsMasterCarton, Decimal InnerBox, String RecordTypeName, String WareHouse, Date PricingDate, String PurchaseOrderId){
        if (String.isBlank(WareHouse)){
            WareHouse = CCM_Constants.GERMANY;
        }
        if (PricingDate == null){
            PricingDate = Date.today();
        }
        DetailProductInfoAndAll objDetailAll = GetProductInfoUtil(ProductId, UnitsPerPallet, CustomerId, UnitsMasterCarton, InnerBox, RecordTypeName, WareHouse, PricingDate, PurchaseOrderId);
        return JSON.serialize(objDetailAll);
    }
    //Honey Added-->2023/08/04校验重复PO Number
    @AuraEnabled
    public static string checkDuplicatePO(String CustomerPo, Boolean isProtal, String CustomerId, String purchaseOrderId){
        system.debug('CustomerPo--------->' + CustomerPo);
        try{
            if (String.isNotBlank(CustomerPo)){
                //获取当前用户角色
                String userType = CCM_PurchaseOrderDetailController.GetUserType();
                List<Purchase_Order__c> lstPurchaseOrder = new List<Purchase_Order__c>();
                List<String> lstDuplicatePo = new List<String>();
                //Honey Updated 只看Customer维度的重复Po .而不是看这个人的数据--。将当前的Po去除
                if (String.isNotBlank(purchaseOrderId)){
                    lstPurchaseOrder = [SELECT Id, Customer__c, Customer_PO_Num__c, Name
                                        FROM Purchase_Order__c
                                        WHERE Customer_PO_Num__c = :CustomerPo AND Customer__c = :CustomerId AND Id != :purchaseOrderId];

                } else{
                    lstPurchaseOrder = [SELECT Id, Customer__c, Customer_PO_Num__c, Name
                                        FROM Purchase_Order__c
                                        WHERE Customer_PO_Num__c = :CustomerPo AND Customer__c = :CustomerId];

                }


                system.debug('lstPurchaseOrder----->' + lstPurchaseOrder);
                if (lstPurchaseOrder == null || lstPurchaseOrder.size() == 0){
                    return null;
                } else{
                    for (Purchase_Order__c objPurchase : lstPurchaseOrder){
                        lstDuplicatePo.add(objPurchase.Name);
                    }
                }
                return Label.Duplicate_Po_Info + ' ' + String.valueOf(lstDuplicatePo);

            }
            return null;

        } catch (Exception e){
            system.debug('报错信息---->' + e.getMessage() + '报错行数----->' + e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    public static DetailProductInfoAndAll GetProductInfoUtil(String ProductId, Decimal UnitsPerPallet, String CustomerId, Decimal UnitsMasterCarton, Decimal InnerBox, String RecordTypeName, String WareHouse, Date PricingDate, String PurchaseOrderId){
        DetailProductInfoAndAll objDetailAll = new DetailProductInfoAndAll();
        system.debug('ProductId-->' + ProductId);

        try{

            Account objAccount = [SELECT Sales_Channel__c, CurrencyIsoCode
                                  from Account
                                  where Id = :CustomerId];
            String SalesChannel = objAccount.Sales_Channel__c;

            List<Product2> filteredProducts = new List<Product2>();
            List<Kit_Item__c> matchingKitItems = [SELECT Product__c, Kit__c, Quantity__c, VK_Product__c
                                                  FROM Kit_Item__c
                                                  WHERE Kit__c = :productId AND VK_Product__c != null];

            Set<String> setItems = new Set<String>();

            system.debug('matchingKitItems-->' + matchingKitItems);
            Map<String, Decimal> mapProduct2QtyTools = new Map<String, Decimal>();
            List<Decimal> lstProductQty = new List<Decimal>();
            if (matchingKitItems != null && matchingKitItems.size() > 0){
                for (Kit_Item__c objKit : matchingKitItems){
                    setItems.add(objKit.VK_Product__c);
                    lstProductQty.add(objKit.Quantity__c);
                    mapProduct2QtyTools.put(objKit.VK_Product__c, objKit.Quantity__c);

                }
                filteredProducts = [SELECT Id, Item_Description_DE__c, InvotoryInfo__c, Item_Description_EN__c, Full_Pallet_Quantity__c, Order_Model__c, Units_Per_Master_Carton_EA__c
                                    FROM Product2
                                    WHERE Id IN:setItems];
            }
            system.debug('filteredProducts--->' + filteredProducts);
            objDetailAll.filteredProducts = filteredProducts;
            Decimal doubleMasterCartonPallent = 0;
            //求Kit的QTy最小公倍数
            if (lstProductQty != null && lstProductQty.size() > 0){
                doubleMasterCartonPallent = calculateLCM(lstProductQty);
                System.debug('最小公倍数: ' + doubleMasterCartonPallent);
            }


            // todo 添加查询语句, Pallet_Item__c, MPQ__c
            Product2 productTmp = [SELECT Id, Pallet_Item__c, MPQ__c
                                   FROM Product2
                                   WHERE Id = :ProductId];

            //判断默认数量, SalesKit 先设置默认值为 0
            objDetailAll.Qty = CaulateQty(SalesChannel, RecordTypeName, WareHouse, productTmp.Pallet_Item__c, UnitsPerPallet, UnitsMasterCarton, InnerBox, doubleMasterCartonPallent, productTmp.MPQ__c, 1);

            system.debug('objDetailAll.Qty-->' + objDetailAll.Qty);
            //查询价格
            //根据ProductId、customerId、Prcing Date去中间表查询
            Map<String, Object> mapFeild2Price = CCM_GetProductInfoUtil.CaucaltePrice(CustomerId, ProductId, PricingDate, objAccount.CurrencyIsoCode);
            //得到价格后获取原价和二次价格
            //Double Discount = 0;
            //假设存在Promotion
            system.debug('mapFeild2Price-->' + mapFeild2Price);
            if (mapFeild2Price != null){
                objDetailAll.listPrice = ((Decimal) mapFeild2Price.get(CCM_Constants.LIST_PRICE)).setScale(2, RoundingMode.HALF_UP);
                objDetailAll.listPriceCaculate = ((Decimal) mapFeild2Price.get(CCM_Constants.LIST_PRICE)).setScale(2, RoundingMode.HALF_UP);
                objDetailAll.SalesPrice = ((Decimal) mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP);
                objDetailAll.SalesPriceCaculate = ((Decimal) mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP);
                //  objDetailAll.unitNetPrice =  (objDetailAll.SalesPrice == null ? 0: objDetailAll.SalesPrice)*(1- objDetailAll.Discount == null ? 0 :objDetailAll.Discount);
                //  objDetailAll.unitNetPriceCaculate =  (objDetailAll.SalesPriceCaculate == null ? 0: objDetailAll.SalesPriceCaculate)*(1- objDetailAll.Discount == null ? 0 :objDetailAll.Discount);
                // objDetailAll.TotalNetPrice = ((objDetailAll.unitNetPrice == null ? 0 : objDetailAll.unitNetPrice) * (objDetailAll.Qty == null ? 0 : objDetailAll.Qty)).setScale(2);
                objDetailAll.applicationMethod = (String) mapFeild2Price.get(CCM_Constants.APPLICATION_METHOD);
                if (objDetailAll.applicationMethod == 'Percent'){
                    objDetailAll.standDiscount = (Decimal) mapFeild2Price.get(CCM_Constants.STAND_DISCOUNT);
                }

            }


            List<String> lstProductId = new List<String>();
            lstProductId.add(ProductId);
            for (Product2 objProduct : filteredProducts){
                lstProductId.add(objProduct.Id);
                objProduct.Units_Per_Master_Carton_EA__c = mapProduct2QtyTools.get(objProduct.Id);
            }
            // mapProduct2QtyTools.put(ProductId,objDetailAll.Qty );
            // if(matchingKitItems != null && matchingKitItems.size()>0){
            //     for(Kit_Item__c objKit : matchingKitItems){
            //         mapProduct2QtyTools.put(objKit.Product__c,(objKit.Quantity__c == null ? 0 : objKit.Quantity__c)*
            //                                 (objDetailAll.Qty == null ? 0 : objDetailAll.Qty ) );
            //     }
            // }

            Map<String, Map<String, Object>> mapProductId2QtyInfo = CheckInvotary(CustomerId, lstProductId, mapProduct2QtyTools, PricingDate, PurchaseOrderId);
            system.debug('mapProductId2QtyInfo-->' + mapProductId2QtyInfo);
            for (Product2 objProduct : filteredProducts){
                Map<String, Object> mapFeild2Value = mapProductId2QtyInfo.get(objProduct.Id);
                objProduct.InvotoryInfo__c = (String) mapFeild2Value.get('CurrentStatus');
                objProduct.MaxGreenLight__c = (Decimal) mapFeild2Value.get('MaxGreenLight');
                objProduct.MaxYellowLight__c = (Decimal) mapFeild2Value.get('MaxYellowLight');
            }
            Map<String, Object> mapFeild2ValueByProduct = mapProductId2QtyInfo.get(ProductId);
            objDetailAll.CurrentStatus = (String) mapFeild2ValueByProduct.get('CurrentStatus');
            objDetailAll.MaxGreenLight = (Decimal) mapFeild2ValueByProduct.get('MaxGreenLight');
            objDetailAll.MaxYellowLight = (Decimal) mapFeild2ValueByProduct.get('MaxYellowLight');
            system.debug('objDetailAll-->' + objDetailAll);
        } catch (Exception e){
            system.debug('报错行数-->' + e.getLineNumber() + '报错信息--->' + e.getMessage());
        }

        return objDetailAll;
    }

    // 重写 GetProductInfoUtil, 防止外层方法 for 循环调用 GetProductInfoUtil 方法的内部查询
    public static DetailProductInfoAndAll GetProductInfoUtil(String ProductId, Decimal UnitsPerPallet, String CustomerId, Decimal UnitsMasterCarton, Decimal InnerBox, String RecordTypeName, String WareHouse, Date PricingDate, String PurchaseOrderId
                                                                , Account objAccountQuery, List<Kit_Item__c> matchingKitItemsQuery, List<Product2> filteredProductsQuery, Product2 productTmpQuery){
        DetailProductInfoAndAll objDetailAll = new DetailProductInfoAndAll();
        system.debug('ProductId-->' + ProductId);

        try{

            Account objAccount = objAccountQuery;
            String SalesChannel = objAccount.Sales_Channel__c;

            List<Product2> filteredProducts = new List<Product2>();
            List<Kit_Item__c> matchingKitItems = matchingKitItemsQuery;

            Set<String> setItems = new Set<String>();

            system.debug('matchingKitItems-->' + matchingKitItems);
            Map<String, Decimal> mapProduct2QtyTools = new Map<String, Decimal>();
            List<Decimal> lstProductQty = new List<Decimal>();
            if (matchingKitItems != null && matchingKitItems.size() > 0){
                for (Kit_Item__c objKit : matchingKitItems){
                    setItems.add(objKit.VK_Product__c);
                    lstProductQty.add(objKit.Quantity__c);
                    mapProduct2QtyTools.put(objKit.VK_Product__c, objKit.Quantity__c);

                }
                filteredProducts = filteredProductsQuery;
            }
            system.debug('filteredProducts--->' + filteredProducts);
            objDetailAll.filteredProducts = filteredProducts;
            Decimal doubleMasterCartonPallent = 0;
            //求Kit的QTy最小公倍数
            if (lstProductQty != null && lstProductQty.size() > 0){
                doubleMasterCartonPallent = calculateLCM(lstProductQty);
                System.debug('最小公倍数: ' + doubleMasterCartonPallent);
            }


            // todo 添加查询语句, Pallet_Item__c, MPQ__c
            Product2 productTmp = productTmpQuery;

            //判断默认数量, SalesKit 先设置默认值为 0
            objDetailAll.Qty = CaulateQty(SalesChannel, RecordTypeName, WareHouse, productTmp.Pallet_Item__c, UnitsPerPallet, UnitsMasterCarton, InnerBox, doubleMasterCartonPallent, productTmp.MPQ__c, 1);

            system.debug('objDetailAll.Qty-->' + objDetailAll.Qty);
            //查询价格
            //根据ProductId、customerId、Prcing Date去中间表查询
            Map<String, Object> mapFeild2Price = CCM_GetProductInfoUtil.CaucaltePrice(CustomerId, ProductId, PricingDate, objAccount.CurrencyIsoCode);
            //得到价格后获取原价和二次价格
            //Double Discount = 0;
            //假设存在Promotion
            system.debug('mapFeild2Price-->' + mapFeild2Price);
            if (mapFeild2Price != null){
                objDetailAll.listPrice = ((Decimal) mapFeild2Price.get(CCM_Constants.LIST_PRICE)).setScale(2, RoundingMode.HALF_UP);
                objDetailAll.listPriceCaculate = ((Decimal) mapFeild2Price.get(CCM_Constants.LIST_PRICE)).setScale(2, RoundingMode.HALF_UP);
                objDetailAll.SalesPrice = ((Decimal) mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP);
                objDetailAll.SalesPriceCaculate = ((Decimal) mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP);
                //  objDetailAll.unitNetPrice =  (objDetailAll.SalesPrice == null ? 0: objDetailAll.SalesPrice)*(1- objDetailAll.Discount == null ? 0 :objDetailAll.Discount);
                //  objDetailAll.unitNetPriceCaculate =  (objDetailAll.SalesPriceCaculate == null ? 0: objDetailAll.SalesPriceCaculate)*(1- objDetailAll.Discount == null ? 0 :objDetailAll.Discount);
                // objDetailAll.TotalNetPrice = ((objDetailAll.unitNetPrice == null ? 0 : objDetailAll.unitNetPrice) * (objDetailAll.Qty == null ? 0 : objDetailAll.Qty)).setScale(2);
                objDetailAll.applicationMethod = (String) mapFeild2Price.get(CCM_Constants.APPLICATION_METHOD);
                if (objDetailAll.applicationMethod == 'Percent'){
                    objDetailAll.standDiscount = (Decimal) mapFeild2Price.get(CCM_Constants.STAND_DISCOUNT);
                }

            }


            List<String> lstProductId = new List<String>();
            lstProductId.add(ProductId);
            for (Product2 objProduct : filteredProducts){
                lstProductId.add(objProduct.Id);
                objProduct.Units_Per_Master_Carton_EA__c = mapProduct2QtyTools.get(objProduct.Id);
            }
            // mapProduct2QtyTools.put(ProductId,objDetailAll.Qty );
            // if(matchingKitItems != null && matchingKitItems.size()>0){
            //     for(Kit_Item__c objKit : matchingKitItems){
            //         mapProduct2QtyTools.put(objKit.Product__c,(objKit.Quantity__c == null ? 0 : objKit.Quantity__c)*
            //                                 (objDetailAll.Qty == null ? 0 : objDetailAll.Qty ) );
            //     }
            // }

            Map<String, Map<String, Object>> mapProductId2QtyInfo = CheckInvotary(CustomerId, lstProductId, mapProduct2QtyTools, PricingDate, PurchaseOrderId);
            system.debug('mapProductId2QtyInfo-->' + mapProductId2QtyInfo);
            for (Product2 objProduct : filteredProducts){
                Map<String, Object> mapFeild2Value = mapProductId2QtyInfo.get(objProduct.Id);
                objProduct.InvotoryInfo__c = (String) mapFeild2Value.get('CurrentStatus');
                objProduct.MaxGreenLight__c = (Decimal) mapFeild2Value.get('MaxGreenLight');
                objProduct.MaxYellowLight__c = (Decimal) mapFeild2Value.get('MaxYellowLight');
            }
            Map<String, Object> mapFeild2ValueByProduct = mapProductId2QtyInfo.get(ProductId);
            objDetailAll.CurrentStatus = (String) mapFeild2ValueByProduct.get('CurrentStatus');
            objDetailAll.MaxGreenLight = (Decimal) mapFeild2ValueByProduct.get('MaxGreenLight');
            objDetailAll.MaxYellowLight = (Decimal) mapFeild2ValueByProduct.get('MaxYellowLight');
            system.debug('objDetailAll-->' + objDetailAll);
        } catch (Exception e){
            system.debug('报错行数-->' + e.getLineNumber() + '报错信息--->' + e.getMessage());
        }

        return objDetailAll;
    }

    //数量改变后校验红绿灯
    @AuraEnabled
    public static Map<String, Map<String, Object>> CheckInvotary(String CustomerId, List<String> lstProductId, Map<String, Decimal> mapProduct2Qty, Date PricingDate, String PurchaseOrderId){
        String LightInfo = null;
        Map<String, Map<String, Object>> mapFeild2ValueReturn = new Map<String, Map<String, Object>>();
        String RecordName = '';
        String wareHouse = '';
        if (String.isNotBlank(PurchaseOrderId)){
            //根据PruchaseOrderId查询仓库和RecordType
            Purchase_Order__c objPurchaseOrder = [SELECT Warehouse__c, RecordTypeId, RecordType.Name
                                                  FROM Purchase_Order__c
                                                  WHERE Id = :PurchaseOrderId];
            RecordName = objPurchaseOrder.RecordType.Name;
            wareHouse = objPurchaseOrder.Warehouse__c;
        } else{
            system.debug('进入设置默认值');
            RecordName = CCM_Constants.REGULAR_ORDER;
            wareHouse = CCM_Constants.GERMANY;
        }

        //批量查询Invotory总库
        List<Inventory__c> lstInventory = [SELECT Id, Product__c, Available_QTY__c
                                           FROM Inventory__c
                                           WHERE Product__c IN:lstProductId AND RecordType.Name = 'All Inventory' AND Sub_Inventory_Code__c = 'EGD01'];
        system.debug('lstInventory-->' + lstInventory);
        Map<String, Decimal> mapProduct2Invotory = new Map<String, Decimal>();
        for (Inventory__c objInvotory : lstInventory){
            mapProduct2Invotory.put(objInvotory.Product__c, objInvotory.Available_QTY__c);
        }
        system.debug('mapProduct2Invotory-->' + mapProduct2Invotory);
        //通过CustomerID查询字库名字
        Account objAccount = [SELECT Id, Planning_Territory_No__c, Sales_Territory__r.Name
                              FROM Account
                              WHERE Id = :CustomerId];
        //获取年月
        Integer Year = PricingDate.year();
        Integer Month = PricingDate.month();
        //根据仓库名字、产品、时间唯一确认一个planning Tool
        List<Planning_Tool__c> lstPlanningTool = [SELECT Id, Actual_QTY__c, Product__c, Current_Month__c, Supply_Year__c
                                                  FROM Planning_Tool__c
                                                  WHERE Product__c IN:lstProductId AND Current_Month__c = :Month AND Supply_Year__c = :Year AND Territory_NO__c = :objAccount.Planning_Territory_No__c];
        system.debug('lstPlanningTool-->' + lstPlanningTool);
        Map<String, Decimal> mapProductId2Tools = new Map<String, Decimal>();
        for (Planning_Tool__c objPlanningTool : lstPlanningTool){
            mapProductId2Tools.put(objPlanningTool.Product__c, objPlanningTool.Actual_QTY__c);
        }
        system.debug('mapProductId2Tools-->' + mapProductId2Tools);
        //对于同一个产品
        for (String ProductId : lstProductId){

            Decimal PlanningToolQty = mapProductId2Tools.get(ProductId) == null ? -1 : mapProductId2Tools.get(ProductId);
            Decimal AllProductQty = mapProduct2Invotory.get(ProductId) == null ? -1 : mapProduct2Invotory.get(ProductId);

            Decimal MaxGreenLight = 0;
            Decimal MaxYellowLight = 0;
            Map<String, Object> mapFeild2Status = new Map<String, Object>();
            system.debug('RecordName计算红绿灯--->' + RecordName);
            system.debug('wareHouse计算红绿灯--->' + wareHouse);
            system.debug('PlanningToolQty--->' + PlanningToolQty);
            system.debug('AllProductQty--->' + AllProductQty);
            if (CCM_Constants.CHINA.equals(wareHouse) || CCM_Constants.PRE_SEASON_ORDER.equals(RecordName)){
                //中国仓库或者预订单都为黄灯
                LightInfo = CCM_Constants.YELLOW_LIGHT;
            } else{
                if ((mapProductId2Tools.get(ProductId) == null || mapProductId2Tools.get(ProductId) <= 0) && (mapProduct2Invotory.get(ProductId) == null || mapProduct2Invotory.get(ProductId) <= 0)){
                    LightInfo = CCM_Constants.RED_LIGHT;
                } else if (mapProductId2Tools.get(ProductId) == null){
                    //表示planning Tool没有数据。只看总库
                    if (AllProductQty > 0){
                        LightInfo = CCM_Constants.GREEN_LIGHT;
                        MaxGreenLight = AllProductQty;
                        MaxYellowLight = AllProductQty;
                    } else{
                        LightInfo = CCM_Constants.RED_LIGHT;
                    }

                } else if (mapProduct2Invotory.get(ProductId) == null){
                    //表示总库没有，则黄灯
                    LightInfo = CCM_Constants.YELLOW_LIGHT;
                    // if (PlanningToolQty > 0){
                    //     LightInfo = CCM_Constants.GREEN_LIGHT;
                    //     MaxGreenLight = PlanningToolQty;
                    //     MaxYellowLight = PlanningToolQty;
                    // } else{
                    //     LightInfo = CCM_Constants.RED_LIGHT;
                    // }

                } else{
                    if (PlanningToolQty > 0 && AllProductQty > 0){
                        //两个最小值
                        MaxGreenLight = PlanningToolQty < AllProductQty ? PlanningToolQty : AllProductQty;
                        MaxYellowLight = PlanningToolQty > AllProductQty ? PlanningToolQty : AllProductQty;
                        LightInfo = CCM_Constants.GREEN_LIGHT;
                    } else if (PlanningToolQty <= 0 && AllProductQty <= 0){
                        system.debug('返回红灯');
                        LightInfo = CCM_Constants.RED_LIGHT;
                    } else{
                        //两个最大值
                        MaxYellowLight = PlanningToolQty > AllProductQty ? PlanningToolQty : AllProductQty;
                        MaxGreenLight = PlanningToolQty < AllProductQty ? PlanningToolQty : AllProductQty;
                        LightInfo = CCM_Constants.GREEN_LIGHT;
                    }

                }


            }
            mapFeild2Status.put('CurrentStatus', LightInfo);
            mapFeild2Status.put('MaxYellowLight', MaxYellowLight);
            mapFeild2Status.put('MaxGreenLight', MaxGreenLight);
            mapFeild2ValueReturn.put(ProductId, mapFeild2Status);
        }

        return mapFeild2ValueReturn;
    }
    @AuraEnabled
    public static String getProductInformationById(Id recordId){
        List<Product2> productItemList = [SELECT Id, Units_Per_Inner_BOX_EA__c, Name, RecordType.DeveloperName, Units_Per_Pallet_EA__c, Retail_Unit_Weight__c, Retail_Height__c, Retail_Length__c, Retail_Unit_Volume__c, Retail_Width__c, Inner_Unit_Weight__c, Inner_Height__c, Inner_Length__c, Inner_Unit_Volume__c, Inner_Width__c, Master_Height__c, Master_Length__c, Master_Unit_Volume__c, Master_Unit_Weight__c, Master_Width__c, InnerBoxes_P_Master_Carton_EA__c, Units_Per_Master_Carton_EA__c, Unit_Retail_Net_Weight__c, Inner_Pack_Net_Weight__c, Master_Pack_Net_Weight__c
                                          FROM Product2
                                          WHERE Id = :recordId];
        System.debug('getProductInformationById' + productItemList);
        return JSON.serialize(productItemList);
    }
    @AuraEnabled
    public static Map<String, Decimal> CaulateAllListPrice(List<Decimal> unitNetPrice, String CustomerId){
        //Total Value--->所有产品售出价格相加
        Map<String, Decimal> mapFeild2Price = new Map<String, Decimal>();
        Decimal TotalValue = 0;
        if (unitNetPrice != null && unitNetPrice.size() > 0){
            for (Decimal netPrice : unitNetPrice){
                TotalValue += (netPrice == null ? 0 : netPrice);
            }
        }

        mapFeild2Price.put('TotalValue', TotalValue);


        system.debug('mapFeild2Price-->' + mapFeild2Price);
        return mapFeild2Price;
    }
    //校验Customer的类型。是PBE或者Distributor 运费默认是0
    //Customer的Name包含HKL的运费默认是12.5
    //其余的类型。按四层结构
    @AuraEnabled
    public static String QueryCustomerType(String CustomerId){
        Set<String> setDESalesChannel = new Set<String>();
        setDESalesChannel.add('OPE Dealer');
        setDESalesChannel.add('Agricultural Machinery Dealer');
        setDESalesChannel.add('Construction Material Dealer');
        setDESalesChannel.add('Power Tool Dealer');
        setDESalesChannel.add('Online Dealer');
        setDESalesChannel.add('Other Dealers');
        Account objAccount = [SELECT Id, Name, Sales_Channel__c, Country__c, RecordType.DeveloperName
                              FROM Account
                              WHERE Id = :CustomerId];
        String customerType = '';
        if (objAccount.Name.contains('HKL')){
            customerType = 'HKL';
        } else if (objAccount.RecordType.DeveloperName == 'Channel' && (CCM_Constants.PBE.equals(objAccount.Sales_Channel__c) || CCM_Constants.DISTRIBUTOR.equals(objAccount.Sales_Channel__c))){
            //用户为PBE或者Distribute类型的时候。运费为0
            customerType = 'PBE';
        } else if (objAccount.RecordType.DeveloperName == 'Channel' && objAccount.Country__c == 'DE' && setDESalesChannel.contains(objAccount.Sales_Channel__c)){
            customerType = 'DE Customer';
        } else{
            customerType = 'Others';
        }
        return customerType;
    }
    @AuraEnabled
    public static Void UpsertPurchaseOrderItem(List<Purchase_Order_Item__c> lstPurchaseOrder, Purchase_Order__c objPurchaseOrder, String AuthBrandId, String productSelectJson){
        system.debug('AuthBrandId--->' + AuthBrandId);
        //更新的Purchase信息-->根据PurchaseOrderId获取Order信息
        Purchase_Order__c objUpsertPurchaseOrder = [SELECT Id, Freight_Term__c, Inco_Term__c, Payment_Term__c, 
                                                           Authorized_Brand__c, Shipping_Place__c, Expected_Delivery_Date__c, 
                                                           CurrencyIsoCode, Header_Discount__c, Header_Discount_Amount__c, 
                                                           Customer__c, Authorized_Brand__r.List_Price_1__c, Pricing_Date__c, 
                                                           Authorized_Brand__r.Price_Book__c, Authorized_Brand__r.List_Price_2__c, 
                                                           Authorized_Brand__r.List_Price_3__c, SelectProductJson__c, 
                                                           Current_Step__c, Freight_Fee__c, Total_Amount__c, Total_Value_Net__c, 
                                                           Total_Quantity__c, RecordType.DeveloperName
                                                    FROM Purchase_Order__c
                                                    WHERE Id = :objPurchaseOrder.Id];
        //先根据PoID删除Item信息
        List<Purchase_Order_Item__c> lstPurchaseItem = [SELECT Id, Purchase_Order__c
                                                        FROM Purchase_Order_Item__c
                                                        WHERE Purchase_Order__c = :objPurchaseOrder.Id];
        //先删除原有的
        delete lstPurchaseItem;
        //获取前端传入的PurchaseOrderItem信息--->将OrderItem信息存入数据库
        //根据Auth Brand查询下面的PriceBook查询PriceBookEntry-->产品、PriceBook唯一确认一个PriceBookName
        Set<String> setPriceBookName = new Set<String>();
        Sales_Program__c objSalesProgram = new Sales_Program__c();
        //通过AuthBrandID查询PriceBookId信息
        if (String.isNotBlank(AuthBrandId)){
            objSalesProgram = [SELECT Id, List_Price_1__c, List_Price_2__c, List_Price_3__c, Price_Book__c
                               FROM Sales_Program__c
                               WHERE Id = :AuthBrandId];
            setPriceBookName.add(objSalesProgram.List_Price_1__c);
            setPriceBookName.add(objSalesProgram.List_Price_2__c);
            setPriceBookName.add(objSalesProgram.List_Price_3__c);
            setPriceBookName.add(objSalesProgram.Price_Book__c);
        }

        List<Decimal> lstDoubleNetPrice = new List<Decimal>();

        Set<String> setProductId = new Set<String>();
        Boolean isPreSeaonOrder = objUpsertPurchaseOrder.RecordType.DeveloperName == CCM_Constants.PRE_SEASON_ORDER_DEV_NAME ? true : false;
        for (Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            system.debug('Model---->' + objPurchaseOrderitem.Product_Model__c);
            system.debug('Product__c---->' + objPurchaseOrderitem.Product__c);
            system.debug('objPurchaseOrderitem-->' + objPurchaseOrderitem);
            objPurchaseOrderitem.Purchase_Order__c = objPurchaseOrder.Id;
            setProductId.add(objPurchaseOrderitem.Product__c);
            system.debug('objPurchaseOrderitem.unit_Net_Price2__c---->' + objPurchaseOrderitem.unit_Net_Price2__c);
            lstDoubleNetPrice.add(objPurchaseOrderitem.Unit_Price__c);
            system.debug('objPurchaseOrderitem.Inventory__c---->' + objPurchaseOrderitem.Inventory__c);
            system.debug('oCCM_Constants.GREEN_LIGHT---->' + CCM_Constants.GREEN_LIGHT);
            system.debug(' objPurchaseOrder.Expected_Delivery_Date__c---->' + objPurchaseOrderitem.Request_Date__c);
            // if (objPurchaseOrderitem.Inventory__c == 'Green' || isPreSeaonOrder){
            //     //如果是绿灯。发货时间与期望时间一致
            //     objPurchaseOrderitem.Schedule_Ship_Date__c = objPurchaseOrderitem.Request_Date__c;
            // }
            objPurchaseOrderitem.Schedule_Ship_Date__c = objPurchaseOrder.Expected_Delivery_Date__c;
        }
        //通过Id查询Volunm 信息
        Decimal estVolumn = 0;
        Decimal estWeight = 0;
        Map<String, Decimal> mapId2Volum = new Map<String, Decimal>();
        Map<String, Decimal> mapId2Weight = new Map<String, Decimal>();
        List<Product2> lstProduct = [SELECT Id, Est_Order_Volum__c, Est_Order_Weight__c
                                     FROM Product2
                                     WHERE Id IN:setProductId];
        for (Product2 objProduct : lstProduct){
            mapId2Volum.put(objProduct.Id, objProduct.Est_Order_Volum__c == null ? 0 : objProduct.Est_Order_Volum__c);
            mapId2Weight.put(objProduct.Id, objProduct.Est_Order_Weight__c == null ? 0 : objProduct.Est_Order_Weight__c);
        }
        for (Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            estVolumn += (mapId2Volum.ContainsKey(objPurchaseOrderitem.Product__c) ? mapId2Volum.get(objPurchaseOrderitem.Product__c) : 0) * objPurchaseOrderitem.Quantity__c;
            estWeight += (mapId2Weight.ContainsKey(objPurchaseOrderitem.Product__c) ? mapId2Weight.get(objPurchaseOrderitem.Product__c) : 0) * objPurchaseOrderitem.Quantity__c;
        }
        Map<String, Decimal> mapFeile2Price = CaulateAllListPrice(lstDoubleNetPrice, objUpsertPurchaseOrder.Customer__c);
        system.debug('mapFeile2Price-->' + mapFeile2Price);
        List<Pricebook_Entry__c> lstPriceBookEntry = [SELECT Id, Product__c, Start_Date__c, End_Date__c,PriceBook__c, PriceBook__r.Name
                                                      FROM Pricebook_Entry__c
                                                      WHERE Product__c IN:setProductId AND Start_Date__c <= :objUpsertPurchaseOrder.Pricing_Date__c AND End_Date__c >= :objUpsertPurchaseOrder.Pricing_Date__c AND PriceBook__c IN:setPriceBookName ];
        // Map<String, String> mapProductDate2Name = new Map<String, String>();
        // String PriceBookName1st = '';
        Map<String, String> productPriceBookMap = new Map<String, String>();
        for (Pricebook_Entry__c objPriceBook : lstPriceBookEntry){
            //优先一级价格册
            if (objPriceBook.PriceBook__c == objSalesProgram.Price_Book__c){
                productPriceBookMap.put(objPriceBook.Product__c, objPriceBook.PriceBook__r.Name);
            }
        }
        for (Pricebook_Entry__c objPriceBook : lstPriceBookEntry){
            if(!productPriceBookMap.containsKey(objPriceBook.Product__c)) {
                productPriceBookMap.put(objPriceBook.Product__c, objPriceBook.PriceBook__r.Name);
            }
        }
        for (Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            system.debug('objPurchaseOrderitem--->' + objPurchaseOrderitem.Unit_Price__c);
            objPurchaseOrderitem.List_Price_Name__c = productPriceBookMap.get(objPurchaseOrderitem.Product__c);
            objPurchaseOrderitem.UOM__c = 'EA';
            objPurchaseOrderitem.CurrencyIsoCode = objUpsertPurchaseOrder.CurrencyIsoCode;
        }


        system.debug('需要存入的数据--->' + lstPurchaseOrder);
        upsert lstPurchaseOrder;

        objUpsertPurchaseOrder.Freight_Term__c = objPurchaseOrder.Freight_Term__c;
        objUpsertPurchaseOrder.Inco_Term__c = objPurchaseOrder.Inco_Term__c;
        objUpsertPurchaseOrder.Expected_Delivery_Date__c = objPurchaseOrder.Expected_Delivery_Date__c;
        objUpsertPurchaseOrder.Multi_Header_Promotion__c = objPurchaseOrder.Multi_Header_Promotion__c;
        system.debug('objPurchaseOrder.Header_Discount_Amount__c--->' + objPurchaseOrder.Header_Discount_Amount__c);
        system.debug('objPurchaseOrder.Header_Discount__c--->' + objPurchaseOrder.Header_Discount__c);
        objUpsertPurchaseOrder.Header_Discount_Amount__c = objPurchaseOrder.Header_Discount_Amount__c;
        objUpsertPurchaseOrder.Header_Discount__c = objPurchaseOrder.Header_Discount__c;
        objUpsertPurchaseOrder.Promotion__c = objPurchaseOrder.Promotion__c;
        objUpsertPurchaseOrder.Est_OrderVolum__c = estVolumn;
        objUpsertPurchaseOrder.Est_Weight__c = estWeight;
        objUpsertPurchaseOrder.SelectProductJson__c = productSelectJson;
        objUpsertPurchaseOrder.Payment_Term__c = objPurchaseOrder.Payment_Term__c;
        objUpsertPurchaseOrder.Freight_Fee__c = objPurchaseOrder.Freight_Fee__c;
        objUpsertPurchaseOrder.Current_Step__c = objUpsertPurchaseOrder.Current_Step__c > CCM_Constants.BRAND_PRODUCT ? objUpsertPurchaseOrder.Current_Step__c : CCM_Constants.BRAND_PRODUCT;
        if (String.isNotBlank(AuthBrandId)){
            objUpsertPurchaseOrder.Authorized_Brand__c = AuthBrandId;
        }


        objUpsertPurchaseOrder.Total_Quantity__c = objPurchaseOrder.Total_Quantity__c;


        system.debug('更新的Purchase信息--->' + objUpsertPurchaseOrder);
        update objUpsertPurchaseOrder;
    }
    @AuraEnabled
    public static String InsertPurchaseInfoProtal(List<Purchase_Order_Item__c> lstPurchaseOrder, Purchase_Order__c objPurchaseOrder, String AuthBrandId, String productSelectJson){
        system.debug('AuthBrandId--->' + AuthBrandId);
        String selectCustomer = objPurchaseOrder.Customer__c;
        system.debug('selectCustomer-->' + selectCustomer);
        Account objAccount = [SELECT OwnerId, Id
                              FROM Account
                              WHERE Id = :selectCustomer];
        objPurchaseOrder.Salesperson__c = objAccount.OwnerId;
        objPurchaseOrder.Warehouse__c = CCM_Constants.GERMANY;
        objPurchaseOrder.Pricing_Date__c = Date.today();
        objPurchaseOrder.Status__c = CCM_Constants.DRAFT_STOCK;

        objPurchaseOrder.RecordTypeId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get('Regular_Order').getRecordTypeId();
        objPurchaseOrder.Order_Type__c = CCM_Constants.DSVR;
        if (String.isNotBlank(objPurchaseOrder.Id)){
            //先根据PoID删除Item信息
            List<Purchase_Order_Item__c> lstPurchaseItem = [SELECT Id, Purchase_Order__c
                                                            FROM Purchase_Order_Item__c
                                                            WHERE Purchase_Order__c = :objPurchaseOrder.Id];
            //先删除原有的
            delete lstPurchaseItem;
        }
        //更新Po信息
        upsert objPurchaseOrder;

        //获取前端传入的PurchaseOrderItem信息--->将OrderItem信息存入数据库
        //根据Auth Brand查询下面的PriceBook查询PriceBookEntry-->产品、PriceBook唯一确认一个PriceBookName
        Set<String> setPriceBookName = new Set<String>();
        Sales_Program__c objSalesProgram = new Sales_Program__c();
        //通过AuthBrandID查询PriceBookId信息
        if (String.isNotBlank(AuthBrandId)){
            objSalesProgram = [SELECT Id, List_Price_1__c, List_Price_2__c, List_Price_3__c, Price_Book__c
                               FROM Sales_Program__c
                               WHERE Id = :AuthBrandId];
            setPriceBookName.add(objSalesProgram.List_Price_1__c);
            setPriceBookName.add(objSalesProgram.List_Price_2__c);
            setPriceBookName.add(objSalesProgram.List_Price_3__c);
            setPriceBookName.add(objSalesProgram.Price_Book__c);
        }

        List<Decimal> lstDoubleNetPrice = new List<Decimal>();

        Set<String> setProductId = new Set<String>();
        for (Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            system.debug('Model---->' + objPurchaseOrderitem.Product_Model__c);
            system.debug('Product__c---->' + objPurchaseOrderitem.Product__c);
            system.debug('objPurchaseOrderitem-->' + objPurchaseOrderitem);
            objPurchaseOrderitem.Purchase_Order__c = objPurchaseOrder.Id;
            setProductId.add(objPurchaseOrderitem.Product__c);
            lstDoubleNetPrice.add(objPurchaseOrderitem.Unit_Price__c);
            system.debug('objPurchaseOrderitem.Inventory__c---->' + objPurchaseOrderitem.Inventory__c);
            system.debug('oCCM_Constants.GREEN_LIGHT---->' + CCM_Constants.GREEN_LIGHT);
            system.debug(' objPurchaseOrder.Expected_Delivery_Date__c---->' + objPurchaseOrderitem.Request_Date__c);
            if (objPurchaseOrderitem.Inventory__c == 'Green'){
                //如果是绿灯。发货时间与期望时间一致
                objPurchaseOrderitem.Schedule_Ship_Date__c = objPurchaseOrderitem.Request_Date__c;
            }
        }
        //通过Id查询Volunm 信息
        Decimal estVolumn = 0;
        Decimal estWeight = 0;
        Map<String, Decimal> mapId2Volum = new Map<String, Decimal>();
        Map<String, Decimal> mapId2Weight = new Map<String, Decimal>();
        List<Product2> lstProduct = [SELECT Id, Est_Order_Volum__c, Est_Order_Weight__c
                                     FROM Product2
                                     WHERE Id IN:setProductId];
        for (Product2 objProduct : lstProduct){
            mapId2Volum.put(objProduct.Id, objProduct.Est_Order_Volum__c == null ? 0 : objProduct.Est_Order_Volum__c);
            mapId2Weight.put(objProduct.Id, objProduct.Est_Order_Weight__c == null ? 0 : objProduct.Est_Order_Weight__c);
        }
        for (Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            estVolumn += (mapId2Volum.ContainsKey(objPurchaseOrderitem.Product__c) ? mapId2Volum.get(objPurchaseOrderitem.Product__c) : 0) * objPurchaseOrderitem.Quantity__c;
            estWeight += (mapId2Weight.ContainsKey(objPurchaseOrderitem.Product__c) ? mapId2Weight.get(objPurchaseOrderitem.Product__c) : 0) * objPurchaseOrderitem.Quantity__c;
        }
        Map<String,Decimal> mapFeile2Price =  CaulateAllListPrice(lstDoubleNetPrice,objPurchaseOrder.Customer__c);
        system.debug('mapFeile2Price-->'+mapFeile2Price);
        List<Pricebook_Entry__c> lstPriceBookEntry = [
            SELECT Id,Product__c,Start_Date__c,End_Date__c,PriceBook__c, PriceBook__r.Name
                                                      FROM Pricebook_Entry__c WHERE Product__c IN :setProductId
            AND Start_Date__c <= :objPurchaseOrder.Pricing_Date__c AND End_Date__c >= :objPurchaseOrder.Pricing_Date__c
            AND PriceBook__c IN :setPriceBookName 
        ];
        Map<String,String> mapProductDate2Name2st = new Map<String,String>();
        Map<String,String> mapProductDate2Name1st = new Map<String,String>();

        for (Pricebook_Entry__c objPriceBook : lstPriceBookEntry){
            //优先一级价格册
            if (objPriceBook.PriceBook__c == objSalesProgram.Price_Book__c){
                mapProductDate2Name1st.put(objPriceBook.Product__c, objPriceBook.PriceBook__r.Name);
            } else{
                mapProductDate2Name2st.put(objPriceBook.Product__c, objPriceBook.PriceBook__r.Name);
            }

        }
        for (Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            String PriceBookName1st = mapProductDate2Name1st.get(objPurchaseOrderitem.Product__c);
            objPurchaseOrderitem.List_Price_Name__c = String.isNotBlank(PriceBookName1st) ? PriceBookName1st : mapProductDate2Name2st.get(objPurchaseOrderitem.Product__c);
            objPurchaseOrderitem.UOM__c = 'EA';
        }


        system.debug('需要存入的数据--->' + lstPurchaseOrder);
        upsert lstPurchaseOrder;


        objPurchaseOrder.Est_OrderVolum__c = estVolumn;
        objPurchaseOrder.Est_Weight__c = estWeight;
        objPurchaseOrder.SelectProductJson__c = productSelectJson;
        objPurchaseOrder.Payment_Term__c = objPurchaseOrder.Payment_Term__c;
        objPurchaseOrder.Freight_Fee__c = objPurchaseOrder.Freight_Fee__c;
        Decimal selectProduct = Decimal.valueOf(CCM_Constants.BRAND_PRODUCT) + 1;
        objPurchaseOrder.Current_Step__c = objPurchaseOrder.Current_Step__c > String.valueOf(selectProduct) ? objPurchaseOrder.Current_Step__c : String.valueOf(selectProduct);
        if (String.isNotBlank(AuthBrandId)){
            objPurchaseOrder.Authorized_Brand__c = AuthBrandId;
        }


        objPurchaseOrder.Total_Quantity__c = objPurchaseOrder.Total_Quantity__c;
        objPurchaseOrder.Header_Discount__c = objPurchaseOrder.Header_Discount__c;
        system.debug('更新的Purchase信息--->' + objPurchaseOrder);
        update objPurchaseOrder;

        return objPurchaseOrder.Id;
    }
    @AuraEnabled
    public static String getPicklistOption(String objectAPI, String fieldAPI, String fifterString, Boolean isPortal){
        List<SelectOption> selectList = new List<SelectOption>();
        List<SelectOptionItem> selectListReturn = new List<SelectOptionItem>();
        if (String.isNotBlank(fifterString)){
            List<String> exceptValueList = New List<String>();
            exceptValueList.add(fifterString);
            selectList = CCM_Untils.getPicklistOption(objectAPI, fieldAPI, exceptValueList);
        } else{
            selectList = CCM_Untils.getPicklistOption(objectAPI, fieldAPI);
        }

        List<SelectOption> compareOptions = new List<SelectOption>();
        if(isPortal && objectAPI == 'Sales_Program__c' && fieldAPI == 'Payment_Term__c') {
            compareOptions = CCM_Untils.getPicklistOption('Purchase_Order__c', 'Payment_Term__c');
        }

        for (SelectOption item : selectList){
            SelectOptionItem option = new SelectOptionItem();
            option.label = item.getLabel();
            option.value = item.getValue();
            // if(isPortal && objectAPI == 'Sales_Program__c' && fieldAPI == 'Payment_Term__c') {
            //     for(SelectOption compareOption : compareOptions) {
            //         if(compareOption.getLabel() == item.getLabel()) {
            //             option.label = compareOption.getValue();
            //         }
            //     }
            // }
            selectListReturn.add(option);
        }
        return JSON.serialize(selectListReturn);
    }
    public static List<Sales_Program__c> QueryAuthBrandInfo(String CustomerId){
        List<Sales_Program__c> lstSalesProgram = new List<Sales_Program__c>();


        lstSalesProgram = [SELECT Id, Brands__c, Freight_Term__c, IncoTerm__c, Payment_Term__c, Customer__c, Customer__r.Sales_Channel__c
                           FROM Sales_Program__c
                           WHERE Customer__c = :CustomerId AND Order_Type__c = 'Sales Order - DSV' AND Status__c = 'Active'];
        for (Sales_Program__c objsales : lstSalesProgram){
            objsales.Brands__c = String.isBlank(objsales.Brands__c) ? '' : objsales.Brands__c;
            objsales.Freight_Term__c = String.isBlank(objsales.Freight_Term__c) ? '' : objsales.Freight_Term__c;
            objsales.IncoTerm__c = String.isBlank(objsales.IncoTerm__c) ? '' : objsales.IncoTerm__c;
            objsales.Payment_Term__c = String.isBlank(objsales.Payment_Term__c) ? '' : objsales.Payment_Term__c;
            objsales.Customer__c = String.isBlank(objsales.Customer__c) ? '' : objsales.Customer__c;

        }


        system.debug('objSalesProgram-->' + lstSalesProgram);
        return lstSalesProgram;
    }
    private class SelectOptionItem{
        public String label;
        public String value;
    }
    public Class DetailProductInfoAndAll{
        //子库信息
        public List<Product2> filteredProducts{ get; set; }
        //默认数量信息
        public String Qty{ get; set; }
        //库存信息
        //String Invotory{get;set;}
        //绿灯表示可改变--->黄灯，红灯表示不可变
        public String CurrentStatus{ get; set; }
        public String productId{ get; set; }
        public Decimal MaxYellowLight{ get; set; }
        public Decimal MaxGreenLight{ get; set; }
        public String Model{ get; set; }
        public String OriginalProductName{ get; set; }
        public String ProductDescription{ get; set; }
        public Decimal standDiscount{ get; set; }
        public String applicationMethod{ get; set; }
        //原价
        public Decimal listPrice{ get; set; }
        public Decimal listPriceCaculate{ get; set; }
        //折扣
        public Double Discount{ get; set; }
        public Double unitNetPriceCaculate{ get; set; }
        //二级价格册
        public Decimal SalesPrice{ get; set; }
        public Decimal SalesPriceCaculate{ get; set; }
        //售出价
        public Decimal unitNetPrice{ get; set; }
        //总价
        public Double TotalNetPrice{ get; set; }

        public String RecordTypeName{ get; set; }
    }
    // public Static Integer CaulateQty(String SalesChannel,Decimal UnitsPerPallet,Decimal UnitsMasterCarton ,Decimal InnerBox,String RecordTypeName,String WareHouse,Double doubleMasterCartonPallent){
    //     system.debug('RecordTypeName-->'+RecordTypeName);
    //     if(CCM_Constants.MKT.equals(RecordTypeName)|| CCM_Constants.SAM.equals(RecordTypeName)){
    //         //记录类型为MKT和SAM的不论德国仓库还是中国仓库都返回一样的值-->不论Sales是distribute还是Dealer都是一样的
    //         return Integer.valueOf(UnitsPerPallet);
    //     }
    //     if(CCM_Constants.TLS_PRODUCT.equals(RecordTypeName)){
    //         //对于Tools的recordType只用判断仓库---》德国仓库为Unit 中国为master
    //         if(CCM_Constants.GERMANY.equals(WareHouse)){
    //             //德国仓库
    //             return Integer.valueOf(UnitsPerPallet);
    //         }else if(CCM_Constants.GERMANY.equals(UnitsMasterCarton)){
    //             return Integer.valueOf(UnitsMasterCarton);
    //         }
    //     }
    //     //对于Acc类型的产品-->如果是德国仓库  需要区分Channel。中国仓库不需要
    //     if(CCM_Constants.ACC.equals(RecordTypeName)){
    //         if(CCM_Constants.GERMANY.equals(WareHouse)){
    //             if(CCM_Constants.DISTRIBUTOR.equals(SalesChannel)){
    //                 //对于德国仓库的distribute取Inner Box
    //                 return Integer.valueOf(InnerBox);
    //             }else if(SalesChannel.contains(CCM_Constants.DEALER) ){
    //                 //对于德国仓库的dealer取unit
    //                 return Integer.valueOf(UnitsPerPallet);
    //             }
    //         }else if(CCM_Constants.CHINA.equals(WareHouse)){
    //             //对于中国仓库都取master
    //             return Integer.valueOf(UnitsMasterCarton);
    //         }
    //     }
    //     if(CCM_Constants.KIT.equals(RecordTypeName)){
    //         return  Integer.valueOf(doubleMasterCartonPallent);
    //     }
    //     return 0;
    // }
    // 修改后的 CaulateQty 方法
    public Static String CaulateQty(String SalesChannel, String RecordTypeName, String WareHouse, String PalletItem, Decimal UnitsPerPallet, Decimal UnitsMasterCarton, Decimal InnerBox, Decimal doubleMasterCartonPallent, Decimal MPQ, Decimal SalesKit){
        // 根据条件查询
        if (SalesChannel != null && SalesChannel.contains(CCM_Constants.DEALER)){
            SalesChannel = CCM_Constants.DEALER;
        } else if (SalesChannel != null && SalesChannel.contains(CCM_Constants.DISTRIBUTOR)){
            SalesChannel = CCM_Constants.DISTRIBUTOR;
        }
        // if (RecordTypeName == 'TLS_VK'){
        //     RecordTypeName = 'TLS_KIT';
        // }

        String queryStr = 'Select p.Field_Value__c, p.Meaasge__c, Message_DE__c from Product_Data__c p';
        queryStr += ' where p.Sales_Channel__c =:SalesChannel';
        queryStr += ' and p.Item_Class__c =:RecordTypeName';
        queryStr += ' and p.DSV__c =:WareHouse';
        queryStr += ' and p.Pallet_Item__c =:PalletItem';
        System.debug('---------------> CaulateQty: queryStr ' + queryStr);

        List<Product_Data__c> productDataList = Database.query(queryStr);
        String resultStr = '';

        if (productDataList != null && productDataList.size() == 1 && productDataList.get(0) != null){
            Product_Data__C productData = productDataList.get(0);
            System.debug('---------------> CaulateQty: productData ' + productData);

            // 结果拼接
            Map<String, Decimal> fieldValueMap = new Map<String, Decimal>();
            fieldValueMap.put('UnitsPerPallet', UnitsPerPallet == null ? 0 : UnitsPerPallet);
            fieldValueMap.put('UnitsMasterCarton', UnitsMasterCarton == null ? 0 : UnitsMasterCarton);
            fieldValueMap.put('InnerBox', InnerBox == null ? 0 : InnerBox);
            fieldValueMap.put('doubleMasterCartonPallent', doubleMasterCartonPallent == null ? 0 : doubleMasterCartonPallent);
            fieldValueMap.put('MPQ', MPQ == null ? 0 : MPQ);
            fieldValueMap.put('SalesKit', SalesKit == null ? 0 : SalesKit);
            fieldValueMap.put('Each', 1);

            Boolean isDE = UserInfo.getLanguage() == 'de';

            resultStr = fieldValueMap.get(productData.Field_Value__c) + ' ' + (isDE ? productData.Message_DE__c : productData.Meaasge__c);
        }

        System.debug('---------------> CaulateQty: resultStr ' + resultStr);

        return resultStr;
    }

    public static Map<String, String> calculateQtyForProducts(String salesChannel, String wareHouse, List<Product2> products, Map<String, Decimal> mapProductId2DoubleQty, Decimal salesKit) {
        Map<String, String> productQtyMap = new Map<String, String>();
        if (salesChannel != null && salesChannel.contains(CCM_Constants.DEALER)){
            salesChannel = CCM_Constants.DEALER;
        } else if (salesChannel != null && salesChannel.contains(CCM_Constants.DISTRIBUTOR)){
            salesChannel = CCM_Constants.DISTRIBUTOR;
        }

        Set<String> recordTypeSet = new Set<String>();
        Set<String> palletItemSet = new Set<String>();

        Map<String, String> productAttributeMap = new Map<String, String>();
        Map<String, Map<String, Decimal>> productFieldValueMap = new Map<String, Map<String, Decimal>>();
        for(Product2 product : products) {
            recordTypeSet.add(product.RecordType.Name);
            palletItemSet.add(product.Pallet_Item__c);
            productAttributeMap.put(product.RecordType.Name + product.Pallet_Item__c, product.Id);

            Map<String, Decimal> fieldValueMap = new Map<String, Decimal>();
            fieldValueMap.put('UnitsPerPallet', product.Units_Per_Pallet_EA__c == null ? 0 : product.Units_Per_Pallet_EA__c);
            fieldValueMap.put('UnitsMasterCarton', product.Units_Per_Master_Carton_EA__c == null ? 0 : product.Units_Per_Master_Carton_EA__c);
            fieldValueMap.put('InnerBox', product.Units_Per_Inner_BOX_EA__c == null ? 0 : product.Units_Per_Inner_BOX_EA__c);
            fieldValueMap.put('doubleMasterCartonPallent', mapProductId2DoubleQty.get(product.Id) == null ? 0 : mapProductId2DoubleQty.get(product.Id));
            fieldValueMap.put('MPQ', product.MPQ__c == null ? 0 : product.MPQ__c);
            fieldValueMap.put('SalesKit', salesKit == null ? 0 : salesKit);
            fieldValueMap.put('Each', 1);
            productFieldValueMap.put(product.Id, fieldValueMap);
        }

        String query = 'SELECT Field_Value__c, Meaasge__c, Message_DE__c, Item_Class__c, Pallet_Item__c FROM Product_Data__c';
        query += ' WHERE Sales_Channel__c = :salesChannel';
        query += ' AND Item_Class__c IN :recordTypeSet';
        query += ' AND DSV__c = :wareHouse';
        query += ' AND Pallet_Item__c IN :palletItemSet';

        List<Product_Data__c> productDataList = Database.query(query);
        Map<String, List<Product_Data__c>> productDataAttributeMap = new Map<String, List<Product_Data__c>>();
        for(Product_Data__c productData : productDataList) {
            String key = productData.Item_Class__c + productData.Pallet_Item__c;
            if(!productDataAttributeMap.containsKey(key)) {
                productDataAttributeMap.put(key, new List<Product_Data__c>());
            }
            productDataAttributeMap.get(key).add(productData);
        }

        
        Boolean isDE = UserInfo.getLanguage() == 'de';
        for(String key : productAttributeMap.keySet()) {
            if(productDataAttributeMap.containsKey(key)) {
                List<Product_Data__c> productDatas = productDataAttributeMap.get(key);
                if(productDatas.size() == 1) {
                    String productId = productAttributeMap.get(key);
                    String resultStr = productFieldValueMap.get(productId).get(productDatas[0].Field_Value__c) + ' ' + (isDE ? productDatas[0].Message_DE__c : productDatas[0].Meaasge__c);
                    productQtyMap.put(productId, resultStr);
                }
            }
        }
        return productQtyMap;
    }

    public CCM_RequestPurchaseOrderController(){
    }
    public static Decimal calculateLCM(List<Decimal> numbers){
        Decimal lcm = numbers[0];
        for (Integer i = 1; i < numbers.size(); i++){
            lcm = calculateLCM(lcm, numbers[i]);
        }
        return lcm;
    }
    private static Decimal calculateLCM(Decimal a, Decimal b){
        Decimal gcd = calculateGCD(a, b);
        return (a * b) / gcd;
    }
    private static Decimal calculateGCD(Decimal a, Decimal b){
        while (b != 0){
            Decimal temp = b;

            b = Math.mod(Integer.valueOf(a), Integer.valueOf(b));
            a = temp;
        }
        return a;
    }
    // add by haibo -- order promotion
    @AuraEnabled
    public static List<CCM_Order_Promotion.PromotionData> getPromotionsByProduct(String prodId, String customerId, Boolean isPortal, Boolean isDropShip, Date PricingDate){
        return CCM_Order_Promotion.getPromotionsByProduct(prodId, customerId, isPortal, isDropShip, PricingDate);
    }
    // add by haibo
    @AuraEnabled
    public static String QueryPirchaseAndItemInfo(String PurchaseOrderId, Boolean IsProtal){
        return CCM_PurchaseOrderPreview.QueryPirchaseAndItemInfo(PurchaseOrderId, IsProtal);
    }
    @AuraEnabled
    public static Map<String, Object> getWholePromotionOffering(String uploadData){
        return CCM_Order_Promotion.getWholePromotionOffering(uploadData);
    }
    @AuraEnabled
    public static Map<String, Object> getQuantityLimitInventoryCondition(String uploadData){
        return CCM_Order_Promotion.getQuantityLimitInventoryCondition(uploadData);
    }
    
    @AuraEnabled
    public static String getProductPriceAnInventory(String customerId, String productInfo) {
        Map<String, Object> productQuantityMap = (Map<String, Object>)JSON.deserializeUntyped(productInfo);
        List<String> productIds = new List<String>();
        Map<String, Double> mapProductId2Qty = new Map<String, Double>();
        for(String productId : productQuantityMap.keySet()) {
            productIds.add(productId);
            mapProductId2Qty.put(productId, Double.valueOf(productQuantityMap.get(productId)));
        }

        Map<String, Product2> productMap = new Map<String, Product2>();
        String QueryStr = 'SELECT Units_Per_Inner_BOX_EA__c,Units_Per_Pallet_EA__c, Item_Description_DE_Formula__c, Item_Description_EN_Formula__c, ';
        QueryStr += ' Units_Per_Master_Carton_EA__c, Pallet_Item__c, MPQ__c, RecordType.Name, Order_Model__c';
        QueryStr += ' FROM Product2 WHERE Id IN :productIds AND (Product_Status__c != \'INACT\' AND  Product_Status__c != \'RTO\')';
        for(Product2 product : Database.query(QueryStr)) {
            productMap.put(product.Id, product);
        }
        
        Map<String, DetailProductInfoAndAll> productInfoResultMap = new Map<String, DetailProductInfoAndAll>();
        Map<String, Map<String, Object>> mapProductId2PriceInfo = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfo(CustomerId, productIds, Date.today(), mapProductId2Qty);
        for(String productId : mapProductId2PriceInfo.keySet()) {
            DetailProductInfoAndAll objDetailAll = new DetailProductInfoAndAll();
            Map<String, Object> mapFeild2Price = (Map<String, Object>)mapProductId2PriceInfo.get(productId);
            objDetailAll.listPrice = ((Decimal) mapFeild2Price.get(CCM_Constants.LIST_PRICE)).setScale(2, RoundingMode.HALF_UP);
            objDetailAll.listPriceCaculate = ((Decimal) mapFeild2Price.get(CCM_Constants.LIST_PRICE)).setScale(2, RoundingMode.HALF_UP);
            objDetailAll.SalesPrice = ((Decimal) mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP);
            objDetailAll.SalesPriceCaculate = ((Decimal) mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP);
            objDetailAll.applicationMethod = (String) mapFeild2Price.get(CCM_Constants.APPLICATION_METHOD);
            if (objDetailAll.applicationMethod == 'Percent'){
                objDetailAll.standDiscount = (Decimal) mapFeild2Price.get(CCM_Constants.STAND_DISCOUNT);
            }
            productInfoResultMap.put(productId, objDetailAll);
        }

        Map<String, Decimal> mapProduct2QtyTools = new Map<String, Decimal>();
        Map<String, Map<String, Object>> mapProductId2QtyInfo = CheckInvotary(CustomerId, productIds, mapProduct2QtyTools, Date.today(), null);
        for(String productId : productInfoResultMap.keySet()) {
            if(mapProductId2QtyInfo.containsKey(productId)) {
                Map<String, Object> mapFeild2ValueByProduct = mapProductId2QtyInfo.get(productId);
                productInfoResultMap.get(productId).CurrentStatus = (String) mapFeild2ValueByProduct.get('CurrentStatus');
                productInfoResultMap.get(productId).MaxGreenLight = (Decimal) mapFeild2ValueByProduct.get('MaxGreenLight');
                productInfoResultMap.get(productId).MaxYellowLight = (Decimal) mapFeild2ValueByProduct.get('MaxYellowLight');
            }
        }
        String userLanguage = UserInfo.getLanguage();
        Account objAccount = [SELECT Sales_Channel__c, CurrencyIsoCode
                                from Account
                                where Id = :customerId];
        String SalesChannel = objAccount.Sales_Channel__c;
        for(String productId : productInfoResultMap.keySet()) {
            if(productMap.containsKey(productId)) {
                Product2 prod = productMap.get(productId);
                productInfoResultMap.get(productId).Qty = CaulateQty(SalesChannel, prod.RecordType.Name, CCM_Constants.GERMANY, prod.Pallet_Item__c, prod.Units_Per_Pallet_EA__c, prod.Units_Per_Master_Carton_EA__c, prod.Units_Per_Inner_BOX_EA__c, 0, prod.MPQ__c, 1);
                productInfoResultMap.get(productId).ProductDescription = prod.Item_Description_EN_Formula__c;
                if(userLanguage == CCM_Constants.DE) {
                    productInfoResultMap.get(productId).ProductDescription = prod.Item_Description_DE_Formula__c;
                }
                productInfoResultMap.get(productId).productId = prod.Id;
                productInfoResultMap.get(productId).Model = prod.Order_Model__c;
                productInfoResultMap.get(productId).RecordTypeName = prod.RecordType.Name;
            }
        }
        return JSON.serialize(productInfoResultMap);
    }
    
    @AuraEnabled
    public static String getWarrantyClaimNumber(String warrantyClaimId) {
        String claimNumber = null;
        for(Warranty_Claim__c claim : [SELECT Name FROM Warranty_Claim__c WHERE Id = :warrantyClaimId]) {
            claimNumber = claim.Name;
        }
        return claimNumber;
    }
    
    public class DropAdItem{
        public String dropAdId;
        public String dropAdName;
    }
    public class ContactItem{
        public String contactId;
        public String contactName;
    }
}