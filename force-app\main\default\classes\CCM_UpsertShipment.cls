/**
 * url : /services/apexrest/CCM_RestService_DealShipmentInfoEU
 * [
  {
    "SHIP_NO": "1321",
    "ORDER_ORACLEID": "123",
    "ORDER_ORACLE_NUMBER": "213",
    "DELIVERY_POSTAL_CODE": "123",
    "DELIVERY_COUNTRY": "DE",
    "DELIVERY_STATE": "123",
    "DELIVERY_CITY": "213",
    "DELIVERY_ADDRESS": "123",
    "RECEIPT_POSTAL_CODE": "123",
    "RECEIPT_COUNTRY": "DE",
    "RECEIPT_STATE": "1323",
    "RECEIPT_CITY": "123",
    "RECEIPT_ADDRESS": "312",
    "Attribute1": "",
    "Attribute2": "",
    "Attribute3": "",
    "Attribute4": "",
    "Attribute5": "",
    "Attribute6": "",
    "Attribute7": "",
    "Attribute8": "",
    "Attribute9": "",
    "Attribute10": "",
    "Attribute11": "",
    "Attribute12": "",
    "Attribute13": "",
    "Attribute14": "",
    "Attribute15": "",
    "ShipmentLine": [
      {
        "SHIP_LINE_NUMBER": "213",
        "ORDER_LINE_NUMBER": "1231",
        "ORDER_LINE_ORACLEID": "132",
        "STATUS": "Booked",
        "SHIP_LINE_QUANTITY": "12",
        "PRODUCT_MODEL_NO": "321",
        "SUB_INVENTORY": "321",
        "SHIP_SET": "21312",
        "Attribute1": "",
        "Attribute2": "",
        "Attribute3": "",
        "Attribute4": "",
        "Attribute5": "",
        "Attribute6": "",
        "Attribute7": "",
        "Attribute8": "",
        "Attribute9": "",
        "Attribute10": "",
        "Attribute11": "",
        "Attribute12": "",
        "Attribute13": "",
        "Attribute14": "",
        "Attribute15": ""
      }
    ]
  }
]
 * Author : Honey
 * Date : 2023/06/04
 * Description: 从外部接口获取Shipment信息并存入数据库
*/
@RestResource(urlMapping='/CCM_RestService_DealShipmentInfoEU')
global without sharing class CCM_UpsertShipment {
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        resObj.Process_Result = new List<ReturnItem>();
        String resStr = req.requestBody.toString();
        try {
            // resStr = resStr.replace('\'', '\\\'');
            if(!resStr.startsWith('[')){
                resStr = '[' + resStr + ']';
            }
            reqObjList = parse(resStr);
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObjList);
            //遍历reqObj用于创建Order\OrderLine\OderLineAdjustment
            List<Shipment__c> lstUpsertShipment = new List<Shipment__c>();
            List<Shipment_Item__c> lstUpsertShipmentItem = new List<Shipment_Item__c>();
            //创建唯一标识到具体对象的映射
            Map<String,Shipment__c> mapId2Shipment = new Map<String,Shipment__c>();
            Map<String,Shipment_Item__c> mapId2ShipmentLine = new Map<String,Shipment_Item__c>();
            
            //校验是否需要插入或更新通过各种Id
            Set<String> setShipmentId = new Set<String>();
            Set<String> setShipmentLineId = new Set<String>();
            //通过OrderModel绑定Product
            Set<String> setOrderModel = new Set<String>();
            Set<String> setOrderOracleId = new Set<String>();
            Map<String, String> orderShipmentItemMap = new Map<String, String>();
            Map<String, String> orderItemOracleMap = new Map<String, String>();
            Map<String, String> shipmentItemDeliveryDateMap = new Map<String, String>();
            for(ReqestObj objShipment : reqObjList){
                setShipmentId.add(objShipment.SHIP_NO);
                setOrderOracleId.add(objShipment.ORDER_ORACLE_ID);
                Shipment__c objUpsertShipment = new Shipment__c();
                SetShipmentValue(objShipment, objUpsertShipment);
                mapId2Shipment.put(objShipment.SHIP_NO + objShipment.ORDER_ORACLE_ID, objUpsertShipment);
                system.debug('objUpsertShipment-->'+ objUpsertShipment);
                if(objShipment.ShipmentLine == null){
                    continue;
                }
                //设置完Oder后遍历Item
                for(ShipmentItem objItem : objShipment.ShipmentLine){
                    setOrderModel.add(objItem.PRODUCT_MODEL_NO);
                    setShipmentLineId.add(objItem.SHIP_LINE_NUMBER);
                    orderShipmentItemMap.put(objItem.SHIP_LINE_NUMBER, objShipment.SHIP_NO + objShipment.ORDER_ORACLE_ID);
                    if(String.isNotBlank(objItem.ORDER_LINE_ORACLEID)) {
                        orderItemOracleMap.put(objItem.ORDER_LINE_ORACLEID, '');
                    }
                    Shipment_Item__c objShipmentItem = new Shipment_Item__c();
                    SetShipmentLineValue(objItem, objShipmentItem, objShipment);
                    shipmentItemDeliveryDateMap.put(objItem.SHIP_LINE_NUMBER, objShipment.DELIVERY_DATE);
                    system.debug('objShipmentItem-->'+objShipmentItem);
                    mapId2ShipmentLine.put(objItem.SHIP_LINE_NUMBER, objShipmentItem);
                  
                }
            }
            //通过OrderOracleId去Order表中查询Order Id
            List<Order> lstOrder = [
                SELECT Id ,Order_OracleID__c FROM Order WHERE Order_OracleID__c IN : setOrderOracleId
            ];
            Map<String,String> mapOrderOracle2Id = new Map<String,String>();
            for(Order objOrder : lstOrder){
                mapOrderOracle2Id.put(objOrder.Order_OracleID__c,objOrder.Id);
            }

            for(Order_Item__c item : [SELECT OrderLine_OracleID__c FROM Order_Item__c WHERE OrderLine_OracleID__c IN :orderItemOracleMap.keySet()]) {
                orderItemOracleMap.put(item.OrderLine_OracleID__c, item.Id);
            }

            //通过Shipment的Oracle Id 查询Shipment信息--->用于校验是否重复
            List<Shipment__c> lstExitShipment = [
                SELECT Id ,Ship_OracleID__c,Order_Text__c  FROM Shipment__c WHERE  Ship_OracleID__c IN :setShipmentId AND Order_Text__c IN:setOrderOracleId
            ];
            List<Shipment_Item__c> lstExitShipmentLine = [
                SELECT Id ,Shipment_Item_Number__c FROM Shipment_Item__c WHERE Shipment_Item_Number__c IN :setShipmentLineId
            ];
            //通过OrderModel匹配Order
            List<Product2> lstProducts = [
                SELECT Id,Order_Model__c FROM Product2 WHERE Order_Model__c IN :setOrderModel
            ];
            Map<String,String> mapOrderModel2ProductId = new Map<String,String>();
            for(Product2 objProduct : lstProducts){
                mapOrderModel2ProductId.put(objProduct.Order_Model__c, objProduct.Id);
            }
          
            CheckUpdateOrInsertShipment(lstExitShipment, mapId2Shipment, lstUpsertShipment, mapOrderOracle2Id);
          
            system.debug('需要插入的数据Shipment-->'+lstUpsertShipment);
            
            
            Map<String,String> mapShipmentOracleId2Id = new Map<String,String>();
            if(lstUpsertShipment.size()>0){
                Database.UpsertResult[] resShipmentList= Database.upsert(lstUpsertShipment,false);
                for (Integer i = 0 ; i < resShipmentList.size() ; i++) {
                    if (!resShipmentList.get(i).isSuccess()) {
                        Database.Error[] err = resShipmentList.get(i).getErrors();
                        ReturnItem request = new ReturnItem();
                        request.External_Id = String.valueOf(lstUpsertShipment.get(i).Ship_OracleID__c);
                        request.Error_Message = 'This Shipment was failed saving in Salesforce';
                        request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                        resObj.Process_Result.add(request);
                    }else{
                        //表示更新或插入成功
                        mapShipmentOracleId2Id.put(String.valueOf(lstUpsertShipment.get(i).Ship_OracleID__c) + lstUpsertShipment.get(i).Order_Text__c, lstUpsertShipment.get(i).Id);
                    }
                }
            }
            // 插入Shipment后获取Shipment的Id ,用于关联Shipment Item
            CheckUpdateOrInsertOrderLine(lstExitShipmentLine, mapId2ShipmentLine, lstUpsertShipmentItem, mapShipmentOracleId2Id, mapOrderModel2ProductId, orderShipmentItemMap, orderItemOracleMap, shipmentItemDeliveryDateMap);
            system.debug('需要插入的数据OrderLine-->'+lstUpsertShipmentItem);
           
            if(lstUpsertShipmentItem.size()>0){
                Database.UpsertResult[] resOrderLineList= Database.upsert(lstUpsertShipmentItem, false);
                for (Integer i = 0 ; i < resOrderLineList.size() ; i++) {
                    if (!resOrderLineList.get(i).isSuccess()) {
                        Database.Error[] err = resOrderLineList.get(i).getErrors();
                        ReturnItem request = new ReturnItem();
                        request.External_Id = String.valueOf(lstUpsertShipmentItem.get(i).Shipment_Item_Number__c);
                        request.Error_Message = 'This Shipment Item was failed saving in Salesforce';
                        request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                        resObj.Process_Result.add(request);
                    }
                }
            }
            if (resObj.Process_Result.size() == 0) {
                resObj.Process_Status = 'Success';
                String logId = Util.logIntegration('ShipmentService Log','CCM_UpsertShipment','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            }else {
                resObj.Process_Status = 'Fail';
                String logId = Util.logIntegration('ShipmentService Exception','CCM_UpsertShipment','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept Shipment Info',logId,getMailErrorMessage(resObj));
            }
            
        } catch (Exception e) {
           
            resObj.Process_Status = 'Fail';
            ReturnItem request = new ReturnItem();
            request.Error_Message = 'This Shipment was failed saving in Salesforce';
            request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
            resObj.Process_Result.add(request);
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration('ShipmentService Exception','CCM_UpsertShipment','POST',
                                               JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept Order Info',logId,getMailErrorMessage(resObj));
            system.debug('错误的resObj--->'+resObj);
            return resObj;
        }
        system.debug('resObj--->'+resObj);
        return resObj;
    }
    global static void CheckUpdateOrInsertShipment(List<Shipment__c> lstExitShipment , Map<String,Shipment__c> mapId2Shipment, List<Shipment__c> lstUpsertShipment,Map<String,String> mapOrderOracle2Id) {
        for(Shipment__c objExitShipment : lstExitShipment){
            if(mapId2Shipment.containsKey(objExitShipment.Ship_OracleID__c + objExitShipment.Order_Text__c)){
                //如果存在的列表中的OrderId包含传入的则是修改。将Id赋值
                Shipment__c objShipment = mapId2Shipment.get(objExitShipment.Ship_OracleID__c + objExitShipment.Order_Text__c);
                objShipment.Id = objExitShipment.Id;
                mapId2Shipment.put(objExitShipment.Ship_OracleID__c + objExitShipment.Order_Text__c, objShipment);
            }
        }
        for(String key : mapId2Shipment.keySet()){
            Shipment__c objShipment = mapId2Shipment.get(key);
            objShipment.Order__c = mapOrderOracle2Id.get(objShipment.Order_Text__c);
            lstUpsertShipment.add(objShipment);
        }
    }
    global static void CheckUpdateOrInsertOrderLine(List<Shipment_Item__c> lstExitShipmentLine , Map<String,Shipment_Item__c> mapId2ShipmentLine ,  
                                                    List<Shipment_Item__c> lstUpsertShipmentItem,Map<String,String> mapShipmentOracleId2Id,Map<String,String> mapOrderModel2ProductId,
                                                    Map<String, String> orderShipmentItemMap, Map<String, String> orderItemOracleMap, Map<String, String> shipmentItemDeliveryDateMap) {
        for(Shipment_Item__c objExitShipmentLine : lstExitShipmentLine){
            if(mapId2ShipmentLine.containsKey(objExitShipmentLine.Shipment_Item_Number__c)){
                //如果存在的列表中的OrderId包含传入的则是修改。将Id赋值
                Shipment_Item__c objShipmentLine = mapId2ShipmentLine.get(objExitShipmentLine.Shipment_Item_Number__c);
                objShipmentLine.Id = objExitShipmentLine.Id;
                mapId2ShipmentLine.put(objExitShipmentLine.Shipment_Item_Number__c, objShipmentLine);
            }
        }
        List<Order_Item__c> orderItemsToUpdate = new List<Order_Item__c>();
        for(String key : mapId2ShipmentLine.keySet()){
            String orderShipKey = orderShipmentItemMap.get(key);
            Shipment_Item__c objShipmentItem = mapId2ShipmentLine.get(key);
            objShipmentItem.Shipment__c = mapShipmentOracleId2Id.get(orderShipKey);
            objShipmentItem.Product__c = mapOrderModel2ProductId.get(objShipmentItem.Product_Text__c);
            if(orderItemOracleMap.containsKey(objShipmentItem.Order_Line_OracleId__c)) {
                if(String.isNotBlank(orderItemOracleMap.get(objShipmentItem.Order_Line_OracleId__c))) {
                    Order_Item__c orderItem = new Order_Item__c();
                    orderItem.Id = orderItemOracleMap.get(objShipmentItem.Order_Line_OracleId__c);
                    if(String.isNotBlank(shipmentItemDeliveryDateMap.get(objShipmentItem.Shipment_Item_Number__c))) {
                        orderItem.Delivery_Date__c = Date.valueOf(shipmentItemDeliveryDateMap.get(objShipmentItem.Shipment_Item_Number__c));
                    }
                    orderItemsToUpdate.add(orderItem);
                }
            }
            lstUpsertShipmentItem.add(objShipmentItem);
        }
        List<Log__c> logs = new List<Log__c>();
        Integer index = 0;
        for(Database.SaveResult result : Database.update(orderItemsToUpdate, false)) {
            if(!result.isSuccess()) {
                String errMsg = result.getErrors()[0].getMessage();
                Log__c log = new Log__c();
                log.RecordId__c = orderItemsToUpdate.get(index).Id;
                log.ApexName__c = 'Update Delivery Date';
                log.Error_Message__c = errMsg;
                logs.add(log);
            }
            index++;
        }
        if(!logs.isEmpty()) {
            insert logs;
        }
    }
    global static void SetShipmentValue(ReqestObj objShipment,Shipment__c objUpsertShipment){
        objUpsertShipment.Ship_OracleID__c  = objShipment.SHIP_NO;
        objUpsertShipment.Order_Text__c  = objShipment.ORDER_ORACLE_ID;
        objUpsertShipment.Order_Number__c = objShipment.ORDER_ORACLE_NUMBER;
        objUpsertShipment.Delivery_Postal_Code__c = objShipment.DELIVERY_POSTAL_CODE;
        objUpsertShipment.Delivery_Country__c = objShipment.DELIVERY_COUNTRY;
        objUpsertShipment.Delivery_State__c = objShipment.DELIVERY_STATE;
        objUpsertShipment.Delivery_City__c = objShipment.DELIVERY_CITY;
        objUpsertShipment.Delivery_Date__c = String.isBlank(objShipment.DELIVERY_DATE) ? null : Date.valueOf(objShipment.DELIVERY_DATE) ;
        objUpsertShipment.Delivery_Note_Number__c = objShipment.DELIVERY_NOTE_NUMBER;
        objUpsertShipment.Delivery_Address__c = objShipment.DELIVERY_ADDRESS;
        objUpsertShipment.Receipt_Postal_Code__c = objShipment.RECEIPT_POSTAL_CODE;
        objUpsertShipment.Receipt_Country__c = objShipment.RECEIPT_COUNTRY;
        objUpsertShipment.Receipt_State__c = objShipment.RECEIPT_STATE;
        objUpsertShipment.Receipt_City__c = objShipment.RECEIPT_CITY;
        objUpsertShipment.Receipt_Address__c = objShipment.RECEIPT_ADDRESS;
    }
     global static void SetShipmentLineValue(ShipmentItem objShipmentLine,Shipment_Item__c objUpsertShipmentLine,ReqestObj objShipment){
        // objUpsertShipmentLine.External_Id__c = objShipmentLine.LINEID;
        objUpsertShipmentLine.SHIP_NO__c = objShipment.SHIP_NO;
        objUpsertShipmentLine.Shipment_Item_Number__c = objShipmentLine.SHIP_LINE_NUMBER;
        objUpsertShipmentLine.OrderLine_Number__c = objShipmentLine.ORDER_LINE_NUMBER;
        objUpsertShipmentLine.Order_Line_OracleId__c = objShipmentLine.ORDER_LINE_ORACLEID;
        objUpsertShipmentLine.Status__c = objShipmentLine.STATUS;
        objUpsertShipmentLine.Item_Quantity_in_Shipment__c = String.isBlank(objShipmentLine.SHIP_LINE_QUANTITY)? null : Double.valueOf(objShipmentLine.SHIP_LINE_QUANTITY);
        objUpsertShipmentLine.Product_Text__c = objShipmentLine.PRODUCT_MODEL_NO;
     }
  
    global static List<ReqestObj> parse(String jsonStr) {
        return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }
   
    global class ResultObj {
        global String Process_Status;
        global List<ReturnItem> Process_Result;
    }
    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
        errContent += 'Process Status : Fail<br/><br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
                errContent += 'External ID : ' + Item.External_Id + '<br/>';
                errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
                errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>'; 
            }
        }
        return errContent;
    }
    global class ReqestObj {
       global String SHIP_NO;
        global String ORDER_ORACLE_ID;
        global String ORDER_ORACLE_NUMBER;
        global String DELIVERY_POSTAL_CODE;
        global String DELIVERY_COUNTRY;
        global String DELIVERY_STATE;
        global String DELIVERY_CITY;
        global String DELIVERY_DATE;
        global String DELIVERY_NOTE_NUMBER;
        
        global String DELIVERY_ADDRESS;
        global String RECEIPT_POSTAL_CODE;
        global String RECEIPT_COUNTRY;
        global String RECEIPT_STATE;
        
        global String RECEIPT_CITY;
        global String RECEIPT_ADDRESS;
        global List<ShipmentItem> ShipmentLine;
    }
    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
    global class ShipmentItem {
        // global String LINEID;
        global String SHIP_LINE_NUMBER;
        global String ORDER_LINE_NUMBER;
        global String ORDER_LINE_ORACLEID;
        global String STATUS;
        global String SHIP_LINE_QUANTITY;
        global String PRODUCT_MODEL_NO;
        global String SUB_INVENTORY;
        global String SHIP_SET;
    }
   
}