/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 02-26-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_Community_OrderApplicationDetailCtl{
    public static String userType = '';
    public enum userTypeEnums{
        InsideSales, 
        SalesRep
    }
    public enum OrderTypeEnums{
        Regular_Order, 
        PreSeason_Order
    }
    public static String orderQueryStr = 'SELECT Id, Name, ' + 
                                     'Customer_EU__r.Name,' + 
                                     'Customer_EU__r.OwnerId,' + 
                                     'Customer_EU__r.Owner.Name,' +  
                                     'Customer_EU__r.Owner.Firstname,' +  
                                     'Customer_EU__r.Owner.LastName,' +  
                                     'Customer_EU__c,' + 
                                     'Account.CurrencyIsoCode, ' +
                                     'Account_Text__c,' + 
                                     'Order_Number_CRM__c,'+
                                     'PO_Number__c,' + 
                                     'Freight_Term__c,' + 
                                     'toLabel(Payment_Term__c),' + 
                                     'toLabel(Inco_Term__c),' + 
                                     'Sales_Rep__c,' + 
                                     'Sales_Rep__r.Name,' + 
                                     'Order_Date__c,' + 
                                     'Date_Order__c,' + 
                                     'Purchase_Order__c,' + 
                                     'Purchase_Order__r.CreatedBy.Name,' + 
                                     'Purchase_Order__r.Header_Discount_Amount__c,' + 
                                     'Purchase_Order__r.Name,' + 
                                     'Order_Number__c,' + 
                                     'CreatedBy.Name,' + 
                                     'Last_Update_By_In_EBS__c,' +
                                     'Source__c,' + 
                                     'toLabel(Order_Status__c),' + 
                                     'RecordType.Name,' + 
                                     'Order_Type_In_Oracle__c, ' +
                                     'toLabel(warehouse__c),' + 
                                     'Expected_Delivery_Date__c,' + 
                                     'Remark__c,' + 
                                     'Notes__c,' + 
                                     'CurrencyIsoCode,' + 
                                     'Dropship_ZIP__c  ,'+
                                     'Price_Book_Text__c,' + 
                                     'Est_Order_Weight__c,' + 
                                     'Est_Order_Volume__c,' + 
                                     'Is_Dropship__c,' + 
                                     'BillTo__r.Account_Address__r.Country__c,' + 
                                     'BillTo__r.Account_Address__r.City__c,' + 
                                     'BillTo__r.Account_Address__r.Street_1__c,' + 
                                     'BillTo__r.Account_Address__r.Postal_Code__c,' + 
                                     'BillTo__r.Account_Address__r.Company_name_1__c,' + 
                                     'ShipTo__r.Account_Address__r.Country__c,' + 
                                     'ShipTo__r.Account_Address__r.City__c,' + 
                                     'ShipTo__r.Account_Address__r.Street_1__c,' + 
                                     'ShipTo__r.Account_Address__r.Postal_Code__c,' + 
                                     'ShipTo__r.Account_Address__r.Company_name_1__c,' + 
                                     'Dropship_Name__c,' + 
                                     'Dropship_Country__c,' + 
                                     'Dropship_City__c,' + 
                                     'Dropship_Address1__c,' + 
                                     'Total_Value__c,' + 
                                     'header_discount__c,' + 
                                     'header_discount_amount__c,' + 
                                     'Total_Value_Net__c,' + 
                                     'Feright_Fee__c,' + 
                                     'Total_Amount__c,' + 
                                     'Insurance_fee__c,' + 
                                     'Other_fee__c,' + 
                                     'Total_Tax__c,' + 
                                     'Dropship_Type__c,' + 
                                     'Pricing_Date__c,' + 
                                     'RecordType.DeveloperName, ' + 
                                     'Comments__c, ' + 
                                     // add haibo (获取product record type -- Product__r.RecordType_Name__c)
                                     '(SELECT IsDeleted,Product__c,Product__r.RecordType_Name__c,Product_Text__c,CurrencyIsoCode,Inventory__c,Sales_Agreement_No__c,Date_Order__c,Schedule_Ship_Date__c,Pricing_Date__c,Returned_Qty__c,Description__c,Product__r.Order_Model__c,UOM__c,Request_Date__c,Order_Quantity__c,Release_to_warehouse_Qty__c,Pick_and_Packed_Qty__c,Shipped_Qty__c,Invoiced_Qty__c,cancelled_qty__c,Back_Order_Qty__c,Est_Replenish_Date__c,List_Price__c,Discount__c,Unit_Net_Price__c,Total_Net_Price_Formal__c,Remark__c,Order_Line_Status__c,Delivery_Date__c FROM Order_Items_Order__r where Product__c != null order by Order_Line_Number_Format__c asc)' + 
                                     'FROM Order ' + 
                                     'WHERE IsDeleted = false ';
    @AuraEnabled
    public static String getData(String recordId){
        Boolean isSuccess = true;
        InitData initD = new InitData();
        //判断用户
        /*String profileName = [SELECT Id, Name
                              FROM Profile
                              WHERE Id = :UserInfo.getProfileId()].Name;
        if (profileName == 'Inside Sales Manager' || profileName == 'Inside Sales Rep' || profileName == 'NJ Account Director' || profileName == 'NJ Operation Manager'){
            userType = 'InsideSales';
        } else if (profileName == 'Sales Manager'){
            userType = 'SalesRep';
        }*/
         userType = CCM_PurchaseOrderDetailController.GetUserType();

        //判断类型
        if (String.isNotBlank(recordId)){
            String objName = Util.findObjectNameFromRecordIdPrefix(recordId);
            if (objName == 'Purchase_Order__c'){
            } else if (objName == 'Order'){
                orderQueryStr += ' AND Id = \'' + recordId + '\'';
                List<Order> orderList = Database.query(orderQueryStr);
                if (orderList != null && orderList.size() > 0){
                    Order orderData = orderList[0];
                    initD = retOrderMappingInfo(orderData);
                    initD.shipmentInfo = retShipmentMappingInfo(recordId);
                    initD.AttachMentData = retAttachmentInfo(recordId);
                    initD.UserType = userType;
                }
            }
            isSuccess = true;
        } else{
            isSuccess = false;
            initD.errorMsg = 'No found order record.';
        }
        return JSON.serialize(initD);
    }
    //Order header/order item Mapping
    public static InitData retOrderMappingInfo(Order order){
        InitData initData = new InitData();
        OrderData oData = new OrderData();
        List<OrderItemData> orderItems = new List<OrderItemData>();
        List<OrderItemData> returnOrderItems = new List<OrderItemData>();
        //判断step
        if (order.Order_Status__c == Label.Order_Process){
            initData.currentStep = 4;
        } else if (order.Order_Status__c == Label.Partial_Shipment){
            initData.currentStep = 5;
        } else if (order.Order_Status__c == Label.Shipment_Complete){
            initData.currentStep = 6;
        } else if (order.Order_Status__c == Label.Cancelled){
            initData.currentStep = 4;
        }
        //查询结果赋值
        oData.Id = order.Id;
        oData.Customer = order.Customer_EU__r.Name;
        oData.currencyCode = order.CurrencyIsoCode;
        oData.remark = order.Remark__c;
        oData.CustomerNumber = order.Account_Text__c;
        oData.CustomerPO = order.PO_Number__c;
        oData.PoNumber = order.Purchase_Order__r.Name;
        //根据Po Number查找
        oData.Source = order.Source__c;
        
        oData.FreightTerm = order.Freight_Term__c;
        oData.PaymentTerm = order.Payment_Term__c;
        order.Inco_Term__c = order.Inco_Term__c == null ? '' : order.Inco_Term__c;
        //IncoTerm value
        oData.IncoTerm = order.Inco_Term__c;
        if(String.isNotBlank(order.Notes__c)) {
            oData.IncoTerm = oData.IncoTerm + ' ' + order.Notes__c;
        }
        // if (order.Inco_Term__c == 'FCA' || order.Inco_Term__c == 'EXW'){
        //     oData.IncoTerm = order.Inco_Term__c + ' ' + 'Möckmühl';
        // } else{
        //     if(order.Is_Dropship__c == 'Y'){
        //         oData.IncoTerm = order.Inco_Term__c + ' ' + (order.Dropship_City__c <> null ? order.Dropship_City__c : '');
        //     }else{
        //         oData.IncoTerm = order.Inco_Term__c + ' ' + (order.ShipTo__r.Account_Address__r.City__c <> null ? order.ShipTo__r.Account_Address__r.City__c : '');

        //     }
        // }
       // system.debug('Customer_EU__r.Owner.Name--->'+order.Customer_EU__r.Owner.Name);
        oData.Salesperson = (order.Customer_EU__r.Owner.FirstName == null ? '' : order.Customer_EU__r.Owner.FirstName ) + ' ' + 
        (order.Customer_EU__r.Owner.LastName == null ? '' :  order.Customer_EU__r.Owner.LastName ) ;
        oData.OrderDate = order.Order_Date__c <> null ? String.ValueOf(Order.Order_Date__c) : null;
        oData.OrderCreatedinEBS = order.Date_Order__c <> null ? String.ValueOf(Order.Date_Order__c) : null;
        oData.OrderNumber = order.Order_Number__c;
        oData.OrderStatus = order.Order_Status__c;
        // oData.OrderType = order.RecordType.Name;
        oData.OrderType = order.Order_Type_In_Oracle__c;
        oData.Warehouse = order.warehouse__c;
        oData.ExpectedDeliveryDate = order.Expected_Delivery_Date__c <> null ? String.ValueOf(Order.Expected_Delivery_Date__c) : null;
        oData.ShippingPlace = order.Notes__c;
        oData.PricingDate = order.Pricing_Date__c <> null ? String.ValueOf(Order.Pricing_Date__c) : null;
        oData.PriceList = order.Price_Book_Text__c;
        // oData.createdBy = order.Purchase_Order__r.createdBy.name == null ? order.createdBy.Name : order.Purchase_Order__r.createdBy.name;
        oData.createdBy = order.Last_Update_By_In_EBS__c == null ? 'Integration' : order.Last_Update_By_In_EBS__c;
        oData.EstWeight = order.Est_Order_Weight__c;
        oData.EstVolume = order.Est_Order_Volume__c;
        oData.IsDropshipOrder = order.Is_Dropship__c;
        //deliver!
        AddressData billToAddress = new AddressData();
        billToAddress.Country = order.BillTo__r.Account_Address__r.Country__c;
        billToAddress.City = order.BillTo__r.Account_Address__r.City__c;
        billToAddress.Street = order.BillTo__r.Account_Address__r.Street_1__c;
        billToAddress.PostCode = order.BillTo__r.Account_Address__r.Postal_Code__c;
        billToAddress.CompanyName = order.BillTo__r.Account_Address__r.Company_name_1__c;
        oData.BillToAddress = billToAddress;

        AddressData shipToAddress = new AddressData();
        shipToAddress.Country = order.ShipTo__r.Account_Address__r.Country__c;
        shipToAddress.City = order.ShipTo__r.Account_Address__r.City__c;
        shipToAddress.Street = order.ShipTo__r.Account_Address__r.Street_1__c;
        shipToAddress.PostCode = order.ShipTo__r.Account_Address__r.Postal_Code__c;
        shipToAddress.CompanyName = order.ShipTo__r.Account_Address__r.Company_name_1__c;
        oData.ShipToAddress = shipToAddress;

        AddressData dropshipAddress = new AddressData();
        dropshipAddress.Country = order.Dropship_Country__c;
        dropshipAddress.City = order.Dropship_City__c;
        dropshipAddress.Street = order.Dropship_Address1__c;
        dropshipAddress.PostCode = order.Dropship_ZIP__c ;
        dropshipAddress.CompanyName = order.Dropship_Name__c;
        oData.DropshipAddress = dropshipAddress;
        oData.DropshipType = order.Dropship_Type__c;
        //At bottom right corner
        oData.TotalValue = String.ValueOf(order.Total_Value__c);
        oData.HeaderDiscount = String.ValueOf(order.header_discount__c);
        //如果是PO下单。需要 取Po上的Amount 
      
        oData.HeaderDiscountAmount = String.ValueOf(order.header_discount_amount__c);
        oData.TotalValueNet = String.ValueOf(order.Total_Value_Net__c);
        oData.FreightCost = String.ValueOf(order.Feright_Fee__c);
        oData.InsuranceFee = String.ValueOf(order.Insurance_fee__c);
        oData.OtherFee = String.ValueOf(order.Other_fee__c);
        oData.VAT = String.ValueOf(order.Total_Tax__c);
        oData.TotalDueAmount = String.ValueOf(order.Total_Amount__c);
        oData.comments = order.Comments__c;
        Date pricingDate ;
        Map<String, OrderItemData> productMap = new Map<String, OrderItemData>();
        if (order.Order_Items_Order__r != null && order.Order_Items_Order__r.size() > 0){
            for (Order_Item__c oItem : order.Order_Items_Order__r){
                if(oItem.Product_Text__c == CCM_Constants.INSURANCE_FEE || oItem.Product_Text__c == CCM_Constants.OTHER_FEE || 
                   oItem.Product_Text__c == CCM_Constants.FREIGHT_FEE || oItem.Product_Text__c == CCM_Constants.CHARGE_FORWEAR_FEE || oItem.Product_Text__c == CCM_Constants.LABOR  ){
                    continue;
                }
                OrderItemData orderItem = new OrderItemData();
                orderItem.Id = oItem.Id;
                orderItem.kitId = oItem.Product__c;
                orderItem.ProductDescription = oItem.Description__c;
                orderItem.Model = oItem.Product__r.Order_Model__c;
                orderItem.UOM = oItem.UOM__c;
                orderItem.OrderDate = oItem.Date_Order__c <> null ? String.ValueOf(oItem.Date_Order__c) : null;
                orderItem.RequestDate = String.ValueOf(oItem.Request_Date__c);
                orderItem.deliveryDate = String.valueOf(oItem.Delivery_Date__c);
                orderItem.currencyCode = oItem.CurrencyIsoCode;
                orderItem.OrderQty = String.ValueOf(oItem.Order_Quantity__c);
                orderItem.BackOrderQty = String.ValueOf(oItem.Back_Order_Qty__c);
                orderItem.ReleasetoWarehouseQty = String.ValueOf(oItem.Release_to_warehouse_Qty__c);
                orderItem.Inventory = oItem.Inventory__c;
                orderItem.PickedPackedQty = String.ValueOf(oItem.Pick_and_Packed_Qty__c);
                orderItem.ShippedQty = String.ValueOf(oItem.Shipped_Qty__c);
                orderItem.InvoicedQty = String.ValueOf(oItem.Invoiced_Qty__c);
                orderItem.CancelledQty = String.ValueOf(oItem.cancelled_qty__c);
                orderItem.ReturnedQty = String.ValueOf(oItem.Returned_Qty__c);
                orderItem.EstReplenishDate = oItem.Est_Replenish_Date__c <> null ? String.ValueOf(oItem.Est_Replenish_Date__c) : null;
                orderItem.ScheduleShipDate = oItem.Schedule_Ship_Date__c <> null ? String.ValueOf(oItem.Schedule_Ship_Date__c) : null;
                orderItem.PricingDate = oItem.Pricing_Date__c != null ? String.ValueOf(oItem.Pricing_Date__c) : String.ValueOf(Order.Pricing_Date__c);
                PricingDate = orderItem.PricingDate == null ? null : Date.valueOf(orderItem.PricingDate);
                orderItem.ListPrice = String.ValueOf(oItem.List_Price__c);
                orderItem.Discount = String.ValueOf(oItem.Discount__c);
                orderItem.UnitNetPrice = String.ValueOf(oItem.Unit_Net_Price__c);
                orderItem.TotalNetPrice = String.ValueOf(oItem.Total_Net_Price_Formal__c);
                orderItem.Remark = oItem.Remark__c;
                orderItem.SalesAgreementNo = oItem.Sales_Agreement_No__c;
                //begin add by vince 20240221
                orderItem.OrderLineStatus = oItem.Order_Line_Status__c;
                //end add by vince 20240221
                // add haibo (获取product record type -- Product__r.RecordType_Name__c)
                orderItem.productRecordType = oItem.Product__r.RecordType_Name__c;
                orderItems.add(orderItem) ;
                productMap.put(orderItem.kitId, orderItem);
            }
        }
        //查询kit item
        List<Kit_Item__c> kitList = [SELECT id, Kit__c, vk_product__c, vk_product__r.PPC_Desp__c, vk_product__r.Order_Model__c, Quantity__c,vk_product__r.Unit_Measure__c
                                     from kit_item__c
                                     where Kit__c in:productMap.keySet() and vk_product__c <> null];
        List<String> lstKitProductIds = new List<String>();
        for(Kit_Item__c objKit : kitList){
            lstKitProductIds.add(objKit.vk_product__c);
        }
        Map<String,Map<String,Object>> mapProductId2Inventory = new Map<String,Map<String,Object>>();

        if(lstKitProductIds != null && lstKitProductIds.size()>0 ){
            mapProductId2Inventory = CheckInvotary
            (order.Customer_EU__c,lstKitProductIds,null,PricingDate,oData.Id);

        }
          

        Map<String, List<ProductData>> reProMap = new Map<String, List<ProductData>>();
        for (Kit_Item__c kItem : kitList){
            ProductData pritem = new ProductData();
            OrderItemData orderItem = productMap.get(kItem.Kit__c);
            pritem.Id = kItem.vk_product__c;
            pritem.KitId = kItem.Kit__c;
            pritem.ProductDescription = kItem.vk_product__r.PPC_Desp__c;
            pritem.Model = kItem.vk_product__r.Order_Model__c;
            pritem.UOM = kItem.vk_product__r.Unit_Measure__c;
            pritem.OrderDate = orderItem.OrderDate;
            pritem.RequestDate = orderItem.RequestDate;
            try{
                pritem.OrderQty = String.valueOf(Integer.valueOf((orderItem.OrderQty == null ? '0' : orderItem.OrderQty)) * (kItem.Quantity__c == null ? 0 : kItem.Quantity__c)); 
            if (reProMap.containsKey(kItem.Kit__c)){
                List<ProductData> prItemList = reProMap.get(kItem.Kit__c);
                prItemList.add(pritem);
                reProMap.put(kItem.kit__c, prItemList);
            } else{
                List<ProductData> prItemList = new List<ProductData>();
                prItemList.add(pritem);
                reProMap.put(kItem.kit__c, prItemList);
            }
            Map<String,Object> mapValue2Inventory = mapProductId2Inventory.get(kItem.VK_Product__c);
            String InventoryTools = '';
            if(mapValue2Inventory != null && mapValue2Inventory.get('CurrentStatus') ==  CCM_Constants.GREEN_LIGHT){
                //表示是可以变化的
                Decimal maxGreen = (Decimal)mapValue2Inventory.get('MaxGreenLight');
                Decimal maxYellow = (Decimal)mapValue2Inventory.get('MaxYellowLight');
                if(Integer.valueOf(pritem.OrderQty) <=  maxGreen){
                    InventoryTools = 'Green';
                }else if(Integer.valueOf(pritem.OrderQty) <=  maxYellow){
                    InventoryTools = 'Yellow';
                }else{
                    InventoryTools = 'Red';
                }


            }else if(mapValue2Inventory != null && mapValue2Inventory.get('CurrentStatus') ==  CCM_Constants.YELLOW_LIGHT){
                InventoryTools = 'Yellow';
            }else{
                InventoryTools = 'Red';
            }
            system.debug('InventoryTools---->'+InventoryTools);
            pritem.Inventory = InventoryTools;

            }catch (Exception e){
                system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            }
            

        }
        for (OrderItemData orderItem : orderItems){
            if (reProMap.containsKey(orderItem.KitId)){
                orderItem.productList = reProMap.get(orderItem.KitId);
            }
            returnOrderItems.add(orderItem) ;

        }
        //存放
        initData.OrderData = oData;
        initData.OrderItemData = returnOrderItems;
        return InitData;
    }
    //Shipment header/shipment item Mapping
    public static List<ShipmentInfo> retShipmentMappingInfo(String orderId){
        List<ShipmentInfo> shipmentInfoList = new List<ShipmentInfo>();
        List<ShipmentInfo> returnShipmentInfoList = new List<ShipmentInfo>();
        Map<String, ShipmentItemData> productMap = new Map<String, ShipmentItemData>();

        List<Shipment__c> shipments = [SELECT Id, IsDeleted, Delivery_Address__c, Delivery_City__c, Delivery_Country__c, Delivery_Postal_Code__c, Receipt_Address__c, Receipt_City__c, Receipt_Country__c, Receipt_Postal_Code__c, Ship_OracleID__c, (SELECT Id, Product__c, Shipped_Date__c, Product__r.Order_Model__c, Product__r.Item_Description_EN__c, Item_Quantity_in_Shipment__c, Ship_Set__c
                                                                                                                                                                                                                                    FROM Shipment_Items__r
                                                                                                                                                                                                                                    WHERE IsDeleted = false and Product__c <> null)
                                       FROM Shipment__c 
                                       WHERE Order__c = :orderId AND IsDeleted = false];
        for (Shipment__c shipment : shipments){
            ShipmentInfo shipmentInfo = new ShipmentInfo();
            ShipmentData shipmentData = new ShipmentData();
            shipmentData.ShipmentNumber = shipment.Ship_OracleID__c;
            AddressData shipFromAddress = new AddressData();
            shipFromAddress.PostCode = shipment.Delivery_Postal_Code__c;
            shipFromAddress.Country = shipment.Delivery_Country__c;
            shipFromAddress.City = shipment.Delivery_City__c;
            shipFromAddress.Street = shipment.Delivery_Address__c;
            shipmentData.ShipFrom = shipFromAddress;
            AddressData shipToAddress = new AddressData();
            shipToAddress.PostCode = shipment.Receipt_Postal_Code__c;
            shipToAddress.Country = shipment.Receipt_Country__c;
            shipToAddress.City = shipment.Receipt_City__c;
            shipToAddress.Street = shipment.Receipt_Address__c;
            shipmentData.ShipTo = shipToAddress;
            shipmentInfo.shipmentData = shipmentData;
            //Shipment Items
            if (shipment.Shipment_Items__r != null && shipment.Shipment_Items__r.size() > 0){
                List<ShipmentItemData> shipmentItemDataList = new List<ShipmentItemData>();
                for (Shipment_Item__c shipmentItem : shipment.Shipment_Items__r){
                    ShipmentItemData shipmentItemData = new ShipmentItemData();
                    shipmentItemData.Id = shipmentItem.Id;
                    shipmentItemData.ShippedDate = String.valueOf(shipmentItem.Shipped_Date__c);
                    shipmentItemData.kitId = shipmentItem.Product__c;
                    shipmentItemData.ProductDescription = shipmentItem.Product__r.Item_Description_EN__c;
                    shipmentItemData.Model = shipmentItem.Product__r.Order_Model__c;
                    shipmentItemData.ItemQtyInShipment = String.valueOf(shipmentItem.Item_Quantity_in_Shipment__c);
                    shipmentItemData.ShipSet = shipmentItem.Ship_Set__c;
                    shipmentItemDataList.add(shipmentItemData);
                    productMap.put(shipmentItemData.kitId, shipmentItemData);
                }
                shipmentInfo.ShipmentItems = shipmentItemDataList;
            }
            shipmentInfoList.add(shipmentInfo);

        }
        //查询kit item
        List<Kit_Item__c> kitList = [SELECT id, Kit__c, vk_product__c, vk_product__r.PPC_Desp__c, vk_product__r.Order_Model__c, vk_product__r.Unit_Measure__c
                                     from kit_item__c
                                     where Kit__c in:productMap.keySet() and vk_product__c <> null];
        Map<String, List<ProductData>> reProMap = new Map<String, List<ProductData>>();
        for (Kit_Item__c kItem : kitList){
            ProductData pritem = new ProductData();
            ShipmentItemData shipItem = productMap.get(kItem.Kit__c);
            pritem.Id = kItem.vk_product__c;
            pritem.KitId = kItem.Kit__c;
            pritem.ProductDescription = kItem.vk_product__r.PPC_Desp__c;
            pritem.Model = kItem.vk_product__r.Order_Model__c;
            pritem.UOM = kItem.vk_product__r.Unit_Measure__c;
            pritem.OrderQty = shipItem.ItemQtyInShipment;
            if (reProMap.containsKey(kItem.Kit__c)){
                List<ProductData> prItemList = reProMap.get(kItem.Kit__c);
                prItemList.add(pritem);
                reProMap.put(kItem.kit__c, prItemList);
            } else{
                List<ProductData> prItemList = new List<ProductData>();
                prItemList.add(pritem);
                reProMap.put(kItem.kit__c, prItemList);
            }
        }
        for (ShipmentInfo shipInfo : shipmentInfoList){
            List<ShipmentItemData> shipItemList = new List<ShipmentItemData>();
            if(shipInfo.ShipmentItems != null) {
                for (ShipmentItemData shipItem : shipInfo.ShipmentItems){
                    if (reProMap.containsKey(shipItem.KitId)){
                        shipItem.productList = reProMap.get(shipItem.KitId);
                    }
                    shipItemList.add(shipItem);
                }
                shipInfo.ShipmentItems = shipItemList;
                returnShipmentInfoList.add(shipInfo);
            }
        }
        return returnShipmentInfoList;
    }
    public static List<AttachMentData> retAttachmentInfo(String orderId){
        List<AttachMentData> attachMentList = new List<AttachMentData>();
        //get receipt
        List<Invoice__c> contentList = [SELECT Id, CreatedDate, Invoice_Number__c, RecordType.Name
                                        FROM Invoice__c 
                                        WHERE IsDeleted = false AND Order__c = :orderId];
        if (contentList.size() > 0){
            for (Invoice__c item : contentList){
                AttachMentData data = new AttachMentData();
                data.Id = item.Id;
                data.AttachMentDate = String.ValueOf(item.CreatedDate);
                data.AttachmentName = item.Invoice_Number__c;
                data.AttachmentType = item.RecordType.Name;
                attachMentList.add(data);
            }

        }
        return attachMentList;
    }
    /**
     * Author: Honey
     * Date: 2023/0801
     * 新增红绿灯刷新接口
     */
    @AuraEnabled
    public static void refreshInventory(String OrderId){
        system.debug('lstOrderItemId--->'+OrderId);
        try {
            //---->查询OrderItem
            List<Order_Item__c> lstOrderItem = [
                SELECT Id,Order__c,Order__r.Customer_EU__c,Product__c,Pricing_Date__c,Inventory__c,Order_Quantity__c
                FROM Order_Item__c  WHERE Order__c = :OrderId
            ];
            Map<String,Decimal> mapProductId2Qty = new Map<String,Decimal>();
            String customerId = '';
            List<String> lstProductId = new List<String>();
            Date pricingDate;
            for(Order_Item__c objOrderItem : lstOrderItem){
                mapProductId2Qty.put(objOrderItem.Product__c, objOrderItem.Order_Quantity__c);
                customerId = objOrderItem.Order__r.Customer_EU__c;
                lstProductId.add(objOrderItem.Product__c);
                pricingDate = objOrderItem.Pricing_Date__c;
            }
            system.debug('进入前Csutoemr--->'+customerId);
            Map<String,Map<String,Object>> mapProductId2Inventory 
                =  CheckInvotary(customerId,lstProductId,mapProductId2Qty,pricingDate,OrderId);
            for(Order_Item__c objOrderItem : lstOrderItem){
                Map<String,Object> mapFeild2Status = new Map<String,Object>();
                if(mapProductId2Inventory != null ){
                    Map<String,Object> mapValue2Inventory = mapProductId2Inventory.get(objOrderItem.Product__c);
                    mapFeild2Status = mapProductId2Inventory.get(objOrderItem.Product__c);
                    String InventoryTools = '';
                    if(mapValue2Inventory.get('CurrentStatus') ==  CCM_Constants.GREEN_LIGHT){
                    //表示是可以变化的
                    Decimal maxGreen = (Decimal)mapValue2Inventory.get('MaxGreenLight');
                    Decimal maxYellow = (Decimal)mapValue2Inventory.get('MaxYellowLight');
                    if(Integer.valueOf(objOrderItem.Order_Quantity__c)  <=  maxGreen){
                        InventoryTools = 'Green';
                    }else if(Integer.valueOf(objOrderItem.Order_Quantity__c) <=  maxYellow){
                        InventoryTools = 'Yellow';
                    }else{
                        InventoryTools = 'Red';
                    }
                }else if(mapValue2Inventory.get('CurrentStatus') ==  CCM_Constants.YELLOW_LIGHT){
                    InventoryTools = 'Yellow';
                }else{
                    InventoryTools = 'Red';
                }
                objOrderItem.Inventory__c = InventoryTools;

                    }
                
        }
        update lstOrderItem;
            
        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数----->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
      
    }
    /**
     * 
     * Author : Honey
     * Date : 2023/08/01
     * Description: 查询红绿灯状态--->返回Id到红绿灯映射
     */
    public static Map<String,Map<String,Object>> CheckInvotary(String CustomerId,List<String> lstProductId,Map<String,Decimal> mapProduct2Qty,Date PricingDate,String OrderId){
        Map<String,Map<String,Object>> mapFeild2ValueReturn = new Map<String,Map<String,Object>>();
        system.debug('customerId---->'+CustomerId);
        try{
            String LightInfo = null;
        
        String RecordName = '';
        String wareHouse = '';
        if(String.isNotBlank(OrderId)){
            //根据PruchaseOrderId查询仓库和RecordType
            Order objOrder = [
                SELECT warehouse__c,RecordTypeId,RecordType.Name FROM Order WHERE Id = :OrderId
            ];
            RecordName = objOrder.RecordType.Name;
            wareHouse = objOrder.Warehouse__c;
        }else{
            system.debug('进入设置默认值');
            RecordName = CCM_Constants.REGULAR_ORDER;
            wareHouse = CCM_Constants.GERMANY;
        }
        system.debug('lstProductId---->'+lstProductId);
        //批量查询Invotory总库
        List<Inventory__c> lstInventory = [
            SELECT Id,Product__c,Available_QTY__c FROM Inventory__c WHERE Product__c IN :lstProductId 
            AND RecordType.Name = 'All Inventory' AND Sub_Inventory_Code__c = 'EGD01'
        ];
        system.debug('lstInventory-->'+lstInventory);
        Map<String,Decimal> mapProduct2Invotory = new Map<String,Decimal>();
        for(Inventory__c objInvotory : lstInventory){
            mapProduct2Invotory.put(objInvotory.Product__c,objInvotory.Available_QTY__c);
        }
        system.debug('mapProduct2Invotory-->'+mapProduct2Invotory);
        //通过CustomerID查询字库名字
        Account objAccount = [
            SELECT Id,Planning_Territory_No__c,Planning_Territory_No__r.Name FROM Account WHERE Id = :CustomerId
        ];
        //获取年月
        Integer Year = PricingDate.year();
        Integer Month = PricingDate.month();
        //根据仓库名字、产品、时间唯一确认一个planning Tool
        List<Planning_Tool__c> lstPlanningTool = [
            SELECT Id,Actual_QTY__c,Product__c,Current_Month__c,Supply_Year__c
            FROM Planning_Tool__c WHERE Product__c IN :lstProductId AND Current_Month__c = :Month AND Supply_Year__c = :Year AND Territory_NO__c = :objAccount.Planning_Territory_No__c
        ];
        system.debug('lstPlanningTool-->'+lstPlanningTool);
        Map<String,Decimal> mapProductId2Tools = new Map<String,Decimal>();
        for(Planning_Tool__c objPlanningTool : lstPlanningTool){
            mapProductId2Tools.put(objPlanningTool.Product__c,objPlanningTool.Actual_QTY__c);
        }
        system.debug('mapProductId2Tools-->'+mapProductId2Tools);
        //对于同一个产品
        for(String ProductId : lstProductId){
  
            Decimal PlanningToolQty = mapProductId2Tools.get(ProductId) == null ? 0 : mapProductId2Tools.get(ProductId);
            Decimal AllProductQty = mapProduct2Invotory.get(ProductId)== null ? 0 : mapProduct2Invotory.get(ProductId);
            
            Decimal MaxGreenLight = 0;
            Decimal MaxYellowLight = 0;
            Map<String,Object> mapFeild2Status = new Map<String,Object>();
            system.debug('RecordName计算红绿灯--->'+RecordName);
            system.debug('wareHouse计算红绿灯--->'+wareHouse);
            system.debug('PlanningToolQty--->'+PlanningToolQty);
            system.debug('AllProductQty--->'+AllProductQty);
            if(CCM_Constants.CHINA.equals(wareHouse) || CCM_Constants.PRE_SEASON_ORDER.equals(RecordName)){
                    //中国仓库或者预订单都为黄灯
                    LightInfo =  CCM_Constants.YELLOW_LIGHT;
            }else{
                if(PlanningToolQty > 0 && AllProductQty >0){
                    //两个最小值
                    MaxGreenLight = PlanningToolQty < AllProductQty ? PlanningToolQty : AllProductQty;
                    MaxYellowLight = PlanningToolQty > AllProductQty ? PlanningToolQty : AllProductQty;
                    LightInfo =  CCM_Constants.GREEN_LIGHT;
                }else if(PlanningToolQty <= 0 && AllProductQty <= 0){
                    system.debug('返回红灯');
                    LightInfo = CCM_Constants.RED_LIGHT;
                }else{
                    //两个最大值
                    MaxYellowLight = PlanningToolQty > AllProductQty ? PlanningToolQty : AllProductQty;
                    MaxGreenLight = PlanningToolQty < AllProductQty ? PlanningToolQty : AllProductQty;
                    LightInfo =  CCM_Constants.GREEN_LIGHT;
                }
                
            }
            mapFeild2Status.put('CurrentStatus', LightInfo);
            mapFeild2Status.put('MaxYellowLight', MaxYellowLight);
            mapFeild2Status.put('MaxGreenLight', MaxGreenLight);
            mapFeild2ValueReturn.put(ProductId,mapFeild2Status);
        }

        }catch(Exception e){
            system.debug('报错信息---->'+e.getMessage()+'报错行数----->'+e.getLineNumber());
        }
        
        
        return mapFeild2ValueReturn;
        
                
    }

    // add by haibo
    @AuraEnabled
    public static String queryAlertMessage(String CustomerNumber,String AlertMode){
        return CCM_PurchaseOrderPreview.queryAlertMessage(CustomerNumber, AlertMode);
    }

    
    //Order Header
    public class OrderData{
        public String Id;
        public String PONumber;
        public String remark;
        public String currencyCode;
        public String Source;
        public String Customer;

        public String CustomerNumber;

        public String CustomerPO;

        public String FreightTerm;

        public String PaymentTerm;

        public String IncoTerm;

        public String Salesperson;

        public String OrderDate;

        public String OrderCreatedinEBS;

        public String OrderNumber;

        public String OrderStatus;
        public String createdBy;

        public String OrderType;

        public String Warehouse;

        public String ExpectedDeliveryDate;

        public String ShippingPlace;

        public String PricingDate;

        public String PriceList;

        public String EstWeight;

        public String EstVolume;

        public String IsDropshipOrder;

        //Delivery Information
        public AddressData BillToAddress;

        public AddressData ShipToAddress;

        public AddressData DropshipAddress;

        public String DropshipType;

        //At bottom right corner
        public String TotalValue;

        public String HeaderDiscount;

        public String HeaderDiscountAmount;

        public String TotalValueNet;

        public String FreightCost;

        public String InsuranceFee;

        public String OtherFee;

        public String VAT;

        public String TotalDueAmount;

        public String comments;

    }
    //Order Item
    public class OrderItemData{
        public String Id;

        public String KitId;

        public String currencyCode;

        public String ProductDescription;

        public String Model;

        public String UOM;

        public String OrderDate;

        public String RequestDate;
        public String deliveryDate;
        public String OrderQty;

        public String Inventory;
        public String BackOrderQty;

        public String ReleasetoWarehouseQty;

        public String PickedPackedQty;

        public String ShippedQty;

        public String InvoicedQty;

        public String CancelledQty;

        public String ReturnedQty;

        public String EstReplenishDate;

        public String ScheduleShipDate;

        public String PricingDate;

        public String ListPrice;

        public String Discount;

        public String UnitNetPrice;

        public String TotalNetPrice;

        public String Remark;

        public String SalesAgreementNo;
        //begin add by vince 20240221
        public String OrderLineStatus;
        //end add by vince 20240221
        // add haibo (获取product record type -- Product__r.RecordType_Name__c)
        public String productRecordType;
        public List<ProductData> ProductList;

    }
    public class ProductData{
        public String Id;

        public String KitId;

        public String ProductDescription;

        public String Model;

        public String UOM;

        public String OrderDate;

        public String RequestDate;

        public String OrderQty;

        public String Inventory;

        public String ListPrice;

        public String UnitNetPrice;

        public String TotalNetPrice;
        public ProductData(){
            this.ListPrice = '0';
            this.UnitNetPrice = '0';
            this.TotalNetPrice = '0';
            this.Inventory = '';
        }
    }
    //Shipment Header
    public class ShipmentInfo{
        public ShipmentData ShipmentData;

        public List<ShipmentItemData> ShipmentItems;

    }
    public class ShipmentData{
        public AddressData ShipFrom;

        public AddressData ShipTo;

        public String ShipmentNumber;

    }
    public class AddressData{
        public String Country;

        public String City;

        public String Street;

        public String PostCode;

        public String CompanyName;

    }
    public class ShipmentItemData{
        public String ProductDescription;

        public String Model;

        public String ItemQtyInShipment;

        public String ShipSet;

        public String Id;

        public String KitId;

        public String ShippedDate;

        public List<ProductData> ProductList;

    }
    // AttachMent
    public class AttachMentData{
        public String Id;

        public String AttachmentName;

        public String AttachmentType;

        public String AttachMentDate;

    }
    public class InitData{
        public OrderData OrderData;

        public List<OrderItemData> OrderItemData;

        public List<ShipmentInfo> ShipmentInfo;

        public List<AttachMentData> AttachMentData;

        public Integer CurrentStep;

        public String UserType;

        public Boolean isSuccess;

        public String errorMsg;

        /**
         * @description This property is used for fetching Purchase Order attachments.
         */
        public Id attachmentSourceId;
        public InitData(){
            this.OrderData = new OrderData();
            this.OrderItemData = new List<OrderItemData>();
            this.ShipmentInfo = new List<ShipmentInfo>();
            this.AttachMentData = new List<AttachMentData>();
            this.currentStep = 1;
            this.isSuccess = false;
            this.errorMsg = '';
        }
    }
}