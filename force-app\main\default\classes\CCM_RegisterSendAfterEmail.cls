/**
 * Honey
 * 结束后发送邮件
 */
public without sharing class CCM_RegisterSendAfterEmail {
    public CCM_RegisterSendAfterEmail() {

    }
     //后三天发送邮件到相关用户方法必须返回一个  List  类型的结果。 
    @InvocableMethod 
    public static void sendEditEmailToAfterUser(List<String> lstRecordId){
        try {
            //审核通过发送邮件到创建人
            List<EmailTemplate> lstEmailTemplate = new List<EmailTemplate>();
            //不同语言不同邮件内容
            //判断当前语言
            String userLanguage = UserInfo.getLanguage();
            String EmailName = '';
            if(userLanguage == CCM_Constants.DE){
                EmailName = 'Training After Email DE';
            }else{
                EmailName = 'Training After Email';
            }
            lstEmailTemplate = [
                Select e.Id, e.Name,Subject,Body,HtmlValue from EmailTemplate e where Name  = :EmailName
            ];
             
            if(lstEmailTemplate != null && lstEmailTemplate.size()>0){
                String templateId = lstEmailTemplate[0].Id;
                //通过recordId查询Register的创建人
                List<Course_Register__c> lstCourse = new List<Course_Register__c>();
                lstCourse = [
                   SELECT Id,CreatedById,CreatedBy.Email,CreatedBy.IsActive,Status__c,Customer__c,
                        Customer__r.Name,Customer__r.AccountNumber,Billing_Address__c,Billing_Address__r.Final_Address__c,
                        Course_Arrangement__c,Training_Course__r.Course_Name__c,Training_Course_Name__c,
                        Training_Course__c,Course_Arrangement__r.Course_Date__c,Course_Arrangement__r.End_Time__c,
                        Course_Arrangement__r.Survery__c,Course_Arrangement__r.Training_Location__r.name,
                        Shiping_Address__c,
                        Billing_Address__r.Acknowledgement_Customer__c,
                        Billing_Address__r.Acknowledgement_Customer__r.Email,
                        ToLabel(Billing_Address__r.Acknowledgement_Customer__r.Salutation),
                        Shiping_Address__r.Acknowledgement_Customer__c,
                        Shiping_Address__r.Acknowledgement_Customer__r.Email,
                        ToLabel(Shiping_Address__r.Acknowledgement_Customer__r.Salutation),
                        Trainee__c
                     FROM Course_Register__c
                   WHERE CreatedBy.IsActive = TRUE AND Id IN :lstRecordId
                ];
                //通过recordId查询register Item
                List<Course_Register_Item__c>  lstRegister = [
                    SELECT c.Course_Register__c, c.email__c, c.Id, c.Name,Trainee__c FROM Course_Register_Item__c c 
                    WHERE  Course_Register__c IN : lstRecordId
                ];
                Map<String,List<Course_Register_Item__c>> mapRegisterId2Item = new Map<String,List<Course_Register_Item__c>>();
                for(Course_Register_Item__c objItem : lstRegister){
                    List<Course_Register_Item__c> lstRegisterItem = mapRegisterId2Item.containsKey(objItem.Course_Register__c) ?
                    mapRegisterId2Item.get(objItem.Course_Register__c) : new List<Course_Register_Item__c>();
                    lstRegisterItem.add(objItem);
                    mapRegisterId2Item.put(objItem.Course_Register__c,lstRegisterItem);
                }
               
                List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
                List<Messaging.SingleEmailMessage> lstemails = new List<Messaging.SingleEmailMessage>();
                for(Course_Register__c objCourse : lstCourse){
                    // Course_Arrangement__r.Survery__c 判空
                    if(objCourse.Course_Arrangement__r.Survery__c == null){
                        break ;
                    }

                    List<String> lstReceiptEmail = new List<String>();
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    //邮件内容设定
                    email.setSubject(lstEmailTemplate[0].Subject);
                    String strBody = lstEmailTemplate[0].HtmlValue;
                    strBody = strBody.replace('{Training Course Name}', objCourse.Training_Course_Name__c);
                    strBody = strBody.replace('{Survery}', objCourse.Course_Arrangement__r.Survery__c == null ? '' : objCourse.Course_Arrangement__r.Survery__c);
                    List<Course_Register_Item__c> lstRegisterItem = mapRegisterId2Item.containsKey(objCourse.Id) ?
                    mapRegisterId2Item.get(objCourse.Id) : new List<Course_Register_Item__c>();
                    if(lstRegisterItem.size() == 0 ){
                        break;
                    }else{
                        for(Course_Register_Item__c objItem : lstRegisterItem){
                            lstReceiptEmail.add(objItem.email__c);
                        }
                    }
                    String gender = objCourse.Billing_Address__r.Acknowledgement_Customer__c == null ? 
                    objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Salutation : 
                    objCourse.Billing_Address__r.Acknowledgement_Customer__r.Salutation;
                    strBody = strBody.replace('{Family Name}', gender == null ? '': gender);
                    email.setTargetObjectId(UserInfo.getUserId());
                    email.setHtmlBody(strBody);
              
                    if(lstReceiptEmail == null || lstReceiptEmail.size() == 0){
                        break ;
                    }
                  
                    system.debug('send Email--->'+lstReceiptEmail);
                    email.setToAddresses(lstReceiptEmail);
                    // email.setCcAddresses(new List<String>{'<EMAIL>'});
                    //设置发件人
               
                    if(listAddresses != null  && listAddresses.size() > 0){
                        email.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }
                    email.setSaveAsActivity(false);
                    
                    lstemails.add(email);
                }
                //发送邮件
                if (!System.Test.isRunningTest()){
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(lstemails);
                }
            }
        } catch (Exception e) {
            system.debug('报错信息－－－＞'+e.getMessage()+'报错行数－－－－'+e.getLineNumber());
        }
    }
}