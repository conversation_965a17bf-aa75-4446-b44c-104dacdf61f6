<aura:component description="CCM_UserRegistration" extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout" access="global" 
                controller="CCM_ServiceHome">
    
    <!-- <aura:attribute type="CCM_UserRegistrationController.Parameter" name="params" /> -->
    <!--  Company info -->
    <aura:attribute name="company" type="String" default=""/>
    <aura:attribute name="businessArea" type="String" default=""/>
    <aura:attribute name="address" type="String" default=""/>
    <aura:attribute name="streetNo" type="String" default=""/>
    <aura:attribute name="city" type="String" default=""/>
    <aura:attribute name="state" type="String" default=""/>
    <aura:attribute name="country" type="String" default=""/>
    <aura:attribute name="postCode" type="String" default=""/>
    <aura:attribute name="status" type="String" default=""/>
    <aura:attribute name="dueDate" type="String" default=""/>
    <aura:attribute name="emailAddress" type="String" default=""/>
    <aura:attribute name="phone" type="String" default=""/>
    <aura:attribute name="firstName" type="String" default=""/>
    <aura:attribute name="lastName" type="String" default=""/>
    <aura:attribute name="fleetManager" type="String" default=""/>
    <aura:attribute name="fleetManagerEmail" type="String" default=""/>

    <!-- 表格参数 -->
    <aura:attribute name="commercialViewColumns" type="List" default="[]"/>
    <aura:attribute name="commercialViewCurrent" type="List" default="[]"/>
    <aura:attribute name="residentialEditColumns" type="List" default="[]"/>
    <aura:attribute name="residentialEditCurrent" type="List" default="[]"/>
    <aura:attribute name="residentialViewColumns" type="List" default="[]"/>
    <aura:attribute name="residentialViewCurrent" type="List" default="[]"/>

    <aura:attribute name="userType" type="String" default=""/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="actionType" type="String" default=""/>
    
    <aura:attribute name="showType" type="String" default="commercialView"/>
    <aura:attribute name="checkEmail" type="Boolean" default="true"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>

    <aura:handler name="init" value="{!this}" action="{!c.init}"/>

    <section class="slds-p-around_x-small">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed loading-wrap' : 'slds-hide' }"/>
        <!-- company info Commercial User view Page-->
        <aura:if isTrue="{!v.showType == 'commercialView'}">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_CompanyInformation}">
                                        <span><strong>{!$Label.c.CCM_CompanyInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 company-form-wrap">
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="company" label="{!$Label.c.CCM_Company}" value="{!v.company}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="address" label="{!$Label.c.CCM_Address}" value="{!v.address}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="City" label="{!$Label.c.CCM_City}" value="{!v.city}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="country" label="{!$Label.c.CCM_Country}" value="{!v.country}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="fleetManager" label="{!$Label.c.CCM_FleetManager}" value="{!v.fleetManager}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="businessArea" label="{!$Label.c.CCM_BusinessArea}" value="{!v.businessArea}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="Street" label="{!$Label.c.CCM_StreetNo}" value="{!v.streetNo}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="State" label="{!$Label.c.CCM_StateProvince}" value="{!v.state}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="postCode" label="{!$Label.c.CCM_PostCode}" value="{!v.postCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="fleetManagerEmail" label="{!$Label.c.CCM_FleetManagerEmail}" value="{!v.fleetManagerEmail}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            <!-- product info -->
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_ProductInformation}">
                                        <span><strong>{!$Label.c.CCM_ProductInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_DataTable columns="{!v.commercialViewColumns}" data="{!v.commercialViewCurrent}" />
                </div>
            </article>
            <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
                <!-- <lightning:button class="" variant="brand"  label="Registration" title="Registration" onclick="{!c.handlerRegistration}"/> -->
                <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.handlerCancel}"/>
            </div>
        </aura:if>
        <!-- company info Commercial User Edit Page-->
        <aura:if isTrue="{!v.showType == 'commercialEdit'}">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_CompanyInformation}">
                                        <span><strong>{!$Label.c.CCM_CompanyInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 company-form-wrap">
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="company" label="{!$Label.c.CCM_Company}" value="{!v.company}" onblur="{!c.getCompany}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="address" label="{!$Label.c.CCM_Address}" value="{!v.address}" onblur="{!c.getAddress}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="city" label="{!$Label.c.CCM_City}" value="{!v.city}" onblur="{!c.getCity}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="country" label="{!$Label.c.CCM_Country}" value="{!v.country}" onblur="{!c.getCountry}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="fleetMananger" label="{!$Label.c.CCM_Status}" value="{!v.status}" onblur="{!c.getFleetMananger}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="accountCode" label="{!$Label.c.CCM_BusinessArea}" value="{!v.businessArea}" onblur="{!c.getAccountCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="street" label="{!$Label.c.CCM_StreetNo}" value="{!v.streetNo}" onblur="{!c.getStreet}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="stateProvince" label="{!$Label.c.CCM_StateProvince}" value="{!v.state}" onblur="{!c.getStateProvince}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="postCode" label="{!$Label.c.CCM_PostCode}" value="{!v.postCode}" onblur="{!c.getPostCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="dueDate" label="{!$Label.c.CCM_DueDate}" value="{!v.dueDate}" onblur="{!c.getPostCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            <!-- personal info -->
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_PersonalInformation}">
                                        <span><strong>{!$Label.c.CCM_PersonalInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 company-form-wrap">
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="company" label="{!$Label.c.CCM_EmailAddress}" value="{!v.emailAddress}" onblur="{!c.changeEmail}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="address" label="{!$Label.c.CCM_FirstName}" value="{!v.firstName}" onblur="{!c.getAddress}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="accountCode" label="{!$Label.c.CCM_Phone}" value="{!v.phone}" onblur="{!c.getAccountCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="street" label="{!$Label.c.CCM_LastName}" value="{!v.lastName}" onblur="{!c.getStreet}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
                <lightning:button class="" variant="brand"  label="{!$Label.c.CCM_Submit}" title="{!$Label.c.CCM_Submit}" onclick="{!c.handlerSubmit}"/>
                <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.handlerCancel}"/>
            </div>
        </aura:if>
        <!-- company info Residential User view Page-->
        <aura:if isTrue="{!v.showType == 'residentialView'}">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_CompanyInformation}">
                                        <span><strong>{!$Label.c.CCM_CompanyInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 company-form-wrap">
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="Email Address" label="{!$Label.c.CCM_EmailAddress}" value="{!v.emailAddress}" onblur="{!c.getCompany}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="First Name" label="{!$Label.c.CCM_FirstName}" value="{!v.firstName}" onblur="{!c.getAddress}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="city" label="{!$Label.c.CCM_LastName}" value="{!v.lastName}" onblur="{!c.getCity}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="country" label="{!$Label.c.CCM_Country}" value="{!v.country}" onblur="{!c.getCountry}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="country" label="{!$Label.c.CCM_PostCode}" value="{!v.postCode}" onblur="{!c.getCountry}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="accountCode" label="{!$Label.c.CCM_Phone}" value="{!v.phone}" onblur="{!c.getAccountCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="street" label="{!$Label.c.CCM_Address}" value="{!v.address}" onblur="{!c.getStreet}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="stateProvince" label="{!$Label.c.CCM_StreetNo}" value="{!v.streetNo}" onblur="{!c.getStateProvince}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="stateProvince" label="{!$Label.c.CCM_City}" value="{!v.city}" onblur="{!c.getStateProvince}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="stateProvince" label="{!$Label.c.CCM_StateProvince}" value="{!v.state}" onblur="{!c.getStateProvince}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            <!-- product info -->
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_ProductInformation}">
                                        <span><strong>{!$Label.c.CCM_ProductInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_DataTable columns="{!v.residentialViewColumns}" data="{!v.residentialViewCurrent}" />
                </div>
            </article>
            <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
                <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.handlerCancel}"/>
            </div>
        </aura:if>
        <!-- company info Residential User Edit Page-->
        <aura:if isTrue="{!v.showType == 'residentialEdit'}">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_CompanyInformation}">
                                        <span><strong>{!$Label.c.CCM_CompanyInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80 company-form-wrap">
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="Email Address" label="{!$Label.c.CCM_EmailAddress}" value="{!v.emailAddress}" onblur="{!c.changeEmail}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="First Name" label="{!$Label.c.CCM_FirstName}" value="{!v.firstName}" onblur="{!c.getAddress}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"  disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="city" label="{!$Label.c.CCM_LastName}" value="{!v.lastName}" onblur="{!c.getCity}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"  disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="country" label="{!$Label.c.CCM_Country}" value="{!v.country}" onblur="{!c.getCountry}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"  disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="country" label="{!$Label.c.CCM_PostCode}" value="{!v.postCode}" onblur="{!c.getCountry}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"  disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="fleetMananger" label="{!$Label.c.CCM_Status}" value="{!v.status}" onblur="{!c.getFleetMananger}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"  disabled="true"/>
                            </div>
                        </div>
                        <div class="slds-text-align--right">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="accountCode" label="{!$Label.c.CCM_Phone}" value="{!v.phone}" onblur="{!c.getAccountCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="street" label="{!$Label.c.CCM_Address}" value="{!v.address}" onblur="{!c.getStreet}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="stateProvince" label="{!$Label.c.CCM_StreetNo}" value="{!v.streetNo}" onblur="{!c.getStateProvince}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="stateProvince" label="{!$Label.c.CCM_City}" value="{!v.city}" onblur="{!c.getStateProvince}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="stateProvince" label="{!$Label.c.CCM_StateProvince}" value="{!v.state}" onblur="{!c.getStateProvince}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <lightning:input aura:id="postCode" label="{!$Label.c.CCM_DueDate}" value="{!v.dueDate}" onblur="{!c.getPostCode}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255" disabled="true"/>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            <!-- product info -->
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="{!$Label.c.CCM_ProductInformation}">
                                        <span><strong>{!$Label.c.CCM_ProductInformation}</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_DataTable columns="{!v.residentialEditColumns}" data="{!v.residentialEditCurrent}" />
                </div>
            </article>
            <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
                <lightning:button class="" variant="brand"  label="{!$Label.c.CCM_Submit}" title="{!$Label.c.CCM_Submit}" onclick="{!c.handlerSubmit}"/>
                <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.handlerCancel}"/>
            </div>
        </aura:if>
    </section>
</aura:component>