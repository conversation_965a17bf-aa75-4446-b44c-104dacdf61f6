/**
Author : Honey
Date : 2023/07/11
Description: 测试类
*/
@isTest
public class CCM_ProductRegistrationTest {
     @isTest
    static void testSetupProductRegistration() {
        // 创建测试数据
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        Contact contact = new Contact(
            FirstName = 'Test',
            LastName = 'Contact',
            Email = '<EMAIL>',
            Role__c = 'Fleet Manager',
            AccountId = acc.Id
        );
        insert contact;
      

        Profile profile1 = [Select Id
                                from Profile
                                where name = 'Partner Community User'];        
        User testUser = new User(Alias = 'standt', Email='<EMAIL>', 
            EmailEncodingKey='UTF-8', LastName='Testing1', LanguageLocaleKey='en_US', 
            LocaleSidKey='en_US', ProfileId = profile1.Id, 
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>',
            ContactId = contact.Id);
        insert testUser;
        // 调用被测试的方法
        Test.startTest();
        
        system.runAs(testUser){
            String result = CCM_ProductRegistration.setupProductRegistration(UserInfo.getUserId(), acc.Id);
            
             // 验证结果
        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        system.debug('resultMap-->'+resultMap);
        System.assertEquals(acc.Name, resultMap.get('comapnyName'));
        }
        
        Test.stopTest();
    }
    
    
    @isTest
    static void testCheckEmail() {
        // Create test data
        RecordType objRecordTYpe = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Residential_Consumer' LIMIT 1];
        system.debug('objRecordTYpe-->'+objRecordTYpe);
        Account acc = new Account(
            FirstName = 'John',
            LastName = 'Doe',
            PersonEmail = '<EMAIL>',
            Dealer_View__c = 'Y',
            Consumer_Status__c = 'Waiting Customer Approval',
            RecordTypeId = objRecordTYpe.Id
        );
        insert acc;
         Account acc2 = new Account(
            FirstName = 'John',
            LastName = 'Doe',
            PersonEmail = '<EMAIL>',
            Dealer_View__c = 'N',
            Consumer_Status__c = 'Active',
            RecordTypeId = objRecordTYpe.Id
        );
        insert acc2;
        
         Account acc3 = new Account(
            FirstName = 'John',
            LastName = 'Doe',
            PersonEmail = '<EMAIL>',
            Dealer_View__c = 'y',
            Consumer_Status__c = 'Active',
            RecordTypeId = objRecordTYpe.Id
        );
        insert acc3;
        
        
        // Call the method being tested
        Test.startTest();
        Map<String, Object> result = CCM_ProductRegistration.checkEmail('<EMAIL>');
        
        Map<String, Object> result2 = CCM_ProductRegistration.checkEmail('<EMAIL>');
        
        Map<String, Object> result3 = CCM_ProductRegistration.checkEmail('<EMAIL>');
        
        Map<String, Object> result4 = CCM_ProductRegistration.checkEmail('<EMAIL>');
        Test.stopTest();
        
        // Verify the result
        // System.assertNotEquals ('Fail', result);
    }
    
    @isTest
    static void testMasterModelNumber() {
        // Create test data
        String filterStr = 'test';
        String brandName = 'EGO';
        RecordTYpe objRecoreType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Product2' AND DeveloperName IN ('TLS_Kit', 'TLS_Product') LIMIT 1];
        // Create a test product
        Product2 testProduct = new Product2(
            Name = 'Test Product',
            ProductCode = 'TEST001',
            IsActive = true,
            Brand_Name__c = brandName,
            RecordTypeId = objRecoreType.Id
        );
        insert testProduct;
        
        // Call the method being tested
        Test.startTest();
        String result = CCM_ProductRegistration.masterModelNumber(filterStr, brandName);
        Test.stopTest();
        
       system.assertNotEquals(null, result);
    }

    @isTest
    static void testAddWarrantyItem() {
        // Create test data
        String email = '<EMAIL>';
        Date purchaseDate = Date.today().addDays(1);
        String brandName = 'Test Brand';
        String masterModelNo = 'Test Master Model';
        String masterModelId = 'Test Master Model Id';
        String purchasePlace = 'Test Purchase Place';
        
        RecordType objRecordType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Residential_Consumer' LIMIT 1];
        // Create a test account
        Account testAccount = new Account(
            PersonEmail = email,
            LastName = 'Honey',
            Consumer_Status__c = 'Waiting Customer Approval',
            recordTypeId = objRecordType.Id
        );
        insert testAccount;
        
        // 创建测试数据
        
        // 创建关联的产品
        Product2 testProduct1 = new Product2(
            Name = 'Test Product',
            IsActive = true
        );
        insert testProduct1;
        Product2 testProduct2 = new Product2(
            Name = 'Test Product2',
            IsActive = true
        );
        insert testProduct2;
        RecordType objrecoesType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Kit_Item__c' AND DeveloperName = 'Kits_and_Products' LIMIT 1];
        // 创建关联的Kit Item
        Kit_Item__c testKitItem = new Kit_Item__c(
            Kit__c = testProduct2.Id,
            Product__c = testProduct1.Id,
            recordTypeId = objrecoesType.Id
        );
        insert testKitItem;
        masterModelId = testProduct2.Id;
        // Call the method being tested
        Test.startTest();
        String result = CCM_ProductRegistration.addWarrantyItem(email, purchaseDate, brandName, masterModelNo, masterModelId, purchasePlace);
        system.assertNotEquals(null, result);
        Test.stopTest();

    }
      @isTest
    static void testAddWarrantyItem2() {
        // Create test data
        String email = '<EMAIL>';
        Date purchaseDate = Date.today().addDays(1);
        String brandName = 'Test Brand';
        String masterModelNo = 'Test Master Model';
        String masterModelId = 'Test Master Model Id';
        String purchasePlace = 'Test Purchase Place';
        RecordType objRecordType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Residential_Consumer' LIMIT 1];
        // Create a test account
        Account testAccount = new Account(
            PersonEmail = email,
            LastName = 'Honey',
            Consumer_Status__c = 'Active',
            recordTypeId = objRecordType.Id
        );
        insert testAccount;
        
        // 创建测试数据
        
        // 创建关联的产品
        Product2 testProduct1 = new Product2(
            Name = 'Test Product',
            IsActive = true
        );
        insert testProduct1;
        Product2 testProduct2 = new Product2(
            Name = 'Test Product2',
            IsActive = true
        );
        insert testProduct2;
        RecordType objrecoesType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Kit_Item__c' AND DeveloperName = 'Kits_and_Products' LIMIT 1];
        // 创建关联的Kit Item
        Kit_Item__c testKitItem = new Kit_Item__c(
            Kit__c = testProduct2.Id,
            Product__c = testProduct1.Id,
            recordTypeId = objrecoesType.Id
        );
        insert testKitItem;
        masterModelId = testProduct2.Id;
        // Call the method being tested
        Test.startTest();
        String result = CCM_ProductRegistration.addWarrantyItem(email, purchaseDate, brandName, masterModelNo, masterModelId, purchasePlace);
        system.assertNotEquals(null, result);
        Test.stopTest();

    }
     @isTest
    static void testAddWarrantyItem3() {
        // Create test data
        String email = '<EMAIL>';
        Date purchaseDate = Date.today();
        String brandName = 'Test Brand';
        String masterModelNo = 'Test Master Model';
        String masterModelId = 'Test Master Model Id';
        String purchasePlace = 'Test Purchase Place';
        RecordType objRecordType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Residential_Consumer' LIMIT 1];
        // Create a test account
        Account testAccount = new Account(
            PersonEmail = email,
            LastName = 'Honey',
            Consumer_Status__c = 'Active',
            recordTypeId = objRecordType.Id
        );
        insert testAccount;
        
        // 创建测试数据
        
        // 创建关联的产品
        Product2 testProduct1 = new Product2(
            Name = 'Test Product',
            IsActive = true
        );
        insert testProduct1;
        Product2 testProduct2 = new Product2(
            Name = 'Test Product2',
            IsActive = true
        );
        insert testProduct2;
        RecordType objrecoesType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Kit_Item__c' AND DeveloperName = 'Kits_and_Products' LIMIT 1];
        // 创建关联的Kit Item
        Kit_Item__c testKitItem = new Kit_Item__c(
            Kit__c = testProduct2.Id,
            Product__c = testProduct1.Id,
            recordTypeId = objrecoesType.Id
        );
        insert testKitItem;
        masterModelId = testProduct2.Id;
        // Call the method being tested
        Test.startTest();
        String result = CCM_ProductRegistration.addWarrantyItem(email, purchaseDate, brandName, masterModelNo, masterModelId, purchasePlace);
        system.assertNotEquals(null, result);
        Test.stopTest();

    }
     @isTest
    static void testAddWarrantyItem4() {
        // Create test data
        String email = '<EMAIL>';
        Date purchaseDate = Date.today();
        String brandName = 'Test Brand';
        String masterModelNo = 'Test Master Model';
        String masterModelId = 'Test Master Model Id';
        String purchasePlace = 'Test Purchase Place';
        RecordType objRecordType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Residential_Consumer' LIMIT 1];
        // Create a test account
        Account testAccount = new Account(
            PersonEmail = email,
            LastName = 'Honey',
            Consumer_Status__c = 'Active',
            recordTypeId = objRecordType.Id
        );
        insert testAccount;
        
       
        // Call the method being tested
        Test.startTest();
        String result = CCM_ProductRegistration.addWarrantyItem(email, purchaseDate, brandName, masterModelNo, masterModelId, purchasePlace);
        system.assertNotEquals(null, result);
        Test.stopTest();

    }
    
      @isTest
    static void testUploadFile() {
        // Create test data
        String fileName = 'TestFile.txt';
        String content = 'Test file content';
        
        // Call the method to be tested
        Test.startTest();
        String result = CCM_ProductRegistration.uploadFile(fileName, content);
        Test.stopTest();
        
        // Assert the expected results
        Map<String, Object> resultMap = (Map<String, Object>) JSON.deserializeUntyped(result);
        System.assertEquals('Success', resultMap.get('Status'));
        System.assertEquals('', resultMap.get('Message'));
        System.assertNotEquals('', resultMap.get('ContentId'));
    }
    @isTest
    static void testGetCountry() {
        // Create test data
        String filter = 'CS';
        
        // Call the method to be tested
        Test.startTest();
        String result = CCM_ProductRegistration.getCountry(filter);
        system.assertNotEquals(null, result);
        Test.stopTest();
        
        // Assert the expected results
    }
     @isTest
    static void testGetCountry2() {
        // Create test data
        String filter = '';
        
        // Call the method to be tested
        Test.startTest();
        String result = CCM_ProductRegistration.getCountry(filter);
        system.assertNotEquals(null, result);
        Test.stopTest();
        
        // Assert the expected results
    }
     @isTest
    static void testSnVerify() {
        // Create test data
        String modelNo = 'TestModel';
        String sn = 'E2345240122222A';
        
        // Create a Warranty_Rules__c record for testing
        Warranty_Rules__c warrantyRule = new Warranty_Rules__c();
        warrantyRule.Product_Model__c = modelNo;
        warrantyRule.Code_in_Serial__c = '2345';
        insert warrantyRule;
        
        // Call the method to be tested
        Test.startTest();
        String result = CCM_ProductRegistration.snVerify(modelNo, sn, Date.today());
        Test.stopTest();
        
        // Assert the expected results
        // System.assertEquals('invalid', result);
    }
    
     @isTest static void testMassUploadVerification() {
        // Prepare test data
        // You need to create test data for Product2, Warranty_Rules__c, Kit_Item__c, etc.
        // The test data should be created based on your business logic
        // Here I just show an example of creating a Product2 record
        Product2 prod = new Product2(Name='Test Product', IsActive=TRUE, Order_Model__C='Test Model');
        insert prod;

        // Prepare input for the method
        CCM_ProductRegistration.WarrantyForm warrantyForm = new CCM_ProductRegistration.WarrantyForm();
        warrantyForm.productList = new List<CCM_ProductRegistration.Product3>();
         CCM_ProductRegistration.Product3 objProduct3 = new CCM_ProductRegistration.Product3();
         objProduct3.modelNo = prod.Name;
          objProduct3.email='<EMAIL>';
         objProduct3.purchaseDate=System.today();
         objProduct3.purchasePlace='Test Place';
         objProduct3.serialNo='1234567890';
        warrantyForm.productList.add(objProduct3);
        String proListStr = JSON.serialize(warrantyForm);
                 // 创建关联的产品
                 // TLS_Product
         RecordType objrecoesType1 = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'product2' AND DeveloperName = 'TLS_Product' LIMIT 1];
        Product2 testProduct1 = new Product2(
            Name = 'Test Product',
            recordTypeId = objrecoesType1.Id,
            IsActive = true
        );
        insert testProduct1;
        Product2 testProduct2 = new Product2(
            Name = 'Test Product2',
            IsActive = true
        );
        insert testProduct2;
        RecordType objrecoesType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Kit_Item__c' AND DeveloperName = 'Kits_and_Products' LIMIT 1];
        // 创建关联的Kit Item
        Kit_Item__c testKitItem = new Kit_Item__c(
            Kit__c = testProduct2.Id,
            Product__c = testProduct1.Id,
            recordTypeId = objrecoesType.Id
        );
        insert testKitItem;

        // Call the method
        String result = CCM_ProductRegistration.massUploadVerification(proListStr);

        // Check the result
        CCM_ProductRegistration.WarrantyForm resultForm = (CCM_ProductRegistration.WarrantyForm)JSON.deserialize(result, CCM_ProductRegistration.WarrantyForm.class);
        System.assertEquals(1, resultForm.productList.size());
     
    }
    @isTest static void testMassUploadVerification3() {
        // Prepare test data
        // You need to create test data for Product2, Warranty_Rules__c, Kit_Item__c, etc.
        // The test data should be created based on your business logic
        // Here I just show an example of creating a Product2 record
        Product2 prod = new Product2(Name='Test Product', IsActive=TRUE, Order_Model__C='Test Model');
        insert prod;

        // Prepare input for the method
        CCM_ProductRegistration.WarrantyForm warrantyForm = new CCM_ProductRegistration.WarrantyForm();
        warrantyForm.productList = new List<CCM_ProductRegistration.Product3>();
         CCM_ProductRegistration.Product3 objProduct3 = new CCM_ProductRegistration.Product3();
         objProduct3.modelNo = prod.Name;
          objProduct3.email='<EMAIL>';
         objProduct3.receiptId='1';
         objProduct3.purchaseDate=System.today();
         objProduct3.purchasePlace='Test Place';
         objProduct3.serialNo='1234567890';
        warrantyForm.personEmail = '<EMAIL>';
        warrantyForm.firstName = 'Honey Test';
        warrantyForm.lastName = 'Honey Test';
        warrantyForm.phone = '176743072113';
        warrantyForm.shippingPostalCode = '32141';
        warrantyForm.shippingCountry = 'CS';
        warrantyForm.shippingCity = 'Honey Test';
        
        warrantyForm.shippingStreet = '32141';
        warrantyForm.address = 'Honey Test';
        warrantyForm.branName = 'Honey Test';
        warrantyForm.isMassUpload = false;
       warrantyForm.shippingState = '2142';
        warrantyForm.productList.add(objProduct3);
        String proListStr = JSON.serialize(warrantyForm);
                 // 创建关联的产品
                 // TLS_Product
         RecordType objrecoesType1 = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'product2' AND DeveloperName = 'TLS_Product' LIMIT 1];
        Product2 testProduct1 = new Product2(
            Name = 'Test Product',
            recordTypeId = objrecoesType1.Id,
            IsActive = true
        );
        insert testProduct1;
        Product2 testProduct2 = new Product2(
            Name = 'Test Product2',
            IsActive = true
        );
        insert testProduct2;
        RecordType objrecoesType = [SELECT Id, DeveloperName FROM RecordType WHERE SObjectType = 'Kit_Item__c' AND DeveloperName = 'Kits_and_Products' LIMIT 1];
        // 创建关联的Kit Item
        Kit_Item__c testKitItem = new Kit_Item__c(
            Kit__c = testProduct2.Id,
            Product__c = testProduct1.Id,
            recordTypeId = objrecoesType.Id
        );
        insert testKitItem;

        // Call the method
        String result = CCM_ProductRegistration.massUploadVerification(proListStr);

        // Check the result
        CCM_ProductRegistration.WarrantyForm resultForm = (CCM_ProductRegistration.WarrantyForm)JSON.deserialize(result, CCM_ProductRegistration.WarrantyForm.class);
        System.assertEquals(1, resultForm.productList.size());
     
    }

    @IsTest
    static void saveWarrantyTest(){
        Product2 product = new Product2();
        product.Name = 'test';
        insert product;

        Receipt_Junk_Data__c data = new Receipt_Junk_Data__c();
        data.Receipt_Junk_Data_Link__c = 'test';
        insert data;
        Test.startTest();
        CCM_ProductRegistration.WarrantyForm form = new CCM_ProductRegistration.WarrantyForm();
        form.productList = new List<CCM_ProductRegistration.Product3>();
        form.registrationType = 'test';
        form.personEmail = '<EMAIL>';
        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3();
        product3.masterModelId = product.Id;
        product3.modelId = 'test';
        product3.serialNo = 'test';
        product3.purchaseDate = Date.today();
        product3.purchasePlace = 'Online Dealers';
        product3.receipt = 'https://test.com/img.jpg';
        product3.receiptName = 'testimg';
        product3.junkDataLink = 'test';
        form.productList.add(product3);
        CCM_ProductRegistration.SaveWarranty(JSON.serialize(form));
        Test.stopTest();
    }

    @IsTest
    static void testOthers(){
        Receipt_Junk_Data__c data = new Receipt_Junk_Data__c();
        insert data;

        ContentVersion cv = new ContentVersion();
        cv.Title = 'Test Document';
        cv.PathOnClient = 'TestDocument.pdf';
        cv.VersionData = Blob.valueOf('Test Content');
        cv.IsMajorVersion = true;
        Insert cv;
        
        Test.startTest();
        try {
            ContentDocument conDocument = [SELECT Id FROM ContentDocument LIMIT 1];
            CCM_ProductRegistration.ReceiptJunkPreCreate();
            CCM_ProductRegistration.UploadReceiptToAws(data.Id, '', 'test', cv.Id);
        }
        catch(Exception ex) {
            
        }
        
        Test.stopTest();
    }

    @IsTest
    static void testOther2(){
        
        Test.startTest();
        try{CCM_ProductRegistration.lookUpSearch('test', 'test');}catch(Exception ex){}
        try{CCM_ProductRegistration.GetProductInfo('test', null, Date.today());}catch(Exception ex){}
        try{CCM_ProductRegistration.QueryAddressByName('test', null, null);}catch(Exception ex){}
        try{CCM_ProductRegistration.getPromotionsByProduct(null, null, false, false, Date.today());}catch(Exception ex){}
        try{CCM_ProductRegistration.QueryAddressByName('test');}catch(Exception ex){}
        try{CCM_ProductRegistration.queryAvaliableCustomer('test');}catch(Exception ex){}
        try{CCM_ProductRegistration.queryBillAddressInfo('test', null);}catch(Exception ex){}
        try{CCM_ProductRegistration.queryLocationLookUpInfo();}catch(Exception ex){}
        try{CCM_ProductRegistration.queryShipAddressInfo('test', null);}catch(Exception ex){}
        try{CCM_ProductRegistration.QueryAddressInfo('test');}catch(Exception ex){}
        try{CCM_ProductRegistration.getDeliveryAddress(null, 'test', 'test', false);}catch(Exception ex){}
        try{CCM_ProductRegistration.queryCustomer('test', 10, 10);}catch(Exception ex){}
        try{CCM_ProductRegistration.queryWarrantyClaim('test', 10, 10, null);}catch(Exception ex){}
        try{CCM_ProductRegistration.queryModelNumber(null, 'test', 10, 10);}catch(Exception ex){}
        try{CCM_ProductRegistration.queryModelNumberWithoutCustomer('test', 10, 10);}catch(Exception ex){}
        try{CCM_ProductRegistration.queryAddress(null, 'test', 'test', 10, 10);}catch(Exception ex){}
        try{CCM_ProductRegistration.queryParts(null, 10, 10, null, 'test', null);}catch(Exception ex){}
        try{CCM_ProductRegistration.getConsumerInfoById(UserInfo.getUserId());}catch(Exception ex){}

        CCM_ProductRegistration.WarrantyWrapper wrapper = new CCM_ProductRegistration.WarrantyWrapper();
        wrapper.PurchaseDate = 'test';
        wrapper.Brand = 'test';
        wrapper.PurchasePlace = 'test';
        wrapper.MasterProductNumber = 'test';
        wrapper.ModelNumber = 'test';
        wrapper.SerialNumber = 'test';

        // CCM_ProductRegistration.forCodeCoverage();
        Test.stopTest();
    }
}