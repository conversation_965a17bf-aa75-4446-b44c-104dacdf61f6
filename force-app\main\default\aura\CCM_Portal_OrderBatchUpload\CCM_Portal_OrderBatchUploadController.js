({
    doInit : function(component, event, helper) {
        component.set('v.addressColumns', [
            { label: $A.get("$Label.c.CCM_CustomerName"), fieldName: 'CustomerName', type: 'text' },
            { label: $A.get("$Label.c.CCM_CustomerNumber"), fieldName: 'CustomerNumber', type: 'text' },
            { label: $A.get("$Label.c.CCM_AddressCode"), fieldName: 'AddressCode', type: 'text' },
            { label: $A.get("$Label.c.CCM_AddressType"), fieldName: 'AddressType', type: 'text' },
            { label: $A.get("$Label.c.CCM_Country"), fieldName: 'Country', type: 'text' },
            { label: $A.get("$Label.c.CCM_City"), fieldName: 'City', type: 'text'},
            { label: $A.get("$Label.c.CCM_Street"), fieldName: 'Street', type: 'text' },
            { label: $A.get("$Label.c.CCM_PostalCode"), fieldName: 'PostalCode', type: 'text' },
        ]);
        helper.getUserType(component);
        helper.getCustomerInfo(component);
    },

    // 模板选择
    handleSelect : function(component, event, helper) {
        let selectedMenuItemValue = event.getParam("value");
        switch (selectedMenuItemValue) {
            case 'insideSalesTemplete':
                console.log('insideSalesTemplete=============');
                let insideSales = document.createElement('a'); 
                // 创建一个隐藏的a标签
                insideSales.style = 'display: none';
                insideSales.download = 'Inside Sales Templete';
                insideSales.href = $A.get('$Resource.Batch_Upload_Templete') + '/InsideSalesTemplate.xlsx';
                document.body.appendChild(insideSales);
                // 触发a标签的click事件
                insideSales.click();
                document.body.removeChild(insideSales);
                break;
            case '​templeteSalesRep':
                let salesRep = document.createElement('a'); 
                let downStr = '​Templete Sales Rep';
                // 创建一个隐藏的a标签
                salesRep.style = 'display: none';
                salesRep.download = downStr;
                salesRep.href = $A.get('$Resource.Templete_SalesRep') + '/TemplateSalesRep.xlsx';
                document.body.appendChild(salesRep);
                // 触发a标签的click事件
                salesRep.click();
                document.body.removeChild(salesRep);
                break;
            case 'dealersTemplete':
                let dealers = document.createElement('a'); 
                // 创建一个隐藏的a标签
                dealers.style = 'display: none';
                dealers.download = 'Dealers Templete';
                dealers.href = $A.get('$Resource.Batch_Upload_Templete') + '/DealersTemplate.xlsx';
                document.body.appendChild(dealers);
                // 触发a标签的click事件
                dealers.click();
                document.body.removeChild(dealers);
                break;
        }
    },
    // 模板下载
    handleSelectInsideSales : function(component, event, helper) {
        let locale = $A.get("$Locale.language");
        let insideSales = document.createElement('a'); 
        // 创建一个隐藏的a标签
        insideSales.style = 'display: none';
        if(locale === 'de') {
            insideSales.download = 'Vorlage für Innensales';
            insideSales.href = $A.get('$Resource.Batch_Upload_Templete') + '/InsideSalesTemplate_DE.xlsx';
        }
        else {
            insideSales.download = 'Inside Sales Template';
            insideSales.href = $A.get('$Resource.Batch_Upload_Templete') + '/InsideSalesTemplate.xlsx';
        }
        document.body.appendChild(insideSales);
        // 触发a标签的click事件
        insideSales.click();
        document.body.removeChild(insideSales);
    },
    handleSelectSalesRep : function(component, event, helper) {
        let locale = $A.get("$Locale.language");
        // 创建一个隐藏的a标签
        let salesRep = document.createElement('a');
        salesRep.style = 'display: none';
        if(locale === 'de') {
            salesRep.download = 'Verkäufervertreter-Vorlage';
            salesRep.href = $A.get('$Resource.Templete_SalesRep') + '/SalesRepTemplate_DE.xlsx';
        }
        else {
            salesRep.download = 'Sales Rep Template';
            salesRep.href = $A.get('$Resource.Templete_SalesRep') + '/SalesRepTemplate.xlsx';
        }
        document.body.appendChild(salesRep);
        // 触发a标签的click事件
        salesRep.click();
        document.body.removeChild(salesRep);
    },
    handleSelectDealers : function(component, event, helper) {
        let locale = $A.get("$Locale.language");
        let dealers = document.createElement('a');
        dealers.style = 'display: none';
        // 创建一个隐藏的a标签
        if(locale === 'de') {
            dealers.download = 'Verkäufervorlage';
            dealers.href = $A.get('$Resource.Batch_Upload_Templete') + '/DealersTemplate_DE.xlsx';
        }
        else {
            dealers.download = 'Dealers Template';
            dealers.href = $A.get('$Resource.Batch_Upload_Templete') + '/DealersTemplate.xlsx';
        }
        document.body.appendChild(dealers);
        // 触发a标签的click事件
        dealers.click();
        document.body.removeChild(dealers);
    },

    // 获取对应地址code
    previewCodeEvent : function(component, event, helper) {
        // 判断是否有customer
        component.set('v.modalFlag', true);
        var action = component.get('c.QueryAddressInfo');
        action.setParams({
            CustomerName : component.get('v.customerId'),
            IsProtal : true,
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            console.log(response, 'setCallback=========');
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            console.log(result, '获取对应地址code result=========');
            if (state === 'SUCCESS' ) {
                const arr = [];
                result.forEach((item)=>{
                    arr.push(
                        {
                            CustomerName: item.Customer__r.Name,
                            CustomerNumber: item.Customer__r.AccountNumber,
                            AddressCode: item.Customer_Line_Oracle_ID__c,
                            AddressType: item.RecordType_Name__c,
                            Country: item.Country__c,
                            City: item.City__c,
                            Street: item.Street_1__c,
                            PostalCode: item.Postal_Code__c,
                        }
                    )
                })
                console.log(JSON.parse(JSON.stringify(arr)), 'arr==========');
                component.set('v.addressData', JSON.parse(JSON.stringify(arr)));
            }else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
        
    },
    // 关闭address弹框
    cancelEvent : function(component, event, helper) {
        component.set('v.modalFlag', false);
    },

    // 获取excel解析数据
    getParseData : function(component, event, helper) {
        let parseData = JSON.parse(JSON.stringify(event.getParam('parseData')));
        console.log(parseData, '获取excel解析数据-------------');
        // 表格字段处理
        const arr = [];
        let locale = $A.get("$Locale.language");
        if(locale === 'de') {
            parseData.forEach((item)=>{
                arr.push({
                    'OrderGroupNumber': item[$A.get("$Label.c.CCM_OrderGroupNumber")] || '',
                    // 'CustomerNumber': item['Customer Number'],
                    'CustomerPO': item[$A.get("$Label.c.CCM_CustomerPO")] || '',
                    'BillToAddressCode': item[$A.get("$Label.c.CCM_BillToAddressCode")] || '',
                    'ShipToAddressCode': item[$A.get("$Label.c.CCM_ShipToAddressCode")] || '',
                    'DropshipAddressCode': item[$A.get("$Label.c.CCM_DropshipAddressCode")] || '',
                    'InsuranceFee': item[$A.get("$Label.c.CCM_InsuranceFee") + ' (E.g 100,000.00)'] || '',
                    'OtherFee': item[$A.get("$Label.c.CCM_OtherFee") + ' (E.g 100,000.00)'] || '',
                    'OrderType': item[$A.get("$Label.c.CCM_OrderType")] || '',
                    'Warehouse': item[$A.get("$Label.c.CCM_Warehouse")] || '',
                    'Qty': item[$A.get("$Label.c.CCM_Qty")] || '',
                    'IsDropship': item[$A.get("$Label.c.Order_IsDropship")] || '',
                    'ModelNumber': item[$A.get("$Label.c.CCM_Model")] || '',
                    'RequestDate': item[$A.get("$Label.c.CCM_RequestDate") + ' (YYYY-MM-DD)'] ? item[$A.get("$Label.c.CCM_RequestDate") + ' (YYYY-MM-DD)'].toString().slice(0, 10) : '',
                    'ScheduleShipDate': item[$A.get("$Label.c.CCM_ScheduleShipDate") + ' (YYYY-MM-DD)'] ? item[$A.get("$Label.c.CCM_ScheduleShipDate") + ' (YYYY-MM-DD)'].toString().slice(0, 10) : '',
                    'Remark': item[$A.get("$Label.c.CCM_Remark")] || '',
                })
            });
        }
        else {
            parseData.forEach((item)=>{
                arr.push({
                    'OrderGroupNumber': item['Order Group Number'] || '',
                    // 'CustomerNumber': item['Customer Number'],
                    'CustomerPO': item['Customer PO'] || '',
                    'BillToAddressCode': item['Bill To Address (Code)'] || '',
                    'ShipToAddressCode': item['Ship To Address (Code)'] || '',
                    'DropshipAddressCode': item['Dropship Address (Code)'] || '',
                    'InsuranceFee': item['Insurance Fee (E.g 100,000.00)'] || '',
                    'OtherFee': item['Other Fee (E.g 100,000.00)'] || '',
                    'OrderType': item['Order Type'] || '',
                    'Warehouse': item['Warehouse'] || '',
                    'Qty': item['Qty'] || '',
                    'IsDropship': item['Is Dropship?'] || '',
                    'ModelNumber': item['Model #'] || '',
                    'RequestDate': item['Request Date (YYYY-MM-DD)'] ? item['Request Date (YYYY-MM-DD)'].toString().slice(0, 10) : '',
                    'ScheduleShipDate': item['Schedule Ship Date (YYYY-MM-DD)'] ? item['Schedule Ship Date (YYYY-MM-DD)'].toString().slice(0, 10) : '',
                    'Remark': item['Remark'] || '',
                })
            });
        }
        console.log(JSON.stringify(arr), '上传数据==================');
        // 判断上传数据是否为空
        if (arr.length) {
            // 上传后直接校验数据
            helper.checkUploadData(component, arr);
        } else {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": $A.get("$Label.c.CCM_FillInUploadDataWarning"),
                "type": "warning"
            }).fire();
            component.set('v.resetFlag', true);
        }
        // let userType = component.get('v.userType');
        // let vaild = true;
        // switch (userType) {
        //     case 'InsideSales': 
        //         arr.forEach((item)=>{
        //             if (!item.OrderGroupNumber) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Order Group Number' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.CustomerPO) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Customer PO' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.BillToAddressCode) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Bill To Address (Code)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.ShipToAddressCode) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Ship To Address (Code)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.OrderType) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Order Type' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.Warehouse) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Warehouse' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.IsDropship) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Is Dropship?' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.Model) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Model #' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.Qty) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Qty' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.RequestDate) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Request Date (YYYY-MM-DD)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //         });
        //         break;
        //     case 'SalesRep': 
        //         arr.forEach((item)=>{
        //             if (!item.OrderGroupNumber) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Order Group Number' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.CustomerPO) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Customer PO' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.BillToAddressCode) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Bill To Address (Code)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.ShipToAddressCode) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Ship To Address (Code)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.OrderType) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Order Type' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.IsDropship) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Is Dropship?' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.Model) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Model #' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.Qty) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Qty' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.RequestDate) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Request Date (YYYY-MM-DD)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //         });
        //         break;
        //     case 'Dealer': 
        //         arr.forEach((item)=>{
        //             if (!item.OrderGroupNumber) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Order Group Number' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.CustomerPO) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Customer PO' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.BillToAddressCode) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Bill To Address (Code)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.ShipToAddressCode) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Ship To Address (Code)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.Model) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Model #' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.Qty) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Qty' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //             if (!item.RequestDate) {
        //                 var toastEvt = $A.get("e.force:showToast");
        //                 toastEvt.setParams({
        //                     "title": "Error",
        //                     "message": 'Request Date (YYYY-MM-DD)' + $A.get("$Label.c.CCM_RequiredField"),
        //                     "type": "error"
        //                 }).fire();
        //                 vaild = false;
        //                 return;
        //             };
        //         });
        //         break;
        //     default:
        //         break;
        // }
        // if (vaild) {
        //     component.set('v.tableData', JSON.parse(JSON.stringify(arr)));
        // };
        // component.set('v.resetFlag', true);
    },

    // 提交事件
    onClickSave : function(component, event, helper) {
        helper.submitEvent(component);
    },

    // 取消事件
    onClickCancel : function(component, event, helper) {
        let url = window.location.origin + '/s/orderinformation';
        window.open(url, '_self');
    }
})