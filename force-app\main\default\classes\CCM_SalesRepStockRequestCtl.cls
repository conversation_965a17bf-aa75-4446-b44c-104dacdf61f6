/**
Authour: Honey
Description: 申请SalesRepStock信息
Date: 2023/06/30
*/
public without sharing class CCM_SalesRepStockRequestCtl {
    //查询Product--->根据Model或者Name模糊匹配
    @AuraEnabled
    public static Map<String,Object> QueryProductByModel(String fiterString, String currencyIsoCode,String userId) {
        Map<String,Object> mapFeild2Value = new Map<String,Object>();

        List<String> lstProductType = new List<String>();
        lstProductType.add('ACC');
        lstProductType.add('TLS_Product');
        lstProductType.add('TLS_KIT');
        lstProductType.add('SP');
        lstProductType.add('TLS_VK');
      
        List<Product2> lstProductTmp1 = [
            SELECT  Id,Name,Order_Model__c,RecordType.DeveloperName
            ,Item_Description_EN__c
            FROM Product2 WHERE Id != NULL 
            AND IsActive = TRUE 
            AND RecordType.DeveloperName IN : lstProductType
        ];
        // model + desc 的筛选
        List<Product2> lstProduct = new List<Product2>();
        for (Product2 prd : lstProductTmp1) {
            String strModel = prd.Order_Model__c;
            String strDesc = prd.Item_Description_EN__c;

            if (String.isNotBlank(strDesc)) {
                if (strModel.containsIgnoreCase(fiterString.trim()) || strDesc.containsIgnoreCase(fiterString.trim())) {
                    lstProduct.add(prd);
                }
            } else {
                if (strModel.containsIgnoreCase(fiterString.trim())) {
                    lstProduct.add(prd);
                }
            }
        }

        List<String> lstProductId = new List<String>();
        for(Product2 objProduct : lstProduct){
            lstProductId.add(objProduct.Id);
        }
        //申请得时候使用公共Customer 
        Account objAccount = [
            SELECT Id FROM Account WHERE AccountNumber = :Label.Public_Customer_For_Sync_Order
        ];
        
        Map<String,Decimal> mapProductId2Price = CCM_GetProductInfoUtil.getListPrice(lstProductId, Date.today(), currencyIsoCode,objAccount.Id);
        List<Map<String,Object>> lstMapDate2Value = new List<Map<String,Object>>();

        // KIT 推荐 Tools BEGIN
        List<Kit_Item__c> lstKits = [
            SELECT id, Kit__c,Kit__r.Order_Model__c, vk_product__c, vk_product__r.Item_Description_EN__c, 
            vk_product__r.Order_Model__c, vk_product__r.Unit_Measure__c,vk_product__r.Name,Quantity__c
            FROM kit_item__c WHERE Id != NULL
            AND RecordType.DeveloperName = 'Kits_And_Products'
            AND Kit__c in:lstProductId 
            AND vk_product__c <> null
        ];
        Map<String,List<Object>> mapKitWithTools = new Map<String,List<Object>>();
        
        for (Kit_Item__c kit : lstKits) {
            List<Object> lstKitWithTools =  mapKitWithTools.containsKey(kit.Kit__c) ? mapKitWithTools.get(kit.Kit__c) : new List<Object>();
            Map<String,Object> mapKitItem = new Map<String,Object>();
            mapKitItem.put('id', kit.Id);
            mapKitItem.put('kitid', kit.Kit__c);
            mapKitItem.put('tooid', kit.vk_product__c);
            mapKitItem.put('toolmodel', kit.vk_product__r.Order_Model__c);
            mapKitItem.put('toolname', kit.vk_product__r.Name);
            mapKitItem.put('qty', kit.Quantity__c);
            mapKitItem.put('listPrice',0);
            mapKitItem.put('subTotal', 0);
            lstKitWithTools.add(mapKitItem);
            lstProductId.add(kit.vk_product__c);
            mapKitWithTools.put(kit.Kit__c, lstKitWithTools);
        }
        // KIT 推荐 Tools END

        // 行上的 product ，初始 qty 是 1，拿到 1 的红绿灯 begin
        Integer intQty = 1;
        // TODO,两个查询应该合并的
        // 返回的是一个 Yellow/Red/Green Light 的字符串
        Map<String,Integer> mapAllInventory = new Map<String,Integer>();
       
        Map<String,String> mapProductInventory = new Map<String,String>();
        // 查询总库信息
        // 不能用 单独对象去查，有可能结果为 list 为 null
        List<Inventory__c> lstInventory = [
            SELECT Id,Product__c,Available_QTY__c,RecordTypeId,RecordType.Name 
            FROM Inventory__c 
            WHERE Id != null 
            AND Product__c IN :lstProductId 
            AND RecordType.Name = 'All Inventory' AND Sub_Inventory_Code__c = 'EGD01'
        ];
        System.debug('lstInventory-->' + lstInventory);
        
       
        System.debug('lstInventory:' + lstInventory);
   

        // 封装 product 和 qty
        for (Inventory__c invAllTmp : lstInventory) {
            mapAllInventory.put(invAllTmp.Product__c,(Integer)invAllTmp.Available_QTY__c);
        }
      
        // 遍历 product 给 product inventory 赋值
        for (String prodId : lstProductId) {
            String strInventory = CCM_Constants.RED;
            Integer intAllInv = mapAllInventory.get(prodId);
          
           
           
            if(intAllInv != null ){
                //request申请。只看总库数据
                if(intAllInv >= 0 ){
                    strInventory =  CCM_Constants.GREEN;
                }else if(intAllInv < 0){
                    strInventory =  CCM_Constants.RED;
                }

            }else{
                strInventory =  CCM_Constants.RED;
            }

            mapProductInventory.put(prodId, strInventory);
        }

        // 行上的 product ，初始 qty 是 1，拿到 1 的红绿灯 end
        for(Product2 objProduct : lstProduct){
            Map<String,Object> mapdate2Value = new Map<String,Object>();
            // 没有价格，不返回数据
            Decimal dcPrice = mapProductId2Price.get(objProduct.Id);
            if (dcPrice == null) {
                continue;
            }
            if(mapProductId2Price.get(objProduct.Id) != null){
                List<Object> lstKitwithToolReturn = new List<Object>();
                mapdate2Value.put('productid',objProduct.Id);
                // 需求明确，name 要展示 product description 的内容
                mapdate2Value.put('name',objProduct.Item_Description_EN__c);
                mapdate2Value.put('model',objProduct.Order_Model__c);
                mapdate2Value.put('price', dcPrice);
                mapdate2Value.put('recordtype', objProduct.RecordType.DeveloperName);
                List<Object> lstKitwithTool = mapKitWithTools.get(objProduct.Id) == NULL ? new List<Object>() : mapKitWithTools.get(objProduct.Id) ;
                system.debug('lstKitwithTool--->'+lstKitwithTool);
                for(Object objKit : lstKitwithTool){
                    Map<String,Object> mapKitItem = (Map<String,Object>)objKit;
                    String toolId = (String)mapKitItem.get('tooid');
                    system.debug('toolId--->'+toolId);
                    mapKitItem.put('inventorystatus', mapProductInventory.get(toolId));
                    system.debug('mapKitItem--->'+mapKitItem);
                    lstKitwithToolReturn.add(mapKitItem);
                }
                
                mapdate2Value.put('tools',lstKitwithToolReturn);


                // TODO 需要有红绿灯的逻辑
                system.debug('mapProductInventory-->'+mapProductInventory);
                mapdate2Value.put('inventorystatus',mapProductInventory.get(objProduct.Id));
                lstMapDate2Value.add(mapdate2Value);
            }
        }
        mapFeild2Value.put('state',CCM_Constants.SUCCESS);
        mapFeild2Value.put('currencycode', currencyIsoCode);
        mapFeild2Value.put('data',lstMapDate2Value);
        System.debug('mapFeild2Value:'  + JSON.serialize(mapFeild2Value));
        return mapFeild2Value;
    }
    //获取当前用户信息
    @AuraEnabled
    public static Map<String,Object> QueryUserInfo(){
        Map<String,Object> mapRestInfo = new Map<String,Object>();
        Map<String,Object> mapDate2Value = new Map<String,Object>();
        CurrentUserInfo objCurrentUser = new CurrentUserInfo();
        String UserId = UserInfo.getUserId();
        User objUser = [
            SELECT Id,Name
            ,UserRoleId,UserRole.Name,ProfileId, Profile.Name
            ,DefaultCurrencyIsoCode
            FROM User where Id = :UserId
        ];
        
        objCurrentUser.UserName = objUser.Name;
        objCurrentUser.UserId = UserId;
        objCurrentUser.UserRole = objUser.UserRole.Name;
        if(CCM_Constants.INSIDE_SALES_MANAGER.equals(objUser.Profile.Name) || CCM_Constants.SALES_MANAGER.equals(objUser.Profile.Name) ||
        CCM_Constants.SALES_DIRECTOR.equals(objUser.Profile.Name) ||
           CCM_Constants.INSIDE_SALES_REP.equals(objUser.Profile.Name)|| Label.ADMIN.equals(objUser.Profile.Name)){
            mapDate2Value.put('blCanSelectRequestFor',true);
        }else{
           mapDate2Value.put('blCanSelectRequestFor',false);
        }
        
        mapDate2Value.put('userId',UserId);
        mapDate2Value.put('name',objUser.Name);
        // TODO, 一些情况下，取默认user，作为 request for，此时的币种，为这个user的币种
        mapDate2Value.put('currency',objUser.DefaultCurrencyIsoCode);
        mapRestInfo.put('state',CCM_Constants.SUCCESS);
        mapRestInfo.put('data',mapDate2Value);
        System.debug('mapRestInfo-->'+mapRestInfo);
        return mapRestInfo;
    }
    
    //如果是inside Sales 可以选择用户
    @AuraEnabled
    public static List<Object> QueryInfo(String fiterString){
        fiterString = '%'+fiterString+'%' ;
        //选择可选的---用户
        // request 的币种，需要取自 request for user 的币种字段 DefaultCurrencyIsoCode
        Set<String> setProfileNames = new Set<String>();
        setProfileNames.add('Inside Sales Rep');
        setProfileNames.add('Inside Sales Manager');
        setProfileNames.add('Sales Rep');
        setProfileNames.add('Sales Manager');
        setProfileNames.add('Sales Director');
        setProfileNames.add('Product Management');
        List<User> lstUser = [
            SELECT Id,Name,DefaultCurrencyIsoCode,ProfileId, Profile.Name FROM User WHERE IsActive = true AND Name Like :fiterString
            AND (Profile.Name IN :setProfileNames )
        ];
        Map<String,Object> mapRestInfo = new Map<String,Object>();
        List<Object> lstmapDate2Value = new List<Object>();
        for(User objUser : lstUser){
            Map<String,Object> mapDate2Value = new Map<String,String>();
            mapDate2Value.put('Id',objUser.Id);
            mapDate2Value.put('Name',objUser.Name);
            mapDate2Value.put('Currency',objUser.DefaultCurrencyIsoCode);
            lstmapDate2Value.add(mapDate2Value);
        }
        return lstmapDate2Value;
     
    }
    // 查询红绿灯状态信息
    @AuraEnabled
    public static Map<String,Object> queryInventoryInfo(String ProductId, String UserId, Decimal Qty){
        Map<String,Object> mapRes = new Map<String,oBJECT>();
        try {
            // TODO,两个查询应该合并的
            // 返回的是一个 Yellow/Red/Green Light 的字符串--->给Inventory一个默认值
            String Inventory = CCM_Constants.RED_LIGHT;
            // todo
            // 查询总库信息
            // 不能用 单独对象去查，有可能结果为 list 为 null
            List<Inventory__c> lstInventory = [
                SELECT Id,Product__c,Available_QTY__c,RecordTypeId,RecordType.Name 
                FROM Inventory__c 
                WHERE Id != null 
                AND Product__c = :ProductId 
                AND RecordType.Name = 'All Inventory' AND Sub_Inventory_Code__c = 'EGD01'
            ];
            System.debug('lstInventory-->' + lstInventory);
            // 通过UserId查询UserCode
            // 这个 user id， 是 salesfor user id ，不是当前执行 apex class 代码的 user
            User objUser = null;
            List<User> lstUsers  = [
                SELECT Id,FederationIdentifier,Profile.Name 
                FROM User WHERE Id != null
                AND Id IN (:UserInfo.getUserId(), :UserId)
            ];

            for (User usr : lstUsers) {
                if (usr.Profile.Name == Label.Admin) {
                    objUser = usr;
                } else {
                    objUser = usr;
                }
            }
            System.debug('objUser:' + objUser);
            // Honey Update --->没有销售子库一说
            System.debug('lstInventory:' + lstInventory);
            if((lstInventory != null && lstInventory.size() > 0)  ){
                Decimal AllInventory = lstInventory.get(0).Available_QTY__c - Qty;
                if(AllInventory >= 0 ){
                    Inventory =  CCM_Constants.GREEN_LIGHT;
                }else if(AllInventory < 0){
                    Inventory =  CCM_Constants.RED_LIGHT;
                }
            }
                mapRes.put('state', 'SUCCESS');
                mapRes.put('else', 'else');
                mapRes.put('inventorystatus', Inventory);
            

        } catch (Exception ex) {
            mapRes.put('state', 'ERROR');
            mapRes.put('errormsg', ex.getMessage());
            mapRes.put('stack', ex.getStackTraceString());
        }
        return mapRes;
    }
    //Submit提交后。保存申请信息
    @AuraEnabled
    public static Map<String,Object> UpsertRequestInfo(String JSONSubInvotoryInfoString){
        Map<String,Object> mapRes = new Map<String,Object>();
        // try {
            System.debug('JSONSubInvotoryInfoString-->'+JSONSubInvotoryInfoString);
            SubInvotoryInfo objSubInvotoryAllInfo = (SubInvotoryInfo)JSON.deserialize(JSONSubInvotoryInfoString, SubInvotoryInfo.class);
            System.debug('objSubInvotoryInfo-->'+objSubInvotoryAllInfo);
            Sub_inventory_Request__c objSubInvotory = new Sub_inventory_Request__c();
            SubInvotoryRequestInfo objSubInvotoryInfo = objSubInvotoryAllInfo.requestInfo;
            if(!String.isBlank(objSubInvotoryInfo.requestId)){
                objSubInvotory.Id = objSubInvotoryInfo.requestId;
            }
            String strCurrencyIsoCode = objSubInvotoryInfo.currencyCode;
            // TODO, 参数判空处理

            objSubInvotory.Sales_Rep_Name__c = objSubInvotoryInfo.salesRepId;
            objSubInvotory.Request_For__c = objSubInvotoryInfo.salesRepForId;
            objSubInvotory.CurrencyIsoCode = strCurrencyIsoCode;
            objSubInvotory.Request_Status__c = CCM_Constants.DRAFT;
            //先插入
            Database.upsert(objSubInvotory,false);

            // 先删除之前的 sub products
            List<Sub_inventory_Request_Product__c> lstSubProducts = [
                SELECT Id,Name
                FROM Sub_inventory_Request_Product__c WHERE Id != NULL
                AND Sub_Invotory_Request__c = :objSubInvotory.Id
            ];
            Delete lstSubProducts;

            List<SubInvotoryItemInfo> lstInvotoryItemInfo = objSubInvotoryAllInfo.productList;
            System.debug('lstInvotoryItemInfo-->'+lstInvotoryItemInfo);
            List<Sub_inventory_Request_Product__c> lstInvotoryItemProduct = new List<Sub_inventory_Request_Product__c>();
            if(lstInvotoryItemInfo == null || lstInvotoryItemInfo.size() > 0){
                for(SubInvotoryItemInfo objSubInvotoryItem : lstInvotoryItemInfo){
                    Sub_inventory_Request_Product__c objSubInvotoryItemProduct = new Sub_inventory_Request_Product__c();
  
                    objSubInvotoryItemProduct.Sub_Invotory_Request__c = objSubInvotory.Id;
                    objSubInvotoryItemProduct.Model__c = objSubInvotoryItem.model;
                    objSubInvotoryItemProduct.Product_Description__c = objSubInvotoryItem.productId;
                    objSubInvotoryItemProduct.Qty__c = objSubInvotoryItem.qty;
                    objSubInvotoryItemProduct.Inventory__c = objSubInvotoryItem.inventoryStatus;
                    objSubInvotoryItemProduct.subTotal_Price__c = objSubInvotoryItem.subTotal;
                    objSubInvotoryItemProduct.CurrencyIsoCode = strCurrencyIsoCode;
                    objSubInvotoryItemProduct.Is_Tools__c = false;
                    if(objSubInvotoryItem.recordTypeName == 'TLS_Product'){
                        objSubInvotoryItemProduct.Is_Tools__c = true;
                    }
                    lstInvotoryItemProduct.add(objSubInvotoryItemProduct);
                }
            }
            System.debug('requestId:' + objSubInvotory.Id);
            System.debug('requestitem:' + JSON.serialize(lstInvotoryItemProduct));
            upsert lstInvotoryItemProduct;
            List<Database.UpsertResult> urlist = Database.upsert(lstInvotoryItemProduct,false);
            for(Database.UpsertResult ur : urlist){
                if (!ur.isSuccess()) {
                    
                    List<Database.Error> eList = ur.getErrors();
                    System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
                }
            }
            Map<String,Object> mapData = new Map<String,Object>();
            mapData.put('recordId', objSubInvotory.Id);
            mapRes.put('state', 'SUCCESS');
            mapRes.put('data', mapData);
        // } catch (Exception ex) {
        //     mapRes.put('state', 'ERROR');
        //     mapRes.put('errmsg', ex.getStackTraceString());
        //     mapRes.put('stack', ex.getMessage());
        // }
        return mapRes;        
    }
    @AuraEnabled
    public static String queryRepStockByUpdate(String recordId){
        //通过recordId查询request详细信息
        System.debug('recordId信息--->'+recordId);
        RequestDetail objRequestDetail = new RequestDetail();
        try{
            Sub_inventory_Request__c objSubRequest = [
                SELECT Id,Request_For__c,Request_Status__c,Sales_Rep_Name__c,Request_For__r.Name,Sales_Rep_Name__r.Name,Approval_Step__c,
                Name,Total_Qty__c,Total_Value__c,CreatedById,CurrencyIsoCode,
                CreatedBy.Name,CreatedDate,LastModifiedById,LastModifiedBy.Name
                FROM Sub_inventory_Request__c WHERE Id = :recordId LIMIT 1
            ];
            //通过recordId查询Product信息
            List<Sub_inventory_Request_Product__c> lstSubItem  = [
                SELECT  Id,Inventory__c, Model__c,subTotal_Price__c, Name, Product_Description__c, Sub_Invotory_Request__c,
                Product_Description__r.Name, Product_Type__c, Qty__c 
                FROM Sub_inventory_Request_Product__c WHERE Sub_Invotory_Request__c = :recordId
            ];
            Data objData = new Data();
            objData.recordId = recordId;
            objData.requestNo = objSubRequest.Name;
            
            objData.currencyCode = objSubRequest.CurrencyIsoCode;
            objData.salesRepId = objSubRequest.Sales_Rep_Name__c;
            
            objData.salesRepName =  objSubRequest.Sales_Rep_Name__r.Name;
            objData.salesRepFor = objSubRequest.Request_For__c;
            
            objData.salesRepForName =  objSubRequest.Request_For__r.Name;
            objData.approvalStatus = objSubRequest.Request_Status__c;
            
            objData.totalQty =  objSubRequest.Total_Qty__c;
            objData.totalValue = objSubRequest.Total_Value__c;
            
            objData.createdByName =  objSubRequest.CreatedBy.Name;
            objData.lastmodifiedByName = objSubRequest.LastModifiedBy.Name;
            
            objData.createdDate =  date.valueOf(objSubRequest.CreatedDate);
            List<Product> lstProduct = new List<Product>();
            for(Sub_inventory_Request_Product__c objSubItem : lstSubItem){
                Product objProduct = new Product();
                objProduct.productName = objSubItem.Product_Description__r.Name;
                objProduct.productType = objSubItem.Product_Type__c;
                objProduct.model = objSubItem.Model__c;
                objProduct.qty = objSubItem.Qty__c;
                objProduct.requestItemId = objSubItem.Id;
                objProduct.inventoryStatus = objSubItem.Id;
                objProduct.salesPrice = objSubItem.subTotal_Price__c;
                
                lstProduct.add(objProduct);
            }
            objData.productList = lstProduct;
            objRequestDetail.Data = objData;
            
            //获取当前用户Id.查询Role和Profile-->用于判断是否有权限审批
            String userId = UserInfo.getUserId();
            //根据UserId查询Role和Profile信息
            User objUser = [
                Select Id, ProfileId, Profile.Name, UserRoleId, UserRole.Name from User  where Id = :userId LIMIT 1
            ];
            //判断状态为status则Approval_Step__c
            
            if(objSubRequest.Request_Status__c == 'Draft'){
                //草稿状态-->提交人需要等于当前用户
                if(objUser.Id == objSubRequest.CreatedById){
                    objData.blCanApprove = true;
                }else{
                    objData.blCanApprove = false;
                }
            }
            if(objSubRequest.Request_Status__c == 'pending for approval'){
                //判断审批步骤--》该Step 几
                if(objSubRequest.Approval_Step__c == 2){
                    if(objUser.UserRole.Name.contains('Sales Manager')){
                        objData.blCanApprove = true;
                    }else{
                        objData.blCanApprove = false;
                    }
                }else if(objSubRequest.Approval_Step__c == 3){
                    if(objUser.UserRole.Name.contains('Sales Director')){
                        objData.blCanApprove = true;
                    }else{
                        objData.blCanApprove = false;
                    }
                }
            }else{
                objData.blCanApprove = false;
            }
            //如果是系统管理员直接是true
            if(objUser.Profile.Name == Label.ADMIN){
                objData.blCanApprove = true;
            }
        }catch(Exception e){
            objRequestDetail.state = CCM_Constants.ERROR;
            System.debug('报错信息--->'+e.getMessage()+'报错行数--->'+e.getLineNumber());
        }
        objRequestDetail.state = CCM_Constants.SUCCESS;
        System.debug('objRequestDetail-->'+objRequestDetail);
        return JSON.serialize(objRequestDetail);
    }    
    
    public Class CurrentUserInfo{
        
        public String UserName{get;set;}
        public String UserId{get;set;}
        public String UserRole{get;set;}
        public String UserType{get;set;}
    }
    
    public Class SubInvotoryInfo{
        //这个字段在更新的时候才会传
        @AuraEnabled public SubInvotoryRequestInfo requestInfo{get;set;}
        @AuraEnabled public List<SubInvotoryItemInfo> productList{get;set;}
        
    }
    public Class SubInvotoryRequestInfo{
        @AuraEnabled public String requestId{get;set;}
        @AuraEnabled public String salesRepId{get;set;}
        @AuraEnabled public String salesRepForId{get;set;}
        @AuraEnabled public String currencyCode{get;set;}
    }
    public Class SubInvotoryItemInfo{
        //这个字段在更新的时候才会传
        @AuraEnabled public String SubInvotoryItemId{get;set;}
        @AuraEnabled public String productId{get;set;}
        @AuraEnabled public String productName{get;set;}
        @AuraEnabled public String model{get;set;}
        @AuraEnabled public Decimal qty{get;set;}
        @AuraEnabled public String inventoryStatus{get;set;}
        @AuraEnabled public Decimal salesPrice{get;set;}
        @AuraEnabled public Decimal subTotal{get;set;}
        @AuraEnabled public String requestItemId{get;set;}
        @AuraEnabled public String recordTypeName{get;set;}
        
    }
    
    public class RequestDetail{
        public String state {get;set;}
        
        
        public Data data {get;set;}
    }
    public class Data{
        public String recordId {get;set;}
        public String requestNo {get;set;}
        public Boolean blCanApprove {get;set;}
        public String currencyCode {get;set;}
        public String salesRepId {get;set;}
        
        public String salesRepName {get;set;}
        public String salesRepFor {get;set;}
        
        public String salesRepForName {get;set;}
        public String approvalStatus {get;set;}
        
        public Decimal totalQty {get;set;}
        public Decimal totalValue {get;set;}
        
        public String createdByName {get;set;}
        public String lastmodifiedByName {get;set;}
        
        public Date createdDate {get;set;}
        public List<Product> productList {get;set;}
        
    }
    public class Product{
        public String productName {get;set;}
        
        public String productType {get;set;}
        
        public String model {get;set;}
         public String requestItemId {get;set;}
        
        public String inventoryStatus {get;set;}
        public Decimal salesPrice {get;set;}
        public Decimal qty {get;set;}
        
    }
    
    public class ApprovalInfo{
        public String recordId {get;set;}
        
        public String comments {get;set;}
        
        public String action {get;set;}
        
        
    }
}