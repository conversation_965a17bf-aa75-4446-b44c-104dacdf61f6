<aura:component controller="CCM_PurchaseOrderDetailController" access="global" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId">

    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="stepNameList" type="List" default="[]"/>
    <aura:attribute name="currentStep" type="Integer"/>
    <aura:attribute name="customerId" type="String"/>
    <aura:attribute name="customerName" type="String"/>
    <aura:attribute name="pricingDate" type="String"/>
    <aura:attribute name="warehouse" type="String"/>
    <aura:attribute name="recordTypeName" type="String"/>
    <aura:attribute name="purchaseOrderId" type="String"/>
    <aura:attribute name="isDropShip" type="String"/>
    <aura:attribute name="userType" type="String"/>
    <aura:attribute name="actionType" type="String"/>
    <aura:attribute name="maxStep" type="Integer"/>
    <aura:attribute name="freightCost" type="Decimal"/>
    <aura:attribute name="customerType" type="String"/>
    <aura:attribute name="editFreightCost" type="Boolean"/>
    <aura:attribute name="showHelpText" type="Boolean"/>
    <aura:attribute name="currencySymbol" type="String"/>
    <aura:attribute name="contactId" type="String"/>
    <aura:attribute name="isDropAddressInFirst" type="Boolean"/>
    <aura:attribute name="dropshipAddressId" type="String"/>
    
    
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    
    <div class="slds-box slds-theme_default">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium">
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <div class="slds-col slds-size_1-of-1 slds-wrap">
                <c:CCM_ProcessStep currentStep="{!v.currentStep}" stepName="{!v.stepNameList}" />
                <div class="slds-path  slds-p-top_small slds-p-vertical_medium">
                    <aura:if isTrue="{!v.currentStep == 1}">
                        <c:CCM_IN_Service_SelectCustomer currentStep="{!v.currentStep}"
                                                         customerId="{!v.customerId}"
                                                         customerName="{!v.customerName}"
                                                         pricingDate="{!v.pricingDate}"
                                                         warehouse="{!v.warehouse}"
                                                         recordTypeName="{!v.recordTypeName}"
                                                         purchaseOrderId="{!v.purchaseOrderId}"
                                                         isDropShip="{!v.isDropShip}" 
                                                         userType="{!v.userType}"
                                                         actionType="{!v.actionType}"
                                                         currencySymbol="{!v.currencySymbol}"
                                                         contactId="{!v.contactId}"
                                                         isDropAddressInFirst="{!v.isDropAddressInFirst}"
                                                         dropshipAddressId="{!v.dropshipAddressId}"
                        />
                    </aura:if>
                    <aura:if isTrue="{!v.currentStep == 2}">
                        <c:CCM_IN_PartsOrder_SelectProduct currentStep="{!v.currentStep}"
                                                           customerId="{!v.customerId}"
                                                           purchaseOrderId="{!v.purchaseOrderId}"
                                                           pricingDate="{!v.pricingDate}"
                                                           warehouse="{!v.warehouse}"
                                                           recordTypeName="{!v.recordTypeName}"
                                                           isDropShip="{!v.isDropShip}"
                                                           userType="{!v.userType}"
                                                           actionType="{!v.actionType}"
                                                           freightCost="{!v.freightCost}"
                                                           customerType="{!v.customerType}"
                                                           editFreightCost="{!v.editFreightCost}"
                                                           showHelpText="{!v.showHelpText}"
                                                           currencySymbol="{!v.currencySymbol}"
                                                           contactId="{!v.contactId}"
                                                           isDropAddressInFirst="{!v.isDropAddressInFirst}"
                                                           dropshipAddressId="{!v.dropshipAddressId}"
                        />
                    </aura:if>
                    <aura:if isTrue="{!v.currentStep == 3}">
                        <c:CCM_PartsOrder_FillAddress_Cmp currentStep="{!v.currentStep}"
                                                          customerId="{!v.customerId}"
                                                          purchaseOrderId="{!v.purchaseOrderId}"
                                                          userType="{!v.userType}"
                                                          actionType="{!v.actionType}"
                                                          isDropShip="{!v.isDropShip}"
                                                          freightCost="{!v.freightCost}"
                                                          customerType="{!v.customerType}"
                                                          editFreightCost="{!v.editFreightCost}"
                                                          showHelpText="{!v.showHelpText}"
                                                          currencySymbol="{!v.currencySymbol}"
                                                          contactId="{!v.contactId}"
                                                          isDropAddressInFirst="{!v.isDropAddressInFirst}"
                        />
                    </aura:if>

                    <aura:if isTrue="{!v.currentStep == 4}">
                        <c:CCM_PartsOrder_Detail currentStep="{!v.currentStep}"
                                                 customerId="{!v.customerId}"
                                                 purchaseOrderId="{!v.purchaseOrderId}"
                                                 actionType="{!v.actionType}"
                                                 userType="{!v.userType}"
                                                 currencySymbol="{!v.currencySymbol}"
                                                 contactId="{!v.contactId}"
                        />
                    </aura:if>
                </div>
            </div>
        </div>
    </div>
    
</aura:component>