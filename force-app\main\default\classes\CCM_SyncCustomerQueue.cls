global class CCM_SyncCustomerQueue implements Queueable, Database.AllowsCallouts  {

    private String recordId;

    // public CCM_SyncCustomerQueue(String recordId) {
    //     this.recordId = recordId;
    // }

    private List<Alert_Message__c> alertMessges;
    private Integer index;
    private Boolean syncWhenApproved;
    private Boolean agRemoved;
    private String oldAGAccountNumber;

    public CCM_SyncCustomerQueue(String recordId, List<Alert_Message__c> alertMessges, Boolean syncWhenApproved, Boolean agRemoved, String oldAGAccountNumber, Integer index) {
        this.alertMessges = alertMessges;
        this.index = index;
        this.recordId = recordId;
        this.syncWhenApproved = syncWhenApproved;
        this.agRemoved = agRemoved;
        this.oldAGAccountNumber = oldAGAccountNumber;
    }

    global void execute(QueueableContext context) {
        if(alertMessges.isEmpty()) {
            if(syncWhenApproved) {
                String result = CCM_PushCustomerInfoService.pushCustInfo(this.recordId, this.agRemoved, this.oldAGAccountNumber, null);
                System.debug('Push customer result: ' + result);
            }
            else {
                String result = CCM_Service.pushCustInfo(this.recordId, null);
                System.debug('Push customer result: ' + result);
            }
        }
        else {
            Integer size = alertMessges.size();
            if(size == index) {
                return;
            }
            if(syncWhenApproved) {
                String result = CCM_PushCustomerInfoService.pushCustInfo(this.recordId, this.agRemoved, this.oldAGAccountNumber, alertMessges[index]);
                System.debug('Push customer result: ' + result);
            }
            else {
                String result = CCM_Service.pushCustInfo(this.recordId, alertMessges[index]);
                System.debug('Push customer result: ' + result);
            }
            this.index += 1;
            System.enqueueJob(new CCM_SyncCustomerQueue(this.recordId, this.alertMessges, this.syncWhenApproved, this.agRemoved, this.oldAGAccountNumber, this.index));
        }

    }
}