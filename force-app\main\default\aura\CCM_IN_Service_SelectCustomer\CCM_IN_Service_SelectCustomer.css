.THIS .label-wrap {
    width: 100%;
    padding: 0 50px;
}
.THIS .label-wrap .cCCM_Field {
    width: auto;
    display: flex !important;
    margin-bottom: 10px;
}
.THIS .label-wrap .slds-form-element__control {
    padding-top: 12px;
}
.THIS .c-container .cCCM_Community_LookUp {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}
.THIS .c-container .cCCM_Community_LookUp .slds-combobox__form-element {
    margin-top: 0px !important;
}
.THIS .c-container .cCCM_Community_LookUp .slds-form-element__control {
    width: 100% !important;
}
.THIS .label-wrap .CCM_Label {
    min-width: 250px;
}
.THIS .field-required .slds-form-element__control::before{
    content:"*";
    display: inline-block;
    transform: scale(1.5);
    position:relative;
    color:rgb(194, 57, 52);
    position: absolute;
    left: -.5rem;
    top: 50%;
    transform: translateY(-50%);
}
.THIS .field-error.input{
    border: 2px solid rgb(194, 57, 52);
}
.THIS .field-error input, .THIS .field-error .slds-dueling-list__options{
    border:2px solid rgb(194, 57, 52);
}
.THIS .required-wrap {
    position: relative;
}
.THIS .error-text{
    color: rgb(194, 57, 52);
    display: flex;
    justify-content: end;
    position: absolute;
    bottom: -18px;
    right: 0;
}
.THIS .search-wrap {
    margin-bottom: 18px;
}
.THIS .address-wrap .slds-combobox__input {
    width: 100%;
}
.THIS .address-content {
    margin-top: 10px;
    margin-left: 0px;
}
.THIS .combobox-label {
    word-wrap: break-word;
    display: inline-block;
    color: var(--slds-g-color-neutral-base-30, var(--lwc-colorTextLabel,rgb(68, 68, 68)));
    font-size: var(--lwc-formLabelFontSize,0.75rem);
    padding-right: var(--lwc-spacingXSmall,0.5rem);
    padding-top: var(--lwc-spacingXxSmall,0.25rem);
    margin-bottom: var(--lwc-spacingXxxSmall,0.125rem);
}
.THIS .address-wrap .error-text {
    bottom: -8px !important;
    right: 12px !important;
}