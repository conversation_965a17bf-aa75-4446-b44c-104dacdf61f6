@IsTest
public without sharing class CCM_Community_AccountBalanceTabCtlTest {
    @TestSetup
    static void makeData(){
        Invoice__c inv1 = new Invoice__c();
        inv1.Credit_note_for_Invoice__c = 'INV';
        inv1.Invoice_Number__c = 'INV1';
        inv1.Invoice_Date__c = System.today().addDays(-40);
        
        Invoice__c inv2 = new Invoice__c();
        inv2.Credit_note_for_Invoice__c = 'CREDIT';
        inv2.Invoice_Number__c = 'CREDIT1';
        inv2.Invoice_Date__c = System.today().addDays(-40);

        List<Invoice__c> lstInvs = new List<Invoice__c>();
        lstInvs.add(inv1);
        lstInvs.add(inv2);
        
        insert lstInvs;

        List<Accounting_Balance__c> lstAbs = new List<Accounting_Balance__c>();

        Accounting_Balance__c ab1 = new Accounting_Balance__c();
        ab1.Invoice_Number__c = 'inv1';
        ab1.Invoice_Date__c = System.today().addDays(-10);
        ab1.Gl_Date__c = System.today();
        ab1.Due_Date__c = System.today();
        ab1.Amt_Original__c = 100.00;
        ab1.Amt_Due_Remaining__c = 100.00;
        ab1.Invoice__c = inv1.Id;
        ab1.Due_Date__c = System.today();
        ab1.Credit_note_for_Invoice__c = 'CREDIT';
        
        Accounting_Balance__c ab2 = new Accounting_Balance__c();
        ab2.Invoice_Number__c = 'inv1';
        ab2.Invoice_Date__c = System.today().addDays(-10);
        ab2.Gl_Date__c = System.today();
        ab2.Due_Date__c = System.today();
        ab2.Amt_Original__c = 100.00;
        ab2.Amt_Due_Remaining__c = 100.00;
        ab2.Invoice__c = inv2.Id;
        ab2.Due_Date__c = System.today();

        lstAbs.add(ab1);
        lstAbs.add(ab2);

        insert lstAbs;

    }

    @IsTest
    public static void test_searchBalance() {
        Integer pageNumber1 = 1;
        Integer pageSize1 = 10;
        String invoiceDateRange1 = '';
        String memoType1 = 'INV';


        Integer pageNumber2 = 1;
        Integer pageSize2 = 10;
        String invoiceDateRange2 = 'last30';
        String memoType2 = 'CREDIT';

        
        CCM_Community_AccountBalanceTabCtl.searchBalance(pageNumber1, pageSize1, invoiceDateRange1, memoType1, '', '', '');
        CCM_Community_AccountBalanceTabCtl.searchBalance(pageNumber2, pageSize2, invoiceDateRange2, memoType2, '', '', '');
    }
}