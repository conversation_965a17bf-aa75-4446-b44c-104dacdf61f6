({
    getUrlParameter : function(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    },
    showToast : function(title, message, type){
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type" : type
        });
        toastEvent.fire();
    },
    fileByBase64 : function(file, component, attachmentName) {
        console.log(file, attachmentName, 'file===========123123');
        const self = this;
        const reader = new FileReader();
        // 传入一个参数对象即可得到基于该参数对象的文本内容
        reader.readAsDataURL(file[0]);
        reader.onload = function (e) {
            // target.result 该属性表示目标对象的DataURL
            let fileStr = e.target.result;
            let index = fileStr.indexOf(',') + 1;
            let fileValue = fileStr.substring(index);
            self.getFileId(component, fileValue, file[0].name, attachmentName);
            // let uploadItem = component.get('v.attachmentItem');
            // uploadItem.base64 = fileValue;
            // // 存附件
            // component.set('v.attachmentItem', uploadItem);
        };
    },
    // 保存附件
    uplaodFileEvent : function(component) {
        var action = component.get('c.uploadFile');
        let attachmentList = JSON.parse(JSON.stringify(component.get('v.attachment')));
        console.log(attachmentList, 'attachmentList---------------');
        let paramsArr = [];
        attachmentList.forEach((item)=>{
            paramsArr.push({
                contentId: item.contentId,
                fileType: item.attachmentType,
                fileName: item.attachmentName,
                fileDate: item.attachmentDate,
            })
        })
        action.setParams({
            purchaseOrderId: component.get('v.recordId'),
            lstuploadFileInfo: paramsArr
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, '保存附件-----------------');
            if (state === 'SUCCESS' ) {
                var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Success"),
                   "message": $A.get("$Label.c.CCM_AttachmentSaveSuccess"),
                   "type": "success"
               }).fire();
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 同步数据
    syncEvent : function(component) {
        var action = component.get('c.SubmitAndSync');
        action.setParams({
            PurchaseOrderId: component.get('v.recordId'),
            TotalDueAmount: Number(component.get('v.TotalDueAmount')),
            CountLine: component.get('v.CountLine'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = response.getReturnValue();
            console.log(result, '同步数据-----------------');
            if (state === 'SUCCESS' ) {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Success"),
                    "message": $A.get("$Label.c.CCM_SyncToEBSSuccess"),
                    "type": "success"
                }).fire();
                let url = window.location.origin + '/lightning/n/Purchase_Order_List';
                window.open(url, '_self');
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // customer提示信息
    queryAlertMessage : function(component, customerNumber) {
        var action = component.get('c.queryAlertMessage');
        action.setParams({
            CustomerNumber: customerNumber,
            AlertMode: 'Create',
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'customer提示信息=========');
            if (state === 'SUCCESS' ) {
                var result = response.getReturnValue();
                console.log(result, 'customer提示信息 result=========');
                var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Success"),
                   "message": result,
                   "type": "success",
                   "duration": "pester"
               }).fire();
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 存附件
    getFileId : function(component, content, name, attachmentName) {
        var action = component.get('c.uploadFileMidel');
        action.setParams({
            content: content,
            uploadFileName: name,
            fileName: attachmentName
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, '存附件=========');
            if (state === 'SUCCESS' ) {
                var result = JSON.parse(response.getReturnValue());
                console.log(result, '存附件 result=========');
                let uploadItem = component.get('v.attachmentItem');
                uploadItem.contentId = result.ContentId;
                component.set('v.attachmentItem', uploadItem);
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": $A.get("$Label.c.CCM_Error"),
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 刷新红绿灯
    refreshLight: function(component) {
        let self = this;
        console.log('刷新红绿灯=========');
        component.set('v.isBusy', true);
        var action = component.get("c.refreshInventory");
        action.setParams({
            purchaseOrderId: component.get('v.recordId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                self.getBaseInfo(component);
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Success"),
                    "message": $A.get("$Label.c.CCM_RefreshTraficLightSuccess"),
                    "type": "success"
                }).fire();
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 取消同步
    cancelSyncEvent: function(component) {
        console.log('取消同步=========');
        component.set('v.isBusy', true);
        var action = component.get("c.CancelOrder");
        action.setParams({
            purchaseOrderId: component.get('v.recordId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                console.log(results, '取消同步==========');
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Success"),
                    "message": $A.get("$Label.c.CCM_Success"),
                    "type": "success"
                }).fire();
                let url = window.location.origin + '/lightning/n/Purchase_Order_List';
                window.open(url, '_self');
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    getBaseInfo: function(component) {
        var action = component.get("c.QueryPirchaseAndItemInfo");
        action.setParams({
            PurchaseOrderId: component.get('v.recordId'),
            IsProtal: false,
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                component.set('v.orderItemList', results.lstPurchaseOrderItem);
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
     // 获取信息回填
     queryRegisterInfo : function(component) {
        var action = component.get("c.queryOrderInfo");
        action.setParams({
            "recordId":component.get('v.recordId'),
        });
        action.setCallback(this, function (response) {
            console.log(JSON.parse(JSON.stringify(response)),"cccc");
            var state = response.getState();
            component.set('v.isBusy',false)
            console.log('state--->>>>>>>>>eeeeeee',state,state == "SUCCESS");
            if (state == "SUCCESS") {
                // 两套进度条
                // 1:同步前
                var requestprogressbar = [
                    {label: 'New', icon: 'edit_form'},                                                      // 新建
                    {label: $A.get("$Label.c.Order_PendingReview"), icon: 'description'},                   // 待审批
                    {label: "Reserved", icon: 'locker_service_api_viewer'},                                 // 保留
                    {label: "Confirmed", icon: 'entitlement'},                                              // 审批通过
                    {label: "Cancelled", icon:'cancel_file_request'}                                        // 关闭
                ];
                // 2:同步后
                var orderprogressbar = [
                    {label: 'Order Processing', icon: 'edit_form'},             // 订单处理
                    {label: 'Invoiced', icon: 'locker_service_api_viewer'},     // 发票
                    {label: "Cancelled", icon: 'cancel_file_request'},          // 关闭
                ];
                var results = response.getReturnValue();
                results.lstmapAccachmentInfo.forEach(item => {
                    item.contentId = item.fileId
                    item.attachmentName = item.fileName
                    item.attachmentType = item.fileType
                    item.attachmentDate = item.fileDate
                })
                /*if(results.mapItemInfo.length){
                    results.mapItemInfo.forEach((item,index) => {
                        results.mapItemInfo[index].participants = results.lstparticipants[index]
                    });
                }*/
                console.log("获取表格信息",JSON.stringify(results.mapItemInfo));
                component.set('v.producelist',results.mapItemInfo);
                
                component.set('v.basicInformation',results);
                component.set('v.attachment',results.lstmapAccachmentInfo);
                console.log("attachment信息===>",JSON.stringify(results.lstmapAccachmentInfo));
                console.log(JSON.parse(JSON.stringify(results)),"basicInformation");
           


                // Comfirm按钮
                component.set('v.isComfirm', results.isComfirm);
                console.log("获取表格信息3");

                let basicInformation = component.get('v.basicInformation')
                console.log("获取表格信息5");
                basicInformation.BillToAddressInfo = {
                    CompanyName:   results.addressName,
                    City:          results.addressCity,
                    Country:       results.addressCountry,
                    PostalCode:    results.addressPostalCode
                }
                basicInformation.ShipToAddressInfo = {
                    CompanyName:   results.shipAddressCompanyName,
                    City:          results.shipAddressCity,
                    Country:       results.shipAddressCountry,
                    PostalCode:    results.shipAddressPostalCode
                }
                basicInformation.VAT = results.vat
                basicInformation.TrainingAmount = results.trainingAmount
                basicInformation.TotalDueAmount = results.totalAmount
                /*if(basicInformation.approvalInfo.submitDate){
                    basicInformation.approvalInfo.submitDate = basicInformation.approvalInfo.submitDate.substring(0, 19).replace("T"," ")
                }*/
                component.set('v.currencySymbol',results.currencyCode)
                component.set('v.basicInformation',results)
                console.log(JSON.parse(JSON.stringify(results)),"resultsInfo");
                if(results.isOrder == "true"){
                    // 隐藏approva history的section
                    // 所有request替换为order
                    // 另一套进度条展示
                    component.set('v.processData', orderprogressbar);
                    if(results.status == "Order Processing"){
                        component.set('v.currentStep',1)
                    }else if(results.status == "Order Completed"){
                        component.set('v.currentStep',2)
                    }else{
                        component.set('v.currentStep',3)
                    }
                    component.set('v.requestOrorder',"Order")
                    component.set('v.beforeSyncId',results.beforeSyncId)
                }else{
                    component.set('v.processData', requestprogressbar);
                    if(results.status == "Draft"){
                        component.set('v.currentStep',1)
                    }else if(results.status == "Submitted"){
                        component.set('v.currentStep',2)
                    }else if(results.status == "Reserved"){
                        component.set('v.currentStep',3)
                    }else if(results.status == "Confirmed"){
                        component.set('v.currentStep',4)
                    }else{
                        component.set('v.currentStep',5)
                    }
                    if(results.afterSyncId){
                        component.set('v.afterSyncId',results.afterSyncId)
                    }
                }

                console.log(JSON.parse(JSON.stringify(results)),"获取信息回填");
            }
        });
        $A.enqueueAction(action);
    },
    // 审批拒绝或通过的方法
    approvalOrRejectRegister : function(component,actionstr) {
        var action = component.get("c.approvalOrRejectRegister");
        action.setParams({
            JsonApprovalInfoString: JSON.stringify({
                "recordId":      component.get('v.recordId'),
                "comments":      component.get('v.comments'),
                "action":        actionstr,
            })
        });
        action.setCallback(this, function (response) {
            console.log(JSON.parse(JSON.stringify(response)),"返回审批后的数据");
            var state = response.getState();
            console.log('state--->>>>>>>>>',state,state == "SUCCESS");
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                console.log(JSON.parse(JSON.stringify(results)),"返回审批后的数据");
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Success"),
                    "message": $A.get("$Label.c.CCM_Success"),
                    "type": "success",
                }).fire();
                setTimeout(() => {
                    let url = window.location.origin + '/lightning/n/TrainingOrderView?0.recordId=' + component.get('v.recordId');
                    window.open(url, '_self'); 
                }, 1000);
            }
        });
        $A.enqueueAction(action);
    },
    // 必填校验
    getValidation : function (component) {
        let self = this;
        let valid1 = self.getElementRequiredError(component, 'cancellationReason');
        return valid1;
    },
    // 校验错误提示信息
    getElementRequiredError : function (component, ele) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = element.get('v.value');
        var valid = !!val;
        console.log(valid, 'valid-------------');
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
})