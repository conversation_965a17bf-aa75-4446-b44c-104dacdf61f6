/**
 * Created by gluo006 on 8/28/2019.
 */
({
    doInit: function (component, event, helper) {
        var productObj = component.get('v.selectedValue');
        if (productObj && productObj.Name) {
            component.set('v.inputVal', productObj.Name);
            component.set('v.inputValList', [productObj.Name]);
            $A.util.removeClass(component.find('lookup-product'), 'field-error');
        }
        else if(component.get('v.inputVal')) {
            component.set('v.inputValList', [component.get('v.inputVal')]);
            $A.util.removeClass(component.find('lookup-product'), 'field-error');
        }
        let fieldNameLabel = component.get('v.fieldNameLabel');
        let fieldName = component.get('v.fieldName');
        if(!fieldNameLabel && fieldName) {
            fieldNameLabel = fieldName;
            component.set('v.fieldNameLabel', fieldNameLabel);
        }
    },
    //clear the product select
    handleProductClear:function(component){
        component.set('v.inputVal', '');

    },
    onSelectedValueChange: function (component, event, helper) {
        let selectedValue = component.get('v.selectedValue');
        if(selectedValue) {
            let productObj = JSON.parse(JSON.stringify(component.get('v.selectedValue')));
            if ((productObj && productObj.Name)) {
                component.set('v.inputVal', productObj.Name);
                component.set('v.inputValList', [productObj.Name]);
                $A.util.removeClass(component.find('lookup-product'), 'field-error');
            }else{
                component.set('v.inputValList', '');
                $A.util.addClass(component.find('lookup-product'), 'field-error');
            }
        }
        else {
            component.set('v.inputValList', '');
            $A.util.addClass(component.find('lookup-product'), 'field-error');
        }
    },
    hideProductList:function(component, event, helper){
        let inputVal = JSON.parse(JSON.stringify(component.get('v.inputVal')));
        let inputValList = JSON.parse(JSON.stringify(component.get('v.inputValList')));
        if (!inputValList[0]) {
            component.set('v.selectedValue', {Id: "", Name: ''});
            helper.fireEvent(component,'onSelect', {value: {Id: "", Name: ''}, label: 'productInfo'});
        }
        if (inputVal !== inputValList[0]) {
            console.log(inputVal,inputValList, 'diff-------------');
            component.set('v.selectedValue', {Id: "", Name: ''});
            helper.fireEvent(component,'onSelect', {value: {Id: "", Name: ''}, label: 'productInfo'});
        }
        if(!component.get('v.scrollBarDown')){
            component.set('v.dropDownOpen', false);
        	component.set('v.isSearching', false);
        }
        component.set('v.scrollBarDown', false);
        component.set('v.productList', []);
    },
    onInputChange: function (component, event, helper) {
        component.set('v.scrollBarDown', false);
        component.set('v.modelNumberIndex', event.currentTarget.dataset.id);
        var value = event.currentTarget.value;
        component.set('v.dropDownOpen', value.length >= 0);
        component.set('v.inputVal', value);
        // 父子传参
        if (component.get('v.fieldName') == $A.get("$Label.c.CCM_OrderNo")) {
            const button = document.getElementById('orderBtn');
            button.click();
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_InvoiceNo")) {
            const button = document.getElementById('invoiceBtn');
            button.click();
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_ShipmentNo")) {
            const button = document.getElementById('shipmentBtn');
            button.click();
        }
        helper.getProductList(component);
    },
    avoidBlur:function(component, event, helper){
        component.set('v.scrollBarDown', true);
    },
    onSelectItem: function (component, event, helper) {
        var index = event.currentTarget.dataset.index;
        var dataSelected = component.get('v.productList')[index];
        var inputValList = component.get('v.inputValList');
        // 去重
        let addInputFlag = true;
        
        if (addInputFlag) {
            inputValList[component.get('v.modelNumberIndex')] = dataSelected.Name;
        }else{
            inputValList[component.get('v.modelNumberIndex')] = '';
        }
        component.set('v.inputVal', dataSelected.Name);
        component.set('v.inputValList', inputValList);
        component.set('v.selectedValue', dataSelected);

        component.set('v.dropDownOpen', false);
        component.set('v.productList', []);
        var cmpEvent = component.getEvent("getProductChange");
        if(component.get('v.fieldName') == 'Purchase place:'){
            cmpEvent.setParams({"purchasePlace" : productObj.Name});
        }else if(component.get('v.fieldName') == '' || component.get('v.fieldName') == 'Repairable Parts'){
          cmpEvent.setParams({"masterProductId" : dataSelected});
          cmpEvent.setParams({'communityPartsIndex': component.get('v.fieldName')});
          cmpEvent.setParams({'rowIndex': component.get('v.index')});
        }else{
            cmpEvent.setParams({"masterProductId" : dataSelected.Id});
        }
        console.log('222');
        console.log(dataSelected);
        cmpEvent.fire();
        // 父子传参
        if (component.get('v.fieldName') == $A.get("$Label.c.CCM_OrderNo")) {
            const button = document.getElementById('orderBtn');
            button.click();
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_InvoiceNo")) {
            const button = document.getElementById('invoiceBtn');
            button.click();
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_ShipmentNo")) {
            const button = document.getElementById('shipmentBtn');
            button.click();
        };
        //onSelect return v.value's value
        helper.fireEvent(component,'onSelect', {value: dataSelected, label: 'productInfo'});
    },
    onParamsChanged: function (component) {
        component.set('v.selectedValue', {});

        component.set('v.inputVal', '');
        component.set('v.dropDownOpen', false);
    },
    clearDropdownOptions: function (component) {
        component.set("v.productList", []);
    },
    addModelNumber: function (component) {
        var inputValList = component.get('v.inputValList');
        inputValList.push('');
        component.set("v.inputValList", inputValList);
    },
    delModelNumber: function (component, event, helper) {
        var inputValList = component.get('v.inputValList');
        var delIndex = event.getSource().get("v.alternativeText");
        let productCode = inputValList[delIndex];
        inputValList.splice(delIndex,1);
        component.set("v.inputValList", inputValList);
        // 调用父组件的方法，通过productCode删除原数组里的内容
        productCode = productCode.substring(0,productCode.indexOf(" "));
        var cmpEvent = component.getEvent("delModelNumber");
        cmpEvent.setParams({"productCode" : productCode});
        cmpEvent.fire();
    },
    setInputVal: function(component, event, helper) {
        component.set('v.inputValList', event.getParam('arguments'));
        $A.util.removeClass(component.find('lookup-product'), 'field-error');
    }
})