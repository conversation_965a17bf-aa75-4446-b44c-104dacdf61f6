<aura:component description="CCM_ProductRegistration" extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout"
    access="global" controller="CCM_ProductRegistration">

<!-- 产品注册用户类别 -->
    <aura:attribute name="showResidentialInfo" type="String" default="true"/>
    <aura:attribute name="showCommercialInfo" type="String" default="false"/>

    <aura:attribute name="organizationName" type="String" default=""/>
    <aura:attribute name="firstName" type="String" default=""/>
    <aura:attribute name="lastName" type="String" default=""/>
    <aura:attribute name="addressLine1" type="String" default=""/>
    <aura:attribute name="address" type="String" default=""/>
    <aura:attribute name="emailAddress" type="String" default=""/>
    <aura:attribute name="phone" type="String" default=""/>
    <aura:attribute name="postCode" type="String" default=""/>
    <aura:attribute name="city" type="String" default=""/>
    <aura:attribute name="state" type="String" default=""/>
    <aura:attribute name="country" type="String" default=""/>
    <aura:attribute name="street" type="String" default=""/>

    <!-- 表格参数 -->
    <aura:attribute name="tableData" type="Object" default="[]"/>
    <aura:attribute name="columns" type="List"/>
    <aura:attribute name="exportData" type="List" default="[]"/>
    <aura:attribute name="parseData" type="List" default="[]"/>
    <aura:attribute name="draftValues" type="List" default="[]"/>
    <aura:attribute name="selectList" type="List" default="[]"/>

    <aura:attribute name="purchaseDate" type="Date" default=""/>
    <aura:attribute name="brandName" type="String" default="EGO"/>
    <aura:attribute name="masterModelnumber" type="String" default="" />
    <aura:attribute name="purchasePlace" type="String" default="" />
    <aura:attribute name="toDay" type="String" default="" />


    <!--product information-->
    <aura:attribute name="modelNumber" type="String" default="product"/>
    <aura:attribute name="ProductList" type="List" default=""/>
    <aura:attribute name="masterProductObj" type="Map" default=""/>
    <aura:attribute name="purchasePlaceObj" type="Map" default=""/>
    <aura:attribute name="brandList" type="List" default=""/>
    <aura:attribute name="purchasePlaceList" type="List" default="['Online Dealers', 'Stationary Stores'​]"/>
    <aura:attribute name="purchaseUseTypeList" type="List" default=""/>
    <aura:attribute name="productListData" type="List"/>
    <aura:attribute name="firstSave" type="Boolean" default="true" />
    <aura:attribute name="errortip" type="Boolean" default="false" />
    <aura:attribute name="uploadFileName" type="String" default="" />
    <aura:attribute name="contentId" type="String" default="" />
    <aura:attribute name="Company" type="String" default="" />
    <aura:attribute name="fleetManager" type="String" default="" />
    <aura:attribute name="fleetManagerEmail" type="String" default="" />
    <!-- <aura:attribute name="errorinfo" type="Map" default="{}" /> -->
    <aura:attribute name="errorList" type="List" default="[]" />
    <!-- yanko add -->
    <aura:attribute name="isNew" type="Boolean" default="true" />
    <aura:attribute name="submitFlag" type="Boolean" default="true" />
    <aura:attribute name="isDisabled" type="Boolean" default="false" />
    <aura:attribute name="isPending" type="Boolean" default="false" />
    <aura:attribute name="isActive" type="Boolean" default="true" />
    <aura:attribute name="isInactive" type="Boolean" default="false" />


    <aura:attribute name="isReceiveInfo" type="List" default="[
    {'label': 'Yes', 'value': 'true'},
    {'label': 'No', 'value': 'false'}
    ]"/>
    <aura:attribute name="isVisitWebsite" type="List" default="[
    {'label': 'Yes', 'value': 'true'},
    {'label': 'No', 'value': 'false'}
    ]"/>
    <aura:attribute name="options" type="List" default="[]"/>
    <aura:attribute name="visitWebsiteValue" type="String" default="true"/>
    <aura:attribute name="receiveInfoValue" type="String" default="true"/>
    <aura:attribute name="targetRecord" type="Object" access="private"/>
    <aura:attribute name="targetFields" type="Object" access="private"/>
    <aura:attribute name="targetError" type="String" access="private"/>
    <aura:attribute name="lostReceipt" type="Boolean" access="private" default="false"/>
    <aura:attribute name="registrationType" type="String" default="residentialUser"/>
    <aura:attribute name="commercialId" type="String" default=""/>

    <!--file upload-->
    <aura:attribute name="multiple" type="Boolean" default="true"/>
    <aura:attribute name="accept" type="List" default="['.jpg', '.png']"/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="disabled" type="Boolean" default="false"/>

    <aura:attribute name="fileList" type="List" default="[]"/>
    <aura:attribute name="receiptList" type="List" default="[]"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="resetFlag" type="Boolean" default="true"/>
    <aura:attribute name="receiptUrl" type="Map" default="{}"/>

<!-- 显示内容 -->
<!-- <aura:attribute name="hiddenSectionInfo" type="Boolean" default="true" /> -->

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <!-- <aura:handler event="c:CCM_ProductLookUpChange" name="delModelNumber" action="{!c.delMasterProductByProductCode}"/> -->

    <div class="slds-grid slds-grid_align-space">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed loading-wrap' : 'slds-hide' }"/>
        <section class="slds-p-around_x-small">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="Organization information">
                                        <span><strong>Organization information</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <div class="slds-grid slds-wrap slds-align_absolute-center width80">
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div>
                                <strong>Dealer/Distributor Name: </strong>
                                <span>{!v.organizationName}</span>
                            </div>
                        </div>
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                            <div>
                                <strong>Registration source: </strong>
                                <span> CRM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            <!-- Owner information -->
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                    title="Owner information">
                                        <span><strong>Owner information</strong></span>
                                </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <!-- <aura:if isTrue="{!v.isPrivacy}">
                        <p class="show-help-text">
                            <span>* This user has been registered before, due to the user's privacy setting, the users' information is hidden from the dealers</span>
                        </p>
                    </aura:if> -->
                    <aura:if isTrue="{!v.isPending}">
                        <p class="show-help-text">
                            <span>* The user did not activate the account ,please let consumer to login in brand website to set password </span>
                        </p>
                    </aura:if>
                    <aura:if isTrue="{!v.isInactive}">
                        <p class="show-help-text">
                            <span>* This user has been registered before, due to the user’s privacy setting, the users’ information is hidden from the dealers​</span>
                        </p>
                    </aura:if>
                    <!-- 个人注册组织信息 -->
                    <aura:if isTrue="{!v.showResidentialInfo}">
                        <div class="slds-grid slds-wrap slds-align_absolute-center width80 residential-wrap">
                            <div class="left-wrap">
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                    <lightning:input type="Email" aura:id="emailAddress" label="Email address:" value="{!v.emailAddress}" class="field-required" onblur="{!c.emailOrPhoneGetCustomerInfo}" maxlength="255"/>
                                    <div aura:id="emailAddress-error-required" class="error-text slds-hide">This field isrequired</div>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input aura:id="firstName" label="First name:" value="{!v.firstName}" class="field-required slds-text-align_right" onblur="{!c.chengeFirstName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <div aura:id="firstName-error-required" class="error-text slds-hide">This field isrequired</div>
                                        <aura:set attribute="else">
                                            <lightning:input label="First name:" value="" class="slds-text-align_right" onblur="{!c.chengeFirstName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input aura:id="lastName" label="Last name:" value="{!v.lastName}" class="field-required slds-text-align_right" onblur="{!c.chengeLastName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <div aura:id="lastName-error-required" class="error-text slds-hide">This field is required</div>
                                        <aura:set attribute="else">
                                            <lightning:input label="Last name:" value="" class="slds-text-align_right" onblur="{!c.chengeLastName}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                    <aura:if isTrue="{!v.isNew}">
                                        <c:CCM_Community_LookUp aura:id="country"
                                                            fieldName="Country"
                                                            selectedValue="{!v.country}"
                                                            class="field-required"
                                                            isDisabled="{!v.isDisabled}"
                                                            onSelect="{!c.changeCountry}"
                                        />
                                        <div aura:id="country-error-required" class="error-text slds-hide">This field is required</div>
                                        <aura:set attribute="else">
                                            <lightning:input label="Country:" value="" class="slds-text-align_right" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if> 
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small form-left-wrap">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input aura:id="postCode" label="Postcode:" value="{!v.postCode}" class="field-required slds-text-align_right" onblur="{!c.changePostCode}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <div aura:id="postCode-error-required" class="error-text slds-hide">This field is required</div>
                                        <aura:set attribute="else">
                                            <lightning:input label="Postcode:" value="" class="slds-text-align_right" onblur="{!c.changePostCode}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                            </div>
                            <div class="slds-text-align--right">
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input label="Phone:" value="{!v.phone}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <aura:set attribute="else">
                                            <lightning:input label="Phone:" value="" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small addressPosition">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input aura:id="address" label="Address:" value="{!v.address}" class="slds-text-align_right" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <aura:set attribute="else">
                                            <lightning:input aura:id="address" label="Address:" value="" class="slds-text-align_right" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small addressPosition">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input aura:id="street" label="Street/No.:" value="{!v.street}" class="field-required slds-text-align_right" onblur="{!c.changeStreet}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <div aura:id="street-error-required" class="error-text slds-hide">This field is required</div>
                                        <aura:set attribute="else">
                                            <lightning:input label="Street/No.:" value="" class="slds-text-align_right" onblur="{!c.changeStreet}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input aura:id="city" label="City:" value="{!v.city}" class="field-required slds-text-align_right" onblur="{!c.changeCity}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <div aura:id="city-error-required" class="error-text slds-hide">This field is required</div>
                                        <aura:set attribute="else">
                                            <lightning:input label="City:" value="" class="slds-text-align_right" onblur="{!c.changeCity}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                    <aura:if isTrue="{!v.isActive}">
                                        <lightning:input aura:id="state" label="State/Province:" value="{!v.state}" class="field-required slds-text-align_right" onblur="{!c.changeState}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        <div aura:id="state-error-required" class="error-text slds-hide">This field is required</div>
                                        <aura:set attribute="else">
                                            <lightning:input label="State/Province:" value="" class="slds-text-align_right" onblur="{!c.changeState}" maxlength="255" disabled="{!v.isDisabled}"/>
                                        </aura:set>
                                    </aura:if>
                                </div>
                            </div>
                        </div>
                    </aura:if>
                    <!-- 公司注册组织信息 -->
                    <aura:if isTrue="{!v.showCommercialInfo}">
                        <div class="slds-grid slds-wrap slds-align_absolute-center width80 commercial-info-wrap">
                            <!-- Company -->
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required slds-col slds-size_1-of-3">
                                <div>
                                    <strong>Company: </strong>
                                    <span>{!v.Company}</span>
                                </div>
                            </div>
                            <!-- Fleet Manager -->
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required slds-col slds-size_1-of-3">
                                <div>
                                    <strong>Fleet Manager: </strong>
                                    <span>{!v.fleetManager}</span>
                                </div>
                            </div>
                            <!-- Fleet Manager Email -->
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required slds-col slds-size_1-of-3">
                                <div>
                                    <strong>Fleet Manager Email: </strong>
                                    <span>{!v.fleetManagerEmail}</span>
                                </div>
                            </div>
                        </div>
                    </aura:if>
                </div>
            </article>
            <!-- Product information -->
            <article class="slds-card relative">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                title="Product information">
                                    <span><strong>Product information</strong></span>
                            </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <!-- end -->
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small productSection slds-clearfix">
                    <div class="select-wrap">
                        <!-- Purchase date -->
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small datePicker field-required select-item date-required">
                            <label class="slds-form-element__label" style="margin-top: 5px;">Purchase Date:</label>
                            <lightning:input aura:id="purchaseDate" type="date" class="date-box-item slds-p-top--xx-small" variant="label-hidden" value="{!v.purchaseDate}" max="{!v.toDay}"/>
                            <!-- <ui:inputDate aura:id="purchaseDate" value="{!v.purchaseDate}" displayDatePicker="true" format="MM/dd/yy" /> -->
                            <div aura:id="purchaseDate-error-required" class="error-text slds-hide purchaseDate-error-required">This field is required</div>
                        </div>
                        <!-- brand -->
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required select-item" style="margin-top: 5px;">
                            <lightning:select label="Brand:" aura:id="brand" value="{!v.brandName}" >
                                <aura:iteration items="{!v.brandList}" var="brand">
                                    <option text="{!brand}" value="{!brand}"></option>
                                </aura:iteration>
                            </lightning:select>
                            <div aura:id="brand-error-required" class="error-text slds-hide">This field is required</div>
                        </div>
                        <!-- Master Model number -->
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex field-required select-item">
                            <c:CCM_Community_LookUp aura:id="masterProduct"
                                                    fieldName="Kit/Model:"
                                                    brandName="EGO"
                                                    selectedValue="{!v.masterModelnumber}"
                                                    fromProductRegistration="true"
                            />
                            <div aura:id="masterProduct-error-required" class="error-text slds-hide masterProduct-error-required">This field is required</div>
                        </div>
                        <!-- Purchase place -->
                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-flex purchasePlace field-required select-item" style="margin-top: 5px;">
                            <lightning:select label="Purchase Place:" aura:id="purchasePlace" value="{!v.purchasePlace}">
                                <option value="">-- None --</option>
                                <aura:iteration items="{!v.purchasePlaceList}" var="purchasePlace">
                                    <option text="{!purchasePlace}" value="{!purchasePlace}"></option>
                                </aura:iteration>
                            </lightning:select>
                            <!-- <c:CCM_Community_LookUp  fieldName="Purchase place:"
                                                    brandName="{!v.brandName}"
                                                    selectedValue="{!v.purchasePlaceObj}"
                                                    aura:id="purchasePlace"
                            /> -->
                            <div aura:id="purchasePlace-error-required" class="error-text slds-hide purchaseDate-error-required">This field is
                                required</div>
                        </div>
                        <!-- Upload your receipt -->
                        <div class="upload-receipt-wrap">
                            <div class="fileName-wrap" style="margin-top: 5px;">
                                <!-- <lightning:input aura:id="upload" class="upload-wrap" name="" type="file" label="Upload Your Receipt:" multiple="true" accept="image/png, .pdf" onchange="{!c.handleFilesChange}"/>
                                <aura:if isTrue="{!v.receiptUrl.fileName}">
                                    <p class="uploadFinished">
                                        <span class="fileName">{!v.receiptUrl.fileName}</span>
                                        <a class="delete" onclick="{!c.deleteReceipt}">Delete</a>
                                    </p>
                                </aura:if> -->
                                <lightning:fileUpload 
                                    class="upload-wrap" 
                                    label="Upload Your Receipt:"
                                    name="fileUploader"
                                    multiple="false"
                                    accept="['.png', '.jpg', '.jpeg', '.pdf']"
                                    recordId="{!v.recordId}"
                                    onuploadfinished="{!c.handleUploadFinished}" 
                                />
                                <aura:if isTrue="{!v.receiptUrl.fileName}">
                                    <p class="uploadFinished">
                                        <span class="fileName">{!v.receiptUrl.fileName}</span>
                                        <a class="delete" onclick="{!c.deleteReceipt}">Delete</a>
                                    </p>
                                </aura:if>
                            </div>
                            <!-- 避免添加按钮被单独挤到下一行，和最后一个item放一起 -->
                            <lightning:buttonIcon class="addBtn" iconName="utility:add" variant="bare" onclick="{!c.getTableData}"/> 
                        </div>
                    </div>
                    <!-- calvin -->
                    <aura:if isTrue="{!v.errortip}">
                        <div class="error-tips-wrap">
                            <div class="error-tips">
                                <aura:iteration items="{!v.errorList}" var="item">
                                    <p>{!item}</p>
                                </aura:iteration>
                            </div>
                        </div>
                    </aura:if>
                    <div class="slds-grid slds-wrap select-wrap">
                        <div class="slds-grid slds-wrap slds-align_absolute-center width80 select-btn-wrap">
                            <div class="flex-center-wrap template-btn-wrap">
                                <!-- 导出模板 -->
                                <!-- <c:ccmExcelTools type="export" btnText="Download Template" btnType="brand" excelData="{!v.exportData}" excelTitle="template"></c:ccmExcelTools> -->
                                <div class="select-template-wrap">
                                    <lightning:buttonMenu label="Download Template" variant="border-inverse" onselect="{!c.handleSelect }">
                                        <lightning:menuItem label="Warranty Registration Template" value="warrantyRegistrationTemplate" />
                                    </lightning:buttonMenu>
                                </div>
                                <!-- 导入模板 -->
                                <aura:if isTrue="{!v.resetFlag}">
                                    <c:ccmExcelTools ontabledata="{!c.getParseData}" type="parse" btnText="Mass Upload" btnType="brand"></c:ccmExcelTools>
                                </aura:if>
                            </div>
                            <div class="apply-btn-wrap">
                                <lightning:button variant="brand" label="Lost Receipt For Selected Line" title="Lost Receipt For Selected Line" onclick="{!c.lostReceipt}"/>

                                <!-- <lightning:input class="" type="checkbox" label="Lost Receipt For Selected Line" name="" checked="{!v.lostReceipt}" aura:id="lostReceipt"/> -->
                                <lightning:button variant="brand" label="Apply Receipt For Selected Line" title="Apply Receipt For Selected Line" onclick="{!c.applyReceipt}"/>
                            </div>
                        </div>
                    </div>
                    <!-- 表格数据 -->
                    <div class="table-wrap">
                        <lightning:datatable class="table simple-table"
                            columns="{!v.columns}"
                            data="{!v.tableData}"
                            keyField="id"
                            onrowselection="{!c.tableSelected}"
                            onrowaction="{!c.handleRowAction}"
                            draftValues="{!v.draftValues}"
                            onsave="{!c.handleSaveEdition}"
                            oncancel="{!c.handleCancelEdition}"
                            oncellchange="{!c.handleCellChange}"
                        />
                    </div>
                </div>
            </article>
            <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
                <lightning:button class="" variant="brand"  label="Export PDF" title="Export PDF" onclick="{!c.onExportPDF}"/>
                <aura:if isTrue="{!v.isPending}">
                    <lightning:button class="{!v.isDisabled ? 'disabled-btn' : 'submit-btn'}" variant="brand"  label="Submit" title="Submit" onclick="{!c.onClickSubmit}" disabled="{!v.isPending}"/>
                    <aura:set attribute="else">
                        <lightning:button class="submit-btn" variant="brand"  label="Submit" title="Submit" onclick="{!c.onClickSubmit}"/>
                    </aura:set>
                </aura:if>
                <lightning:button class="" variant="brand-outline"  label="Cancel" title="Cancel" onclick="{!c.onClickCancel}"/>
            </div>
        </section>
    </div>

</aura:component>