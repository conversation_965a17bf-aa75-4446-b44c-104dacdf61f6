/**
 * Honey
 * 开始前发送邮件
 */
public without sharing class CCM_RegisterSenfBeforeEmail {
    
    public CCM_RegisterSenfBeforeEmail() {
    }
    
    //前七天发送邮件到相关用户
    @InvocableMethod 
    public static void sendEditEmailToAfterUser(List<String> lstRecordId){
        try {
            //审核通过发送邮件到创建人
            List<EmailTemplate> lstEmailTemplate = new List<EmailTemplate>();
            List<EmailTemplate> lstParticipantsEmailTemplate = new List<EmailTemplate>();
            List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
            List<Messaging.SingleEmailMessage> lstemails = new List<Messaging.SingleEmailMessage>();
            //判断当前语言
            String userLanguage = UserInfo.getLanguage();
            //不同语言不同邮件内容
            String EmailName = '';
            String ParticipantsEmail = '';
            if(userLanguage == CCM_Constants.DE){
                EmailName = 'Training Reminder within 7 days (Dealer) DE';
            }else{
                EmailName = 'Training Reminder within 7 days (Dealer)';
            }
            lstEmailTemplate = [
                Select e.Id, e.Name,Subject,Body,HtmlValue from EmailTemplate e where Name  = :EmailName
            ];
            List<Course_Register__c> lstCourse = new List<Course_Register__c>();
            if(lstEmailTemplate != null && lstEmailTemplate.size()>0){
                String templateId = lstEmailTemplate[0].Id;
                //通过recordId查询Register的创建人
                lstCourse = [
                    SELECT Id,CreatedById,CreatedBy.Email,CreatedBy.IsActive,Status__c,Customer__c,
                    Customer__r.Name,Customer__r.AccountNumber,Billing_Address__c,Billing_Address__r.Final_Address__c,
                    Course_Arrangement__c,Training_Course__r.Course_Name__c,Training_Course_Name__c,
                    Training_Course__c,Course_Arrangement__r.Course_Date__c,Course_Arrangement__r.End_Time__c,
                    Course_Arrangement__r.Start_Time__c,Course_Arrangement__r.Training_Location__r.Final_Address__c,
                    Trainee__c,Start_Time_Before_Days__c,
                    Shiping_Address__c,
                    Course_Arrangement__r.Training_Location__c,
                    Billing_Address__r.Acknowledgement_Customer__c,
                    Billing_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Billing_Address__r.Acknowledgement_Customer__r.Salutation),
                    Billing_Address__r.Acknowledgement_Customer__r.Name,
                    Shiping_Address__r.Acknowledgement_Customer__c,
                    Shiping_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Shiping_Address__r.Acknowledgement_Customer__r.Salutation),
                    Shiping_Address__r.Acknowledgement_Customer__r.Name
                      FROM Course_Register__c
                    WHERE CreatedBy.IsActive = TRUE AND Id IN :lstRecordId
                ];

                for(Course_Register__c objCourse : lstCourse){
                    List<String> lstReceiptEmail = new List<String>();
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    //邮件内容设定
                    email.setSubject(lstEmailTemplate[0].Subject);
                    String strBody = lstEmailTemplate[0].HtmlValue;
                    strBody = strBody.replace('{Customer_Name__c}', objCourse.Customer__r.Name);
                    if(String.isNotBlank(objCourse.Billing_Address__r.Acknowledgement_Customer__c)){
                        //不为空。表示取billAddress上的email
                        lstReceiptEmail.add(objCourse.Billing_Address__r.Acknowledgement_Customer__r.Email);
                    }else if(String.isNotBlank(objCourse.Shiping_Address__r.Acknowledgement_Customer__c)){
                        //不为空。表示取ShipAddress上的email
                        lstReceiptEmail.add(objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Email);

                    }
                    String gender = '';
                    if(String.isNotBlank(objCourse.Billing_Address__r.Acknowledgement_Customer__c)) {
                        gender = objCourse.Billing_Address__r.Acknowledgement_Customer__r.Salutation + ' ' + objCourse.Billing_Address__r.Acknowledgement_Customer__r.Name;
                    }
                    else if(String.isNotBlank(objCourse.Shiping_Address__r.Acknowledgement_Customer__c)) {
                        gender = objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Salutation + ' ' + objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Name;
                    }
                    strBody = strBody.replace('{Family Name}', gender == null ? '': gender);
                    strBody = strBody.replace('{Customer_Number__c}', objCourse.Customer__r.AccountNumber);
                    strBody = strBody.replace('{Start_Time_Before_Days__c}', String.valueOf(objCourse.Start_Time_Before_Days__c));
                    strBody = strBody.replace('{Bill_Address_Info__c}', objCourse.Billing_Address__r.Final_Address__c);
                    strBody = strBody.replace('{Training_Course_Name__c}', objCourse.Training_Course_Name__c);
                    strBody = strBody.replace('{Course_Location_Info__c}', objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c == null ? '' : objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c);
                    strBody = strBody.replace('{days}', String.valueOf( (Integer)(Date.today().daysBetween(objCourse.Course_Arrangement__r.Course_Date__c) ) ) );
                    String courseTime = String.valueOf(objCourse.Course_Arrangement__r.Course_Date__c) +' ' 
                    + String.valueOf( objCourse.Course_Arrangement__r.Start_Time__c).subString(0,8) +'-'+
                     String.valueOf(objCourse.Course_Arrangement__r.End_Time__c).subString(0,8);
                    strBody = strBody.replace('{Course_Time__c}', courseTime);
                    strBody = strBody.replace('{Trainee__c}', objCourse.Trainee__c);
                    // String baseUrl = URL.getSalesforceBaseUrl().toExternalForm();
                    // baseUrl += '/'+objCourse.Id;

                    String baseUrl = 'https://chervoneuro--uat.sandbox.my.site.com/s/training-detail-page?0.recordId=';
                    if(!CCM_Service.IsSandboxOrg()) {
                        baseUrl = 'https://ego-eu.my.site.com/ecube/s/training-detail-page?0.recordId=';
                    }
                    baseUrl += objCourse.Id;
                    strBody = strBody.replace('{Link}', baseUrl);
                    email.setHtmlBody(strBody);
              
                    if(lstReceiptEmail == null || lstReceiptEmail.size() == 0){
                        break ;
                    }
                    system.debug('send Email--->'+lstReceiptEmail);
                    email.setToAddresses(lstReceiptEmail);
                    // email.setCcAddresses(new List<String>{'<EMAIL>'});
                    //设置发件人
                
                    if(listAddresses != null  && listAddresses.size() > 0){
                        email.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }
                    email.setSaveAsActivity(false);
                    lstemails.add(email);
                }
            }

            //Honey Added 发送给Participants
            if(userLanguage == CCM_Constants.DE){
                ParticipantsEmail = 'Training Reminder within 7 days (Participants) DE';
            }else{
                ParticipantsEmail = 'Training Reminder within 7 days (Participants)';
            }
            lstParticipantsEmailTemplate = [
                 Select e.Id, e.Name,Subject,Body,HtmlValue from EmailTemplate e where Name  = :ParticipantsEmail
            ];
            if(lstParticipantsEmailTemplate != null && lstParticipantsEmailTemplate.size()>0){
                String templateId = lstParticipantsEmailTemplate[0].Id;
                //通过recordId查询Register的创建人
                List<Course_Register_Item__c> lstCourseItem = new List<Course_Register_Item__c>();
                lstCourseItem = [
                    SELECT Course_Arrangement__c, Course_Arrangement__r.Course_Date__c, Course_Arrangement__r.Training_Location__r.Final_Address__c,
                    Course_Register__c, Course_Register__r.Training_Course_Name__c, email__c, 
                     Course_Arrangement__r.Start_Time__c,Course_Arrangement__r.End_Time__c,
                    Id, Trainee__c, Course_Register__r.Course_Arrangement__r.Training_Location__c FROM Course_Register_Item__c 
                    WHERE CreatedBy.IsActive = TRUE AND Course_Register__c IN :lstRecordId
                ];
                
                Map<String, List<ContentVersion>> locationFilesMap = getLocationFiles(lstCourse);

                for(Course_Register_Item__c objCourseItem : lstCourseItem){
                    List<String> lstReceiptEmail = new List<String>();
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    //邮件内容设定
                    email.setSubject(lstParticipantsEmailTemplate[0].Subject);
                    String strBody = lstParticipantsEmailTemplate[0].HtmlValue;
                    strBody = strBody.replace('{Family Name}', objCourseItem.Trainee__c);
                    strBody = strBody.replace('{days}', String.valueOf( (Integer)(Date.today().daysBetween(objCourseItem.Course_Arrangement__r.Course_Date__c) ) ) );
                    strBody = strBody.replace('{Training_Course_Name__c}', objCourseItem.Course_Register__r.Training_Course_Name__c);
                    strBody = strBody.replace('{Course_Location_Info__c}', objCourseItem.Course_Arrangement__r.Training_Location__r.Final_Address__c == null ? '' : objCourseItem.Course_Arrangement__r.Training_Location__r.Final_Address__c);
                    String courseTime = String.valueOf(objCourseItem.Course_Arrangement__r.Course_Date__c) +' ' 
                    + String.valueOf( objCourseItem.Course_Arrangement__r.Start_Time__c).subString(0,8) +'-'+
                     String.valueOf(objCourseItem.Course_Arrangement__r.End_Time__c).subString(0,8);
                    strBody = strBody.replace('{Course_Time__c}', courseTime);

               
                    email.setHtmlBody(strBody);
                    lstReceiptEmail.add(objCourseItem.email__c);
                    
                    List<ContentVersion> locationFiles = locationFilesMap.get(objCourseItem.Course_Register__r.Course_Arrangement__r.Training_Location__c);
                    if(!locationFiles.isEmpty()) {
                        Messaging.EmailFileAttachment[] eAttachments = new Messaging.EmailFileAttachment[]{};
                        for(ContentVersion att : locationFiles) {
                            Messaging.EmailFileAttachment eAttachment = new Messaging.EmailFileAttachment();
                            eAttachment.setFileName(att.Title + '.' + att.FileExtension);
                            eAttachment.setBody(att.VersionData);
                            eAttachments.add(eAttachment);
                        }
                        email.setFileAttachments(eAttachments);
                    }
              
                    if(lstReceiptEmail == null || lstReceiptEmail.size() == 0){
                        break ;
                    }
                    system.debug('send Email--->'+lstReceiptEmail);
                    email.setToAddresses(lstReceiptEmail);
                    email.setCcAddresses(new List<String>{'<EMAIL>'});
                    //设置发件人
                
                    if(listAddresses != null  && listAddresses.size() > 0){
                        email.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }
                    email.setSaveAsActivity(false);
                    lstemails.add(email);
                }
            }
            //发送邮件
            if (!System.Test.isRunningTest()){
                Messaging.SendEmailResult[] results = Messaging.sendEmail(lstemails);
            }
        } catch (Exception e) {
            system.debug('报错信息－－－＞'+e.getMessage()+'报错行数－－－－'+e.getLineNumber());
        }
    }

    private static Map<String, List<ContentVersion>> getLocationFiles(List<Course_Register__c> courseRegisters) {
        Map<String, List<ContentVersion>> locationFilesMap = new Map<String, List<ContentVersion>>();
        Set<String> trainingLocations = new Set<String>();
        for(Course_Register__c courseRegister : courseRegisters) {
            if(String.isNotBlank(courseRegister.Course_Arrangement__r.Training_Location__c)) {
                trainingLocations.add(courseRegister.Course_Arrangement__r.Training_Location__c);
            }
        }

        List<ContentDocumentLink> lstContentDocumentLink = [SELECT ContentDocumentId, LinkedEntityId 
                                                            FROM ContentDocumentLink 
                                                            WHERE LinkedEntityId IN :trainingLocations];
        Map<String, Set<String>> locationDocumentIdMap = new Map<String, Set<String>>();
        Set<String> documentIds = new Set<String>();
        for(ContentDocumentLink cdl : lstContentDocumentLink) {
            documentIds.add(cdl.ContentDocumentId);
            if(!locationDocumentIdMap.containsKey(cdl.LinkedEntityId)) {
                locationDocumentIdMap.put(cdl.LinkedEntityId, new Set<String>());
            }
            locationDocumentIdMap.get(cdl.LinkedEntityId).add(cdl.ContentDocumentId);
        }

        List<ContentVersion> versions = [SELECT Title, FileExtension, VersionData, ContentDocumentId
                                          FROM ContentVersion
                                          WHERE ContentDocumentId IN :documentIds];

        for(ContentVersion version : versions) {
            for(String locationId : locationDocumentIdMap.keySet()) {
                Set<String> locationDocumentIds = locationDocumentIdMap.get(locationId);
                if(locationDocumentIds.contains(version.ContentDocumentId)) {
                    if(!locationFilesMap.containsKey(locationId)) {
                        locationFilesMap.put(locationId, new List<ContentVersion>());
                    }
                    locationFilesMap.get(locationId).add(version);
                }
            }
        }
        return locationFilesMap;
    }
}