/**
 * <AUTHOR>
 * @date 2025-07-01
 * @description Simple test class for CCM_ProspectAddressInfoQueryCtrl
 */
@isTest
public class CCM_ProspectAddressQuery_SimpleTest {
    
    @isTest
    static void testQueryAddressInfo_BasicFunctionality() {
        // Create a simple test Lead
        Lead testLead = new Lead();
        testLead.TestFlag__c = true;
        testLead.RecordTypeId = CCM_Constants.PROSPECT_CHANNEL_RECORD_TYPE_ID;
        testLead.Status = 'Open';
        testLead.LastName = 'Test Lead';
        testLead.Company = 'Test Company';
        testLead.Prospect_Type__c = 'Dealer';
        testLead.Sales_Channel__c = 'OPE Dealer';
        testLead.Classification_1__c = 'Dealer_Standard';
        testLead.Re_automatic__c = false;
        testLead.Country__c = 'DE';
        testLead.State__c = 'Bavaria';
        testLead.City__c = 'Munich';
        testLead.Street_1__c = 'Test Street 123';
        testLead.Postal_Code__c = '80331';
        testLead.Country_All__c = 'DE-Germany';
        insert testLead;
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(testLead.Id);
        Test.stopTest();
        
        // Verify the result is not null and not empty
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals('{}', result, 'Result should not be empty JSON');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify some basic fields are included
        System.assertNotEquals(null, addressMap.get('State__c'), 'State__c should be included');
        System.assertNotEquals(null, addressMap.get('City__c'), 'City__c should be included');
        System.assertNotEquals(null, addressMap.get('Street_1__c'), 'Street_1__c should be included');
        System.assertNotEquals(null, addressMap.get('Postal_Code__c'), 'Postal_Code__c should be included');
        
        System.debug('Address Map: ' + addressMap);
    }
    
    @isTest
    static void testQueryAddressInfo_EmptyResult() {
        // Test with a non-existing Lead ID
        String fakeLeadId = '00Q000000000000AAA';
        
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(fakeLeadId);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify the map is empty since no lead was found
        System.assertEquals(0, addressMap.size(), 'Address map should be empty when lead is not found');
    }
    
    @isTest
    static void testQueryAddressInfo_NullInput() {
        // Test with null Lead ID
        Test.startTest();
        String result = CCM_ProspectAddressInfoQueryCtrl.queryAddressInfo(null);
        Test.stopTest();
        
        // Verify the result
        System.assertNotEquals(null, result, 'Result should not be null');
        
        // Parse the JSON result
        Map<String, String> addressMap = (Map<String, String>) JSON.deserialize(result, Map<String, String>.class);
        
        // Verify the map is empty since leadId is null
        System.assertEquals(0, addressMap.size(), 'Address map should be empty when leadId is null');
    }
}
