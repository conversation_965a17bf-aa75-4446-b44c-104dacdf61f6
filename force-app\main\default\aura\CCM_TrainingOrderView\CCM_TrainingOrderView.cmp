<aura:component description="CCM_Community_PODetail"
                implements="forceCommunity:availableForAllPageTypes,force:hasRecordId,flexipage:availableForAllPageTypes,force:appHostable"
                controller="CCM_CourseRegisterCtl"
                access="global">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="poRecordId" type="String" default=""/>
    <aura:attribute name="currencySymbol" type="String" default="€"/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="order" type="Object" default=""/>
    <aura:attribute name="invoiceInfo" type="List" default="[]"/>
    <aura:attribute name="shipmentColumns" type="List" default="[]"/>
    <aura:attribute name="producelist" type="List" default="[]"/>
    <aura:attribute name="invoiceColumns" type="List" default="[]"/>
    <aura:attribute name="showSpinner" type="Boolean" default="true"/>
    <aura:attribute name="processData" type="List" default="[]"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="isInnerUser" type="Boolean" default="false"/>
    <aura:attribute name="isShow" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareProducts" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareShipments" type="Boolean" default="false"/>
    <aura:attribute name="isCanReverse" type="Boolean" default="false"/>
    <aura:attribute name="reverseOrderLink" type="String" default=""/>
    <aura:attribute name="attachmentColumns" type="List" default="[]"/>
    <aura:attribute name="attachmentTypeOptions" type="List" default="[]"/>
    <aura:attribute name="contentDocumentId" type="String" default=""/>

    <aura:attribute name="basicInformation" type="Object" default=""/>
    <aura:attribute name="attachment" type="List" default="[]"/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="attachmentItem" type="Map" default="{}"/>
    <aura:attribute name="uploadList" type="List" default="[]"/>
    <aura:attribute name="uploadModalFlag" type="Boolean" default="false"/>
    <aura:attribute name="uploadFinished" type="Boolean" default="false" />
    <aura:attribute name="attachmentName" type="String" default=""/>
    <aura:attribute name="attachmentType" type="String" default=""/>
    <aura:attribute name="TotalDueAmount" type="String" default=""/>
    <aura:attribute name="CountLine" type="String" default=""/>
    <!-- <aura:attribute name="showSync" type="Boolean" default="true"/> -->
    <aura:attribute name="userType" type="String" default=""/>
    <aura:attribute name="poStatus" type="String" default=""/>
    <aura:attribute name="requestOrorder" type="String" default="Request"/>
    <aura:attribute name="beforeSyncId" type="String" default=""/>
    <aura:attribute name="afterSyncId" type="String" default=""/>
    
    
    <aura:attribute name="ratifyPup" type="Boolean" default="false"/>
    <aura:attribute name="ratifyPuplabel" type="String" default=""/>

    <aura:attribute name="isInsideSales" type="Boolean" default="false"/>
    <aura:attribute name="isCCA" type="Boolean" default="false"/>

    <!-- cancel 弹框 -->
    <aura:attribute name="cancelFlag" type="Boolean" default="false"/>
    <aura:attribute name="cancellationReason" type="String" default=""/>
    <aura:attribute name="isComfirm" type="Boolean" default="false"/>

    
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="{!v.showSpinner ? 'slds-box slds-theme_default margin' : 'slds-theme_default'}" style="{!v.showSpinner ? 'margin: 0 5%;padding-bottom: 50px;' : 'margin: 0'}">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium" style="position: relative;">
            <lightning:spinner size="large" style="z-index: 10000;" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <aura:if isTrue="{!v.showSpinner}">
                <c:CCM_PathProcess processData="{!v.processData}" currentStep="{!v.currentStep}"/>
            </aura:if>
            <!-- Basic Information -->
            <c:CCM_Section title="{!$Label.c.CCM_BasicInformation}" expandable="true">
                <lightning:layout multipleRows="true">
                    <!-- Customer -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Customer}">
                            {!v.basicInformation.customerName}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Customer Number​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                       <c:CCM_Field label="{!$Label.c.CCM_CustomerNumber}">
                           {!v.basicInformation.customerNumber}
                       </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Contact Number​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                       <c:CCM_Field label="{!$Label.c.CCM_Contact}">
                           {!v.basicInformation.contactName}
                       </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Payment Term​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_PaymentTerm}">
                            {!v.basicInformation.paymentTerm}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Date​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.Order_OrderDate}">
                            {!v.basicInformation.orderDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Request Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_RequestNumber}">
                            {!v.basicInformation.requestNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Salesperson​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Salesperson}">
                            {!v.basicInformation.salesPerson}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Record Status -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_RecordStatus}">
                            {!v.basicInformation.status}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Record Type​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_RecordType}">
                            {!v.requestOrorder}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Creation Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_CreationDate}">
                            {!v.basicInformation.orderDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>

            <!-- Arrangement Information -->
            <c:CCM_Section title="{!$Label.c.CCM_TrainingCourseInformation}" expandable="true">
                <lightning:layout multipleRows="true">
                    <!-- Training Course Name -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_TrainingCourseName}">
                            {!v.basicInformation.trainingCourseName}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Training Course Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                       <c:CCM_Field label="{!$Label.c.CCM_TrainingCourseNumber}">
                           {!v.basicInformation.trainingCourseNumber}
                       </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Training Description -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_TrainingDescription}">
                            <!-- {!v.basicInformation.trainingDescription} -->
                            <div class="richtext"><aura:unescapedHtml value="{!v.basicInformation.trainingDescription}"/></div>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Training Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_TrainingDate}">
                            {!v.basicInformation.trainingDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Training Start Time​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_TrainingStartTime}">
                            {!v.basicInformation.trainingStartTime}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Training End Time​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_TrainingEndTime}">
                            {!v.basicInformation.trainingEndTime}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Training Location -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_TrainingLocation}">
                            {!v.basicInformation.trainingLocation}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            <!-- Delivery Information -->
            <c:CCM_Section title="{!$Label.c.CCM_DeliveryInformation}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Bill To Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_BillToAddress}">
                            <aura:if isTrue="{!v.basicInformation.BillToAddressInfo.CompanyName}">
                                <span>{!v.basicInformation.BillToAddressInfo.CompanyName}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!(v.basicInformation.BillToAddressInfo.City) || (v.basicInformation.BillToAddressInfo.PostalCode)}">
                                <span>{!v.basicInformation.BillToAddressInfo.PostalCode}&nbsp;&nbsp;{!v.basicInformation.BillToAddressInfo.City}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.BillToAddressInfo.Country}">
                                <span>{!v.basicInformation.BillToAddressInfo.Country}</span>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_ShipToAddress}">
                            <aura:if isTrue="{!v.basicInformation.ShipToAddressInfo.CompanyName}">
                                <span>{!v.basicInformation.ShipToAddressInfo.CompanyName}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!(v.basicInformation.ShipToAddressInfo.City) || (v.basicInformation.ShipToAddressInfo.PostalCode)}">
                                <span>{!v.basicInformation.ShipToAddressInfo.PostalCode}&nbsp;&nbsp;{!v.basicInformation.ShipToAddressInfo.City}</span><br/>
                            </aura:if>
                            <aura:if isTrue="{!v.basicInformation.ShipToAddressInfo.Country}">
                                <span>{!v.basicInformation.ShipToAddressInfo.Country}</span>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            
            <!-- Approve History -->
            <aura:if isTrue="{!v.basicInformation.approvalInfo.submitUserName}">
            <c:CCM_Section title="{!$Label.c.CCM_ApproveHistory}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Submit User Name -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_SubmitUserName}">
                            {!v.basicInformation.approvalInfo.submitUserName}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Submit Date​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                       <c:CCM_Field label="{!$Label.c.CCM_SubmitDate}">
                            {!v.basicInformation.approvalInfo.submitDate}
                       </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Approval Status -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_ApprovalStatus}">
                            {!v.basicInformation.approvalInfo.ApprovalStatus}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            </aura:if>
            <!-- Request Item Information -->
            <c:CCM_Section title="{!$Label.c.CCM_RequestItemInformation}" expandable="true" >
                <div class="slds-p-left_medium slds-p-right--medium">
                    <div class="table-wrap">
                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productTable" role="grid">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_No}">{!$Label.c.CCM_No}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Product Description -->
                                    <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                            </div>
                                        </a>
                                    </th> -->
                                    <!-- Sub Model -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_SubModel}">{!$Label.c.CCM_SubModel}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- UOM-->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_UOM}">{!$Label.c.CCM_UOM}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Participants-->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Participants}">{!$Label.c.CCM_Participants}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- SMS-->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="SMS">SMS</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Email-->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Email">Email</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Remark-->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_Remark}">{!$Label.c.Order_Remark}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_OrderDate}">{!$Label.c.CCM_OrderDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable " scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_OrderQty}">{!$Label.c.CCM_OrderQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- List Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.Order_ListPrice}">{!$Label.c.Order_ListPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <aura:iteration items="{!v.producelist}" var="product" indexVar="index">
                                    <tr aria-selected="false" id="{!index}" data-expanded="false" class="slds-hint-parent" onclick="{!c.showToolList}">
                                        <!-- line -->
                                        <td scope="row">
                                            <div class="slds-truncate" title="">
                                                {!index + 1}
                                            </div>
                                        </td>
                                        <!-- Product Description -->
                                        <!-- <td role="gridcell" title="{!product.productDescription}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!product.productDescription}
                                            </div>
                                        </td> -->
                                        <!-- Sub Model -->
                                        <td role="gridcell" title="{!product.productName}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!product.productName}
                                            </div>
                                        </td>
                                        <!-- UOM -->
                                        <td role="gridcell" title="{!product.uom}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!product.uom}
                                            </div>
                                        </td>
                                        <!-- Participants -->
                                        <td role="gridcell" title="{!product.trainee}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!product.trainee}
                                            </div>
                                        </td>
                                        <!-- SMS -->
                                        <td role="gridcell" title="{!product.sms}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!product.sms}
                                            </div>
                                        </td>
                                        <!-- Email -->
                                        <td role="gridcell" title="{!product.email}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!product.email}
                                            </div>
                                        </td>
                                        <!-- Remark -->
                                        <td role="gridcell" title="{!product.remark}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                {!product.remark}
                                            </div>
                                        </td>
                                        <!-- Order Date -->
                                        <td role="gridcell" title="{!product.orderDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!product.orderDate}</span>
                                            </div>
                                        </td>
                                        <!-- Order Qty -->
                                        <td role="gridcell" title="{!product.orderQty}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!product.orderQty}</span>
                                            </div>
                                        </td>
                                        <!-- List Price -->
                                        <td role="gridcell" title="{!product.listPrice}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <lightning:formattedNumber value="{!product.listPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                            </div>
                                        </td>
                                    </tr>
                                </aura:iteration>
                            </tbody>
                        </table>
                    </div>
                    <div class="slds-clearfix slds-m-top--medium">
                        <aura:if isTrue="{!v.poStatus == 'Pending Review'}">
                            <lightning:layout>
                                <div class="c-container" style="padding: 10px">
                                    <lightning:layoutItem alignmentBump="right">
                                        <lightning:button variant="brand" label="{!$Label.c.CCM_RefreshTrafficLight}" onclick="{!c.refreshTrafficLight}"/>
                                    </lightning:layoutItem>
                                </div>
                            </lightning:layout> 
                        </aura:if>
                        <div class="slds-grid slds-float--right">
                        <div class="slds-text-align--right">
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_TrainingAmount}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_VAT}:&nbsp;</div>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.Order_TotalDueAmount}:&nbsp;</strong></div>
                        </div>
                        <div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.TrainingAmount)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!abs(v.basicInformation.VAT)}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.basicInformation.TotalDueAmount}" style="currency" currencyCode="{!v.currencySymbol}"/> </strong></div>
                        </div>
                    </div>
                    </div>
                </div>
            </c:CCM_Section>
            <!-- Invoice 信息-->
            <aura:if isTrue="{!v.requestOrorder == 'Order'}">
                <c:CCM_Section title="{!$Label.c.CCM_Attachment}" expandable="true">
                    <aura:if isTrue="{!v.attachment.length > 0}">
                        <c:CCM_DataTable columns="{!v.attachmentColumns}" data="{!v.attachment}"/>
                        <aura:set attribute="else">
                            <p class="slds-text-align_center slds-p-around--medium">{!$Label.c.CCM_NoItemsToDisplay}</p>
                        </aura:set>
                    </aura:if>
                </c:CCM_Section>
            </aura:if>

            <div class="CCM_PaddingTop slds-m-bottom_medium">
                <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_UploadAttachmentFile}" onclick="{!c.uploadFileItem}"/>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!(v.poStatus == 'Pending Review')}">
                    <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_UploadAttachmentFile}" onclick="{!c.uploadFileItem}"/>
                    </aura:if>
                </aura:if>
            </div>

            <aura:if isTrue="{!v.showSpinner}">
                <div class="footer-btn slds-m-bottom_medium">
                    <!-- Submit -->
                    <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Edit}" onclick="{!c.doEdit}"/>
                        </aura:if>
                    </aura:if>
                    <!-- Edit -->
                    <aura:if isTrue="{!v.basicInformation.status == 'Draft'}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Edit}" onclick="{!c.doEdit}"/>
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Submit}" onclick="{!c.Submit}" />
                    </aura:if>
                    <!-- Confirm -->
                    <aura:if isTrue="{!v.isComfirm}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.Confirm}" onclick="{!c.onclickConfirm}" />
                    </aura:if>
                    <!-- Cancel -->
                    <aura:if isTrue="{!v.basicInformation.isCancel == true}">
                        <lightning:button variant="brand" label="{!$Label.c.CCM_Cancel}" onclick="{!c.onclickCancel}" />
                    </aura:if>
                    <aura:if isTrue="{!v.requestOrorder == 'Order'}">
                    <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_CheckTrainingRequest}" onclick="{!c.Syncbefore}"/>
                    </aura:if>
                    <aura:if isTrue="{!v.afterSyncId}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_CheckTrainingOrder}" onclick="{!c.Syncafter}"/>
                    </aura:if>
                    <!-- Back -->
                    <lightning:button variant="brand-outline" label="{!$Label.c.CCM_Back}" onclick="{!c.doBack}"/>



                    <!-- <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.doCancelSync}"/>
                    </aura:if>
                    <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SaveAttachment}" onclick="{!c.saveFileLIst}"/>
                        </aura:if>
                    </aura:if>
                    <aura:if isTrue="{!(v.poStatus == 'Pending Review')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SaveAttachment}" onclick="{!c.saveFileLIst}"/>
                        </aura:if>
                    </aura:if>
                    <aura:if isTrue="{!(v.poStatus == 'Submitted') || (v.poStatus == 'review in process')}">
                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SyncToEBS}" onclick="{!c.doSync}"/>
                        </aura:if>
                    </aura:if> -->
                </div>
            </aura:if>
        </div>
        <!-- 附件上传 -->
        <aura:if isTrue="{!v.uploadModalFlag}">
            <div>
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; height:auto !important; transform: translate(0%, 25%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                                <lightning:icon iconName="utility:close" alternativeText="{!$Label.c.CCM_Close + '!'}" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_UploadAttachmentFile}</h2>
                        </div>
                        <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                            <div class="content-wrap">
                                <lightning:layout horizontalAlign="space" verticalAlign="center">
                                    <!-- Attachment Name -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                        <lightning:input name="" label="{!$Label.c.CCM_AttachmentName}" value="{!v.attachmentName}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255"/>
                                    </lightning:layoutItem>
                                    <!-- Attachment Type -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                        <span class="combobox-label">{!$Label.c.CCM_AttachmentType}</span>
                                        <lightning:combobox class="ccm_display"
                                            name="Attachment Type" 
                                            value="{!v.attachmentType}" 
                                            options="{!v.attachmentTypeOptions}" 
                                            label=""
                                            variant="label-hidden"
                                        />
                                    </lightning:layoutItem>
                                    <!-- <lightning:layoutItem alignmentBump="center" size="5">
                                        <lightning:input name="" label="Attachment Type" value="{!v.attachmentType}" messageWhenTooLong="Please enter no more than 255 bytes." maxlength="255"/>
                                    </lightning:layoutItem> -->
                                </lightning:layout>
                                <lightning:layout horizontalAlign="space" verticalAlign="center">
                                    <!-- Attachment file -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                        <lightning:input aura:id="upload" class="upload-wrap" name="" type="file" label="{!$Label.c.CCM_UploadAttachmentFile}" multiple="true" accept="" onchange="{!c.handleFilesChange}"/>
                                        <aura:if isTrue="{!v.uploadFinished}">
                                            <p class="uploadFinished" title="{!v.attachmentItem.name}">
                                                <span class="fileName">{!v.attachmentItem.name}</span>
                                                <a class="delete" onclick="{!c.deleteAttachmentItem}">{!$Label.c.CCM_Delete}</a>
                                            </p>
                                        </aura:if>
                                    </lightning:layoutItem>
                                    <!-- 占位 -->
                                    <lightning:layoutItem alignmentBump="center" size="5">
                                    </lightning:layoutItem>
                                </lightning:layout>
                            </div>
                        </div>
                        <footer class="slds-modal__footer">
                            <div class="footer-wrap">
                                <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancelEvent}"/>
                                <lightning:button class="" variant="brand" label="{!$Label.c.CCM_Save}"  onclick="{!c.saveFileItem}"/>
                            </div>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
        <!-- 隐藏fileCard，弹出专用 -->
        <div class="slds-hide">
            <lightning:fileCard hideDescription="true"/>
        </div>


    <!-- 点击审批按钮的弹窗 -->
    <!-- 弹窗 start -->
    <aura:if isTrue="{!v.ratifyPup}">
        <div>
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                    <div class="modal-header slds-modal__header">
                        <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                            <lightning:icon iconName="utility:close" alternativeText="{!$Label.c.CCM_Close + '!'}" variant="close" class = "modal_close"/>
                            <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                        </button>
                        <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!v.ratifyPuplabel}</h2>
                    </div>
                    <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                        <div class="content-wrap">
                            <lightning:textarea class="pup-up" name="comments" value="{!v.comments}" label="Comments" />
                        </div>
                    </div>
                    <footer class="slds-modal__footer">
                        <div class="footer-wrap">
                            <aura:if isTrue="{!v.ratifyPuplabel == 'Approve'}">
                                <lightning:button class="" variant="brand" label="{!$Label.c.Approve}" title="Approve" onclick="{!c.onclickApprove}"/>
                            </aura:if>
                            <aura:if isTrue="{!v.ratifyPuplabel == 'Reject'}">
                                <lightning:button class="" variant="brand" label="{!$Label.c.Reject}" title="Reject" onclick="{!c.onclickReject}"/>
                            </aura:if>
                            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" title="Cancel" onclick="{!c.cancelEvent}"/>
                        </div>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </aura:if>
    <!-- 弹窗 end -->

    <!-- cancel弹框 -->
    <!-- 弹窗 start -->
    <aura:if isTrue="{!v.cancelFlag}">
        <div>
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                    <div class="modal-header slds-modal__header">
                        <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelEvent}">
                            <lightning:icon iconName="utility:close" alternativeText="{!$Label.c.CCM_Close + '!'}" variant="close" class = "modal_close"/>
                            <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                        </button>
                        <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_CancellationReason}</h2>
                    </div>
                    <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                        <div class="content-wrap required-wrap">
                            <lightning:textarea aura:id="cancellationReason" class="pup-up field-required" value="{!v.cancellationReason}" label="Cancellation Reason" onblur="{!c.changeCancellationReason}"/>
                            <div aura:id="cancellationReason-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                        </div>
                    </div>
                    <footer class="slds-modal__footer">
                        <div class="footer-wrap">
                            <lightning:button class="" variant="brand" label="{!$Label.c.CCM_Submit}" onclick="{!c.cancelCourseEvent}"/>
                            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" onclick="{!c.cancelEvent}"/>
                        </div>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </aura:if>
    <!-- 弹窗 end -->


    </div>
</aura:component>