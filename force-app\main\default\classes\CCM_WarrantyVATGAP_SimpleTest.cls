/**
 * <AUTHOR>
 * @date 2025-06-30
 * @description Simple test class for CCM_WarrantyClaim_VATGAPCalculateCtl and Handler
 */
@isTest
public class CCM_WarrantyVATGAP_SimpleTest {
    
    @isTest
    static void testCalculateVATGAPForVATCorrection() {
        // Create test warranty claim with all required fields
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Serial_Number__c = 'TESTSIMPLE123X';
        claim.Service_Option__c = 'Repair';
        claim.Claim_Status__c = 'Draft';
        claim.Bench_Mark__c = 100.00;
        claim.Historical_Warranty_Efforts__c = 80.00;
        claim.Labor_Input_Time__c = 2.0;
        claim.Actual_Labor_Rate__c = 15.0; // Labor_Input_Cost = 30.00
        claim.Diagnostic_Fee__c = 5.00;
        insert claim;
        
        // Create claim item to populate Material_Actual_Cost__c
        Warranty_Claim_Item__c item = new Warranty_Claim_Item__c();
        item.Warranty_Claim__c = claim.Id;
        item.Actual_Price__c = 15.00;
        item.Quantity__c = '1';
        insert item;
        
        // Refresh claim to get calculated fields
        claim = [SELECT Id, Bench_Mark__c, Historical_Warranty_Efforts__c, Total_Actual__c, 
                 Material_Actual_Cost__c, Labor_Final_Cost__c, Diagnostic_Fee__c 
                 FROM Warranty_Claim__c WHERE Id = :claim.Id];
        
        Test.startTest();
        Decimal vatGap = CCM_WarrantyClaim_VATGAPCalculateCtl.calculateVATGAPForVATCorrection(claim);
        Test.stopTest();
        
        // Verify VAT gap calculation
        System.assertNotEquals(null, vatGap, 'VAT gap should be calculated');
        System.debug('Total_Actual__c: ' + claim.Total_Actual__c);
        System.debug('VAT Gap: ' + vatGap);
    }
    
    @isTest
    static void testHandlerBasic() {
        // Disable handler initially
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = false;
        
        // Create test Account
        Account dealer = new Account();
        dealer.Name = 'Test Dealer Simple';
        dealer.Country_All__c = 'DE-Germany';
        dealer.Country__c = 'DE';
        dealer.Sales_Channel__c = 'OPE Dealer';
        dealer.Classification1__c = 'Dealer';
        dealer.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        insert dealer;
        
        // Create test warranty claim
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Serial_Number__c = 'TESTHANDLER123X';
        claim.Dealer_Name__c = dealer.Id;
        claim.Service_Option__c = 'Repair';
        claim.Claim_Status__c = 'Draft';
        claim.Labor_Input_Time__c = 2.0;
        claim.Actual_Labor_Rate__c = 15.0;
        claim.Diagnostic_Fee__c = 5.00;
        claim.Bench_Mark__c = 100.00;
        claim.Historical_Warranty_Efforts__c = 50.00;
        claim.Bench_Mark_Calculated__c = false;
        insert claim;
        
        // Re-enable handler
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = true;
        
        Test.startTest();
        // Update to trigger handler
        claim.Bench_Mark_Calculated__c = true;
        update claim;
        Test.stopTest();
        
        // Verify handler executed
        Warranty_Claim__c updatedClaim = [SELECT VAT_Cost_Center__c, All_Cost_Calculated__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals(true, updatedClaim.All_Cost_Calculated__c, 'All Cost Calculated should be true');
        System.assertEquals('EEG', updatedClaim.VAT_Cost_Center__c, 'VAT Cost Center should be EEG for dealer');
    }
    
    @isTest
    static void testCalculateHistoricalWarrantyEfforts() {
        // Create test warranty claim
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Serial_Number__c = 'TESTHISTORICAL123X';
        claim.Service_Option__c = 'Repair';
        claim.Claim_Status__c = 'Draft';
        insert claim;
        
        Test.startTest();
        Decimal result = CCM_WarrantyClaim_VATGAPCalculateCtl.calculateHistoricalWarrantyEfforts(
            claim.Id, 
            claim.Serial_Number__c, 
            'Repair', 
            null
        );
        Test.stopTest();
        
        // Verify result (should be 0 since no historical claims)
        System.assertEquals(0, result, 'Historical warranty efforts should be 0 for new serial number');
        
        // Verify warranty claim was updated
        Warranty_Claim__c updatedClaim = [SELECT Historical_Warranty_Efforts__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals(0, updatedClaim.Historical_Warranty_Efforts__c, 'Historical warranty efforts should be updated to 0');
    }
}
