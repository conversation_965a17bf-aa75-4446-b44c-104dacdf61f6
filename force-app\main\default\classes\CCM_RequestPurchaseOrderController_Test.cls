/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 06-13-2024
 * @last modified by  : <EMAIL>
**/
@isTest
public with sharing class CCM_RequestPurchaseOrderController_Test{
    @testSetup
    static void setup(){
        // Create test data
        Account acc = new Account(Name = 'Test Account', AccountNumber = 'E7123', RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID);
        insert acc;
        
        Contact conta = new Contact(LastName='Test Contact');
        insert conta;

        List<Product2> lstProducts = new List<Product2>();
        Product2 product1 = new Product2();
        product1.Source__c = 'EBS';
        product1.Name = 'BH1001';
        product1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        product1.ExternalId__c = 'BH1001';
        product1.Order_Model__c = 'BH1001';
        product1.Master_Product__c = 'BH1001';
        product1.Item_Description_DE__c = 'DEDescription';
        product1.Item_Description_EN__c = 'ENDescription';
        product1.Units_Per_Pallet_EA__c = 6;
        product1.Units_Per_Inner_BOX_EA__c = 5;
        product1.Units_Per_Master_Carton_EA__c = 7;
        insert product1;
        
        Product2 product101 = new Product2();
        product101.Source__c = 'EBS';
        product101.Name = 'BH10011';
        product101.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('ACC').getRecordTypeId();
        product101.ExternalId__c = 'BH10011';
        product101.Order_Model__c = 'BH10011';
        product101.Master_Product__c = 'BH10011';
        product101.Item_Description_DE__c = 'DEDescription';
        product101.Item_Description_EN__c = 'ENDescription';
        product101.Units_Per_Pallet_EA__c = 6;
        product101.Units_Per_Inner_BOX_EA__c = 5;
        product101.Units_Per_Master_Carton_EA__c = 7;
        insert product101;
        
        Product2 product102 = new Product2();
        product102.Source__c = 'EBS';
        product102.Name = 'BH10012';
        product102.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        product102.ExternalId__c = 'BH10012';
        product102.Order_Model__c = 'BH10012';
        product102.Master_Product__c = 'BH10012';
        product102.Item_Description_DE__c = 'DEDescription';
        product102.Item_Description_EN__c = 'ENDescription';
        product102.Units_Per_Pallet_EA__c = 6;
        product102.Units_Per_Inner_BOX_EA__c = 5;
        product102.Units_Per_Master_Carton_EA__c = 7;
        insert product102;
        
        Product2 product103 = new Product2();
        product103.Source__c = 'EBS';
        product103.Name = 'BH10013';
        product103.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_KIT').getRecordTypeId();
        product103.ExternalId__c = 'BH10013';
        product103.Order_Model__c = 'BH10013';
        product103.Master_Product__c = 'BH10013';
        product103.Item_Description_DE__c = 'DEDescription';
        product103.Item_Description_EN__c = 'ENDescription';
        product103.Units_Per_Pallet_EA__c = 6;
        product103.Units_Per_Inner_BOX_EA__c = 5;
        product103.Units_Per_Master_Carton_EA__c = 7;
        insert product103;
        
   
        
        Inventory__c testInventory = new Inventory__c(Product__c = product1.Id, Available_QTY__c = 5);
        testInventory.Sub_Inventory_Code__c = 'EGD01';
        testInventory.RecordTypeId = Schema.SObjectType.Inventory__c.getRecordTypeInfosByDeveloperName().get('All_Inventory').getRecordTypeId();
        insert testInventory;
        Product2 prodKit1 = new Product2();
        prodKit1.Source__c = 'EBS';
        prodKit1.Name = 'CS1614E';
        prodKit1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_VK').getRecordTypeId();
        prodKit1.ExternalId__c = 'CS1614E';
        prodKit1.Order_Model__c = 'CS1614E';
        prodKit1.Master_Product__c = 'CS1614E';
        prodKit1.Item_Description_DE__c = 'DEDescription';
        prodKit1.Item_Description_EN__c = 'ENDescription';
        prodKit1.Units_Per_Pallet_EA__c = 6;
        prodKit1.Units_Per_Inner_BOX_EA__c = 5;
        prodKit1.Units_Per_Master_Carton_EA__c = 7;
        insert prodKit1;
        
        Kit_Item__c kiProductAcc1 = new Kit_Item__c();
        kiProductAcc1.RecordTypeId = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get('Kits_and_Products').getRecordTypeId();
        kiProductAcc1.Kit__c = prodKit1.Id;
        kiProductAcc1.VK_Product__c = product1.Id;
        kiProductAcc1.ReferenceId__c = prodKit1.Order_Model__c + '-' + product1.Order_Model__c;
        insert kiProductAcc1;
        
        Kit_Item__c kiAcc1 = new Kit_Item__c();
        kiAcc1.RecordTypeId = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get('Kits_and_Accessories').getRecordTypeId();
        kiAcc1.Kit_Acc__c = product103.Id;
        kiAcc1.Accessories__c = product101.Id;
        kiAcc1.ReferenceId__c = product103.Order_Model__c + '-' + product101.Order_Model__c;
        insert kiAcc1;
        
        Kit_Item__c kiAcc2 = new Kit_Item__c();
        kiProductAcc1.RecordTypeId = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get('Products_and_Accessories').getRecordTypeId();
        kiProductAcc1.Product_Acc__c = product102.Id;
        kiProductAcc1.Accessories__c = product101.Id;
        kiProductAcc1.ReferenceId__c = product102.Order_Model__c + '-' + product101.Order_Model__c;
        insert kiAcc2;
        
        Kit_Item__c kiSubstituted= new Kit_Item__c();
        kiSubstituted.RecordTypeId = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get('Product_And_Substituted').getRecordTypeId();
        kiSubstituted.Product_Substituted__c = product102.Id;
        kiSubstituted.Substituted__c = product101.Id;
        kiSubstituted.ReferenceId__c = product102.Order_Model__c + '-' + product101.Order_Model__c+'-Substituted';
        insert kiSubstituted;
        
        Kit_Item__c kiRelated= new Kit_Item__c();
        kiRelated.RecordTypeId = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get('Product_And_Related').getRecordTypeId();
        kiRelated.Product_Related__c = product103.Id;
        kiRelated.Related__c = product101.Id;
        kiRelated.ReferenceId__c = product103.Order_Model__c + '-' + product101.Order_Model__c+'-Related';
        insert kiRelated;
        
        
        Pricebook2 pricebook = new Pricebook2(Name = 'Test Pricebook');
        insert pricebook;
        Pricebook_Entry__c priceEntry = new Pricebook_Entry__c(PriceBook__c = pricebook.Id);
        priceEntry.start_Date__c = Date.today().addDays(-7);
        priceEntry.End_Date__c = Date.today().addDays(7);
        priceEntry.UnitPrice__c = 10;
        priceEntry.Product__c = prodKit1.Id;
        priceEntry.PriceBook__c = pricebook.Id;
        priceEntry.CurrencyIsoCode = 'EUR';
        priceEntry.Start_Date__c = Date.today().addDays(-7);
        priceEntry.End_Date__c = Date.today().addDays(7);
        insert priceEntry;
        
        Pricebook_Entry__c priceEntryKit = new Pricebook_Entry__c(PriceBook__c = pricebook.Id);
        priceEntryKit.start_Date__c = Date.today().addDays(-7);
        priceEntryKit.End_Date__c = Date.today().addDays(7);
        priceEntryKit.UnitPrice__c = 10;
        priceEntryKit.Product__c = product103.Id;
        priceEntryKit.PriceBook__c = pricebook.Id;
        priceEntryKit.CurrencyIsoCode = 'EUR';
        priceEntryKit.Start_Date__c = Date.today().addDays(-7);
        priceEntryKit.End_Date__c = Date.today().addDays(7);
        insert priceEntryKit;
        
        Pricebook_Entry__c priceEntryProduct = new Pricebook_Entry__c(PriceBook__c = pricebook.Id);
        priceEntryProduct.start_Date__c = Date.today().addDays(-7);
        priceEntryProduct.End_Date__c = Date.today().addDays(7);
        priceEntryProduct.UnitPrice__c = 10;
        priceEntryProduct.Product__c = product102.Id;
        priceEntryProduct.PriceBook__c = pricebook.Id;
        priceEntryProduct.CurrencyIsoCode = 'EUR';
        priceEntryProduct.Start_Date__c = Date.today().addDays(-7);
        priceEntryProduct.End_Date__c = Date.today().addDays(7);
        insert priceEntryProduct;
        
        Pricebook_Entry__c priceEntryAcc = new Pricebook_Entry__c(PriceBook__c = pricebook.Id);
        priceEntryAcc.start_Date__c = Date.today().addDays(-7);
        priceEntryAcc.End_Date__c = Date.today().addDays(7);
        priceEntryAcc.UnitPrice__c = 10;
        priceEntryAcc.Product__c = product101.Id;
        priceEntryAcc.PriceBook__c = pricebook.Id;
        priceEntryAcc.CurrencyIsoCode = 'EUR';
        priceEntryAcc.Start_Date__c = Date.today().addDays(-7);
        priceEntryAcc.End_Date__c = Date.today().addDays(7);
        insert priceEntryAcc;
        
        Sales_Program__c salesP = new Sales_Program__c(Name = 'Test Sales Program', Brands__c = 'Test Brand', Customer__c = acc.Id);
        salesP.Price_Book__c = pricebook.Id;
        insert salesP;
        Purchase_Order__c objPurchaseOrder = new Purchase_Order__c();
        objPurchaseOrder.ORG_ID__c = 'EEG';
        objPurchaseOrder.Warehouse__c = 'Germany (DSV)';
        objPurchaseOrder.Customer__c = acc.Id;
        objPurchaseOrder.WearHouse_In_EBS__c = 'EEG';
        objPurchaseOrder.Order_Type__c = CCM_Constants.DSVR;
        objPurchaseOrder.Customer_PO_Num__c = 'P001';
        objPurchaseOrder.Pricing_Date__c = Date.today().addDays(-5);
        objPurchaseOrder.Product__c = product1.Id;
        objPurchaseOrder.RecordTypeId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get('Regular_Order').getRecordTypeId();
        insert objPurchaseOrder;
        Purchase_Order_Item__c objPurchaseOrderItem = new Purchase_Order_Item__c();
        objPurchaseOrderItem.Purchase_Order__c = objPurchaseOrder.Id;
        objPurchaseOrderItem.Product__c = prodKit1.Id;
        objPurchaseOrderItem.Quantity__c = 2;
        insert objPurchaseOrderItem;
        Order order = new Order(Name = 'test order', Customer_EU__c = acc.Id, AccountId = acc.Id, EffectiveDate = Date.today(), Status = 'Draft');
        insert order;
        List<Order_Item__c> testOrderItems = new List<Order_Item__c>();
        for (Integer i = 1; i <= 5; i++){
            Order_Item__c testOrderItem = new Order_Item__c(Order__c = order.Id, Product__c = product1.Id, Pricing_Date__c = Date.today(), Order_Quantity__c = 10, Inventory__c = 'Red');
            testOrderItems.add(testOrderItem);
        }
        insert testOrderItems;
        Recommended_Accessories__c ra = new Recommended_Accessories__c(Access_Model__c = 'BH1001', Masert_Model__c = 'CS1614E');
        insert ra;
        Alert_Message__c am = new Alert_Message__c();
        am.Start_Date__c = Date.today().addDays(-7);
        am.End_Date__c = Date.today().addDays(7);
        am.Customer_Account__c = 'E7123';
        am.Alert_Mode__c = 'Create';
        am.Alert_Message__c = 'test Message';
        insert am;
        Modifier__c modifier1 = new Modifier__c(Name = 'Test');
        insert modifier1;
        Modifier_Entry__c modifierentry = new Modifier_Entry__c(Modifier_Header_Number__c = 'M123', ExternalID__c = 'M123', Modifier__c = modifier1.Id);
        insert modifierentry;
        MasterProductPrice__c testMasterProductPrice = new MasterProductPrice__c(Account__c = acc.Id, Product__c = prodKit1.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today().addDays(7), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = false, Modifier_Entry__c = modifierentry.Id);
        insert testMasterProductPrice;
        MasterProductPrice__c testMasterProductPriceKit = new MasterProductPrice__c(Account__c = acc.Id, Product__c = product103.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today().addDays(7), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = false, Modifier_Entry__c = modifierentry.Id);
        insert testMasterProductPriceKit;
        MasterProductPrice__c testMasterProductPriceProduct = new MasterProductPrice__c(Account__c = acc.Id, Product__c = product102.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today().addDays(7), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = false, Modifier_Entry__c = modifierentry.Id);
        insert testMasterProductPriceProduct;
        MasterProductPrice__c testMasterProductPriceAcc = new MasterProductPrice__c(Account__c = acc.Id, Product__c = product101.Id, Start_Date__c = Date.today().addDays(-7), End_Date__c = Date.today().addDays(7), Final_Price__c = 90, List_Price__c = 100, Has_AllItem__c = false, Modifier_Entry__c = modifierentry.Id);
        insert testMasterProductPriceAcc;
        
    }
    @IsTest
    static void testRefreshOrderInventoryBatch(){
        Test.startTest();
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        CCM_RequestPurchaseOrderController.QueryCustomer(String.valueOf(acc.Id));
        CCM_RequestPurchaseOrderController.QueryAuthBrand(String.valueOf(acc.Id));
        CCM_RequestPurchaseOrderController.QueryCustomerType(String.valueOf(acc.Id));
        CCM_RequestPurchaseOrderController.QueryAuthBrandInfo(String.valueOf(acc.Id));
        Test.stopTest();
    }
    @IsTest
    public static void testCreatePO(){
        try{
            Account acc = [select id
                           from account
                           where Name = 'Test Account'
                           limit 1];
            
            Contact conta = [select Id from Contact where LastName='Test Contact' limit 1];
            CCM_RequestPurchaseOrderController.createPO(acc.Id, 'Germany (DSV)', true, Date.today().addDays(-5), '1111', 'Regular Order', '', '', conta.Id);
            
        }catch(Exception e){
            
        }
        
    }
    @IsTest
    public static void testCreatePO2(){
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        acc.Country__c='DE';
        update acc;
        Contact conta = [select Id from Contact where LastName='Test Contact' limit 1];
        CCM_RequestPurchaseOrderController.createPO(acc.Id, 'Germany (DSV)', true, Date.today().addDays(-5), '1111', 'Pre_season_Order', '', '', conta.Id);
        
        acc.Country__c='DE';
        update acc;
        CCM_RequestPurchaseOrderController.createPO(acc.Id, 'Germany (DSV)', true, Date.today().addDays(-5), '1111', 'Regular Order', '', '', conta.Id);
        CCM_RequestPurchaseOrderController.createPO(acc.Id, 'Germany (DSV)', true, Date.today().addDays(-5), '1111', 'Pre_season_Order', '', '', conta.Id);
        
    }
    @IsTest
    public static void testGetAccessProductInfo(){
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        CCM_RequestPurchaseOrderController.GetAccessProductInfo('BH10013', String.valueOf(acc.Id), '', 'Germany (DSV)', Date.today().addDays(-5),String.valueOf(acc.Id));
        CCM_RequestPurchaseOrderController.GetSubstituteProductInfo('BH10012', String.valueOf(acc.Id),  'Germany (DSV)', String.valueOf(Date.today().addDays(-5)),String.valueOf(acc.Id));
        CCM_RequestPurchaseOrderController.GetRelatedProductInfo('BH10013', String.valueOf(acc.Id),  'Germany (DSV)', String.valueOf(Date.today().addDays(-5)), String.valueOf(acc.Id));
    }
    @IsTest
    public static void testAueryAlertMessage(){
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        CCM_RequestPurchaseOrderController.queryAlertMessage('E7123', 'CS1614E');
    }
    @IsTest
    public static void testGetProductInfo(){
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        Date thisDate = Date.today();
        //Planning_Tool__c PT_c = new Planning_Tool__c(Product__c='1', Current_Month__c=thisDate.Month(), Supply_Year__c=thisDate.Year(), Territory_NO__c = '');
        //insert PT_c;
        CCM_RequestPurchaseOrderController.GetProductInfo('1', acc.Id, date.today().addDays(-5));
        List<String> Ids = new List<String>();
        Ids.add('1');
        List<Inventory__c> lstInventory = [
            SELECT Id,Product__c,Available_QTY__c FROM Inventory__c];
        delete lstInventory;
        CCM_RequestPurchaseOrderController.GetProductInfo('1', acc.Id, date.today().addDays(-5));
    }
    @IsTest
    public static void testGetProductListInfo(){
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        Product2 kit = [select id
                        from Product2
                        WHERE Order_Model__c = 'CS1614E'
                        limit 1];
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
       
        CCM_RequestPurchaseOrderController.GetProductListInfo(kit.Id, 10, acc.Id, 10, 10, 'MKT', '', Date.today().addDays(-5), po.Id);
    }
    @IsTest
    public static void testCheckDuplicatePO(){
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        Product2 kit = [select id
                        from Product2
                        WHERE Order_Model__c = 'CS1614E'
                        limit 1];
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        CCM_RequestPurchaseOrderController.checkDuplicatePO('P001', true, acc.Id, po.Id);
        CCM_RequestPurchaseOrderController.checkDuplicatePO('P002', true, acc.Id, null);
    }
    @IsTest
    public static void testUpsertPurchaseOrderItem(){
        Sales_Program__c ab = [select id
                               from Sales_Program__c
                               limit 1];
        Purchase_Order__c po = [select id, Customer__c, Freight_Term__c, Inco_Term__c, Expected_Delivery_Date__c, Multi_Header_Promotion__c, Header_Discount_Amount__c, Header_Discount__c, Promotion__c, Payment_Term__c, Freight_Fee__c, Total_Quantity__c
                                from Purchase_Order__c
                                limit 1];
        Product2 prodKit1 = new Product2();
        prodKit1.Source__c = 'EBS';
        prodKit1.Name = 'CS1614E';
        prodKit1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_VK').getRecordTypeId();
        prodKit1.ExternalId__c = 'CS1614E';
        prodKit1.Order_Model__c = 'CS1614E';
        prodKit1.Master_Product__c = 'CS1614E';
        prodKit1.Item_Description_DE__c = 'DEDescription';
        prodKit1.Item_Description_EN__c = 'ENDescription';
        prodKit1.Units_Per_Pallet_EA__c = 6;
        prodKit1.Units_Per_Inner_BOX_EA__c = 5;
        prodKit1.Units_Per_Master_Carton_EA__c = 7;
        insert prodKit1;
        Purchase_Order_Item__c objPurchaseOrderItemTest = new Purchase_Order_Item__c(Product__c = prodKit1.Id, Quantity__c = 2);
      
        
        List<Purchase_Order_Item__c> poiList  = new List<Purchase_Order_Item__c>();
        poiList.add(objPurchaseOrderItemTest);
        Test.startTest();
        CCM_RequestPurchaseOrderController.UpsertPurchaseOrderItem(poiList, po, ab.Id, '');
        try{
            CCM_RequestPurchaseOrderController.InsertPurchaseInfoProtal(poiList, po, ab.Id, '');
        } catch (Exception e){
            system.debug('报错行数--->'+e.getMessage());
        }
        Test.stopTest();
    }
    
    @IsTest
    public static void testUpsertPurchaseOrderItem2(){
        Sales_Program__c ab = [select id
                               from Sales_Program__c
                               limit 1];
        Purchase_Order__c po = [select id, Customer__c, Freight_Term__c, Inco_Term__c, Expected_Delivery_Date__c, Multi_Header_Promotion__c, Header_Discount_Amount__c, Header_Discount__c, Promotion__c, Payment_Term__c, Freight_Fee__c, Total_Quantity__c
                                from Purchase_Order__c
                                limit 1];
        Product2 prodKit1 = new Product2();
        prodKit1.Source__c = 'EBS';
        prodKit1.Name = 'CS1614E';
        prodKit1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_VK').getRecordTypeId();
        prodKit1.ExternalId__c = 'CS1614E';
        prodKit1.Order_Model__c = 'CS1614E';
        prodKit1.Master_Product__c = 'CS1614E';
        prodKit1.Item_Description_DE__c = 'DEDescription';
        prodKit1.Item_Description_EN__c = 'ENDescription';
        prodKit1.Units_Per_Pallet_EA__c = 6;
        prodKit1.Units_Per_Inner_BOX_EA__c = 5;
        prodKit1.Units_Per_Master_Carton_EA__c = 7;
        insert prodKit1;
        Purchase_Order_Item__c objPurchaseOrderItemTest = new Purchase_Order_Item__c(Product__c = prodKit1.Id, Quantity__c = 2);
      
        
        List<Purchase_Order_Item__c> poiList  = new List<Purchase_Order_Item__c>();
        poiList.add(objPurchaseOrderItemTest);
        Test.startTest();
        
        try{
            CCM_RequestPurchaseOrderController.InsertPurchaseInfoProtal(poiList, po, ab.Id, '');
        } catch (Exception e){
            system.debug('报错行数--->'+e.getMessage());
        }
        Test.stopTest();
    }
    @IsTest
    public static void testOther(){
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        Product2 kit = [select id
                        from Product2
                        WHERE Order_Model__c = 'CS1614E'
                        limit 1];
        Purchase_Order__c po = [select id
                                from Purchase_Order__c
                                limit 1];
        CCM_RequestPurchaseOrderController.getPicklistOption('Account', 'Country__c', 'A', false);
        CCM_RequestPurchaseOrderController.CaulateAllListPrice(new List<Decimal>{ 12.2, 23 }, acc.Id);
        CCM_RequestPurchaseOrderController.getPromotionsByProduct(kit.Id, acc.Id, false, false, Date.today().addDays(-5));
        CCM_RequestPurchaseOrderController.QueryPirchaseAndItemInfo(po.Id, false);
        try{
            CCM_RequestPurchaseOrderController.getWholePromotionOffering('');
        } catch (Exception e){

        }
        try{
            CCM_RequestPurchaseOrderController.getQuantityLimitInventoryCondition('');
        } catch (Exception e){

        }
    }

    @isTest
    public static void testCaulateQty(){
        Product_Data__c productData = new Product_Data__c(DSV__c='Germany (DSV)', Field_Value__c='UnitsPerPallet', Item_Class__c='TLS_Product',
                                            Meaasge__c='pieces per Palltet Qty', Pallet_Item__c='YES', Sales_Channel__c='Distributor');
        insert productData;
        
        String result = CCM_RequestPurchaseOrderController.CaulateQty('Distributor', 'TLS_Product', 'Germany (DSV)',
                                                                'Yes', 1.0, 2.0, 3.0, 4.0, 5.0, 6.0);
        
        System.debug(result);
    }

    @IsTest
    public static void getProductPriceAnInventoryTest() {
        Account acc = [select id
                       from account
                       where Name = 'Test Account'
                       limit 1];
        Product2 kit = [select id
                        from Product2
                        WHERE Order_Model__c = 'CS1614E'
                        limit 1];
        Map<String, Object> productQuantityMap = new Map<String, Object>{kit.Id => 1};
        String productInfo = JSON.serialize(productQuantityMap);
        CCM_RequestPurchaseOrderController.getProductPriceAnInventory(acc.Id, productInfo);

    }
    

}