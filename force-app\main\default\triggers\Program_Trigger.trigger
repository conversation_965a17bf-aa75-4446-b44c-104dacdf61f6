trigger Program_Trigger on Sales_Program__c (before insert, after insert, before update, after update, before delete, after delete){
    
    Boolean isRun = CCM_TriggerSwitcher.triggerStatus.get('Program_Trigger');
    if(isRun != null && !isRun) {
        return;
    }

    new Triggers()
    .bind(Triggers.Evt.afterinsert, new CCM_NewAuthorizedBrandAutoApproveHandler())
    .bind(Triggers.Evt.beforeupdate, new CCM_AuthorizedBrandUpdateHandler())//Aria
    .bind(Triggers.Evt.afterupdate, new CCM_AuthorizedBrandAfterUpdateHandler())
    .bind(Triggers.Evt.afterupdate, new CCM_UpdateChannelwithAssGroupPT())//Zoe
    .bind(Triggers.Evt.beforeinsert, new CCM_UpdateChannelwithAssGroupPT())//Zoe
    .bind(Triggers.Evt.afterinsert, new CCM_UpdateAccountInfoHandler())//Zoe
    .bind(Triggers.Evt.beforeinsert, new CCM_UpsertAuthorizedBrandWithTermHandler())//Vince
    .bind(Triggers.Evt.beforeupdate, new CCM_UpsertAuthorizedBrandWithTermHandler())//Vince
    .bind(Triggers.Evt.afterupdate, new CCM_AuthorizedBrandPriceChangedHdl())//Vince
    .bind(Triggers.Evt.afterinsert, new CCM_AuthorizedBrandPriceChangedHdl())//Vince 0729
    .bind(Triggers.Evt.beforeupdate, new CCM_AuthorizedBrandProspectHandler())//Aria.2023/07/26
    .bind(Triggers.Evt.beforeinsert, new CCM_AuthorizedBrandProspectHandler())//Aria.2023/07/26
    .bind(Triggers.Evt.beforeupdate, new CCM_ABSalesTargetHandler())//Aria.2023/07/28
    .bind(Triggers.Evt.beforeinsert, new CCM_ABSalesTargetHandler())//Aria.2023/07/28
    .bind(Triggers.Evt.afterinsert, new CCM_ABSalesTargetHandler())//Aria 2023/08/08
    .manage();
}