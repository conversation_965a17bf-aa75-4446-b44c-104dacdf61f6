<aura:component description="CCM_Community_WarrantyOrderDetail"
                implements="forceCommunity:availableForAllPageTypes,force:hasRecordId,flexipage:availableForAllPageTypes,force:appHostable"
                controller="CCM_WarrantyOrderInfoCtl"
                access="global">
    <!-- claim -->
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="accId" type="String" default=""/>
    <aura:attribute name="processData" type="List" default="[]"/>
    <aura:attribute name="currencySymbol" type="String" default="EUR"/>
    <aura:attribute name="basicInformation" type="Map" default=""/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="shipmentInfo" type="List" default="[]"/>
    <aura:attribute name="attachment" type="Object" default="[]"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="orderType" type="String" default=""/>
    <aura:attribute name="isEdit" type="Boolean" default="false"/>
    <aura:attribute name="productOptions" type="List" default="[]"/>
    <aura:attribute name="shipType" type="String" default=""/>
    <aura:attribute name="shipTypeOpyions" type="List" default="[]"/>
    <aura:attribute name="isPortal" type="Boolean" default="false"/>
    <aura:attribute name="isDE" type="Boolean" default="false"/>

    <!-- edit -->
    <aura:attribute name="editInfo" type="Map" default=""/>

    
    <!-- <aura:attribute name="editBasicInformation" type="Object" default=""/> -->


    <aura:attribute name="afterSyncId" type="String" default=""/>
    <aura:attribute name="poRecordId" type="String" default=""/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="order" type="Object" default=""/>
    <aura:attribute name="invoiceInfo" type="List" default="[]"/>
    <aura:attribute name="shipmentColumns" type="List" default="[]"/>
    <aura:attribute name="invoiceColumns" type="List" default="[]"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="paymentTermVal" type="String" default=""/>
    <aura:attribute name="freightTermVal" type="String" default=""/>
    <aura:attribute name="isInnerUser" type="Boolean" default="false"/>
    <aura:attribute name="isShow" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareProducts" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareShipments" type="Boolean" default="false"/>
    <aura:attribute name="isCanReverse" type="Boolean" default="false"/>
    <aura:attribute name="reverseOrderLink" type="String" default=""/>
    <aura:attribute name="attachmentColumns" type="List" default="[]"/>
    <aura:attribute name="attachmentTypeOptions" type="List" default="[]"/>
    <aura:attribute name="isProspect" type="Boolean" default="false"/>

    
    <aura:attribute name="attachmentItem" type="Map" default="{}"/>
    <aura:attribute name="uploadList" type="List" default="[]"/>
    <aura:attribute name="uploadModalFlag" type="Boolean" default="false"/>
    <aura:attribute name="uploadFinished" type="Boolean" default="false" />
    <aura:attribute name="attachmentName" type="String" default=""/>
    <aura:attribute name="attachmentType" type="String" default=""/>
    <aura:attribute name="CountLine" type="String" default=""/>
    <aura:attribute name="showSync" type="Boolean" default="true"/>
    <aura:attribute name="userType" type="String" default="InsideSales"/>
    <aura:attribute name="resalesStatus" type="String" default=""/>
    
    <aura:attribute name="isDropShipOpt" type="List" default="[{'label': 'Yes', 'value': 'Y'},{'label': 'No', 'value': 'N'}]"/>
    <aura:attribute name="isDropshipOrder" type="String" default=""/>


    <aura:attribute name="isSync" type="Boolean" default="false"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="{!(v.isPortal ? 'portal-wrap slds-box slds-theme_default' : 'slds-box slds-theme_default')}">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium">
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <c:CCM_PathProcess processData="{!v.processData}" currentStep="{!v.currentStep}"/>
            <!-- Basic Information -->
            <c:CCM_Section title="Basic Information" expandable="true">
                <lightning:layout multipleRows="true">
                    <!-- Customer​ -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                    <c:CCM_Field label="Customer​">
                        {!v.basicInformation.Customer}
                    </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Customer Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Customer Number">
                            {!v.basicInformation.CustomerNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Warranty Claim Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Warranty Claim Number">
                            {!v.basicInformation.warrantyClaimNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Salesperson -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Salesperson">
                            {!v.basicInformation.Salesperson}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Order Date">
                            {!v.basicInformation.OrderDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Created Date in EBS -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Order Created Date in EBS">
                            {!v.basicInformation.OrderCreatedinEBS}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Number -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Order Number">
                            {!v.basicInformation.OrderNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Status -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Order Status">
                            {!v.basicInformation.OrderStatus}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Order Type -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Order Type">
                            {!v.basicInformation.OrderType}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Expected Delivery Date -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="Expected Delivery Date">
                            {!v.basicInformation.ExpectedDeliveryDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>
            <!-- Delivery Information -->
            <c:CCM_Section title="{!$Label.c.CCM_DeliveryInformation}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <!-- Bill To Address -->
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small required-wrap">
                        <c:CCM_Field label="{!$Label.c.Order_BillToAddress}">
                            <aura:if isTrue="{!v.isEdit}">
                                <c:CCM_Community_LookUp
                                    fieldName="{!$Label.c.CCM_BillToAddress}"
                                    selectedValue="{!v.editInfo.billToAddress}"
                                    onSelect="{!c.changeBillToAddress}"
                                    customerId="{!v.customerId}"
                                    aura:id="billToAddress"
                                    class="field-required"
                                />
                                <div aura:id="billToAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!v.basicInformation.BillToAddress.CompanyName}">
                                        <p>{!v.basicInformation.BillToAddress.CompanyName}</p>
                                    </aura:if>
                                    <aura:if isTrue="{!v.basicInformation.BillToAddress.Street}">
                                        <p>{!v.basicInformation.BillToAddress.Street}</p>
                                    </aura:if>
                                    <aura:if isTrue="{!(v.basicInformation.BillToAddress.City || v.basicInformation.BillToAddress.PostCode)}">
                                        <p>
                                            <span>{!v.basicInformation.BillToAddress.PostCode}&nbsp;&nbsp;{!v.basicInformation.BillToAddress.City}</span>
                                        </p>
                                    </aura:if>
                                    <aura:if isTrue="{!v.basicInformation.BillToAddress.Country}">
                                        <p>{!v.basicInformation.BillToAddress.Country}</p>
                                    </aura:if>
                                </aura:set>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <!-- Ship Type -->
                    <!-- <lightning:layoutItem padding="horizontal-small" size="2" class="slds-p-bottom_small">
                        <c:CCM_Field label="Ship Type">
                            <aura:if isTrue="{!v.isEdit}">
                                <lightning:combobox
                                    class="address-wrap"
                                    label="​"
                                    name="Ship Type​"
                                    value="{!v.editInfo.shipType}" 
                                    options="{!v.shipTypeOpyions}"
                                    aura:id="shipType"
                                />
                                <aura:set attribute="else">
                                    {!v.basicInformation.shipType}
                                </aura:set>
                            </aura:if>
                        </c:CCM_Field>
                    </lightning:layoutItem> -->
                    <!-- Ship To Address -->
                    <aura:if isTrue="{!v.isEdit}">
                        <aura:if isTrue="{!v.editInfo.shipType == 'Ship To'}">
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small required-wrap">
                                <c:CCM_Field label="{!$Label.c.Order_ShipToAddress}">
                                    <c:CCM_Community_LookUp
                                        fieldName="{!$Label.c.CCM_ShipToAddress}"
                                        selectedValue="{!v.editInfo.shipToAddress}"
                                        onSelect="{!c.changeShipToAddress}"
                                        customerId="{!v.customerId}"
                                        aura:id="shipToAddress"
                                        class="{!(v.editInfo.shipType == 'Ship To' ? 'field-required' : '')}"
                                    />
                                    <div aura:id="shipToAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                        </aura:if>
                        <aura:set attribute="else">
                            <aura:if isTrue="{!v.basicInformation.shipType == 'Ship To'}">
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small required-wrap">
                                    <c:CCM_Field label="{!$Label.c.Order_ShipToAddress}">
                                        <aura:if isTrue="{!v.basicInformation.ShipToAddress.CompanyName}">
                                            <p>{!v.basicInformation.ShipToAddress.CompanyName}</p>
                                        </aura:if>
                                        <aura:if isTrue="{!v.basicInformation.ShipToAddress.Street}">
                                            <p>{!v.basicInformation.ShipToAddress.Street}</p>
                                        </aura:if>
                                        <aura:if isTrue="{!(v.basicInformation.ShipToAddress.City || v.basicInformation.ShipToAddress.PostCode)}">
                                            <p>
                                                <span>{!v.basicInformation.ShipToAddress.PostCode}&nbsp;&nbsp;{!v.basicInformation.ShipToAddress.City}</span>
                                            </p>
                                        </aura:if>
                                        <aura:if isTrue="{!v.basicInformation.ShipToAddress.Country}">
                                            <p>{!v.basicInformation.ShipToAddress.Country}</p>
                                        </aura:if>
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                            </aura:if>
                        </aura:set>
                    </aura:if>
                    <!-- 详细字段 -->
                    <aura:if isTrue="{!v.isEdit}">
                        <aura:if isTrue="{!v.editInfo.shipType == 'Other'}">
                            <!-- Country -->
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="Country">
                                    <lightning:input label="Country" value="{!v.editInfo.country}" maxlength="255" class="address-wrap"/>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <!-- province -->
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="Province">
                                    <lightning:input label="Province" value="{!v.editInfo.province}" maxlength="255" class="address-wrap"/>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <!-- city -->
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="City">
                                    <lightning:input label="City" value="{!v.editInfo.city}" maxlength="255" class="address-wrap"/>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <!-- street -->
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="Street">
                                    <lightning:input label="Street" value="{!v.editInfo.address}" maxlength="255" class="address-wrap"/>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <!-- postalCode -->
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="PostalCode">
                                    <lightning:input label="PostalCode" value="{!v.editInfo.postalCode}" maxlength="255" class="address-wrap"/>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                        </aura:if>
                        <aura:set attribute="else">
                            <aura:if isTrue="{!v.basicInformation.shipType == 'Other'}">
                                <!-- Country -->
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="Country">
                                        {!v.basicInformation.country}
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                                <!-- province -->
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="Province">
                                        {!v.basicInformation.province}
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                                <!-- city -->
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="City">
                                        {!v.basicInformation.city}
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                                <!-- street -->
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="Street">
                                        {!v.basicInformation.address}
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                                <!-- postalCode -->
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="PostalCode">
                                        {!v.basicInformation.postalCode}
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                            </aura:if>
                        </aura:set>
                    </aura:if>
                </lightning:layout>
            </c:CCM_Section>
            <!-- Product Item Information -->
            <c:CCM_Section title="Order information" expandable="true" >
                <div class="slds-p-left_medium slds-p-right--medium">
                    <div class="table-wrap">
                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productTable" role="grid">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Action">{!$Label.c.CCM_Action}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="ID">{!$Label.c.CCM_Line}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Product Description -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col" style="width: 302px;">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Product Description">{!$Label.c.Order_ProductDescription}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Model #  -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Model #">{!$Label.c.Order_Model}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- UOM -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="UOM">{!$Label.c.Order_UOM}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Order Date">{!$Label.c.Order_OrderDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Order Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Order Qty">{!$Label.c.CCM_OrderQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Inventory -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                <span class="slds-truncate" title="Inventory">{!$Label.c.Order_Inventory}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Back Order Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Back Order Qty">{!$Label.c.Order_BackOrderQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Release to Warehouse Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Release to Warehouse Qty">{!$Label.c.Order_ReleasetowarehouseQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Picked & Packed Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Picked And Packed Qty">{!$Label.c.Order_PickedandPackedqty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Shipped Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Shipped Qty">{!$Label.c.Order_ShippedQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Cancelled Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Cancelled Qty">{!$Label.c.Order_CancelledQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Returned Qty -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Returned Qty">{!$Label.c.CCM_ReturnedQty}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Est Replenish Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Est Replenish Date">{!$Label.c.Order_EstReplenishDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Schedule Ship Date -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Schedule Ship Date">{!$Label.c.Order_ScheduleShipDate}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Unit Net Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Unit Net Price">{!$Label.c.Order_UnitNetPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Total Net Price -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Total Net Price">{!$Label.c.Order_TotalNetPrice}</span>
                                            </div>
                                        </a>
                                    </th>
                                    <!-- Remark -->
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="Remark">{!$Label.c.Order_Remark}</span>
                                            </div>
                                        </a>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="tbody-wrap">
                                <aura:iteration items="{! (v.isEdit ? v.editInfo.orderItemList : v.orderItemList)}" var="orderItem" indexVar="index">
                                    <tr aria-selected="false" id="{!index}" data-expanded="false" class="index-wrap" onclick="{!c.showToolList}">
                                        <!-- Action -->
                                        <td role="gridcell">
                                            <div class="slds-truncate" title="">
                                                <aura:if isTrue="{!orderItem.ProductList}">
                                                    <lightning:icon iconName="utility:chevrondown" size="xx-small" class="collapse_icn"/>
                                                </aura:if>
                                            </div>
                                        </td>
                                        <!-- line -->
                                        <td role="gridcell">
                                            <div class="slds-truncate" title="">
                                                {!index + 1}
                                            </div>
                                        </td>
                                        <!-- Product Description -->
                                        <td title="{!orderItem.ProductDescription}">
                                            <div class="picklist-label">
                                                <aura:if isTrue="{!v.isEdit}">
                                                    <c:CCM_Front_AutoPicklist
                                                        id="{!index}"
                                                        defaultOptions="{!v.productOptions}"
                                                        labelField="productDescription"
                                                        labelField1="orderModel"
                                                        labelId="productId"
                                                        onSelect="{!c.selectProduct}"
                                                        selectValue="{!orderItem.ProductDescription}"
                                                    />
                                                    <aura:set attribute="else">
                                                        {!orderItem.ProductDescription}
                                                    </aura:set>
                                                </aura:if>
                                            </div>
                                        </td>
                                        <!-- Model #  -->
                                        <td role="gridcell" title="{!orderItem.Model}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.Model}</span>
                                            </div>
                                        </td>
                                        <!-- UOM -->
                                        <td role="gridcell" title="{!orderItem.UOM}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.UOM}</span>
                                            </div>
                                        </td>
                                        <!-- Order Date -->
                                        <td role="gridcell" title="{!orderItem.OrderDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.OrderDate}</span>
                                            </div>
                                        </td>
                                        <!-- Order Qty -->
                                        <td role="gridcell" title="{!orderItem.OrderQty}">
                                            <div class="picklist-label">
                                                <aura:if isTrue="{!v.isEdit}">
                                                    <lightning:input id="{!index}" type="number" value="{!orderItem.OrderQty}" maxlength="255" class="address-wrap"/>
                                                    <aura:set attribute="else">
                                                        <span>{!orderItem.OrderQty}</span>
                                                    </aura:set>
                                                </aura:if>
                                            </div>
                                        </td>
                                        <!-- Inventory -->
                                        <td role="gridcell" title="Name">
                                            <aura:if isTrue="{!orderItem.ProductList.length > 0}">
                                                <aura:set attribute="else">
                                                    <div class="slds-truncate icon-position-wrap">
                                                        <aura:if isTrue="{!orderItem.Inventory == 'Red'}">
                                                            <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                            <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                                <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                    <lightning:helptext class="icon-size" content="The order cannot be fulfilled now, and it shall be fulfilled after a period of time​"/>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                            </div>
                                                        </aura:if>
                                                        <aura:if isTrue="{!orderItem.Inventory == 'Yellow'}">
                                                            <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                            <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                                <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                    <aura:if isTrue="{! (v.warehouse == 'China (DI)')}">
                                                                        <lightning:helptext class="icon-size" content="Availability will be checked by Chervon NJ"/>
                                                                        <aura:set attribute="else">
                                                                            <lightning:helptext class="icon-size" content="Please proceed with order placement ASAP due to insufficient stock inventory / planning tool​"/>
                                                                        </aura:set>
                                                                    </aura:if>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                            </div>
                                                        </aura:if>
                                                        <aura:if isTrue="{!orderItem.Inventory == 'Green'}">
                                                            <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                            <div class="slds-truncate clear-user-agent-styles icon-position-kit">
                                                                <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                    <lightning:helptext class="icon-size" content="Please proceed with order placement​"/>
                                                                    <aura:set attribute="else">
                                                                        <lightning:helptext class="icon-size" content="Please proceed with order placement"/>
                                                                    </aura:set>
                                                                </aura:if>
                                                            </div>
                                                        </aura:if>
                                                    </div>
                                                </aura:set>
                                            </aura:if>
                                        </td>
                                        <!-- Back Order Qty -->
                                        <td role="gridcell" title="{!orderItem.BackOrderQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.BackOrderQty}</span>
                                            </div>
                                        </td>
                                        <!-- Release to Warehouse Qty -->
                                        <td role="gridcell" title="{!orderItem.ReleasetoWarehouseQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.ReleasetoWarehouseQty}</span>
                                            </div>
                                        </td>
                                        <!-- Picked & Packed Qty -->
                                        <td role="gridcell" title="{!orderItem.PickedPackedQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.PickedPackedQty}</span>
                                            </div>
                                        </td>
                                        <!-- Shipped Qty -->
                                        <td role="gridcell" title="{!orderItem.ShippedQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.ShippedQty}</span>
                                            </div>
                                        </td>
                                        <!-- Cancelled Qty -->
                                        <td role="gridcell" title="{!orderItem.CancelledQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.CancelledQty}</span>
                                            </div>
                                        </td>
                                        <!-- Returned Qty -->
                                        <td role="gridcell" title="{!orderItem.ReturnedQty}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.ReturnedQty}</span>
                                            </div>
                                        </td>
                                        <!-- Est Replenish Date -->
                                        <td role="gridcell" title="{!orderItem.EstReplenishDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.EstReplenishDate}</span>
                                            </div>
                                        </td>
                                        <!-- Schedule Ship Date​ -->
                                        <td role="gridcell" title="{!orderItem.ScheduleShipDate}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.ScheduleShipDate}</span>
                                            </div>
                                        </td>
                                        <!-- Unit Net Price -->
                                        <td role="gridcell" title="{!orderItem.UnitNetPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.UnitNetPrice}" style="currency" currencyCode="{!orderItem.currencyCode}"/>
                                            </div>
                                        </td>
                                        <!-- Total Net Price -->
                                        <td role="gridcell" title="{!orderItem.TotalNetPrice}">
                                            <div class="slds-truncate">
                                                <lightning:formattedNumber value="{!orderItem.TotalNetPrice}" style="currency" currencyCode="{!orderItem.currencyCode}"/>
                                            </div>
                                        </td>
                                        <!-- Remark -->
                                        <td role="gridcell" title="{!orderItem.Remark}">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <span>{!orderItem.Remark}</span>
                                            </div>
                                        </td>
                                    </tr>
                                    <aura:if isTrue="{!orderItem.ProductList}">
                                        <tr aria-selected="false" class="slds-hint-parent" id="{!('row' + index)}" style="display: none">
                                            <td colspan="25">
                                                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productToolTable" role="grid">
                                                    <thead>
                                                        <tr class="slds-line-height_reset">
                                                            <!-- No. -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="No.">{!$Label.c.CCM_Line}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Product Description -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 302px;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Product Description">{!$Label.c.Order_ProductDescription}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Model #  -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Model #">{!$Label.c.Order_Model}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- UOM -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="UOM">{!$Label.c.Order_UOM}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Order Date -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Order Date">{!$Label.c.Order_OrderDate}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Order Qty -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Order Qty">{!$Label.c.CCM_OrderQty}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Inventory -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="Inventory">{!$Label.c.Order_Inventory}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- List Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="List Price">{!$Label.c.Order_ListPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Unit Net Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Unit Net Price">{!$Label.c.Order_UnitNetPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Total Net Price -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="Total Net Price">{!$Label.c.Order_TotalNetPrice}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <aura:iteration items="{!orderItem.ProductList}" var="toolItem" indexVar="toolIndex">
                                                            <tr aria-selected="false" class="slds-hint-parent">
                                                                <!-- line -->
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                        {!index + 1}.{!toolIndex + 1}
                                                                    </div>
                                                                </td>
                                                                <!-- Product Description -->
                                                                <td role="gridcell" title="{!orderItem.ProductDescription}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!toolItem.ProductDescription}
                                                                    </div>
                                                                </td>
                                                                <!-- Model #  -->
                                                                <td role="gridcell" title="{!orderItem.Model}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        <span>{!toolItem.Model}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- UOM -->
                                                                <td role="gridcell" title="{!orderItem.OrderDate}">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        <span>{!toolItem.UOM}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- Order Date -->
                                                                <td role="gridcell" title="{!orderItem.RequestDate}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <span>{!toolItem.OrderDate}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- Order Qty -->
                                                                <td role="gridcell" title="{!orderItem.OrderQty}">
                                                                    <div class="slds-truncate clear-user-agent-styles">
                                                                        <span>{!toolItem.OrderQty}</span>
                                                                    </div>
                                                                </td>
                                                                <!-- Inventory -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate icon-position-wrap">
                                                                        <aura:if isTrue="{!toolItem.Inventory == 'Red'}">
                                                                            <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>
                                                                                    <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                            <lightning:helptext class="icon-size" content="The order cannot be fulfilled now, and it shall be fulfilled after a period of time​"/>
                                                                                            <aura:set attribute="else">
                                                                                                <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                                            </aura:set>
                                                                                        </aura:if>
                                                                                    </div>
                                                                        </aura:if>
                                                                        <aura:if isTrue="{!toolItem.Inventory == 'Yellow'}">
                                                                            <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>
                                                                                    <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                            <aura:if isTrue="{! (v.warehouse == 'China (DI)')}">
                                                                                                <lightning:helptext class="icon-size" content="Availability will be checked by Chervon NJ"/>
                                                                                                <aura:set attribute="else">
                                                                                                    <lightning:helptext class="icon-size" content="Please proceed with order placement ASAP due to insufficient stock inventory / planning tool​"/>
                                                                                                </aura:set>
                                                                                            </aura:if>
                                                                                            <aura:set attribute="else">
                                                                                                <lightning:helptext class="icon-size" content="Wait for internal sales team's confirmation"/>
                                                                                            </aura:set>
                                                                                        </aura:if>
                                                                                    </div>
                                                                        </aura:if>
                                                                        <aura:if isTrue="{!toolItem.Inventory == 'Green'}">
                                                                            <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>
                                                                                    <div class="slds-truncate clear-user-agent-styles icon-position-tool">
                                                                                        <aura:if isTrue="{! (v.userType == 'InsideSales')}">
                                                                                            <lightning:helptext class="icon-size" content="Please proceed with order placement​"/>
                                                                                            <aura:set attribute="else">
                                                                                                <lightning:helptext class="icon-size" content="Please proceed with order placement"/>
                                                                                            </aura:set>
                                                                                        </aura:if>
                                                                                    </div>
                                                                        </aura:if>
                                                                    </div>
                                                                </td>
                                                                <!-- List Price -->
                                                                <td role="gridcell" title="{!orderItem.ListPrice}">
                                                                    <div class="slds-truncate">
                                                                        <lightning:formattedNumber value="{!toolItem.ListPrice}" style="currency" currencyCode="{!toolItem.currencyCode}"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Unit Net Price -->
                                                                <td role="gridcell" title="{!toolItem.UnitNetPrice}">
                                                                    <div class="slds-truncate">
                                                                        <lightning:formattedNumber value="{!toolItem.UnitNetPrice}" style="currency" currencyCode="{!toolItem.currencyCode}"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Total Net Price -->
                                                                <td role="gridcell" title="{!toolItem.TotalNetPrice}">
                                                                    <div class="slds-truncate">
                                                                        <lightning:formattedNumber value="{!toolItem.TotalNetPrice}" style="currency" currencyCode="{!toolItem.currencyCode}"/>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </aura:iteration>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </aura:if>
                                </aura:iteration>
                            </tbody>
                        </table>
                    </div>
                    <div class="slds-clearfix slds-m-top--medium">
                        <div class="slds-grid slds-float--right">
                        <div class="slds-text-align--right">
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_TotalValue}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_FreightCost}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_VAT}:&nbsp;</div>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.Order_TotalDueAmount}:&nbsp;</strong></div>
                        </div>
                        <div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.basicInformation.TotalValue}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.basicInformation.FreightCost}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.basicInformation.vat}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.basicInformation.TotalDueAmount}" style="currency" currencyCode="{!v.currencySymbol}"/> </strong></div>
                        </div>
                    </div>
                    </div>
                </div>
            </c:CCM_Section>
            <!-- shipmentInfo -->
            <div class="shipmentInfo">
                <c:CCM_Section title="{!$Label.c.CCM_ShipmentInformation}" expandable="true">
                    <div class="slds-p-left_medium slds-p-right--medium">
                        <aura:if isTrue="{!v.shipmentInfo.length > 0}">
                            <aura:iteration items="{!v.shipmentInfo}" var="shipment" indexVar="allIndex">
                                <aura:if isTrue="{!shipment.ShipmentItems.length > 0}">
                                    <lightning:layout multipleRows="true">
                                        <!-- Ship From -->
                                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                            <c:CCM_Field label="{!$Label.c.Order_ShipFrom}">
                                                <span>{!shipment.ShipmentData.ShipFrom.Street}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipFrom.City}&nbsp;&nbsp;{!shipment.ShipmentData.ShipFrom.PostCode}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipFrom.Country}</span>
                                            </c:CCM_Field>
                                        </lightning:layoutItem>
                                        <!-- Ship To -->
                                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                            <c:CCM_Field label="{!$Label.c.Order_ShipTo}">
                                                <span>{!shipment.ShipmentData.ShipTo.Street}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipTo.City}&nbsp;&nbsp;{!shipment.ShipmentData.ShipTo.PostCode}</span><br/>
                                                <span>{!shipment.ShipmentData.ShipTo.Country}</span>
                                            </c:CCM_Field>
                                        </lightning:layoutItem>
                                    </lightning:layout>
                                    <div class="slds-p-left--medium slds-p-right--medium">
                                        <c:CCM_Section title="{!$Label.c.CCM_ShipmentItemInformation}" expandable="true" state="open">
                                            <div class="table-wrap">
                                                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productTable" role="grid">
                                                    <thead>
                                                        <tr class="slds-line-height_reset">
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_Action}">{!$Label.c.CCM_Action}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Product Description -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Model # -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_Model}">{!$Label.c.CCM_Model}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Item Qty in Shipment  -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.Order_ItemQtyinShipment}">{!$Label.c.Order_ItemQtyinShipment}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Ship Set -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.Order_ShipSet}">{!$Label.c.Order_ShipSet}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                            <!-- Shipped Date -->
                                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                        <span class="slds-truncate" title="{!$Label.c.CCM_ShippedDate}">{!$Label.c.CCM_ShippedDate}</span>
                                                                    </div>
                                                                </a>
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <aura:iteration items="{!shipment.ShipmentItems}" var="shipmentItem" indexVar="shipmentIndex">
                                                            <tr aria-selected="false" id="{!shipmentIndex}" data-id="{!allIndex}" data-expanded="false" class="slds-hint-parent" onclick="{!c.showShipToolList}">
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                        <aura:if isTrue="{!shipmentItem.ProductList}">
                                                                            <lightning:icon iconName="utility:chevrondown" size="xx-small" class="collapse_icn"/>
                                                                        </aura:if>
                                                                    </div>
                                                                </td>
                                                                <!-- Product Description -->
                                                                <td scope="row">
                                                                    <div class="slds-truncate" title="">
                                                                        {!shipmentItem.ProductDescription}
                                                                    </div>
                                                                </td>
                                                                <!-- Model # -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.Model}
                                                                    </div>
                                                                </td>
                                                                <!-- Item Qty in Shipment  -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.ItemQtyInShipment}
                                                                    </div>
                                                                </td>
                                                                <!-- Ship Set -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.ShipSet}
                                                                    </div>
                                                                </td>
                                                                <!-- Shipped Date -->
                                                                <td role="gridcell">
                                                                    <div class="slds-truncate clear-user-agent-styles" >
                                                                        {!shipmentItem.ShippedDate}
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <aura:if isTrue="{!shipmentItem.ProductList}">
                                                                <tr aria-selected="false" class="slds-hint-parent" id="{!('ship' + shipmentIndex + allIndex)}" style="display: none">
                                                                    <td colspan="6" style="padding: 0;">
                                                                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_resizable-cols slds-table_striped productToolTable" role="grid">
                                                                            <thead>
                                                                                <tr class="slds-line-height_reset">
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 80px;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                                                <span class="slds-truncate" title=""></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Product Description -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                                                                <span class="slds-truncate" title="{!$Label.c.Order_ProductDescription}">{!$Label.c.Order_ProductDescription}</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Model #  -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title="{!$Label.c.CCM_Model}">{!$Label.c.CCM_Model}</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Item Qty in Shipment -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 20%;">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title="{!$Label.c.CCM_ItemQtyInShipment}">{!$Label.c.CCM_ItemQtyInShipment}</span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <!-- Item Qty in Shipment -->
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title=""></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                                                                                <span class="slds-truncate" title=""></span>
                                                                                            </div>
                                                                                        </a>
                                                                                    </th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                <aura:iteration items="{!shipmentItem.ProductList}" var="shipmentToolItem" indexVar="shipmentToolIndex">
                                                                                    <tr aria-selected="false" class="slds-hint-parent">
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles" >
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- Product Description -->
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles" >
                                                                                                {!shipmentToolItem.ProductDescription}
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- Model #  -->
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles" >
                                                                                                <span>{!shipmentToolItem.Model}</span>
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- Order Qty -->
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles">
                                                                                                <span>{!shipmentToolItem.OrderQty}</span>
                                                                                            </div>
                                                                                        </td>
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles">
                                                                                                <span></span>
                                                                                            </div>
                                                                                        </td>
                                                                                        <td role="gridcell">
                                                                                            <div class="slds-truncate clear-user-agent-styles">
                                                                                                <span></span>
                                                                                            </div>
                                                                                        </td>
                                                                                    </tr>
                                                                                </aura:iteration>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </aura:if>
                                                        </aura:iteration>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </c:CCM_Section>
                                    </div>
                                </aura:if>
                            </aura:iteration>
                            <aura:set attribute="else">
                                <p class="slds-text-align_center slds-p-around--medium">{!$Label.c.CCM_NoItemsToDisplay}</p>
                            </aura:set>
                        </aura:if>
                    </div>
                </c:CCM_Section>
            </div>
            <!-- Attachment -->
            <c:CCM_Section title="{!$Label.c.CCM_Attachment}" expandable="true">
                <aura:if isTrue="{!v.attachment.length > 0}">
                    <c:CCM_DataTable columns="{!v.attachmentColumns}" data="{!v.attachment}"/>
                    <aura:set attribute="else">
                        <p class="slds-text-align_center slds-p-around--medium">{!$Label.c.CCM_NoItemsToDisplay}</p>
                    </aura:set>
                </aura:if>
            </c:CCM_Section>

            <div class="footer-btn slds-m-bottom_medium">
                <!-- back -->
                <lightning:button class="slds-p-horizontal_x-large" variant="brand-outline" label="{!$Label.c.CCM_Back}" onclick="{!c.doBack}"/>
                
                <aura:if isTrue="{!v.orderType == 'po'}">
                    <aura:if isTrue="{!v.isEdit}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand-outline" label="{!$Label.c.CCM_QuitEdit}" onclick="{!c.doCancelEdit}"/>
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SaveEdit}" onclick="{!c.doSaveEdit}"/>
                        <aura:set attribute="else">
                            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Edit}" onclick="{!c.doEdit}"/>
                            <!-- <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SyncToEBS}" onclick="{!c.doSync}"/> -->
                        </aura:set>
                    </aura:if>
                </aura:if>
            </div>
        </div>
    </div>
</aura:component>