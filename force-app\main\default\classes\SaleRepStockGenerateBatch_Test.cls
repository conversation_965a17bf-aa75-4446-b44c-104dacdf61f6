@isTest
public with sharing class SaleRepStockGenerateBatch_Test{
    @TestSetup
    static void makeData(){
        Test.startTest();
        // 创建测试数据
        Profile profile2 = [Select Id
                            from Profile
                            where name = 'Sales Manager'];
        User user1 = new User(Sub_Inventory_Code__c = 'Code1', isActive = true, ProfileId = profile2.Id, Username = System.now().millisecond() + '<EMAIL>', Alias = 'batman', Email = '<EMAIL>', EmailEncodingKey = 'UTF-8', Firstname = 'Bruce', Lastname = 'Wayne', LanguageLocaleKey = 'en_US', LocaleSidKey = 'en_US', TimeZoneSidKey = 'America/Chicago');
        insert user1;
        User user2 = new User(Sub_Inventory_Code__c = 'Code1', isActive = true, ProfileId = profile2.Id, Username = System.now().millisecond() + '<EMAIL>', Alias = 'batman', Email = '<EMAIL>', EmailEncodingKey = 'UTF-8', Firstname = 'Bruce', Lastname = '<PERSON>', LanguageLocaleKey = 'en_US', LocaleSidKey = 'en_US', TimeZoneSidKey = 'America/Chicago');
        insert user2;
        Product2 kit = new Product2(Name = 'model1', Order_Model__c = 'Model1');
        Product2 product = new Product2(Name = 'model2', Order_Model__c = 'Product2');
        insert kit;
        insert product;
        Kit_Item__c kitItem1 = new Kit_Item__c(Kit__c = kit.Id, VK_Product__c = product.Id);
        Kit_Item__c kitItem2 = new Kit_Item__c(Kit__c = kit.Id, VK_Product__c = product.Id);
        insert new List<Kit_Item__c>{ kitItem1, kitItem2 };
        Inventory__c inventory1 = new Inventory__c(Sub_Inventory_Code__c = 'Code1', Available_QTY__c = 10, Model_No__c = 'Model1', RecordTypeId = Schema.SObjectType.Inventory__c.getRecordTypeInfosByDeveloperName().get('Sales_Rep_Inventory').getRecordTypeId());
        Inventory__c inventory2 = new Inventory__c(Sub_Inventory_Code__c = 'EGS02', Available_QTY__c = 5, Model_No__c = 'Model2', RecordTypeId = Schema.SObjectType.Inventory__c.getRecordTypeInfosByDeveloperName().get('Sales_Rep_Inventory').getRecordTypeId());
        insert new List<Inventory__c>{ inventory1, inventory2 };
        SaleRepStockGenerateBatch batch2 = new SaleRepStockGenerateBatch(user1.Id);
        Database.executeBatch(batch2);
        Test.stopTest();
    }
    @isTest
    public static void testBatch(){
        // 调用被测试方法
        Test.startTest();
        SaleRepStockGenerateBatch batch = new SaleRepStockGenerateBatch();
        Database.executeBatch(batch);
        Test.stopTest();
    }
}