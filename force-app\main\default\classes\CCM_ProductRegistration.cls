/**
 About
 -----
 Description: This Class is used for product registration in partner community .

 Created for: Partner community
 Created: 2023.05.01

 Update History
 --------------
 Created: 2023.05.01 – <PERSON><PERSON> Lin
 -------------
 **/
public without sharing class CCM_ProductRegistration{
    @AuraEnabled
    public static String setupProductRegistration(String userId, String commercialId){
        Map<String, Object> result = new Map<String, Object>();

        String accId = [SELECT AccountId
                        FROM User
                        WHERE Id = :UserInfo.getUserId()
                        limit 1].AccountId;
        if (accId != null){
            Account acc = [SELECT Id, Name
                           FROM Account 
                           WHERE Id = :accId];
            result.put('CustomerId', acc.Id);
            result.put('Name', acc.Name);
        }

        if (commercialId != null){
            Account commercialAccount = [SELECT Id, Name, (SELECT Id, Name, Email
                                                           FROM Contacts
                                                           WHERE Role__c = 'Fleet Manager')
                                         FROM Account 
                                         WHERE Id = :commercialId
                                         limit 1];
            result.put('comapnyName', commercialAccount.Name);
            if (!commercialAccount.Contacts.isEmpty()){
                result.put('fleetManager', commercialAccount.Contacts[0].Name);
                result.put('fleetManagerEmail', commercialAccount.Contacts[0].Email);
            } else{
                result.put('fleetManager', '');
                result.put('fleetManagerEmail', '');
            }
        }
        return JSON.serialize(result);
    }
    //CCM_EligibilityCheck
    @AuraEnabled
    public static String GeneratePartsList(String productId, String filterStr){
        List<SelectOptions> piList = new List<SelectOptions>();
        //ToDo 筛选条件是Kit上的recordType是Products and Parts 并且 产品的Source__c 为PIM的数据
        Boolean isDE = UserInfo.getLanguage() == 'de';
        Set<String> partIds = new Set<String>();
        Map<String, Double> mapProductId2Qty = new Map<String, Double>();
        if (String.isBlank(filterStr)){
            for (Kit_Item__c ki : [SELECT Id, Wearable_Parts__c, Parts__r.Order_Model__c, Parts__r.Name, Parts__c, Parts__r.Item_Description_DE_Formula__c, Parts__r.Item_Description_EN_Formula__c
                                   FROM Kit_Item__c
                                   WHERE RecordType.DeveloperName in ('Products_and_Parts', 'Accessories_and_Parts') AND (Accessory_SP__C = :productId Or Product__C = :productId)]){
                SelectOptions so = new SelectOptions();
                so.Id = ki.Id;
                so.partId = ki.Parts__c;
                partIds.add(ki.Parts__c);
                mapProductId2Qty.put(ki.Parts__c, 1);
                so.Name = ki.Parts__r.Name;
                //Honey ToDo这里的ProductCode是我们的Order Model还是就说productCode
                so.ProductCode = ki.Parts__r.Order_Model__c;
                so.productDescription = isDE ? ki.Parts__r.Item_Description_DE_Formula__c : ki.Parts__r.Item_Description_EN_Formula__c;
                piList.add(so);
            }
        } else{
            String str = '%' + filterStr + '%';
            for (Kit_Item__c ki : [SELECT Id, Wearable_Parts__c, Parts__r.Order_Model__c, Parts__r.Name, Parts__c, Parts__r.Item_Description_DE_Formula__c, Parts__r.Item_Description_EN_Formula__c
                                   FROM Kit_Item__c
                                   WHERE (Accessory_SP__C = :productId Or Product__C = :productId) AND (Parts__r.ProductCode LIKE:str OR Parts__r.Name LIKE:str) AND RecordType.DeveloperName in ('Products_and_Parts', 'Accessories_and_Parts')]){
                SelectOptions so = new SelectOptions();
                so.Id = ki.Id;
                so.partId = ki.Parts__c;
                partIds.add(ki.Parts__c);
                mapProductId2Qty.put(ki.Parts__c, 1);
                so.Name = ki.Parts__r.Name;
                so.productDescription = isDE ? ki.Parts__r.Item_Description_DE_Formula__c : ki.Parts__r.Item_Description_EN_Formula__c;
                so.ProductCode = ki.Parts__r.Order_Model__c;
                piList.add(so);
            }
        }
        String customerId = null;
        for(User u : [SELECT Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()]) {
            customerId = u.Contact.AccountId;
        }
        if(String.isNotBlank(customerId)) {
            List<String> partIdList = new List<String>();
            partIdList.addAll(partIds);
            Map<String,Map<String,Object>> partsPriceMap = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(customerId, partIdList, Date.today(), mapProductId2Qty);
            for(SelectOptions itemOption : piList) {
                if(partsPriceMap.containsKey(itemOption.partId)) {
                    itemOption.finalPrice = (Decimal)partsPriceMap.get(itemOption.partId).get(CCM_Constants.FINAL_PRICE);
                }
            }
        }

        Map<String, Object> result = new Map<String, Object>();
        result.put('PartsList', piList);
        return JSON.serialize(result);
    }
    @AuraEnabled
    public static Map<String, Object> checkEmail(String email){

        try{
            Account acc = new Account(
            );
            acc = CEU_HerokuAPIUtils.getResidentialByEmailAndBrandName(email, '', '');

            if (acc != null){
                User dealer = [Select Id, AccountId
                               From User
                               Where Id = :UserInfo.getUserId()
                               Limit 1];
                String message = 'No Data';
                list<Dealer_Share__c> DealerPermission = [SELECT id, RelatedConsumer__c, RelatedDealer__c
                                                          FROM Dealer_Share__c
                                                          WHERE RelatedConsumer__c = :acc.id AND RelatedDealer__c = :dealer.AccountId];
                if (acc.Consumer_Status__c == 'Waiting Customer Approval'){
                    message = 'Waiting Customer Approval';
                } else{
                    if (acc.DealerView__c || DealerPermission.size() > 0){
                        message = 'DealerView';
                    } else{
                        message = 'NoPermission';
                    }
                }
                return new Map<String, Object>{ 'message' => message, 'data' => JSON.serialize(acc) };
            }
            return new Map<String, Object>{ 'message' => 'No Data', 'data' => null };
        } catch (Exception objE){
            System.debug(LoggingLevel.INFO, '*** objE.getMessage(): ' + objE.getMessage());
            return new Map<String, Object>{ 'message' => 'Fail', 'data' => null };
        }
    }
    @AuraEnabled
    public static String masterModelNumber(String filterStr, String brandName, Boolean needFilterInactive){
        String filterStrTemp = '%' + filterStr + '%';
        List<SelectOptions> selectList = new List<SelectOptions>();
        brandName = String.isNotBlank(brandName) ? brandName : 'EGO';
        Set<String> recoreTypeDeveloperName = new Set<String>();
        recoreTypeDeveloperName.add('TLS_Kit');
        recoreTypeDeveloperName.add('TLS_Product');
        String query = 'SELECT Id, Name, ProductCode FROM Product2 WHERE RecordType.DeveloperName in:recoreTypeDeveloperName AND Name like :filterStrTemp And Brand_Name__c = :brandName';
        if(needFilterInactive){
            query += ' AND IsActive = TRUE';
        }
        List<Product2> productList = (List<Product2>)Database.query(query);
        // List<Product2> productList = [SELECT Id, Name, ProductCode
        //                               FROM Product2
        //                               WHERE IsActive = TRUE AND RecordType.DeveloperName in:recoreTypeDeveloperName AND Name like:filterStrTemp And Brand_Name__c = :brandName];
        System.debug('masterModelNumber-productList:' + productList);
        for (Product2 item : productList){
            SelectOptions sp = new SelectOptions();
            sp.Id = item.Id;
            sp.Name = item.Name;
            //sp.ProductCode = item.ProductCode;
            selectList.add(sp);
        }
        return JSON.serialize(selectList);
    }
    @AuraEnabled
    public static string queryNoByType(String type, String value, String customerId){
        return CCM_SNRequestController.queryNoByType(type, value, UserInfo.getUserId(), customerId);
    }
    public class SelectOptions{
        public String Id;
        public String Name;
        public String partId;
        public String ProductCode;
        public String ProductDescription;
        public Decimal finalPrice;
    }
    @AuraEnabled
    public static String addWarrantyItem(String email, Date purchaseDate, String branName, String masterModelNo, String masterModelId, String purchasePlace){
        //Validate if account active
        Account acc = new Account(
        );
        acc = CEU_HerokuAPIUtils.getResidentialByEmailAndBrandName(email, '', '');
        if (acc != null){
            if (acc.Consumer_Status__c == 'Waiting Customer Approval'){
                return 'Waiting Customer Approval';
            }
        }

        //Validate purchaseDate
        if (purchaseDate != null && purchaseDate > system.today()){
            return 'Wrong_Date';
        }


        List<Kit_Item__c> kitItems = [Select Id, Kit__c, VK_Product__c, VK_Product__r.Order_Model__c
                                      From Kit_Item__c
                                      Where Kit__c = :masterModelId And Kit__r.IsActive = TRUE And VK_Product__r.IsActive = TRUE And RecordType.DeveloperName = 'Kits_and_Products'];
        List<Product3> returns = new List<Product3>();
        if (kitItems.size() > 0){
            for (Kit_Item__c kitItem : kitItems){
                Product3 newP = new Product3();
                newP.masterModelNo = masterModelNo;
                newP.masterModelId = masterModelId;
                newP.modelNo = kitItem.VK_Product__r.Order_Model__c;
                newP.modelId = kitItem.VK_Product__c;
                newP.purchaseDate = purchaseDate;
                newP.purchasePlace = purchasePlace;
                newP.branName = branName;
                returns.add(newP);
            }
        } else{
            Product3 newP = new Product3();
            newP.masterModelNo = masterModelNo;
            newP.masterModelId = masterModelId;
            newP.modelNo = masterModelNo;
            newP.modelId = masterModelId;
            newP.purchaseDate = purchaseDate;
            newP.purchasePlace = purchasePlace;
            newP.branName = branName;
            returns.add(newP);
        }

        return JSON.serialize(returns);
    }
    public class WarrantyForm{
        public String firstName{ get; set; }
        public String lastName{ get; set; }
        public String phone{ get; set; }
        public String personEmail{ get; set; }
        public String shippingPostalCode{ get; set; }
        public String shippingCountry{ get; set; }
        public String shippingState{ get; set; }
        public String shippingCity{ get; set; }
        public String shippingStreet{ get; set; }
        public String address{ get; set; }
        public String branName{ get; set; }
        public Boolean isMassUpload{ get; set; }
        public String commercialAccountId{ get; set; }
        public String registrationType{ get; set; }
        public List<Product3> productList{ get; set; }
        public MassUploadErrorForm ErrorFormList{ get; set; }
        //********-vince-start
        public String orgType{ get; set; }
        //********-vince-end
    }
    //Yanko add for massUpload error when mass upload
    public class MassUploadErrorForm{
        public String losePurchaseDate{ get; set; }
        public String invalidPurchaseDate{ get; set; }
        public String loseModelNo{ get; set; }
        public String invalidModelNo{ get; set; }
        public String loseSerialNumber{ get; set; }
        public String invalidSerialNumber{ get; set; }
        public String duplicateSerialNumber{ get; set; }
        public String loseEmail{ get; set; }
        public String invalidEmail{ get; set; }
    }
    public class Product3{
        public String email{ get; set; }
        //For mass upload only
        public String masterModelNo{ get; set; }
        public String masterModelId{ get; set; }
        public String modelNo{ get; set; }
        public String modelId{ get; set; }
        public Date purchaseDate{ get; set; }
        public String purchasePlace{ get; set; }
        public String branName{ get; set; }
        public String serialNo{ get; set; }
        public String receiptId{ get; set; }
        public String receipt{ get; set; }
        public String receiptName{ get; set; }
        public Boolean verifyResult{ get; set; }
        //For mass upload only
        public String verifyMessages{ get; set; }
        public String productId{ get; set; }
        //For junkLink
        public String junkDataLink{ get; set; }
        public String commercialAccountId{ get; set; }
        public String registrationType{ get; set; }
        //For mass upload only, error massage:
        //1. Email is Empty; 2. Email not Aligned; 3. Purchase Date is Empty; 4. Purchase Date Invalid; 5. Purchase Place is Empty; 6. Purchase Place Invalid; 7. Model No is Empty; 8. Model No Invalid;  9. Serail No is Empty; 10. Serial No Invalid;
        //********-vince-start
        // is sn verify by pass
        public Boolean byPass{ get; set; }
        //********-vince-end
    }
    @AuraEnabled
    public static String uploadFile(String fileName, String content){
        ContentVersion conVer = new ContentVersion(
        );
        conVer.ContentLocation = 'S'; // S specify this document is in SF, use E for external files
        conVer.PathOnClient = fileName; // The files name, extension is very important here which will help the file in preview.
        conVer.Title = 'Receipt ' + String.valueOf(Datetime.now()); // Display name of the files
        conVer.VersionData = EncodingUtil.base64Decode(content); // converting your binary string to Blog
        Map<String, String> result = new Map<String, String>();
        try{
            insert conVer;
            result.put('Status', 'Success');
            result.put('Message', '');
            result.put('ContentId', conVer.Id);
            return JSON.serialize(result);
        } catch (Exception e){
            result.put('Status', 'Error');
            result.put('Message', e.getMessage());
            result.put('ContentId', '');
            return JSON.serialize(result);
        }
        return null;
    }
    @AuraEnabled
    public static String SaveWarranty(String proListStr){
        System.debug(proListStr);
        //proListStr = '{"personEmail":"<EMAIL>","firstName":"Test","lastName":"Test","shippingCountry":"DE","shippingPostalCode":"Test","phone":"12345678","address":"Test","shippingStreet":"Test","shippingCity":"Test","shippingState":"Test","productList":[{"purchaseDate":"2023-05-09","brand":"EGO","purchasePlace":"Online Dealers","masterModelNumber":"PAD4000E(BS)-S","modelNo":"PAD4000E(BS)-S","serialNumber":null,"id":"row-0","receipt":"中国结算-投资者服务专区.pdf"}]}';
        Map<String, String> result = new Map<String, String>();
        //WarrantyForm warrantyForm = (WarrantyForm)JSON.deserializeUntyped(proListStr);
        WarrantyForm warrantyForm = (WarrantyForm)JSON.deserialize(proListStr, WarrantyForm.class);

        //Validation before save data
        if (warrantyForm.productList == null || warrantyForm.productList.size() == 0){
            result.put('Status', 'Error');
            result.put('Message', System.Label.CCM_AddProductRegistrationError);
            return JSON.serialize(result);
        }

        try{
            System.debug('warrantyForm ==== ' + warrantyForm);
            Boolean RegistrationType = true;
            String RegistrationAccountId = '';
            // for (Product3 prod : warrantyForm.productList){
            if (warrantyForm.registrationType == 'commercialUser'){
                RegistrationType = false;
                RegistrationAccountId = warrantyForm.commercialAccountId;
            }
            // }
            System.debug('RegistrationType === ' + RegistrationType);
            System.debug('RegistrationAccountId === ' + RegistrationAccountId);
            //Partner Community User
            //1. Check if consumer existed, if no then create one
            if (RegistrationType == true){
                Account acc = new Account(
                );
                acc = CEU_HerokuAPIUtils.getResidentialByEmailAndBrandName(warrantyForm.personEmail, '', '');
                if (acc == null){
                    acc = new Account(
                    );
                    acc.RecordTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
                    acc.LastName = warrantyForm.lastName;
                    acc.FirstName = warrantyForm.firstName;
                    acc.Phone = warrantyForm.phone;
                    acc.Product_Type__c = warrantyForm.branName;
                    acc.PersonEmail = warrantyForm.personEmail;
                    acc.EGO_username__c = warrantyForm.personEmail;
                    acc.ShippingPostalCode = warrantyForm.shippingPostalCode;
                    acc.ShippingState = warrantyForm.shippingState;
                    acc.ShippingCity = warrantyForm.shippingCity;
                    acc.ShippingStreet = warrantyForm.shippingStreet;
                    acc.ShippingCountry = warrantyForm.shippingCountry;
                    acc.Address_Detail__c = warrantyForm.address;
                    acc.Consumer_Status__c = 'Waiting Customer Approval';
                    // detail详情对应修改 2023.06.02 byAria
                    acc.Username__c = warrantyForm.personEmail;
                    acc.IsStandard__c = false;
                    Id userId = UserInfo.getUserId();
                    User currentUser = [SELECT Profile.Name
                                        FROM User 
                                        WHERE Id = :userId
                                        LIMIT 1];
                    if (currentUser != null){
                        String profileName = currentUser.Profile.Name;
                        acc.AccountSource = profileName.contains('Community') ? 'Portal' : 'CRM';
                    }
                    insert acc;

                    CEU_HerokuAPIUtils.sendNewConsumerNoticeEmail(acc.Id, true);
                }
                RegistrationAccountId = acc.Id;
            }
            Account acc = [SELECT Id, Name, Consumer_Status__c
                           FROM Account
                           WHERE Id = :RegistrationAccountId];

            //2. Create dealer and consumer related relationship
            User dealer = [Select Id, AccountId
                           From User
                           Where Id = :UserInfo.getUserId()
                           Limit 1];
            if (RegistrationType && dealer.AccountId != null){
                Dealer_Share__c dealerShare = new Dealer_Share__c(
                );
                dealerShare.RelatedDealer__c = dealer.AccountId;
                dealerShare.RelatedConsumer__c = acc.Id;
                dealerShare.Permission__c = 'Owner';
                insert dealerShare;
            }

            //3. Save warranty product registration info
            if (warrantyForm.isMassUpload){
                String errorMessage = SaveWarrantyMassUpload(dealer.AccountId, acc.Id, warrantyForm.productList);
                if (errorMessage != null){
                    result.put('Status', 'Error SaveWarranty MassUpload');
                    result.put('Message', errorMessage);
                    return JSON.serialize(result);
                }
            } else{
                Map<String, Product3> prodMap = new Map<String, Product3>();
                Map<String, String> LinkMap = new Map<String, String>();
                // List<String> receiptIds = new List<String>();
                List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
                List<String> junkLinkLst = new List<String>();
                for (Product3 prod : warrantyForm.productList){
                    Warranty_Item__c wi = new Warranty_Item__c(
                    );
                    wi.Consumer__c = acc.Id;
                    wi.Dealer__c = dealer.AccountId;
                    wi.Master_Product__c = prod.masterModelId;
                    if (String.isNotBlank(prod.modelId)){
                        wi.Product__c = prod.modelId;
                    } else if (String.isNotBlank(prod.modelNo)){
                        List<product2> prodList = [SELECT Id
                                                   FROM Product2
                                                   WHERE Name = :prod.modelNo];
                        if (prodList.size() > 0){
                            wi.Product__c = prodList[0].Id;
                        }
                    }
                    wi.Serial_Number__c = prod.serialNo;
                    wi.Purchase_Date__c = prod.purchaseDate;
                    wi.Purchase_Place__c = prod.purchasePlace;
                    if (acc.Consumer_Status__c == 'Waiting Customer Approval'){
                        wi.Warranty_Status__c = 'Waiting Customer Approval';//?9.11错误修改
                    }
                    if (acc.Consumer_Status__c == 'Active'){
                        wi.Warranty_Status__c = 'In process';
                    }
                    if (String.isNotBlank(prod.receipt)){
                        wi.Receipt_Link__c = prod.receipt;
                        wi.Receipt_Name__c = prod.receiptName;
                        wi.Receipt_Status__c = 'Uploaded';
                    } else{
                        wi.Receipt_Status__c = 'Lost';
                    }
                    wiList.add(wi);
                    prodMap.put(prod.modelId + prod.serialNo, prod);
                    // if (String.isNotBlank(prod.receiptId)){
                    //     receiptIds.add(prod.receiptId);
                    // }
                    if (String.isNotBlank(prod.junkDataLink)){
                        LinkMap.put(prod.junkDataLink, prod.serialNo);
                        junkLinkLst.add(prod.junkDataLink);
                    }
                }
                insert wiList;

                // 查询Receipt_Junk_Data__c记录并关联到Warranty_Item__c
                if (junkLinkLst.size() > 0){
                    List<Receipt_Junk_Data__c> ReceiptJunkDataList = new List<Receipt_Junk_Data__c>();
                    ReceiptJunkDataList = [SELECT Id, Receipt_Junk_Data_Link__c, Warranty__c
                                           FROM Receipt_Junk_Data__c
                                           WHERE Receipt_Junk_Data_Link__c IN:junkLinkLst];
                    for (Receipt_Junk_Data__c ReceiptJunkData : ReceiptJunkDataList){
                        String junkLink = ReceiptJunkData.Receipt_Junk_Data_Link__c;
                        String SerialNumStr = LinkMap.get(junkLink);
                        for (Warranty_Item__c wi : wiList){
                            if (wi.Serial_Number__c == SerialNumStr){
                                ReceiptJunkData.Warranty__c = wi.Id;
                                break;
                            }
                        }
                    }
                    update ReceiptJunkDataList;
                }
            }

            CEU_HerokuAPIUtils.sendNewWarrantyNoticeEmail(acc.Id, true);

            result.put('AccountID', acc.Id);
            result.put('Status', 'Success');
            result.put('Message', '');

            return JSON.serialize(result);
        } catch (Exception e){
            system.debug('Error Line1 : ' + e.getLineNumber());
            system.debug('Error Message : ' + e.getMessage());
            String errorLine = String.valueOf(e.getLineNumber());
            result.put('Status', 'Error SaveWarranty');
            result.put('Message', e.getMessage());
            result.put('Error Line', errorLine);
            return JSON.serialize(result);
        }

        return null;
    }
    private static void logError(String strMessage){
        insert new Log__c(
            Name = 'Save Warranty Error', 
            ApexName__c = 'CCM_ProductRegistration', 
            Method__c = 'SaveWarranty', 
            Error_Message__c = strMessage
        );
    }
    @AuraEnabled
    public static String getCountry(String fifter){
        List<SelectOptions> selectList = new List<SelectOptions>();
        Schema.DescribeFieldResult fieldResult = Account.Country__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for (Schema.PicklistEntry item : ple){
            String label = item.getLabel();
            if (String.isNotBlank(fifter)){
                if (label.contains(fifter)){
                    SelectOptions sp = new SelectOptions();
                    sp.Id = item.getLabel();
                    sp.Name = item.getLabel();
                    selectList.add(sp);
                }
            } else{
                SelectOptions sp = new SelectOptions();
                sp.Id = item.getLabel();
                sp.Name = item.getLabel();
                selectList.add(sp);
            }
        }

        return Json.serialize(selectList);
    }
    @AuraEnabled
    public static string snVerify(String modelNo, String sn, Date purchaseDate){
        Boolean flag = true;
        List<String> errList = new List<String>();
        if (sn.substring(0, 1) != 'E'){
            errList.add('SN-' + sn + ' ' + Label.SN_Check1);
        } else if (sn.length() != 15){
            errList.add('SN-' + sn + ' ' + Label.SN_Check2);
        } else{
            //2-5位校验
            String modelCodeSN = String.isNotBlank(sn) ? sn.substring(1, 5) : '';
            system.debug(modelNo + ' ' + modelCodeSN);
            Boolean flag1 = false;
            for (Warranty_Rules__c warrantyRule : [SELECT Id, Product_Model__c, Code_in_Serial__c
                                                   FROM Warranty_Rules__c
                                                   WHERE Product_Model__c = :modelNo AND RecordType.developerName = 'Model_Code' AND Product_Model__c != null AND Code_in_Serial__c != null]){
                if (warrantyRule.Code_in_Serial__c == modelCodeSN){
                    flag1 = true;
                }
            }
            if (!flag1){
                errList.add('SN-' + sn + ' ' + Label.SN_Check3);
            }

            if(!isInBypassScope(modelNo, 'Purchase Date')) {
                //校验购买日期6-9位
                String getDate = String.isNotBlank(sn) ? sn.substring(5, 9) : '';
                String year = '20' + getDate.substring(0, 2);
                Integer days = Integer.valueOf(getDate.substring(2, 4)) * 7;
                Date firstMonday = Date.newInstance(Integer.valueOf(year), 1, 1).toStartOfWeek().addDays(7);
                Date trueDate = firstMonday.addDays(days).addDays(6);
                if (trueDate > purchaseDate){
                    errList.add('From Number 6th to 9th represent Manufactured Year ＆ Week, and purchase date CANNOT be earlier than manufactured date.');

                }
            }

            
            //4.10-14位数字校验
            if (!sn.substring(9, 14).isNumeric()){
                errList.add('SN-' + sn + ' ' + Label.SN_Check4);
            }
            //5.15位大写字母校验
            Set<String> endCodeSet = new Set<String>{ 'X', 'A', 'B' };
            if (!endCodeSet.contains(sn.right(1))){
                errList.add('SN-' + sn + ' ' + Label.SN_Check5);
            }
            //校验是否重复
            List<Warranty_Item__c> wiList = [select id
                                             from Warranty_Item__c
                                             where Serial_Number__c = :sn];
            if (wiList.size() > 0){
                errList.add(System.Label.CCM_SNCodeExistError);
        }
        //end
        if (errList.size() > 0){
            flag = false;
        }
        }
        Map<String, String> returnMap = new Map<String, String>();
        returnMap.put('isSuccess', String.valueOf(flag));
        returnMap.put('errList', JSON.serialize(errList));
        return JSON.serialize(returnMap);
    }

    private static Boolean isInBypassScope(String modelNo, String bypassType) {
        List<Product_Registration_Bypass_SN_Check__c> scopes = [SELECT Modal_Number__c FROM Product_Registration_Bypass_SN_Check__c WHERE Bypass_Type__c = :bypassType];
        Set<String> modalNumbers = new Set<String>();
        for(Product_Registration_Bypass_SN_Check__c scope : scopes) {
            if(String.isNotBlank(scope.Modal_Number__c)) {
                for(String modalNumber : scope.Modal_Number__c.split(',')) {
                    modalNumbers.add(modalNumber.toUpperCase());
                }
            }
        }
        if(modalNumbers.contains(modelNo.toUpperCase())) {
            return true;
        }
        return false;
    }

    @AuraEnabled
    public static String massUploadVerification(String proListStr){
        System.debug(proListStr);
        Map<String, String> result = new Map<String, String>();
        WarrantyForm warrantyForm = (WarrantyForm)JSON.deserialize(proListStr, WarrantyForm.class);

        //Get product name - code mapping
        Set<String> productNameSet = new Set<String>();
        if (warrantyForm.productList != null){
            for (Product3 prod : warrantyForm.productList){
                productNameSet.add(prod.modelNo);
            }
        }
        //获取masterModel数据
        System.debug('productNameSet ==== ' + productNameSet);
        List<Product2> prodList = [SELECT Id, Name, Order_Model__c
                                   FROM Product2
                                   WHERE Order_Model__c IN:productNameSet OR Master_Product__c IN:productNameSet];
        Map<String, Product3> masterModelMap = new Map<String, Product3>();
        if (prodList.size() > 0){
            for (Product2 prod : prodList){
                Product3 newP = new Product3();
                newP.masterModelNo = prod.Order_Model__c;
                newP.masterModelId = prod.Id;
                newP.modelId = prod.Id;
                masterModelMap.put(prod.Name, newP);
            }
        }
        System.debug('masterModelMap ==== ' + masterModelMap);
        List<Product3> returns = new List<Product3>();
        Map<String, String> proName2CodeMap = new Map<String, String>();
        Map<String, String> proName2IdMap = new Map<String, String>();
        for (Product2 product : [SELECT Id, Name, Order_Model__C
                                 FROM Product2
                                 WHERE IsActive = TRUE AND RecordType.DeveloperName = 'TLS_Product' AND Name in:productNameSet]){
            proName2CodeMap.put(product.Name, product.Order_Model__C);
        }

        //Get Model# - Code mapping
        Map<String, String> ruleMap = new Map<String, String>();
        for (Warranty_Rules__c warrantyRule : [SELECT Id, Product_Model__c, Code_in_Serial__c
                                               FROM Warranty_Rules__c
                                               WHERE RecordType.DeveloperName = 'Model_Code' AND Product_Model__c in:proName2CodeMap.keySet() AND Product_Model__c != null AND Code_in_Serial__c != null]){
            ruleMap.put(warrantyRule.Product_Model__c, warrantyRule.Code_in_Serial__c);
        }

        //Get Purchase Place picklist values
        List<String> purchasePlacePicklist = new List<String>();
        for (Schema.PicklistEntry opt : Schema.getGlobalDescribe().get('Warranty_Item__c').getDescribe().fields.getMap().get('Purchase_Place__c').getDescribe().getPicklistValues()){
            purchasePlacePicklist.add(opt.getValue());
        }

        //Mass upload data verification start.
        //MassUploadErrorForm
        // warrantyForm.ErrorFormList
        String loseEmail = Label.Verify_Error_Email_Empty + ' Line ';
        String invalidEmail = Label.Verify_Error_Email_Invalid + ' Line ';
        String losePurchaseDate = Label.Verify_Error_PD_Empty + ' Line ';
        String invalidPurchaseDate = Label.Verify_Error_PD_Invalid + ' Line ';
        String loseModelNo = Label.Verify_Error_Model_Empty + ' Line ';
        String invalidModelNo = Label.Verify_Error_Model_Invalid + ' Line ';
        String loseSerialNumber = Label.Verify_Error_SN_Empty + ' Line ';
        String invalidSerialNumber = Label.Verify_Error_SN_Invalid + ' Line ';
        String duplicateSerialNumber = Label.CCM_DuplicateSN + ' Line ';

        if (warrantyForm.productList != null){
            Integer errorIndex = 0;
            for (Product3 prod : warrantyForm.productList){
                Boolean NoError = true;
                prod.verifyMessages = '';
                errorIndex++;
                //1. Check Email
                if (String.IsBlank(warrantyForm.personEmail) || String.IsBlank(prod.email)){
                    loseEmail += errorIndex + '. ';
                    NoError = false;
                } else if (prod.email != warrantyForm.personEmail){
                    invalidEmail += errorIndex + '. ';
                    NoError = false;
                }

                //2. Verify purchase date
                if (prod.purchaseDate == null){
                    losePurchaseDate += errorIndex + '. ';
                    NoError = false;
                } else if (prod.purchaseDate > system.today()){
                    invalidPurchaseDate += errorIndex + '. ';
                    NoError = false;
                }

                //4. Check Model No
                if (prod.modelNo == null){
                    loseModelNo += errorIndex + '. ';
                    NoError = false;
                } else if (!proName2CodeMap.containsKey(prod.modelNo)){
                    invalidModelNo += errorIndex + '. ';
                    NoError = false;
                } else{
                    prod.ModelId = proName2CodeMap.get(prod.modelNo);
                }

                //5. Verify serial no
                if (prod.serialNo == null){
                    loseSerialNumber += errorIndex + '. ';
                    NoError = false;
                } else{
                    List<Warranty_Item__c> warrantyList = [SELECT Id, Serial_Number__c
                                                           FROM Warranty_Item__c
                                                           WHERE Serial_Number__c = :prod.serialNo
                                                           LIMIT 1];
                    if (warrantyList.size() > 0){
                        duplicateSerialNumber += errorIndex + '. ';
                        NoError = false;
                    } else{
                            String modelCodeSN = String.isNotBlank(prod.serialNo) ? prod.serialNo.substring(1, 5) : '';
                            String productCode = proName2CodeMap.get(prod.modelNo);
                            if (!ruleMap.containsKey(productCode) || ruleMap.get(productCode) != modelCodeSN){
                                invalidSerialNumber += errorIndex + '. ';
                                NoError = false;
                            } else if (prod.serialNo.substring(0, 1) != 'E' || prod.serialNo.length() != 15){
                                // SN码第一位是否为'E'以及长度是否为15位
                                invalidSerialNumber += errorIndex + '. ';
                                NoError = false;
                            }
                            if (prod.purchaseDate != null){

                                Date PD = prod.PurchaseDate;
                                Integer daysSince1900_01_07 = Date.newInstance(1900, 1, 7).daysBetween(PD);
                                Integer dayNumber = Math.mod(daysSince1900_01_07, 7) + 1;
                                Date dateForYear = PD.addDays(Math.mod(8 - dayNumber, 7) - 3);
                                Integer year = dateForYear.year();
                                Date year_01_01 = Date.newInstance(year, 1, 1);
                                String PurchaseWeek = String.valueOf((Integer) Math.floor((year_01_01.daysBetween(PD) + Math.mod((Math.mod(Date.newInstance(1900, 1, 7).daysBetween(year_01_01), 7) + 1) + 1, 7) - 3) / 7 + 1));
                                String PurchaseYear4 = String.valueOf(prod.purchaseDate.year());
                                String PurchaseYear = PurchaseYear4.substring(Math.max(0, PurchaseYear4.length() - 2));
                                System.debug('Line = ' + errorIndex + '   Years = ' + PurchaseYear);
                                System.debug('Line = ' + errorIndex + '   Weeks = ' + PurchaseWeek);
                                if ((prod.serialNo).substring(5, 7) > PurchaseYear){
                                    // SN码中的年份校验错误
                                    invalidSerialNumber += errorIndex + '. ';
                                    NoError = false;
                                } else if ((prod.serialNo).substring(5, 7) == PurchaseYear && PurchaseWeek < (prod.serialNo).substring(7, 9)){
                                    // SN码中的周数校验错误
                                    invalidSerialNumber += errorIndex + '. ';
                                    NoError = false;
                                }
                            }

                    }
                }
                //返回批量上传的表格对应的masterModelNumber和MasterModelId
                if (masterModelMap.containsKey(prod.modelNo)){
                    Product3 mapProduct = masterModelMap.get(prod.modelNo);
                    prod.masterModelNo = mapProduct.masterModelNo;
                    prod.masterModelId = mapProduct.masterModelId;
                    prod.modelId = mapProduct.modelId;
                }
                prod.verifyResult = NoError;
            }
        }

        //return MassUpload errorInfo
        MassUploadErrorForm errorInfo = new MassUploadErrorForm();
        errorInfo.loseEmail = loseEmail.contains('. ') ? loseEmail : '';
        errorInfo.invalidEmail = invalidEmail.contains('. ') ? invalidEmail : '';
        errorInfo.losePurchaseDate = losePurchaseDate.contains('. ') ? losePurchaseDate : '';
        errorInfo.invalidPurchaseDate = invalidPurchaseDate.contains('. ') ? invalidPurchaseDate : '';
        errorInfo.loseModelNo = loseModelNo.contains('. ') ? loseModelNo : '';
        errorInfo.invalidModelNo = invalidModelNo.contains('. ') ? invalidModelNo : '';
        errorInfo.loseSerialNumber = loseSerialNumber.contains('. ') ? loseSerialNumber : '';
        errorInfo.invalidSerialNumber = invalidSerialNumber.contains('. ') ? invalidSerialNumber : '';
        errorInfo.duplicateSerialNumber = duplicateSerialNumber.contains('. ') ? duplicateSerialNumber : '';
        warrantyForm.ErrorFormList = errorInfo;

        return JSON.serialize(warrantyForm);
    }
    private static string SaveWarrantyMassUpload(String dealer, String consumer, List<Product3> productList){
        if (dealer != null && consumer != null){
            List<Warranty_Temp__c> insertList = new List<Warranty_Temp__c>();
            for (Product3 prod : productList){
                Warranty_Temp__c wTem = new Warranty_Temp__c(
                );
                wTem.Consumer__c = consumer;
                wTem.Dealer__c = dealer;
                wTem.Email__c = prod.email;
                wTem.Model_No__c = prod.modelNo;
                wTem.Model_Id__c = prod.ModelId;
                wTem.Purchase_Date__c = prod.purchaseDate;
                wTem.Purchase_Place__c = prod.purchasePlace;
                wTem.Serial_Number__c = prod.serialNo;
                wTem.Receipt_Id__c = prod.receiptId;
                wTem.Status__c = 'Open';
                insertList.add(wTem);
            }
            if (insertList.size() > 0){
                Database.SaveResult[] srList = Database.insert (insertList, true);

                List<String> ids = new List<String>();
                for (Database.SaveResult sr : srList){
                    if (sr.isSuccess()){
                        ids.add(sr.getId());
                    } else{
                        System.debug('SaveWarranty MassUpload Error');
                        return JSON.serialize(sr.getErrors());
                    }
                }

                if (ids.size() > 0){
                    Database.executeBatch(new CCM_WarrantyRegistrationMassUploadBatch(ids), 2000);
                }
            }
        }

        return null;
    }
    @AuraEnabled
    public static String ReceiptJunkPreCreate(){
        //记录上传错误的file
        Receipt_Junk_Data__c rjd = New Receipt_Junk_Data__c(
        );
        INSERT rjd;

        return rjd.Id;
    }
    @AuraEnabled
    public static AuraResponseEntity UploadReceiptToAws(String idStr, String receiptType, String receiptName, String receiptId){
        //返回值
        AuraResponseEntity result = new AuraResponseEntity();
        //记录上传错误的file
        Receipt_Junk_Data__c rjd = New Receipt_Junk_Data__c(
        );

        ContentVersion file = [SELECT Title, VersionData, ContentDocumentId
                               FROM ContentVersion
                               WHERE id = :receiptId];
        ContentDocument docDelete = [SELECT id
                                     FROM ContentDocument
                                     WHERE id = :file.ContentDocumentId];
        rjd = [select id
               from Receipt_Junk_Data__c
               Where Id = :idStr];
        receiptName = receiptName.replaceAll('\\s+', '_');
        receiptName = receiptName.replaceAll('\\(', '');
        receiptName = receiptName.replaceAll('\\)', '');
        receiptName = receiptName.replaceAll('/', '');

        //callout AWS
        HttpRequest request = new HttpRequest();
        //方法设置PUT
        request.setMethod('PUT');
        //内容设置
        request.setBodyAsBlob(file.VersionData);
        request.setHeader('Content-Encoding', 'base64');
        if (receiptType == 'pdf')
            receiptType = 'application/pdf';
        request.setHeader('Content-Type', receiptType);
        request.setHeader('Connection', 'keep-alive');
        //callout AWS认证设置 Named Credential
        request.setEndpoint('callout:AWS_S3/' + idStr + receiptName);

        try{
            //callout执行
            Http http = new Http();
            HttpResponse res = http.send(request);
            System.debug('>>>> The response');
            System.debug(res);
            System.debug('>>>> Status code');
            System.debug(res.getStatusCode());
            //callout status跟踪
            if (res.getStatusCode() == 200 && res.getStatus() == 'OK'){
                //成功的情况将link更新到跟踪数据
                rjd.Receipt_Junk_Data_Link__c = Label.Warranty_AWS_S3_URL + idStr + receiptName;
                UPDATE rjd;
                result.message = rjd.Receipt_Junk_Data_Link__c;
            } else{
                //失败的情况返回失败信息
                DELETE rjd;
                result.code = 201;
                result.message = Label.Warranty_AWS_S3_File_Upload_Error;
            }
            DELETE docDelete;
        } catch (System.CalloutException e){
            system.debug('AWS Service Callout Exception: ' + e.getMessage());
            DELETE rjd;
            result.code = 201;
            result.message = e.getMessage();
        }

        return result;
    }
    @AuraEnabled
    public static String lookUpSearch(String fifterString, String fieldAPI){
        return CCM_Community_OrderInformationCtl.lookUpSearch(fifterString, fieldAPI);
    }
    @AuraEnabled
    public static String GetProductInfo(String FilterString, String CustomerId, Date PricingDate){
        return CCM_RequestPurchaseOrderController.GetProductInfo(FilterString, CustomerId, PricingDate);
    }
    @AuraEnabled
    public static String QueryAddressByName(String AddressName, String customerId, String recordTypeName){
        return CCM_FillPurchaseInfoController.QueryAddressByName(AddressName, customerId, recordTypeName);
    }
    // add calvin
    @AuraEnabled
    public static List<CCM_Order_Promotion.PromotionData> getPromotionsByProduct(String prodId, String customerId, Boolean isPortal, Boolean isDropShip, Date PricingDate){
        return CCM_Order_Promotion.getPromotionsByProduct(prodId, customerId, isPortal, isDropShip, PricingDate);
    }
    // add calvin
    @AuraEnabled
    public static String QueryAddressByName(String AddressName){
        return CCM_FillPurchaseInfoController.QueryAddressByName(AddressName);
    }
    // add calvin
    @AuraEnabled
    public static List<Map<String, Object>> queryAvaliableCustomer(String queryName){
        return CCM_CourseRegisterCtl.queryAvaliableCustomer(queryName);
    }
    // add calvin
    @AuraEnabled
    public static List<Map<String, String>> queryBillAddressInfo(String addressName, String customerId){
        return CCM_CourseRegisterCtl.queryBillAddressInfo(addressName, customerId);
    }
    // add calvin
    @AuraEnabled
    public static List<Map<String, Object>> queryLocationLookUpInfo(){
        return CCM_CourseRegisterCtl.queryLocationLookUpInfo();
    }
    // add calvin
    @AuraEnabled
    public static List<Map<String, String>> queryShipAddressInfo(String addressName, String customerId){
        return CCM_CourseRegisterCtl.queryShipAddressInfo(addressName, customerId);
    }
    // add by haibo resales address list 2023/8/11
    @AuraEnabled
    public static Map<String, Object> QueryAddressInfo(String JSONStringQueryInfo){
        return CCM_ResalesFillInfoCtl.QueryAddressInfo(JSONStringQueryInfo);
    }
    @AuraEnabled
    public static List<Object> getDeliveryAddress(String entityId, String addressName, String addressType, Boolean isProspect){
        return CCM_SalesRepStock_Invoice_Damage.getDeliveryAddress(entityId, addressName, addressType, isProspect);
    }
    // add haibo
    @AuraEnabled
    public static String queryCustomer(String name, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimCreateController.queryCustomer(name, pageNumber, allPageSize);
        // return '';
    }
    //add haibo
    @AuraEnabled
    public static String queryWarrantyClaim(String name, Integer pageNumber, Integer allPageSize, String ConsumerId){
        return WarrantyClaimCreateController.queryWarrantyClaim(name, pageNumber, allPageSize, ConsumerId);
        // return '';
    }
    // add haibo
    @AuraEnabled
    public static String queryModelNumber(String accId, string orderModel, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimCreateController.queryModelNumber(accId, orderModel, pageNumber, allPageSize);
        // return '';
    }
    // add haibo
    @AuraEnabled
    public static String queryModelNumberWithoutCustomer(String orderModel, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimCreateController.queryModelNumberWithoutCustomer(orderModel, pageNumber, allPageSize);
    }
    // add haibo
    @AuraEnabled
    public static String queryAddress(String AccountId, String type, String name, Integer pageNumber, Integer allPageSize){
        return WarrantyClaimCreateController.queryAddress(AccountId, type, name, pageNumber, allPageSize);
        //    return '';
    }
    // add haibo
    @AuraEnabled
    public static String queryParts(String productId, Integer pageNumber, Integer allPageSize, String customerId, String name, List<String> partIds){
        return WarrantyClaimBasicHandler.queryParts(productId, pageNumber, allPageSize, customerId, name, partIds);
    }
    //add by yanko , init residential product page,show consumer's info
    @AuraEnabled
    public static String getConsumerInfoById(Id ConsumerId){
        Account account = [SELECT Id, RecordType.Name, Consumer_Status__c, Company__c, Address_Detail__c, City__c, Country__c, Business_Area__c, Street__c, State__c, Postal_Code__c, CreatedDate, PersonEmail, Phone, FirstName, LastName, Name, ShippingCity, ShippingCountry, ShippingStreet, ShippingState, ShippingPostalCode
                           FROM Account
                           WHERE Id = :ConsumerId];
        System.debug('DataAccount ===== ' + account);
        OwnerInformation ownerInformation = new OwnerInformation();
        ownerInformation.City = account.ShippingCity;
        ownerInformation.Country = account.ShippingCountry;
        ownerInformation.Status = account.Consumer_Status__c;
        ownerInformation.StreetNo = account.ShippingStreet;
        ownerInformation.State = account.ShippingState;
        ownerInformation.Postcode = account.ShippingPostalCode;
        ownerInformation.DueDate = account.CreatedDate.date().addDays(30);
        ownerInformation.EmailAddress = account.PersonEmail;
        ownerInformation.Phone = account.Phone;
        ownerInformation.FirstName = account.FirstName;
        ownerInformation.LastName = account.LastName;

        Map<String, Object> userHistoryrMap = new Map<String, Object>();
        userHistoryrMap.put('ownerInformation', ownerInformation);
        userHistoryrMap.put('result', 'success');
        System.debug('userHistoryrMap ===== ' + userHistoryrMap);

        return JSON.serialize(userHistoryrMap);
    }
    //add by yanko , for getConsumerInfoById
    public class OwnerInformation{
        public String EmailAddress;
        public String Phone;
        public String FirstName;
        public String LastName;
        public String Address;
        public String City;
        public String Country;
        public String Status;
        public String StreetNo;
        public String State;
        public String Postcode;
        public Date DueDate;
    }
    //勿删  被其他class调用
    public class WarrantyWrapper{
        @AuraEnabled
        public String PurchaseDate{ get; set; }
        @AuraEnabled
        public String Brand{ get; set; }
        @AuraEnabled
        public String PurchasePlace{ get; set; }
        @AuraEnabled
        public String MasterProductNumber{ get; set; }
        @AuraEnabled
        public String ModelNumber{ get; set; }
        @AuraEnabled
        public String SerialNumber{ get; set; }
    }
    //    // 勿删 new warrantyclaim调用
    //    public class parts{
    //        public String kitItemId;
    //
    //        public String partsId;
    //
    //        public String showName;
    //
    //        public String Name;
    //
    //        public String ProductCode;
    //
    //        public String ItemNumber;
    //
    //        public Decimal price;
    //
    //        public Decimal LaborTime;
    //
    //        public Boolean isWearable;
    //
    //        public Integer warrantyDate;
    //
    //        public Decimal fakePrice;
    //
    //    }
    //
}