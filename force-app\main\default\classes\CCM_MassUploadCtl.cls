/**
 * Honey
 * 批量上传
 */
public without sharing class CCM_MassUploadCtl{

    //校验warranty得数据
    @AuraEnabled
    public static Map<String, String> checkWarrantyInfo(String warrantyInfoJson, String customerId){
        Map<String, String> mapFeild2Value = new Map<String, String>();
        try{
            List<MassUploadInfo> lstMassUploadIndo = (List<MassUploadInfo>)JSON.deserialize(warrantyInfoJson, List<MassUploadInfo>.class);
            //trim
            lstMassUploadIndo = toTrimWarranty(lstMassUploadIndo);
            Set<String> setModelNumbers = new Set<String>();
            Set<String> setPartNumbers = new Set<String>();
            Set<String> setPartNumbersNew = new Set<String>();//aria新增
            Set<String> setAllNumbers = new Set<String>();
            Set<String> setCheckDate = new Set<String>();
            Set<String> setCheckCurrency = new Set<String>();
            List<MassUploadInfo> partInfolst = new List<MassUploadInfo>();//aria新增
            for (MassUploadInfo objMassUpload : lstMassUploadIndo){
                setModelNumbers.add(objMassUpload.modelNumber);

                if (objMassUpload.partNumber != Label.Laber_Hourse){
                    setPartNumbers.add(objMassUpload.partNumber);
                    //aria新增获取逻辑
                    if (objMassUpload.partNumber <> objMassUpload.modelNumber && objMassUpload.partNumber != label.Laber_Hourse){
                        setPartNumbersNew.add(objMassUpload.partNumber);
                        partInfolst.add(objMassUpload);
                    }
                }

                setAllNumbers.add(objMassUpload.modelNumber);
                setAllNumbers.add(objMassUpload.partNumber);
                if (!checkDateFormt(objMassUpload.claimDate)){
                    setCheckDate.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_ClaimDate + ' ' + Label.CCM_FieldIsNotValid);
                }
                if (!checkDateFormt(objMassUpload.repairDate)){
                    setCheckDate.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_RepairDate + ' ' + Label.CCM_FieldIsNotValid);
                }
                if (String.isNotBlank(objMassUpload.failureDate) && !checkDateFormt(objMassUpload.failureDate)){
                    setCheckDate.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_FailureDate + ' ' + Label.CCM_FieldIsNotValid);
                }
                if (!checkDateFormt(objMassUpload.purchaseDate)){
                    setCheckDate.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_PurchaseDate + ' ' + Label.CCM_FieldIsNotValid);
                }
                if (!checkCurrency(objMassUpload.currencyCode)){
                    setCheckCurrency.add(objMassUpload.claimReferenceNumber + objMassUpload.currencyCode + ' ' + Label.CCM_Currency + ' ' + Label.CCM_FieldIsNotValid);
                }
            }
            String strCheckDate = '';
            if (setCheckDate != null && setCheckDate.size() > 0){
                strCheckDate = setCheckDate.toString();
            }
            String strCheckCurrency = '';
            if (setCheckCurrency != null && setCheckCurrency.size() > 0){
                strCheckCurrency = setCheckCurrency.toString();
            }

            //根据Number查询ProductId
            List<Product2> lstProduct = new List<Product2>();
            List<Product2> lstProductNew = new List<Product2>();//只存Product
            List<Product2> lstPart = new List<Product2>();//只存part
            lstProduct = [SELECT Id, Order_Model__c
                          FROM Product2
                          WHERE Order_Model__c IN:setAllNumbers];
            //aria新增-查product
            lstProductNew = [SELECT Id, Order_Model__c
                             FROM Product2
                             WHERE Order_Model__c IN:setModelNumbers AND Recordtype.DeveloperName IN ('TLS_Product', 'ACC')];
            //aria新增-查part
            lstPart = [SELECT Id, Order_Model__c
                       FROM Product2
                       WHERE Order_Model__c IN:setPartNumbersNew AND Recordtype.DeveloperName IN ('TLS_Product', 'ACC', 'SP')];
            Set<String> setExistNumbers = new Set<String>();
            Set<String> setProductExistNumbers = new Set<String>();
            Set<String> setPartExistNumbers = new Set<String>();
            List<String> lstProductIds = new List<String>();
            Map<String, Decimal> mapProduct2Qty = new Map<String, Decimal>();
            Map<String, String> mapModel2Id = new Map<String, String>();
            for (Product2 objProduct : lstProduct){
                setExistNumbers.add(objProduct.Order_Model__c);
                lstProductIds.add(objProduct.Id);
                mapProduct2Qty.put(objProduct.Id, 1);
                mapModel2Id.put(objProduct.Order_Model__c, objProduct.Id);
            }
            for (Product2 objProduct : lstProductNew){
                setProductExistNumbers.add(objProduct.Order_Model__c);
            }
            for (Product2 objProduct : lstPart){
                setPartExistNumbers.add(objProduct.Order_Model__c);
            }
            Set<String> setCheckModelNumber = new Set<String>();
            setCheckModelNumber = checkProductModel(setModelNumbers, setProductExistNumbers, lstMassUploadIndo);
            Set<String> setCheckPartNumber = new Set<String>();
            //Honey add 不校验parts与Model必然联系
            //setCheckPartNumber = checkPartModel(setPartNumbersNew, setPartExistNumbers, partInfolst);
            //Honey update  校验Parts与Model必然联系
            setCheckPartNumber = checkPartModel2(setPartNumbersNew, setPartExistNumbers, partInfolst);
            String strCheckModelNumber = '';
            String strCheckPartNumber = '';
            if (setCheckModelNumber != null && setCheckModelNumber.size() > 0){
                strCheckModelNumber = setCheckModelNumber.toString() + ' ';
            }
            if (setCheckPartNumber != null && setCheckPartNumber.size() > 0){
                strCheckPartNumber = setCheckPartNumber.toString() + ' ';
            }
            //
            //校验必填
            String checkRequireResult = checkRequireField(lstMassUploadIndo);
            //查询价格之前做校验
            if (String.isNotBlank(strCheckModelNumber) || String.isNotBlank(strCheckPartNumber) || String.isNotBlank(strCheckCurrency) || String.isNotBlank(strCheckDate) || String.isNotBlank(checkRequireResult)){
                //表示校验不通过
                mapFeild2Value.put('Status', CCM_Constants.ERROR);
                String returnValue = (strCheckModelNumber == null ? '' : strCheckModelNumber) + (strCheckPartNumber == null ? '' : strCheckPartNumber) + (strCheckCurrency == null ? '' : strCheckCurrency) + (strCheckDate == null ? '' : strCheckDate) + (checkRequireResult == null ? '' : checkRequireResult);
                mapFeild2Value.put('Data', returnValue);


            }
            if (mapFeild2Value.get('Status') == CCM_Constants.ERROR){
                system.debug('报错-->' + mapFeild2Value);
                return mapFeild2Value;

            }
            //查询价格
            Map<String, Map<String, Object>> mapProductId2PriceInfo = new Map<String, Map<String, Object>>();
            mapProductId2PriceInfo = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfo(CustomerId, lstProductIds, Date.today(), mapProduct2Qty);
            // Account objAccount = new Account();
            // objAccount = [SELECT Id, Labor_Rate__c
            //               FROM Account
            //               WHERE Id = :customerId];
            //根据KitItem查询Parts 工时
            List<Kit_Item__c> lstKitItem = new List<Kit_Item__c>();
            lstKitItem = [SELECT k.EstimatedRepairTime__c, k.Id, k.Parts__c, k.Parts__r.Order_Model__c, k.Product__c, k.Product__r.Order_Model__c
                          FROM Kit_Item__c k
                          WHERE Product__r.Order_Model__c IN:setModelNumbers AND Parts__r.Order_Model__c IN:setPartNumbers];

            Map<String, Decimal> mapModel2Time = new Map<String, Decimal>();
            for (Kit_Item__c objKit : lstKitItem){
                Decimal standHourse = ((Decimal) objKit.EstimatedRepairTime__c);
                mapModel2Time.put(objKit.Product__r.Order_Model__c + objKit.Parts__r.Order_Model__c, standHourse);
            }
            //
            // Decimal standAmountPerHourse = objAccount.Labor_Rate__c == null ? 0 : objAccount.Labor_Rate__c;
            Decimal standAmountPerHourse = 0;
            String laborPriceBook = '';
            CCM_LaborUtil.LaborWrapper wrapper = CCM_LaborUtil.getLabor(customerId);
            if(wrapper != null) {
                standAmountPerHourse = wrapper.laborRate;
                laborPriceBook = wrapper.priceBookName;
            }
            else {
                // temporary solution for no labor price book
                laborPriceBook = 'EEG DE NP EUR - ab 2020';
            }
            //遍历massUpload-->计算价格
            Map<String, List<MassUploadInfo>> mapUploadNumber2Upload = new Map<String, List<MassUploadInfo>>();
            //把同一个放在一起
            //校验Model号

            for (MassUploadInfo objMassUpload : lstMassUploadIndo){
                List<MassUploadInfo> lstWarrantyClaimByNumber = mapUploadNumber2Upload.containsKey(objMassUpload.claimReferenceNumber) ? mapUploadNumber2Upload.get(objMassUpload.claimReferenceNumber) : new List<MassUploadInfo>();
                lstWarrantyClaimByNumber.add(objMassUpload);
                mapUploadNumber2Upload.put(objMassUpload.claimReferenceNumber, lstWarrantyClaimByNumber);
            }
            for (String Key : mapUploadNumber2Upload.keySet()){
                List<MassUploadInfo> lstWarrantyClaimByNumber = mapUploadNumber2Upload.get(Key);
                Decimal actualHourse = 0;
                Decimal actualAmount = 0;
                Decimal standAmount = 0;
                Decimal standHourse = 0;

                for (MassUploadInfo objMassUploadInfo : lstWarrantyClaimByNumber){

                    if (objMassUploadInfo.partNumber == Label.Laber_Hourse){
                        //行上实际工时
                        objMassUploadInfo.actualLaberHourse = (objMassUploadInfo.quantity == null ? 0 : Decimal.valueOf(objMassUploadInfo.quantity));
                        actualHourse += (objMassUploadInfo.quantity == null ? 0 : Decimal.valueOf(objMassUploadInfo.quantity));
                        // objMassUploadInfo.actualLaberRate = (objMassUploadInfo.customerUnitPrice == null ? 0 : Decimal.valueOf(objMassUploadInfo.customerUnitPrice));
                        objMassUploadInfo.customerUnitPrice = String.valueOf(standAmountPerHourse);
                        objMassUploadInfo.actualLaberRate = standAmountPerHourse;
                    } else{
                        //parts为Laber Hourse 表示是修理。插入修理需要的时间
                        String partsId = mapModel2Id.get(objMassUploadInfo.partNumber);
                        Map<String, Object> mapFeild2price = mapProductId2PriceInfo.get(partsId);
                        //如果不是laberHours 是其他得。需要调用计算价格代码 todo
                        if (mapFeild2price != null){
                            standAmount += ((Decimal) mapFeild2price.get(CCM_Constants.FINAL_PRICE) * Decimal.valueOf(objMassUploadInfo.quantity));
                            //行上系统标准价格
                            objMassUploadInfo.standUnitPrice = (Decimal) mapFeild2price.get(CCM_Constants.FINAL_PRICE);
                            //行上系统原价
                            objMassUploadInfo.standListPrice = (Decimal) mapFeild2price.get(CCM_Constants.LIST_PRICE);
                            //行上系统折扣
                            objMassUploadInfo.standDiscount = (Decimal) mapFeild2price.get(CCM_Constants.STAND_DISCOUNT);
                            //行上折扣类型
                            objMassUploadInfo.standApplicationMethod = (String) mapFeild2price.get(CCM_Constants.APPLICATION_METHOD);

                        } else{
                            //表示没有查到价格。设置stand Price为true
                            objMassUploadInfo.NoStandPrice = true;
                            //有一个行没有。则头上直接没有
                            objMassUploadInfo.NoStandPriceAll = true;
                        }

                    }
                    objMassUploadInfo.standLaberRate = standAmountPerHourse;
                    objMassUploadInfo.laborPriceBook = laborPriceBook;
                    Decimal CurrentHourse = (mapModel2Time.containsKey(objMassUploadInfo.modelNumber + objMassUploadInfo.partNumber) ? mapModel2Time.get(objMassUploadInfo.modelNumber + objMassUploadInfo.partNumber) : 0);
                    standHourse += CurrentHourse;
                    //行上标准工时
                    objMassUploadInfo.standLaberHourse = CurrentHourse;
                    standAmount += (CurrentHourse * standAmountPerHourse).setScale(2, RoundingMode.HALF_UP);
                    actualAmount += (Decimal.valueOf(objMassUploadInfo.quantity) * Decimal.valueOf(objMassUploadInfo.customerUnitPrice)).setScale(2, RoundingMode.HALF_UP);

                }
                for (MassUploadInfo objMassUploadInfo : lstWarrantyClaimByNumber){
                    objMassUploadInfo.actualAmount = String.valueOf(actualAmount.setScale(2, RoundingMode.HALF_UP));
                    objMassUploadInfo.actualHourse = String.valueOf(actualHourse.setScale(2, RoundingMode.HALF_UP));

                    objMassUploadInfo.standAmount = String.valueOf(standAmount.setScale(2, RoundingMode.HALF_UP));
                    objMassUploadInfo.standHourse = String.valueOf(standHourse.setScale(2, RoundingMode.HALF_UP));
                }


            }
            mapFeild2Value.put('Status', CCM_Constants.SUCCESS);
            mapFeild2Value.put('Data', JSon.serialize(lstMassUploadIndo));
            return mapFeild2Value;
        } catch (Exception e){
            system.debug('报错信息---->' + e.getMessage() + '报错行数---->' + e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static string uploadWarrantyClaim(String warrantyInfoJson, String customerId){
        system.debug('warrantyInfoJson--->' + warrantyInfoJson);
        try{
            Map<String, String> userTypeMap = Util.getPickListMap(new Warranty_Claim__c(), 'User_Type__c');
            Map<String, String> failureCodeMap = Util.getPickListMap(new Warranty_Claim__c(), 'Failure_Code__c');
            String distributorOrDealer = null;
            Boolean needCalculateTax = false;
            List<Account> customerList = [SELECT Country__c, Sales_Channel__c, Classification1__c FROM Account WHERE Id = :customerId];
            if(!customerList.isEmpty()) {
                if(customerList[0].Sales_Channel__c.containsIgnoreCase('Distributor')) {
                    distributorOrDealer = 'Distributor';
                }
                else if(customerList[0].Sales_Channel__c.containsIgnoreCase('Dealer')) {
                    distributorOrDealer = 'Dealer';
                }
                needCalculateTax = WarrantyClaimUtil.needCalculateTax(customerList[0]);
            }

            List<Warranty_Claim__c> lstWarrantyClaim = new List<Warranty_Claim__c>();
            List<Warranty_Claim_Item__c> lstWarrantyClaimItem = new List<Warranty_Claim_Item__c>();

            List<MassUploadInfo> lstMassUploadIndo = (List<MassUploadInfo>)JSON.deserialize(warrantyInfoJson, List<MassUploadInfo>.class);
            //对数据trim
            lstMassUploadIndo = toTrimWarranty(lstMassUploadIndo);
            String shipAddressId = null;
            String shippingAddressName = null;
            //一堆插入前校验
            //遍历massUpload-->将同一个number的放在一起
            List<Account_Address__c> lstAddress =    [
                SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                RecordType.Id, RecordType.DeveloperName,Company_name_1__c,Primary__c
                FROM Account_Address__c WHERE  Customer__c = :customerId  AND 
                RecordType.DeveloperName = 'Shipping_Address' AND Status__c = true AND Final_Address__c <> NULL
            ];
            if(lstAddress != null  && lstAddress.size()>0){
                for(Account_Address__c objAccount : lstAddress){
                    if(objAccount.Primary__c){
                        shipAddressId = objAccount.Id;
                        shippingAddressName = objAccount.Name;
                        break;
                    }
    
                }
                //如果没有找到primary的话随便抓一个
                if(String.isBlank(shipAddressId) ){
                    shipAddressId = lstAddress[0].Id;
                    shippingAddressName = lstAddress[0].Name;
                }

            }

            String billingAddressId = lstMassUploadIndo[0].billAddressId;
            String billingAddressName = null;
            for(Account_Address__c address : [SELECT Name FROM Account_Address__c WHERE Id = :billingAddressId]) {
                billingAddressName = address.Name;
            }

            Set<String> setOrderModel = new Set<String>();

            Map<String, List<MassUploadInfo>> mapUploadNumber2Upload = new Map<String, List<MassUploadInfo>>();
            for (MassUploadInfo objMassUpload : lstMassUploadIndo){
                List<MassUploadInfo> lstWarrantyClaimByNumber = mapUploadNumber2Upload.containsKey(objMassUpload.claimReferenceNumber) ? mapUploadNumber2Upload.get(objMassUpload.claimReferenceNumber) : new List<MassUploadInfo>();
                lstWarrantyClaimByNumber.add(objMassUpload);
                mapUploadNumber2Upload.put(objMassUpload.claimReferenceNumber, lstWarrantyClaimByNumber);
                setOrderModel.add(objMassUpload.modelNumber);
                setOrderModel.add(objMassUpload.partNumber);
            }
            //通过Model查询产品Id
            List<Product2> lstProducts = new List<Product2>();
            lstProducts = [SELECT Id, Order_Model__c, Brand_Name__c
                           FROM Product2
                           WHERE Order_Model__c IN:setOrderModel];
            Map<String, String> mapModel2Id = new Map<String, String>();
            set<String> setActualExistModels = new Set<String>();
            List<String> lstProductIds = new List<String>();
            String brand = null;
            for (Product2 objProduct : lstProducts){
                mapModel2Id.put(objProduct.Order_Model__c, objProduct.Id);
                setActualExistModels.add(objProduct.Order_Model__c);
                lstProductIds.add(objProduct.Id);
                brand = objProduct.Brand_Name__c;
            }
            //通过customer查询Code
            Map<String,String> mapProductId2PriceName = CCM_GetProductInfoUtil.getPriceName(lstProductIds,Date.today(),'',customerId);
            //遍历Map
            Set<String> lstSnNumbers = new Set<String>();
            List<String> lstResidentialEmail = new List<String>();
            List<String> lstCommercialEmail = new List<String>();
            for (String Key : mapUploadNumber2Upload.keySet()){
                List<MassUploadInfo> lstWarrantyClaimByNumber = mapUploadNumber2Upload.get(Key);
                //创建Claim信息。Claim以第一行为准
                Warranty_Claim__c objClaim = new Warranty_Claim__c();
                objClaim.Distributor_Or_Dealer__c = distributorOrDealer;
                objClaim.Brand__c = brand;
                objClaim.Dealer_Name__c = customerId;
                objClaim.Bill_Address__c = lstWarrantyClaimByNumber[0].billAddressId;
                objClaim.Bill_Address_Name__c = billingAddressName;
                objClaim.Purchase_Date__c = Date.valueOf(lstWarrantyClaimByNumber[0].purchaseDate);
                objClaim.Claim_Date__c = Date.valueOf(lstWarrantyClaimByNumber[0].claimDate);
                objClaim.Is_Mass_Upload__c = true;
                objClaim.Drop_off_Date__c = Date.valueOf(lstWarrantyClaimByNumber[0].failureDate);
                objClaim.Repair_Date__c = Date.valueOf(lstWarrantyClaimByNumber[0].repairDate);
                objClaim.Email_Address__c = lstWarrantyClaimByNumber[0].emailAddress;
                if(userTypeMap.containsKey(lstWarrantyClaimByNumber[0].userType)) {
                    objClaim.User_Type__c = userTypeMap.get(lstWarrantyClaimByNumber[0].userType);
                }
                else {
                    objClaim.User_Type__c = lstWarrantyClaimByNumber[0].userType;
                }
                objClaim.Master_Reference_Number__c = lstWarrantyClaimByNumber[0].masterReferenceNumber;
                objClaim.Reference_Number__c = lstWarrantyClaimByNumber[0].claimReferenceNumber;
                objClaim.Customer_Claim_Reference_Number__c = lstWarrantyClaimByNumber[0].claimReferenceNumber;
                objClaim.Model_Number__c = lstWarrantyClaimByNumber[0].modelNumber;
                objClaim.Serial_Number__c = lstWarrantyClaimByNumber[0].serialNumber;
                objClaim.Product__c = mapModel2Id.get(lstWarrantyClaimByNumber[0].modelNumber);
                if(failureCodeMap.containsKey(lstWarrantyClaimByNumber[0].failureCode)) {
                    objClaim.Failure_Code__c = failureCodeMap.get(lstWarrantyClaimByNumber[0].failureCode);
                }
                else {
                    objClaim.Failure_Code__c = lstWarrantyClaimByNumber[0].failureCode;
                }
                objClaim.CurrencyIsoCode = lstWarrantyClaimByNumber[0].currencyCode;
                objClaim.Owner__c = lstWarrantyClaimByNumber[0].owner;
                objClaim.Owner_Address__c = lstWarrantyClaimByNumber[0].ownerAddress;
                objClaim.Owner_City__c = lstWarrantyClaimByNumber[0].ownerCity;
                objClaim.Owner_PostalCode__c = lstWarrantyClaimByNumber[0].ownerPostalCode;
                objClaim.Failure_Description__c = lstWarrantyClaimByNumber[0].failureDescription;
                // objClaim.repair_Work__c = lstWarrantyClaimByNumber[0].repairWork;
                objClaim.Ship_Address__c = shipAddressId;
                objClaim.Ship_Address_Name__c = shippingAddressName;
                objClaim.Labor_Price_Book__c = lstWarrantyClaimByNumber[0].laborPriceBook;
                
                //objClaim.Total__c = Decimal.valueOf(lstWarrantyClaimByNumber[0].actualAmount);
                // objClaim.Standard_Parts_Hour__c = Decimal.valueOf(lstWarrantyClaimByNumber[0].standHourse);
                //objClaim.Actual_Time__c = Decimal.valueOf(lstWarrantyClaimByNumber[0].actualHourse);

                //objClaim.Stand_Amount__c = Decimal.valueOf(lstWarrantyClaimByNumber[0].standAmount);
                //默认是Approval
                Decimal standAmount = 0;
                if(lstWarrantyClaimByNumber[0].standAmount != 'N/A') {
                    standAmount = Decimal.valueOf(lstWarrantyClaimByNumber[0].standAmount);
                }
                if (checkHourOrAmount(standAmount, Decimal.valueOf(lstWarrantyClaimByNumber[0].actualAmount))){
                    //标准得比实际小
                    objClaim.Claim_Status__c = 'Submitted';
                } else{
                    objClaim.Claim_Status__c = 'Approved';

                }
                if(lstWarrantyClaimByNumber[0].emailAddress != null){
                    if(objClaim.User_Type__c == 'Commercial'){
                        lstCommercialEmail.add(lstWarrantyClaimByNumber[0].emailAddress);
                    }else if(objClaim.User_Type__c == 'Residential'){
                        lstResidentialEmail.add(lstWarrantyClaimByNumber[0].emailAddress);
                    }
                }
            
                Decimal laborRate = 0;
                
                for(MassUploadInfo objMassUpload : lstWarrantyClaimByNumber){
                    if(objMassUpload.partNumber == Label.Laber_Hourse){
                        laborRate = objMassUpload.actualLaberRate;
                    }
                }
                objClaim.Actual_Labor_Rate__c = laborRate;
                objClaim.Labor_Input_Time__c = lstWarrantyClaimByNumber[0].actualHourse == null ? 0 : Decimal.valueOf(lstWarrantyClaimByNumber[0].actualHourse);
                objClaim.Labor_Rate__c = lstWarrantyClaimByNumber[0].standLaberRate;
                objClaim.No_Standard_Price__c = lstWarrantyClaimByNumber[0].NoStandPriceAll == null ? false : lstWarrantyClaimByNumber[0].NoStandPriceAll;
                lstSnNumbers.add(objClaim.Serial_Number__c);

                lstWarrantyClaim.add(objClaim);
            }
            //通过email查询customer
            
            List<Contact> lstCommercialInfo =  
            [
                SELECT Email, FirstName, LastName, MobilePhone, Password__c, Phone ,accountId,Role__c, account.ShippingCountry,account.Name,Account.Site_Origin__c
                FROM Contact  
                WHERE Email IN : lstCommercialEmail
            ];
            Map<String,String> mapEmailAddress2Commerical = new Map<String,String>();
            for(Contact objContact : lstCommercialInfo){
                mapEmailAddress2Commerical.put(objContact.Email, objContact.AccountId);
            }
           
            List<Account> lstAccount = [
                SELECT Id,FirstName,LastName,PersonEmail,EGO_password__c,Password__c,PersonMobilePhone,Send_marketing_emails__c,Phone,Email__c,Brand_Name__c, 
                ShippingStreet,ShippingPostalCode,ShippingCity,ShippingState,ShippingCountry,Lawn_Size__c,Country__c,Postal_Code__c, Consumer_Status__c,
                Description,Username__c,Type,RecordTypeId,State__c,City__c,PersonContactId,DealerView__c,Status__c,Street__c,UserCode__c,
                Address_Detail__c,Product_Type__c,EGO_username__c,Site_Origin__pc,Company__pc,MarketingOptIn__pc
                FROM Account 
                WHERE Record_Type_Name__c = :CCM_Constants.PERSONACCOUNT  and PersonEmail IN : lstResidentialEmail
            ];
            Map<String,String> mapEmailAddress2Residential = new Map<String,String>();
            for(Account objAccount : lstAccount){
                mapEmailAddress2Residential.put(objAccount.PersonEmail, objAccount.Id);
            }


            //调用check
            Set<String> setErrorSn = checkSnInfo(lstSnNumbers, lstMassUploadIndo);
            if (setErrorSn != null && setErrorSn.size() > 0){
                for (Warranty_Claim__c objFinalClaim : lstWarrantyClaim){
                    
                    if (setErrorSn.contains(objFinalClaim.Serial_Number__c)){
                        //表示sn有问题
                        objFinalClaim.Claim_Status__c = 'Submitted';
                    }

                }

            }
            insert lstWarrantyClaim;
            //遍历插入的Warranty 将Number 映射到 id
            map<String, String> mapNumber2Ids = new Map<String, String>();
            map<String, Warranty_Claim__c> mapNumber2Object = new Map<String, Warranty_Claim__c>();
            List<Warranty_Claim__c> lstUpdateClaim  = new List<Warranty_Claim__c>();
            //遍历Claim 。如果是submit状态需要进入审批流
            List<Approval.ProcessSubmitRequest> requests = new List<Approval.ProcessSubmitRequest>();
            for (Warranty_Claim__c objWarrantyClaim : lstWarrantyClaim){
                mapNumber2Ids.put(objWarrantyClaim.Reference_Number__c, objWarrantyClaim.Id);
                mapNumber2Object.put(objWarrantyClaim.Reference_Number__c, objWarrantyClaim);
                if(objWarrantyClaim.Claim_Status__c == 'Submitted'){
                    Approval.ProcessSubmitRequest req = new Approval.ProcessSubmitRequest();
                    req.setObjectId(objWarrantyClaim.Id);
                    req.setSubmitterId(UserInfo.getUserId());
                    req.setProcessDefinitionNameOrId('Claim_Approval');
                    requests.add(req);

                }
                

            }
            //遍历Item。插入Item数据
            //遍历Map
            for (String Key : mapUploadNumber2Upload.keySet()){
                List<MassUploadInfo> lstWarrantyClaimByNumber = mapUploadNumber2Upload.get(Key);
                //默认没有一个是Parts
                Boolean isParts = false;
                Warranty_Claim__c objclaim = mapNumber2Object.get(lstWarrantyClaimByNumber[0].claimReferenceNumber);
                Decimal totalProductAmount = 0;
                for (MassUploadInfo objMassUploadInfo : lstWarrantyClaimByNumber){
                    Warranty_Claim_Item__c objWarrantyItem = new Warranty_Claim_Item__c();
                    objWarrantyItem.Warranty_Claim__c = mapNumber2Ids.get(objMassUploadInfo.claimReferenceNumber);
                    
                    // 时间-->标准的。
                    objWarrantyItem.labor_Time__c = objMassUploadInfo.standLaberHourse;
                    //objWarrantyItem.Labor_Input_Time__c = objMassUploadInfo.actualLaberHourse == null ? 0 : objMassUploadInfo.actualLaberHourse;
                    //时间。实际的
                    objWarrantyItem.Part_Number__c = objMassUploadInfo.partNumber;
                  
                    objWarrantyItem.Quantity__c = objMassUploadInfo.quantity;
                    objWarrantyItem.Type__c = 'additionalPart';
                    objclaim.Service_Option__c = 'Repair';
                    if (objMassUploadInfo.partNumber == objMassUploadInfo.modelNumber){
                        //标识是replacement
                        objWarrantyItem.Type__c = 'masterProduct';
                        objclaim.Service_Option__c = 'Replacement';
                        objclaim.Replacement_Option__c = 'Credit Memo';
                    }
                    //unitPrice是系统标准价格
                    objWarrantyItem.Unit_Price__c = objMassUploadInfo.standUnitPrice;
                    objWarrantyItem.Original_Price__c = objMassUploadInfo.standListPrice;
                    objWarrantyItem.Stand_Discount_Value__c = objMassUploadInfo.standDiscount;
                    objWarrantyItem.Application_Method__c = String.valueOf(objMassUploadInfo.standApplicationMethod);
                    objWarrantyItem.Part__c = mapModel2Id.get(lstWarrantyClaimByNumber[0].partNumber);
           
                    objWarrantyItem.PriceBook_Name__c = mapProductId2PriceName.get(objWarrantyItem.Part__c);
                    
                    //实际价格*实际重量
                    // objWarrantyItem.Total__c = Decimal.valueOf(objMassUploadInfo.quantity) * Decimal.valueOf(objMassUploadInfo.customerUnitPrice);
                    objWarrantyItem.Actual_Price__c = objMassUploadInfo.customerUnitPrice == null ? 0 : Decimal.valueOf(objMassUploadInfo.customerUnitPrice);

                    //labor Time
                    //工时不用拆。行上只有标准工时。没有实际工时。
                    //unit_Price是标准价格-->通过modifire计算
                    //   还需要加实际价格--->输入的 、标准 不取总的。standHourse。 头上标准的总的拖页面上。single也需要
                    //to Oracle 如果actual 为空。取原始逻辑。 如果不为空。 就取 实际价格
                    //单个产品的材料费用。所有产品材料非
                    //Labor不会存在数据库中
                    if(objMassUploadInfo.partNumber != Label.Laber_Hourse){
                        lstWarrantyClaimItem.add(objWarrantyItem);

                        if(needCalculateTax) {
                            totalProductAmount += objWarrantyItem.Actual_Price__c * Decimal.valueOf(objWarrantyItem.Quantity__c);
                        }
                    }
                    
                    //遍历一个批次的Parts 。如果所有Parts都不是Parts Number 那么repair option 为 labor time only 有一个是parts Number 则为 Parts
                    if( objMassUploadInfo.partNumber != objMassUploadInfo.modelNumber && 
                        objMassUploadInfo.partNumber != Label.Laber_Hourse){
                            //表示存在parts
                            isParts = true;
                    }
                }
                if(isParts && objclaim.Service_Option__c == 'Repair'){
                    //表示存在parts
                    objclaim.Repair_Type__c = 'Parts';
                }else if(!isParts && objclaim.Service_Option__c == 'Repair'){
                    objclaim.Repair_Type__c = 'Labor Time Only';
                }
               
                if(objclaim.User_Type__c == 'Residential'){
                    objclaim.Consumer__c =  mapEmailAddress2Residential.get(objclaim.Email_Address__c);
                }else if(objclaim.User_Type__c == 'Commercial'){
                 
                    objclaim.Consumer__c = mapEmailAddress2Commerical.get(objclaim.Email_Address__c);
                }
                if(needCalculateTax) {
                    objclaim.VAT__c = ((totalProductAmount * 19) / 100).setScale(2, RoundingMode.HALF_UP);
                }
                lstUpdateClaim.add(objclaim);
            }
            insert lstWarrantyClaimItem;
            update lstUpdateClaim;
            List<Approval.ProcessResult> results = Approval.process(requests);
            
           
            return null;


        } catch (Exception e){
            system.debug('报错信息---->' + e.getMessage() + '报错行数---->' + e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }

    //校验Model Number
    //入参: setProductModels 需要校验的Model号  setActualExistModels 目前存在的Model号
    //出参：有问题的Model号
    public static Set<String> checkProductModel(Set<String> setProductModels, set<String> setActualExistModels, List<MassUploadInfo> lstMassUploadIndo){
        Set<String> errorSet = New Set<String>();
        for (MassUploadInfo infoItem : lstMassUploadIndo){
            if (!setActualExistModels.contains(infoItem.modelNumber)){
                errorSet.add(infoItem.claimReferenceNumber + ':Product Model not exist in Oracle -' + infoItem.modelNumber + ' .');
            }
        }
        return errorSet;
    }
    //校验Part Number
    //入参: setProductModels 需要校验的Model号  setActualExistModels 目前存在的Model号,lstMassUploadIndo:导入数据
    //出参：有问题的Model号
    public static Set<String> checkPartModel(Set<String> setPartModels, set<String> setActualExistModels, List<MassUploadInfo> lstMassUploadIndo){
        Set<String> errorSet = New Set<String>();
        //查询是否有对应OrderModel
        for (MassUploadInfo infoItem : lstMassUploadIndo){
            if (String.isBlank(infoItem.partNumber)){
                errorSet.add(infoItem.claimReferenceNumber + ':Part Model can not blank.');
            } else if (!setActualExistModels.contains(infoItem.partNumber)){
                errorSet.add(infoItem.claimReferenceNumber + ':Part Model not exist in Oracle -' + infoItem.partNumber + ' .');
            }
        }
        return errorSet;
    }
    //校验Part Number,已经与modelnumber关系
    public static Set<String> checkPartModel2(Set<String> setPartModels, set<String> setActualExistModels, List<MassUploadInfo> lstMassUploadIndo){
        Set<String> errorSet = New Set<String>();
        Map<String, String> partMap = new Map<String, String>();
        // Map<String, String> queryPartMap = new Map<String, String>();
        Map<String, List<String>> queryPartMap = new Map<String, List<String>>();
        //查询是否有对应OrderModel
        for (MassUploadInfo infoItem : lstMassUploadIndo){
            partMap.put(infoItem.partNumber, infoItem.modelNumber);
            if (String.isBlank(infoItem.partNumber)){
                errorSet.add(infoItem.claimReferenceNumber + ':Part Model can not blank.');
            } else if (!setActualExistModels.contains(infoItem.partNumber)){
                errorSet.add(infoItem.claimReferenceNumber + ':Part Model not exist in Oracle -' + infoItem.partNumber + ' .');
            }
        }

        //查询kit Item下,是否有对应关系

        List<Kit_Item__c> productList = [select id, Parts__c, Parts__r.Order_Model__c, Accessory_SP__c, Product__c, Accessory_SP__r.Order_Model__c, Product__r.Order_Model__c
                                         from Kit_Item__c
                                         where RecordType.DeveloperName in ('Products_and_Parts', 'Accessories_and_Parts') and (Accessory_SP__r.Order_Model__c in:partMap.values() Or Product__r.Order_Model__c in:partMap.values())];
        for (Kit_Item__c kititem : productList){
            if (String.isNotBlank(kititem.Accessory_SP__c)){
                // queryPartMap.put(kititem.Parts__r.Order_Model__c, kititem.Accessory_SP__r.Order_Model__c);
                if(queryPartMap.containsKey(kititem.Parts__r.Order_Model__c)){
                    queryPartMap.get(kititem.Parts__r.Order_Model__c).add(kititem.Accessory_SP__r.Order_Model__c);
                }else{
                    queryPartMap.put(kititem.Parts__r.Order_Model__c, new List<String>{kititem.Accessory_SP__r.Order_Model__c});
                }
            } else{
                // queryPartMap.put(kititem.Parts__r.Order_Model__c, kititem.Product__r.Order_Model__c);
                if(queryPartMap.containsKey(kititem.Parts__r.Order_Model__c)){
                    queryPartMap.get(kititem.Parts__r.Order_Model__c).add(kititem.Product__r.Order_Model__c);
                }else{
                    queryPartMap.put(kititem.Parts__r.Order_Model__c, new List<String>{kititem.Product__r.Order_Model__c});
                }
            }
        }
        for (MassUploadInfo infoItem : lstMassUploadIndo){
            if (!(queryPartMap.containsKey(infoItem.partNumber) && queryPartMap.get(infoItem.partNumber).contains(partMap.get(infoItem.partNumber)))){
                errorSet.add(infoItem.claimReferenceNumber +':Part -' + infoItem.partNumber + ' and Product-' + partMap.get(infoItem.partNumber) + ' have no relationship in oracle');
            }
        }
        return errorSet;
    }
    //校验Currency.入参为currency 出参  true表示currency没有问题 。 false表示currency 有问题
    public static Boolean checkCurrency(String currencyCode){
        if (currencyCode == 'EUR' || currencyCode == 'GBP'){
            return true;
        } else{
            return false;
        }
    }
    //校验日期格式
    public static Boolean checkDateFormt(String dateString){
        try{
            Date FormulaDate = Date.valueOf(dateString);
            return true;
        } catch (Exception e){
            system.debug('时间格式');
            return false;
        }
    }
    //必填校验-->返回而未填的字段
    public static String checkRequireField(List<MassUploadInfo> lstMassUpload){
        Set<String> setBlankFeild = new Set<String>();
        String strMessage = Label.CCM_FieldRequired;
        for (MassUploadInfo objMassUpload : lstMassUpload){
            if (String.isBlank(objMassUpload.claimDate)){
                setBlankFeild.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_ClaimDate + ' ' + strMessage);
            }
            if (String.isBlank(objMassUpload.purchaseDate)){
                setBlankFeild.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_PurchaseDate + ' ' + strMessage);
            }
            if (String.isBlank(objMassUpload.repairDate)){
                setBlankFeild.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_RepairDate + ' ' + strMessage);
            }
            // if (String.isBlank(objMassUpload.failureDate)){
            //     setBlankFeild.add(objMassUpload.claimReferenceNumber + 'Failure Dete' + strMessage);
            // }
            // if (String.isBlank(objMassUpload.owner)){
            //     setBlankFeild.add(objMassUpload.claimReferenceNumber + 'Owner' + strMessage);
            // }
            if (String.isBlank(objMassUpload.modelNumber)){
                setBlankFeild.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_ModelNumber + ' ' + strMessage);
            }
            if (String.isBlank(objMassUpload.serialNumber)){
                setBlankFeild.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_SerialNumber + ' ' + strMessage);
            }
            if (String.isBlank(objMassUpload.failureDescription)){
                setBlankFeild.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_FailureDescription + ' ' + strMessage);
            }
            // if (String.isBlank(objMassUpload.repairWork)){
            //     setBlankFeild.add(objMassUpload.claimReferenceNumber + 'Repair Work' + strMessage);
            // }
            if(objMassUpload.partNumber != Label.Laber_Hourse && String.isBlank(objMassUpload.customerUnitPrice)) {
                setBlankFeild.add(objMassUpload.claimReferenceNumber + ' ' + Label.CCM_CustomerUnitPrice + ' ' + strMessage);
            }
        }
        if (setBlankFeild == null || setBlankFeild.size() == 0){
            return null;
        } else{
            system.debug('setBlankFeild.toString() + strMessage->' + setBlankFeild.toString() + strMessage);
            return setBlankFeild.toString() + strMessage;
        }
    }
    //When labor hour has actual time and it is granter than standard hours​
    //When total amount is granter than standard price amount​
    public static Boolean checkHourOrAmount(Decimal standInfo, Decimal actualInfo){
        if (standInfo < actualInfo){
            return true;
        } else{
            return false;
        }
    }
    //校验SN数据
    //入参： 需要校验的SN
    //出参： 有问题的SN信息
    public static Set<String> checkSnInfo(Set<String> setSerialNumbers, List<MassUploadInfo> lstMassUpload){
        Set<String> errorSet = New Set<String>();
        Set<String> errorSNSet = New Set<String>();
        Map<String, String> pdSnMap = new Map<String, String>();
        Map<String, String> mbSnMap = new Map<String, String>();
        Map<String, String> wrRuleSnMap = new Map<String, String>();
        for (MassUploadInfo infoItem : lstMassUpload){
            pdSnMap.put(infoItem.serialNumber, infoItem.PurchaseDate);
            mbSnMap.put(infoItem.serialNumber, infoItem.modelNumber);
        }
        for (Warranty_Rules__c warrantyRule : [SELECT Id, Product_Model__c, Code_in_Serial__c
                                               FROM Warranty_Rules__c
                                               WHERE Product_Model__c in:mbSnMap.values() AND RecordType.developerName = 'Model_Code' AND Product_Model__c != null AND Code_in_Serial__c != null]){
            wrRuleSnMap.put(warrantyRule.Product_Model__c, warrantyRule.Code_in_Serial__c);
        }
        for (String sn : setSerialNumbers){
            if (String.isBlank(sn)){
                errorSNSet.add(sn);
            } else{
                // 1.SN码第一位是否为'E'以及长度是否为15位
                if (sn.substring(0, 1) != 'E'){
                    errorSet.add('SN-' + sn + ' need Start With \'E\' .');
                    errorSNSet.add(sn);
                } else if (sn.length() != 15){
                    errorSet.add('Length of SN-' + sn + ' need Is 15 .');
                    errorSNSet.add(sn);
                } else{
                    //2.2-5位校验
                    String modelCodeSN = String.isNotBlank(sn) ? sn.substring(1, 5) : '';
                    system.debug(sn + ' ' + modelCodeSN);
                    // for (Warranty_Rules__c warrantyRule : [SELECT Id, Product_Model__c, Code_in_Serial__c
                    //                                        FROM Warranty_Rules__c
                    //                                        WHERE Product_Model__c = :modelnumber AND RecordType.developerName = 'Model_Code' AND Product_Model__c != null AND Code_in_Serial__c != null]){
                    //     if (warrantyRule.Code_in_Serial__c <> modelCodeSN){
                    //         errorSet.add('SN-' + sn + ',Base on warranty rule, check the 4-digit code from Number 2nd  to 5th.');
                    //     }
                    // }
                    //3.6-9位:时间数据校验
                    String pbdateString = pdSnMap.get(sn);
                    Boolean pbdateFlag = checkDateFormt(pbdateString);
                    if (pbdateFlag){
                        Date PD = Date.valueOf(pbdateString);
                        Integer daysSince1900_01_07 = Date.newInstance(1900, 1, 7).daysBetween(PD);
                        Integer dayNumber = Math.mod(daysSince1900_01_07, 7) + 1;
                        Date dateForYear = PD.addDays(Math.mod(8 - dayNumber, 7) - 3);
                        Integer year = dateForYear.year();
                        Date year_01_01 = Date.newInstance(year, 1, 1);
                        String PurchaseWeek = String.valueOf((Integer) Math.floor((year_01_01.daysBetween(PD) + Math.mod((Math.mod(Date.newInstance(1900, 1, 7).daysBetween(year_01_01), 7) + 1) + 1, 7) - 3) / 7 + 1));
                        String PurchaseYear4 = String.valueOf(PD.year());
                        String PurchaseYear = PurchaseYear4.substring(Math.max(0, PurchaseYear4.length() - 2));
                        if ((sn).substring(5, 7) > PurchaseYear){
                            // SN码中的年份校验错误
                            errorSet.add('SN-' + sn + 'contains 6 to 9 digits,Purchase Date > SN Time');
                            errorSNSet.add(sn);
                        } else if (sn.substring(5, 7) == PurchaseYear && PurchaseWeek < sn.substring(7, 9)){
                            // SN码中的周数校验错误
                            errorSet.add('SN-' + sn + 'contains 6 to 9 digits,Purchase Date > SN Time');
                            errorSNSet.add(sn);
                        }
                    }
                    //4.10-14位数字校验
                    if (!sn.substring(9, 14).isNumeric()){
                        errorSet.add('SN-' + sn + 'contains 10 to 14 digits,NOT Number');
                        errorSNSet.add(sn);

                    }
                    //5.15位大写字母校验
                    Set<String> endStr = new Set<String>{ 'E', 'A', 'B' };
                    if (!endStr.contains(sn.right(1))){
                        errorSet.add('SN-' + sn + 'Last character should be in(E,A,B)');
                        errorSNSet.add(sn);
                    }

                }
            }
        }


        return errorSNSet;
    }
    public static List<MassUploadInfo> toTrimWarranty(List<MassUploadInfo> lstMassUploadIndo){
        for (MassUploadInfo objMassUpload : lstMassUploadIndo){
            objMassUpload.claimReferenceNumber = objMassUpload.claimReferenceNumber == null ? '' : objMassUpload.claimReferenceNumber.trim();
            objMassUpload.masterReferenceNumber = objMassUpload.masterReferenceNumber == null ? '' : objMassUpload.masterReferenceNumber.trim();
            objMassUpload.claimDate = objMassUpload.claimDate == null ? '' : objMassUpload.claimDate.trim();
            objMassUpload.purchaseDate = objMassUpload.purchaseDate == null ? '' : objMassUpload.purchaseDate.trim();
            objMassUpload.failureDate = objMassUpload.failureDate == null ? '' : objMassUpload.failureDate.trim();
            objMassUpload.emailAddress = objMassUpload.emailAddress == null ? '' : objMassUpload.emailAddress.trim();
            objMassUpload.userType = objMassUpload.userType == null ? '' : objMassUpload.userType.trim();
            objMassUpload.owner = objMassUpload.owner == null ? '' : objMassUpload.owner.trim();
            objMassUpload.ownerAddress = objMassUpload.ownerAddress == null ? '' : objMassUpload.ownerAddress.trim();
            objMassUpload.ownerCity = objMassUpload.ownerCity == null ? '' : objMassUpload.ownerCity.trim();
            objMassUpload.ownerPostalCode = objMassUpload.ownerPostalCode == null ? '' : objMassUpload.ownerPostalCode.trim();
            objMassUpload.modelNumber = objMassUpload.modelNumber == null ? '' : objMassUpload.modelNumber.trim();
            objMassUpload.serialNumber = objMassUpload.serialNumber == null ? '' : objMassUpload.serialNumber.trim();
            objMassUpload.failureCode = objMassUpload.failureCode == null ? '' : objMassUpload.failureCode.trim();
            objMassUpload.repairWork = objMassUpload.repairWork == null ? '' : objMassUpload.repairWork.trim();
            objMassUpload.partNumber = objMassUpload.partNumber == null ? '' : objMassUpload.partNumber.trim();
            objMassUpload.quantity = objMassUpload.quantity == null ? '' : objMassUpload.quantity.trim();
            objMassUpload.currencyCode = objMassUpload.currencyCode == null ? '' : objMassUpload.currencyCode.trim();
            objMassUpload.customerUnitPrice = objMassUpload.customerUnitPrice == null ? '' : objMassUpload.customerUnitPrice.trim();

        }
        return lstMassUploadIndo;
    }
    @AuraEnabled
    public static List<Map<String, String>> QueryBillAddressInfo(String customerId){
        List<Map<String, String>> lstArrangementInfo = new List<Map<String, String>>();
        try{
            List<Account_Address__c> lstBillAddressInfo = [Select a.Final_Address__c, a.Id, a.RecordType_Name__c
                                                           from Account_Address__c a
                                                           WHERE Customer__c = :customerId AND RecordType_Name__c = 'Billing_Address'];
            for (Account_Address__c objAccountAddress : lstBillAddressInfo){
                Map<String, String> mapBillAddressInfo = new Map<String, String>();
                mapBillAddressInfo.put('addressId', objAccountAddress.Id);
                mapBillAddressInfo.put('addressInfo', objAccountAddress.Final_Address__c);
                lstArrangementInfo.add(mapBillAddressInfo);
            }
            return lstArrangementInfo;


        } catch (Exception e){
            system.debug('报错信息--->' + e.getMessage() + '报错行数----->' + e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static String getCustomer() {
        String customerId = null;
        for(User u : [SELECT Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()]) {
            customerId = u.Contact.AccountId;
        }
        return customerId;
    }

    public class MassUploadInfo{
        @AuraEnabled
        public String claimReferenceNumber{ get; set; }
        @AuraEnabled
        public String masterReferenceNumber{ get; set; }
        @AuraEnabled
        public String claimDate{ get; set; }
        @AuraEnabled
        public String billAddressId{ get; set; }
        @AuraEnabled
        public String purchaseDate{ get; set; }
        @AuraEnabled
        public String repairDate{ get; set; }
        @AuraEnabled
        public String failureDate{ get; set; }
        @AuraEnabled
        public String emailAddress{ get; set; }
        @AuraEnabled
        public String userType{ get; set; }
        @AuraEnabled
        public String owner{ get; set; }
        @AuraEnabled
        public String ownerAddress{ get; set; }
        @AuraEnabled
        public String ownerCity{ get; set; }
        @AuraEnabled
        public String ownerPostalCode{ get; set; }
        @AuraEnabled
        public String modelNumber{ get; set; }
        @AuraEnabled
        public String serialNumber{ get; set; }
        @AuraEnabled
        public String failureCode{ get; set; }
        @AuraEnabled
        public String failureDescription{ get; set; }
        @AuraEnabled
        public String repairWork{ get; set; }
        @AuraEnabled
        public String partNumber{ get; set; }
        @AuraEnabled
        public String quantity{ get; set; }
        @AuraEnabled
        public String currencyCode{ get; set; }
        //行上实际价格
        @AuraEnabled
        public String customerUnitPrice{ get; set; }
        @AuraEnabled
        public String standAmount{ get; set; }
        @AuraEnabled
        public String standHourse{ get; set; }
        @AuraEnabled
        public String actualAmount{ get; set; }
        @AuraEnabled
        public String actualHourse{ get; set; }
        @AuraEnabled
        public Boolean NoStandPriceAll{ get; set; }
        @AuraEnabled
        public Boolean NoStandPrice{ get; set; }
        //行上系统价格  listPrice*Modifire
        @AuraEnabled
        public Decimal standUnitPrice{ get; set; }
        //行上系统工时
        @AuraEnabled
        public Decimal standLaberHourse{ get; set; }
        //行上输入工时
        @AuraEnabled
        public Decimal actualLaberHourse{ get; set; }
        //行上系统人工单价
        @AuraEnabled
        public Decimal standLaberRate{ get; set; }
        //行上输入人工单价
        @AuraEnabled
        public Decimal actualLaberRate{ get; set; }
        //行上原价
        @AuraEnabled
        public Decimal standListPrice{ get; set; }
        //行上折扣
        @AuraEnabled
        public Decimal standDiscount{ get; set; }
        //行上折扣类型
        @AuraEnabled
        public String standApplicationMethod{ get; set; }
        @AuraEnabled
        public String laborPriceBook {get;set;}
    }
    public CCM_MassUploadCtl(){
    }
}