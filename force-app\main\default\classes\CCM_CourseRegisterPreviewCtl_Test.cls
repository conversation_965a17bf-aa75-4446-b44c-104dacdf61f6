/**
 * Honey
 * 2023/11/03
 */
@isTest
public without sharing class CCM_CourseRegisterPreviewCtl_Test {
    @isTest
    public static void testQueryRegisterInfo() {
        // Test data setup
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Country__c = 'US',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            Status__c = true
        );
        insert billingAddress;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = billingAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = account.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 100,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]'
        );
        insert courseRegister;
        Purchase_Order_Attachment__c purchaseAttachment = new Purchase_Order_Attachment__c(
            File_Name__c = 'Sample File',
            Course_Register__c = courseRegister.Id,
            File_Date__c = Date.today()
        );
        insert purchaseAttachment;
        
        Course_Register_Item__c CRI_c = new Course_Register_Item__c(Course_Register__c = courseRegister.Id);
        insert CRI_c;
        
        Test.startTest();
        
        // Call the method
        Map<String, Object> result = CCM_CourseRegisterPreviewCtl.queryRegisterInfo(courseRegister.Id);
        
        Test.stopTest();
        
        // Assertions
        System.assertEquals('false', result.get('isOrder'));
    }
    @IsTest
    static void testuploadFileMidel(){
        
        CCM_PurchaseOrderPreview.uploadFileMidel('1231', '1.txt', '1.txt');
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Country__c = 'US',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            Status__c = true
        );
        insert billingAddress;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = billingAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = account.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 100,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]'
        );
        insert courseRegister;
        CCM_PurchaseOrderPreview.uploadFileInfo info = new CCM_PurchaseOrderPreview.uploadFileInfo();
        info.fileType = '';
        info.fileName = '1.txt';
        info.fileDate = Date.today().addDays(5);
        try{
            CCM_CourseRegisterPreviewCtl.uploadFile(courseRegister.Id, new List<CCM_PurchaseOrderPreview.uploadFileInfo>{ info });
        } catch (Exception e){
        }
        
    }
    @IsTest
    static void testuploadFile(){
        
        try{
            CCM_CourseRegisterPreviewCtl.uploadFileMidel('1231','test','Honey');
        } catch (Exception e){
        }
        
    }
    @isTest
    public static void testQueryRegisterList() {
        // Test data setup
        String fifter = '';
        
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Customer__c = account.Id,
            Order_Type__c = 'Training Request',
            Status__c = 'Draft',
            CurrencyIsoCode = 'EUR',
            Training_Course__c = objSetting.Id,
            Course_Arrangement__c = courseArrangement.Id
        );
        insert courseRegister;
        
        Test.startTest();
        
        // Call the method
        List<Map<String, Object>> result = CCM_CourseRegisterPreviewCtl.queryRegisterList(fifter);
        
        Test.stopTest();
        
        // Assertions
        System.assertEquals(1, result.size());
    }
    @isTest
    public static void testQueryRegisterList2() {
        // Test data setup
        String fifter = '';
        CCM_CourseRegisterPreviewCtl.FilterWrapper objFilter = new CCM_CourseRegisterPreviewCtl.FilterWrapper();
        objFilter.OrderNumber = '123';
        objFilter.OrderType = '123';
       // objFilter.CreatedBy = '123';
        //objFilter.Customer = '123';
        objFilter.CustomerNumber = '123';
        //objFilter.SalesPerson = '123';
        objFilter.requestNo = '123';
       objFilter.OrderDateFrom = String.valueOf(Date.today().addDays(-100));
        objFilter.OrderDateTo =  String.valueOf(Date.today().addDays(100));
         objFilter.trainingDateTimeFrom = '2001-12-01T10:04:00.000Z';
        objFilter.trainingDateTimeTo = '2099-12-01T10:04:00.000Z';
        objFilter.Model = '123';
        objFilter.OrderStatus = '123';
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Customer__c = account.Id,
            Order_Type__c = 'Training Request',
            Status__c = 'Draft',
            CurrencyIsoCode = 'EUR',
            Training_Course__c = objSetting.Id,
            Course_Arrangement__c = courseArrangement.Id
        );
        insert courseRegister;
        
        Test.startTest();
        
        // Call the method
        List<Map<String, Object>> result = CCM_CourseRegisterPreviewCtl.queryRegisterList(JSON.serialize(objFilter));
        
        Test.stopTest();
        
        // Assertions
     
    }
    @isTest
    public static void testRegisterSubmit() {
        // Test data setup
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = true
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Customer__c = account.Id,
            Order_Type__c = 'Training Request',
            Status__c = 'Draft',
            CurrencyIsoCode = 'EUR',
            Training_Course__c = objSetting.Id,
            Course_Arrangement__c = courseArrangement.Id
        );
        insert courseRegister;
        String jsonApprovalInfoString = '{"recordId":"'+ courseRegister.Id+'","comments":"Sample Comments"}';
        String jsonApprovalInfoString2 = '{"recordId":"'+ courseRegister.Id+'","comments":"Sample Comments","action":"Approve"}';
        
        Test.startTest();
        
        // Call the method
        String result = CCM_CourseRegisterPreviewCtl.RegisterSubmit(jsonApprovalInfoString);
        String result2 = CCM_CourseRegisterPreviewCtl.approvalOrRejectRegister(jsonApprovalInfoString2);
        CCM_CourseRegisterPreviewCtl.ReceiptCourse(courseRegister.Id);
        CCM_CourseRegisterPreviewCtl.CancelCourse(courseRegister.Id, 'Test Reason');
        CCM_CourseRegisterPreviewCtl.SendNotifyNocation(courseRegister.Id,Label.After_Sales_Queue);
        
        Test.stopTest();
        
        // Assertions
        System.assertEquals(CCM_Constants.SUCCESS, result);
        
        // Additional assertions for the Approval.ProcessSubmitRequest and SendNotifyNocation methods
    }
    public CCM_CourseRegisterPreviewCtl_Test() {

    }
}