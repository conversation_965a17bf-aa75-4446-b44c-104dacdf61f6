/**
 * Created by gluo006 on 8/28/2019.
 */
({
    getProductList: function (component) {
        component.set('v.isSearching', true);
        console.log(component.get('v.fieldName'), 'component================');
        var self = this;
        if(component.get('v.fieldName') == 'Master Model Number:' || component.get('v.fieldName') == 'Kit/Model:'){
            self.warrantyMasterProduct(component);
        }else if(component.get('v.pageName') == 'eligibilityCheck'){
            component.set('v.isProductCodeShow', true);
            self.getPartsToCheckEligilibility(component);
        }else if(component.get('v.fieldName') == ''){ //claim additional parts
            component.set('v.isProductCodeShow', true);
            self.SearchPartsListByProduct(component);
        }else if(component.get('v.fieldName') == 'Purchase place:'){ //claim
            self.getPurchasePlace(component);
        }else if(component.get('v.fieldName') == 'Product'){
            self.searchProduct(component);
        }else if(component.get('v.fieldName') == 'Model Number'){
            console.log(component.get('v.isClaim'), 'Model Number -----------');
            if (component.get('v.isClaim')) {
                self.queryModelNumber(component);
            } else if (component.get('v.isClaimInfo')) {
                self.queryModelNumberWithoutCustomer(component);
            } else{
                self.queryModelNumberWithoutCustomer(component);
            }
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_OrderNo")) {
            component.set('v.requestType', 'Order');
            self.getSNList(component, 'Order');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_InvoiceNo")) {
            component.set('v.requestType', 'Invoice');
            self.getSNList(component, 'Invoice');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_ShipmentNo")) {
            component.set('v.requestType', 'Shipment');
            self.getSNList(component, 'Shipment');
        } else if (component.get('v.fieldName') == 'Country') {
            self.getCountry(component);
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_DropshipCountry")) {
            self.getCountry(component);
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_Customer") || component.get('v.fieldName') == $A.get("$Label.c.CCM_CustomerName") || component.get('v.fieldName') == $A.get("$Label.c.CCM_SelectCustomer") || component.get('v.fieldName') == $A.get("$Label.c.CCM_LentOutToCustomer")) {
            if (component.get('v.isClaim')) {
                self.getClaimCustomer(component);
            } else {
                self.getOrderList(component, 'Customer');
            }
        } else if (component.get('v.fieldName') == $A.get("$Label.c.Order_Salesperson")) {
            self.getOrderList(component, 'SalesPerson');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_CreatedBy")) {
            self.getOrderList(component, 'CreatedBy');
        } else if (component.get('v.fieldName') == 'Product Description') {
            self.getProductInfo(component);
        } else if (component.get('v.fieldName') == "Select Training Customer") {       // calvin add
            self.Trainingavailable(component);
        } else if (component.get('v.fieldName') == "Select Training Location") {       // calvin add
            self.queryLocationLookUpInfo(component);
        } else if (component.get('v.fieldName') == "Select Bill To Address") {         // calvin add
            self.TrainingavailableBill(component);
        } else if (component.get('v.fieldName') == "Select Ship To Address") {         // calvin add
            self.TrainingavailableShip(component);
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_BillToAddress") && !component.get('v.isResalesFill') && !component.get('v.isInvoice')) {
            self.getAddressByName(component, 'Billing Address');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_ShipToAddress") && !component.get('v.isResalesFill') && !component.get('v.isInvoice')) {
            self.getAddressByName(component, 'Shipping Address');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_DropshipAddress") && !component.get('v.isResalesFill') && !component.get('v.isInvoice')) {
            self.getAddressByName(component, 'Dropship Shipping Address');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_SelectProspect") || component.get('v.fieldName') == $A.get("$Label.c.CCM_LentOutToProspect")) {
            self.getOrderList(component, 'Prospect');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_BillToAddress") && component.get('v.isResalesFill')) {
            self.getResalesAddressList(component, 'Bill To Address');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_ShipToAddress") && component.get('v.isResalesFill')) {
            self.getResalesAddressList(component, 'Ship To Address');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_DropshipAddress") && component.get('v.isResalesFill')) {
            self.getResalesAddressList(component, 'DropShip Address');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_BillToAddress") && component.get('v.isInvoice')) {
            self.getInvoiceAddressList(component, 'Billing_Address');
        } else if (component.get('v.fieldName') == $A.get("$Label.c.CCM_ShipToAddress") && component.get('v.isInvoice')) {
            self.getInvoiceAddressList(component, 'Shipping_Address');
        } else if (component.get('v.fieldName') == 'Parent Warranty Claim') {
            self.getWarrantyClaim(component);
        } else if (component.get('v.fieldName') == 'Address' && component.get('v.isClaim')) {
            self.getClaimAddress(component);
        } else if (component.get('v.fieldName') == 'Bill To' && component.get('v.isClaim')) {
            self.getClaimAddress(component);
        } else if (component.get('v.fieldName') == 'Repairable Parts' && component.get('v.isClaim')) {
            console.log('Repairable Parts-----------111');
            self.getRepairableParts(component);
        } else if (component.get('v.fieldName') == 'Select Contact' && component.get('v.accId')) {
            self.getContactByCustomer(component, component.get('v.accId'));
        } else if (component.get('v.fieldName') == 'Related Contact') {
            self.getRelatedByAddress(component);
        } else if (component.get('v.fieldName') == 'Related Address') {
            self.getRelatedByContact(component);
        } else if (component.get('v.fieldName') == 'Fleet Manager') {
            self.getFleetManager(component);
        }
    },
    // 获取Master Model number
    warrantyMasterProduct:function(component){
        var action = component.get("c.masterModelNumber");
        let needFilterInactive = true;
        const fromProductRegistration = component.get('v.fromProductRegistration');
        if(fromProductRegistration){
            needFilterInactive = false;
        }
        action.setParams({
           filterStr: component.get('v.inputVal'),
           needFilterInactive: needFilterInactive
        });
        action.setCallback(this, function (response) {
            console.log(response, 'warrantyMasterProduct===============');
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               if(result){
                   component.set('v.productList', JSON.parse(result));

                    console.log(component.get('v.productList'));
                   component.set('v.isSearching', false);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    // Eligibilitycheck检索产品
    getPartsToCheckEligilibility: function(component){
        console.log("执行了当前的方法");
        var action = component.get("c.GeneratePartsList");
        action.setParams({
           productId: component.get('v.productId'),
           filterStr: component.get('v.inputVal')
        });
        action.setCallback(this, function (response) {
            console.log("response",JSON.parse(JSON.stringify(response)));
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               console.log("state",state,"result",JSON.parse(JSON.stringify(result)));

               if(result){
                   component.set('v.productList', JSON.parse(result).PartsList);
                   component.set('v.isSearching', false);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    // Training获取可选Customer上的billaddress
    TrainingavailableBill:function(component){
        var action = component.get("c.queryBillAddressInfo");
        action.setParams({
            addressName: component.get('v.inputVal'),
            customerId: component.get('v.customerId'),
            isPortal: false,
            contactId: component.get('v.contactId'),
        });
        action.setCallback(this, function (response) {
            console.log("customerId", component.get('v.customerId'));
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               console.log("resultttt",JSON.stringify(result));
               if(result.length){
                    result.forEach(item => {
                    item.Name = item.name
                    item.Id = item.id
                    })
                   component.set('v.productList', JSON.parse(JSON.stringify(result)));
                   component.set('v.isSearching', false);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    // Training获取可选Customer上的billaddress
    queryLocationLookUpInfo:function(component){
        
    console.log("获取Training 地址信息")
        var action = component.get("c.queryLocationLookUpInfo");
        action.setParams({});
        action.setCallback(this, function (response) {
           var state = response.getState();
           console.log("获取Training 地址信息",JSON.stringify(state));

           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               console.log("获取Training 地址信息",JSON.stringify(result));
               if(result.length){
                    result.forEach(item => {
                    item.Name = item.name
                    item.Id = item.id
                    })
                   component.set('v.productList', JSON.parse(JSON.stringify(result)));
                   console.log(component.get('v.productList'));
                   component.set('v.isSearching', false);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    // Training获取可选Customer上的shipaddress
    TrainingavailableShip:function(component){
        var action = component.get("c.queryShipAddressInfo");
        action.setParams({
            addressName: component.get('v.inputVal'),
            customerId: component.get('v.customerId'),
            isPortal: false,
            contactId: component.get('v.contactId'),
        });
        action.setCallback(this, function (response) {
            console.log(response, 'Training获取可选Customer');
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               if(result.length){
                    result.forEach(item => {
                    item.Name = item.name
                    item.Id = item.customerId
                    })
                   component.set('v.productList', JSON.parse(JSON.stringify(result)));
                   console.log(component.get('v.productList'));
                   component.set('v.isSearching', false);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    // Training获取可选Customer
    Trainingavailable:function(component){
        var action = component.get("c.queryAvaliableCustomer");
        action.setParams({
           queryName: component.get('v.inputVal')
        });
        action.setCallback(this, function (response) {
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
                // console.log(JSON.stringify(result), 'Training获取可选Customer');

                if(result.length){
                    result.forEach(item => {
                    item.Name = item.name
                    item.Id = item.customerId
                    item.modelNumber = item.accountDescription
                    })
                    component.set('v.isProductCodeShow',true)
                    component.set('v.productList', JSON.parse(JSON.stringify(result)));
                    // console.log(JSON.stringify(component.get('v.productList')));
                    component.set('v.isSearching', false);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    getPurchasePlace: function(component){
        var action = component.get("c.PurchasePlacePicklist");
        var userInfo = $A.get("$SObjectType.CurrentUser");
        var userId;
        if(userInfo.Id){
            userId = userInfo.Id;
        }
        action.setParams({
           filterStr: component.get('v.inputVal'),
           userId: userId,
           brand: component.get('v.brandName')
        });
        action.setCallback(this, function (response) {
           var state = response.getState();
           if (state === "SUCCESS") {
               var result = response.getReturnValue();
               if(result){
                   component.set('v.productList', JSON.parse(result).PicklistValue.Name);
                   component.set('v.isSearching', false);
               }
           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    // 获取SN list
    getSNList:function(component, type){
        var action = component.get("c.queryNoByType");
        action.setParams({
            type: type,
            value: component.get('v.inputVal'),
            customerId: component.get('v.userId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if(result){
                    component.set('v.productList', JSON.parse(result));
                    console.log(component.get('v.productList'));
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取order list
    getOrderList:function(component, type) {
        component.set('v.searchCustomerFlag', false);
        var action = component.get("c.lookUpSearch");
        action.setParams({
            fifterString: component.get('v.inputVal'),
            fieldAPI: type
        });
        console.log(action, 'action==========');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                console.log(JSON.parse(result), 'result==========');
                if(result){
                    component.set('v.productList', JSON.parse(result));
                    console.log(component.get('v.productList'));
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取 Country
    getCountry:function(component){
        var action = component.get("c.getCountry");
        action.setParams({
            fifter: component.get('v.inputVal')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if(result){
                    component.set('v.productList', JSON.parse(result));
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取order下单product list
    getProductInfo:function(component){
        var action = component.get("c.GetProductInfo");
        action.setParams({
            FilterString: component.get('v.inputVal'),
            CustomerId: component.get('v.customerId'),
            PricingDate: component.get('v.pricingDate'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if(result){
                    component.set('v.productList', JSON.parse(result));
                    console.log(component.get('v.productList'));
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取订单地址
    getAddressByName:function(component, type){
        console.log(component.get('v.contactId'), '获取订单地址====================contactId');
        var action = component.get("c.QueryAddressByName");
        action.setParams({
            AddressName: component.get('v.inputVal'),
            customerId: component.get('v.customerId'),
            recordTypeName: type,
            isPortal: component.get('v.isPortal'),
            contactId: component.get('v.contactId')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if(result){
                    component.set('v.productList', JSON.parse(result));
                    console.log(component.get('v.productList'));
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    fireEvent:function(comp,eventType,data)
    {
        //Obtain an event
        var event=comp.getEvent(eventType);
        //The value is set to the parameter of the event
        event.setParams({
            type:eventType,
            data:data
        });
        //Emission
        event.fire();
    },
    // 获取resales地址列表
    getResalesAddressList:function(component, type){
        var action = component.get("c.QueryAddressInfo");
        let params = {
            addressName: component.get('v.inputVal'),
            apiName: type,
            entityId: component.get('v.entityId'),
            isProspect: component.get('v.isProspect')
        };
        action.setParams({
            JSONStringQueryInfo: JSON.stringify(params),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let data = JSON.parse(result.data);
                if(data){
                    component.set('v.productList', data);
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    
    // 获取Invoice地址列表
    getInvoiceAddressList:function(component, type){
        var action = component.get("c.getDeliveryAddress");
        action.setParams({
            addressName: component.get('v.inputVal'),
            apiName: type,
            entityId: component.get('v.entityId'),
            isProspect: component.get('v.isProspect')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let data = JSON.parse(result.data);
                if(data){
                    component.set('v.productList', data);
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
// 获取claim customer
    getClaimCustomer:function(component, type){
        var action = component.get("c.queryCustomer");
        action.setParams({
            name: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(result, '获取claim customer1-----------------0');
                let data = result.List;
                console.log(data, '获取claim customer1-----------------1')
                let arr = [];
                data.forEach((item)=>{
                    arr.push({
                        Id: item.consumerId,
                        Name: item.name
                    })
                })
                component.set('v.productList', arr);
                component.set('v.isSearching', false);
                console.log(JSON.parse(JSON.stringify(arr)), '获取claim customer-----------------2')
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // parent Warranty Claim
    getWarrantyClaim : function(component, type){
        let accId = component.get('v.accId');
        console.log(accId, 'accId---------');
        if (!accId) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'Please select customer first!',
                "type": "warning"
            }).fire();
            return;
        }
        var action = component.get("c.queryWarrantyClaim");
        action.setParams({
            ConsumerId: accId,
            name: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                let data = result.List;
                let arr = [];
                data.forEach((item)=>{
                    arr.push({
                        Id: item.warrantyClaimId,
                        Name: item.name
                    })
                })
                component.set('v.productList', arr);
                component.set('v.isSearching', false);
                console.log(JSON.parse(JSON.stringify(arr)), 'parent Warranty Claim-----------------2')
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取claim model number
    queryModelNumber : function(component, type){
        var action = component.get("c.queryModelNumber");
        action.setParams({
            accId: component.get('v.accId') || '',
            orderModel: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                let data = result.List;
                let arr = [];
                data.forEach((item)=>{
                    arr.push({
                        Id: item.modelNumber,
                        Name: item.modelNumber,
                        laborTime: item.laborTime,
                        unitPrice: item.unitPrice,
                        productId: item.productId,
                        productName: item.productName,
                        noCalculate: item.noCalculate,
                        modelNumber: item.modelNumber,
                    })
                })
                component.set('v.productList', arr);
                component.set('v.isSearching', false);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取claim address
    getClaimAddress : function(component, type){
        var action = component.get("c.queryAddress");
        action.setParams({
            AccountId: component.get('v.accId'),
            type: component.get('v.claimAddressType'),
            name: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(result, '获取claim address----------');
                let data = result.List;
                let arr = [];
                data.forEach((item)=>{
                    arr.push({
                        Id: item.addressId,
                        Name: item.name,
                    })
                })
                component.set('v.productList', arr);
                component.set('v.isSearching', false);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取Repairable Parts
    getRepairableParts : function(component, type){
        console.log('Repairable Parts-----------222');

        var action = component.get("c.queryParts");
        action.setParams({
            productId: component.get('v.productId'),
            customerId: component.get('v.accId'),
            name: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                if (!component.get('v.productId')) {
                    component.set('v.productList', []);
                    component.set('v.isSearching', false);
                } else {
                    var result = JSON.parse(response.getReturnValue());
                    console.log(JSON.parse(JSON.stringify(result)), '获取Repairable Parts----------');
                    let data = result.List;
                    let arr = [];
                    data.forEach((item)=>{
                        arr.push({
                            Id: item.productId,
                            Name: item.productName,
                            laborTime: item.laborTime,
                            unitPrice: item.unitPrice,
                            productId: item.productId,
                            productName: item.productName,
                            noCalculate: item.noCalculate,
                            modelNumber: item.modelNumber,
                            ProductDescription: item.productName
                        })
                    })
                    component.set('v.productList', arr);
                    component.set('v.isSearching', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取claim info
    queryModelNumberWithoutCustomer : function(component, type){
        console.log('queryModelNumberWithoutCustomer-----------');
        var action = component.get("c.queryModelNumberWithoutCustomer");
        action.setParams({
            orderModel: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                let data = result.List;
                let arr = [];
                data.forEach((item)=>{
                    arr.push({
                        Id: item.modelNumber,
                        Name: item.modelNumber,
                        laborTime: item.laborTime,
                        unitPrice: item.unitPrice,
                        productId: item.productId,
                        productName: item.productName,
                        noCalculate: item.noCalculate,
                        modelNumber: item.modelNumber,
                    })
                })
                component.set('v.productList', arr);
                component.set('v.isSearching', false);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取contact
    getContactByCustomer : function(component, accId){
        var action = component.get("c.getContactByCustomer");
        action.setParams({
            accId: accId,
            contactName: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(result), 'result=================');
                let data = result.List;
                let arr = [];
                data.forEach((item)=>{
                    arr.push({
                        Id: item.contactId,
                        Name: item.contactName,
                    })
                })
                component.set('v.productList', arr);
                component.set('v.isSearching', false);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 通过address获取关联contact
    getRelatedByAddress : function(component){
        var action = component.get("c.getContactByAddress");
        action.setParams({
            addressId: component.get('v.addressId'),
            name: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(result), 'result=================');
                component.set('v.productList', result.Data);
                component.set('v.isSearching', false);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 通过contact获取关联address
    getRelatedByContact: function(component){
        console.log(component.get('v.contactId'), '通过contact获取关联address=================');
        var action = component.get("c.getAddressByContact");
        console.log(JSON.stringify(action), 'action================');
        action.setParams({
            contactId: component.get('v.contactId'),
            name: component.get('v.inputVal'),
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(result), '通过contact获取关联address=================');
                component.set('v.productList', result.Data);
                component.set('v.isSearching', false);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取FleetManager
    getFleetManager: function(component){
        var action = component.get("c.fleetManageSearch");
        action.setParams({
            fifterString: component.get('v.inputVal'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                component.set('v.isSearching', false);
                let arr = [];
                result.forEach((item)=>{
                    arr.push({
                        Id: item.Id,
                        Name: item.Name,
                    })
                })
                component.set('v.productList', arr);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
})