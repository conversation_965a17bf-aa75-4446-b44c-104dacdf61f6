pipeline {
  agent {
    node {
      label 'python3.8'
    }

  }
  stages {
    stage('stage-xtqz8') {
      agent none
      steps {
        container('python') {
          sh '''python3 -V
sfdx --version
pwd
chmod -R a+x *
ls -alht
echo ${WORKSPACE}
#sleep 999
cd cicd/scripts/
python3 main.py
cd ${WORKSPACE}
if [ -d ${WORKSPACE}/package ]
then
  zip -r package.zip package
  ls -alht 
else
  echo "没有package目录"
fi
'''
        }

      }
    }
	
    stage('Artifacts') {
      agent none
      steps {
        archiveArtifacts 'cicd/config/result/deployment/deploy_result.txt'
		archiveArtifacts 'cicd/config/result/test/test_result.csv'
		archiveArtifacts 'package.zip'
      }
    }

  }
}
