/**@Description SN Export Controller
 * @Auther Vince Yang
 * @Data 
 * {
    "requestDate": "2023-05-04 22:34:00",
    "requestId": "202305042228001",
    "requestValue": "O2023000001",
    "type": "Order",
    "exportType":"EXCEL"
}
 */
public without sharing class CCM_SNRequestController {
    private static final String TYPE_SN = 'SN';
    private static final String TYPE_ORDER = 'Order';
    private static final String TYPE_CONTAINER = 'Container';
    private static final String TYPE_INVOICE = 'Invoice';
    private static final String TYPE_SHIPMENT = 'Shipment';
    private static final String STATUS_SUBMITED = 'Submited';
    // private static final String ORG_TYPE_PORTAL = 'Portal';
    private static final String ORG_TYPE_CRM = 'CRM';
    private static final String EN = 'en_US';
    private static final String DE = 'de';
    


    /**
     * @description generate sn export id
     */
    @AuraEnabled
    public static Id generateId(){
        SN_Export__c exportObj = new SN_Export__c();
        insert exportObj;
        return exportObj.Id;
    }

    /**
     * @description insert or update sn export record
     */
    @AuraEnabled
    public static String UpsertExportRecord(String exportJsonString){
        SNExportInfo jsonInfo = (SNExportInfo)JSON.deserialize(exportJsonString, SNExportInfo.class);
        SN_Export__c exportObj = new SN_Export__c();
        try {
            if(String.isNotBlank(jsonInfo.Id)){
                exportObj.Id = jsonInfo.Id;
            }
            System.System.debug('页面传过来的申请时间==》'+jsonInfo.requestDate);
            exportObj.Request_Date__c = Datetime.now();
            exportObj.Request_Id__c = exportObj.Request_Date__c.format('yyyyMMddHHmmssSSS');
            exportObj.Request_User__c = UserInfo.getName();
            exportObj.Request_Value__c = jsonInfo.requestValue;
            exportObj.Status__c = STATUS_SUBMITED;
            exportObj.Type__c = jsonInfo.type;
            exportObj.Total_Num__c = '0';
            exportObj.Export_Type__c = jsonInfo.exportType;
            exportObj.Org_Type__c = jsonInfo.orgType;
            // 【Start-Add】:By Haodong 2023-12-7
            exportObj.Customer__c = jsonInfo.customerId;
            // SN_Export__c customerInfo = [Select Customer__c, Customer__r.Name from SN_Export__c];
            // 【End-Add】:By Haodong 2023-12-7
            //String strParam='[{"TYPE":"Order","EXTERNAL_ID":"1033531","REQUEST_ID":"202305121707001"}]';
            exportObj.Usage__c = jsonInfo.usage;
            exportObj.Product_Model__c = jsonInfo.productModel;
            exportObj.Warranty_Claim_Id__c = jsonInfo.warrantyClaimId;
            // 【End-Add】:By Haodong 2023-12-7
            SnRequestObject reqStr = new SnRequestObject();
            reqStr.TYPE = jsonInfo.type;
            reqStr.EXTERNAL_ID = jsonInfo.requestValue;
            System.debug('jsonInfo.type===>'+jsonInfo.type);
            // if(jsonInfo.type.equalsIgnoreCase(TYPE_SN)){
            //     reqStr.REQUEST_ID = TYPE_SN+exportObj.Request_Date__c.format('yyyyMMddHHmmssSSS');
            // }else{
                reqStr.REQUEST_ID = exportObj.Request_Date__c.format('yyyyMMddHHmmssSSS');
            // }
            System.debug('exportObj=====>'+exportObj);
            List<SnRequestObject> lstReq = new List<SnRequestObject>();
            lstReq.add(reqStr);
            List<String> lstParam = new List<String>();
            //Seeburger对json 的key-value顺序由要求，手动拼接jsonbody
            String strParam = '[';
            for(Integer i = 0;i < lstReq.size(); i++){
                strParam += '{"TYPE":"'+lstReq[i].TYPE + '",' + '"EXTERNAL_ID":"' + lstReq[i].EXTERNAL_ID + '",' +'"REQUEST_ID":"' + lstReq[i].REQUEST_ID + '"}';
                if(i < lstReq.size() -1){
                    strParam +=',';
                }
            }
            strParam +=']';
            System.debug('最终Call的参数===》'+strParam);
            CCM_DealSNInfo.GetSNList(strParam);
            upsert exportObj; 

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
        return exportObj.Id;
    }

    /**
     * @description query all export request history
     */
    @AuraEnabled
    public static String getAllExportHistory(Integer pageNumber,Integer allPageSize,String orgType){
        //查询当前用户的申请历史
        System.debug(UserInfo.getName());
        String userId = UserInfo.getUserId();
        User user = getUserInfoById(userId);
        String customerId = user.AccountId;
        List<User> lstUser = new List<User>();
        Set<String> setDealerId = new Set<String>();
        lstUser = [SELECT Id,AccountId FROM User WHERE AccountId =:customerId];
        for(User u : lstUser){
            setDealerId.add(u.Id);
        }
        String userName = UserInfo.getName();
        String strSoql = 'SELECT Id,Total_Num__c,Request_Date__c,Request_Id__c,Request_User__c,Request_Value__c,toLabel(Status__c) statusLable,Status__c,';
        strSoql += ' Type__c,Error_Msg__c,Export_Type__c,toLabel(Type__c) typeLabel';
        strSoql += ' FROM SN_Export__c ';
        // 【Start-Add】:By Haodong 2023-12-5
        strSoql += ' WHERE Usage__c = null AND';
        strSoql += ' Status__c IN (\'Finished\', \'Submited\') ';
        // 【End-Add】:By Haodong 2023-12-5
        if(orgType == 'Portal'){
            // strSoql += ' WHERE Request_User__c = \'' + userName + '\' ';
            strSoql += ' AND CreatedById in :setDealerId ';
        }
        strSoql += ' ORDER BY Request_Date__c DESC limit 49999';
        System.debug('strSoql====>'+strSoql);
        List<SN_Export__c> lstHisroty = new List<SN_Export__c>();
        lstHisroty = Database.query(strSoql);
        System.debug(LoggingLevel.INFO, '*** lstHisroty: ' + lstHisroty);
        List<SNExportInfo> lstCurrentPageHistory = new List<SNExportInfo>();
        Integer totalSize = lstHisroty.size();
        //分页处理
        if (totalSize > 0){
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                SNExportInfo data = new SNExportInfo();
                data.Id = lstHisroty[i].Id;
                data.requestDate = String.valueOf(lstHisroty[i].Request_Date__c);
                data.requestId = lstHisroty[i].Request_Id__c;
                data.requestUser = lstHisroty[i].Request_User__c;
                data.requestValue = lstHisroty[i].Request_Value__c;
                data.status = lstHisroty[i].Status__c;
                System.debug(LoggingLevel.INFO, '*** data.status: ' + data.status);
                data.type = lstHisroty[i].Type__c;
                data.exportType = lstHisroty[i].Export_Type__c;
                data.totalNum = lstHisroty[i].Total_Num__c;
                data.statusLable = String.valueOf(lstHisroty[i].get('statusLable'));
                data.typeLabel = String.valueOf(lstHisroty[i].get('typeLabel'));
                System.debug(LoggingLevel.INFO, '*** statusLable: ' + data.statusLable);
                data.errorMsg = lstHisroty[i].Error_Msg__c;
                lstCurrentPageHistory.add(data);
            }
        }

        Map<String, Object> exportHistoryrMap = new Map<String, Object>();
        exportHistoryrMap.put('exportHistory', lstCurrentPageHistory);
        exportHistoryrMap.put('TotalSize', totalSize);
        System.debug('totalSize==》'+totalSize);
        System.debug('exportHistory==》'+JSON.serialize(lstCurrentPageHistory));

        return JSON.serialize(exportHistoryrMap);
    }

    /**
     * 根据查询条件获取Order No或 Invoice No
     */
    @AuraEnabled
    public static string queryNoByType(String type,String value,String userId,String customerId){
        User userInfo = getUserInfoById(userId);
        String accountId = userInfo.AccountId;
        Boolean IsPortalEnabled = userInfo.IsPortalEnabled;

        String strQueryNoByOrder = 'SELECT Id, Order_Number__c,AccountId FROM Order WHERE Order_Number__c LIKE \'%'+ value +'%\'';
        String strQueryNoByInvoice = 'SELECT Id,Invoice_Number__c ,Customer__c FROM Invoice__c WHERE Invoice_Number__c LIKE \'%'+ value +'%\' ';
        String strQueryNoByShipment = 'SELECT Id, Ship_OracleID__c FROM Shipment__c WHERE Ship_OracleID__c LIKE \'%'+ value +'%\' ';
        //判断当前用户是否Portal用户，如果是则过滤所属公司数据
        if(IsPortalEnabled){
            strQueryNoByInvoice += ' And  Customer__c = :accountId ';
            strQueryNoByOrder += ' And  AccountId = :accountId ';
            strQueryNoByShipment += ' AND Order__r.AccountId = accountId';
        }else {
            // 【Start-Add】:By Haodong 2023-12-7
            if (customerId != null && customerId.length() > 0) {
                strQueryNoByInvoice += ' And  Customer__c = :customerId ';
                strQueryNoByOrder += ' And  AccountId = :customerId ';
                strQueryNoByShipment += ' AND Order__r.AccountId = accountId';
            }
            // 【End-Add】:By Haodong 2023-12-7
        }

        strQueryNoByInvoice += ' ORDER BY CreatedDate DESC limit 50 ';
        strQueryNoByOrder += ' ORDER BY CreatedDate DESC limit 50 ';
        strQueryNoByShipment += ' ORDER BY CreatedDate DESC limit 50';

        Map<String,Object> repsonseMap = new Map<String,Object>();
        List<SelectOptions> lstSelect = new List<SelectOptions>();
        try {
            if(type.equals(TYPE_ORDER)){
                List<Order> lstOrder =  (List<Order>)Database.query(strQueryNoByOrder);
                if(lstOrder.size() > 0){
                    for(Order o : lstOrder){
                        SelectOptions s = new SelectOptions();
                        s.Id = o.Id;
                        s.Name = o.Order_Number__c; 
                        lstSelect.add(s);
                    }
                }
            }else if(type.equals(TYPE_INVOICE)){
                List<Invoice__c> lstInvoice =  (List<Invoice__c>)Database.query(strQueryNoByInvoice);
                if(lstInvoice.size() > 0){
                    for(Invoice__c i : lstInvoice){
                        SelectOptions s = new SelectOptions();
                        s.Id = i.Id;
                        s.Name = i.Invoice_Number__c; 
                        lstSelect.add(s);
                    }
                }
            }
            else if(type.equals(TYPE_SHIPMENT)){
                List<Shipment__c> lstShipment =  (List<Shipment__c>)Database.query(strQueryNoByShipment);
                if(lstShipment.size() > 0){
                    for(Shipment__c s : lstShipment){
                        SelectOptions so = new SelectOptions();
                        so.Id = s.Id;
                        so.Name = s.Ship_OracleID__c; 
                        lstSelect.add(so);
                    }
                }
            }
            return JSON.serialize(lstSelect);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }


    // /**
    //  * 根据当前批次号获取SN查询请求返回的临时数据
    //  */
    // @AuraEnabled
    // public static string getSnTempByRequestId(String requestId){
    //     List<SNTempInfo> lstTempInfo = new List<SNTempInfo>();
    //     try {
    //         System.debug('requestId===>'+requestId);
    //         if(String.isNotBlank(requestId)){
    //             List<SN_Temp__c> lstTmp = [
    //                 SELECT 
    //                     Id,Batch_No__c,Container_No__c,Customer_Code__c,Invoice_No__c,
    //                     Model_No__c,Order_Line_No__c,Order_No__c,Shipping_Order_No__c,SN__c ,Union_Batch__c,
    //                     Order_Date__c,Sub_Inventory_Code__c,QTY__c,Shipment_Date__c,Bill_To_Name__c,Ship_To_Name__c,Ship_To_Country__c,
    //                     Order_Line_OracleId__c,Order_OracleId__c 
    //                 FROM 
    //                     SN_Temp__c 
    //                 WHERE 
    //                     Union_Batch__c =:requestId
    //             ];
    //             System.debug('lstTmp.size()===>'+lstTmp.size());
    //             if(lstTmp.size() > 0){
    //                 //获取该批次所有订单行号
    //                 Set<String> setOrderItemExternalId = new Set<String>();
    //                 //获取该批次所有订单号
    //                 Set<String> setOrderOracleId = new Set<String>();
    //                 //获取该批次所有运单号
    //                 Set<String> setShippingOrderNo = new Set<String>();
    //                 //获取该批次所有model号
    //                 Set<String> setModelNo = new Set<String>();
    //                 //获取Bill To Code
    //                 Set<String> setBillToCode = new Set<String>();
    //                 Map<String,Order> mapOrderNoToOrderInfo= new Map<String,Order>();
    //                 Map<String,Order_Item__c> mapExternalToOrderLine= new Map<String,Order_Item__c>();
    //                 Map<String,Product2> mapModelToProduct= new Map<String,Product2>();
    //                 Map<String,Shipment__c> mapShipNoToShipInfo= new Map<String,Shipment__c>();
    //                 for(SN_Temp__c t : lstTmp){
    //                     setOrderItemExternalId.add(t.Order_Line_OracleId__c);
    //                     setOrderOracleId.add(t.Order_OracleId__c);
    //                     setShippingOrderNo.add(t.Shipping_Order_No__c);
    //                     setModelNo.add(t.Model_No__c);
    //                 }
    //                 //获取相关产品信息
    //                 List<Product2> lstProduct = [SELECT Id,Order_Model__c,Item_Description_DE__c , Item_Description_EN__c FROM Product2 WHERE Order_Model__c in :setModelNo];
    //                 //获取相关订单行信息
    //                 List<Order_Item__c> lstOrderLine = [SELECT Id,OrderLine_OracleID__c,Order__r.Order_Date__c,Product__r.Item_Description_DE__c,Order_Quantity__c,Sub_inventory__c,External_Id__c FROM Order_Item__c WHERE OrderLine_OracleID__c in :setOrderItemExternalId];
    //                 //获取相关运单信息
    //                 List<Shipment__c> lstShipment = [SELECT Id,Delivery_Date__c,Customer__c,Receipt_Country__c,Ship_OracleID__c FROM Shipment__c WHERE Ship_OracleID__c in:setShippingOrderNo];

    //                 //获取相关订单信息
    //                 List<Order> lstOrder= [SELECT Id,BillTo_Text__c,ShipTo_Text__c,Order_OracleID__c FROM Order Where Order_OracleID__c in :setOrderOracleId];
    //                 //获取地址信息
    //                 List<Account_Address__c>  lstAddress = new List<Account_Address__c>();
    //                 lstAddress = [SELECT Id,Customer_Line_Oracle_ID__c ,Company_name_1__c,Country__c from Account_Address__c];
    //                 Map<String,Account_Address__c> mapCodeToAddressInfo= new Map<String,Account_Address__c>();

    //                 for(Order_Item__c oitem : lstOrderLine){
    //                     mapExternalToOrderLine.put(oitem.OrderLine_OracleID__c,oitem);
    //                 }
    //                 for(Product2 p : lstProduct){
    //                     mapModelToProduct.put(p.Order_Model__c,p);
    //                 }
    //                 for(Shipment__c smt : lstShipment){
    //                     mapShipNoToShipInfo.put(smt.Ship_OracleID__c,smt);
    //                 }
    //                 for(Order o : lstOrder){
    //                     mapOrderNoToOrderInfo.put(o.Order_OracleID__c,o);
    //                 }
    //                 for(Account_Address__c a : lstAddress){
    //                     if(a.Customer_Line_Oracle_ID__c != null){
    //                         mapCodeToAddressInfo.put(a.Customer_Line_Oracle_ID__c,a);
    //                     }
                        
    //                 }
    //                 /** */
    //                 // 【Start-Add】:By Haodong 2023-12-14
    //                 // 存放kit以及kit对应product的Map
    //                 System.debug('kim.setModelNo---------->'+setModelNo);
    //                 Map<String,List<Kit_Item__c>> proIdList = new Map<String,List<Kit_Item__c>>();
    //                 List<Kit_Item__c> lstPro = new List<Kit_Item__c>();
    //                 Set<String> setKitNo = new Set<String>();
    //                 List<Kit_Item__c> lstKitItem = new List<Kit_Item__c>();
    //                 // 获取Kit的recordTypeId
    //                 String recordTypeIdKit = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('Kits_and_Products').getRecordTypeId();

    //                 lstKitItem = [
    //                     SELECT
    //                         Id,Name,Kit__c,Kit__r.id,Kit__r.Order_Model__c,VK_Product__c,VK_Product__r.id,VK_Product__r.Order_Model__c,Quantity__c
    //                     FROM
    //                         Kit_Item__c 
    //                     WHERE
    //                         Kit__c != null
    //                     AND 
    //                         Kit__r.Order_Model__c IN :setModelNo And RecordTypeId = :recordTypeIdKit 
    //                 ];
    //                 System.debug('kim------------>' + lstKitItem);
    //                 // 存储kit和product的对应关系
    //                 for(Kit_Item__c KI : lstKitItem) {
    //                     // 不为空，说明Kit存在
    //                     if(proIdList.containsKey(KI.Kit__c)) {
    //                         proIdList.get(KI.Kit__r.Order_Model__c).add(KI);
    //                     } else {
    //                         lstPro.add(KI);
    //                         setKitNo.add(KI.Kit__r.Order_Model__c);
    //                         proIdList.put(KI.Kit__r.Order_Model__c, lstPro);
    //                     }
    //                 }
                    
    //                 // 【End-Add】:By Haodong 2023-12-14
    //                 for(SN_Temp__c t : lstTmp){
    //                     SNTempInfo temp =  new SNTempInfo();
    //                     String orderLineExternalId = t.Order_Line_OracleId__c;
    //                     temp.Id = t.Id;
    //                     // temp.batchNo = t.Batch_No__c;
    //                     temp.containerNo = t.Container_No__c;
    //                     temp.customerCode = t.Customer_Code__c;
    //                     temp.invoiceNo = t.Invoice_No__c;
    //                     temp.modelNo = t.Model_No__c;
    //                     temp.orderLineNo = t.Order_Line_No__c;
    //                     temp.salesOrderNo = t.Order_No__c;
    //                     temp.shippingOrderNo = t.Shipping_Order_No__c;
    //                     temp.SN = t.SN__c;
    //                     //订单行信息
    //                     if(mapExternalToOrderLine.containsKey(orderLineExternalId)){
    //                         temp.orderedDate = String.valueOf(mapExternalToOrderLine.get(orderLineExternalId).Order__r.Order_Date__c);
    //                         if(String.isEmpty(temp.orderedDate)){
    //                             temp.orderedDate = String.valueOf(t.Order_Date__c);
    //                         }
    //                         // temp.subInventory = mapExternalToOrderLine.get(orderLineExternalId).Sub_inventory__c;
    //                         // if(String.isEmpty(temp.subInventory)){
    //                         //     temp.subInventory = t.Sub_Inventory_Code__c;
    //                         // }
    //                         temp.orderedQuantity = String.valueOf(mapExternalToOrderLine.get(orderLineExternalId).Order_Quantity__c);
    //                         if(String.isEmpty(temp.orderedQuantity)){
    //                             temp.orderedQuantity = String.valueOf(t.QTY__c);
    //                         }
    //                     }else{
    //                         temp.orderedDate = String.valueOf(t.Order_Date__c);
    //                         // temp.subInventory = t.Sub_Inventory_Code__c;
    //                         temp.orderedQuantity = String.valueOf(t.QTY__c);
    //                     }
                    
    //                     //产品信息
    //                     if(mapModelToProduct.containsKey(t.Model_No__c)){
    //                         if(UserInfo.getLanguage().equals(EN)){
    //                             temp.productShortDescription = mapModelToProduct.get(t.Model_No__c).Item_Description_EN__c;
    //                         }else if(UserInfo.getLanguage().equals(DE)){
    //                             temp.productShortDescription = mapModelToProduct.get(t.Model_No__c).Item_Description_DE__c;
    //                         }
                            
    //                     }
    //                     //运单信息
    //                     if(mapShipNoToShipInfo.containsKey(t.Shipping_Order_No__c)){
    //                         temp.shipmentDate = String.valueOf(mapShipNoToShipInfo.get(t.Shipping_Order_No__c).Delivery_Date__c);
    //                         if(String.isEmpty(temp.shipmentDate)){
    //                             temp.shipmentDate = String.valueOf(t.Shipment_Date__c);
    //                         }
    //                     }else{
    //                         temp.shipmentDate = String.valueOf(t.Shipment_Date__c);
    //                     }
                        
    //                     temp.deliveryNoteNo =t.Shipping_Order_No__c;
    //                     //Order信息
    //                     if(mapOrderNoToOrderInfo.containsKey(t.Order_OracleId__c)){
    //                         Order order = new Order();
    //                         order  = mapOrderNoToOrderInfo.get(t.Order_OracleId__c);
    //                         String billToCode = order.BillTo_Text__c;
    //                         String shipTocode = order.ShipTo_Text__c;
    //                         if(mapCodeToAddressInfo.containsKey(billToCode)){
    //                             temp.billToName = mapCodeToAddressInfo.get(billToCode).Company_name_1__c;
    //                         }
    //                         if(String.isEmpty(temp.billToName)){
    //                             temp.billToName = t.Bill_To_Name__c;
    //                         }
    //                         if(mapCodeToAddressInfo.containsKey(shipTocode)){
    //                             temp.shipToName = mapCodeToAddressInfo.get(shipTocode).Company_name_1__c;
    //                             // temp.shipToCountry = mapCodeToAddressInfo.get(shipTocode).Country__c;
    //                         }
    //                         if(String.isEmpty(temp.shipToName)){
    //                             temp.shipToName = t.Ship_To_Name__c;
    //                         }
    //                         // if(String.isEmpty(temp.shipToCountry)){
    //                         //     temp.shipToCountry = t.Ship_To_Country__c;
    //                         // }
    //                     }else{
    //                         temp.billToName = t.Bill_To_Name__c;
    //                         temp.shipToName = t.Ship_To_Name__c;
    //                         // temp.shipToCountry = t.Ship_To_Country__c;
    //                     }
    //                     lstTempInfo.add(temp);
    //                     //【Start-Add】:By Haodong 2023-12-14
    //                     System.debug('setKitNo------------->'+setKitNo);
    //                     if(temp.modelNo.equals('CHT2001E')){
    //                         System.debug('temp.modelNo------------->'+temp.modelNo);
    //                     }
                        
    //                     // if(temp.modelNo属于kit){
    //                     //     for(resPro Product2 : 当前kit下的productList){
    //                     //         新的temp new SnTempInfo;
    //                     //         新的temp = temp;
    //                     //         ModelNo
    //                     //         Sn = ''
    //                     //         lstTempInfo.add(新的temp);
    //                     //     }                
    //                     // }
    //                     // 查询到该temp是否属于kit
                        
    //                     // 不为空，说明属于Kit
    //                     if(setKitNo.contains(temp.modelNo)) {
    //                         // 拿到该kit下的product
    //                         List<Kit_Item__c> lstItem = new List<Kit_Item__c>();
    //                         lstItem =  proIdList.get(temp.modelNo);
    //                         System.debug('Kim.product in kit----------->'+lstItem);
    //                         for (Kit_Item__c item : lstItem) {
    //                             SNTempInfo newTemp = new SNTempInfo();
    //                             newTemp = (SNTempInfo)JSON.deserialize(JSON.serialize(temp), SNTempInfo.class);
    //                             newTemp.Id = item.VK_Product__c;
    //                             newTemp.modelNo = item.VK_Product__r.Order_Model__c;
    //                             newTemp.SN = '';
    //                             lstTempInfo.add(newTemp);
    //                             System.debug('Kim.newTemp------------>'+newTemp);
    //                         }
    //                     }
    //                     for(SNTempInfo tf : lstTempInfo) {
    //                         System.debug('Kim.all------------------->'+tf);
    //                     }
                        
    //                     // 【End-Add】:By Haodong 2023-12-14
    //                 }
    //             }
    //         }
    //     return JSON.serialize(lstTempInfo);
    //     } catch (Exception e) {
    //         throw new AuraHandledException(e.getMessage());
    //     }
    // }

    /**
     * 根据当前批次号获取SN查询请求返回的临时数据
     */
    @AuraEnabled
    public static string getSnTempByRequestId(String requestId){
        List<SNTempInfo> lstTempInfo = new List<SNTempInfo>();
        try {
            System.debug('requestId===>'+requestId);
            if(String.isNotBlank(requestId)){
                List<SN_Temp__c> lstTmp = [
                    SELECT 
                        Id,Batch_No__c,Container_No__c,Customer_Code__c,Invoice_No__c,
                        Model_No__c,Bare_Tool_Model__c,Order_Line_No__c,Order_No__c,Shipping_Order_No__c,SN__c ,Union_Batch__c,
                        Order_Date__c,Sub_Inventory_Code__c,QTY__c,Shipment_Date__c,Bill_To_Name__c,Ship_To_Name__c,Ship_To_Country__c,
                        Order_Line_OracleId__c,Order_OracleId__c,Bom_SN__c 
                    FROM 
                        SN_Temp__c 
                    WHERE 
                        Union_Batch__c =:requestId
                ];
                System.debug('lstTmp.size()===>'+lstTmp.size());
                if(lstTmp.size() > 0){
                    //获取该批次所有订单行号
                    Set<String> setOrderItemExternalId = new Set<String>();
                    //获取该批次所有订单号
                    Set<String> setOrderOracleId = new Set<String>();
                    //获取该批次所有运单号
                    Set<String> setShippingOrderNo = new Set<String>();
                    //获取该批次所有model号
                    Set<String> setModelNo = new Set<String>();
                    //获取Bill To Code
                    Set<String> setBillToCode = new Set<String>();
                    Map<String,Order> mapOrderNoToOrderInfo= new Map<String,Order>();
                    Map<String,Order_Item__c> mapExternalToOrderLine= new Map<String,Order_Item__c>();
                    Map<String,Product2> mapModelToProduct= new Map<String,Product2>();
                    Map<String,Shipment__c> mapShipNoToShipInfo= new Map<String,Shipment__c>();
                    for(SN_Temp__c t : lstTmp){
                        setOrderItemExternalId.add(t.Order_Line_OracleId__c);
                        setOrderOracleId.add(t.Order_OracleId__c);
                        setShippingOrderNo.add(t.Shipping_Order_No__c);
                        if(String.isNotBlank(t.Bare_Tool_Model__c)) {
                            setModelNo.add(t.Bare_Tool_Model__c);
                        }
                        setModelNo.add(t.Model_No__c);
                    }
                    //获取相关产品信息
                    List<Product2> lstProduct = [SELECT Id,Order_Model__c,Item_Description_DE__c , Item_Description_EN__c FROM Product2 WHERE Order_Model__c in :setModelNo];
                    //获取相关订单行信息
                    List<Order_Item__c> lstOrderLine = [SELECT Id,OrderLine_OracleID__c,Order__r.Order_Date__c,Product__r.Item_Description_DE__c,Order_Quantity__c,Sub_inventory__c,External_Id__c FROM Order_Item__c WHERE OrderLine_OracleID__c in :setOrderItemExternalId];
                    //获取相关运单信息
                    List<Shipment__c> lstShipment = [SELECT Id,Delivery_Date__c,Customer__c,Receipt_Country__c,Ship_OracleID__c FROM Shipment__c WHERE Ship_OracleID__c in:setShippingOrderNo];

                    //获取相关订单信息
                    List<Order> lstOrder= [SELECT Id,BillTo_Text__c,ShipTo_Text__c,Order_OracleID__c FROM Order Where Order_OracleID__c in :setOrderOracleId];
                    //获取地址信息
                    List<Account_Address__c>  lstAddress = new List<Account_Address__c>();
                    lstAddress = [SELECT Id,Customer_Line_Oracle_ID__c ,Company_name_1__c,Country__c from Account_Address__c];
                    Map<String,Account_Address__c> mapCodeToAddressInfo= new Map<String,Account_Address__c>();

                    for(Order_Item__c oitem : lstOrderLine){
                        mapExternalToOrderLine.put(oitem.OrderLine_OracleID__c,oitem);
                    }
                    for(Product2 p : lstProduct){
                        mapModelToProduct.put(p.Order_Model__c,p);
                    }
                    for(Shipment__c smt : lstShipment){
                        mapShipNoToShipInfo.put(smt.Ship_OracleID__c,smt);
                    }
                    for(Order o : lstOrder){
                        mapOrderNoToOrderInfo.put(o.Order_OracleID__c,o);
                    }
                    for(Account_Address__c a : lstAddress){
                        if(a.Customer_Line_Oracle_ID__c != null){
                            mapCodeToAddressInfo.put(a.Customer_Line_Oracle_ID__c,a);
                        }
                        
                    }

                    Boolean isDE = UserInfo.getLanguage().equals(DE);
                    
                    for(SN_Temp__c t : lstTmp){
                        SNTempInfo temp =  new SNTempInfo();
                        String orderLineExternalId = t.Order_Line_OracleId__c;
                        temp.Id = t.Id;
                        // temp.batchNo = t.Batch_No__c;
                        temp.containerNo = t.Container_No__c;
                        temp.customerCode = t.Customer_Code__c;
                        temp.invoiceNo = t.Invoice_No__c;
                        temp.modelNo = t.Model_No__c;
                        temp.bareToolModel = t.Bare_Tool_Model__c;
                        temp.orderLineNo = t.Order_Line_No__c;
                        temp.salesOrderNo = t.Order_No__c;
                        temp.shippingOrderNo = t.Shipping_Order_No__c;
                        temp.SN = t.SN__c;
                        temp.bomSN = t.Bom_SN__c;
                        //订单行信息
                        if(mapExternalToOrderLine.containsKey(orderLineExternalId)){
                            temp.orderedDate = String.valueOf(mapExternalToOrderLine.get(orderLineExternalId).Order__r.Order_Date__c);
                            if(String.isEmpty(temp.orderedDate)){
                                temp.orderedDate = String.valueOf(t.Order_Date__c);
                            }
                            // temp.subInventory = mapExternalToOrderLine.get(orderLineExternalId).Sub_inventory__c;
                            // if(String.isEmpty(temp.subInventory)){
                            //     temp.subInventory = t.Sub_Inventory_Code__c;
                            // }
                            temp.orderedQuantity = String.valueOf(mapExternalToOrderLine.get(orderLineExternalId).Order_Quantity__c);
                            if(String.isEmpty(temp.orderedQuantity)){
                                temp.orderedQuantity = String.valueOf(t.QTY__c);
                            }
                        }else{
                            temp.orderedDate = String.valueOf(t.Order_Date__c);
                            // temp.subInventory = t.Sub_Inventory_Code__c;
                            temp.orderedQuantity = String.valueOf(t.QTY__c);
                        }
                    
                        //产品信息
                        if(String.isNotBlank(t.Bare_Tool_Model__c)) {
                            if(mapModelToProduct.containsKey(t.Bare_Tool_Model__c)){ 
                                temp.productShortDescription = mapModelToProduct.get(t.Bare_Tool_Model__c).Item_Description_EN__c;
                                if(isDE) {
                                    temp.productShortDescription = mapModelToProduct.get(t.Bare_Tool_Model__c).Item_Description_DE__c;
                                }
                            }
                        }
                        else {
                            if(mapModelToProduct.containsKey(t.Model_No__c)){
                                temp.productShortDescription = mapModelToProduct.get(t.Model_No__c).Item_Description_EN__c;
                                if(isDE) {
                                    temp.productShortDescription = mapModelToProduct.get(t.Model_No__c).Item_Description_DE__c;
                                }
                            }
                        }
                        
                        //运单信息
                        if(mapShipNoToShipInfo.containsKey(t.Shipping_Order_No__c)){
                            temp.shipmentDate = String.valueOf(mapShipNoToShipInfo.get(t.Shipping_Order_No__c).Delivery_Date__c);
                            if(String.isEmpty(temp.shipmentDate)){
                                temp.shipmentDate = String.valueOf(t.Shipment_Date__c);
                            }
                        }else{
                            temp.shipmentDate = String.valueOf(t.Shipment_Date__c);
                        }
                        
                        temp.deliveryNoteNo =t.Shipping_Order_No__c;
                        //Order信息
                        if(mapOrderNoToOrderInfo.containsKey(t.Order_OracleId__c)){
                            Order order = new Order();
                            order  = mapOrderNoToOrderInfo.get(t.Order_OracleId__c);
                            String billToCode = order.BillTo_Text__c;
                            String shipTocode = order.ShipTo_Text__c;
                            if(mapCodeToAddressInfo.containsKey(billToCode)){
                                temp.billToName = mapCodeToAddressInfo.get(billToCode).Company_name_1__c;
                            }
                            if(String.isEmpty(temp.billToName)){
                                temp.billToName = t.Bill_To_Name__c;
                            }
                            if(mapCodeToAddressInfo.containsKey(shipTocode)){
                                temp.shipToName = mapCodeToAddressInfo.get(shipTocode).Company_name_1__c;
                                // temp.shipToCountry = mapCodeToAddressInfo.get(shipTocode).Country__c;
                            }
                            if(String.isEmpty(temp.shipToName)){
                                temp.shipToName = t.Ship_To_Name__c;
                            }
                            // if(String.isEmpty(temp.shipToCountry)){
                            //     temp.shipToCountry = t.Ship_To_Country__c;
                            // }
                        }else{
                            temp.billToName = t.Bill_To_Name__c;
                            temp.shipToName = t.Ship_To_Name__c;
                            // temp.shipToCountry = t.Ship_To_Country__c;
                        }
                        lstTempInfo.add(temp);
                    }
                }
            }

            List<SNTempInfo> sortedSNTempInfoList = constructSN(lstTempInfo);

            return JSON.serialize(sortedSNTempInfoList);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    public static List<SNTempInfo> constructSN(List<SNTempInfo> lstTempInfo) {
        List<SNTempInfo> sortedInfos = new List<SNTempInfo>();
        Map<String, List<SNTempInfo>> snTempInfoMap = new Map<String, List<SNTempInfo>>();
        for(SNTempInfo tempInfo : lstTempInfo) {
            if(!snTempInfoMap.containsKey(tempInfo.SN)) {
                snTempInfoMap.put(tempInfo.SN, new List<SNTempInfo>());
            }
            snTempInfoMap.get(tempInfo.SN).add(tempInfo);
        }

        for(String snKey : snTempInfoMap.keySet()) {
            List<SNTempInfo> snTempInfoItems = snTempInfoMap.get(snKey);
            if(snTempInfoItems.size() > 1) {
                // generate new row for kit
                // sort other rows -- same sn(SN, bomSN) first
                List<SNTempInfo> sortedSNTempInfoItems = new List<SNTempInfo>();
                for(SNTempInfo snTempInfoItem : snTempInfoItems) {
                    if(snTempInfoItem.SN == snTempInfoItem.bomSN) {
                        if(sortedSNTempInfoItems.isEmpty()) {
                            sortedSNTempInfoItems.add(snTempInfoItem);
                        }
                        else {
                            sortedSNTempInfoItems.add(0, snTempInfoItem);
                        }
                    }
                    else {
                        sortedSNTempInfoItems.add(snTempInfoItem);
                    }
                }

                SNTempInfo kitInfo = sortedSNTempInfoItems[0].clone();
                kitInfo.bareToolModel = '';
                kitInfo.SN = kitInfo.bomSN;

                for(SNTempInfo sortedSNTempInfoItem : sortedSNTempInfoItems) {
                    sortedSNTempInfoItem.modelNo = '';
                    sortedSNTempInfoItem.SN = sortedSNTempInfoItem.bomSN;
                }
                sortedSNTempInfoItems.add(0, kitInfo);
                snTempInfoMap.put(snKey, sortedSNTempInfoItems);
            }
        }

        for(String snKey : snTempInfoMap.keySet()) {
            sortedInfos.addAll(snTempInfoMap.get(snKey));
        }
        return sortedInfos;
    }

    /**
     * 根据当前用户Id获取用户信息
     */
    public static User getUserInfoById(String userId){
       
        User userInfo = new User();
        userInfo = [Select Id,AccountId,IsPortalEnabled From User Where Id = :userId limit 1];
        
        return userInfo;
    }

    public class SelectOptions{
        public String Id;

        public String Name;

    }
    
    public CCM_SNRequestController() {}

    /**
     * @description Single SN Export information
     */
    public class SNExportInfo{
        @AuraEnabled public String Id{get;set;} 
        @AuraEnabled public String requestDate{get;set;} 
        @AuraEnabled public String requestValue{get;set;} 
        @AuraEnabled public String requestId{get;set;} 
        @AuraEnabled public String requestUser{get;set;} 
        @AuraEnabled public String status{get;set;} 
        @AuraEnabled public String statusLable{get;set;}
        @AuraEnabled public String type{get;set;} 
        @AuraEnabled public String typeLabel{get;set;} 
        @AuraEnabled public String exportType{get;set;} 
        @AuraEnabled public String totalNum{get;set;} 
        @AuraEnabled public String orgType{get;set;} 
        @AuraEnabled public String errorMsg{get;set;}
        @AuraEnabled public String customerId{get;set;}
        @AuraEnabled public String usage {get;set;}
        @AuraEnabled public String productModel {get;set;}
        @AuraENabled public String warrantyClaimId {get;set;}
    }

    /**
     * @description Single SN Temp information
     */
    public class SNTempInfo {
        @AuraEnabled public String Id{get;set;} 
        @AuraEnabled public String orderedDate{get;set;} 
        @AuraEnabled public String shipmentDate{get;set;} 
        @AuraEnabled public String modelNo{get;set;} 
        @AuraEnabled public String bareToolModel{get;set;}
        @AuraEnabled public String productShortDescription{get;set;} 
        @AuraEnabled public String customerCode{get;set;} 
        @AuraEnabled public String billToName{get;set;} 
        @AuraEnabled public String shipToName{get;set;} 
        // @AuraEnabled public String shipToCountry{get;set;} 
        @AuraEnabled public String salesOrderNo{get;set;} 
        @AuraEnabled public String deliveryNoteNo{get;set;} 
        // @AuraEnabled public String subInventory{get;set;} 
        @AuraEnabled public String orderedQuantity{get;set;} 
        @AuraEnabled public String invoiceNo{get;set;} 
        @AuraEnabled public String containerNo{get;set;}
        // @AuraEnabled public String batchNo{get;set;} 
        @AuraEnabled public String SN{get;set;} 
        @AuraEnabled public String orderLineNo{get;set;}
        @AuraEnabled public String shippingOrderNo{get;set;}
        @AuraEnabled public String bomSN{get;set;}
    }
}