<aura:component controller="CCM_MassUpload" extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout" access="global">
    
    <!-- 表格参数 -->
    <aura:attribute name="tableData" type="List" default="[]"/>
    <aura:attribute name="columns" type="List"/>
    <aura:attribute name="exportData" type="List" default="[]"/>
    <aura:attribute name="parseData" type="List" default="[]"/>
    <aura:attribute name="isError" type="Boolean" default="false"/>
    <aura:attribute name="receiptUrl" type="Map" default="{}"/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="resetFlag" type="Boolean" default="true"/>
    <aura:attribute name="errortip" type="Boolean" default="false" />
    <aura:attribute name="errorList" type="List" default="[]" />
    <aura:attribute name="selectList" type="List" default="[]"/>

    
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <div class="slds-grid slds-grid_align-space">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed loading-wrap' : 'slds-hide' }"/>
        <div style="padding: 0 2.8%;">
            <section class="slds-p-around_x-small">
                <!-- personal info -->
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate" title="{!$Label.c.CCM_UploadWarrantyRegistrationPage}">
                                        <span><strong>{!$Label.c.CCM_UploadWarrantyRegistrationPage}</strong></span>
                                    </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-grid slds-wrap slds-align_absolute-center width80 company-form-wrap">
                            <div class="slds-grid slds-wrap select-wrap">
                                <aura:if isTrue="{!v.errortip}">
                                    <div class="error-tips-wrap">
                                        <div class="error-tips">
                                            <aura:iteration items="{!v.errorList}" var="item">
                                                <p>{!item}</p>
                                            </aura:iteration>
                                        </div>
                                    </div>
                                </aura:if>
                                <div class="slds-grid slds-wrap slds-align_absolute-center select-btn-wrap">
                                    <div class="flex-center-wrap template-btn-wrap">
                                        <!-- 导出模板 -->
                                        <div class="select-template-wrap">
                                            <lightning:buttonMenu label="{!$Label.c.CCM_DownloadTemplate}" variant="border-inverse" onselect="{!c.handleSelect }">
                                                <lightning:menuItem label="{!$Label.c.CCM_WarrantyRegistrationMassUpload}" value="warrantyRegistrationMassUpload"/>
                                            </lightning:buttonMenu>
                                        </div>
                                        <!-- 导入模板 -->
                                        <aura:if isTrue="{!v.resetFlag}">
                                            <c:ccmExcelTools ontabledata="{!c.getParseData}" type="parse" btnText="{!$Label.c.CCM_UploadOrderList}" btnType="brand"></c:ccmExcelTools>
                                        </aura:if>
                                    </div>
                                    <div class="apply-btn-wrap">
                                        <div class="fileName-wrap" style="margin-top: 5px;">
                                            <lightning:fileUpload 
                                                class="upload-wrap" 
                                                label="Upload Your Receipt:"
                                                name="fileUploader"
                                                multiple="false"
                                                accept="['.png', '.jpg', '.jpeg', '.pdf']"
                                                recordId="{!v.recordId}"
                                                onuploadfinished="{!c.handleUploadFinished}" 
                                            />
                                            <aura:if isTrue="{!v.receiptUrl.fileName}">
                                                <p class="uploadFinished">
                                                    <span class="fileName">{!v.receiptUrl.fileName}</span>
                                                    <a class="delete" onclick="{!c.deleteReceipt}">Delete</a>
                                                </p>
                                            </aura:if>
                                        </div>
                                        <!-- <lightning:input class="upload-wrap" name="" type="file" label="Upload Invoice:" multiple="true" accept="image/png, .pdf" onchange="{!c.handleFilesChange}"/> -->
                                        <lightning:button class="applt-btn" variant="brand" label="{!$Label.c.CCM_ApplyInvoiceForSelectedLine}" title="{!$Label.c.CCM_ApplyInvoiceForSelectedLine}" onclick="{!c.applyInvoice}"/>
                                    </div>
                                </div>
                            </div>
                            <!-- 表格数据 -->
                            <div class="table-wrap">
                                <lightning:datatable aura:id="datas" class="table simple-table"
                                    columns="{!v.columns}"
                                    data="{!v.tableData}"
                                    keyField="Id"
                                    onrowselection="{!c.tableSelected}"
                                    onrowaction="{!c.handleRowAction}"
                                />
                            </div>
                        </div>
                    </div>
                </article>
                <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
                    <lightning:button class="" variant="brand"  label="{!$Label.c.CCM_Submit}" title="{!$Label.c.CCM_Submit}" onclick="{!c.onClickSubmit}"/>
                    <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.onClickCancel}"/>
                </div>
            </section>
        </div>
    </div>
</aura:component>