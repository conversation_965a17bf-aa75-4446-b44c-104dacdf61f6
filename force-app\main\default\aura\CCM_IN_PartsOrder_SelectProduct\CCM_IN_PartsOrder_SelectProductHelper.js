({
    // 获取product detail info
    getProductDetailInfo: function(component, productInfo, index){
        let self = this;
        if (!productInfo.Id) {
            return;
        }
        var action = component.get("c.GetProductListInfo");
        component.set('v.isBusy', true);
        action.setParams({
            ProductId: productInfo.Id,
            UnitsPerPallet: productInfo.Units_Per_Pallet_EA__c,
            CustomerId: component.get('v.customerId'),
            UnitsMasterCarton: productInfo.Units_Per_Master_Carton_EA__c,
            InnerBox: productInfo.Units_Per_Inner_BOX_EA__c,
            RecordTypeName: productInfo.RecordType.Name,
            WareHouse: component.get('v.warehouse'),
            PricingDate: component.get('v.pricingDate'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                if(result){
                    let productList = component.get('v.productList');
                    let newProductList = JSON.parse(JSON.stringify(productList));
                    if(newProductList[index] && newProductList.filter(item => item.key == newProductList[index].key).length > 1){
                        newProductList = newProductList.filter(item => item.key != newProductList[index].key || (item.key == newProductList[index].key && item.type == "MainProduct"))
                    }
                    // 通过record type展示kit类型的红绿灯 add haibo
                    newProductList[index].productRecordType = productInfo.RecordType.Name;
                    newProductList[index].lastBlankLine = false;
                    if (result.filteredProducts.length) {
                        newProductList[index].isKit = true;
                        newProductList[index].filteredProducts = [];
                        // newProductList[index].filteredProducts = result.filteredProducts;
                        // tool字段处理
                        result.filteredProducts.forEach((toolItem, toolIndex)=>{
                            // 判断红绿灯
                            let toolQty = 0;
                            switch (toolItem.InvotoryInfo__c) {
                                case 'green Light':
                                    toolItem.inventory = 'Green';
                                    if (toolQty <= toolItem.MaxGreenLight__c) {
                                        toolItem.inventory = 'Green';
                                    } else if (toolQty > toolItem.MaxGreenLight__c && toolQty <= toolItem.MaxYellowLight__c) {
                                        toolItem.inventory = 'Yellow';
                                    } else {
                                        toolItem.inventory = 'Red';
                                    };
                                    break;
                                case 'yellow Light':
                                    toolItem.inventory = 'Yellow';
                                    break;
                                case 'red Light':
                                    toolItem.inventory = 'Red';
                                    break;
                                default:
                                    toolItem.inventory = 'Green';
                                    break;
                            }
                            newProductList[index].filteredProducts.push({
                                id: toolItem.Id,
                                productDescription: toolItem.Item_Description_EN__c,
                                model: toolItem.Order_Model__c,
                                requestDate: component.get('v.expectedDeliveryDate'),
                                qty: (toolItem.Units_Per_Master_Carton_EA__c * result.Qty),
                                // 缺失
                                unitPerPallet: toolQty,
                                inventory: toolItem.inventory,
                                listPrice: 0,
                                discount: 0,
                                unitNetPrice: 0,
                                totalNetPrice: 0,
                                scale: toolItem.Units_Per_Master_Carton_EA__c,
                                invotoryInfo: toolItem.InvotoryInfo__c,
                                maxGreenLight: toolItem.MaxGreenLight__c,
                                maxYellowLight: toolItem.MaxYellowLight__c,
                            })
                        })
                    } else {
                        newProductList[index].isKit = false;
                        newProductList[index].filteredProducts = [];
                    }
                    // product 主体赋值
                    newProductList[index].productDescription = productInfo.Item_Description_EN__c;
                    newProductList[index].productId = productInfo.Id;
                    newProductList[index].model = productInfo.Order_Model__c;
                    newProductList[index].qty = "";
                    newProductList[index].unitPerPallet = result.Qty;
                    newProductList[index].listPrice = result.listPrice;
                    newProductList[index].discount = result.discount;
                    newProductList[index].unitNetPrice = result.SalesPrice;
                    newProductList[index].unitNetPriceCaculate = result.SalesPriceCaculate;
                    // newProductList[index].totalNetPrice = (result.SalesPrice && result.Qty) ? Number(result.SalesPrice) * Number(result.Qty) : null;
                    newProductList[index].totalNetPrice = "";
                    newProductList[index].UsePromotion = false;
                    newProductList[index].useHeaderDiscount = true;
                    newProductList[index].remark = result.remark;
                    newProductList[index].promotioncode = "";
                    newProductList[index].MaxGreenLight = result.MaxGreenLight;
                    newProductList[index].MaxYellowLight = result.MaxYellowLight;
                    newProductList[index].CurrentStatus = result.CurrentStatus;
                    newProductList[index].SalesPriceCaculate = result.SalesPriceCaculate;
                    newProductList[index].listPriceCaculate = result.listPriceCaculate;
                    newProductList[index].standDiscount = result.standDiscount;
                    newProductList[index].applicationMethod = result.applicationMethod;
                    // calvin add
                    newProductList[index].key = Date.now().toString();
                    newProductList[index].type = 'MainProduct';
                    newProductList[index].salesPrice = result.SalesPrice;
                    // 判断红绿灯
                    switch (result.CurrentStatus) {
                        case 'green Light':
                            if (0 <= result.MaxGreenLight) {
                                newProductList[index].inventory = 'Green';
                            } else if (0 > result.MaxGreenLight && 0 <= result.MaxYellowLight) {
                                newProductList[index].inventory = 'Yellow';
                            } else {
                                newProductList[index].inventory = 'Red';
                            };
                            break;
                        case 'yellow Light':
                            newProductList[index].inventory = 'Yellow';
                            break;
                        case 'red Light':
                            newProductList[index].inventory = 'Red';
                            break;
                        default:
                            newProductList[index].inventory = 'Green';
                            break;
                    };
                    setTimeout(()=>{
                        // 价格计算
                        component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));
                        setTimeout(() => {
                            self.addNewProductLine(component);
                            self.scrollToBottom(component);
                        }, 100);
                        self.getTotalNumber(component, JSON.parse(JSON.stringify(newProductList)));

                        // show substitute model if product status is inactive or rto
                        if(productInfo.Product_Status__c === 'INACT' || productInfo.Product_Status__c === 'RTO' || newProductList[index].inventory === 'Red'){
                            // show only substitute product tab
                            component.set('v.showOnlySubstitute', true);
                            if(productInfo.Product_Status__c === 'INACT' || productInfo.Product_Status__c === 'RTO') {
                                newProductList[index].productInactive = true;
                            }
                            self.getRelatedSubstituteList(component, newProductList[index], index);
                        }
                        else {
                            // 检查是否有推荐配件数据
                            self.checkRecommendedAccessories(component, productInfo, index);
                            // 检查是否有Related & Substitute数据
                            self.checkRelatedSubstitute(component, productInfo, index);
                        }
                    }, 100)
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 开始计算价格
    getTotalNumber : function (component, _data, whole_product) {
        let data = JSON.parse(JSON.stringify(_data));
        console.log("过来的时候的表格信息",JSON.stringify(whole_product));
        let woheaderdiscount = component.get('v.woheaderdiscount');
        let allqty = 0;
        let totalValue = 0;
        let headerDiscountAmount = 0;
        let totalValueNet = 0;
        let freightCost = 0;
        let insuranceFee = 0;
        let otherFee = 0;
        let VAT = 0;
        let totalDueAmount = 0;
        let total_Different_Categories = 0;
        // product价格
        data.forEach((item)=>{
            if(!item.Fallshortofthreshold && item.type != "offering"){
                allqty = allqty + Number(item.qty);
                total_Different_Categories = total_Different_Categories + 1;
            }
            if(!item.Fallshortofthreshold){
                totalValue = totalValue + (Number(item.qty) * Number(item.unitNetPrice || 0));
             }
        });
    
        let verify_hd = true;
        let headerProductList = [];
        let headerDiscountList = [];
        let headerLineDiscountList = [];
        //判断是否有头折扣
        if(woheaderdiscount.length){
            // 进来先清空
            component.set('v.headerDiscount',"0.00");
            woheaderdiscount.forEach((value,index) =>{
                // value为每一条promotion code下面的数据
                // 这里首先清空整单下的所有产品,通过下面的逻辑每次点击判断
                data = data.filter(item => item.promotioncode != value[0].promotionCode);
                value.forEach(item =>{
                    if(((item.minQty || item.maxQty) && (allqty - item.minQty >= 0) && (allqty - item.maxQty < 0) && total_Different_Categories - item.Different_Categories >= 0)){
                        // console.log("满足Quantity",JSON.parse(JSON.stringify(item)),item.payment_type);
                        let headerDiscount = Number(component.get('v.headerDiscount'));
                        if(headerDiscount){
                            component.set('v.headerDiscount',((100 - ((100 - headerDiscount) * (100 - item.discountoff))/100)).toFixed(2))
                            headerDiscountList.push({
                                "discountoff" : item.discountoff,
                                "promotionCode" : item.promotionCode,
                            });
                        }else{
                            component.set('v.headerDiscount',(item.discountoff || 0).toFixed(2)) 
                            headerDiscountList.push({
                                "discountoff" : item.discountoff,
                                "promotionCode" : item.promotionCode,
                            });
                        }
                        if(item.products && item.products.length > 0){
                            item.products.forEach(value_item =>{
                                if(whole_product && value_item.productId == whole_product.productId && whole_product.promotioncode == item.promotionCode && !item.linediscount){
                                    value_item.qty = whole_product.qty;
                                }
                            });
                            headerProductList.push(...item.products);
                        }
                        if(item.payment_type){
                            let _payment_type = component.get('v.paymentTermOptions').filter(value => value.value == item.payment_type)[0].value;
                            component.set('v.paymentTerm', _payment_type);
                        }
                        if(item.linediscount) {
                            headerLineDiscountList.push({
                                'productId': item.productId,
                                'discount': item.discount
                            });
                        }
                        verify_hd = false;
                    } else if((item.minAmount || item.maxAmount) && (totalValue >= item.minAmount) && (totalValue < item.maxAmount) && total_Different_Categories - item.Different_Categories >= 0){
                        // console.log("满足Amount",JSON.parse(JSON.stringify(item)),item.payment_type);
                        let headerDiscount = Number(component.get('v.headerDiscount'));
                        if(headerDiscount){
                            component.set('v.headerDiscount',((100 - ((100 - headerDiscount) * (100 - item.discountoff))/100)).toFixed(2));
                            headerDiscountList.push({
                                "discountoff" : item.discountoff,
                                "promotionCode" : item.promotionCode,
                            })
                        }else{
                            component.set('v.headerDiscount',(item.discountoff || 0).toFixed(2)) 
                            headerDiscountList.push({
                                "discountoff" : item.discountoff,
                                "promotionCode" : item.promotionCode,
                            })
                        }
                        if(item.products && item.products.length > 0){
                            item.products.forEach(value_item =>{
                                if(whole_product && value_item.productId == whole_product.productId && whole_product.promotioncode == item.promotionCode && !item.linediscount){
                                    value_item.qty = whole_product.qty;
                                }
                            })
                            headerProductList.push(...item.products);
                        }
                        if(item.payment_type){
                            let _payment_type = component.get('v.paymentTermOptions').filter(value => value.value == item.payment_type)[0].value;
                            component.set('v.paymentTerm', _payment_type);
                        }

                        if(item.linediscount) {
                            headerLineDiscountList.push({
                                'productId': item.productId,
                                'discount': item.discount
                            });
                        }

                        verify_hd = false;
                        // console.log(index,"达到金额,获得头折扣","当前的金额",totalValue,"//最小金额",item.minAmount,"//最大金额",item.maxAmount ,"应用的百分比",item.discountoff,"%");
                    }else if(verify_hd){
                        // console.log(index,"未到达header discount条件");
                        component.set('v.headerDiscount',"0.00");
                        component.set('v.paymentTerm',component.get('v.paymentTerm_copy'));
                    }
                })
            })
            // 处理完成,装入符合条件的Product
            data.push(...headerProductList);
            if(verify_hd){
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Warning",
                    "message": 'The current order does not meet the threshold of header discount',
                    "type": "warning",
                    "duration": "dismissible"
                }).fire();
            }
        }else{
            component.set('v.headerDiscount',"0.00");
        }

        if(headerLineDiscountList.length > 0) {
            // cheapest products of whole order
            data.forEach(item=>{
                if(item.type !== 'offering' && !item.Fallshortofthreshold) {
                    headerLineDiscountList.forEach(d=>{
                        if(d.productId == item.productId) {
                            if(item.discount) {
                                item.discount = Number((100 - ((100 - item.discount) * (100 - d.discount))/100).toFixed(2));
                            }
                            else {
                                item.discount = d.discount;
                            }
                            item.unitNetPrice = Number(Number(item.salesPrice * ((100 - item.discount) / 100)).toFixed(2));
                        }
                    });
                }
            });
        }

        totalValue = 0;
        data.forEach((item)=>{
            if(!item.Fallshortofthreshold){
                totalValue = totalValue + (Number(item.qty) * Number(item.unitNetPrice || 0));
            }
        });

        let _headerDiscount = 0;
        // console.log("当前应用的头折扣list",JSON.stringify(headerDiscountList));
        component.set('v.headerDiscountList', headerDiscountList);
        if(headerDiscountList.length){
            headerDiscountList.forEach(item =>{
                if(item.discountoff){
                    _headerDiscount = ((100 - ((100 - _headerDiscount) * (100 - item.discountoff))/100)).toFixed(2);
                }
            })
        }
        component.set('v.headerDiscount', _headerDiscount);
        if(component.get('v.headerDiscount')){
            headerDiscountAmount = 0
            data.forEach((item)=>{
                if(!item.Fallshortofthreshold && item.useHeaderDiscount){
                    // console.log("当前计算头折优惠",headerDiscountAmount,item.unitNetPrice,_headerDiscount,item.qty);
                    if(_headerDiscount){  
                        headerDiscountAmount = Number(Number(headerDiscountAmount) + Number(((Number(item.unitNetPrice) * Number(_headerDiscount)/100  * item.qty)))).toFixed(2);
                    }
                    // console.log("每次计算后的产品价格",Number(((Number(item.unitNetPrice) * Number(_headerDiscount)/100  * item.qty))),"//",headerDiscountAmount);
                }
            });
        }
        component.set('v.productList', data);
        component.set('v.HeaderDiscountAmount__c', headerDiscountAmount);
        totalValueNet = totalValue - headerDiscountAmount;
        // 运费价格
        let customerType = component.get('v.customerType');
        if (!totalValueNet) {
            freightCost = 0;
            component.set('v.editFreightCost', true);
            component.set('v.showHelpText', false);
        } else {
            switch (customerType) {
                case 'Others':
                    freightCost = 0;
                    component.set('v.editFreightCost', true);
                    component.set('v.showHelpText', false);
                    break;
                case 'DE Customer':
                    if (totalValueNet > 0 && totalValueNet <= 150) {
                        freightCost = 7;
                    } else if (totalValueNet > 150 && totalValueNet <= 500) {
                        freightCost = 13;
                    } else if (totalValueNet > 500 && totalValueNet <= 1000) {
                        freightCost = 16.5;
                    } else {
                        freightCost = 0;
                    };
                    component.set('v.editFreightCost', false);
                    component.set('v.showHelpText', true);
                    break;
                case 'PBE':
                    freightCost = 0;
                    component.set('v.editFreightCost', true);
                    component.set('v.showHelpText', true);
                    break;
                case 'HKL':
                    if (totalValueNet > 0 && totalValueNet <= 1000) {
                        freightCost = 12.5;
                        component.set('v.editFreightCost', false);
                        component.set('v.showHelpText', false);
                    } else {
                        freightCost = 0;
                        component.set('v.editFreightCost', true);
                        component.set('v.showHelpText', true);
                    }
                    break;
                default:
                    freightCost = 0;
                    component.set('v.editFreightCost', true);
                    component.set('v.showHelpText', false);
            };
        }
        totalDueAmount = totalValue - headerDiscountAmount + freightCost;
        component.set('v.totalValue', Number(totalValue));
        component.set('v.headerDiscountAmount', Number(headerDiscountAmount));
        component.set('v.totalValueNet', Number(totalValueNet));
        component.set('v.freightCost', Number(freightCost));
        component.set('v.insuranceFee', Number(insuranceFee));
        component.set('v.otherFee', Number(otherFee));
        component.set('v.VAT', Number(VAT));
        component.set('v.totalDueAmount', Number(totalDueAmount));
    },
    // 获取品牌相关信息
    getBrandInfo : function (component) {
        var action = component.get("c.QueryAuthBrand");
        action.setParams({
            CustomerId: component.get('v.customerId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                component.set('v.authBrandId', result.Id);
                component.get('v.paymentTerm') ? "" : component.set('v.paymentTerm', result.Payment_Term__c);
                component.set('v.paymentTerm_copy', result.Payment_Term__c);
                component.set('v.freightTerm', result.Freight_Term__c);
                component.set('v.incoTerm', result.Incoterm__c);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 下一步
    handleNextStep : function (component, isNext) {
        // 参数格式处理
        let productList = component.get('v.productList');
        productList = productList.filter(item=>!item.lastBlankLine);
        let jsonParams = {
            productList: productList,
            headerDiscountList: component.get('v.headerDiscountList'),
            headerDiscount: component.get('v.headerDiscount'),
            woheaderdiscount: component.get('v.woheaderdiscount'),
            paymentTerm: component.get('v.paymentTerm')
        };
        let paramsArr = [];
        console.log(JSON.stringify(productList), 'productList===================');
        productList.forEach((item, index)=>{
            if(!item.linediscount && !item.Fallshortofthreshold) {
                let allInventory = '';
                let toolInventory = [];
                if (item.filteredProducts && item.filteredProducts.length) {
                    item.filteredProducts.forEach((toolItem)=>{
                        toolInventory.push(toolItem.inventory);
                    });
                    if (toolInventory.includes('Red')) {
                        allInventory = 'Red';
                    } else if (toolInventory.includes('Yellow')) {
                        allInventory = 'Yellow';
                    } else {
                        allInventory = 'Green';
                    }
                } else {
                    allInventory = item.inventory;
                }
                paramsArr.push({
                    "NO__c":(index + 1).toString(),
                    "Product_Model__c": item.model,
                    "Product__c": item.productId,
                    "Request_Date__c": item.requestDate,
                    "Quantity__c": item.qty,
                    "Promotion_Product__c": item.type == "offering" ? item.promotionProductId : "",
                    "Promotion_Offering__c": item.type == "offering" ? item.promotionofferingId : "",
                    "Inventory__c": allInventory,
                    "Sales_Price__c": item.salesPrice,
                    "List_Price__c": item.listPrice,
                    "Promotion_Code__c": item.promotioncode,
                    "Discount__c": item.discount,
                    "Fallshortofthreshold__c":item.Fallshortofthreshold,
                    "Unit_Price__c": Number(Number(item.unitNetPrice).toFixed(2)),
                    "unit_Net_Price2__c":item.useHeaderDiscount ? Number(Number(Number(item.unitNetPrice) - Number((item.unitNetPrice * component.get('v.headerDiscount'))/100).toFixed(2)).toFixed(2)) : Number(Number(item.unitNetPrice).toFixed(2)),                             // 使用头折扣之后的价格
                    "userPromotion__c": item.useHeaderDiscount,
                    "Promotion_Discount_Type__c": ((item.type == "MainProduct" || item.type == "threshold") && item.discount) ?  "percent" :item.discounttype,
                    "Total_Net_Price__c": item.showPromotionType == 'FreeGoods' ? 0 : item.showPromotionType == 'DiscountOff' ? Number(((Math.round(item.unitNetPrice * item.discount) * item.discount)/100).toFixed(2)) : Number((item.unitNetPrice * item.qty).toFixed(2)),
                    "Remarks__c": item.remark,
                    "Stand_Discount_Value__c": item.standDiscount,
                    "Application_Method__c": item.applicationMethod,
                    "Id": null,
                });
            }
        });
        let _headerDiscountList = ""
        component.get('v.headerDiscountList').forEach(item => {
            _headerDiscountList =  _headerDiscountList + item.promotionCode + ":" + item.discountoff + ";"
        })
        var action = component.get("c.UpsertPurchaseOrderItem");
        action.setParams({
            lstPurchaseOrder: JSON.parse(JSON.stringify(paramsArr)),
            objPurchaseOrder: {
                Header_Discount_Amount__c:Number(Number(component.get('v.HeaderDiscountAmount__c')).toFixed(2)),  // 当前整单优惠的头折扣的金额
                Header_Discount__c : Number(component.get('v.headerDiscount')),
                Multi_Header_Promotion__c : _headerDiscountList,
                Authorized_Brand__c: component.get('v.authBrandId'),
                Payment_Term__c: component.get('v.paymentTerm'),
                Inco_Term__c: component.get('v.incoTerm'),
                Freight_Term__c: component.get('v.freightTerm'),
                Freight_Fee__c: component.get('v.freightCost'),
                Expected_Delivery_Date__c: component.get('v.expectedDeliveryDate'),
                Id: component.get('v.purchaseOrderId'),
            },
            AuthBrandId: component.get('v.authBrandId'),
            productSelectJson: JSON.stringify(jsonParams),
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                if(isNext) {
                    var result = JSON.parse(response.getReturnValue());
                    component.set("v.currentStep", component.get("v.currentStep") + 1);
                }
                else {
                    if (component.get('v.actionType') != 'edit') {
                        component.set('v.actionType', 'draft');
                    }
                    component.set("v.currentStep", component.get("v.currentStep") - 1);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取当前行的promotion选择列表
    getOrderPromotionList : function (component, index) {
        let productList = component.get('v.productList');
        let productItem = productList[index];
        if (!(productItem.productDescription)) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'Please select product first',
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            return;
        }
        component.set('v.isBusy',true);
        var action = component.get("c.getPromotionsByProduct");
        action.setParams({
            prodId: productItem.productId,
            customerId: component.get('v.customerId'),
            isPortal: false,
            isDropShip: component.get('v.isDropShip') === 'Y' ? true : false,
            PricingDate: component.get('v.pricingDate'),
        });
        action.setCallback(this, function (response) {
            component.set('v.isBusy',false)
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if (result && result.length) {
                result.forEach(item=>{
                    item.Name = item.promotion.Promo_Code__c
                })
                component.set('v.promotionList', result);
                    component.set('v.promotionModalFlag', true);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": 'There is no relevant promotion on this product',
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
                
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },

    // 处理promotion
    disposePromotion : function (component, data, ispush, isthreshold) {   //  **data:Promotiondata  **ispush:Specific Product && Multiple Product **isthreshold:true && false
        // component.get('v.mainProduct')为当前的选择promotion的主产品;
        let key = component.get('v.mainProduct').key;
        let self = this;
        // 全部的rulelist都取得第一条,如果是price break 需要去每一条的rule
        let Min_Total_Amount = data.ruleList[0].thresholdList[0].threshold.Minimum_Total_Amount_Per_Order__c;      // 整单金额大于
        let Min_Total_Qty = data.ruleList[0].thresholdList[0].threshold.Minimum_Total_Quantity_Per_Order__c;       // 整单数量大于
        let Min_Qty = data.ruleList[0].thresholdList[0].threshold.Minimum_Quantity_Per_Order__c;                   // 主产品或搭配产品数量大于
        let Min_Amount = data.ruleList[0].thresholdList[0].threshold.Minimum_Amount_Per_Order__c;                  // 主产品或搭配产品金额大于
        let applicationMethod = data.ruleList[0].thresholdList[0].tproducts[0].applicationMethod;                  // 帮后端带的
        let standardDiscountValue = data.ruleList[0].thresholdList[0].tproducts[0].standardDiscountValue;          // 帮后端带的
        if(ispush == "single"){
            if(isthreshold){
                // 主产品为单个产品,就是已经在order上的产品,添加Min Qty等校验
                let productList = component.get('v.productList')
                let Threshold_Qty = []
                let Threshold_Amount = []
                if(data.promotion.Promotion_Type__c == "Price Break"){
                    data.ruleList.forEach((item,index)=>{
                        item.thresholdList.forEach(value =>{
                            Threshold_Qty.push({
                                Min_qty : value.threshold.Minimum_Quantity_Per_Order_Included__c,
                                Max_qty : value.threshold.Maximum_Quantity_Per_Order_Excluded__c
                            })
                            Threshold_Amount.push({
                                Min_amount : value.threshold.Minimum_Amount_Per_Order_Included__c,
                                Max_amount : value.threshold.Maximum_Amount_Per_Order_Excluded__c
                            })
                        })

                    })
                }
                let _qty = productList[component.get('v.operationRow')].qty < Min_Qty ? Min_Qty : productList[component.get('v.operationRow')].qty
                if(Min_Amount){
                    _qty = Math.ceil(Min_Amount/productList[component.get('v.operationRow')].unitNetPrice)
                }
                if(Threshold_Qty && Threshold_Qty[0] && Threshold_Qty[0].Min_qty){
                    _qty = Threshold_Qty[0].Min_qty
                }else if(Threshold_Amount && Threshold_Amount[0] && Threshold_Amount[0].Min_amount){
                    _qty = Math.ceil(Threshold_Amount[0].Min_amount/productList[component.get('v.operationRow')].unitNetPrice)
                }
                productList[component.get('v.operationRow')].Mixgoods_Qty = Threshold_Qty
                productList[component.get('v.operationRow')].Mixgoods_Amount = Threshold_Amount
                productList[component.get('v.operationRow')].qty = _qty
                productList[component.get('v.operationRow')].Min_Qty = Min_Qty
                productList[component.get('v.operationRow')].Min_Amount = Min_Amount,
                productList[component.get('v.operationRow')].applicationMethod = applicationMethod
                productList[component.get('v.operationRow')].standDiscount = standardDiscountValue
                productList[component.get('v.operationRow')].Min_Total_Amount = Min_Amount
                productList[component.get('v.operationRow')].Min_Total_Qty = Min_Total_Qty,
                productList[component.get('v.operationRow')].UsePromotion = true
                productList[component.get('v.operationRow')].totalNetPrice = _qty * productList[component.get('v.operationRow')].unitNetPrice
                productList[component.get('v.operationRow')].useHeaderDiscount = true
                productList[component.get('v.operationRow')].promotioncode = data.promotion.Promo_Code__c
                component.set('v.productList',productList);
                component.set('v.pupUp_ProductSelect',{});
            }else{
                let _offeringproduct = []
                let _lstproducts = []
                // offering为单个产品,无无需选择,直接添加到orderList
                let productList = component.get('v.productList');
                let selectProduct = component.get('v.pupUp_ProductSelect') && component.get('v.pupUp_ProductSelect').list ? component.get('v.pupUp_ProductSelect').list.filter(item=>item.select) : [];
                let selectProductIds = [];
                selectProduct.forEach(p => {
                    selectProductIds.push(p.productId);
                    _lstproducts.push(p.productId);
                });
                let currentMainProduct = productList[component.get('v.operationRow')];
                // if main product's promotion threshold type is Multiple_Products_By_Quantity / Multiple_Products_By_Amount,
                // need to check if Min_Qty / Min_Amount is empty, if not empty, need to add to selectProduct
                data.ruleList.forEach(value=>{
                    value.thresholdList.forEach(t => {
                        if(t.threshold.RecordType.DeveloperName === 'Multiple_Products_By_Quantity') {
                            let minQty_products = component.get('v.pupUp_ProductSelect') && component.get('v.pupUp_ProductSelect').list ? component.get('v.pupUp_ProductSelect').list.filter(item=>item.Min_Qty > 0) : [];
                            minQty_products.forEach(p=>{
                                if(currentMainProduct && currentMainProduct.productId != p.productId){
                                    if(!selectProductIds.includes(p.productId)) {
                                        selectProduct.push(p);
                                        selectProductIds.push(p.productId);
                                        _lstproducts.push(p.productId);
                                    }
                                    
                                }
                            });
                        }
                        else if(t.threshold.RecordType.DeveloperName === 'Multiple_Products_By_Amount') {
                            let minAmount_products = component.get('v.pupUp_ProductSelect') && component.get('v.pupUp_ProductSelect').list ? component.get('v.pupUp_ProductSelect').list.filter(item=>item.Min_Amount > 0) : [];
                            minAmount_products.forEach(p=>{
                                if(currentMainProduct && currentMainProduct.productId != p.productId){
                                    if(!selectProductIds.includes(p.productId)) {
                                        selectProduct.push(p);
                                        selectProductIds.push(p.productId);
                                        _lstproducts.push(p.productId);
                                    }
                                }
                            });
                        }
                    });
                });
                data.ruleList.forEach((value,index) =>{
                    // let minPriceProduct = {}
                    value.offeringList.forEach(item=>{
                        item.oproducts.forEach(i=>{
                            // 当前产品享受的优惠
                            let _discount = "";
                            let _qty = "";
                            let _unitNetPrice = "";
                            let is_full_discount = false;
                            if(i.product.Discount_Off__c || item.offering.Discount_Off__c){
                                i.product.Discount_Off__c ? _discount = 100 - i.product.Discount_Off__c : "";
                                item.offering.Discount_Off__c ? _discount = 100 - item.offering.Discount_Off__c : "";

                                if(i.product.Discount_Off__c === 100 || item.offering.Discount_Off__c === 100) {
                                    is_full_discount = true;
                                }
                            }
                            // offering产品,qty为当前产品的最大购买数量
                            if(i.product.Quantity_Per_Order__c || item.offering.Free_Goods_Quantity__c){
                                _qty = i.product.Quantity_Per_Order__c || item.offering.Free_Goods_Quantity__c;
                            }
                            else if(i.product.Max_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c){
                                _qty = i.product.Max_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c;
                            }else{
                                _qty = 1;
                            }
                            // 实际享受折扣后参与运算的价格
                            if(i.product.Fixed_Price__c || item.offering.Fixed_Price__c){
                                _unitNetPrice = i.product.Fixed_Price__c || item.offering.Fixed_Price__c;
                            }else{
                                _unitNetPrice = Number((Number(i.salesPrice) - Number(((i.salesPrice * (100 - _discount))/100).toFixed(2))).toFixed(2));
                            }

                            if(_unitNetPrice === 0) {
                                is_full_discount = true;
                            }

                            _offeringproduct.push({
                                promotionproductid: i.product.Id,
                                offeringid:item.offering.Id,
                                qty:_qty,
                            });
                            _lstproducts.push(i.product.Product__c);
                            selectProduct.push({
                                // 把挂在产品上的头折扣以及付款方式挂在offering上,达到threshold时,满足offering的通常满足头折扣以及付款方式
                                'key':key,                                                                                                                                                                    // 标识同一promotion产品的唯一键
                                'useHeaderDiscount': item.offering.Compatible_with_Header_Level_Promotion__c == "Yes" ? true : false,                                                                         // 是否同时享受折扣
                                'type':'offering',                                                                                                                                                            // 类型为offering
                                "discounttype": item.offering.Promotion_Discount_Type__c ? item.offering.Promotion_Discount_Type__c : "",                                                                     // 捎带给后端的值
                                "applicationMethod": i.applicationMethod ? i.applicationMethod : "",                                                                                                          // 捎带给后端的值
                                "standDiscount": i.standardDiscountValue ? i.standardDiscountValue : "",                                                                                                      // 捎带给后端的值
                                "ruleindex":index,                                                                                                                                                            // 如果是price break的类型,存在多条rule
                                "classify": item.offering.RecordType.DeveloperName,                                                                                                                           // 四种大类,不同的计算,BOGO不算,支付方式不算,其实就两种
                                'productDescription': i.product.Product__r.Name,                                                                                                                              // 产品名字
                                'productId': i.product.Product__c,                                                                                                                                            // 产品id
                                'promotionProductId': i.product.Id,                                                                                                                                           // promotion上的产品id
                                'promotionofferingId': item.offering.Id,                                                                                                                                      // promotion上的产品id
                                'model': i.product.Product__r.ProductCode,                                                                                                                                    // 产品的系列
                                'requestDate': null,                                                                                                                                                          // 
                                'qty': _qty,                                                                                                                                                                  // 赋值当前可享受的最大数量
                                'unitPerPallet': null,                                                                                                                                                        //
                                'inventory': null,                                                                                                                                                            //
                                'listPrice': i.listPrice,                                                                                                                                                     // 列出的价格
                                'discount': _discount || is_full_discount ? 100 -_discount : 0,                                                                                                                                   // 折扣(减掉的)
                                'unitNetPrice': item.Largetype == "FreeGoods" ? 0 : _unitNetPrice,                                                                                                            // 实际销售的价格(打折后)
                                "salesPrice" : i.salesPrice,                                                                                                                                                  // 实际计算的价格
                                'totalNetPrice': item.Largetype == "FreeGoods" ? 0 : _unitNetPrice * _qty,                                                                                                    // 当前行产品的总价格
                                'remark': "",                                                                                                                                                                 // 
                                'promotioncode': data.promotion.Promo_Code__c,                                                                                                                                // 
                                'isPromotion': true,                                                                                                                                                          // 禁止修改产品                                                                                                                             //
                                'select':true,                                                                                                                                                                // 是否选中
                                "Min_Qty": i.product.Min_Qty_Per_Order__c || 0,                                                                                                                               // 最小购买数量
                                "Max_Qty": i.product.Max_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c,                                                                                     // 最大可以购买的数量
                                "Min_Amount": 0 ,                                                                                                                                                             // 最后满足的金额
                                "Min_Total_Amount": Min_Total_Amount || 0,                                                                                                                                    // 总的金额价格
                                "showPromotionType": item.Largetype,                                                                                                                                          // 当前的offering类型/BOGO/Price Discount/Price Amount
                                "Fallshortofthreshold": index == 0 ? false : true,                                                                                                                            // 是否显示/如果为Price Break的类型/只显示第一条rule的产品
                                'UsePromotion' : true,
                            })
                        })

                        // cheapest products discount off, add discount to select products
                        if(item.offering.RecordType.DeveloperName == "Discount_Off_Cheapest_Among_Products"){
                            let discount = item.offering.Discount_Off__c;
                            let _product = {
                                'key':key,                                                                                                                                                                    // 标识同一promotion产品的唯一键
                                'useHeaderDiscount': item.offering.Compatible_with_Header_Level_Promotion__c == "Yes" ? true : false,                                                                         // 是否同时享受折扣
                                'type':'offering',                                                                                                                                                            // 类型为offering
                                "discounttype": item.offering.Promotion_Discount_Type__c ? item.offering.Promotion_Discount_Type__c : "",                                                                     // 捎带给后端的值
                                "ruleindex":index,                                                                                                                                                            // 如果是price break的类型,存在多条rule
                                "classify": item.offering.RecordType.DeveloperName,                                                                                                                           // 四种大类,不同的计算,BOGO不算,支付方式不算,其实就两种
                                'promotionofferingId': item.offering.Id,                                                                                                                                      // promotion上的产品id
                                'discount': discount,                                                                                                                                   // 折扣(减掉的)
                                'promotioncode': data.promotion.Promo_Code__c,
                                "maxqty": item.offering.Maximum_Quantity_Per_order__c,                                                                                                                            // 
                                'isPromotion': true,                                                                                                                                                          // 禁止修改产品                                                                                                                             //
                                "showPromotionType": item.Largetype,                                                                                                                                          // 当前的offering类型/BOGO/Price Discount/Price Amount
                                "Fallshortofthreshold": true,                                                                                                                          // 是否显示/如果为Price Break的类型/只显示第一条rule的产品
                                'UsePromotion' : true,
                                "linediscount": true,
                                "UseLineDiscountOff": index == 0 ? true : false
                            };

                            let isthresholdproduct = component.get('v.productList').filter(item => item.promotioncode == data.promotion.Promo_Code__c && item.type != "offering");
                            if(isthresholdproduct.length > 0){
                                let addcheapestproductList = component.get('v.productList');
                                let minprice = undefined;
                                addcheapestproductList.forEach(item =>{
                                    if(item.promotioncode == data.promotion.Promo_Code__c && item.type != "offering"){
                                        if(!minprice) {
                                            minprice = item;
                                        }
                                        else {
                                            if(Number(item.salesPrice) < Number(minprice.salesPrice)) {
                                                minprice = item;
                                            }
                                        }
                                    }
                                });
                                if(minprice) {
                                    _product.productId = minprice.productId;
                                }
                            }
                            selectProduct.push(_product);
                            // console.log("当前的类型是Discount_Off_Cheapest_Among_Products");
                            // let minprice = item.oproducts[0]
                            // minPriceProduct.id = minprice.product.Product__c
                            // minPriceProduct.maxqty = item.offering.Maximum_Quantity_Per_order__c;
                            // minPriceProduct.discountoff = item.offering.Discount_Off__c;
                            // minPriceProduct.ruleindex = index;
                            // minPriceProduct.useHeaderDiscount = item.offering.Compatible_with_Header_Level_Promotion__c;
                            // if(selectProduct.filter(item => item.ruleindex == index).length > 1){
                            //     selectProduct.forEach(v => {
                            //         if(Number(v.salesPrice) < Number(minprice.salesPrice) && v.ruleindex == index){
                            //             minPriceProduct.id = v.productId
                            //         }
                            //     })
                            // }else{
                            //     minPriceProduct.id = item.oproducts[0].product.Product__c
                            // }
                            // Apply cheapest on threshold products
                            // let isthresholdproduct = component.get('v.productList').filter(item => item.promotioncode == data.promotion.Promo_Code__c && item.type != "offering");
                            // if(isthresholdproduct.length > 0){
                            //     let addcheapestproductList = component.get('v.productList');
                            //     let minprice = undefined;
                            //     addcheapestproductList.forEach(item =>{
                            //         if(item.promotioncode == data.promotion.Promo_Code__c && item.type != "offering"){
                            //             if(!minprice) {
                            //                 minprice = item;
                            //             }
                            //             else {
                            //                 if(Number(item.salesPrice) < Number(minprice.salesPrice)) {
                            //                     minprice = item;
                            //                 }
                            //             }
                            //         }
                            //     })

                            //     if(minprice){
                            //         minprice.Max_Qty = minPriceProduct.maxqty;
                            //         minprice.discount = minPriceProduct.discountoff;
                            //         minprice.unitNetPrice = Number((Number(item.salesPrice) - Number(((item.salesPrice * minPriceProduct.discountoff)/100).toFixed(2))).toFixed(2));
                            //         minprice.totalNetPrice = item.unitNetPrice * item.qty;
                            //         minprice.useHeaderDiscount = minPriceProduct.useHeaderDiscount;
                            //     }
                            //     component.set('v.productList',addcheapestproductList);
                            //     selectProduct = selectProduct.filter(item => (data.promotion.Promo_Code__c == item.promotioncode && item.type != "offering") || item.ruleindex != index)
                            // }else{
                            //     selectProduct = selectProduct.filter(item => (item.type != "offering" || (item.type == "offering" && item.productId == minPriceProduct.id)) || item.ruleindex != index)
                            // }
                        }

                        // bundle products discount off, add discount to select products
                        if(item.offering.RecordType.DeveloperName == "Bundle_Products_Discount_Off") {
                            let discount = item.offering.Discount_Off__c;
                            selectProduct.push({
                                'key':key,                                                                                                                                                                    // 标识同一promotion产品的唯一键
                                'useHeaderDiscount': item.offering.Compatible_with_Header_Level_Promotion__c == "Yes" ? true : false,                                                                         // 是否同时享受折扣
                                'type':'offering',                                                                                                                                                            // 类型为offering
                                "discounttype": item.offering.Promotion_Discount_Type__c ? item.offering.Promotion_Discount_Type__c : "",                                                                     // 捎带给后端的值
                                "ruleindex":index,                                                                                                                                                            // 如果是price break的类型,存在多条rule
                                "classify": item.offering.RecordType.DeveloperName,                                                                                                                           // 四种大类,不同的计算,BOGO不算,支付方式不算,其实就两种
                                'promotionofferingId': item.offering.Id,                                                                                                                                      // promotion上的产品id
                                'discount': discount,                                                                                                                                   // 折扣(减掉的)
                                'promotioncode': data.promotion.Promo_Code__c,                                                                                                                                // 
                                'isPromotion': true,                                                                                                                                                          // 禁止修改产品                                                                                                                             //
                                "showPromotionType": item.Largetype,                                                                                                                                          // 当前的offering类型/BOGO/Price Discount/Price Amount
                                "Fallshortofthreshold": true,                                                                                                                          // 是否显示/如果为Price Break的类型/只显示第一条rule的产品
                                'UsePromotion' : true,
                                "linediscount": true,
                                "UseLineDiscountOff": index == 0 ? true : false
                            });
                        }
                    });

                    
                    // value.offeringList.forEach(item=>{
                    //     if(item.offering.RecordType.DeveloperName == "Bundle_Products_Discount_Off") {
                    //         let discount = item.offering.Discount_Off__c;
                    //         selectProduct.push({
                    //             'key':key,                                                                                                                                                                    // 标识同一promotion产品的唯一键
                    //             'useHeaderDiscount': item.offering.Compatible_with_Header_Level_Promotion__c == "Yes" ? true : false,                                                                         // 是否同时享受折扣
                    //             'type':'offering',                                                                                                                                                            // 类型为offering
                    //             "discounttype": item.offering.Promotion_Discount_Type__c ? item.offering.Promotion_Discount_Type__c : "",                                                                     // 捎带给后端的值
                    //             "ruleindex":index,                                                                                                                                                            // 如果是price break的类型,存在多条rule
                    //             "classify": item.offering.RecordType.DeveloperName,                                                                                                                           // 四种大类,不同的计算,BOGO不算,支付方式不算,其实就两种
                    //             'promotionofferingId': item.offering.Id,                                                                                                                                      // promotion上的产品id
                    //             'discount': discount,                                                                                                                                   // 折扣(减掉的)
                    //             'promotioncode': data.promotion.Promo_Code__c,                                                                                                                                // 
                    //             'isPromotion': true,                                                                                                                                                          // 禁止修改产品                                                                                                                             //
                    //             "showPromotionType": item.Largetype,                                                                                                                                          // 当前的offering类型/BOGO/Price Discount/Price Amount
                    //             "Fallshortofthreshold": true,                                                                                                                          // 是否显示/如果为Price Break的类型/只显示第一条rule的产品
                    //             'UsePromotion' : true,
                    //             "linediscount": true,
                    //             "UseLineDiscountOff": index == 0 ? true : false
                    //         });
                    //     }
                    // });

                    // if(index == 0) {
                        // value.offeringList.forEach(item=>{
                        //     if(item.offering.RecordType.DeveloperName == "Bundle_Products_Discount_Off") {
                        //         let bundleDiscountOff = item.offering.Discount_Off__c;
                        //         // select only threshold products
                        //         currentMainProduct.discount = bundleDiscountOff;
                        //         currentMainProduct.unitNetPrice = Number(Number(currentMainProduct.unitNetPrice * (( 100 - currentMainProduct.discount) / 100)).toFixed(2));
                        //         currentMainProduct.totalNetPrice = Number((currentMainProduct.unitNetPrice * currentMainProduct.qty).toFixed(2));
                        //         selectProduct.forEach(item => {
                        //             if(item.type !== 'offering' && item.key === key) {
                        //                 item.discount = bundleDiscountOff;
                        //                 item.unitNetPrice = Number(Number(item.unitNetPrice * (( 100 - item.discount) / 100)).toFixed(2));
                        //                 item.totalNetPrice = Number((item.unitNetPrice * item.qty).toFixed(2));
                        //             }
                        //         });
                        //     }
                        // });
                    // }
                    // if(component.get('v.isCheapest')){
                    //     selectProduct.forEach(item =>{
                    //         if(item.ruleindex == index){
                    //             item.cheapest = minPriceProduct
                    //             }
                    //         })          
                    // }
                });

                let linediscount_products = selectProduct.filter(item => item.linediscount && item.UseLineDiscountOff);
                if(linediscount_products) {

                    // calculate discount on line product
                    linediscount_products.forEach(discount_item=>{
                        let discount = discount_item.discount;
                        if(discount_item.productId) {
                            if(currentMainProduct.productId === discount_item.productId) {
                                if(!currentMainProduct.discount) {
                                    currentMainProduct.discount = discount;
                                }
                                else {
                                    currentMainProduct.discount = Number((100 - ((100 - currentMainProduct.discount) * (100 - discount))/100).toFixed(2));
                                }
                            }
                        }
                        else {
                            if(!currentMainProduct.discount) {
                                currentMainProduct.discount = discount;
                            }
                            else {
                                currentMainProduct.discount = Number((100 - ((100 - currentMainProduct.discount) * (100 - discount))/100).toFixed(2));
                            }
                        }

                        selectProduct.forEach(item => {
                            if(item.type !== 'offering' && item.promotioncode === data.promotion.Promo_Code__c) {
                                if(discount_item.productId) {
                                    if(item.productId === discount_item.productId) {
                                        if(!item.discount) {
                                            item.discount = discount;
                                        }
                                        else {
                                            item.discount = Number((100 - ((100 - item.discount) * (100 - discount))/100).toFixed(2));
                                        }
                                    }
                                }
                                else {
                                    if(!item.discount) {
                                        item.discount = discount;
                                    }
                                    else {
                                        item.discount = Number((100 - ((100 - item.discount) * (100 - discount))/100).toFixed(2));
                                        item.totalNetPrice = Number((item.unitNetPrice * item.qty).toFixed(2));
                                    }
                                }
                            }
                        });
                    });


                    // calculate total price
                    currentMainProduct.unitNetPrice = Number(Number(currentMainProduct.salesPrice * (( 100 - (currentMainProduct.discount ? currentMainProduct.discount : 0)) / 100)).toFixed(2));
                    currentMainProduct.totalNetPrice = Number((currentMainProduct.unitNetPrice * currentMainProduct.qty).toFixed(2));

                    selectProduct.forEach(item => {
                        if(item.type !== 'offering' && item.promotioncode === data.promotion.Promo_Code__c) {
                            item.unitNetPrice = Number(Number(item.salesPrice * (( 100 - (item.discount ? item.discount : 0)) / 100)).toFixed(2));
                            item.totalNetPrice = Number((item.unitNetPrice * item.qty).toFixed(2));
                        }
                    });
                }

                let uploadData = {
                    offeringproduct:_offeringproduct,
                    lstproducts : _lstproducts,
                    purchaseorderid:component.get('v.purchaseOrderId'),   // 不需要处理
                    pricingdate:component.get('v.pricingDate'),           // 不需要处理
                    customerid:component.get('v.customerId')              // 不需要处理
                }
                self.getQuantityLimitInventoryCondition(component, uploadData, key);
                component.set('v.promotionModalFlag', false);
                // 如果当前的产品已经有promotion产品
                if(component.get('v.productList').filter(item => item.key == key).length >1){
                    // 判断主产品的qty
                    productList[component.get('v.operationRow')].qty = productList[component.get('v.operationRow')].qty - Min_Qty > 0 ? productList[component.get('v.operationRow')].qty : Min_Qty;
                };
                selectProduct.forEach((item)=>{
                    item.requestDate = component.get('v.expectedDeliveryDate');
                })
                self.changeOrderList(component, productList, selectProduct);
            }
        }else if(ispush == "multiple"){
            if(isthreshold){
                let productList = component.get('v.productList')
                let Threshold_Qty = []
                let Threshold_Amount = []
                let Mixgoods_Qty = {}
                let Mixgoods_Amount = {}
                if(data.promotion.Promotion_Type__c == "Price Break"){
                    data.ruleList.forEach((item,index)=>{
                        item.thresholdList.forEach(value =>{
                            Threshold_Qty.push({
                                Min_qty : value.threshold.Minimum_Total_Quantity_Per_Order_Incl__c,
                                Max_qty : value.threshold.Maximum_Total_Quantity_Per_Order_Excl__c
                            })
                            Threshold_Amount.push({
                                Min_amount : value.threshold.Minimum_Total_Amount_Per_Order_Includ__c,
                                Max_amount : value.threshold.Maximum_Total_Amount_Per_Order_Exclud__c
                            })
                            value.tproducts.forEach(v => {
                                !Mixgoods_Amount[v.product.Product__c] ? Mixgoods_Amount[v.product.Product__c] = [] : ""
                                !Mixgoods_Qty[v.product.Product__c] ? Mixgoods_Qty[v.product.Product__c] = [] : ""
                                
                                Mixgoods_Amount[v.product.Product__c].push(v.product.Min_Amount_Per_Order__c)
                                Mixgoods_Qty[v.product.Product__c].push(v.product.Min_Qty_Per_Order__c)
                            })
                        })
                    })
                }
                // console.log("Price Break的阶梯threshold","数量",JSON.parse(JSON.stringify(Mixgoods_Qty)),"金额",JSON.parse(JSON.stringify(Mixgoods_Amount)),"promotion数量",JSON.parse(JSON.stringify(Threshold_Qty)),"promotion金额",JSON.parse(JSON.stringify(Threshold_Amount)));
              // 设置弹窗显示为threshold或offering
                component.set('v.bogoFlag', true);                                                              // 打开上面已经设置信息product选择弹窗
                component.set('v.promotionModalFlag', false);                                                   // 关闭Apply弹窗
                let toOrderList = [];
                // 判断是threshold还是offering
                let thresholdselectList = data.ruleList[0].thresholdList[0].tproducts;
                console.log(JSON.stringify(thresholdselectList), 'mix goods======================');
                thresholdselectList.forEach((item)=>{
                    if(item.product.Product__c != component.get('v.mainProduct').productId){
                        let _qty = item.product.Min_Amount_Per_Order__c ? Math.ceil(item.product.Min_Amount_Per_Order__c/item.salesPrice) : item.product.Min_Qty__c || item.product.Min_Qty_Per_Order__c || 1
                        toOrderList.push({
                            'key':key,
                            'type':'threshold',
                            "applicationMethod": item.applicationMethod ? item.applicationMethod : "",
                            "standDiscount": item.standardDiscountValue ? item.standardDiscountValue : "",
                            'productDescription': item.product.Product__r.Name,
                            'productId': item.product.Product__c,
                            'model': item.product.Product__r.ProductCode,
                            'requestDate': null,
                            'qty': _qty,
                            'unitPerPallet': null,
                            'inventory': null,
                            'listPrice': item.listPrice,
                            'discount': null,
                            "useHeaderDiscount":true,
                            "salesPrice" : item.salesPrice,
                            'unitNetPrice': item.salesPrice,
                            'totalNetPrice': _qty * item.salesPrice,
                            'remark': "",
                            "Threshold_Qty" : Mixgoods_Qty[item.product.Product__c],
                            "Threshold_Amount" : Mixgoods_Amount[item.product.Product__c],
                            'promotioncode': data.promotion.Promo_Code__c,
                            'isPromotion': true,
                            'select': false,
                            "Min_Qty": item.product.Min_Qty__c || item.product.Min_Qty_Per_Order__c || 0,
                            "Min_Amount": item.product.Min_Amount_Per_Order__c || 0 ,
                            "showPromotionType":"Mix Goods",
                            'UsePromotion' : true,
                        })
                    }else{
                        // 主产品为单个产品,就是已经在order上的产品,添加Min Qty等校验
                        let _qtyitem_v = item.product.Min_Amount_Per_Order__c ? Math.ceil(item.product.Min_Amount_Per_Order__c/item.salesPrice)  : item.product.Min_Qty__c || item.product.Min_Qty_Per_Order__c || 1
                        let _qty = productList[component.get('v.operationRow')].qty < (item.product.Min_Qty__c || _qtyitem_v) ? (item.product.Min_Qty__c || _qtyitem_v) : productList[component.get('v.operationRow')].qty
                        if(Threshold_Qty && Threshold_Qty[0] && Threshold_Qty[0].Min_qty){
                            _qty = Threshold_Qty[0].Min_qty
                        }else if(Threshold_Amount && Threshold_Amount[0] && Threshold_Amount[0].Min_amount){
                            _qty = Math.ceil(Threshold_Amount[0].Min_amount/productList[component.get('v.operationRow')].unitNetPrice)
                        }
                        // console.log("当前的数量",_qty,"_qtyitem_v",_qtyitem_v,"单价",productList[component.get('v.operationRow')].salesPrice,"当前的数量",productList[component.get('v.operationRow')].qty);
                        // productList[component.get('v.operationRow')].qty = _qty
                        productList[component.get('v.operationRow')].totalNetPrice = _qty * productList[component.get('v.operationRow')].salesPrice
                        productList[component.get('v.operationRow')].applicationMethod = applicationMethod
                        productList[component.get('v.operationRow')].standDiscount = standardDiscountValue
                        productList[component.get('v.operationRow')].Min_Total_Qty = Min_Total_Qty,
                        productList[component.get('v.operationRow')].Mixgoods_Qty = Threshold_Qty
                        productList[component.get('v.operationRow')].Mixgoods_Amount = Threshold_Amount
                        productList[component.get('v.operationRow')].Threshold_Qty = Mixgoods_Qty[productList[component.get('v.operationRow')].productId]
                        productList[component.get('v.operationRow')].Threshold_Amount = Mixgoods_Amount[productList[component.get('v.operationRow')].productId]
                        productList[component.get('v.operationRow')].UsePromotion = true
                        productList[component.get('v.operationRow')].useHeaderDiscount = true
                        productList[component.get('v.operationRow')].promotioncode = data.promotion.Promo_Code__c
                        productList[component.get('v.operationRow')].Min_Qty = item.product.Min_Qty__c|| item.product.Min_Qty_Per_Order__c || 0,
                        productList[component.get('v.operationRow')].qty = _qty
                        productList[component.get('v.operationRow')].Min_Amount = item.product.Min_Amount_Per_Order__c || 0,
                        productList[component.get('v.operationRow')].Min_Total_Amount = Min_Total_Amount || 0,
                        component.set('v.productList',productList)
                    }
                })
                let _ProductSelect = component.get('v.pupUp_BOGO_threshold')
                _ProductSelect.list = toOrderList
                _ProductSelect.detail = data.promotion.Promotion_short_describe__c
                _ProductSelect.type = "threshold"
                component.set('v.pupUp_ProductSelect',_ProductSelect)
            }else{
                // multiple push offering的操作
                component.set('v.bogoFlag', true);                                              // 打开上面已经设置信息product选择弹窗
                component.set('v.promotionModalFlag', false);                                   // 关闭Apply弹窗
                let hasPoolProductCombine = self.hasPoolProductChoice(data.ruleList[0].offeringList);
                component.set('v.hasPoolProductCombine', hasPoolProductCombine);
                component.set('v.signleProductOffering', []);
                let toOrderList = [];
                // 判断是threshold还是offering
                data.ruleList[0].offeringList.forEach(item=>{ 
                    if(hasPoolProductCombine) {
                        if(item.offering.RecordType.DeveloperName === 'Pool_of_Free_Goods_of_Customer_Choice') {
                            item.oproducts.forEach(i=>{
                                // 实际的折扣
                                let _discount = ""
                                if(i.product.Discount_Off__c || item.offering.Discount_Off__c){
                                    i.product.Discount_Off__c ? _discount = 100 - i.product.Discount_Off__c : ""
                                    i.offering.Discount_Off__c ? _discount = 100 - i.product.offering : ""
                                }
                                toOrderList.push({
                                    'key':key,
                                    'useHeaderDiscount': item.offering.Compatible_with_Header_Level_Promotion__c == "Yes" ? true : false,
                                    'type':'offering',
                                    "discounttype": item.offering.Promotion_Discount_Type__c ? item.offering.Promotion_Discount_Type__c : "",
                                    "applicationMethod": i.applicationMethod ? i.applicationMethod : "",
                                    "standDiscount": i.standardDiscountValue ? i.standardDiscountValue : "",
                                    "classify": item.offering.RecordType.DeveloperName,
                                    'productDescription': i.product.Product__r.Name,
                                    'productId': i.product.Product__c,
                                    'model': i.product.Product__r.ProductCode,
                                    'requestDate': null,
                                    'qty': item.offering.Free_Goods_Quantity__c || 1,
                                    'unitPerPallet': null,
                                    'promotionProductId': i.product.Id,                                                                                                                                                    // promotion上的产品id
                                    'promotionofferingId': item.offering.Id,                                                                                                                                               // promotion上的产品id
                                    'inventory': null,
                                    'listPrice': i.listPrice,
                                    'discount': 0,
                                    // 'unitNetPrice': i.product.Fixed_Price__c || item.offering.Fixed_Price__c ||i.salesPrice ?  (Math.round(((i.salesPrice* 100) - (100 * ((i.salesPrice * (100 - (_discount))/100)).toFixed(2)))/100)) : "0",
                                    'unitNetPrice': 0,
                                    "salesPrice" : i.salesPrice,
                                    // 'totalNetPrice': item.Largetype == "FreeGoods" ? 0 : "",
                                    'totalNetPrice': 0,
                                    'remark': "",
                                    'promotioncode': data.promotion.Promo_Code__c, // 标识test calvin
                                    'isPromotion': true,
                                    'select':false, //是否选中
                                    "Min_Qty": i.product.Min_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c || 0,
                                    "Max_Qty": i.product.Max_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c || 0,
                                    "Min_Amount": i.product.Min_Amount_Per_Order__c || item.offering.Minimum_Amount_Per_Order__c || 0,
                                    "Min_Total_Amount": Min_Total_Amount || 0,
                                    "showPromotionType": item.Largetype,
                                    "Fallshortofthreshold": false,
                                    'UsePromotion' : true,
                                })
                            })
                        }
                        else {
                            let singleProductOfferingList = [];
                            item.oproducts.forEach(i=>{
                                // 实际的折扣
                                let _discount = ""
                                if(i.product.Discount_Off__c || item.offering.Discount_Off__c){
                                    i.product.Discount_Off__c ? _discount = 100 - i.product.Discount_Off__c : ""
                                    i.offering.Discount_Off__c ? _discount = 100 - i.product.offering : ""
                                }
                                singleProductOfferingList.push({
                                    'key':key,
                                    'useHeaderDiscount': item.offering.Compatible_with_Header_Level_Promotion__c == "Yes" ? true : false,
                                    'type':'offering',
                                    "discounttype": item.offering.Promotion_Discount_Type__c ? item.offering.Promotion_Discount_Type__c : "",
                                    "applicationMethod": i.applicationMethod ? i.applicationMethod : "",
                                    "standDiscount": i.standardDiscountValue ? i.standardDiscountValue : "",
                                    "classify": item.offering.RecordType.DeveloperName,
                                    'productDescription': i.product.Product__r.Name,
                                    'productId': i.product.Product__c,
                                    'model': i.product.Product__r.ProductCode,
                                    'requestDate': null,
                                    'qty': item.offering.Free_Goods_Quantity__c || 1,
                                    'unitPerPallet': null,
                                    'promotionProductId': i.product.Id,                                                                                                                                                    // promotion上的产品id
                                    'promotionofferingId': item.offering.Id,                                                                                                                                               // promotion上的产品id
                                    'inventory': null,
                                    'listPrice': i.listPrice,
                                    'discount': 0,
                                    // 'unitNetPrice': i.product.Fixed_Price__c || item.offering.Fixed_Price__c ||i.salesPrice ?  (Math.round(((i.salesPrice* 100) - (100 * ((i.salesPrice * (100 - (_discount))/100)).toFixed(2)))/100)) : "0",
                                    'unitNetPrice': 0,
                                    "salesPrice" : i.salesPrice,
                                    // 'totalNetPrice': item.Largetype == "FreeGoods" ? 0 : "",
                                    'totalNetPrice': 0,
                                    'remark': "",
                                    'promotioncode': data.promotion.Promo_Code__c, // 标识test calvin
                                    'isPromotion': true,
                                    'select':false, //是否选中
                                    "Min_Qty": i.product.Min_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c || 0,
                                    "Max_Qty": i.product.Max_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c || 0,
                                    "Min_Amount": i.product.Min_Amount_Per_Order__c || item.offering.Minimum_Amount_Per_Order__c || 0,
                                    "Min_Total_Amount": Min_Total_Amount || 0,
                                    "showPromotionType": item.Largetype,
                                    "Fallshortofthreshold": false,
                                    'UsePromotion' : true,
                                })
                            })
                            component.set('v.signleProductOffering', singleProductOfferingList);
                        }
                    }
                    else {
                        item.oproducts.forEach(i=>{
                            // 实际的折扣
                            let _discount = ""
                            if(i.product.Discount_Off__c || item.offering.Discount_Off__c){
                                i.product.Discount_Off__c ? _discount = 100 - i.product.Discount_Off__c : ""
                                i.offering.Discount_Off__c ? _discount = 100 - i.product.offering : ""
                            }
                            toOrderList.push({
                                'key':key,
                                'useHeaderDiscount': item.offering.Compatible_with_Header_Level_Promotion__c == "Yes" ? true : false,
                                'type':'offering',
                                "discounttype": item.offering.Promotion_Discount_Type__c ? item.offering.Promotion_Discount_Type__c : "",
                                "applicationMethod": i.applicationMethod ? i.applicationMethod : "",
                                "standDiscount": i.standardDiscountValue ? i.standardDiscountValue : "",
                                "classify": item.offering.RecordType.DeveloperName,
                                'productDescription': i.product.Product__r.Name,
                                'productId': i.product.Product__c,
                                'model': i.product.Product__r.ProductCode,
                                'requestDate': null,
                                'qty': item.offering.Free_Goods_Quantity__c || 1,
                                'unitPerPallet': null,
                                'promotionProductId': i.product.Id,                                                                                                                                                    // promotion上的产品id
                                'promotionofferingId': item.offering.Id,                                                                                                                                               // promotion上的产品id
                                'inventory': null,
                                'listPrice': i.listPrice,
                                'discount': 0,
                                // 'unitNetPrice': i.product.Fixed_Price__c || item.offering.Fixed_Price__c ||i.salesPrice ?  (Math.round(((i.salesPrice* 100) - (100 * ((i.salesPrice * (100 - (_discount))/100)).toFixed(2)))/100)) : "0",
                                'unitNetPrice': 0,
                                "salesPrice" : i.salesPrice,
                                // 'totalNetPrice': item.Largetype == "FreeGoods" ? 0 : "",
                                'totalNetPrice': 0,
                                'remark': "",
                                'promotioncode': data.promotion.Promo_Code__c, // 标识test calvin
                                'isPromotion': true,
                                'select':false, //是否选中
                                "Min_Qty": i.product.Min_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c || 0,
                                "Max_Qty": i.product.Max_Qty_Per_Order__c || item.offering.Maximum_Quantity_Per_order__c || 0,
                                "Min_Amount": i.product.Min_Amount_Per_Order__c || item.offering.Minimum_Amount_Per_Order__c || 0,
                                "Min_Total_Amount": Min_Total_Amount || 0,
                                "showPromotionType": item.Largetype,
                                "Fallshortofthreshold": false,
                                'UsePromotion' : true,
                            })
                        })
                    }
                })
                let _ProductSelect = component.get('v.pupUp_BOGO_Offering')
                _ProductSelect.list = toOrderList
                _ProductSelect.detail = data.promotion.Promotion_short_describe__c
                _ProductSelect.type = "offering"
                component.set('v.pupUp_ProductSelect',_ProductSelect)
            }
        }
    },

    hasPoolProductChoice: function(offeringList) {
        let hasPoolProductCombine = false;
        let offeringTypes = [];
        offeringList.forEach(item => {
            if(!offeringTypes.includes(item.offering.RecordType.DeveloperName)) {
                offeringTypes.push(item.offering.RecordType.DeveloperName);
            }
        });

        if(offeringTypes.length > 1 && offeringTypes.includes('Pool_of_Free_Goods_of_Customer_Choice')) {
            hasPoolProductCombine = true;
        }
        return hasPoolProductCombine;
    },

    hasPoolProductChoice: function(offeringList) {
        let hasPoolProductCombine = false;
        let offeringTypes = [];
        offeringList.forEach(item => {
            if(!offeringTypes.includes(item.offering.RecordType.DeveloperName)) {
                offeringTypes.push(item.offering.RecordType.DeveloperName);
            }
        });

        if(offeringTypes.length > 1 && offeringTypes.includes('Pool_of_Free_Goods_of_Customer_Choice')) {
            hasPoolProductCombine = true;
        }
        return hasPoolProductCombine;
    },

    // promotion添加到order列表
    changeOrderList : function (component, orderList, productList) {
        let self = this;
        let index = component.get('v.operationRow');
        orderList.splice(index + 1, 0, ...productList);
        component.set('v.thresholdAndoffering',[])
        component.set('v.productList', [...orderList]);
        let _productList = component.get('v.productList')
        self.getTotalNumber(component,component.get('v.productList'))
    },
    // 检查产品是否有推荐配件数据
    checkRecommendedAccessories: function(component, productInfo, index) {
        // 确保有产品模型信息
        if (!productInfo || (!productInfo.Order_Model__c && !productInfo.model)) {
            let productList = component.get('v.productList');
            let newProductList = JSON.parse(JSON.stringify(productList));
            newProductList[index].hasRecommendedAccessories = false;
            component.set('v.productList', newProductList);
            return;
        }

        var action = component.get("c.GetAccessProductInfo");
        action.setParams({
            ProductModel: productInfo.Order_Model__c || productInfo.model,
            CustomerId: component.get('v.customerId'),
            RecordTypeName: component.get('v.recordTypeName'),
            WareHouse: component.get('v.warehouse'),
            PricingDate: component.get('v.pricingDate'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let productList = component.get('v.productList');
                let newProductList = JSON.parse(JSON.stringify(productList));

                // 检查是否有推荐配件数据
                if (result && result !== 'null' && result !== null) {
                    var parsedResult = JSON.parse(result);
                    newProductList[index].hasRecommendedAccessories = (parsedResult && parsedResult.length > 0);
                } else {
                    newProductList[index].hasRecommendedAccessories = false;
                }

                component.set('v.productList', newProductList);
            } else {
                // 如果出错，默认设置为false
                let productList = component.get('v.productList');
                let newProductList = JSON.parse(JSON.stringify(productList));
                newProductList[index].hasRecommendedAccessories = false;
                component.set('v.productList', newProductList);
            }
        });
        $A.enqueueAction(action);
    },

    // 检查产品是否有Related & Substitute数据
    checkRelatedSubstitute: function(component, productInfo, index) {
        // 确保有产品模型信息
        if (!productInfo || (!productInfo.Order_Model__c && !productInfo.model)) {
            let productList = component.get('v.productList');
            let newProductList = JSON.parse(JSON.stringify(productList));
            newProductList[index].hasRelatedSubstitute = false;
            component.set('v.productList', newProductList);
            return;
        }

        var action = component.get("c.getRelatedAndSubstituteProducts");
        action.setParams({
            ProductModel: productInfo.Order_Model__c || productInfo.model,
            CustomerId: component.get('v.customerId'),
            WareHouse: component.get('v.warehouse'),
            PricingDate: component.get('v.pricingDate'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let productList = component.get('v.productList');
                let newProductList = JSON.parse(JSON.stringify(productList));

                // 检查是否有Related或Substitute数据
                if (result && result !== 'null' && result !== null) {
                    var parsedResult = JSON.parse(result);
                    var hasRelatedOrSubstitute = false;

                    // 检查是否有related products
                    if ('relatedProducts' in parsedResult) {
                        var relatedProducts = JSON.parse(parsedResult['relatedProducts']);
                        if (relatedProducts && relatedProducts.length > 0) {
                            hasRelatedOrSubstitute = true;
                        }
                    }

                    // 检查是否有substitute products
                    if ('substituteProducts' in parsedResult) {
                        var substituteProducts = JSON.parse(parsedResult['substituteProducts']);
                        if (substituteProducts && substituteProducts.length > 0) {
                            hasRelatedOrSubstitute = true;
                        }
                    }

                    newProductList[index].hasRelatedSubstitute = hasRelatedOrSubstitute;
                } else {
                    newProductList[index].hasRelatedSubstitute = false;
                }

                component.set('v.productList', newProductList);
            } else {
                // 如果出错，默认设置为false
                let productList = component.get('v.productList');
                let newProductList = JSON.parse(JSON.stringify(productList));
                newProductList[index].hasRelatedSubstitute = false;
                component.set('v.productList', newProductList);
            }
        });
        $A.enqueueAction(action);
    },

    // 获取配件列表
    getAccessoriesList : function(component, productInfo){
        var action = component.get("c.GetAccessProductInfo");
        action.setParams({
            ProductModel: productInfo.model,
            CustomerId: component.get('v.customerId'),
            RecordTypeName: component.get('v.recordTypeName'),
            WareHouse: component.get('v.warehouse'),
            PricingDate: component.get('v.pricingDate'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(result), 'result=================');
                // 是否存在配件
                if (result && result.length) {
                    let accList = [];
                    result.forEach((accItem, accIndex)=>{
                        accList.push({
                            id: accItem.productId,
                            no: accIndex + 1,
                            productDescription: accItem.ProductDescription,
                            originalProductName: productInfo.productDescription,
                            model: accItem.Model,
                            requestDate: component.get('v.expectedDeliveryDate'),
                            qty: null,
                            rowInfo: accItem,
                            unitPerPallet: accItem.Qty == 0 ? null : accItem.Qty,
                        })
                    });
                    component.set('v.accessoriesModalFlag', true);
                    component.set('v.accessoriesData', accList);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": 'There are no related accessories on this product',
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },

    getRelatedSubstituteList: function(component, productInfo, index) {
        component.set('v.isBusy', true);
        let self = this;
        let action = component.get("c.getRelatedAndSubstituteProducts");
        action.setParams({
            ProductModel: productInfo.model,
            CustomerId: component.get('v.customerId'),
            WareHouse: component.get('v.warehouse'),
            PricingDate: component.get('v.pricingDate'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function(response){
            let state = response.getState();
            if(state === 'SUCCESS') {
                let result = JSON.parse(response.getReturnValue());
                let hasRelatedOrSubstitute = false;
                if('relatedProducts' in result) {
                    hasRelatedOrSubstitute = true;

                    let accList = [];
                    let relatedProducts = JSON.parse(result['relatedProducts']);
                    relatedProducts.forEach((accItem, accIndex)=>{
                        accList.push({
                            id: accItem.productId,
                            no: accIndex + 1,
                            productDescription: accItem.ProductDescription,
                            originalProductName: productInfo.productDescription,
                            model: accItem.Model,
                            requestDate: component.get('v.expectedDeliveryDate'),
                            qty: null,
                            rowInfo: accItem,
                            unitPerPallet: accItem.Qty == 0 ? null : accItem.Qty,
                        })
                    });
                    component.set('v.relatedSubstituteModalFlag', true);
                    component.set('v.relatedData', accList);
                }
                if('substituteProducts' in result) {
                    hasRelatedOrSubstitute = true;

                    let accList = [];
                    let substituteProducts = JSON.parse(result['substituteProducts']);
                    let replacementModels = [];
                    substituteProducts.forEach((accItem, accIndex)=>{
                        if(index) {
                            accItem['replaceIndex'] = index;
                        }

                        let accInfo = {
                            id: accItem.productId,
                            no: accIndex + 1,
                            productDescription: accItem.ProductDescription,
                            originalProductName: productInfo.productDescription,
                            model: accItem.Model,
                            requestDate: component.get('v.expectedDeliveryDate'),
                            qty: null,
                            rowInfo: accItem,
                            unitPerPallet: accItem.Qty == 0 ? null : accItem.Qty
                        };

                        accList.push(accInfo);
                        replacementModels.push(accItem.Model);
                    });

                    if(productInfo.productInactive) {
                        productInfo.reminder = $A.get("$Label.c.CCM_ProductInactiveReminder") + ' ' + replacementModels.join(', ');
                    }
                    else if(productInfo.inventory === 'Red') {
                        productInfo.reminder = $A.get("$Label.c.CCM_ProductLackOfReminder") + ' ' + replacementModels.join(', ');
                    }

                    let newProductList = component.get('v.productList');
                    newProductList[index] = productInfo;
                    component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));

                    component.set('v.relatedSubstituteModalFlag', true);
                    component.set('v.substituteData', accList);
                }

                if(!hasRelatedOrSubstitute) {
                    let toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": 'There are no related / substitute on this product',
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
            }
            else {
                let toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            if(index) {
                // 检查是否有推荐配件数据
                self.checkRecommendedAccessories(component, productInfo, index);
                // 检查是否有Related & Substitute数据
                self.checkRelatedSubstitute(component, productInfo, index);
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },

    // 获取关联列表
    getRelatedList : function(component, productInfo){
        var action = component.get("c.GetRelatedProductInfo");
        action.setParams({
            ProductModel: productInfo.model,
            CustomerId: component.get('v.customerId'),
            WareHouse: component.get('v.warehouse'),
            PricingDate: component.get('v.pricingDate'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(result), 'result=================');
                // 是否存在关联产品
                if (result && result.length) {
                    let accList = [];
                    result.forEach((accItem, accIndex)=>{
                        accList.push({
                            id: accItem.productId,
                            no: accIndex + 1,
                            productDescription: accItem.ProductDescription,
                            originalProductName: productInfo.productDescription,
                            model: accItem.Model,
                            requestDate: component.get('v.expectedDeliveryDate'),
                            qty: null,
                            rowInfo: accItem,
                            unitPerPallet: accItem.Qty == 0 ? null : accItem.Qty,
                        })
                    });
                    component.set('v.relatedSubstituteModalFlag', true);
                    component.set('v.relatedData', accList);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": 'There are no related on this product',
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取替换品列表
    getSubstituteList : function(component, productInfo){
        var action = component.get("c.GetSubstituteProductInfo");
        action.setParams({
            ProductModel: productInfo.model,
            CustomerId: component.get('v.customerId'),
            WareHouse: component.get('v.warehouse'),
            PricingDate: component.get('v.pricingDate'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(result), 'result=================');
                if (result && result.length) {
                    let accList = [];
                    result.forEach((accItem, accIndex)=>{
                        accList.push({
                            id: accItem.productId,
                            no: accIndex + 1,
                            productDescription: accItem.ProductDescription,
                            originalProductName: productInfo.productDescription,
                            model: accItem.Model,
                            requestDate: component.get('v.expectedDeliveryDate'),
                            qty: null,
                            rowInfo: accItem,
                            unitPerPallet: accItem.Qty == 0 ? null : accItem.Qty,
                        })
                    });
                    component.set('v.relatedSubstituteModalFlag', true);
                    component.set('v.substituteData', accList);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": 'There are no substitute on this product',
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取基本信息
    getBaseInfo: function (component) {
        var action = component.get("c.InitDate");
        action.setParams({
            PurchaseId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                component.set('v.paymentTerm', result[0].Payment_Term__c);
                component.set('v.freightTerm', result[0].Freight_Term__c);
                component.set('v.incoTerm', result[0].Incoterm__c);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取picklist
    getPicklist : function(component, type){
        var action = component.get("c.getPicklistOption");
        action.setParams({
            objectAPI: 'Sales_Program__c',
            fieldAPI: type === 'incoTerm' ? 'Incoterm__c' : 'Payment_Term__c',
            fifterString: '',
            isPortal: false
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                // component.set('v.statusOptions', JSON.parse(results));
                if (type === 'incoTerm') {
                    component.set('v.incoTermOptions', results);
                } else {
                    component.set('v.paymentTermOptions', results);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    // 获取运费
    getQueryCustomerType: function (component) {
        var action = component.get("c.QueryCustomerType");
        action.setParams({
            CustomerId: component.get('v.customerId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                component.set('v.customerType', result);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取order数据
    getPODetailInfo : function(component) {
        let self = this;
        var action = component.get('c.QueryPirchaseAndItemInfo');
        action.setParams({
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            IsProtal: false,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            if (state === 'SUCCESS') {
                component.set('v.PodetailInfo', result);
                // 判断draft状态是否有保存当前步骤
                if ((component.get('v.actionType') === 'draft' && Number(result.MaxStep) >= 2) || component.get('v.actionType') === 'edit') {
                    // 回填参数
                    component.set('v.paymentTerm', result.PaymentTerm);
                    component.set('v.incoTerm', result.IncoTerm);
                    if (result.PaymentTerm || result.IncoTerm) {
                        // 获取品牌相关信息
                        self.getBrandInfo(component);
                    }
                    component.set('v.expectedDeliveryDate', result.ExpectedDeliveryDate);
                    component.set('v.productList', result.lstPurchaseOrderItem);
                    // product 回填
                    let productArr = [];
                    // 判断是否批量上传
                    if (component.get('v.actionType') === 'edit' && !result.selectProductJson && result.lstDetailProductINfo.length) {
                        result.lstDetailProductINfo.forEach((batchItem, batchIndex)=>{
                            let newProductItem = {
                                'productDescription': batchItem.ProductDescription,
                                'productId': batchItem.productId,
                                'model': batchItem.Model,
                                'requestDate': component.get('v.expectedDeliveryDate'),
                                'qty': batchItem.Qty,
                                'unitPerPallet': batchItem.defaultQty,
                                'inventory': 'Green',
                                'listPrice': batchItem.listPrice,
                                'discount': batchItem.Discount,
                                'standDiscount': batchItem.standDiscount,
                                'applicationMethod': batchItem.applicationMethod,
                                'unitNetPrice': batchItem.unitNetPrice,
                                'totalNetPrice': batchItem.TotalNetPrice,
                                'remark': '',
                                'salesPrice':batchItem.SalesPrice,
                                'promotioncode': '',
                                'isPromotion': false,
                                'key': batchIndex + '',
                                'type':'MainProduct',
                                'isKit': (batchItem.filteredProducts && batchItem.filteredProducts.length) ? true : false,
                                'filteredProducts': batchItem.filteredProducts,
                                'CurrentStatus': batchItem.CurrentStatus,
                                'MaxGreenLight': batchItem.MaxGreenLight,
                                'MaxYellowLight': batchItem.MaxYellowLight,
                                'expanded': false,
                            };
                            productArr.push(newProductItem);
                        });
                        // tool红绿灯处理
                        productArr.forEach((batchProductItem, batchProductIndex)=>{
                            if (batchProductItem.filteredProducts && batchProductItem.filteredProducts.length) {
                                let toolList = [];
                                batchProductItem.filteredProducts.forEach((batchToolItem, batchToolIndex)=>{
                                    let toolQty = batchToolItem.Units_Per_Master_Carton_EA__c * batchProductItem.qty;
                                    switch (batchToolItem.InvotoryInfo__c) {
                                        case 'green Light':
                                            batchToolItem.inventory = 'Green';
                                            if (toolQty <= batchToolItem.MaxGreenLight__c) {
                                                batchToolItem.inventory = 'Green';
                                            } else if (toolQty > batchToolItem.MaxGreenLight__c && toolQty <= batchToolItem.MaxYellowLight__c) {
                                                batchToolItem.inventory = 'Yellow';
                                            } else {
                                                batchToolItem.inventory = 'Red';
                                            };
                                            break;
                                        case 'yellow Light':
                                            batchToolItem.inventory = 'Yellow';
                                            break;
                                        case 'red Light':
                                            batchToolItem.inventory = 'Red';
                                            break;
                                        default:
                                            batchToolItem.inventory = 'Green';
                                            break;
                                    };
                                    toolList.push({
                                        id: batchToolItem.Id,
                                        productDescription: batchToolItem.Item_Description_EN__c,
                                        model: batchToolItem.Order_Model__c,
                                        requestDate: component.get('v.expectedDeliveryDate'),
                                        qty: toolQty,
                                        unitPerPallet: batchToolItem.Units_Per_Master_Carton_EA__c,
                                        inventory: batchToolItem.inventory,
                                        listPrice: 0,
                                        discount: 0,
                                        unitNetPrice: 0,
                                        totalNetPrice: 0,
                                        scale: batchToolItem.Units_Per_Master_Carton_EA__c,
                                        invotoryInfo: batchToolItem.InvotoryInfo__c,
                                        maxGreenLight: batchToolItem.MaxGreenLight__c,
                                        maxYellowLight: batchToolItem.MaxYellowLight__c,
                                    })
                                })
                                batchProductItem.filteredProducts = JSON.parse(JSON.stringify(toolList));
                            }
                            // 判断红绿灯
                            switch (batchProductItem.CurrentStatus) {
                                case 'green Light':
                                    if (result.qty <= batchProductItem.MaxGreenLight) {
                                        batchProductItem.inventory = 'Green';
                                    } else if (batchProductItem.qty > batchProductItem.MaxGreenLight && batchProductItem.qty <= result.MaxYellowLight) {
                                        batchProductItem.inventory = 'Yellow';
                                    } else {
                                        batchProductItem.inventory = 'Red';
                                    };
                                    break;
                                case 'yellow Light':
                                    batchProductItem.inventory = 'Yellow';
                                    break;
                                case 'red Light':
                                    batchProductItem.inventory = 'Red';
                                    break;
                                default:
                                    batchProductItem.inventory = 'Green';
                                    break;
                            };
                        })
                    } else {
                        let jsonParams = JSON.parse(result.selectProductJson);
                        productArr = jsonParams.productList;
                        component.set('v.headerDiscountList', jsonParams.headerDiscountList);
                        component.set('v.headerDiscount', jsonParams.headerDiscount);
                        component.set('v.woheaderdiscount',jsonParams.woheaderdiscount)
                        jsonParams.paymentTerm ? component.set('v.paymentTerm',jsonParams.paymentTerm) : ""
                        self.getTotalNumber(component, jsonParams.productList);
                    }
                    let warehouse = component.get('v.warehouse');
                    if (warehouse === 'China (DI)') {
                        // China (DI)全为黄灯
                        productArr.forEach((productItem)=>{
                            if (productItem.filteredProducts && productItem.filteredProducts.length) {
                                productItem.filteredProducts.forEach((wareToolItem)=>{
                                    wareToolItem.inventory = 'Yellow';
                                })
                            }
                            productItem.inventory = 'Yellow';
                        })
                    }
                    setTimeout(()=>{
                        // 价格计算
                        self.getTotalNumber(component, productArr);
                        component.set('v.productList', JSON.parse(JSON.stringify(productArr)));
                    },100);
                } else {
                    // 获取当前时间
                    const year = new Date().getFullYear().toString();
                    const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
                    const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
                    component.set('v.expectedDeliveryDate', `${year}-${month}-${day}`);
                    component.set('v.freightCost', 0);
                    let isDropAddressInFirst = component.get('v.isDropAddressInFirst');
                    if (isDropAddressInFirst) {
                        self.paymentOfdropAdOfCollectiveCustomer(component);
                    } else {
                        // 获取品牌相关信息
                        self.getBrandInfo(component);
                    }
                    self.addNewProductLine(component);
                }
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 校验部分 (点击mixgoods加减号或者输入值时触发)
    applypromotioncheck : function(component,index,newProductList){
        // 场景1:当前mixgoods不满足设置的最小数量         完成/校验1
        // 场景2:当前mixgoods不满足设置的最小金额         完成/校验2
        // 场景3:所有mixgoods总和不满足设置的最小数量     完成/校验3
        // 场景4:所有mixgoods总和不满足设置的最小金额     完成/校验4
        // 场景5:所有mixgoods不满足设置的最小数量         完成/校验5
        // whole order类型
        // 场景6:订单总数量未达到设置的值
        // 场景7:订单总金额未到达设置的值
        // Price Break类型
        // 检查所有mixgoods上的每一产品的条件,多个mixgoods达到不同的rule条件,取最低的rule
        // 任意mixgoods数量或价格改变,校验全部的条件,调整rule的使用
        let productList = JSON.parse(JSON.stringify(newProductList));

        let _qty             = newProductList[index].qty                                                                                                     //当前产品的数量
        let _totalNetPrice   = newProductList[index].totalNetPrice                                                                                           //当前产品的总价格
        let _min_qty_v       = newProductList[index].Min_Qty                                                                                                 //当前产品的最小数量
        let _min_amount_v    = newProductList[index].Min_Amount                                                                                              //当前产品的最小金额
        let _mixGoods_v      = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct");                              //当前产品的mixgoods
        let _total_qty_v     = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Min_Total_Qty              //当前promotion要求的最低数量(单个threshold)
        let _total_amount_v  = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Min_Total_Amount           //当前promotion要求的最低金额(单个threshold)
        let _pb_total_amount_v  = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Mixgoods_Amount         //当前promotion要求的最低金额(Price Break)
        let _pb_total_qty_v  = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Mixgoods_Qty               //当前promotion要求的最低金额(Price Break)
        // console.log("当前产品的数量",_qty,"当前产品的金额",_totalNetPrice);
        // console.log("当前产品的最小数量",_min_qty_v,"当前产品的最小金额",_min_amount_v,"当前产品的mixgoods",JSON.parse(JSON.stringify(_mixGoods_v)),"当前产品组的最小总数量",_total_qty_v,"当前产品组的最小总金额",_total_amount_v);
        let verify = true
        let pass = 0
        // 1===校验单当前产品的最小数量
        if(verify && Number(_qty) && Number(_qty) < Number(_min_qty_v)){
            component.set('v.errorinfo',"最小数量未通过"+"当前的数量"+_qty+"最小数量"+_min_qty_v);
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'Please purchase at least ' + newProductList[index].Min_Qty + ' to get the promotion offering ',
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            verify = false
            // newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "")
        }else{
            pass += 1
        }
        // 2===校验单个产品的最大数量
        if(verify && Number(newProductList[index].Max_Qty) && Number(_qty) > Number(newProductList[index].Max_Qty)){
            component.set('v.errorinfo',"最大数量未通过"+"当前的数量"+_qty+"最大数量"+newProductList[index].Max_Qty)
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'No more than the maximum, ' + newProductList[index].Max_Qty,
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            verify = false
            // newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "")
        }else{
            pass += 1
        }
        let mixgoods = productList.filter(item => item.key == newProductList[index].key && item.type != "offering" && item.promotioncode);
        // 3===校验所有mixgoods产品的最小数量
        if(verify && mixgoods.length != mixgoods.filter((item,i) => i==index ?  Number(item.qty) : Number(item.qty) >= item.Min_Qty).length){
            component.set('v.errorinfo',"当前promotion产品组最小数量未通过");
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'All Mixgoods Min_Qty',
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            verify = false
        }else{
            pass += 1
        }
        // 4===校验mixgoods最小总金额(类型为单个threshold)
        let Min_Total_Amount = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Min_Total_Amount
        if(verify && Min_Total_Amount){
            let Amount = 0;
            productList.filter(item => item.key == newProductList[index].key && item.type != "offering").forEach(item =>{
                Amount = Amount + (Number(item.totalNetPrice) || 0)
            })
            if(Number(Amount) < Number(Min_Total_Amount)){
                component.set('v.errorinfo',"最小总金额未通过"+"当前产品组金额"+Amount+"当前产品组最小总金额"+Min_Total_Amount);
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Warning",
                    "message": 'Please purchase at least ' + Min_Total_Amount + ' to get the promotion offering ',
                    "type": "warning",
                    "duration": "dismissible"
                }).fire();
                verify = false
                // newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "")
            }else{
                pass += 1
            }
        }else{
            pass += 1
        }
        // 5===校验最小总数量
        if(verify && productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Min_Total_Qty){
            let Min_Total_Quantity = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Min_Total_Qty
            let Qty = 0
            productList.filter(item => item.key == newProductList[index].key && item.type != 'offering').forEach(item =>{
                Qty += Number(item.qty);
            })
            if(Number(Qty) < Number(Min_Total_Quantity)){
                component.set('v.errorinfo',"Mixgoods最小数量未通过");
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Warning",
                    "message": 'The minimum total Qty of the current product does not meet this application offering, the minimum Qty is ' + Min_Total_Quantity,
                    "type": "warning",
                    "duration": "dismissible"
                }).fire();
                verify = false
                // newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "")
            }else{
                pass += 1
            }
        }else{
            pass += 1
        }
        // 6===校验当前mixgoods的最小数量
        let isCheapestGoods = newProductList[index].unitNetPrice
        if(newProductList[index].cheapest && newProductList[index].type != "offering"){
            isCheapestGoods = newProductList[index].salesPrice
        }
        if(verify && _min_amount_v && Number(_qty) < Math.ceil(_min_amount_v/isCheapestGoods)){
            component.set('v.errorinfo',"校验mixgoods的最小金额"+Math.ceil(_min_amount_v/isCheapestGoods));
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Warning",
                "message": 'Please purchase at least ' + _min_amount_v + ' to get the promotion offering ',
                "type": "warning",
                "duration": "dismissible"
            }).fire();
            verify = false
            // newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "")
        }else{
            pass += 1
        }
        // 开始多条Rule的校验
        if(pass == 6){
            component.set('v.errorinfo',"校验全部通过");
            newProductList.map(item => {
                if(item.key == newProductList[index].key && item.type == "MainProduct") {
                    item.hasPromotionError = false;
                }
            });
        }
        console.log("进入price break类型的校验","pass等于",pass,component.get('v.errorinfo'));
        let mainProduct = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0];
        if(mainProduct && ((mainProduct.Mixgoods_Qty && mainProduct.Mixgoods_Qty.length > 0) || (mainProduct.Mixgoods_Amount && mainProduct.Mixgoods_Amount.length > 0))){
            let _Mixgoods_Amount = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Mixgoods_Amount          // 每一层threshold的Amount // 如果有
            let _Mixgoods_Qty = productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Mixgoods_Qty                // 每一层threshold的Qty    // 如果有
            let _min_total_qty = 0                                                                                                                             // 累加mix goods Qty的初始值
            let _min_total_amount = 0                                                                                                                          // 累加mix goods Amount的初始值
            let _mixgoods = productList.filter(item => item.key == newProductList[index].key && item.type != "offering")                                       // mix goods总数量
            let allqty = 0                                                                                                                                     // mix goods Qty总量
            let allamount = 0                                                                                                                                  // mix goods Amount总量
            _mixgoods.forEach(_item => {
                allqty += Number(_item.qty);
                allamount = Number(allamount) + Number(Number(_item.qty) * ((_item.cheapest && _item.type != "offering") ? Number(_item.salesPrice) : Number(_item.unitNetPrice)))
            })
            // console.log("总数量",JSON.parse(JSON.stringify(_Mixgoods_Qty)),"总金额",JSON.parse(JSON.stringify(_Mixgoods_Amount)));
            let offeringindex = []
            if(productList.filter(item => item.key == newProductList[index].key).length - 1 > 0){
                productList.filter(item => item.key == newProductList[index].key && item.type != "offering").forEach(item =>{
                    _min_total_qty += Number(item.qty)
                    _min_total_amount += (Number(item.qty) * ((item.cheapest && item.type != "offering") ? item.salesPrice : item.unitNetPrice))
                    let num = "cc"
                    let verify = false
                    if(item.Threshold_Qty && item.Threshold_Qty.length && !item.Threshold_Qty.every(v => v == item.Threshold_Qty[0])){
                        // console.log("校验数量",item.qty);
                        item.Threshold_Qty.forEach((v,i) =>{
                            if(Number(item.qty) >= Number(v) && v){
                                num = i
                                verify = true
                            }
                        })
                    }else if(item.Threshold_Amount && item.Threshold_Amount.length && !item.Threshold_Amount.every(v => v == item.Threshold_Amount[0])){
                        // console.log("校验金额",Number(item.qty) * item.unitNetPrice,JSON.parse(JSON.stringify(item.Threshold_Amount)));
                        item.Threshold_Amount.forEach((v,i) =>{
                            let _qty = item.qty || 0
                            if(Number(_qty * ((item.cheapest && item.type != "offering") ? item.salesPrice : item.unitNetPrice)) >= Number(v)){
                                num = i
                                verify = true
                            }
                        })
                    }
                    verify ? offeringindex.push(num) : ""
                })
                // 校验总数
                if(_Mixgoods_Qty.length && !_Mixgoods_Qty.every(v => v.Max_qty == _Mixgoods_Qty[0].Min_qty)){
                    // console.log("有总数量为",_min_total_qty);
                    let num = "NotPass"
                    _Mixgoods_Qty.forEach((_item,i) =>{
                        if(Number(_item.Min_qty) <= Number(_min_total_qty) && Number(_item.Max_qty) > Number(_min_total_qty)){
                            num = i
                        }
                    })
                    offeringindex.push(num)
                // 校验总金额
                }else if(_Mixgoods_Amount.length && !_Mixgoods_Amount.every(v => v.Max_amount == _Mixgoods_Amount[0].Min_amount)){
                    // console.log("有总金额",JSON.parse(JSON.stringify(_Mixgoods_Amount)));
                    let num = "NotPass"
                    _Mixgoods_Amount.forEach((_item,i) =>{
                        if(Number(_item.Min_amount) <= Number(_min_total_amount) && Number(_item.Max_amount) > Number(_min_total_amount)){
                            num = i
                        }
                    })
                    offeringindex.push(num)
                }
            }else{
                // console.log("只有一个rule");
            }
            let validOffering = true;
            offeringindex.forEach(o=>{
                if(o === 'NotPass') {
                    newProductList.map(item => {
                        if(item.key == newProductList[index].key && item.type == "MainProduct") {
                            item.hasPromotionError = true;
                        }
                    });
                    validOffering = false;
                    let line_discounts = newProductList.filter(item => item.key == newProductList[index].key && item.linediscount && item.UseLineDiscountOff);
                    if(line_discounts && line_discounts.length > 0) {
                        newProductList.map(item => {
                            if(item.key == newProductList[index].key && item.type != "offering" && !item.Fallshortofthreshold) {
                                item.discount = 0;
                                item.unitNetPrice = Number(Number(item.salesPrice * ((100 - item.discount) / 100)).toFixed(2));
                                item.totalNetPrice = Number((item.unitNetPrice * item.qty).toFixed(2));
                            }
                        });
                    }
                    // newProductList.map(item => item.key == newProductList[index].key && item.type == "threshold" ? item.Fallshortofthreshold = true : "");
                    newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "");
                    newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.UseLineDiscountOff = false : "");
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Warning",
                        "message": 'Mix Goods Quantity / Amount not match thresholds',
                        "type": "warning",
                        "duration": "dismissible"
                    }).fire();
                }
            });
            if(!validOffering) {
                component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));
                return;
            }
            newProductList.map(item => item.key == newProductList[index].key && item.type == "threshold" ? item.Fallshortofthreshold = false : "");
            newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "");
            newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.UseLineDiscountOff = false : "");
            if(verify){
                let verify2 = true
                newProductList.map(item => {
                    if(item.cheapest){
                        item.discount = ""
                        item.unitNetPrice = item.salesPrice
                        item.totalNetPrice = item.qty * item.salesPrice
                        item.Max_Qty = ""
                        item.useHeaderDiscount = true
                    }
                    if(verify2 && item.cheapest && item.cheapest.ruleindex == Math.min(...offeringindex)){
                        let minPriceProduct = item.cheapest
                        let _unitNetPrice = Number((Number(item.salesPrice) - Number(minPriceProduct.discountoff/100 * item.salesPrice).toFixed(2)).toFixed(2))
                        item.discount = minPriceProduct.discountoff
                        item.unitNetPrice = _unitNetPrice
                        item.totalNetPrice = item.qty * _unitNetPrice
                        item.Max_Qty = minPriceProduct.maxqty
                        item.useHeaderDiscount = minPriceProduct.useHeaderDiscount == "Yes" ? true : false

                    }
                    if(item.key == newProductList[index].key && item.type == "offering" && item.ruleindex == Math.min(...offeringindex)){
                        // console.log("当前的Ruleindex",item.ruleindex,"当前ruleindex下的产品",JSON.stringify(item));
                        item.Fallshortofthreshold = false;
                        // Bundle_Products_Discount_Off or Discount_Off_Cheapest_Among_Products
                        if(item.linediscount) {
                            item.Fallshortofthreshold = true;
                            item.UseLineDiscountOff = true;
                        }
                    }
                });

                let line_discounts = newProductList.filter(item => item.key == newProductList[index].key && item.linediscount && item.UseLineDiscountOff);
                if(line_discounts && line_discounts.length > 0) {
                    // reset discount before apply new discount
                    newProductList.map(item => {
                        if(item.key == newProductList[index].key && item.type != "offering" && !item.Fallshortofthreshold) {
                            item.discount = 0;
                        }
                    });
                    newProductList.map(item => {
                        if(item.key == newProductList[index].key && item.type != "offering" && !item.Fallshortofthreshold) {
                            line_discounts.forEach(item_discount=>{
                                let discount1 = item_discount.discount;
                                if(item_discount.productId) {
                                    if(item_discount.productId === item.productId) {
                                        if(!item.discount) {
                                            item.discount = discount1;
                                        }
                                        else {
                                            item.discount = Number((100 - ((100 - item.discount) * (100 - discount1))/100).toFixed(2));
                                        }
                                    }
                                }
                                else {
                                    if(!item.discount) {
                                        item.discount = discount1;
                                    }
                                    else {
                                        item.discount = Number((100 - ((100 - item.discount) * (100 - discount1))/100).toFixed(2));
                                    }
                                }
                            });
                            // item.discount = bundleDiscountOff;
                            // item.unitNetPrice = Number(Number(item.salesPrice * ((100 - item.discount) / 100)).toFixed(2));
                            // item.totalNetPrice = Number((item.unitNetPrice * item.qty).toFixed(2));
                        }
                    });

                    newProductList.map(item => {
                        if(item.key == newProductList[index].key && item.type != "offering" && !item.Fallshortofthreshold) {
                            item.unitNetPrice = Number(Number(item.salesPrice * ((100 - item.discount) / 100)).toFixed(2));
                            item.totalNetPrice = Number((item.unitNetPrice * item.qty).toFixed(2));
                        }
                    });
                }
            }
            else {
                newProductList.map(item => item.key == newProductList[index].key && item.type == "threshold" ? item.Fallshortofthreshold = true : "");
                newProductList.map(item => {
                    if(item.key == newProductList[index].key && item.type == "MainProduct") {
                        item.hasPromotionError = true;
                    }
                });
                // let line_discounts = newProductList.filter(item => item.key == newProductList[index].key && item.linediscount && item.UseLineDiscountOff);
                // if(line_discounts && line_discounts.length > 0) {
                newProductList.map(item => {
                    if(item.key == newProductList[index].key && item.type != "offering") {
                        item.discount = 0;
                        item.unitNetPrice = Number(Number(item.salesPrice * ((100 - item.discount) / 100)).toFixed(2));
                        item.totalNetPrice = Number((item.unitNetPrice * item.qty).toFixed(2));
                    }
                });
                // }
            }
            // console.log("pass是多少",pass,"newProductList",JSON.stringify(!productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Mixgoods_Qty));
        }else if(!productList.filter(item => item.key == newProductList[index].key && item.type == "MainProduct")[0].Mixgoods_Qty && pass == 6){
            newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = false : "")
        }else if(pass == 6){
            newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = false : "")
        }else if(pass != 6){
            newProductList.map(item => item.key == newProductList[index].key && item.type == "offering" ? item.Fallshortofthreshold = true : "")
        }
        component.set('v.productList', JSON.parse(JSON.stringify(newProductList)));
    },
    getQuantityLimitInventoryCondition : function(component,uploadData,key) {
        var action = component.get("c.getQuantityLimitInventoryCondition")
        action.setParams({
            uploadData:JSON.stringify(uploadData)}
        );
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let productlist = component.get('v.productList')
                productlist.forEach(item => {
                    if(item.key == key){
                        
                    item.limit = result.promotion.filter(v => v.productId == item.productId && v.promotionproductid == item.promotionProductId).length ? result.promotion.filter(v => v.productId == item.productId && v.promotionproductid == item.promotionProductId)[0].available : ""
                    if(result.inventory[item.productId]){
                        // 判断红绿灯
                        switch (result.inventory[item.productId].CurrentStatus) {
                            case 'green Light':
                                if (item.qty <= result.inventory[item.productId].MaxGreenLight) {
                                    item.inventory = 'Green';
                                } else if (item.qty > result.inventory[item.productId].MaxGreenLight && result.inventory[item.productId].MaxYellowLight) {
                                    item.inventory = 'Yellow';
                                } else {
                                    item.inventory = 'Red';
                                };
                                break;
                            case 'yellow Light':
                                item.inventory = 'Yellow';
                                break;
                            case 'red Light':
                                item.inventory = 'Red';
                                break;
                            default:
                                item.inventory = 'Green';
                                break;
                        };
                        item.CurrentStatus = result.inventory[item.productId].CurrentStatus;
                        item.MaxGreenLight = result.inventory[item.productId].MaxGreenLight;
                        item.MaxYellowLight = result.inventory[item.productId].MaxYellowLight;
                    }
                    }
                })
                productlist = productlist.filter(item => (item.type == "offering" && item.limit == true) || item.type != "offering" || item.linediscount);
                component.set('v.productList',productlist)
                    // if(result){
                    //     component.set('v.productOptions', JSON.parse(result));
                    // }
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Error",
                        "message": response.getError()[0].message,
                        "type": "error"
                    }).fire();
                }
            });
        $A.enqueueAction(action);
    },
    // 获取customer上的product 列表
    getCustomerProduct: function (component) {
        var action = component.get("c.GetProductInfo");
        action.setParams({
            FilterString: '',
            CustomerId: component.get('v.customerId'),
            PricingDate: component.get('v.pricingDate'),
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                console.log(JSON.stringify(JSON.parse(result)), '获取customer上的product 列表===============');
                if(result){
                    component.set('v.productOptions', JSON.parse(result));
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 必填校验
    getValidation : function (component) {
        let valid = true;
        valid = valid && this.getElementRequiredError(component, 'paymentTerm');
        valid = valid && this.getElementRequiredError(component, 'incoTerm');

        return valid;
    },
    // 校验错误提示信息
    getElementRequiredError: function (component, ele) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = element.get('v.value');
        var valid = !!val;
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }

        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
    // 获取unit per pallet 数据
    productInformationById: function (component, recordId) {
        var action = component.get("c.getProductInformationById");
        action.setParams({
            recordId: recordId
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                let result = JSON.parse(response.getReturnValue());
                if (result[0].RecordType && result[0].RecordType.DeveloperName) {
                    let recordType = result[0].RecordType.DeveloperName;
                    if (recordType === 'TLS_VK') {
                        result[0].retailUnit = ''; 
                    } else {
                        result[0].retailUnit = 1;
                    }
                }
                component.set('v.palletInfo', result[0]);
                console.log(JSON.stringify(result), '获取unit per pallet 数据============');
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 根据 drop address 获取term
    paymentOfdropAdOfCollectiveCustomer: function (component) {
        var action = component.get("c.paymentOfdropAdOfCollectiveCustomer");
        action.setParams({
            dropAdId: component.get('v.dropshipAddressId')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                let result = JSON.parse(response.getReturnValue());
                console.log(JSON.stringify(result), 'paymentOfdropAdOfCollectiveCustomer==========');
                component.set('v.authBrandId', result.authBrandId);
                component.get('v.paymentTerm') ? "" : component.set('v.paymentTerm', result.PaymentTerm);
                component.set('v.paymentTerm_copy', result.PaymentTerm);
                component.set('v.freightTerm', result.freightTerm);
                component.set('v.incoTerm', result.Incoterm);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    scrollToBottom: function(component) {
        setTimeout(()=>{
            let tableBody = document.querySelector('.table-wrap');
            tableBody.scrollTo({top: tableBody.scrollHeight, behavior: "smooth"});
        }, 100);
    },
    addNewProductLine: function(component) {
        let productList = component.get('v.productList');
        let hasEmptyLine = false;
        productList.forEach(item=>{
            if(!item.productId) {
                hasEmptyLine = true;
            }
        });
        if(hasEmptyLine) {
            return;
        }
        const newProductItem = {
            'productDescription': null,
            'productId': null,
            'model': '',
            'requestDate': component.get('v.expectedDeliveryDate'),
            'qty': null,
            'unitPerPallet': '',
            'inventory': '',
            'listPrice': '',
            'discount': '',
            'unitNetPrice': '',
            'totalNetPrice': '',
            'remark': '',
            'promotioncode': '',
            'isPromotion': false,
            'key': Date.now().toString(),
            'type':'MainProduct',
            'isKit': false,
            'filteredProducts': [],
            'expanded': false,
            'productRecordType': null,
        };
        productList.push(newProductItem);
        component.set('v.productList', productList);
    },

    checkProductIsActive: function(productList) {
        let hasInactive = false;
        let inactiveProducts = [];
        productList.forEach(item=>{
            if(item.productInactive) {
                hasInactive = true;
                inactiveProducts.push(item.model);
            }
        });
        if(hasInactive) {
            let locale = $A.get("$Locale.language");
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Warning"),
                "message": locale === 'de' ? 'Artikel ' + inactiveProducts.join(', ') + ' sind nicht mehr verfügbar.' : 'Items ' + inactiveProducts.join(', ') + ' are no longer available.',
                "type": "warning",
                "duration": "dismissible"
            }).fire();
        }
        return hasInactive;
    }
})