({
    // add haibo
    // 获取当前环境
    getEnvironmentType : function(component) {
        let self = this;
        var action = component.get("c.IsPortal");
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            const state = response.getState();
            const res = response.getReturnValue();
            if (state === "SUCCESS") {
                component.set('v.isPortal', res);
                let claimId;
                if (!res) {
                    // CRM 端获取url中的参数
                    let urlParams = new URLSearchParams(window.location.search);
                    let accId = urlParams.get('0.customerId');
                    claimId = urlParams.get('0.claimId');
                    let claimType = urlParams.get('0.type');
                    let modelNumber = urlParams.get('0.modelNumber');
                    let modelNumberId = urlParams.get('0.modelNumberId');
                    let serialNumber = urlParams.get('0.serialNumber');
                    let userType = urlParams.get('0.userType');
                    let email = urlParams.get('0.email');
                    let isCopy = urlParams.get('0.isCopy');
                    component.set('v.claimType', claimType);
                    component.set('v.accId', accId);
                    component.set('v.claimId', claimId);
                    component.set('v.modelNumber', modelNumber);
                    component.set('v.emailAddress', email);
                    component.set('v.isCopy', isCopy);
                    component.set('v.modelNumberInfo', {
                        Name: modelNumber,
                        Id: modelNumberId
                    });
                    component.set('v.serialNumber', serialNumber);
                    if (modelNumber) {
                        // 获取model信息
                        self.queryModelNumber(component, modelNumber);
                    }
                    if (modelNumber && serialNumber) {
                        self.getReceiptInfo(component);
                    }
                    component.set('v.consumerType', userType);
                    component.set('v.emailAddress', email);
                    // CRM端直接展示invoice信息
                    component.set('v.ownerPermission', true);
                    component.set('v.viewPermission', false);
                    component.set('v.noPermission', false);
                    component.set('v.noWarranty', false);
                    component.set('v.permission', 'Owner');
                } else {
                    // portal端
                    let urlParams = new URLSearchParams(window.location.search);
                    let accId = urlParams.get('0.customerId');
                    claimId = urlParams.get('0.claimId');
                    let claimType = urlParams.get('0.type');
                    let modelNumber = urlParams.get('0.modelNumber');
                    let modelNumberId = urlParams.get('0.modelNumberId');
                    let serialNumber = urlParams.get('0.serialNumber');
                    let userType = urlParams.get('0.userType');
                    let email = urlParams.get('0.email');
                    let isCopy = urlParams.get('0.isCopy');
                    component.set('v.accId', accId);
                    component.set('v.claimType', claimType);
                    component.set('v.claimId', claimId);
                    component.set('v.modelNumber', modelNumber);
                    component.set('v.isCopy', isCopy);
                    component.set('v.emailAddress', email);
                    component.set('v.serialNumber', serialNumber);
                    component.set('v.modelNumberInfo', {
                        Name: modelNumber,
                        Id: modelNumberId
                    });
                    component.set('v.serialNumber', serialNumber);
                    if (modelNumber) {
                        // 获取model信息
                        self.queryModelNumber(component, modelNumber);
                    }
                    if (modelNumber && serialNumber) {
                        self.getReceiptInfo(component);
                    }
                    component.set('v.consumerType', userType);
                    component.set('v.emailAddress', email);
                }
                // 获取用户信息
                self.getCustomerInformation(component, res);
                
                // get receipt link if no warranty
                self.getReceiptIdWithoutWarranty(component, claimId);
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
                component.set('v.isBusy', false);
            }
        });
        $A.enqueueAction(action);
    },
    // 必填校验
    getValidation : function (component, type) {
        let self = this;
        if (type == 'Scenario 1') {
            let valid1 = self.getElementRequiredError(component, 'dropOffDate');
            let valid2 = self.getElementRequiredError(component, 'repairDate');
            let valid3 = self.getElementRequiredError(component, 'brand');
            let valid4 = self.getElementRequiredError(component, 'description');
            return (valid1 && valid2 && valid3 && valid4);
        } else if (type == 'Scenario 3') {
            let overTimeHour = component.get('v.overTimeHour');
            let valid1 = self.getElementRequiredError(component, 'dropOffDate');
            let valid2 = self.getElementRequiredError(component, 'repairDate');
            let valid3 = self.getElementRequiredError(component, 'brand');
            let valid4 = self.getElementRequiredError(component, 'description');
            let valid5 = self.getElementRequiredError(component, 'failureCode');
            if (overTimeHour > 0) {
                let valid6 = self.getElementRequiredError(component, 'overTimeDescription');
                return (valid1 && valid2 && valid3 && valid4 && valid5 && valid6);
            } else {
                return (valid1 && valid2 && valid3 && valid4 && valid5);
            }
        } else if (type == 'Scenario 4') {
            let valid1 = self.getElementRequiredError(component, 'dropOffDate');
            let valid2 = self.getElementRequiredError(component, 'repairDate');
            let valid3 = self.getElementRequiredError(component, 'brand');
            let valid4 = self.getElementRequiredError(component, 'description');
            let valid5 = self.getElementRequiredError(component, 'failureCode4');
            return (valid1 && valid2 && valid3 && valid4 && valid5);
        }
    },
    // 校验错误提示信息
    getElementRequiredError : function (component, ele) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = element.get('v.value');
        var valid = !!val;
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
    // 获取用户信息
    getCustomerInformation: function(component, isPortal) {
        let self = this;
        var action = component.get("c.getCustomerInformation");
        action.setParams({
            accId: isPortal ? '' : component.get('v.accId'),
        });
        action.setCallback(this, function (response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                const res = JSON.parse(response.getReturnValue());
                if (isPortal) {
                    // res.DistributorOrDealer = 'Distributor'; //delete
                    if (!component.get('v.accId')) {
                        component.set('v.accId', res.AccountId);
                    }
                    // portal端设置Service Information默认值
                    if (res.DistributorOrDealer == 'Distributor') {
                        component.set('v.replacementType', 'Credit Memo');
                    } else if (res.DistributorOrDealer == 'Dealer') {
                        component.set('v.replacementType', 'For a free tools');
                    }
                } 
                component.set('v.distributorOrDealer', res.DistributorOrDealer);
                if(res.DistributorOrDealer === 'Dealer' && res.Country === 'DE') {
                    component.set('v.batteryDiagnosticScope', true);
                }
                else {
                    component.set('v.batteryDiagnosticScope', false);
                }
                component.set('v.currencySymbol', res.CurrencyIsoCode);
                component.set('v.laborRate', Number(res.LaborRate));
                component.set('v.laborPriceBook', res.LaborPriceBook);
                component.set('v.needCalculateVAT', res.needCalculateTax);
                // 获取claim address
                self.getClaimAddress(component);
                if (component.get('v.claimId')) {
                    // 获取claim 数据
                    self.getClaimDetail(component);
                }
                else {
                    self.preFillInforFromEligibilityCheck(component);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // base info　字段校验提示
    getBaseInfoTips: function(component) {
        let self = this;
        // 校验必填字段
        let dropOffDate = component.get('v.dropOffDate');
        let repairDate = component.get('v.repairDate');
        let brand = component.get('v.brand');
        let modelNumber = component.get('v.modelNumber');
        if (dropOffDate && repairDate && brand && modelNumber) {
            var action = component.get("c.checkBasicInformation");
            action.setParams({
                emailAddress: component.get('v.emailAddress'),
                dropDate: dropOffDate,
                repairDate: repairDate,
                brand: brand,
                serialNumber: component.get('v.serialNumber'),
                modelnumber: modelNumber,
                accId: component.get('v.accId')
            });
            action.setCallback(this, function (response) {
                const state = response.getState();
                const res = JSON.parse(response.getReturnValue());
                if (state === "SUCCESS") {
                    if (res.isSuccess == 'False') {
                        var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": $A.get("$Label.c.CCM_Warning"),
                            "message": res.message.join(','),
                            "type": "Warning"
                        }).fire();
                    }
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            alert("ERROR: " + errors[0].message);
                        }
                    } else {
                        alert("ERROR: Unknown error");
                    }
                }
            });
            $A.enqueueAction(action);
        }
    },
    // 获取发票信息
    getReceiptInfo: function(component) {
        let self = this;
        // 校验model & sn
        let modelNumber = component.get('v.modelNumber');
        let serialNumber = component.get('v.serialNumber');
        let email = component.get('v.emailAddress');
        if (modelNumber && ((serialNumber && (serialNumber.length == 15 || serialNumber[0] == 'E')))) {
            var action = component.get("c.queryInvoice");
            action.setParams({
                modelNumber: modelNumber,
                serialNumber: serialNumber,
                email: email,
                accId: component.get('v.accId')
            });
            component.set('v.isBusy', true);
            action.setCallback(this, function (response) {
                const state = response.getState();
                const res = JSON.parse(response.getReturnValue());
                if (state === "SUCCESS") {
                    if (res.isSuccess == 'True') {
                        component.set('v.placeOfPurchase', res.data[0].placeOfPurchase);
                        component.set('v.purchaseDate', res.data[0].purchaseDate);
                        component.set('v.expiredDate', res.data[0].expiredDate);
                        component.set('v.permission', res.data[0].basicPermission);
                        component.set('v.consumerId', res.data[0].consumerId);
                        component.set('v.receiptName', res.data[0].receiptName);
                        component.set('v.receiptLink', res.data[0].receiptLink);
                        component.set('v.warrantyStatus', res.data[0].warrantyStatus);
                        // test
                        // component.set('v.receiptName', 'haibo-test');
                        // component.set('v.receiptLink', 'https://files-to-pim-20230422.s3.amazonaws.com/a1q7Y000009oDQ3QAMpwcPreview2023-07-3108-29-53.pdf');
                        // 判断是否portal
                        let isPortal = component.get('v.isPortal');
                        if (!isPortal) {
                            if (!res.data[0].emailCheck) {
                                component.set('v.emailValid', true);
                                component.set('v.ownerPermission', false);
                                component.set('v.viewPermission', false);
                                component.set('v.noPermission', false);
                                component.set('v.noWarranty', false);
                            } else {
                                component.set('v.emailValid', false);
                                component.set('v.ownerPermission', true);
                                component.set('v.viewPermission', false);
                                component.set('v.noPermission', false);
                                component.set('v.noWarranty', false);
                                component.set('v.permission', 'Owner');
                            }
                        } else {
                            // 判断是否含有warranty记录
                            if (res.data[0].warrantyId) {
                                component.set('v.noWarranty', false);
                                // component.set('v.showAdditionalForNonWarranty', false);
                                component.set('v.warrantyId', res.data[0].warrantyId);
                                if (!res.data[0].emailCheck) {
                                    component.set('v.emailValid', true);
                                    component.set('v.ownerPermission', false);
                                    component.set('v.viewPermission', false);
                                    component.set('v.noPermission', false);
                                } else {
                                    component.set('v.emailValid', false);
                                    // 根据权限展示发票
                                    switch(res.data[0].basicPermission) {
                                        case 'Owner':
                                            component.set('v.ownerPermission', true);
                                            component.set('v.viewPermission', false);
                                            component.set('v.noPermission', false);
                                            component.set('v.permission', 'Owner');
                                        break;
                                        case 'View':
                                            component.set('v.ownerPermission', false);
                                            component.set('v.viewPermission', true);
                                            component.set('v.noPermission', false);
                                            component.set('v.permission', 'View');
                                        break;
                                        case 'Null':
                                            component.set('v.ownerPermission', false);
                                            component.set('v.viewPermission', false);
                                            component.set('v.noPermission', true);
                                            component.set('v.permission', 'Null');
                                        break;
                                    }
                                }
                            } else {
                                component.set('v.warrantyId', '');
                                component.set('v.noWarranty', true);
                                // component.set('v.showAdditionalForNonWarranty', true);
                                component.set('v.ownerPermission', false);
                                component.set('v.viewPermission', false);
                                component.set('v.noPermission', false);
                                component.set('v.emailValid', false);
                            }
                        }
                        
                        
                    } else {
                        var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": $A.get("$Label.c.CCM_Warning"),
                            "message": res.message.join(','),
                            "type": "Warning"
                        }).fire();
                        component.set('v.warrantyId', '');
                        component.set('v.noWarranty', true);
                        // component.set('v.showAdditionalForNonWarranty', true);
                        component.set('v.ownerPermission', false);
                        component.set('v.viewPermission', false);
                        component.set('v.noPermission', false);
                        component.set('v.emailValid', false);
                    }
                    self.isAdditionalRequired(component);
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            alert("ERROR: " + errors[0].message);
                        }
                    } else {
                        alert("ERROR: Unknown error");
                    }
                }
                component.set('v.isBusy', false);
            });
            $A.enqueueAction(action);
        }
    },
    // 获取发票预览url
    showReceiptUrl : function(component, receiptLink){
        const self = this;
        const action = component.get("c.getAWSSignedURL");
        action.setParams({
            url: receiptLink
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                const res = response.getReturnValue();
                window.open(res, '_blank');
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 发票转base64
    fileByBase64ForReceipt : function(component, file, fileType) {
        const self = this;
        const reader = new FileReader();
        // 传入一个参数对象即可得到基于该参数对象的文本内容
        reader.readAsDataURL(file[0]);
        reader.onload = function (e) {
            // target.result 该属性表示目标对象的DataURL
            let fileStr = e.target.result;
            let index = fileStr.indexOf(',') + 1;
            let fileValue = fileStr.substring(index);
            // 先获取附件插入ID
            self.getReceiptId(component, fileValue, file[0].name, fileType);
        };
    },
    // 先获取附件插入ID
    getReceiptId : function (component, fileValue, fileName, fileType) {
        const self = this;
        const action = component.get("c.ReceiptJunkPreCreate");
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            let res = response.getReturnValue();
            if (state === "SUCCESS") {
                let receiptId = res;
                self.getReceiptUrl(component, fileValue, fileName, fileType, receiptId);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
                component.set('v.isBusy', false);
            }
        });
        $A.enqueueAction(action);
    },
    // 获取发票第三方Url
    getReceiptUrl : function (component, fileValue, fileName, fileType, receiptId) {
        const self = this;
        const action = component.get("c.UploadReceiptToAws");
        action.setParams({
            idStr: receiptId,
            receiptName: fileName,
            receiptType: fileType,
            receiptId: fileValue
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            let res = response.getReturnValue();
            if (state === "SUCCESS") {
                if (res.code == 200) {
                    component.set('v.receiptUploadInfo', {
                        fileName: fileName,
                        fileUrl: res.message
                    });
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": res.message,
                        "type": "error"
                    }).fire();
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 判断当前Scenario
    changeScenario: function(component) {
        let self = this;
        let serviceType = component.get('v.serviceType');
        let replacementType = component.get('v.replacementType');
        let repairType = component.get('v.repairType');
        let partsItemList = [];
        let modelProductInfo = component.get('v.modelProductInfo');
        let partProductInfo = component.get('v.partProductInfo');
        if (serviceType == 'Replacement') {
            if (replacementType == 'Credit Memo') {
                component.set('v.scenarioType', 'Scenario 1');
            } else {
                component.set('v.scenarioType', 'Scenario 2');
            }
            // 添加到parts list中
            partsItemList = [
                {
                    partInfo: {
                        Name: modelProductInfo.productName,
                        Id: modelProductInfo.productId,
                    },
                    partName: modelProductInfo.productName,
                    itemNumber: modelProductInfo.modelNumber,
                    quantity: 1,
                    unitPrice: modelProductInfo.unitPrice,
                    total: Number(modelProductInfo.unitPrice) * 1,
                    noCalculate: modelProductInfo.noCalculate,
                    type: 'masterProduct'
                }
            ];
            component.set('v.partsItemList', [...partsItemList]);
            component.set('v.isBatteryDiagnosticFee', false);
        } else {
            if (repairType == 'Parts') {
                component.set('v.scenarioType', 'Scenario 3');
                component.set('v.overTimeHour', 0);
                let failureCode = component.get('v.failureCode');
                let isBatteryDiagnosticFee = false;
                let batteryDiagnosticScope = component.get('v.batteryDiagnosticScope');
                if(failureCode === '0 - Battery/Charger' && batteryDiagnosticScope) {
                    isBatteryDiagnosticFee = true;
                    component.set('v.diagnosticFee', component.get('v.batteryDiagnosticFee'));
                }
                component.set('v.isBatteryDiagnosticFee', isBatteryDiagnosticFee);
                if(isBatteryDiagnosticFee && partProductInfo.productId !== 'Diagnostic Flat Rate') {
                    partsItemList.push({
                        partInfo: {
                            Name: 'Diagnostic Flat Rate',
                            Id: 'Diagnostic Flat Rate',
                        },
                        partName: 'Diagnostic Flat Rate',
                        itemNumber: 'Diagnostic Flat Rate',
                        quantity: 1,
                        unitPrice: component.get('v.batteryDiagnosticFee'),
                        total: component.get('v.batteryDiagnosticFee'),
                        noCalculate: false,
                        type: 'batterydiagnostic',
                        laborTime: 0,
                    });
                }
                if (partProductInfo.productId) {
                    // 添加到parts list中
                    partsItemList.push({
                        partInfo: {
                            Name: partProductInfo.productName,
                            Id: partProductInfo.productId,
                        },
                        partName: partProductInfo.productName,
                        itemNumber: partProductInfo.modelNumber,
                        quantity: 1,
                        unitPrice: partProductInfo.unitPrice,
                        total: Number(partProductInfo.unitPrice) * 1,
                        noCalculate: partProductInfo.noCalculate,
                        type: partProductInfo.productId === 'Diagnostic Flat Rate' ? 'batterydiagnostic' : 'repairPart',
                        laborTime: partProductInfo.laborTime,
                    });
                }
                component.set('v.partsItemList', [...partsItemList]);
                self.calculateTotalLaborTime(component);
            } else {
                component.set('v.scenarioType', 'Scenario 4');
                component.set('v.isBatteryDiagnosticFee', false);
            }
        }
        self.calculateTotalHourAndPrice(component);
    },
    // 获取claim address
    getClaimAddress : function(component, type){
        var action = component.get("c.queryAddress");
        action.setParams({
            AccountId: component.get('v.accId'),
            type: 'ship',
            name: '',
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                // 设置默认ship address
                if (result.BillDefault && result.BillDefault.addressId) {
                    component.set('v.addressInfo', {
                        Id: result.BillDefault.addressId,
                        Name: result.BillDefault.name
                    })
                } else if (result.List.length) {
                    component.set('v.addressInfo', {
                        Id: result.List[0].addressId,
                        Name: result.List[0].name
                    })
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取picklist
    getPicklist : function(component, type){
        var action = component.get("c.getPicklistOption");
        action.setParams({
            objectAPI: 'Warranty_Claim__c',
            fieldAPI: type,
            fifterString: ''
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                // component.set('v.statusOptions', JSON.parse(results));
                if (type === 'Failure_Code__c') {
                    component.set('v.failureCodeOptions', results);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    // 计算总价
    calculateTotalHourAndPrice: function(component){
        let scenarioType = component.get('v.scenarioType');
        let actualHour = 0;
        // 实际时长
        let overTimeHour = component.get('v.overTimeHour');
        let laborRate = component.get('v.laborRate');
        // 人工成本
        let laborHourSubtotal = 0;
        // 材料成本
        let partsItemList = component.get('v.partsItemList');
        let partsTotal = 0;
        let totalPrice = 0;
        if (scenarioType == 'Scenario 1') {
            let modelProductInfo = component.get('v.modelProductInfo');
            component.set('v.diagnosticFee', 0);
            component.set('v.finialLaborHour', 0.00);
            let partsCost = (modelProductInfo.unitPrice ? Number(Number(modelProductInfo.unitPrice) * 1).toFixed(2) : 0.00);
            // VAT
            let VAT = 0;
            let needCalculateVAT = component.get('v.needCalculateVAT');
            if(needCalculateVAT) {
                VAT = Number(partsCost * 19 / 100).toFixed(2);
            }
            totalPrice = Number(Number(partsCost) + Number(VAT)).toFixed(2);
            component.set('v.partsCost', partsCost);
            component.set('v.VAT', VAT);
            component.set('v.laborCostSubtotal', 0.00);
            component.set('v.totalPrice', totalPrice);
        } else if (scenarioType == 'Scenario 2') {
            component.set('v.diagnosticFee', 0);
            component.set('v.finialLaborHour', 0.00);
            component.set('v.partsCost', 0.00);
            component.set('v.laborCostSubtotal', 0.00);
            component.set('v.totalPrice', 0.00);
        } else if (scenarioType == 'Scenario 3') {
            // 计算product list中的所有产品labor time * number
            let listLaborTime = 0;
            partsItemList.forEach((item)=>{
                listLaborTime = listLaborTime + (Number(item.laborTime) * Number(item.quantity));
            });
            if (Number(overTimeHour) < 1) {
                actualHour = listLaborTime;
            } else {
                actualHour = overTimeHour;
            }
            // 材料成本
            partsItemList.forEach((item)=>{
                if(item.type !== 'batterydiagnostic') {
                    partsTotal = Number(partsTotal) + Number(item.total);
                }
            })
            // VAT
            let VAT = 0;
            let needCalculateVAT = component.get('v.needCalculateVAT');
            if(needCalculateVAT) {
                VAT = Number(partsTotal * 19 / 100).toFixed(2);
            }
            // 人工成本
            laborHourSubtotal = (Number(actualHour)) * Number(laborRate);
            totalPrice = totalPrice + Number(component.get('v.diagnosticFee'));
            // 总价
            totalPrice = (Number(totalPrice) + Number(partsTotal) + Number(laborHourSubtotal) + Number(VAT)).toFixed(2);
            
            // 赋值
            component.set('v.finialLaborHour', actualHour);
            component.set('v.partsCost', partsTotal.toFixed(2));
            component.set('v.laborCostSubtotal', laborHourSubtotal.toFixed(2));
            component.set('v.VAT', VAT);
            component.set('v.totalPrice', totalPrice);
        } else if (scenarioType == 'Scenario 4') {
            // 人工成本
            laborHourSubtotal = (Number(overTimeHour)) * Number(laborRate);
            component.set('v.diagnosticFee', 0);
            // 总价
            totalPrice = Number(laborHourSubtotal).toFixed(2);
            component.set('v.finialLaborHour', overTimeHour);
            component.set('v.partsCost', 0.00);
            component.set('v.laborCostSubtotal', Number(laborHourSubtotal).toFixed(2));
            component.set('v.totalPrice', totalPrice);
        }
    },
    // 提交事件
    submitEvent: function(component, type){
        var action = component.get("c.submitClaim");
        let partsItemList = component.get('v.partsItemList');
        let partList = [];
        let isCopy = component.get('v.isCopy');
        if(component.get('v.scenarioType') === 'Scenario 3' || component.get('v.scenarioType') === 'Scenario 1') {
            partsItemList.forEach((item)=>{
                if(item.type !== 'batterydiagnostic') {
                    partList.push({
                        unitPrice: item.unitPrice,
                        type: item.type,
                        total: item.total,
                        quantity: item.quantity,
                        partName: item.partName,
                        partNumber: item.itemNumber,
                        partId: item.partInfo.Id,
                        laborTime: item.laborTime,
                        noCalculate: item.noCalculate,
                        claimItemId: (isCopy == 'true') ? '' : item.claimItemId,
                    });
                }
            });
        }    
        let params = {
            warrantyId: component.get('v.warrantyId') || '',
            userType: component.get('v.consumerType') || '',
            totalLaborHours: component.get('v.finialLaborHour').toString() || '',
            Total: Number(component.get('v.totalPrice')) || 0,
            shipAddressName: component.get('v.addressInfo').Name,
            shipAddressId: component.get('v.addressInfo').Id,
            serviceOption: component.get('v.serviceType'),
            serialNumber: component.get('v.serialNumber'),
            replacementOption: component.get('v.replacementType'),
            repairType: component.get('v.repairType') || '',
            repairDate: component.get('v.repairDate') || null,
            receiptName: component.get('v.receiptName'),
            receiptLost: false, // 不需要的字段
            receiptLink: component.get('v.receiptLink'),
            productName: component.get('v.productName'),
            productId: component.get('v.productId'),
            placeOfPurchase: component.get('v.placeOfPurchase'),
            paymentStatus: component.get('v.paymentStatus') || '', // ?
            partList: partList,
            modelNumber: component.get('v.modelNumber'),
            materialCost: Number(component.get('v.partsCost')) || 0,
            laborRate: component.get('v.laborRate').toString(),
            laborPriceBook: component.get('v.laborPriceBook'),
            laborCost: Number(component.get('v.laborCostSubtotal')) || 0,
            failureCode: (component.get('v.scenarioType') == 'Scenario 4' ? component.get('v.failureCode4') : component.get('v.failureCode')) || '',
            Explanation: component.get('v.overTimeDescription') || '',
            expiredDate: component.get('v.expiredDate') || null,
            emailAddress: component.get('v.emailAddress'),
            dropOffDate: component.get('v.dropOffDate') || null,
            DistributorOrDealer: component.get('v.distributorOrDealer'),
            description: component.get('v.description'),
            CustomerId: component.get('v.accId'),
            currencyIsoCode: component.get('v.currencySymbol'),
            consumerId: component.get('v.consumerId'),
            claimStatus: 'Submitted',
            brand: component.get('v.brand'),
            billAddressName: component.get('v.billAddressInfo').Name,
            billAddressId: component.get('v.billAddressInfo').Id,
            basicPermission: component.get('v.permission'),
            actualTime: Number(component.get('v.overTimeHour')) || '',
            // standardPartsHour: Number(component.get('v.laborTime')) || '',
            totalLaborHours: '',
            projectId: component.get('v.projectId'),
            projectCode: component.get('v.projectCode'),
            NewSerialNumber: component.get('v.newSerialNumber'),
            customerClaimReferenceNumber: component.get('v.customerClaimReferenceNumber'),
            diagnosticFee: component.get('v.diagnosticFee'),
            receiptContentVersionId: component.get('v.receiptContentVersionId'),
            requestDate: component.get('v.requestDate'),
            VAT: component.get('v.VAT')
        }
        
        let uploadToolImages = component.get('v.uploadToolImages');
        let fileIds = [];
        uploadToolImages.forEach(item=>{
            if(item.fileId) {
                fileIds.push(item.fileId);
            }
        });

        action.setParams({
            saveStr: JSON.stringify(params),
            claimId: (isCopy == 'true') ? '' : component.get('v.claimId'),
            fileIds: fileIds
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                if (result.isSuccess == 'True') {
                    let isPortal = component.get('v.isPortal');
                    if (isPortal) {
                        let url = '/s/servicehome';
                        window.open(url, '_self');
                    } else {
                        let url = '/lightning/n/Service_List';
                        window.open(url, '_self');
                    }
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": result.message,
                        "type": "error"
                    }).fire();
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取claim detail
    getClaimDetail: function(component){
        let self = this;
        var action = component.get("c.queryClaimDetail");
        action.setParams({
            claimId: component.get('v.claimId')
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results.isSuccess == 'True') {
                    // 详情赋值
                    component.set('v.warrantyId', results.Data.warrantyId);
                    component.set('v.consumerType', results.Data.userType);
                    component.set('v.finialLaborHour', results.Data.totalLaborHours);
                    component.set('v.totalPrice', results.Data.Total);
                    component.set('v.addressInfo', {Name: results.Data.shipAddressName, Id: results.Data.shipAddressId});
                    component.set('v.serviceType', results.Data.serviceOption);
                    component.set('v.serialNumber', results.Data.serialNumber);
                    component.set('v.replacementType', results.Data.replacementOption);
                    component.set('v.repairType', results.Data.repairType);
                    component.set('v.repairDate', results.Data.repairDate);
                    component.set('v.receiptName', results.Data.receiptName);
                    component.set('v.receiptLink', results.Data.receiptLink);
                    component.set('v.purchaseDate', results.Data.purchaseDate);
                    component.set('v.productName', results.Data.productName);
                    component.set('v.productId', results.Data.productId);
                    component.set('v.placeOfPurchase', results.Data.placeOfPurchase);
                    component.set('v.paymentStatus', results.Data.paymentStatus);
                    component.set('v.modelNumber', results.Data.modelNumber);
                    component.set('v.modelNumberInfo', {Name: results.Data.modelNumber, Id: results.Data.productId});
                    component.set('v.rejectComments', results.Data.rejectComments);
                    component.set('v.warrantyStatus', results.Data.warrantyStatus);
                    // if (results.Data.modelNumber) {
                    //     // 获取model信息
                    //     self.queryModelNumber(component, results.Data.modelNumber);
                    // }
                    component.set('v.partsCost', results.Data.materialCost);
                    let laborRate = component.get('v.laborRate');
                    if (!laborRate) {
                        component.set('v.laborRate', results.Data.laborRate);
                    }
                    if(results.Data.isMassUpload && results.Data.laborRate) {
                        component.set('v.laborRate', results.Data.laborRate);
                    }
                    component.set('v.laborCostSubtotal', results.Data.laborCost);
                    component.set('v.failureCode', results.Data.failureCode);
                    component.set('v.failureCode4', results.Data.failureCode);
                    component.set('v.overTimeDescription', results.Data.Explanation);
                    component.set('v.expiredDate', results.Data.expiredDate);
                    component.set('v.emailAddress', results.Data.emailAddress);
                    component.set('v.dropOffDate', results.Data.dropOffDate);
                    component.set('v.distributorOrDealer', results.Data.DistributorOrDealer);
                    component.set('v.description', results.Data.description);
                    component.set('v.currencySymbol', results.Data.currencyIsoCode);
                    component.set('v.claimStatus', results.Data.claimStatus);
                    component.set('v.brand', results.Data.brand);
                    component.set('v.billAddressInfo', {Name: results.Data.billAddressName, Id: results.Data.billAddressId});
                    component.set('v.overTimeHour', results.Data.actualTime);
                    // component.set('v.laborTime', results.Data.partList.length ? results.Data.partList[0].laborTime : '0');
                    component.set('v.laborTime', results.Data.standardPartsHour ? results.Data.standardPartsHour : '0');
                    component.set('v.accId', results.Data.CustomerId);
                    component.set('v.consumerId', results.Data.consumerId);
                    component.set('v.newSerialNumber', results.Data.NewSerialNumber);
                    component.set('v.customerClaimReferenceNumber', results.Data.customerClaimReferenceNumber);
                    component.set('v.VAT', results.Data.VAT);

                    let isBatteryDiagnosticFee = false;
                    let batteryDiagnosticScope = component.get('v.batteryDiagnosticScope');
                    if(results.Data.repairType === 'Parts' && results.Data.failureCode === '0 - Battery/Charger' && batteryDiagnosticScope) {
                        isBatteryDiagnosticFee = true;
                        component.set('v.isBatteryDiagnosticFee', isBatteryDiagnosticFee);
                    }

                    component.set('v.requestDate', results.Data.requestDate);
                    // 判断Repairable Parts
                    let partList = results.Data.partList;
                    let partsArr = [];
                    if (partList.length) {
                        if(isBatteryDiagnosticFee) {
                            partsArr.push({
                                partInfo: {Id: 'Diagnostic Flat Rate', Name: 'Diagnostic Flat Rate'},
                                partName: 'Diagnostic Flat Rate',
                                itemNumber: 'Diagnostic Flat Rate',
                                quantity: 1,
                                unitPrice: component.get('v.batteryDiagnosticFee'),
                                total: component.get('v.batteryDiagnosticFee'),
                                type: 'batterydiagnostic',
                                noCalculate: false,
                                laborTime: 0
                            });
                        }
                        partList.forEach((item)=>{
                            if (item.type == 'repairPart') {
                                component.set('v.repairablePartsInfo', {Name: item.partName, Id: item.partId});
                            }
                            partsArr.push({
                                partInfo: {Id: item.partId, Name: item.partName},
                                partName: item.partName,
                                itemNumber: item.partNumber,
                                quantity: Number(item.quantity),
                                unitPrice: Number(item.unitPrice),
                                total: Number(item.total),
                                type: item.type,
                                noCalculate: item.noCalculate,
                                claimItemId: item.claimItemId,
                                laborTime: item.laborTime
                            })
                        })
                        component.set('v.partsItemList', partsArr);
                    } else {
                        component.set('v.partsItemList', []);
                    }
                    component.set('v.diagnosticFee', results.Data.diagnosticFee);

                    let isCopy = component.get('v.isCopy');
                    if(isCopy !== 'true' && (partList.length > 0 || results.Data.repairType === 'Labor Time Only')) {
                        component.set('v.showNewPOButton', true);
                    }

                    let imageList = results.Data.imageList;
                    let uploadToolImages = [];
                    imageList.forEach(item=>{
                        uploadToolImages.push({
                            'fileName': item.fileName,
                            'fileId': item.fileId,
                            'versionUrl': item.versionUrl
                        });
                    });
                    component.set('v.uploadToolImages', uploadToolImages);

                    let isPortal = component.get('v.isPortal');
                    if (!isPortal) {
                        component.set('v.ownerPermission', true);
                        component.set('v.viewPermission', false);
                        component.set('v.noPermission', false);
                        component.set('v.noWarranty', false);
                        component.set('v.permission', 'Owner');
                    } else {
                        // 判断是否含有warranty记录
                        if (results.Data.warrantyId) {
                            component.set('v.noWarranty', false);
                            // component.set('v.showAdditionalForNonWarranty', false);
                            // 根据权限展示发票
                            switch(results.Data.basicPermission) {
                                case 'Owner':
                                    component.set('v.ownerPermission', true);
                                    component.set('v.viewPermission', false);
                                    component.set('v.noPermission', false);
                                    component.set('v.permission', 'Owner');
                                break;
                                case 'View':
                                    component.set('v.ownerPermission', false);
                                    component.set('v.viewPermission', true);
                                    component.set('v.noPermission', false);
                                    component.set('v.permission', 'View');
                                break;
                                case 'Null':
                                    component.set('v.ownerPermission', false);
                                    component.set('v.viewPermission', false);
                                    component.set('v.noPermission', true);
                                    component.set('v.permission', 'Null');
                                break;
                            }
                        } else {
                            component.set('v.noWarranty', true);
                            // component.set('v.showAdditionalForNonWarranty', true);
                            component.set('v.ownerPermission', false);
                            component.set('v.viewPermission', false);
                            component.set('v.noPermission', false);
                        }
                    }
                    self.isAdditionalRequired(component);
                    // 判断当前Scenario
                    let serviceType = component.get('v.serviceType');
                    let replacementType = component.get('v.replacementType');
                    let repairType = component.get('v.repairType');
                    if (serviceType == 'Replacement') {
                        if (replacementType == 'Credit Memo') {
                            component.set('v.scenarioType', 'Scenario 1');
                        } else {
                            component.set('v.scenarioType', 'Scenario 2');
                            if (!results.Data.NewSerialNumber) {
                                component.set('v.showNewSN', true);
                            } else {
                                component.set('v.showNewSN', false);
                            }
                        }
                    } else {
                        if (repairType == 'Parts') {
                            component.set('v.scenarioType', 'Scenario 3');
                        } else {
                            component.set('v.scenarioType', 'Scenario 4');
                        }
                    }
                    self.calculateTotalHourAndPrice(component);
                    self.getProjectInfoBySn(component);
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": res.message,
                        "type": "error"
                    }).fire();
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 保存草稿
    saveClaimAsDraft: function(component){
        var action = component.get("c.saveClaim");
        let partsItemList = component.get('v.partsItemList');
        let partList = [];
        let isCopy = component.get('v.isCopy');
        if(component.get('v.scenarioType') === 'Scenario 3' || component.get('v.scenarioType') === 'Scenario 1') {
            partsItemList.forEach((item)=>{
                if(item.type !== 'batterydiagnostic') {
                    partList.push({
                        unitPrice: item.unitPrice,
                        type: 'repairpart',
                        total: item.total,
                        quantity: item.quantity,
                        partNumber: item.itemNumber,
                        partId: item.partInfo.Id,
                        laborTime: item.laborTime,
                        claimItemId: (isCopy == 'true') ? '' : item.claimItemId,
                    });
                } 
            });
        }
        
        let params = {
            warrantyId: component.get('v.warrantyId') || '',
            userType: component.get('v.consumerType') || '',
            totalLaborHours: component.get('v.finialLaborHour') ? component.get('v.finialLaborHour').toString() : '',
            Total: Number(component.get('v.totalPrice')) || 0,
            shipAddressName: component.get('v.addressInfo') ? component.get('v.addressInfo').Name : "",
            shipAddressId: component.get('v.addressInfo') ? component.get('v.addressInfo').Id : "",
            serviceOption: component.get('v.serviceType') || '',
            serialNumber: component.get('v.serialNumber') || '',
            replacementOption: component.get('v.replacementType') || '',
            repairType: component.get('v.repairType') || '',
            repairDate: component.get('v.repairDate') || null,
            receiptName: component.get('v.receiptName') || '',
            receiptLost: false, // 不需要的字段
            receiptLink: component.get('v.receiptLink') || '',
            productName: component.get('v.productName') || '',
            productId: component.get('v.productId') || '',
            placeOfPurchase: component.get('v.placeOfPurchase') || '',
            paymentStatus: component.get('v.paymentStatus') || '', // ?
            partList: partList,
            modelNumber: component.get('v.modelNumber') || '',
            materialCost: Number(component.get('v.partsCost')) || 0,
            laborRate: component.get('v.laborRate').toString(),
            laborPriceBook: component.get('v.laborPriceBook'),
            laborCost: Number(component.get('v.laborCostSubtotal')) || 0,
            failureCode: component.get('v.scenarioType') == 'Scenario 4' ? component.get('v.failureCode4') : component.get('v.failureCode'),
            Explanation: component.get('v.overTimeDescription'),
            expiredDate: component.get('v.expiredDate') || null,
            emailAddress: component.get('v.emailAddress') || '',
            dropOffDate: component.get('v.dropOffDate') || null,
            DistributorOrDealer: component.get('v.distributorOrDealer') || '',
            description: component.get('v.description') || '',
            CustomerId: component.get('v.accId'),
            currencyIsoCode: component.get('v.currencySymbol'),
            consumerId: component.get('v.consumerId'),
            claimStatus: 'Draft',
            brand: component.get('v.brand') || '',
            billAddressName: component.get('v.billAddressInfo') ? component.get('v.billAddressInfo').Name : '',
            billAddressId: component.get('v.billAddressInfo') ? component.get('v.billAddressInfo').Id : '',
            basicPermission: component.get('v.permission') || '',
            actualTime: component.get('v.overTimeHour') ? component.get('v.overTimeHour').toString() : '',
            // standardPartsHour: component.get('v.laborTime').toString() || '',
            projectId: component.get('v.projectId'),
            projectCode: component.get('v.projectCode'),
            NewSerialNumber: component.get('v.newSerialNumber'),
            customerClaimReferenceNumber: component.get('v.customerClaimReferenceNumber'),
            diagnosticFee: component.get('v.diagnosticFee'),
            receiptContentVersionId: component.get('v.receiptContentVersionId'),
            requestDate: component.get('v.requestDate'),
            VAT: component.get('v.VAT')
        };

        let uploadToolImages = component.get('v.uploadToolImages');
        let fileIds = [];
        uploadToolImages.forEach(item=>{
            if(item.fileId) {
                fileIds.push(item.fileId);
            }
        });

        action.setParams({
            saveStr: JSON.stringify(params),
            claimId: (isCopy == 'true') ? '' : component.get('v.claimId'),
            fileIds: fileIds
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                if (result.isSuccess == 'True') {
                    let isPortal = component.get('v.isPortal');
                    if (isPortal) {
                        let url = '/s/servicehome';
                        window.open(url, '_self');
                    } else {
                        let url = '/lightning/n/Service_List';
                        window.open(url, '_self');
                    }
                } else {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": result.message,
                        "type": "error"
                    }).fire();
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 获取claim model number
    queryModelNumber : function(component, name){
        var action = component.get("c.queryModelNumber");
        action.setParams({
            accId: component.get('v.accId') || '',
            orderModel: name,
            pageNumber: 1,
            allPageSize: 500,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                let data = result.List;
                let modelNumberInfo = data[0];
                component.set('v.productId', modelNumberInfo.productId);
                component.set('v.productName', modelNumberInfo.productName);
                // 获取关联product信息
                component.set('v.modelProductInfo', {
                    unitPrice: modelNumberInfo.unitPrice,
                    productId: modelNumberInfo.productId,
                    productName: modelNumberInfo.productName,
                    noCalculate: modelNumberInfo.noCalculate,
                    modelNumber: modelNumberInfo.modelNumber,
                });
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取claim model number
    getProjectInfoBySn : function(component){
        let self = this;
        var action = component.get("c.getProjectInfo");
        action.setParams({
            snParam: component.get('v.serialNumber')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results == null){
                    component.set('v.isNeedProject', false);
                }else{
                    // 详情赋值
                    if (results.length > 0) {
                        component.set('v.projectList', results);
                        component.set('v.isNeedProject', true);
                    }
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    // 保存新sn
    saveNewSN : function(component){
        let self = this;
        var action = component.get("c.saveNewSN");
        action.setParams({
            calimId: component.get('v.claimId'),
            serialNumber: component.get('v.newSerialNumber')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results.isSuccess == 'true') {
                    let isPortal = component.get('v.isPortal');
                    if (isPortal) {
                        let url = '/s/servicehome';
                        window.open(url, '_self');
                    } else {
                        let url = '/lightning/n/Service_List';
                        window.open(url, '_self');
                    }
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },

    calculateTotalLaborTime: function(component) {
        let partItemList = component.get('v.partsItemList');
        let laborTime = 0;
        partItemList.forEach(item => {
            if(item.laborTime) {
                laborTime = laborTime + Number(item.laborTime);
            }
        });
        component.set('v.laborTime', laborTime);
    },

    preFillInforFromEligibilityCheck: function(component) {
        let self = this;
        let urlParams = new URLSearchParams(window.location.search);
        let serviceType = urlParams.get('servicetype');
        let isFromEligibilityCheck = false;
        let productId;
        let partIds = [];
        let partNum = {};
        if(serviceType === 'repair') {
            productId = urlParams.get('productId');
            let partParams = urlParams.get('partsIds').split(',');
            partParams.forEach(params=>{
                partIds.push(params.split(';')[0]);
                partNum[params.split(';')[0]] = params.split(';')[1];
            });
            isFromEligibilityCheck = true;
            component.set('v.serviceType', 'Repair');
        }
        else if(serviceType === 'replacement') {
            productId = urlParams.get('productId');
            isFromEligibilityCheck = true;
            component.set('v.serviceType', 'Replacement');
        }
        if(isFromEligibilityCheck) {
            let action = component.get("c.getProductInfo");
            action.setParams({
                productId: productId,
                partIds: partIds ? JSON.stringify(partIds) : null
            });
            action.setCallback(this, function(response){
                let state = response.getState();
                if(state === 'SUCCESS') {
                    let resultMap = JSON.parse(response.getReturnValue());
                    if('productInfo' in resultMap) {
                        let modelNumberInfo = resultMap.productInfo;
                        modelNumberInfo.Id = modelNumberInfo.productId;
                        component.set('v.modelNumberInfo', modelNumberInfo);
                        component.set('v.modelNumber', modelNumberInfo.modelNumber);
                        component.find('modelNumber').setInputVal(modelNumberInfo.modelNumber);
                        component.set('v.productId', modelNumberInfo.productId);
                        component.set('v.productName', modelNumberInfo.productName);
                        component.set('v.modelProductInfo', {
                            unitPrice: modelNumberInfo.unitPrice,
                            productId: modelNumberInfo.productId,
                            productName: modelNumberInfo.productName,
                            noCalculate: modelNumberInfo.noCalculate,
                            modelNumber: modelNumberInfo.modelNumber,
                        });
                        
                    }
                    if('partsInfo' in resultMap) {
                        let partsInfoList = JSON.parse(resultMap.partsInfo);
                        if(partsInfoList && partsInfoList.length > 0) {
                            let repairablePartsInfo = partsInfoList[0];
                            repairablePartsInfo.Id = repairablePartsInfo.productId;
                            component.set('v.repairablePartsInfo', repairablePartsInfo);
                            component.set('v.partProductInfo', {
                                unitPrice: repairablePartsInfo.unitPrice,
                                productId: repairablePartsInfo.productId,
                                productName: repairablePartsInfo.productName,
                                noCalculate: repairablePartsInfo.noCalculate,
                                modelNumber: repairablePartsInfo.modelNumber,
                                laborTime: repairablePartsInfo.laborTime,
                            });
                            component.find('repairableParts').setInputVal(repairablePartsInfo.productName);

                            let partsItemList = [];
                            partsInfoList.forEach(info=>{
                                let item = {};
                                item.partName = info.productName;
                                item.itemNumber = info.modelNumber;
                                item.unitPrice = info.unitPrice;
                                item.noCalculate = info.noCalculate;
                                item.laborTime = info.laborTime;
                                item.quantity = partNum[info.productId] || 1;
                                item.total = Number(info.unitPrice) * item.quantity;
                                item.type = 'repairPart';
                                item.partInfo = {
                                    Name: info.productName
                                };
                                partsItemList.push(item);
                            });
                            component.set('v.partsItemList', [...partsItemList]);
                            self.calculateTotalLaborTime(component);
                        }
                    }
                    self.calculateTotalHourAndPrice(component);
                }
            });
            $A.enqueueAction(action);
        }
    },

    getModelInfo: function(component, serialNumber) {
        let self = this;
        let action = component.get('c.getModelInfoBySN');
        let customerId = component.get('v.accId');
        action.setParams({'serialNumber': serialNumber, 'customerId': customerId});
        action.setCallback(this, function(response){
            let state = response.getState();
            if(state === 'SUCCESS') {
                let resultMap = response.getReturnValue();
                if(resultMap) {
                    resultMap = JSON.parse(resultMap);
                    if('productInfo' in resultMap) {
                        let modelNumberInfo = resultMap.productInfo;
                        modelNumberInfo.Id = modelNumberInfo.productId;
                        component.set('v.modelNumberInfo', modelNumberInfo);
                        component.set('v.modelNumber', modelNumberInfo.modelNumber);
                        component.find('modelNumber').setInputVal(modelNumberInfo.modelNumber);
                        component.set('v.productId', modelNumberInfo.productId);
                        component.set('v.productName', modelNumberInfo.productName);
                        component.set('v.modelProductInfo', {
                            unitPrice: modelNumberInfo.unitPrice,
                            productId: modelNumberInfo.productId,
                            productName: modelNumberInfo.productName,
                            noCalculate: modelNumberInfo.noCalculate,
                            modelNumber: modelNumberInfo.modelNumber,
                        });

                        self.getReceiptInfo(component);
                        self.getBaseInfoTips(component);
                    }
                }
            }
            else {
                var errors = response.getError();
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": errors[0].message,
                    "type": "error"
                }).fire();
            }
        });
    },

    getReceiptIdWithoutWarranty: function(component, claimId) {
        let action = component.get('c.getReceiptWithoutWarranty');
        action.setParams({'claimId' : claimId});
        action.setCallback(this, function(response) {
            let state = response.getState();
            if(state === 'SUCCESS') {
                let returnValue = JSON.parse(response.getReturnValue());
                if('receiptContentVersionId' in returnValue) {
                    component.set('v.receiptContentVersionId', returnValue.receiptContentVersionId);
                }

                if('receiptName' in returnValue) {
                    let receiptUrl = {
                        fileName: returnValue.receiptName,
                        link: returnValue.receiptLink
                    };
                    component.set('v.receiptUrl', receiptUrl);
                }
            }
            else {
                console.log(response.getError());
            }
        });
        $A.enqueueAction(action);
    },

    isAdditionalRequired: function(component) {
        let serialNumber = component.get('v.serialNumber');
        let noWarranty = component.get('v.noWarranty');
        if(!serialNumber || noWarranty) {
            component.set('v.additionalRequired', true);
        }
        else {
            component.set('v.additionalRequired', false);
        }
    },

    deleteToolImageImp: function(component, fileId) {
        let action = component.get('c.deleteImage');
        action.setParams({'fileId': fileId});
        action.setCallback(this, function(response){
            let state = response.getState();
            if(state === 'SUCCESS') {
                let toolImages = component.get('v.uploadToolImages');
                toolImages = toolImages.filter(item=>item.fileId !== fileId);
                component.set('v.uploadToolImages', toolImages);
            }
            else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    }
})