/**
 * <AUTHOR>
 * @date 2025-06-25
 * @description Calculate VAT Gap for VAT Correction based on Bench Mark and Total Warranty Efforts
 */
public with sharing class CCM_WarrantyClaim_VATGAPCalculateHandler implements Triggers.Handler {
    public static Boolean isRun = true;
    private List<Warranty_Claim__c> newRecords = (List<Warranty_Claim__c>)Trigger.new;
    private Map<Id, Warranty_Claim__c> oldMap = (Map<Id, Warranty_Claim__c>)Trigger.oldMap;

    public void handle() {
        if(!isRun) {
            return;
        }
        
        if(Trigger.isUpdate && Trigger.isBefore) {
            calculateVATGapForVATCorrection();
        }
    }

    private void calculateVATGapForVATCorrection() {
        List<Warranty_Claim__c> claimsBenchMarkCalculated = new List<Warranty_Claim__c>();
        Set<String> customerIds = new Set<String>();
        for(Warranty_Claim__c newRecord : this.newRecords) {
            Warranty_Claim__c oldRecord = this.oldMap.get(newRecord.Id);
            if(!oldRecord.Bench_Mark_Calculated__c && newRecord.Bench_Mark_Calculated__c) {
                claimsBenchMarkCalculated.add(newRecord);
                customerIds.add(newRecord.Dealer_Name__c);
            }
        }
        if(!claimsBenchMarkCalculated.isEmpty()) {
            Map<String, Account> dealerMap = new Map<String, Account>();
            for(Account acc : [SELECT Sales_Channel__c, Classification1__c, Country__c FROM Account WHERE Id IN :customerIds]) {
                dealerMap.put(acc.Id, acc);
            }
            Set<String> EuSet = new Set<String>();
            Schema.DescribeFieldResult fieldResult = Account.Country_EU__c.getDescribe();
            List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
            for (Schema.PicklistEntry item : ple){
                EuSet.add(String.valueOf(item.value));
            }
            for(Warranty_Claim__c claim : claimsBenchMarkCalculated) {
                Account acc = dealerMap.get(claim.Dealer_Name__c);
                if(isDEDealerOrPBE(acc)) {
                    Decimal vatGapForVATCorrection = CCM_WarrantyClaim_VATGAPCalculateCtl.calculateVATGAPForVATCorrection(claim);
                    claim.VAT_Gap_for_VAT_Correction__c = vatGapForVATCorrection;
                }
                if(dealerMap.containsKey(claim.Dealer_Name__c)) {
                    claim.VAT_Cost_Center__c = setCostCenter(dealerMap.get(claim.Dealer_Name__c), EuSet);
                }
                else {
                    claim.VAT_Cost_Center__c = 'default';
                }
                claim.All_Cost_Calculated__c = true;
            }
        }
    }

    private String setCostCenter(Account acc, Set<String> EuSet) {
        String costCenter = 'default';
        if(isDEDealerOrPBE(acc)){
            costCenter = 'EEG';
        }
        else {
            if(EuSet.contains(acc.Country__c) && acc.Sales_Channel__c.containsIgnoreCase('Distributor')) {
                costCenter = 'type2';
            }
        }
        return costCenter;
    }

    private Boolean isDEDealerOrPBE(Account acc) {
        Boolean isDEDealerOrPBE = false;
        if(acc.Country__c == 'DE' && (acc.Sales_Channel__c.containsIgnoreCase('Dealer') 
            || (acc.Sales_Channel__c.containsIgnoreCase('PbE') && (acc.Classification1__c == 'PbE Importdealer' || acc.Classification1__c == 'International Manufacturers' || acc.Classification1__c == 'National Manufacturer' )))) {
                isDEDealerOrPBE = true;
            }
        return isDEDealerOrPBE;
    }
}