/**
 * <AUTHOR>
 * @date 2025-06-24
 * @description Calculate Bench Mark, Total Warranty Efforts and VAT GAP for VAT Correction
 */
public without sharing class CCM_WarrantyClaim_BenchMarkCalBatch implements Database.Batchable<sObject>, Database.AllowsCallouts {
    private List<String> claimIds;
    private String query;

    public CCM_WarrantyClaim_BenchMarkCalBatch(List<String> claimIds) {
        this.claimIds = claimIds;
        this.query = 'SELECT Serial_Number__c, Dealer_Name__c, Product__r.Order_Model__c FROM Warranty_Claim__c WHERE Id In :claimIds';
    }

    public Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator(this.query);
    }

    public void execute(Database.BatchableContext BC, List<Sobject> scope) { 
        Warranty_Claim__c claim = (Warranty_Claim__c)scope[0];
        CCM_WarrantyClaim_VATGAPCalculateCtl.calculateBenchMark(claim.Id, claim.Serial_Number__c, claim.Dealer_Name__c, claim.Product__r.Order_Model__c);
    }

    public void finish(Database.BatchableContext BC) { 
        
    }
    
}