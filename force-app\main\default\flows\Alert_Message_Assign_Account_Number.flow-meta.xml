<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>Alert Message - Assign Account Number {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Alert Message - Assign Account Number</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Customer_Name_and_Customer_Account</name>
        <label>Update Customer Name and Customer Account</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Customer_Account__c</field>
            <value>
                <elementReference>$Record.Customer__r.AccountNumber</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Name__c</field>
            <value>
                <elementReference>$Record.Customer__r.Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Customer_Name_and_Customer_Account</targetReference>
        </connector>
        <object>Alert_Message__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
