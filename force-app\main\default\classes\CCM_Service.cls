/**
 * @description       : CCM_Service
 * <AUTHOR> vince yang
 * @group             : 
 * @last modified on  : 07-02-2024
**/
global without sharing class CCM_Service {
    private static final String ENDPOINT_NAME_SEEBURGER_UAT = 'chervon_seeburger_uat'; 
    private static final String ENDPOINT_NAME_SEEBURGER_PROD = 'chervon_seeburger_prod'; 
    private static final String CONTACT_RT_INVOICE = 'Invoice';
    private static final String CONTACT_RT_DUNNING = 'Dunning';
    private static final String CONTACT_RT_ACCKNOWLEDGMENT = 'Acknowledgment';
    private static final String CUST_STATUS_ACT = 'Active';
    private static final String CUST_STATUS_INACT = 'Inactive';
    private static final String CUST_STATUS_DISCONTINUE = 'DisContinue';
    private static final String CUST_INTERNAL = 'Internal';
    private static final String CUST_EXTERNAL = 'External';
    //获取当前环境类型
    public static Boolean IsSandboxOrg(){
        Boolean isSandbox = true;
        isSandbox = [SELECT IsSandbox FROM Organization LIMIT 1].IsSandbox;
        return isSandbox;
    }

    // 获取 TLS_Product 的 ProductSeries 
    public static String getProductSeries(String startIndex, String pageSize) {
        // String endPoint = 'http://18.214.131.24:1512/rest/V1.0/list/Article/byAssortment?assortment=7402&pageSize=-1&fields=';
        String endPoint = 'https://pim.chervon.com.cn/rest/V1.0/list/Article/byAssortment?assortment=7402';
        // String endPoint = 'https://pim.chervon.com.cn/rest/V1.0/list/Article/ArticleMediaAssetMap/byAssortment?assortment=7402';
        endPoint += '&startIndex=' + startIndex;
        endPoint += '&pageSize=' + pageSize;
        endPoint += '&fields=';

        String fieldsName = 'Article.ProductSeries';
        endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');

        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String HeaderKey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get parts info
    public static String getPartFromPIM(
        String startIndex,
        String pageSize,
        Boolean doInit
     ) {
        //migration
        String endPoint =
            'https://pim.chervon.com.cn/rest/V1.0/list/Article/byAssortment?assortment=\'SFDC_Spareparts_NA\'&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'Article.SupplierAID,ArticleLang.DescriptionShort(9,1),ArticleLang.Name(9,1)';
            fieldsName += ',ArticleLang.DescriptionLong(9,1),Article.ManufactureType';
            fieldsName += ',ArticleAttributeValue.Value("Wearing Parts",9,DEFAULT),ArticleAttributeValue.Value("Warranty Period for wearing parts",9,DEFAULT)';
            fieldsName += ',Article.SellableFlag';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        //Prod key
        //String headerToken = 'cHdjX3Jlc3Q6Y2hlcnZvbg==';
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    //get the relationship between kit and product
    public static String getKitsAndProducts(
        String startIndex,
        String pageSize,
        Boolean doInit
     ) {
        // migration
        String endPoint =
            'https://pim.chervon.com.cn/rest/V1.0/list/Article/ArticleComponent/byAssortment?assortment=7405&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleComponent.RefArticleIdentifier,ArticleComponent.Quantity';
            fieldsName += ',ArticleComponent.DisplayOrder';
            // fieldsName += ',Article.ProductSeries';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        //String HeaderKey = 'Basic ' + headerToken;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    //get the relationship between product and part
    public static String getAccessoriesAndParts(
        String startIndex,
        String pageSize,
        Boolean doInit
     ) {
        //migration
        //  String endPoint = getEndPoint();
        String endPoint =
            'https://pim.chervon.com.cn/rest/V1.0/list/Article/ArticleReference/byAssortment?assortment=7404&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleReference.Type,ArticleReference.ReferencedArticle';
            fieldsName += ',ArticleReference.Quantity,ArticleReference.ExplosionID';
            fieldsName += ',ArticleReference.Repairable,ArticleReference.EstimatedRepairTime';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get the relationship product and part
    public static String getToolsOnlyAndParts(
        String startIndex,
        String pageSize,
        Boolean doInit
     ) {
        //migration
        //  String endPoint = getEndPoint();
        String endPoint =
            'https://pim.chervon.com.cn/rest/V1.0/list/Article/ArticleReference/byAssortment?assortment=7402&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleReference.Type,ArticleReference.ReferencedArticle';
            fieldsName += ',ArticleReference.Quantity,ArticleReference.ExplosionID';
            fieldsName += ',ArticleReference.Repairable,ArticleReference.EstimatedRepairTime';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }


    //get the relationship between product and Diagram
    public static String getAccessoriesProductsAndDiagram(
        String startIndex,
        String pageSize,
        Boolean doInit
     ) {
        //migration
        //  String endPoint = getEndPoint();
        String endPoint =
            'https://pim.chervon.com.cn/rest/V1.0/list/Article/ArticleMediaAssetMap/byAssortment?assortment=7404&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleMediaAssetDocument.Identifier';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetDocumentAttributes.FilenameHMM(9,originalimage)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Priority';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetLog.ModificationDate(HPM)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Version';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Page';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        endPoint = endPoint + '&qualificationFilter=mediaAssetType(expdia)';
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get the  relationship between product and Diagram
    public static String getToolsOnlyAndDiagram(
        String startIndex,
        String pageSize,
        Boolean doInit
     ) {
        //migration
        //  String endPoint = getEndPoint();
        String endPoint =
            'https://pim.chervon.com.cn/rest/V1.0/list/Article/ArticleMediaAssetMap/byAssortment?assortment=7402&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleMediaAssetDocument.Identifier';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetDocumentAttributes.FilenameHMM(9,originalimage)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Priority';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetLog.ModificationDate(HPM)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Version';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Page';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        endPoint = endPoint + '&qualificationFilter=mediaAssetType(expdia)';
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    public static HttpResponse getProductsAndDiagram(String DiagramID) {
        //migration
        //  String endPoint = getEndPoint();
        String endPoint =
            'https://pim.chervon.com.cn/rest/V1.0/media/originalimage/' +
            DiagramID;
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        // HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
        HttpResponse res = CCM_ServiceCallout.getDataViaHttpDiagram(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res;
    }
    
    public static String pullSNInfoByCondition(
        String paramStr
        ) {
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
        HttpResponse res = new HttpResponse();
        String endPointName;
        if(IsSandboxOrg()){
            endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
        }else {
            endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
        }
        
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'getsn';
        //0718  改用NA basic验证方式
        // String HeaderKey = headerToken;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        
        if (!Test.isRunningTest()) {
            res = CCM_ServiceCallout.getDataViaHttpInEU( // callout
                paramStr,
                endPoint,
                'POST',
                HeaderKey
            );     
        }else{
            res.setBody('{"Process_Status":"Success"}');
        }
        return res.getBody();
    }
    /**
     * 同步Customer信息到Seeburger，由Seeburger同步到EBS
     */
    @AuraEnabled
    public static String pushCustInfo(String recordId, Alert_Message__c alertMessage) {
        System.debug('开始同步Customer---Id===》'+recordId);
        List<String> paramStrlist = new List<String>();
        HttpResponse res = new HttpResponse();
        String endPointName ='';
        String querystr =
            'SELECT Id,Customer_SF_ID__c, Customer_SF_Number__c, Customer_Oracle_ID__c, AccountNumber,'+
            ' Name, Account_Description__c, Country__c, Country_Description__c, Sales_Channel__c, Classification1__c,'+
            ' Association_Group__c, Association_Group__r.AccountNumber,Association_Membership_No__c, Owner.FederationIdentifier, Status__c, Discount_Type__c, '+
            'Record_Type_Name__c, DSV_Instruction__c, Insurance_Currency__c, Insurance_Credit_Limit__c, Cost_Center__c,List_Price__r.name, '+
            'Cost_Center_Descrption__c, AR_Overdue_Check__c, Credit_Check__c, Credit_Hold__c, Credit_Limit__c, Currency__c, '+
            'Risk_Code__c, List_Price__c, Planning_Territory_Name__c, Allow_Backorder__c, Default_Reporting_Country_Name__c, '+
            'Default_Reporting_Regsitration_Number__c, Regime_Code__c, Tax__c, CreatedById, CreatedDate, Payment_Term_To_Oracle__c,Planning_Territory_No__c,Planning_Territory_No__r.Territory_Name__c, '+
            'Send_dunning_letters__c, Association_Group__r.Association_Type__c FROM Account ' +
            'WHERE id = :recordId and Approval_Status__c = \'Approved\' ';
        if(IsSandboxOrg()){
            endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
        }else {
            endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
        }
        
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        //TODO 待Seeburger提供地址
        String methodName = 'customer';
        String endPoint = MeIS.End_Point__c + methodName;
        // String HeaderKey = headerToken;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));

        Account acc = new Account();
        acc = (Account)Database.query(querystr);
        // Create a new instance of the Customer map
        Map<String, Object> customerMap = new Map<String, Object>();
        // Set the values for the Customer fields
        customerMap.put('HEADERID', 'Salesforce-' + acc.Customer_SF_ID__c + '-' +system.now().getTime());
        customerMap.put('CUSTOMER_SF_ID', acc.Customer_SF_ID__c);
        customerMap.put('CUSTOMER_SF_NUMBER', acc.Customer_SF_Number__c);
        customerMap.put('CUSTOMER_ORACLE_ID', acc.Customer_Oracle_ID__c);
        customerMap.put('ACCOUNTNUMBER', acc.AccountNumber);
        customerMap.put('CUSTOMER_NAME', acc.Name);
        customerMap.put('ACCOUNT_DESCRIPTION', acc.Account_Description__c);
        customerMap.put('COUNTRY', acc.Country__c);
        customerMap.put('COUNTRY_DESCRIPTION', acc.Country_Description__c);
        customerMap.put('SALES_CHANNAL', acc.Sales_Channel__c);
        customerMap.put('CLASSIFICATION_1', acc.Classification1__c);
        customerMap.put('ASSOCIATION_GROUP_NUMBER', acc.Association_Group__r.AccountNumber);
        customerMap.put('ASSOCIATION_MEMBERSHIP_NO', acc.Association_Membership_No__c);
        System.debug('ASSOCIATION_MEMBERSHIP_NO====》'+acc.Association_Membership_No__c);
        customerMap.put('OWNER_ORACLE_NUMBER', acc.Owner.FederationIdentifier);

        //start update vince ********
        String strStatus = '';
        if(acc.Status__c == CUST_STATUS_DISCONTINUE){
            strStatus =  CUST_STATUS_ACT;
        }else {
            strStatus = acc.Status__c;
        }
        customerMap.put('STATUS', strStatus);
        
        String strRecordTypeName = '';
        if(acc.Sales_Channel__c == CUST_INTERNAL){
            strRecordTypeName = acc.Sales_Channel__c;
        }else {
            strRecordTypeName = CUST_EXTERNAL;
        }
        customerMap.put('RECORD_TYPE_NAME', strRecordTypeName);
        //end update vince ********

        customerMap.put('DISCOUNT_TYPE', acc.Discount_Type__c);
        customerMap.put('DSV_INSTRUCTION', acc.DSV_Instruction__c);
        customerMap.put('INSURANCE_CURRENCY', acc.Insurance_Currency__c);
        customerMap.put('INSURANCE_CREDIT_LIMIT', acc.Insurance_Credit_Limit__c);
        customerMap.put('COST_CENTER', acc.Cost_Center__c);
        customerMap.put('COST_CENTER_DESCRIPTION',acc.Cost_Center_Descrption__c);
        customerMap.put('AR_OVERDUE_CHECK', acc.AR_Overdue_Check__c);
        customerMap.put('CREDIT_CHECK', acc.Credit_Check__c);
        customerMap.put('CREDIT_HOLD', acc.Credit_Hold__c);
        customerMap.put('CREDIT_LIMIT', acc.Credit_Limit__c);
        customerMap.put('CREDIT_CURRENCY', acc.Currency__c);
        customerMap.put('RISK_CODE', acc.Risk_Code__c);
        customerMap.put('LIST_PRICE_NAME', acc.List_Price__r.name);
        if(String.isNotBlank(acc.Planning_Territory_No__c)){
            customerMap.put('PLANNING_TERRITORY_NAME', acc.Planning_Territory_No__r.Territory_Name__c);
        }else{
            customerMap.put('PLANNING_TERRITORY_NAME', '');
        }
        String allbackOrder = '';
        if(acc.Allow_Backorder__c == 'Yes'){
            customerMap.put('ALLOW_BACKORDER', 'YES');
        }else if(acc.Allow_Backorder__c == 'No'){
            customerMap.put('ALLOW_BACKORDER', 'NO');
        }else {
            customerMap.put('ALLOW_BACKORDER', acc.Allow_Backorder__c);
        }
        
        System.debug('ALLOW_BACKORDER============>'+customerMap.get('ALLOW_BACKORDER'));
        customerMap.put('DEFAULT_REPORTING_COUNTRY_NAME', acc.Default_Reporting_Country_Name__c);
        customerMap.put('DEFAULT_REPORTING_REGSITRATION_NUMBER', acc.Default_Reporting_Regsitration_Number__c);
        customerMap.put('REGIME_CODE', acc.Regime_Code__c);
        customerMap.put('TAX', acc.Tax__c);
        customerMap.put('CREATED_USER_CODE', acc.CreatedById);
        customerMap.put('CREATED_DATETIME', acc.CreatedDate.format('yyyy-MM-dd HH:mm:ss'));
        //查询Ab 获取PAYMENT_TERM
        Sales_Program__c ab = new Sales_Program__c();
        List<Sales_Program__c> lstAb = new List<Sales_Program__c>();
        lstAb = [Select Payment_Term__c, Status__c from Sales_Program__c  where Customer__c = :recordId And Status__c = 'Active' limit 1];
        if(lstAb.size() > 0){
            ab = lstAb[0];
        }
        if(ab != null){
            customerMap.put('PAYMENT_TERM_TO_ORACLE', ab.Payment_Term__c);
        }else{
            customerMap.put('PAYMENT_TERM_TO_ORACLE', '');
        }
       
        if(String.isNotBlank(acc.Send_dunning_letters__c)){
            customerMap.put('SEND_DUNNING_LETTERS', acc.Send_dunning_letters__c);
        }else{
            customerMap.put('SEND_DUNNING_LETTERS', '');
        }

        if(acc.Association_Group__c != null) {
            customerMap.put('Attribute1', acc.Association_Group__r.AccountNumber);
            customerMap.put('Attribute2', 'Y');
            if(String.isNotBlank(acc.Association_Group__r.Association_Type__c) && acc.Association_Group__r.Association_Type__c.contains('Bill to Association Group')) {
                customerMap.put('Attribute3', 'Y');
            }
            else {
                customerMap.put('Attribute3', 'N');
            }
            customerMap.put('Attribute4', 'N');
            customerMap.put('Attribute5', 'Y');
        }
        if(alertMessage != null) {
            customerMap.put('Attribute6', alertMessage.Alert_Message__c);
            if(alertMessage.Alert_Mode__c == 'Delivery') {
                alertMessage.Alert_Mode__c = 'INQUIRY';
            }
            customerMap.put('Attribute7', alertMessage.Alert_Mode__c);
            if(alertMessage.Start_Date__c != null) {
                customerMap.put('Attribute8', String.valueOf(alertMessage.Start_Date__c));
            }
            if(alertMessage.End_Date__c != null) {
                customerMap.put('Attribute9', String.valueOf(alertMessage.End_Date__c));
            }
        }
        String paramStr = JSON.serialize(customerMap);
        System.debug('pushCustomer--paramStr:'+paramStr);
        if (!Test.isRunningTest()) {
            res = CCM_ServiceCallout.getDataViaHttpInEU( // callout
                paramStr,
                endPoint,
                'POST',
                HeaderKey
            );
            String resBody = res.getBody();
            CustomerProcessResult objRes = (CustomerProcessResult)JSON.deserialize(resBody,CustomerProcessResult.class);
            String logId;
            if(String.isNotBlank(objRes.PROCESS_MESSAGE)){
                objRes.PROCESS_MESSAGE = objRes.PROCESS_MESSAGE.replace('"','');
            }
            System.debug('objRes.PROCESS_STATUS==>'+objRes.PROCESS_STATUS);
            if(objRes.PROCESS_STATUS.equals('Success')){
                System.debug('pushCustomer====>Success');
                Boolean needUpdate = false;
                if(String.isBlank(acc.Customer_Oracle_ID__c) && String.isNotBlank(objRes.CUSTOMER_ORACLE_ID)){
                    acc.Customer_Oracle_ID__c = objRes.CUSTOMER_ORACLE_ID;
                    needUpdate = true;
                }
                if(String.isBlank(acc.AccountNumber) && String.isNotBlank(objRes.ACCOUNTNUMBER)){
                    acc.AccountNumber = objRes.ACCOUNTNUMBER;
                    needUpdate = true;
                }
                if(needUpdate){
                    Database.upsert(acc,false);
                } 
                logId = Util.logIntegration('Push Customer Log', 'CCM_Service', 'POST','',paramStr, resBody);
                //同步地址
                List<String> lstBaabId = new List<String>();
                List<Address_With_Program__c> lstBaab = new List<Address_With_Program__c>();
                lstBaab = [Select a.Customer_Line_Oracle_ID__c, 
                a.Customer_Line_SF_ID__c,
                a.Customer_Oracle_ID__c, a.Customer_SF_ID__c, a.Program__c, a.Status__c from Address_With_Program__c a
                where Customer_SF_ID__c = :acc.Customer_SF_ID__c];
                if(lstBaab.size() > 0){
                    for(Address_With_Program__c baab : lstBaab ){
                        lstBaabId.add(baab.Id);
                    }
                    CCM_Service.pushAddressInfo(lstBaabId, false);
                }
                
            }else {
                System.debug('pushCustomer====>Error');
                logId = Util.logIntegration('Push Customer Exception', 'CCM_Service', 'POST',objRes.PROCESS_MESSAGE,paramStr, resBody);
                Util.pushExceptionEmail('Sync Customer Exception', logId,resBody);
            }
            //res.setBody('{"Process_Status":"Success"}');      
        }else{
            res.setBody('{"Process_Status":"Success"}');
        }
        System.debug('pushCustomer==========>'+res.getBody());
        return res.getBody();
    }

    // @future (Callout = true)
    // public static void pushAddressInfoByFuture(List<String> idLst){
    //     pushAddressInfo(idLst);
    // }

    // public static void pushAddressInfoBySync(List<String> idLst){
    //     pushAddressInfo(idLst);
    // }

    /**
     * 同步Address信息到Seeburger，由Seeburger同步到EBS
     */
    public static void pushAddressInfo(List<String> idLst, Boolean syncCopyOnly) {
        System.debug('baab地址同步idLst===》'+idLst.size());
        List<String> approveMaplist = new List<String>();
        approveMaplist.add('');
        approveMaplist.add('Approved');
        approveMaplist.add('Pending for Approval');
        String strDateTime = '-' +String.valueOf(system.now().getTime());
        List<String> paramStrlist = new List<String>();
        //返回体list
        List<AddressResult> addressResultList = new List<AddressResult>();
        String endPointName ='';
        String paramStr = '';
        //根据BAAB的idList 获取BAAB上的地址list
        String querystr =
            'SELECT ID, Customer_Line_SF_ID__c, Customer_Line_Oracle_ID__c, Customer_SF_ID__c, Customer_Oracle_ID__c, OwnerId,Program__r.Customer__c,Account_Address__c,  ' + 
            'CreatedBy.FederationIdentifier, CreatedDate, Account_Address__r.Name, Account_Address__r.Company_name_1__c, Account_Address__r.Company_name_2__c,  ' +
            'Account_Address__r.Street_1__c, Account_Address__r.Street_2__c, Account_Address__r.City__c, Account_Address__r.State__c,  ' +
            'Account_Address__r.Country__c, Account_Address__r.Postal_Code__c, Account_Address__r.County__c,Account_Address__r.RecordType.DeveloperName,  ' +
            'Account_Address__r.Province__c, Program__r.Status__c, Account_Address__r.Document_Language__c,Account_Address__r.Primary__c,  ' +
            'Account_Address__r.Communication_Method__c, Account_Address__r.Location__c, Account_Address__r.Sales_Person__c,  ' +
            'Account_Address__r.Planning_Territory_No__c,Account_Address__r.Planning_Territory_No__r.Name, Account_Address__r.Sub_Class__c, Account_Address__r.Send_Dunning_Letters__c,  ' +
            'Account_Address__r.Customer__c,Account_Address__r.Customer__r.Country__c,Account_Address__r.Customer__r.Association_Group__r.AccountNumber,'+
            //pricebook 240301
            'Program__c,Program__r.Price_Book__c,'+
            'Program__r.List_Price_1__c,Program__r.List_Price_1__r.Name,'+
            'Program__r.List_Price_2__c,Program__r.List_Price_2__r.Name,'+
            'Program__r.List_Price_3__c,Program__r.List_Price_3__r.Name,'+
            //Contact
            'Account_Address__r.Dunning_Letter_Contact__c,Account_Address__r.Invoice_Credit_Customer__c,Account_Address__r.Acknowledgement_Customer__c, ' +
            'Account_Address__r.Dunning_Letter_Addition_1_Customer__c,Account_Address__r.Dunning_Letter_Additional_2_Customer__c,Account_Address__r.Invoice_Credit_Additional_1_Customer__c, ' +
            'Account_Address__r.Invoice_Credit_Additional_2_Customer__c,Account_Address__r.Acknowledgement_Additional_1_Customer__c,Account_Address__r.Acknowledgement_Additional_2_Customer__c, ' +
            'Account_Address__r.Dunning_Letter_Additional_3_Customer__c,Account_Address__r.Invoice_Credit_Additional_3_Customer__c,Account_Address__r.Acknowledgement_Additional_3_Customer__c, ' +
            'Account_Address__r.Copied_Invoice_Credit_1_Customer__c, Account_Address__r.Copied_Invoice_Credit_2_Customer__c, Account_Address__r.Copied_Invoice_Credit_3_Customer__c, Account_Address__r.Copied_Invoice_Credit_4_Customer__c, ' + 
            //User
            'Account_Address__r.Dunning_Letter_Finance__c ,Account_Address__r.Dunning_Letter_Contact_Sales_Rep__c , '+
            'Account_Address__r.Invoice_Credit_Finance__c ,Account_Address__r.Invoice_Credit_Contact_Sales_Rep__c , '+
            'Account_Address__r.Acknowledgement_Contact_Sales_Rep__c , '+
            'Account_Address__r.Customer__r.OwnerId, ' +
            //contact orcle id 、num
            'Account_Address__r.Dunning_Contact_OId__c,Account_Address__r.Dunning_Contact_ONum__c,Account_Address__r.Invoice_Contact_OId__c, '+
            'Account_Address__r.Invoice_Contact_ONum__c,Account_Address__r.Acknowledgement_Contact_OId__c,Account_Address__r.Acknowledgement_Contcat_ONum__c, '+
            
            'Account_Address__r.Email__c, Program__r.Order_Type__c,Account_Address__r.Customer__r.List_Price__c,Account_Address__r.Customer__r.List_Price__r.name, ' +
            'Program__r.Price_Book__r.Name, Program__r.Customer__r.Owner.FederationIdentifier,Account_Address__r.OwnerId,  ' +
            'Program__r.Payment_Term__c, Program__r.Incoterm__c, Program__r.ORG_Code__c,Account_Address__r.StatusNew__c ' +
            'FROM Address_With_Program__c  ' +
            'WHERE ID in :idLst And Program__r.Order_Type__c = \'Sales Order - DSV\' ' +
            'And Program__r.Approval_Status__c in :approveMaplist';

            System.debug('querystr=====>'+querystr);
        List<Address_With_Program__c> lstAddress = new List<Address_With_Program__c>();
        lstAddress = (List<Address_With_Program__c>)Database.query(querystr);
        //查询所属Customer下的Contact list
        List<Contact> lstContact = new List<Contact>();
        List<User> lstUser = new List<User>();
        List<Account_Address__c> lstAccAddress = new List<Account_Address__c>();
        Set<String> setContactId = new Set<String>();
        Set<String> setUserId = new Set<String>();
        Set<String> setAddressId = new Set<String>();
        Set<String> setSalesPersonId = new Set<String>();
        List<User> lstSalesPerson = new List<User>();
        Map<String,String> mapIdToFId = new Map<String,String>();
        if(lstAddress.size() > 0){
            for(Address_With_Program__c addressObj : lstAddress){
                setContactId.add(addressObj.Account_Address__r.Dunning_Letter_Contact__c);
                setContactId.add(addressObj.Account_Address__r.Invoice_Credit_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Acknowledgement_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Dunning_Letter_Addition_1_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Dunning_Letter_Additional_2_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Dunning_Letter_Additional_3_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Invoice_Credit_Additional_1_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Invoice_Credit_Additional_2_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Invoice_Credit_Additional_3_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Acknowledgement_Additional_1_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Acknowledgement_Additional_2_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Acknowledgement_Additional_3_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Copied_Invoice_Credit_1_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Copied_Invoice_Credit_2_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Copied_Invoice_Credit_3_Customer__c);
                setContactId.add(addressObj.Account_Address__r.Copied_Invoice_Credit_4_Customer__c);
                setUserId.add(addressObj.Account_Address__r.Dunning_Letter_Finance__c);
                setUserId.add(addressObj.Account_Address__r.Dunning_Letter_Contact_Sales_Rep__c);
                setUserId.add(addressObj.Account_Address__r.Invoice_Credit_Finance__c);
                setUserId.add(addressObj.Account_Address__r.Invoice_Credit_Contact_Sales_Rep__c);
                setUserId.add(addressObj.Account_Address__r.Acknowledgement_Contact_Sales_Rep__c);
                setUserId.add(addressObj.Account_Address__r.Customer__r.OwnerId);
                setAddressId.add(addressObj.Account_Address__c);
                setSalesPersonId.add(addressObj.Account_Address__r.OwnerId);
            }
        }
        if(lstAddress.size() > 0){
            if(setContactId != null){
                lstContact = [
                SELECT Id, Contact_Oracle_ID__c, Account.Customer_SF_Number__c,Contact_Oracle_Number__c, Salutation, FirstName, MiddleName, 
                LastName, Suffix, Title, MobilePhone, Phone, Fax, Email, Status__c, RecordType.Name, Customer_SF_Number__c, CreatedBy.FederationIdentifier, CreatedDate
                FROM Contact
                WHERE Id in :setContactId];
            }
            if(setUserId !=  null){
                lstUser = [
                    SELECT CreatedById, CreatedBy.CreatedDate, CreatedBy.FederationIdentifier, Email, Fax, FederationIdentifier, 
                    FirstName, LastName, MiddleName, MobilePhone, Phone, Suffix, Title 
                    FROM User 
                    WHERE id in :setUserId];
            }
            if(setSalesPersonId != null){
                lstSalesPerson = [
                    SELECT Id, FederationIdentifier
                    FROM User 
                    WHERE id in :setSalesPersonId
                ];
            }
            if(lstSalesPerson != null && lstSalesPerson.size() > 0 ){
                for(User salesPerson : lstSalesPerson){
                    mapIdToFId.put(salesPerson.Id, salesPerson.FederationIdentifier);
                }
            }
            
            for(Address_With_Program__c addressObj : lstAddress){
                Map<String, Object> paramMap = new Map<String, Object>();
                //封装BaaB 地址信息
                paramMap.put('HEADERID', 'SF-' + addressObj.id + strDateTime);
                paramMap.put('ADDRESS_SF_ID', addressObj.Customer_Line_SF_ID__c);
                paramMap.put('ADDRESS_ORACLE_NUMBER', addressObj.Customer_Line_Oracle_ID__c);
                paramMap.put('CUSTOMER_SF_ID', addressObj.Customer_SF_ID__c);
                paramMap.put('CUSTOMER_ORACLE_ID', addressObj.Customer_Oracle_ID__c);
                paramMap.put('USER_NUMBER', addressObj.OwnerId);
                String billFlag = 'NO';
                String shipFlag = 'NO';
                String dropFlag = 'NO';
                if(addressObj.Account_Address__r.RecordType.DeveloperName.equals('Billing_Address')){
                    billFlag = 'YES';
                    
                }else if(addressObj.Account_Address__r.RecordType.DeveloperName.equals('Shipping_Address')){
                    shipFlag = 'YES';
                }else if(addressObj.Account_Address__r.RecordType.DeveloperName.equals('Dropship_Shipping_Address')){
                    dropFlag = 'YES';
                }
                paramMap.put('BILLTO_FLAG', billFlag);
                paramMap.put('SHIPTO_FLAG', shipFlag);
                paramMap.put('DROP_FLAG', dropFlag);
                String primaryFlag = 'NO';
                if(addressObj.Account_Address__r.Primary__c){
                    paramMap.put('PRIMARY_FLAG', 'YES');
                }else {
                    paramMap.put('PRIMARY_FLAG', 'NO');
                }
                
                paramMap.put('ADDRESS_NAME', addressObj.Account_Address__r.Name);
                paramMap.put('COMPANY_NAME_1', addressObj.Account_Address__r.Company_name_1__c);
                paramMap.put('COMPANY_NAME_2', addressObj.Account_Address__r.Company_name_2__c);
                paramMap.put('STREET_1', addressObj.Account_Address__r.Street_1__c);
                paramMap.put('STREET_2', addressObj.Account_Address__r.Street_2__c);
                paramMap.put('CITY', addressObj.Account_Address__r.City__c);
                paramMap.put('STATE', addressObj.Account_Address__r.State__c);
                //取简称
                paramMap.put('COUNTRY_DESCRIPTION', addressObj.Account_Address__r.Country__c);
                paramMap.put('POSTAL_CODE', addressObj.Account_Address__r.Postal_Code__c);
                paramMap.put('COUNTY', addressObj.Account_Address__r.County__c);
                paramMap.put('PROVINCE', addressObj.Account_Address__r.Province__c);
                String addressStatus = CUST_STATUS_ACT;
                if(addressObj.Account_Address__r.StatusNew__c == CUST_STATUS_INACT || addressObj.Program__r.Status__c == CUST_STATUS_INACT){
                    addressStatus = CUST_STATUS_INACT;
                }
                paramMap.put('STATUS', addressStatus);
                System.debug('Program__r.Status__c=====>'+addressObj.Program__r.Status__c);
                paramMap.put('DOCUMENT_LANGUAGE', addressObj.Account_Address__r.Document_Language__c);
                paramMap.put('COMMUNICATION_METHOD', addressObj.Account_Address__r.Communication_Method__c);
                paramMap.put('LOCATION', addressObj.Account_Address__r.Location__c);
                if(mapIdToFId.containsKey(addressObj.Account_Address__r.OwnerId)){
                    paramMap.put('SALES_PERSON', mapIdToFId.get(addressObj.Account_Address__r.OwnerId)); 
                }else{
                    paramMap.put('SALES_PERSON', ''); 
                }
                System.debug('SALES_PERSON====>'+paramMap.get('SALES_PERSON'));
                //********变更为SALES_PERSON取地址owner
                // paramMap.put('SALES_PERSON', addressObj.Account_Address__r.Owner.FederationIdentifier);
                if(String.isNotEmpty(addressObj.Account_Address__r.Planning_Territory_No__c)){
                    paramMap.put('SALES_TERRITORY', addressObj.Account_Address__r.Planning_Territory_No__r.Name);
                }else{
                    paramMap.put('SALES_TERRITORY', '');   
                }
                paramMap.put('CLASSIFICATION_2', addressObj.Account_Address__r.Sub_Class__c);
                paramMap.put('SEND_DUNNING_LETTERS', addressObj.Account_Address__r.Send_Dunning_Letters__c);
                //Sales Order - DSV   ====>   EEG Sales Domestic
                // if(addressObj.Program__r.Order_Type__c.equals('Sales Order - DSV')){
                //     paramMap.put('ORDER_TYPE', 'EEG Sales Domestic');
                // }

                //11-24 vince Change Start： DE: EEG Sales Domestic, EU excl DE: EEG Sales EU, Outside EU: EEG Sales Outside EU
                // 获取哪些国家是欧盟国家
                Set<String> EuSet = new Set<String>();
                Schema.DescribeFieldResult fieldResult = Account.Country_EU__c.getDescribe();
                List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
                for (Schema.PicklistEntry item : ple){
                    EuSet.add(String.valueOf(item.value));
                }
                String strCountry = addressObj.Account_Address__r.Customer__r.Country__c;
                if ('DE'.equals(strCountry)){
                    paramMap.put('ORDER_TYPE', 'EEG Sales Domestic');
                } else if (EuSet.contains(strCountry)){
                    paramMap.put('ORDER_TYPE', 'EEG Sales EU');
                } else {
                    paramMap.put('ORDER_TYPE', 'EEG Sales Outside EU');
                }
                //11-24 vince Change End

                paramMap.put('PAYMENT_TERM', addressObj.Program__r.Payment_Term__c);
                paramMap.put('INCOTERM', addressObj.Program__r.Incoterm__c);
                paramMap.put('ORG_CODE', addressObj.Program__r.ORG_Code__c);
                paramMap.put('CREATED_USER_CODE', addressObj.CreatedBy.FederationIdentifier);
                paramMap.put('CREATED_DATETIME', addressObj.CreatedDate.format('yyyy-MM-dd HH:mm:ss'));
                paramMap.put('EMAIL', addressObj.Account_Address__r.Email__c);
                if(addressObj.Account_Address__r.Customer__r.List_Price__c != null){
                    paramMap.put('LIST_PRICEBOOK_NAME', addressObj.Account_Address__r.Customer__r.List_Price__r.name);
                }else if(addressObj.Program__r.Price_Book__c != null){
                    paramMap.put('LIST_PRICEBOOK_NAME', addressObj.Program__r.Price_Book__r.Name);
                }else if(addressObj.Program__r.List_Price_1__c != null){
                    paramMap.put('LIST_PRICEBOOK_NAME', addressObj.Program__r.List_Price_1__r.Name);
                }else if(addressObj.Program__r.List_Price_2__c != null){
                    paramMap.put('LIST_PRICEBOOK_NAME', addressObj.Program__r.List_Price_2__r.Name);
                }else if(addressObj.Program__r.List_Price_2__c != null){
                    paramMap.put('LIST_PRICEBOOK_NAME', addressObj.Program__r.List_Price_3__r.Name);
                }else {
                    paramMap.put('LIST_PRICEBOOK_NAME', '');
                }
                paramMap.put('Attribute1', '');
                paramMap.put('Attribute2', '');
                paramMap.put('Attribute3', '');
                paramMap.put('Attribute4', '');
                paramMap.put('Attribute5', '');
                paramMap.put('Attribute6', '');
                paramMap.put('Attribute7', '');
                paramMap.put('Attribute8', '');
                paramMap.put('Attribute9', '');
                paramMap.put('Attribute10', '');
                paramMap.put('ContactLine', '');
                //获取contact  map id to email
                Map<String,Contact> mapIdToContact = new Map<String,Contact>();
                List<Map<String, Object>> lstStrContact = new List<Map<String, Object>>();
                if(lstContact.size() > 0){
                    for(Contact c : lstContact){
                        mapIdToContact.put(c.id, c);
                    }
                }
                //user封装为contact
                if(lstUser.size() > 0){
                    User salesRepUser = null;
                    User invSalesRepUser = null;
                    User dunSalesRepUser = null;
                    User ackSalesRepUser = null;
                    User financeUser = null;
                    User addressOwner = null;
                    Boolean isShipping = false;
                    for(User objUser : lstUser){
                        if(shipFlag == 'YES' || dropFlag == 'YES') {
                            isShipping = true;
                            if(objUser.Id == addressObj.Account_Address__r.Customer__r.OwnerId) {
                                addressOwner = objUser;
                            }
                        }
                        
                        if(objUser.Id == addressObj.Account_Address__r.Dunning_Letter_Contact_Sales_Rep__c) {
                            salesRepUser = objUser;
                            dunSalesRepUser = objUser;
                        }

                        if(objUser.Id == addressObj.Account_Address__r.Invoice_Credit_Contact_Sales_Rep__c) {
                            invSalesRepUser = objUser;
                        }

                        if(objUser.Id == addressObj.Account_Address__r.Acknowledgement_Contact_Sales_Rep__c) {
                            ackSalesRepUser = objUser;
                        }
                        
                        if(objUser.Id == addressObj.Account_Address__r.Dunning_Letter_Finance__c){
                            financeUser = objUser;
                        }
                    }
                    if(salesRepUser != null || addressOwner != null){
                        String ackOid = addressObj.Account_Address__r.Acknowledgement_Contact_OId__c;
                        String ackONum = addressObj.Account_Address__r.Acknowledgement_Contcat_ONum__c;
                        String invOid = addressObj.Account_Address__r.Invoice_Contact_OId__c;
                        String invONum = addressObj.Account_Address__r.Invoice_Contact_ONum__c;
                        String dunOid = addressObj.Account_Address__r.Dunning_Contact_OId__c;
                        String dunONum = addressObj.Account_Address__r.Dunning_Contact_ONum__c;

                        if(syncCopyOnly) {
                            Map<String, Object> paramContactMap1 = new Map<String, Object>();
                            paramContactMap1 = getUserMap(salesRepUser, addressOwner, invSalesRepUser, dunSalesRepUser, ackSalesRepUser, financeUser, strDateTime,addressObj, CONTACT_RT_INVOICE,mapIdToContact, true, false, isShipping);
                            lstStrContact.add(paramContactMap1);
                        }
                        else {
                            //若shipping address,customer Contact为空时不传
                            //invoice
                            if(shipFlag.equals('YES') &&
                                String.isBlank(addressObj.Account_Address__r.Invoice_Credit_Customer__c)&&
                                String.isBlank(addressObj.Account_Address__r.Invoice_Credit_Additional_1_Customer__c)&&
                                String.isBlank(addressObj.Account_Address__r.Invoice_Credit_Additional_2_Customer__c)&&
                                String.isBlank(addressObj.Account_Address__r.Invoice_Credit_Additional_3_Customer__c)){
                                    if(String.isNotEmpty(invOid) && String.isNotEmpty(invONum)){
                                        Map<String, Object> paramContactMap1 = new Map<String, Object>();
                                        paramContactMap1 = getUserMap(salesRepUser, addressOwner, invSalesRepUser, dunSalesRepUser, ackSalesRepUser, financeUser, strDateTime, addressObj, CONTACT_RT_INVOICE,mapIdToContact, false, true, isShipping);
                                        lstStrContact.add(paramContactMap1);
                                    }
                            }else{
                                Map<String, Object> paramContactMap1 = new Map<String, Object>();
                                paramContactMap1 = getUserMap(salesRepUser, addressOwner, invSalesRepUser, dunSalesRepUser, ackSalesRepUser, financeUser, strDateTime,addressObj, CONTACT_RT_INVOICE,mapIdToContact, true, true, isShipping);
                                lstStrContact.add(paramContactMap1);
                            }
                            //dunning
                            if(shipFlag.equals('YES') && 
                                !String.isNotEmpty(addressObj.Account_Address__r.Dunning_Letter_Contact__c)&&
                                !String.isNotEmpty(addressObj.Account_Address__r.Dunning_Letter_Addition_1_Customer__c)&&
                                !String.isNotEmpty(addressObj.Account_Address__r.Dunning_Letter_Additional_2_Customer__c)&&
                                !String.isNotEmpty(addressObj.Account_Address__r.Dunning_Letter_Additional_3_Customer__c)){
                                if(String.isNotEmpty(dunOid) && String.isNotEmpty(dunONum)){
                                    Map<String, Object> paramContactMap2 = new Map<String, Object>();
                                    paramContactMap2 = getUserMap(salesRepUser, addressOwner, invSalesRepUser, dunSalesRepUser, ackSalesRepUser, financeUser,strDateTime,addressObj,CONTACT_RT_DUNNING,mapIdToContact,false, true, isShipping);
                                    lstStrContact.add(paramContactMap2); 
                                }
                            }else{
                                Map<String, Object> paramContactMap2 = new Map<String, Object>();
                                paramContactMap2 = getUserMap(salesRepUser, addressOwner, invSalesRepUser, dunSalesRepUser, ackSalesRepUser, financeUser,strDateTime,addressObj,CONTACT_RT_DUNNING,mapIdToContact,true, true, isShipping);
                                lstStrContact.add(paramContactMap2);
                            }
                            //acknowledge
                            if(shipFlag.equals('YES') && 
                                !String.isNotEmpty(addressObj.Account_Address__r.Acknowledgement_Customer__c)&&
                                !String.isNotEmpty(addressObj.Account_Address__r.Acknowledgement_Additional_1_Customer__c)&&
                                !String.isNotEmpty(addressObj.Account_Address__r.Acknowledgement_Additional_2_Customer__c)&&
                                !String.isNotEmpty(addressObj.Account_Address__r.Acknowledgement_Additional_3_Customer__c)){
                                if(String.isNotEmpty(ackOid) && String.isNotEmpty(ackONum)){
                                    Map<String, Object> paramContactMap3 = new Map<String, Object>();
                                    paramContactMap3 = getUserMap(salesRepUser, addressOwner, invSalesRepUser, dunSalesRepUser, ackSalesRepUser, financeUser,strDateTime,addressObj,CONTACT_RT_ACCKNOWLEDGMENT,mapIdToContact,false, true, isShipping);
                                    lstStrContact.add(paramContactMap3);
                                }
                            }else{
                                Map<String, Object> paramContactMap3 = new Map<String, Object>();
                                paramContactMap3 = getUserMap(salesRepUser, addressOwner, invSalesRepUser, dunSalesRepUser, ackSalesRepUser, financeUser,strDateTime,addressObj,CONTACT_RT_ACCKNOWLEDGMENT,mapIdToContact,true, true, isShipping);
                                lstStrContact.add(paramContactMap3);
                            }
                        }
                        paramMap.put('ContactLine', lstStrContact);  
                    }
                }
                paramStr = Json.serialize(paramMap);
                paramStrlist.add(paramStr);
            }
        }
        if(IsSandboxOrg()){
            endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
        }else {
            endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
        }
        
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        //TODO 待Seeburger提供地址
        String methodName = 'addrcontact';
        String endPoint = MeIS.End_Point__c + methodName;
        // String HeaderKey = headerToken;
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        //取消多条传输 0802，改为单条传输
        System.debug('pushAddressAndContact--BAAB同步条数'+paramStrlist.size());
        if (!Test.isRunningTest()) {
            if(paramStrlist.size() > 0){
                CCM_PushAddressCallOutBatch bc = new CCM_PushAddressCallOutBatch(paramStrlist, lstAddress, lstContact, endPoint, HeaderKey, syncCopyOnly);
                Database.executeBatch(bc, 1);
            }
            //遍历addressResultList，更新oracle到address和Contact
            System.debug('addressResultList====>'+addressResultList.size());
        }
    }

    public static Map<String, Object> getUserMap(User salesRepUser, User addressOwner, User invSalesRepUser, User dunSalesRepUser, User ackSalesRepUser, User financeUser, String strDateTime, Address_With_Program__c addressObj, String recordTypeName, Map<String,Contact> mapIdToContact, Boolean contactSyncFlag, Boolean isPrimary, Boolean isShipping){
        Long nowMillisecond = system.now().getTime();
        //避免id重复问题，延时1毫秒
        while(system.now().getTime() < nowMillisecond + 1) {
        }

        if(salesRepUser == null && addressOwner != null) {
            salesRepUser = addressOwner;
        }
        String strDateTimeLine = '-' +String.valueOf(nowMillisecond);
        Map<String, Object> paramContactMap = new Map<String, Object>();
        String contcatSFId  = addressObj.Customer_Line_SF_ID__c+recordTypeName.substring(0, 3) +salesRepUser.id;
        paramContactMap.put('LINE_ID','SF-' + salesRepUser.Id + strDateTimeLine);
        paramContactMap.put('CUSTOMER_ADDRESS_ID','SF-' + addressObj.Id + strDateTime);
        paramContactMap.put('ADDRESS_SF_ID',addressObj.Customer_Line_SF_ID__c);
        paramContactMap.put('ADDRESS_ORACLE_NUMBER',addressObj.Customer_Line_Oracle_ID__c);
        //Role的前三位做区分 联系人类别
        paramContactMap.put('CONTACT_SF_ID',contcatSFId);
        

        paramContactMap.put('SALUTATION','');
        paramContactMap.put('FIRSTNAME',salesRepUser.FirstName);
        paramContactMap.put('MIDDLE_NAME',salesRepUser.MiddleName);
        paramContactMap.put('LASTNAME',salesRepUser.LastName);
        paramContactMap.put('SUFFIX',salesRepUser.Suffix);
        paramContactMap.put('TITLE',salesRepUser.Title);
        paramContactMap.put('MOBILEPHONE',salesRepUser.MobilePhone);
        paramContactMap.put('PHONE',salesRepUser.Phone);
        paramContactMap.put('FAX',salesRepUser.Fax);
        String emailStr = '';
        
        String COId = '';
        String CONum = '';
        
        String strCountry = addressObj.Account_Address__r.Customer__r.Country__c;
        
        //封装Dunning 联系人，拼接Email
        if(recordTypeName == CONTACT_RT_DUNNING){
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Dunning_Letter_Contact__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Contact__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Contact__c).Email + ',';
                }
            }
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Dunning_Letter_Addition_1_Customer__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Addition_1_Customer__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Addition_1_Customer__c).Email + ',';
                }
            }
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Dunning_Letter_Additional_2_Customer__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Additional_2_Customer__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Additional_2_Customer__c).Email + ',';
                }
            }
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Dunning_Letter_Additional_3_Customer__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Additional_3_Customer__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Dunning_Letter_Additional_3_Customer__c).Email + ',';
                }
            }
            if(financeUser != null){
                emailStr += financeUser.Email + ',';
            }
            if(isShipping) {
                if(dunSalesRepUser != null) {
                    emailStr += dunSalesRepUser.Email;
                }
            }
            else if(salesRepUser != null){
                emailStr += salesRepUser.Email;
            }
            COId = addressObj.Account_Address__r.Dunning_Contact_OId__c;
            CONum = addressObj.Account_Address__r.Dunning_Contact_ONum__c;
        }
        //封装Invoice联系人，拼接Email
        if(recordTypeName == CONTACT_RT_INVOICE){
            if(isPrimary) {
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Invoice_Credit_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Customer__c).Email + ',';
                    }
                }
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Invoice_Credit_Additional_1_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Additional_1_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Additional_1_Customer__c).Email + ',';
                    }
                }
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Invoice_Credit_Additional_2_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Additional_2_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Additional_2_Customer__c).Email + ',';
                    }
                }
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Invoice_Credit_Additional_3_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Additional_3_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Invoice_Credit_Additional_3_Customer__c).Email + ',';
                    }
                }
                if(financeUser != null){
                    if(addressObj.Account_Address__r.Customer__r.Association_Group__r.AccountNumber != 'E01005') {
                        emailStr += financeUser.Email + ',';
                    }
                }
                if(isShipping) {
                    if(invSalesRepUser != null) {
                        if (!'DE'.equals(strCountry)){
                            emailStr += invSalesRepUser.Email;
                        } 
                    }
                }
                else if(salesRepUser != null){
                    if (!'DE'.equals(strCountry)){
                        emailStr += salesRepUser.Email;
                    } 
                }
                emailStr = emailStr.removeEnd(',');
            }
            else {
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Copied_Invoice_Credit_1_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_1_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_1_Customer__c).Email + ',';
                    }
                }
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Copied_Invoice_Credit_2_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_2_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_2_Customer__c).Email + ',';
                    }
                }
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Copied_Invoice_Credit_3_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_3_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_3_Customer__c).Email + ',';
                    }
                }
                if(mapIdToContact.containsKey(addressObj.Account_Address__r.Copied_Invoice_Credit_4_Customer__c)){
                    if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_4_Customer__c).Email)){
                        emailStr += mapIdToContact.get(addressObj.Account_Address__r.Copied_Invoice_Credit_4_Customer__c).Email + ',';
                    }
                }
            }
            COId = addressObj.Account_Address__r.Invoice_Contact_OId__c;
            CONum = addressObj.Account_Address__r.Invoice_Contact_ONum__c;
        }
        //封装Accknowledge联系人，拼接Email
        if(recordTypeName == CONTACT_RT_ACCKNOWLEDGMENT){
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Acknowledgement_Customer__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Customer__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Customer__c).Email + ',';
                }
            }
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Acknowledgement_Additional_1_Customer__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Additional_1_Customer__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Additional_1_Customer__c).Email + ',';
                }
            }
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Acknowledgement_Additional_2_Customer__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Additional_2_Customer__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Additional_2_Customer__c).Email + ',';
                }
            }
            if(mapIdToContact.containsKey(addressObj.Account_Address__r.Acknowledgement_Additional_3_Customer__c)){
                if(String.isNotBlank(mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Additional_3_Customer__c).Email)){
                    emailStr += mapIdToContact.get(addressObj.Account_Address__r.Acknowledgement_Additional_3_Customer__c).Email + ',';
                }
            }
            if(isShipping) {
                if(ackSalesRepUser != null) {
                    emailStr += ackSalesRepUser.Email;
                }
            }
            else if(salesRepUser != null){
                emailStr += salesRepUser.Email;
            }
            COId = addressObj.Account_Address__r.Acknowledgement_Contact_OId__c;
            CONum = addressObj.Account_Address__r.Acknowledgement_Contcat_ONum__c;
        }
        
        paramContactMap.put('CONTACT_ORACLE_ID',COId);
        paramContactMap.put('CONTACT_ORACLE_NUMBER',CONum);
        if(contactSyncFlag){
            paramContactMap.put('EMAIL',emailStr);
        }else{
            paramContactMap.put('EMAIL','');
        }
        
        System.debug('Combo Email Info======>'+emailStr);
        paramContactMap.put('STATUS','');
        paramContactMap.put('RECORD_TYPE_NAME',recordTypeName);
        paramContactMap.put('CUSTOMER_SF_NUMBER',salesRepUser.id);
        paramContactMap.put('CREATED_USER_CODE','');
        paramContactMap.put('CREATED_DATETIME','');
        paramContactMap.put('Attribute1', 'Y');
        if(!isPrimary) {
            paramContactMap.put('Attribute1', 'N');
        }
        paramContactMap.put('Attribute2', '');
        paramContactMap.put('Attribute3', '');
        paramContactMap.put('Attribute4', '');
        paramContactMap.put('Attribute5', '');
        paramContactMap.put('Attribute6', '');
        paramContactMap.put('Attribute7', '');
        paramContactMap.put('Attribute8', '');
        paramContactMap.put('Attribute9', '');
        paramContactMap.put('Attribute10', '');
        return paramContactMap;
    }

    
    // public static void updateAddressAndContact(List<AddressResult> addressResultList,List<Address_With_Program__c> lstAddress,List<Contact> lstContact){
    //     //映射地址Id ：SF Id -> Oracle Id
    //     Map<String,String> mapAddressIdSFToOracle = new Map<String,String>();
    //     //映射联系人Id ：SF Id -> Oracle Id
    //     Map<String,String> mapContactSFIdToOracle = new Map<String,String>();
    //     Map<String,String> mapContactSFIdToOracleNum = new Map<String,String>();
    //     Set<String> accAdressId = new Set<String>();
    //     //映射SF标识到Oracle标识
    //     if(addressResultList.size() > 0){
    //         for(AddressResult addressResp : addressResultList){
    //                 mapAddressIdSFToOracle.put(addressResp.ADDRESS_SF_ID,addressResp.ADDRESS_ORACLE_NUMBER);
    //                 List<ContactLineResult> lstContactResp = addressResp.ContactLine;
    //                 if(lstContactResp.size() > 0){
    //                     for(ContactLineResult contactResp: lstContactResp){
    //                         mapContactSFIdToOracle.put(addressResp.ADDRESS_SF_ID + contactResp.CONTACT_SF_ID,contactResp.CONTACT_ORACLE_ID);
    //                         mapContactSFIdToOracleNum.put(addressResp.ADDRESS_SF_ID + contactResp.CONTACT_SF_ID,contactResp.CONTACT_ORACLE_NUMBER);
    //                     }
    //                 }
    //         }
    //     }
    //     //更新BaaB 地址的Oracle标识
    //     Map<String,Object> mapBAABToA = new Map<String,Object>();
    //     if(mapAddressIdSFToOracle != null){
    //         for(Address_With_Program__c baab : lstAddress){
    //             accAdressId.add(baab.Account_Address__c);
    //             String customerLineOracleId = mapAddressIdSFToOracle.get(baab.Customer_Line_SF_ID__c);
                
    //             if(String.isNotBlank(customerLineOracleId)){
    //                 baab.Customer_Line_Oracle_ID__c = customerLineOracleId;
    //             }
    //         }
    //         //更新Account Address 上的Customer_Line_Oracle_ID__c
    //         List<Account_Address__c> lstAccAddress = new List<Account_Address__c>();
    //         lstAccAddress = [
    //             SELECT Customer_Line_Oracle_ID__c,Acknowledgement_Contact_OId__c, Acknowledgement_Contcat_ONum__c, Dunning_Contact_OId__c, 
    //             Dunning_Contact_ONum__c, Id, Invoice_Contact_OId__c, Invoice_Contact_ONum__c 
    //             FROM Account_Address__c 
    //             WHERE Id in :accAdressId]; 
    //         for(Address_With_Program__c baab1 : lstAddress){
    //             for(Account_Address__c  accAdress : lstAccAddress){
    //                 if(baab1.Account_Address__c == accAdress.Id){
    //                     accAdress.Customer_Line_Oracle_ID__c = baab1.Customer_Line_Oracle_ID__c;
    //                     //组装contact key
    //                     String dunContactKey = baab1.Customer_Line_SF_ID__c + 'Dun' + baab1.Account_Address__r.Dunning_Letter_Contact_Sales_Rep__c;
    //                     String invContactKey = baab1.Customer_Line_SF_ID__c + 'Inv' + baab1.Account_Address__r.Dunning_Letter_Contact_Sales_Rep__c;
    //                     String ackContactKey = baab1.Customer_Line_SF_ID__c + 'Ack' + baab1.Account_Address__r.Dunning_Letter_Contact_Sales_Rep__c;
    //                     String dunOid ='';
    //                     String dunONum ='';
    //                     String invOid ='';
    //                     String invONum ='';
    //                     String ackOid ='';
    //                     String ackONum ='';
    //                      //Get Contact Oracle Id
    //                     if(mapContactSFIdToOracle.containsKey(dunContactKey)){
    //                         dunOid = mapContactSFIdToOracle.get(dunContactKey);
    //                     }
    //                     if(mapContactSFIdToOracle.containsKey(invContactKey)){
    //                         invOid = mapContactSFIdToOracle.get(dunContactKey);
    //                     }
    //                     if(mapContactSFIdToOracle.containsKey(ackContactKey)){
    //                         ackOid = mapContactSFIdToOracle.get(dunContactKey);
    //                     }
    //                     //Get Contact Oracle Num
    //                     if(mapContactSFIdToOracleNum.containsKey(dunContactKey)){
    //                         dunONum = mapContactSFIdToOracleNum.get(dunContactKey);
    //                     }
    //                     if(mapContactSFIdToOracleNum.containsKey(invContactKey)){
    //                         invONum = mapContactSFIdToOracleNum.get(dunContactKey);
    //                     }
    //                     if(mapContactSFIdToOracleNum.containsKey(ackContactKey)){
    //                         ackONum = mapContactSFIdToOracleNum.get(dunContactKey);
    //                     }
    //                     accAdress.Dunning_Contact_OId__c = dunOid;
    //                     accAdress.Dunning_Contact_ONum__c = dunONum;
    //                     accAdress.Invoice_Contact_OId__c = invOid;
    //                     accAdress.Invoice_Contact_ONum__c = invONum;
    //                     accAdress.Acknowledgement_Contact_OId__c = ackOid;
    //                     accAdress.Acknowledgement_Contcat_ONum__c = ackONum;
    //                 }
    //             }
    //         }
    //         if(lstAddress.size() > 0){
    //             update lstAddress;
    //         }
    //         if(lstAccAddress.size() > 0){
    //             update lstAccAddress;
    //         }
    //     }
    // }

    /**
     * 20240327 contentServ链接变更，原方法弃用
    public static String getContentServProductline(String ModelNumber) {
        String contentID = getContentServID(ModelNumber);
        String URL = 'https://npo505.saas.contentserv.com/admin/rest/smart/preset/119?ContextIDs=';
        URL = URL + contentID;
        URL =
            URL +
            '&ContextClass=PdmArticle&Format=pdfreactor&Language=1&CSSLink=';
        return URL;
    }
     */


    /**
     * 根据modelnumbe获取contentServ新链接 
     * 20240327
     * Vince
     */
    public static String getContentServProductline(String ModelNumber) {
        String URL = '';
        ModelNumber = ModelNumber + '_EU';
        if(IsSandboxOrg()){
            // https://infoportal-qas.chervon.com.cn/pdf/generate?model-number=RM4000E_EU&brand=EGO EMEA&template-id=DatasheetGlobal&language=English
            URL = 'https://infoportal-qas.chervon.com.cn/pdf/generate?model-number='+ ModelNumber +'&brand=EGO EMEA&template-id=DatasheetGlobal&language=English'; 
        }else {
            // https://infoportal.chervon.com.cn/pdf/generate?model-number=RM4000E_EU&brand=EGO EMEA&template-id=DatasheetGlobal&language=English
            URL = 'https://infoportal.chervon.com.cn/pdf/generate?model-number='+ ModelNumber +'&brand=EGO EMEA&template-id=DatasheetGlobal&language=English'; 
        }

        String lang = UserInfo.getLanguage();
        if(lang == 'de') {
            URL = URL.replace('language=English', 'language=de_DE');
        }
        return URL;
    }
    //https://npo505.saas.contentserv.com/admin/rest/smart/preset/72?ContextIDs=4271&ContextClass=PdmArticle&Format=pdfreactor&Language=1&CSSLink=
    public static String getContentServID(String ModelNumber) {
        String endPointName = 'Chervon_ProductLine';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + ModelNumber;
        String HeaderKey = 'Basic ' + headerToken;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        Map<String, Object> InfoObj = (Map<String, Object>) JSON.deserializeUntyped(
            res.getBody()
        );
        System.debug('InfoObj==>'+InfoObj);
        Object ProductObjs = (Object) InfoObj.get('Product');
        Map<String, Object> InfoObj2 = (Map<String, Object>) ProductObjs;
        Object ContentID = InfoObj2 == null ? null : InfoObj2.get('ID');
        String ContentIDstr = ContentID == null
            ? ''
            : String.valueOf(ContentID);
        return ContentIDstr;
    }
    /**
     * 获取egohome链接 弃用  
     * 20240327
     * Vince
    public static String getEGOHomelink() {
        //String ContentSessionStr = getContentServSession('DistributorEGONA','Cherv0n!@#','https://ego-us.saas.contentserv.com');
        String ContentSessionStr = getContentServSession(
            'Chervon_EGO',
            'https://ego-us.saas.contentserv.com'
        );
        String URL =
            'https://ego-emesaas.contentserv.com/admin/rest/deepsearch/?t=10' +
            ContentSessionStr;
        return URL;
    }
    */

    /**
     * 获取egohome新链接 
     * 20240327
     * Vince
     */
    public static String getEGOHomelink() {
        String URL ='';
        if(IsSandboxOrg()){
            URL = 'https://infoportal-qas.chervon.com.cn/en_GB/ego/home?e_code=6600e6989bdf9cea36f666fc';
        }else{
            URL = 'https://infoportal.chervon.com.cn/en_GB/ego/home?e_code=65f3eef5fbae598857472fd4';
        }
        
        return URL;
    }

    public static String getContentServSession(
        String endPointName,
        String ctsWebsite
    ) {
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c, userName__c, password__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String ctsUser = MeIS.userName__c;
        String ctsPassword = MeIS.password__c;
        endPoint = endPoint + ctsUser;
        endPoint = endPoint + '&ctsPassword=';
        endPoint = endPoint + ctsPassword;
        endPoint = endPoint + '&ctsWebsite=';
        endPoint = endPoint + ctsWebsite;
        String HeaderKey = 'Basic ' + headerToken;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        Map<String, Object> InfoObj = (Map<String, Object>) JSON.deserializeUntyped(
            res.getBody()
        );
        Object TicketObjs = (Object) InfoObj.get('Ticket');
        Map<String, Object> InfoObj2 = (Map<String, Object>) TicketObjs;
        Object ContentSession = InfoObj2.get('Parameter');
        String ContentSessionStr = ContentSession == null
            ? ''
            : String.valueOf(ContentSession);
        return ContentSessionStr;
    }


    public static String getPimSession(String endPoint
    ) {
        // String endPoint =   
        //     'https://pim.chervon.com.cn/rest/V1.0/media/originalimage/D1200012533157';
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        System.debug('username&passward===>'+headerToken);
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        System.debug('sso Headerkey==>'+Headerkey);
        HttpResponse res = CCM_ServiceCallout.getDataViaStaticResourceHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        System.debug('pim sso body==>'+res.getBody());
        String pimUrl = res.getBody();
        if(String.isNotEmpty(pimUrl)){
            pimUrl = pimUrl.replace('file://amznfsxuotstuy3.corp.chervon.com/share/Chervon/IMMVolumes/Volume0/opasdata/d120001', 'http://pimmedia.chervon.com.cn');
            System.debug('final pim url ===>'+pimUrl);
            return pimUrl;
        }
        System.debug('final pim url ===>'+pimUrl);


        // Map<String, Object> InfoObj = (Map<String, Object>) JSON.deserializeUntyped(
        //     res.getBody()
        // );
        // System.debug('InfoObj===========>'+InfoObj);
        return pimUrl;
    }
    public class SyncRes {
        public String ReturnCode;
        public String ReturnMessage;
    }

    public class SyncResult {
        public String ReturnCode;
        public String ReturnMessage;
        public String Process_Status;
        public List<ProcessResult> Process_Result;
    }

    public class ProcessResult {
        public String SFDC_Id;
        public String Oracle_Id;
        public String Error_Message;
        public String Error_Detail;
    }
    /**
     * Customer  SF->Oracle
     * Response Sample：
     * {
        "CUSTOMER_HEADER_ID":"Cust-EU-********",
        "CUSTOMER_SF_ID":"Cust-EU-********",
        "CUSTOMER_ORACLE_ID":"",
        "PROCESS_STATUS":"Error",
        "PROCESS_MESSAGE":"Creation of Party and customer account failed:1) 2)"
    }
     */
    public class CustomerProcessResult {
        public String CUSTOMER_HEADER_ID;
        public String CUSTOMER_SF_ID;
        public String CUSTOMER_ORACLE_ID;
        public String ACCOUNTNUMBER;
        public String PROCESS_STATUS;
        public String PROCESS_MESSAGE;
    }

    
    public class AddressResult {
        public String ADDRESS_SF_ID;
        public String ADDRESS_ORACLE_NUMBER;
        public String CUSTOMER_ORACLE_ID;
        public List<ContactLineResult> ContactLine;
        public String PROCESS_STATUS;
        public String PROCESS_MSG;
    }
    public class ContactLineResult {
        public String ADDRESS_SF_ID;
        public String ADDRESS_ORACLE_NUMBER;
        public String CONTACT_SF_ID;
        public String CONTACT_ORACLE_ID;
        public String CONTACT_ORACLE_NUMBER;
    }

}