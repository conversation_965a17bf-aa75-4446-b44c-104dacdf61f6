import generatepackage
import validatepackage
import runtest
import os
import subprocess
import shutil

package_path = r'package'
repo = 'https://188977:<EMAIL>/crm/chervon-eu.git'

FAIL = '\033[91m'
ENDC = '\033[0m'


def process():
    branch_name = checkout_branch()
    if branch_name is None:
        print('Branch not in specific list')
        return
    remove_existing_package()
    process_deployment(branch_name)


def remove_existing_package():
    if os.path.exists(package_path):
        shutil.rmtree(package_path)


def get_commit_branch():
    cmd = 'git log -n 1 --pretty=format:"%H"'
    commit_hash = get_shell_result(cmd)
    print('commit hash: {0}'.format(commit_hash))

    cmd = 'git branch -a --contains ' + commit_hash
    branch_name = get_shell_result(cmd)
    print('branch name: {0}'.format(branch_name))

    branch = None
    if 'development' in branch_name:
        branch = 'development'
    elif 'DEV' in branch_name:
        branch = 'DEV'
    elif 'SIT' in branch_name:
        branch = 'SIT'
    elif 'UAT' in branch_name:
        branch = 'UAT'
    elif 'HotFix' in branch_name:
        branch = 'HotFix'
    elif 'PreProd' in branch_name:
        branch = 'PreProd'
    print('branch: {0}'.format(branch))
    return branch


def checkout_branch():
    project_path = r'../..'
    os.chdir(project_path)
    
    cwd = os.getcwd()
    print('Current work directory: {0}'.format(cwd))
    cmd = 'git config --global --add safe.directory ' + cwd
    call_shell(cmd)

    cmd = 'git remote set-url origin ' + repo
    call_shell(cmd)

    branch_name = get_commit_branch()
    if branch_name is None:
        return None
    cmd = 'git checkout ' + branch_name
    call_shell(cmd)
    cmd = 'git fetch --tags'
    call_shell(cmd)
    return branch_name


def process_deployment(branch_name):
    has_file_diff = generatepackage.process(branch_name)
    if not has_file_diff:
        print('No Change File Found')
        return
    is_validate_success = validatepackage.validate_package(True, branch_name)
    if is_validate_success:
        is_test_success = True
        if branch_name == 'PreProd':
            is_test_success = runtest.run_test(branch_name)
        if is_test_success:
            is_deploy_success = validatepackage.validate_package(False, branch_name)
            if is_deploy_success:
                print('Deploy Success')
                remark_release_tag(branch_name)
            else:
                print(f"{FAIL}Deploy Fail. Check errors in deploy_result.txt{ENDC}")
                remove_existing_package()
        else:
            print(f"{FAIL}Test Fail. Check errors in test_result.csv{ENDC}")
            remove_existing_package()
    else:
        print(f"{FAIL}Validate Fail. Check errors in deploy_result.txt{ENDC}")

def remark_release_tag(branch_name):
    cmd = 'git tag -d {0}Release'.format(branch_name)
    call_shell(cmd)
    cmd = 'git push --delete origin {0}Release'.format(branch_name)
    call_shell(cmd)

    latest_commit_file_path = r'cicd/config/gitparam/latest_commit.txt'
    with open(latest_commit_file_path, 'r') as f:
        commits = f.readlines()
    commits = [x.strip() for x in commits]

    cmd = 'git tag -a {0}Release '.format(branch_name) + commits[0] + ' -m "remark release tag"'
    call_shell(cmd)
    cmd = 'git push origin {0}Release'.format(branch_name)
    call_shell(cmd)


def call_shell(cmd):
    subprocess.call(cmd, shell=True)


def get_shell_result(cmd):
    return subprocess.run(cmd, shell=True, stdout=subprocess.PIPE).stdout.decode('utf-8')


def main():
    process()


if __name__ == "__main__":
    main()
