/**
 * Created by gluo006 on 10/30/2019.
 */
 
({
    generateCondition: function(component){
        var defaultCondition = '[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"Value":"PIM","FieldName":"Source__c","Condtion":"="},{"Value":"Product","FieldName":"Recordtype.Name","Condtion":"="} ]';
        defaultCondition= defaultCondition.replace('{1}', component.get('v.brand'));
        component.set('v.prodCondition', defaultCondition);
    },
    generatePartsCondition: function(component){
        var defaultCondition = '[{"Value":"true","FieldName":"IsActive","Condtion":"="},{"Value":"(\'{1}\')","FieldName":"Brand_Name__c","Condtion":"IN"},{"Value":"PIM","FieldName":"Source__c","Condtion":"="}]';
        defaultCondition= defaultCondition.replace('{1}', component.get('v.brand'));
        component.set('v.partsCondition', defaultCondition);
    },
    IsBatteryOrCharger: function(component){
        var action = component.get("c.IsBatteryOrCharger");
         action.setParams({
             "productId":  component.get('v.productId')
         });
        action.setCallback(this, function (response) {
            console.log(JSON.parse(JSON.stringify(response)),"response")
            var state = response.getState();
            console.log('state--->'+ state);
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if(results){
                    // component.set('v.disableBtn', true);
                    $A.get("e.force:showToast").setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": $A.get("$Label.c.CCM_NoPartsExist"),
                        "type": "Error"
                    }).fire();
                }else{
                    component.set('v.disableBtn', false);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    getPartsInfo: function(component, PartsNum){
        var self = this;
        var orderItemList = component.get('v.orderItemList');
        var rowIndex = component.get('v.operationRow');

        orderItemList[rowIndex].Name = orderItemList[rowIndex].PartsNum.Name;
        orderItemList[rowIndex].quantity = '1' //results.PartsList.Parts__r.quantity;
        orderItemList[rowIndex].Brand__c = "EGO";
        orderItemList[rowIndex].ProductCode = orderItemList[rowIndex].PartsNum.ProductCode;
        orderItemList[rowIndex].ProductDescription = orderItemList[rowIndex].PartsNum.ProductDescription;
        orderItemList[rowIndex].Id = orderItemList[rowIndex].PartsNum.Id;
        orderItemList[rowIndex].partId = orderItemList[rowIndex].PartsNum.partId;
        orderItemList[rowIndex].finalPrice = orderItemList[rowIndex].PartsNum.finalPrice;
        component.set('v.orderItemList', orderItemList);
        self.hideEle(component, 'modalSpinner');
    },
    checkEligibility: function(component){
        console.log("点了check")
        var self = this;
        self.hideEle(component, 'checkResult');
        var action = component.get("c.eligibilityCheck");
        action.setParams({
            'partsList': JSON.stringify(component.get('v.orderItemList')),
            "productId": component.get('v.productId'),
            "brand": component.get('v.brand')
        });

        action.setCallback(this, function (response) {
            console.log(JSON.parse(JSON.stringify(response)),"response")

            var state = response.getState();
            console.log('state--->'+ state);
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                component.set('v.isBusy',false)
                console.log(results);
                if (results){
                    self.showEle(component, 'checkResult');
                    component.set('v.checkResult', results.Message);
                    if(results.Status == 'Success'){
                        if('result' in results) {
                            component.set('v.result', results.result);
                            if(results.result === 'repair' || results.result === 'replacement') {
                                component.set('v.showNewClaimButton', true);
                            }
                            else {
                                component.set('v.showNewClaimButton', false);
                            }
                        }
                        else {
                            component.set('v.result', '');
                            component.set('v.showNewClaimButton', false);
                        }
                    }
                    else {
                        component.set('v.result', '');
                        component.set('v.showNewClaimButton', false);
                    }
                    if(results.Status == 'Success'){
                        component.set('v.lampColor', 'eligible');
                    }else{
                        component.set('v.lampColor', 'ineligible');
                    }
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
                self.hideEle(component, 'modalSpinner');
            }
        });
        $A.enqueueAction(action);
    },
    GenerateVersionList: function(component, productId){
        var action = component.get("c.GenerateVersionList");
        action.setParams({
            "productId": productId,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results){
                    component.set('v.versionList', results.Version);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    showEle: function(component, eleName) {
        let cmpTarget = component.find(eleName);
        $A.util.removeClass(cmpTarget, 'slds-hide');
    },

    hideEle: function(component, eleName) {
        let cmpTarget = component.find(eleName);
        $A.util.addClass(cmpTarget, 'slds-hide');
    }
});