({
    doInit : function(component, event, helper) {
        var userInfo = $A.get("$SObjectType.CurrentUser");
        component.set('v.currentUserId', userInfo.Id);
        console.log(component.get('v.currentUserId'), 'currentUserId===========');
        component.set('v.isBusy', true);
        // 判断url参数
        const params = new URLSearchParams(window.location.search);
        const tabsValue =  params.get('tabsValue');
        console.log(tabsValue, 'tabsValue==========');
        if (tabsValue) {
            component.set('v.tabId', tabsValue);
        }
        var host = window.location.origin;
        component.set('v.vfHost', host);

        var columns = [
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '80px',
                tdStyle:'text-align: left',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${row}",
                        variant:"bare",
                        showTitle:false,
                        iconName:"utility:preview",
                        alternativeText:"View",
                        onclick: component.getReference('c.doView')
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${Id}",
                        variant:"bare",
                        showTitle:false,
                        label:"",
                        iconName:"utility:edit",
                        alternativeText:"Edit",
                        class: "${editStyleCss}",
                        onclick: component.getReference("c.doEdit")
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${Id}",
                        variant:"bare",
                        iconName:"utility:delete",
                        alternativeText:"Delete",
                        class: "${deleteStyleCss}",
                        onclick: component.getReference("c.doDelete")
                    }
                }
            ]
            },
            {label: $A.get("$Label.c.Order_CustomerPO"), fieldName:'CustomerPo'},
            {label: $A.get("$Label.c.CCM_OrderNumber"), fieldName: 'OrderNumber'},
            {label: $A.get("$Label.c.CCM_OrderType"), fieldName: 'OrderType'},
            {label: $A.get("$Label.c.Order_OrderStatus"), fieldName: 'OrderStatus'},
            {label: $A.get("$Label.c.Order_TotalDueAmount"),
                children:[
                {
                    type: "lightning:formattedNumber",
                    attributes:{
                        value: "${TotalDueAmount}",
                        currencyCode: $A.get("$Locale.currencyCode"),
                        currencyDisplayAs:"code",
                        style:"currency"
                    }
                }]
            },
            {label: $A.get("$Label.c.Order_OrderCreatedDateinEBS"), fieldName: 'OrderCreatedDateInEBS'},
            {label: $A.get("$Label.c.CCM_CreatedBy"), fieldName: 'CreatedBy'},
        ];
        var columnsSN= [
            {   
                label: $A.get("$Label.c.CCM_Action"),
                width: '50px',
                tdStyle: 'text-align: center',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${rowData}",
                        variant:"bare",
                        iconName:"utility:arrow_bottom",
                        alternativeText:"download",
                        class: "${isShowBtn}",
                        onclick: component.getReference('c.doDownload')
                    }
                },
          ]},
            {label: $A.get("$Label.c.CCM_RequestTime"), fieldName: 'requestDate'},
            {label: $A.get("$Label.c.CCM_RequestType"), fieldName: 'exportType'},
            {label: $A.get("$Label.c.CCM_RequestUser"), fieldName:'requestUser'},
            {label: $A.get("$Label.c.CCM_Type"), fieldName: 'type'},
            {label: $A.get("$Label.c.CCM_RequestValue"), fieldName: 'requestValue'},
            {label: $A.get("$Label.c.CCM_TotalNum"), fieldName: 'totalNum'},
            {label: $A.get("$Label.c.CCM_Status"), fieldName: 'status'},
            {label: $A.get("$Label.c.CCM_Error"), fieldName: 'errorMsg', tdStyle: 'color: red; max-width: 300px;'}, //Added by Zoe

        ];
        component.set('v.columns',columns);
        component.set('v.columnsSN',columnsSN);
        helper.getObjectRecords(component,event, helper);
        helper.initNumberData(component,event, helper);
        // 初始化获取SN表格数据
        // helper.getSNTableData(component,event, helper);
        component.set('v.isBusy', false);
    },
    pageChange: function(component, event, helper) {
        const tabStr = component.get('v.tabId');
        var pageNumber = event.getParam("pageNumber");
        component.set("v.pageNumber",pageNumber);
        switch (tabStr) {
            case 'Order':
                helper.getObjectRecords(component,event, helper);
                helper.initNumberData(component,event, helper);
                break;
            case 'SN':
                component.set('v.isBusy', true);
                helper.getSNTableData(component,event, helper);
                // component.set('v.isBusy', true);
                break;
            default:
                break;
        }
        event.stopPropagation();
    },
    pageCountChange : function(component, event, helper){
        const tabStr = component.get('v.tabId');
        var pageCount = event.getParam("pageCount");
        component.set("v.pageCount",pageCount);
        component.set("v.pageNumber", 1);
        switch (tabStr) {
            case 'Order':
                helper.getObjectRecords(component,event, helper);
                helper.initNumberData(component,event, helper);
                break;
            case 'SN':
                component.set('v.isBusy', true);
                helper.getSNTableData(component,event, helper);
                break;
            default:
                break;
        }
        event.stopPropagation();
    },
    doSearch : function(component, event, helper){
        console.log(event, 'doSearch=========');
        component.set('v.pageNumber', 1);
        var orderType = event.getParam("orderType");
        var orderStatus = event.getParam("orderStatus");
        var orderNumber = event.getParam("orderNumber");
        var customerPO = event.getParam("customerPO");
        var submitDateMin = event.getParam("submitDateMin");
        var submitDateMax = event.getParam("submitDateMax");
        component.set('v.orderType', orderType);
        component.set('v.status', orderStatus);
        component.set('v.orderNum', orderNumber);
        component.set('v.customerPo', customerPO);
        component.set('v.fromDate', submitDateMin);
        component.set('v.endDate', submitDateMax);
        helper.getObjectRecords(component,event, helper);
    },
    goToPartsOrder : function(component, event, helper){
        var url = window.location.origin + '/s/parts-order';
        window.open(url,'_self');
    },
    doView : function(component, event, helper){
        var rowData = event.getSource().get('v.value');
        console.log(JSON.stringify(rowData), 'rowData-----------');
        // 判断是否warranty order
        if (rowData.recordType == 'Warranty_Order') {
            let url = window.location.origin + '/s/warranty-order?0.recordId=' + rowData.Id + '&0.accId=' + rowData.CustomerId;
            window.open(url, '_self');
        } else {
            // 判断是order还是po
            if (rowData.OrderStatus === $A.get("$Label.c.New_Order") || rowData.OrderStatus === $A.get("$Label.c.Submitted")) {
                let url = window.location.origin + '/s/po-detail?0.recordId=' + rowData.Id;
                window.open(url,'_self');
            } else {
                let url = window.location.origin + '/s/purchase-order-detail-page?recordId=' + rowData.Id;
                window.open(url,'_self');
            }
        }
    },
    doEdit : function(component, event, helper){
        var recordId = event.getSource().get('v.value');
        let url = window.location.origin + '/s/place-order?0.recordId=' + recordId + '&0.type=draft';
        window.open(url,'_self');
    },
    doDelete : function(component, event, helper){
        var recordId = event.getSource().get('v.value');
        var action = component.get("c.deleteOrderInfo");
        action.setParams({
            "recordId": recordId,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if (results === 'Success'){
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Success"),
                        "message": $A.get("$Label.c.CCM_OrderDeleteTips"),
                        "type": "success"
                    });
                    toastEvent.fire();
                    
                    var refreshEvt = $A.get('e.force:refreshView');
                    refreshEvt.fire();
                }else{
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": $A.get("$Label.c.CCM_OrderDeleteFailTips"),
                        "type": "error"
                    });
                    toastEvent.fire();
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    // 下载SN Excel
    doDownload : function(component, event, helper){
        component.set('v.isBusy', true);
        var rowData = event.getSource().get('v.value');
        console.log(rowData, 'rowData=============');
        helper.getSNExportData(component, rowData.id, rowData.exportType, 'SN_Report_'+rowData.type+'_' + rowData.requestValue);
        // helper.getSNExportData(component, rowData.id, rowData.exportType, 'SN_Report_Order_' + rowData.requestValue);
    },
    // 打开sn导出弹框
    openSnPopup : function(component, event, helper){
        component.set("v.modalFlag", true);
        // 关闭计时器
        clearInterval(component.get('v.timer'));
        console.log('清除计时器===========');
    },
    // 提交事件
    applyRequest : function(component, event, helper){
        // 三选一参数判断
        const orderNo = JSON.parse(JSON.stringify(component.get('v.orderNo')));
        const invoiceNo = JSON.parse(JSON.stringify(component.get('v.invoiceNo')));
        const containerNo = JSON.parse(JSON.stringify(component.get('v.containerNo')));
        console.log(orderNo.Id, invoiceNo.Id, '三选一参数判断==========');
        if (orderNo.Id) {
            component.set('v.requestValue', orderNo.Name);
            component.set('v.requestType', 'Order');
            console.log(orderNo.Id, 'Order===========');
        } else if (invoiceNo.Id) {
            component.set('v.requestValue', invoiceNo.Name);
            component.set('v.requestType', 'Invoice');
            console.log(invoiceNo.Id, 'Invoice===========');
        } else if (containerNo) {
            component.set('v.requestValue', containerNo);
            component.set('v.requestType', 'Container');
        } else {
            component.set('v.isError', true);
            return;
        }
        const year = new Date().getFullYear().toString();
        const month = new Date().getMonth().toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
        const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
        const hour = new Date().getHours().toString().length < 2 ? '0' + new Date().getHours() : new Date().getHours();
        const minutes = new Date().getMinutes().toString().length < 2 ? '0' + new Date().getMinutes() : new Date().getMinutes();
        const seconds = new Date().getSeconds().toString().length < 2 ? '0' + new Date().getSeconds() : new Date().getSeconds();
        const milliseconds = new Date().getMilliseconds().toString();
        // 毫秒补零
        if (milliseconds.length === 1) {
            milliseconds = '00' + milliseconds;
        } else if (milliseconds.length === 2) {
            milliseconds = '0' + milliseconds;
        }
        const requestDate = `${year}-${month}-${day} ${hour}:${minutes}:${seconds}`;
        const requestId = `${year}${month}${day}${hour}${minutes}${seconds}${milliseconds}`;
        const requestValue = component.get('v.requestValue');
        const type = component.get('v.requestType');
        const exportType = component.get('v.exportType');
        const exportParams = {
            requestDate,
            requestId,
            requestValue,
            type,
            exportType,
            orgType: 'Portal',
        };
        console.log(JSON.stringify(exportParams), 'exportParams===========');
        component.set('v.isBusy', true);
        var action = component.get("c.UpsertExportRecord");
        action.setParams({
            exportJsonString: JSON.stringify(exportParams),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                helper.showToast('Success', $A.get("$Label.c.CCM_SNApplySuccessTips"), 10000);
                component.set("v.modalFlag", false);
                component.set('v.isEditOrder', false);
                component.set('v.isEditInvoice', false);
                component.set('v.isEditContainer', false);
                component.set('v.isError', false);
                // 置空
                component.set('v.orderNo', {});
                component.set('v.containerNo', '');
                component.set('v.invoiceNo', {});
                // 刷新表格
                const tabStr = component.get('v.tabId');
                if (tabStr === 'SN') {
                    helper.getSNTableData(component,event, helper);
                    // 计时器（每10s刷新表格数据）
                    const timer = setInterval(() => {
                        console.log('计时器===========');
                        helper.getSNTableData(component,event, helper);
                    }, 15000);
                    component.set('v.timer', timer);
                } else {
                    component.set('v.tabId', 'SN');
                }
            } else {
                helper.showToast('Failed', $A.get("$Label.c.CCM_SNApplyFailTips"), 10000);
            }
        });
        $A.enqueueAction(action);
    },

    // 关闭sn导出弹框
    cancelEvent: function(component, event, helper) {
        component.set("v.modalFlag", false);
        component.set('v.isEditOrder', false);
        component.set('v.isEditInvoice', false);
        component.set('v.isEditContainer', false);
        component.set('v.isError', false);
        // 置空
        component.set('v.orderNo', {});
        component.set('v.containerNo', '');
        component.set('v.invoiceNo', {});
        // 计时器（每10s刷新表格数据）
        const timer = setInterval(() => {
            console.log('计时器===========');
            helper.getSNTableData(component,event, helper);
        }, 10000);
        component.set('v.timer', timer);
    },

    // tabs点击事件
    handleChange: function(component, event, helper){
        var selected = component.get("v.tabId");
        component.set('v.tabId', selected);
        // 重置分页
        component.set('v.pageNumber', 1);
        component.set('v.pageCount', 10);
        component.set('v.totalRecords', 0);
        switch (selected) {
            case 'Order':
                // helper.getInit(component, 'user');
                console.log('Order=========');
                helper.getObjectRecords(component,event, helper);
                helper.initNumberData(component,event, helper);
                // 关闭计时器
                clearInterval(component.get('v.timer'));
                console.log('清除计时器===========');
                break;
            case 'SN':
                // helper.getInit(component, 'warranty');
                console.log('SN=========');
                component.set('v.isBusy', true);
                helper.getSNTableData(component,event, helper);
                // 计时器（每10s刷新表格数据）
                const timer = setInterval(() => {
                    console.log('计时器===========');
                    helper.getSNTableData(component,event, helper);
                }, 10000);
                component.set('v.timer', timer);
                break;
            default:
                break;
        }
    },

    // 子组件lookup传值
    getOrderFromLookup : function(component, event, helper){
        component.set('v.invoiceNo', '');
        const childCmp = component.find('orderNo');
        console.log(childCmp.get('v.requestType'), JSON.parse(JSON.stringify(component.get('v.orderNo'))), 'order子组件lookup传值==========');
        const requestType = childCmp.get('v.requestType');
        const orderValue = JSON.parse(JSON.stringify(component.get('v.orderNo')));
        // 控制输入框
        if (requestType === 'Order' && orderValue.Id) {
            console.log('只显示order============');
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', true);
            component.set('v.isEditContainer', true);
            component.set('v.isError', false);
        } else {
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', false);
        }
    },
    getInvoiceFromLookup : function(component, event, helper){
        component.set('v.orderNo', '');
        const childCmp = component.find('invoiceNo');
        console.log(childCmp.get('v.requestType'), JSON.parse(JSON.stringify(component.get('v.invoiceNo'))), 'invoice子组件lookup传值==========');
        const requestType = childCmp.get('v.requestType');
        const orderValue = JSON.parse(JSON.stringify(component.get('v.invoiceNo')));
        // 控制输入框
        if (requestType === 'Invoice' && orderValue.Id) {
            console.log('只显示Invoice============');
            component.set('v.isEditOrder', true);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', true);
            component.set('v.isError', false);
        } else {
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', false);
        }
    },
    containerChange : function(component, event, helper){
        console.log(event.currentTarget.value, 'containerInput==========');
        if (!event.currentTarget.value) {
            component.set('v.isEditOrder', false);
            component.set('v.isEditInvoice', false);
            component.set('v.isEditContainer', false);
        } else {
            component.set('v.isEditOrder', true);
            component.set('v.isEditInvoice', true);
            component.set('v.isEditContainer', false);
            component.set('v.isError', false);
        }
    },
    // order template 下载
    orderBatchUpload : function(component, event, helper){
        var url = window.location.origin + '/s/order-batch-upload';
        window.open(url,'_self'); 
    },
    // 下单
    placeOrder : function(component, event, helper){
        let url = window.location.origin + '/s/place-order?0.recordType=Regular_Order';
        window.open(url, '_self');
    },
})