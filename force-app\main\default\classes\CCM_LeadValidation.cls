/**************************************************************************************************
 * Name: CCM_LeadValidation
 * Object: Lead
 * Purpose: 验证
 * Author:  Aria w Zhong
 * Create Date: 2023-06-14
 * Modify History:
 *
 **************************************************************************************************/
public with sharing class CCM_LeadValidation implements Triggers.Handler{
    static boolean isRun = true;
    public void handle(){
        if (isRun){
            if (Trigger.isBefore){
                if (Trigger.isUpdate){
                    upsertCountryDescription((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                    upsertCostCenter((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                    stepCheck((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                    checkCreditLimit((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                    getOnlyStandAB((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                }
                if (Trigger.isInsert){
                    upsertCountryDescription((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                    upsertCostCenter((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                    insertCheck((List<Lead>)Trigger.new, (Map<Id, Lead>)Trigger.oldMap);
                }
            }
        }
    }
    //给Dealer / Brand Partner / Others​只有standard suthorized brand时，sales director不需要审批
    private void getOnlyStandAB(List<Lead> newList, Map<Id, Lead> oldMap){
        for (Lead newItem : newList){
            if (newItem.Approval_Type__c == 'Dealer/BrandPartner Approval' && newItem.Status == 'Pending Review' && newItem.Approval_Status__c == 'Pending for Approval'){
                Lead oldItem = oldMap.get(newItem.Id);
                List<Sales_Program__c> abList = [select id, RecordType.Name
                                                 from Sales_Program__c
                                                 where Prospect__c = :newItem.Id];
                Boolean flag1 = false;
                Boolean flag2 = true;
                if (abList.size() > 0){
                    for (Sales_Program__c abItem : abList){
                        if (abItem.RecordType.Name.containsIgnoreCase('Standard')){
                            flag1 = true;
                        } else{
                            flag2 = false;
                        }

                    }
                }
                if (flag1 && flag2){
                    newItem.OnlyStandAB__c = true;
                } else{
                    newItem.OnlyStandAB__c = false;
                }
            }

        }
    }
    /**校验step的必填内容 */
    public static void stepCheck(List<Lead> newItemList, Map<Id, Lead> oldItemMap){
        String prospectAssociationGroupRecordTypeId = CCM_Constants.PROSPECT_ASSOCIATIONGROUP_RECORD_TYPE_ID;
        for (Lead newItem : newItemList){
            List<String> errList = new List<String>();
            Lead oldItem = oldItemMap.get(newItem.Id);
            /**check step status */
            //不能从convert,pending review 不能往后改状态
            if (oldItem.Status == 'Converted' && newItem.Status <> 'Converted'){
                errList.add(Label.Prospect_Stepcheck_Converted);
            }
            if (oldItem.Status == 'Pending Review' && (!'Pending Review,Converted'.contains(newItem.Status))){
                if (newItem.Status == 'Qualified' && !'Rejected,Draft'.contains(newItem.Approval_Status__c)){
                    errList.add(Label.Prospect_Stepcheck_Pending);
                }
            }

            Boolean isInsideSales = false;
            Profile p = [SELECT Name FROM Profile WHERE Id = :UserInfo.getProfileId() LIMIT 1];
            if(p.Name == CCM_Constants.INSIDE_SALES_MANAGER || p.Name == CCM_Constants.INSIDE_SALES_REP) {
                isInsideSales = true;
            }

            if(CCM_ApprovalProcessBypassUtil.needBypass('Lead', 'CCM_LeadValidation')) {
                if(newItem.Status == 'Pending Review' && oldItem.Status == 'Qualified') {
                    if(isInsideSales) {
                        newItem.Approval_Status__c = 'Approved';
                    }
                }
            }
            //判断是否由submit过来
            if (newItem.Status == 'Pending Review' && !newItem.TestFlag__c){
                String compareStatus = 'Pending for Approval';
                if(CCM_ApprovalProcessBypassUtil.needBypass('Lead', 'CCM_LeadValidation')) {
                    if(isInsideSales) {
                        compareStatus = 'Approved';
                    }
                }
                if ((oldItem.Status <> newItem.Status && oldItem.Status <> 'Qualified') || (oldItem.Status <> newItem.Status && newItem.Approval_Status__c <> compareStatus)){
                    errList.add(Label.Prospect_Stepcheck_Qualified);
                }
            }
            //check to Converted
            if (newItem.Status == 'Converted'){
                if (!'Pending Review,Converted'.contains(oldItem.Status) && !newItem.TestFlag__c){
                    errList.add(Label.Prospect_Stepcheck_Qualified);
                }
                if (newItem.Approval_Status__c <> 'Approved'){
                    errList.add(Label.Prospect_Stepcheck_Approval);
                }
            }
            //check qusetionNaire
            if (String.isBlank(newItem.QuestionNaire__c)){
                if (newItem.Prospect_Type__c == 'End User' && newItem.Classification_1__c == 'Commercial User'){
                    if (newItem.Status == 'Contacted' || newItem.Status == 'Qualified' || newItem.Status == 'Pending Review'){
                        errList.add(Label.Prospect_Stepcheck_QuestionNaire);
                    }
                } else if (newItem.Prospect_Type__c <> 'Brand Partner' && newItem.Prospect_Type__c <> 'End User' && newItem.RecordTypeId != prospectAssociationGroupRecordTypeId){
                    if (newItem.Status == 'Qualified' || newItem.Status == 'Pending Review'){
                        errList.add(Label.Prospect_Stepcheck_QuestionNaire);
                    }

                }
            }
            //check relatedList of Qualified&&Pending
            if ((newItem.Status == 'Qualified' && oldItem.Status <> 'Qualified') || (newItem.Status == 'Pending Review' && oldItem.Status <> 'Pending Review')){
                Lead lead = [select Id, OwnerId, RecordTypeId, (Select Id, Name
                                                                from Account_Address__r), (SELECT Id, Name
                                                                                           from Sales_Program__r), (Select Id, OwnerId
                                                                                                                    from Contacts__r), (Select Id, OwnerId
                                                                                                                                        from Customer_Profile__r), (Select Id, OwnerId
                                                                                                                                                                    from Brand_Partner_Profile__r), (Select Id, OwnerId
                                                                                                                                                                                                     from End_User_Profile__r), (Select Id, OwnerId
                                                                                                                                                                                                                                 from Sales_Rep_Stock_Lentouts__r)
                             from Lead
                             where Id = :newItem.Id];
                if (lead.Sales_Program__r == null || lead.Sales_Program__r.size() == 0){
                    errList.add(Label.Prospect_Stepcheck_AB);
                }
                if (newItem.Prospect_Type__c == 'End User'){
                    List<ContentDocumentLink> fileList = [SELECT LinkedEntityId, ContentDocumentId, ContentDocument.LatestPublishedVersion.PathOnClient, ContentDocument.LatestPublishedVersion.CreatedDate, ContentDocument.LatestPublishedVersion.OwnerId, ContentDocument.LatestPublishedVersion.Owner.Name, ContentDocument.LatestPublishedVersion.FileExtension, ContentDocument.LatestPublishedVersion.ContentSize
                                                          FROM ContentDocumentLink
                                                          WHERE LinkedEntityId = :newItem.Id And ContentDocument.LatestPublishedVersion.ExternalDocumentInfo1 = :CCM_Constants.PROSPECT_FILETYPE_TEST];
                    if (fileList.size() == 0){
                        errList.add(Label.Prospect_Stepcheck_TestReport);

                    }
                    if (lead.Sales_Rep_Stock_Lentouts__r == null || lead.Sales_Rep_Stock_Lentouts__r.size() == 0){
                        errList.add(Label.Prospect_Stepcheck_LentOut);
                    }
                }
                // check ToPending
                if (newItem.Status == 'Pending Review'){
                    //校验

                    if (lead.Account_Address__r == null || lead.Account_Address__r.size() == 0){
                        errList.add(Label.Prospect_Stepcheck_Address);
                    }
                    if (lead.Contacts__r == null || lead.Contacts__r.size() == 0){
                        errList.add(Label.Prospect_Stepcheck_Contact);
                    }
                    //customer profile
                    if (lead.RecordTypeId == CCM_Constants.PROSPECT_ASSOCIATIONGROUP_RECORD_TYPE_ID || lead.RecordTypeId == CCM_Constants.PROSPECT_CHANNEL_RECORD_TYPE_ID){
                        if (lead.Customer_Profile__r == null || lead.Customer_Profile__r.size() == 0){
                            errList.add(Label.Prospect_Stepcheck_CustomerProfile);
                        }
                    }
                    if (lead.RecordTypeId == CCM_Constants.PROSPECT_BRANDPARTNER_RECORD_TYPE_ID){
                        if (lead.Brand_Partner_Profile__r == null || lead.Brand_Partner_Profile__r.size() == 0){
                            errList.add(Label.Prospect_Stepcheck_BrandPartnerProfile);
                        }
                    }
                    if (lead.RecordTypeId == CCM_Constants.PROSPECT_ENDUSER_RECORD_TYPE_ID){
                        if (lead.End_User_Profile__r == null || lead.End_User_Profile__r.size() == 0){
                            errList.add(Label.Prospect_Stepcheck_EndUserProfile);
                        }
                    }

                }

            }
            //报错
            if (errList.size() > 0){
                String errString = '';
                Integer i = 1;
                for (String item : errList){
                    errString += i + '.' + item;
                    i++;
                }
                newItem.addError(errString);
            }
        }
    }
    /**创建时的重复校验 */
    public static void insertCheck(List<Lead> newItemList, Map<Id, Lead> oldItemMap){
        for (Lead newItem : newItemList){
            List<String> errList = new List<String>();

            /**状态校验 */
            if (newItem.Status != 'Open'){
                errList.add(Label.Prospect_InsertCheck_Only);
            }
            if (newItem.Re_automatic__c){
                errList.add(Label.Prospect_InsertCheck_RestartAssignment);
            }
            /**重复校验 */
            if (newItem.Prospect_Type__c == 'End User'){
                //比较转换的customer和contact
                //在prospect比较
                List<Lead> leadList = [select id
                                       from Lead
                                       where Company = :newItem.Company and Name = :newItem.Name];
                if (leadList.size() > 0){
                    errList.add(Label.Prospect_InsertCheck_Repeate);
                }
                List<Account> accountList = [select id
                                             from Account
                                             where name = :newItem.Company];
                List<Contact> contactList = [select id
                                             from Contact
                                             where LastName = :newItem.LastName and MiddleName = :newItem.MiddleName and FirstName = :newItem.FirstName];
                if (accountList.size() > 0 && contactList.size() > 0){
                    errList.add(Label.Prospect_InsertCheck_Reject);

                }
            } else{
                //在prospect比较
                List<Lead> leadList = [select id
                                       from Lead
                                       where Company = :newItem.Company and Country_All__c = :newItem.Country_All__c and State__c = :newItem.State__c and County__c = :newItem.County__c and Province__c = :newItem.Province__c and City__c = :newItem.City__c and Street_1__c = :newItem.Street_1__c and Street_2__c = :newItem.Street_2__c and Postal_Code__c = :newItem.Postal_Code__c];
                if (leadList.size() > 0){
                    errList.add(Label.Prospect_InsertCheck_Repeate);
                }
            }
            //报错
            if (errList.size() > 0){
                String errString = '';
                Integer i = 1;
                for (String item : errList){
                    errString += i + '.' + item;
                    i++;
                }
                newItem.addError(errString);
            }
        }
    }
    /**give value to country__c */
    public static void upsertCountryDescription(List<Lead> newItemList, Map<Id, Lead> oldItemMap){
        for (Lead newItem : newItemList){
            if (Trigger.isInsert){
                List<String> parts = newItem.Country_All__c.split('-');
                newItem.Country__c = parts.get(0);
                newItem.Country_Description__c = parts.get(1);
            }
            if (Trigger.isUpdate){
                Lead oldItem = oldItemMap.get(newItem.Id);
                if (newItem.Country_All__c <> oldItem.Country_All__c){
                    List<String> parts = newItem.Country_All__c.split('-');
                    newItem.Country__c = parts.get(0);
                    newItem.Country_Description__c = parts.get(1);
                }
            }
        }
    }
    /**save costcenter and description  */
    public static void upsertCostCenter(List<Lead> newItemList, Map<Id, Lead> oldItemMap){
        for (Lead newItem : newItemList){
            if (String.isNotBlank(newItem.Cost_Center_All__c)){
                if (Trigger.isInsert){
                    List<String> parts = newItem.Cost_Center_All__c.split('_');
                    newItem.Cost_Center_Description__c = parts.get(0);
                    newItem.Cost_Center__c = parts.get(1);
                }
                if (Trigger.isUpdate){
                    Lead oldItem = oldItemMap.get(newItem.Id);
                    if (newItem.Cost_Center_All__c <> oldItem.Cost_Center_All__c){
                        if (newItem.Approval_Status__c == 'Approved' || newItem.Approval_Status__c == 'Draft'){
                            List<String> parts = newItem.Cost_Center_All__c.split('_');
                            newItem.Cost_Center_Description__c = parts.get(0);
                            newItem.Cost_Center__c = parts.get(1);
                        }

                    }
                }
            }


        }
    }
    /**credit limt check before Finance in Approval */
    private void checkCreditLimit(List<Lead> newList, Map<Id, Lead> oldMap){
        for (Lead newItem : newList){
            Lead oldItem = oldMap.get(newItem.Id);
            if (newItem.Credit_Limit_Check__c == true){
                if (newItem.Prospect_Type__c <> 'End User'){
                    newItem.Description = 'limit:' + newItem.Insurance_Credit_Limit__c + 'country:' + newItem.Country__c;
                    if (newItem.Country__c == 'DE'){
                        if (newItem.Insurance_Credit_Limit__c > 170000){
                            newItem.Is_Finance__c = true;
                        }
                    } else{
                        if (newItem.Insurance_Credit_Limit__c > 340000){
                            newItem.Is_Finance__c = true;
                        }
                    }
                }
            }
        }
    }
}