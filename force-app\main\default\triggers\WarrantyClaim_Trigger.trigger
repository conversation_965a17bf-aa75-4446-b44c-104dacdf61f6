trigger WarrantyClaim_Trigger on Warranty_Claim__c (before insert, before update,after update,after insert){
    new Triggers()
    .bind(Triggers.Evt.beforeinsert, new WarrantyClaimChange())
    .bind(Triggers.Evt.beforeupdate, new WarrantyClaimChange())
    .bind(Triggers.Evt.afterupdate, new WarrantyClaimChange())
    .bind(Triggers.Evt.afterinsert, new WarrantyClaimChange())
    .bind(Triggers.Evt.beforeupdate, new CCM_WarrantyClaim_VATGAPCalculateHandler())
    .manage();
}