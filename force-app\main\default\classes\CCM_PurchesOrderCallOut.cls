public without Sharing class CCM_PurchesOrderCallOut {
    public static final String ENDPOINT_NAME_SEEBURGER_UAT = 'chervon_seeburger_uat';
    
    public static final String ENDPOINT_NAME_SEEBURGER_PROD = 'chervon_seeburger_prod';
    public static String pushOrderInfo(String quoteId) {
        System.debug(LoggingLevel.INFO, '--1-->' + Limits.getCpuTime());
        List<String> paramStrlist = new List<String>();
        
        String querystr =
            'select id,ORG_ID__c,Name,Promotion_Code__c,Warehouse__c,Customer__r.AccountNumber,Customer_PO_Num__c,WearHouse_In_EBS__c,Pricing_Date__c,Customer__r.Owner.FederationIdentifier, ' +
            'Customer__c, Customer__r.Distributor_or_Dealer__c,Shipping_Address__c,Shipping_Address__r.Customer_Line_Oracle_ID__c,'+
            //add by vince ********
            'createdby.name,createdby.FederationIdentifier, ' +
            //add by vince ********
            'Billing_Address__c,Billing_Address__r.Customer_Line_Oracle_ID__c ,VAT__c,Place__c,Order_Type__c,CurrencyIsoCode, ' +
            'Price_List__c,Salesperson__c,Salesperson__r.FederationIdentifier,Submit_Date__c,Payment_Term__c,Dropship_Address__r.Name,' +
            'Freight_Term__c,Inco_Term__c,Shipping_Place__c,Freight_Fee__c,Insurance_Fee__c,' +
            'Other_Fees__c,Change_Forwear_Fee__c,Status__c,Additional_Contact_Name__c,Dropship_Address__r.Customer_Line_Oracle_ID__c,' +
            'Dropship_Address__c, Dropship_Address__r.City__c, Dropship_Address__r.Country__c, Dropship_Address__r.Postal_Code__c, '+
            'Dropship_Address__r.Province__c, Dropship_Address__r.Street_1__c, Dropship_Address__r.Street_2__c,'+
            'Additional_Shipping_Street__c,Additional_Shipping_Street2__c,Additional_Contact_Phone__c,' +
            'Additional_Shipping_Country__c,Additional_Shipping_Postal_Code__c,Additional_Shipping_Province__c,' +
            'Additional_Shipping_City__c,Instruction_To_Dsv__c,Carrier_Information__c,Shipping_Address__r.Country__c,' +
            'Total_Value_Formal__c,Header_Discount__c,Header_Discount_Amount__c,Multi_Header_Promotion__c,' +
            'Total_Value_Net__c,Total_Amount__c,Is_DropShip__c,RecordType.DeveloperName,Comments__c,' +
            '(select id,Name,Product__r.ProductCode,Quantity__c,Sales_Price__c,Purchase_Order__c,Pricing_Date__c,Submit_Date__c,Product__r.Order_Model__c,' +
            'Product_Model__c,Discount__c,Promotion_Discount_Type__c,Header_Discount__c,Stand_Discount__c,unit_Net_Price2__c,Stand_Discount_Value__c,Application_Method__c,' +
            'List_Price_Name__c,Request_Date__c,List_Price__c,Unit_Price__c,CreatedDate,' +
            'Est_Replenish_Date__c,Promotion_Code__c,userPromotion__c,Inventory__c,' +
            'UOM__c,Total_Net_Price__c,Remarks__c,Schedule_Ship_Date__c,Fallshortofthreshold__c  ' +
            'from Purchase_Order_Items__r WHERE Fallshortofthreshold__c != TRUE) ' +
            'from Purchase_Order__c ' +
            'where Id = :quoteId';
            Map<String, Object> paramMap = new Map<String, Object>();
        try{
            SObject obj = Database.query(querystr);
            System.debug(LoggingLevel.INFO, '--2-->' + Limits.getCpuTime());
            
            Purchase_Order__c po = (Purchase_Order__c) obj;
            String strAccountNumber;
            if ('2nd Tier Dealer'.equals(po.Customer__r.Distributor_or_Dealer__c)) {
                for (Account_Address__c objShipTo : [
                    SELECT Customer__r.AccountNumber
                    FROM Account_Address__c
                    WHERE
                    X2nd_Tier_Dealer__c = :po.Customer__c
                    AND (RecordType_Name__c = 'Shipping_Address'
                         OR RecordType_Name__c = 'Dropship_Shipping_Address')
                    AND Approval_Status__c = 'Approved'
                    AND Status__c = TRUE
                    LIMIT 1
                ]) {
                    strAccountNumber = objShipTo.Customer__r.AccountNumber;
                }
            }
            String wareHouse = po.Warehouse__c;
            paramMap.put('ORDER_NUMBER_CRM', po.Name);
            //paramMap.put('ORDER_NUMBER_CRM', '121313');
            system.debug('ORDER_NUMBER_CRM-->'+po.Name);
            paramMap.put('HEADERID',  po.Name);
            //paramMap.put('HEADERID', '121313');
            paramMap.put('PROMOTION_CODE',  po.Multi_Header_Promotion__c);
            paramMap.put('RETURN_TO_SUBINVENTORY',  '');
            paramMap.put('RETURN_FROM_SUBINVENTORY',  '');
            paramMap.put('REQUEST_TO_SUBINVENTORY',  '');
            paramMap.put('REQUEST_FROM_SUBINVENTORY',  '');
            paramMap.put('CURRENCY_CODE',  po.CurrencyIsoCode);
            paramMap.put('ORDER_FLAG',  'YES');
            paramMap.put('SCENE_TYPE',  'Sales Order');
            paramMap.put('VAT',  po.VAT__c);
            system.debug('HEADERID-->'+paramMap.get('HEADERID'));
            paramMap.put(
                'CUSTOMER',
                String.isBlank(strAccountNumber)
                ? po.Customer__r.AccountNumber
                : strAccountNumber
            );
            paramMap.put('ORG_CODE', po.ORG_ID__c);
            system.debug('OrgCode-->'+paramMap.get('ORG_CODE'));
            paramMap.put('PO_NUMBER', po.Customer_PO_Num__c);
            
            // paramMap.put('PO_NUMBER', '121313');
            String ShipToCode = '';
            if(String.isNotBlank( po.Shipping_Address__r.Customer_Line_Oracle_ID__c)){
                ShipToCode = po.Shipping_Address__r.Customer_Line_Oracle_ID__c;
            }else{
                ShipToCode = po.Dropship_Address__r.Customer_Line_Oracle_ID__c;
            }
            paramMap.put('SHIPTO', po.Shipping_Address__r.Customer_Line_Oracle_ID__c);
            paramMap.put('BILLTO', po.Billing_Address__r.Customer_Line_Oracle_ID__c);
            //Honey Update Start调取公共方法获取Oracle Order Type Mapping
            String oracelOrderType = CCM_CovertOrderTypeUtil.covertOrder2OracleType(po.RecordType.DeveloperName, po.Warehouse__c, po.Shipping_Address__r.Country__c);
            /*if( CCM_Constants.DSVR.equals(po.Order_Type__c)){
                
                paramMap.put('ORDER_TYPE', CCM_Constants.EEG_SALES_DOMESTIC);
            }else if(CCM_Constants.DSVP.equals(po.Order_Type__c)){
                paramMap.put('ORDER_TYPE', CCM_Constants.EEG_PRESEASON_DOMESTIC);
                
            } else if(CCM_Constants.DIP.equals(po.Order_Type__c)){
                 paramMap.put('ORDER_TYPE', CCM_Constants.EEG_DROPSHIPMENT_DOMESTIC);
                
            }else if(CCM_Constants.DIR.equals(po.Order_Type__c)){
                paramMap.put('ORDER_TYPE', CCM_Constants.EEG_DROPSHIPMENT_DOMESTIC);
            }
            else {
                paramMap.put('ORDER_TYPE', po.Order_Type__c);  
            }*/
            if(po.RecordType.DeveloperName == CCM_Constants.EEG_Influencer_EU_ORDER) {
                oracelOrderType = 'EEG Influencer EU';
            }
            paramMap.put('ORDER_TYPE', oracelOrderType);  
             system.debug('ORDER_TYPE-->'+paramMap.get('ORDER_TYPE'));
             String priceList = po.Price_List__c;
            if(String.isBlank(po.Price_List__c)){
                //查询Auth上的二级价格册
                List<Sales_Program__c> lstSalesProgram = [
                    SELECT Id , List_Price_1__c,List_Price_1__r.Name FROM  Sales_Program__c WHERE Customer__c = : po.Customer__c
                ];
                if(lstSalesProgram != null && lstSalesProgram.size() > 0 ){
                    priceList = lstSalesProgram[0].List_Price_1__r.Name;
                }
            }
            paramMap.put('PRICE_LIST', priceList);
            
            
            paramMap.put('WARE_HOUSE_ORACLE', po.WearHouse_In_EBS__c);
            paramMap.put('SALES_REP_Code', po.Customer__r.Owner.FederationIdentifier);
            paramMap.put('DATE_ORDER', String.valueOf(po.Submit_Date__c) );
            paramMap.put('CURRENCY_CODE', po.CurrencyIsoCode);
            paramMap.put('PAYMENT_TERM', po.Payment_Term__c);
            paramMap.put('FREIGHT_TERM', po.Freight_Term__c);
            paramMap.put('INCO_TERM', po.Inco_Term__c.toUpperCase());
            paramMap.put('SHPPING_PLACE', po.Shipping_Place__c);
            paramMap.put('PRICE_DATE', String.valueOf(po.Pricing_Date__c) );
            paramMap.put('FERIGHT_FEE', po.Freight_Fee__c > 0 ? String.valueOf(po.Freight_Fee__c)  : '');
            paramMap.put('INSURANCE_FEE', po.Insurance_Fee__c > 0 ? String.valueOf(po.Insurance_Fee__c): '');
            paramMap.put('OTHER_FEE', po.Other_Fees__c > 0 ? String.valueOf(po.Other_Fees__c) : '');
            
            paramMap.put('CHARGE_FORWEAR_FEE', po.Change_Forwear_Fee__c);
            paramMap.put(
                'ORDER_STATUS',
                po.Status__c
            );
            paramMap.put( 'DROPSHIP_NAME',po.Additional_Contact_Name__c == null ? po.Dropship_Address__r.Name : po.Additional_Contact_Name__c );
            paramMap.put('DROPSHIP_ADDRESS1', po.Additional_Shipping_Street__c == null ? po.Dropship_Address__r.Street_1__c : po.Additional_Shipping_Street__c );
            paramMap.put('DROPSHIP_ADDRESS2', po.Additional_Shipping_Street2__c == null ? po.Dropship_Address__r.Street_2__c : po.Additional_Shipping_Street2__c );
            
            
            paramMap.put('DROPSHIP_PHONE', po.Additional_Contact_Phone__c);
            paramMap.put('DROPSHIP_COUNTRY', po.Additional_Shipping_Country__c == null  ? po.Dropship_Address__r.Country__c : po.Additional_Shipping_Country__c );
            paramMap.put('DROPSHIP_ZIP', po.Additional_Shipping_Postal_Code__c == null  ? po.Dropship_Address__r.Postal_Code__c : po.Additional_Shipping_Postal_Code__c );
            paramMap.put('DROPSHIP_STATE', po.Additional_Shipping_Province__c == null  ? po.Dropship_Address__r.Province__c : po.Additional_Shipping_Province__c );
            paramMap.put('DROPSHIP_CITY', po.Additional_Shipping_City__c == null  ? po.Dropship_Address__r.City__c : po.Additional_Shipping_City__c );
            paramMap.put('INSTRUCTION_TO_DSV', po.Instruction_To_Dsv__c);
            paramMap.put('CARRIER_INFORMATION', po.Carrier_Information__c);
            paramMap.put('TOTAL_VALUE', po.Total_Value_Formal__c);
            paramMap.put('HEADER_DISCOUNT', po.Header_Discount__c);
            paramMap.put('HEADER_DISCOUNT_AMOUNT', po.Header_Discount_Amount__c);
            paramMap.put('TOTAL_VALUE_NET', po.Total_Value_Net__c);
            paramMap.put('TOTAL_AMOUNT', po.Total_Amount__c);
            String isDropship = '';
            if(po.Is_DropShip__c){
                isDropship = 'Y';
            }else{
                isDropship = 'N';
            }
            paramMap.put('IS_DROPSHIP', isDropship);
            
            //Modified by John Jiang 2020-04-21 : this field is none in seeburger
            // if(String.isEmpty(po.createdby.FederationIdentifier)){
                // paramMap.put('ATTRIBUTE1', 'Integration');
            // }else {
                paramMap.put('ATTRIBUTE1', po.createdby.name);
            // }
            paramMap.put('ATTRIBUTE2', '');
            if(String.isNotBlank(po.Comments__c)) {
                paramMap.put('ATTRIBUTE2', po.Comments__c);
            }
            paramMap.put('ATTRIBUTE3', '');
            paramMap.put('ATTRIBUTE4', '');
            paramMap.put('ATTRIBUTE5', '');
            paramMap.put('ATTRIBUTE6', '');
            paramMap.put('ATTRIBUTE7', '');
            paramMap.put('ATTRIBUTE8', '');
            paramMap.put('ATTRIBUTE9', '');
            paramMap.put('ATTRIBUTE10', '');
            paramMap.put('ATTRIBUTE11', '');
            paramMap.put('ATTRIBUTE12', '');
            paramMap.put('ATTRIBUTE13', '');
            paramMap.put('ATTRIBUTE14', '');
            paramMap.put('ATTRIBUTE15', '');
            
            
            List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
            List<Purchase_Order_Item__c> itemlist = po.Purchase_Order_Items__r;
            if (itemlist != null && itemlist.size() > 0) {
                for (Purchase_Order_Item__c item : itemlist) {
                    Map<String, Object> itemMap = new Map<String, Object>();
                    itemMap.put('ORDER_NUMBER_CRM', po.Name);
                    itemMap.put('ORDERLINE_CRM_ID', item.Name);
            
                    itemMap.put('LINEID', item.Name);
                    itemMap.put('HEADERID', po.Name);
                 
                    system.debug('HEADERID-->'+itemMap.get('HEADERID'));
                    itemMap.put('CRM_ORDERLINE_NUMBER', item.Name);
                    
                    itemMap.put('PRICE_DATE', String.valueOf(item.Pricing_Date__c) );
                    itemMap.put('PRODUCT_MODEL', item.Product__r.Order_Model__c);
                    itemMap.put(
                        'ORDER_QUANTITY',
                        item.Quantity__c
                    );
                    itemMap.put('LIST_PRICE_NAME', item.List_Price_Name__c);
                    itemMap.put('SERIAL_NUMBER', '');
                    itemMap.put('PRODUCT_RATING_STATUS', '');
                    itemMap.put('PROMOTION_CODE', item.Promotion_Code__c);
                    itemMap.put('LIST_PRICE', item.List_Price__c);
                    
                    itemMap.put('REQUEST_DATE', String.valueOf(item.Request_Date__c) ) ;
                    if(wareHouse == 'Germany (DSV)'){
                        itemMap.put('SUBINVENTORY_CODE', 'EGD01' ) ;
                    }else{
                        itemMap.put('SUBINVENTORY_CODE', 'EG1DS' ) ;
                    }
                    //Todo-->待前端传了sales Price计算-->经过Modifire打折后的计算
                    itemMap.put('SALES_PRICE', item.Sales_Price__c ) ;
                    system.debug('SALES_PRICE-->'+itemMap.get('SALES_PRICE'));
                    //Promotion行折扣
                    
                    if(item.Discount__c != null && item.Discount__c != 0 ){
                        itemMap.put('PROMOTION_DISCOUNT1', item.Discount__c ) ;
                    }else{
                         itemMap.put('PROMOTION_DISCOUNT1','') ;
                    }
                    //promotion 头折扣--->如果不应用头折扣promotioncode就是null
                    if(item.userPromotion__c == false || item.userPromotion__c == null){
                        itemMap.put('PROMOTION_DISCOUNT2',0 ) ;
                    }else{
                        itemMap.put('PROMOTION_DISCOUNT2',item.Header_Discount__c ) ;

                    }
                    itemMap.put('TRAFFIC_LIGHT',item.Inventory__c ) ;
                    
                    //Modifire的折扣
                    if(item.Application_Method__c  == 'Percent'){
                        itemMap.put('STANDARD_DISCOUNT', item.Stand_Discount_Value__c ) ;
                    }else{
                        itemMap.put('STANDARD_DISCOUNT', item.Stand_Discount__c ) ;
                    }
                    
                    //Modifire打折类型
                    itemMap.put('STANDARD_DISCOUNT_TYPE', item.Application_Method__c ) ;
                    //经过promotion行折扣的计算后的价格
                    itemMap.put('UNIT_NET_PRICE1', item.Unit_Price__c ) ;
                    //经过Promotion头折扣的计算后的价格
                    
                    itemMap.put('UNIT_NET_PRICE2', item.unit_Net_Price2__c ) ;
                    
                    itemMap.put('PROMOTION_DISCOUNT1_TYPE', item.Promotion_Discount_Type__c ) ;
                    //最终用户取值价格
                    itemMap.put('UNIT_SELLING_PRICE', item.unit_Net_Price2__c);
                    itemMap.put('CRM_ORDERLINE_NUMBER', item.Name);
                    itemMap.put('EST_REPLENISH_DATE',String.valueOf( item.Est_Replenish_Date__c));
                    itemMap.put('UOM', item.UOM__c);
                    itemMap.put('TOTAL_NET_PRICE', item.Total_Net_Price__c);
                    itemMap.put('REMARK', item.Remarks__c);
                    itemMap.put('SCHEDULE_SHIP_DATE',String.valueOf( item.Schedule_Ship_Date__c)); 
                    itemMap.put('ATTRIBUTE1', '');
                    itemMap.put('ATTRIBUTE2', '');
                    itemMap.put('ATTRIBUTE3', '');
                    itemMap.put('ATTRIBUTE4', '');
                    itemMap.put('ATTRIBUTE5', '');
                    itemMap.put('ATTRIBUTE6', '');
                    itemMap.put('ATTRIBUTE7', '');
                    itemMap.put('ATTRIBUTE8', '');
                    itemMap.put('ATTRIBUTE9', '');
                    itemMap.put('ATTRIBUTE10', '');
                    itemMap.put('ATTRIBUTE11', '');
                    itemMap.put('ATTRIBUTE12', '');
                    itemMap.put('ATTRIBUTE13', '');
                    itemMap.put('ATTRIBUTE14', '');
                    itemMap.put('ATTRIBUTE15', '');
                    
                    itemMaplist.add(itemMap);
                }
            }
            paramMap.put('OrderLine', itemMaplist);
            String paramStr = Json.serialize(paramMap);
            paramStrlist.add(paramStr);
            String endPointName = '';
            if(CCM_Service.IsSandboxOrg()){
                endPointName = ENDPOINT_NAME_SEEBURGER_UAT;
            }else {
                endPointName = ENDPOINT_NAME_SEEBURGER_PROD;
            }
           
            MeIntegration_Setting__mdt MeIS = [
                SELECT End_Point__c, Key__c
                FROM MeIntegration_Setting__mdt
                WHERE DeveloperName = :endPointName
                LIMIT 1
            ];
            String headerToken = MeIS.Key__c;
            
            String endPoint = MeIS.End_Point__c + 'order';
            String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf(headerToken));
        
            SyncRes Sres = new SyncRes();
            if (!Test.isRunningTest()) {
              
                HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
             
                Sres = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);           
            }
            if(Sres.Process_Status == 'Failed' || Sres.Process_Status == null){
                String logId = Util.logIntegration('PushOrder Exception','CCM_PurchesOrderCallOut','POST',JSON.serialize(Sres),Json.serialize(paramMap), Json.serialize(paramMap));
            }else{
                String logId = Util.logIntegration('PushOrder .Log','CCM_PurchesOrderCallOut','POST',JSON.serialize(Sres),Json.serialize(paramMap), Json.serialize(paramMap));
            }
            
            
            return Sres.Process_Status;    
        }catch(Exception e){
            String logId = Util.logIntegration('PushOrder Exception','CCM_PurchesOrderCallOut','POST','报错行数---->'+e.getLineNumber()+'报错信息---->'+e.getMessage(),Json.serialize(paramMap), Json.serialize(paramMap));
        }
        return null;
    }
    
    public class SyncRes {
        
        public String Process_Status;
        
        public String PROCESS_MSG;
        
    }
    
    
    
    
    public class SyncResult {
        
        public String ReturnCode;
        
        public String ReturnMessage;
        
        public String Process_Status;
        
        public List<ProcessResult> Process_Result;
        
    }
    
    
    
    
    public class ProcessResult {
        
        public String SFDC_Id;
        
        public String Oracle_Id;
        
        public String Error_Message;
        
        public String Error_Detail;
        
    }
}