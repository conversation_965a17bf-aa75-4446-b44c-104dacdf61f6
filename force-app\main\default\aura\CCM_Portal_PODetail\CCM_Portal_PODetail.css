.THIS {
    background: #fff;
    padding: 0 5%;
}

.THIS .topCon{
    margin: 0 auto;
    justify-content: space-between;
    padding-bottom: 0;
}

.THIS .CCM_PaddingTop{
    padding-top: 10px;
    text-align:center;
}

.THIS .cCCM_Field .CCM_Label {
    font-size: .8125rem;
    font-weight: 700;
}

.THIS .slds-form-element__control .slds-radio{
    display: inline-block;
    margin-right: 1rem;
    line-height: 1.875rem;
}

.THIS lightning-radio-group .slds-form-element__legend{
    font-weight: normal;
}
.THIS .approvalProcess{
    width: 50%;
    margin: 0 auto;
}
.THIS .approvalProcess  .slds-form-element__control .slds-radio{
    margin-right: 6rem;
}
.THIS .approvalProcess .slds-form-element__control{
    text-align: center;
}

.THIS .ccm_widthStyle {
    width: 48%;
}

.THIS .ccm_iconStyle {
    width: 0%;
    padding-top: 2.5%;
    margin-left: -25px;
    color: blue;
}

.THIS .slds-button_icon-bare {
    line-height: 1;
    vertical-align: middle;
    color: blue;
}

.THIS .ccm_label {
    color: rgba(0, 0, 0, 0.6);
}
.THIS .rebate_label {
    font-weight: bold;
    width: calc(50% - 0.5rem);
    text-align: right;
    display: inline-block;
}
.THIS .rebate_value {
    padding-left:5px;
    width: calc(50% - 0.5rem);
    text-align: left;
    display: inline-block;
    vertical-align: top;
}
.THIS .shipmentInfo .cCCM_Section .cCCM_Section.section_open{
    border: 1px solid #d7d7d7;
}
.THIS .shipmentInfo .cCCM_Section .cCCM_Section .slds-section__title {
    background-color: rgb(250, 250, 249);
    border-top: 1px solid #d7d7d7;
    border-bottom: 1px solid #d7d7d7;
    color: rgb(81, 79, 77);
    font-weight: var(--lwc-fontWeightBold,700);
    font-size: var(--lwc-fontSize3,0.8125rem);
}
.THIS .largeWidth{
    width: 320px !important;
    min-width: 320px;
    max-width: 320px;
    overflow: hidden;
}
.THIS .mediumWidth{
    width: 110px !important;
    min-width: 110px;
    max-width: 110px;
}
.THIS .mediumLWidth{
    width: 140px;
    min-width: 140px;
    max-width: 140px;
}
.THIS .smallWidth{
    width: 80px;
    min-width: 80px;
    max-width: 80px;
}
.THIS .slds-form-element__label {
    color: #080707;
}
.THIS .smallXSWidth{
    min-width: 50px;
    max-width: 50px;
    width: 50px;
}
.THIS .mediumXSWidth{
    width: 100px;
}
.THIS .ccm_fontColor2{
    color: red !important;
}
.THIS .table-wrap {
    width: 100%;
    height: auto;
    max-height: 500px;
    overflow: scroll;
}

.THIS .table-wrap thead th{
    position: sticky;
    top: 0;
    z-index: 1;
}

.THIS .clear-user-agent-styles {
    max-width: 320px !important;
}
.THIS .shipment-table {
    padding: 10px !important;
}
.THIS .productTable > tbody > tr {
    background-color: #ffffff !important;
}
.THIS .productTable > tbody > tr > td {
    background-color: #ffffff !important;
}
.THIS .productTable > tbody > tr:hover{
    background-color: #eee;
}
.THIS .productToolTable > tbody > tr {
    background-color: #eee !important;
}
.THIS .productToolTable > tbody > tr > td {
    background-color: #eee !important;
}
.THIS .productToolTable > tbody > tr:hover{
    background-color: #ffffff;
}
.THIS .content-wrap {
    padding: 10px 40px; 
    max-height: 500px;
}
.THIS .uploadFinished{
    color: #90c41f;
    display: flex;
    justify-content: space-between;
    padding: 5px 0px; 
}
.THIS .delete{
    color: #777;
    cursor: pointer;
    text-decoration: underline;
}
.THIS .footer-btn {
    display: flex;
    justify-content: flex-end;
}
.THIS .slds-button_outline-brand {
    color: #90c41f !important;
    border-color: #90c41f !important;
}
.THIS .table-wrap {
    width: 100%;
    height: auto;
    max-height: 500px;
    overflow: scroll;
}
.THIS .productTable > tbody > tr {
    background-color: #ffffff !important;
}
.THIS .productTable > tbody > tr > td {
    background-color: #ffffff !important;
}
.THIS .productTable > tbody > tr:hover{
    background-color: #eee;
}
.THIS .tool-tabel > tbody > tr {
    background-color: #eee !important;
}
.THIS .tool-tabel > tbody > tr > td {
    background-color: #eee !important;
}
.THIS .tool-tabel > tbody > tr:hover{
    background-color: #ffffff;
}
.THIS .icon-position-wrap {
    position: relative;
}
.THIS .icon-position-tool {
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: 0;
}
.THIS .icon-position-kit {
    position: absolute;
    top: -3px;
    left: 0px;
    opacity: 0;
}