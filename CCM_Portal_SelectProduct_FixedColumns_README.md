# CCM_Portal_SelectProduct 固定列功能实现

## 功能描述
在 `CCM_Portal_SelectProduct` 组件的表格中实现了前4列固定功能，当表格水平滚动时，前4列保持固定不动。

## 固定的列
1. **Action** (操作列) - 宽度: 80px
2. **No.** (序号列) - 宽度: 40px  
3. **Product Description** (产品描述列) - 宽度: 240px
4. **Model #** (型号列) - 宽度: 90px/100px (根据语言调整)

## 实现方式
通过修改 `CCM_Portal_SelectProduct.css` 文件，使用 CSS 的 `position: sticky` 属性来实现列固定功能。

### 主要CSS样式
```css
/* 固定前4列：Action, No., Product Description, Model # */
.THIS .productTable th:nth-child(1),
.THIS .productTable td:nth-child(1) {
    position: sticky;
    left: 0;
    z-index: 99;
    background-color: #ffffff !important;
}

.THIS .productTable th:nth-child(2),
.THIS .productTable td:nth-child(2) {
    position: sticky;
    left: 80px; /* Action列的宽度 */
    z-index: 99;
    background-color: #ffffff !important;
}

.THIS .productTable th:nth-child(3),
.THIS .productTable td:nth-child(3) {
    position: sticky;
    left: 120px; /* Action(80px) + No.(40px) */
    z-index: 99;
    background-color: #ffffff !important;
}

.THIS .productTable th:nth-child(4),
.THIS .productTable td:nth-child(4) {
    position: sticky;
    left: 360px; /* Action(80px) + No.(40px) + Product Description(240px) */
    z-index: 99;
    background-color: #ffffff !important;
}
```

## 特性
- ✅ 前4列在水平滚动时保持固定
- ✅ 保持原有的表格样式和交互功能
- ✅ 支持鼠标悬停效果
- ✅ 支持展开的工具表格固定列
- ✅ 最后一列（删除列）仍然固定在右侧
- ✅ 固定列之间有清晰的边框分隔

## 兼容性
- 支持现代浏览器（Chrome, Firefox, Safari, Edge）
- 与现有的表格功能完全兼容
- 不影响响应式布局

## 测试建议
1. 在不同屏幕尺寸下测试水平滚动功能
2. 验证固定列的交互功能（按钮点击、输入等）
3. 检查展开/收起产品详情时的表格显示
4. 确认鼠标悬停效果正常工作
