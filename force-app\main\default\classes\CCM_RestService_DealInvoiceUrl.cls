/**
 * @url: /services/apexrest/CCM_RestService_DealProductInfo
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 04-09-2024
 * @last modified by  : <EMAIL>
**/

@RestResource(urlMapping='/CCM_RestService_DealInvoiceUrl')
global without sharing class CCM_RestService_DealInvoiceUrl {
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        String resStr = req.requestBody.toString();
        resObj.Process_Result = new List<ReturnItem>();
        List<Invoice_Url__c> lstUrlToUpsert = new List<Invoice_Url__c>();
        //Custome Code 映射SF Id
        Map<String,String> mapCustomerCodeToId = new Map<String,String>();
        Set<String> setCustomerCode = new Set<String>();

        //Order Oracle Id 映射SF Id
        Map<String,String> mapOrderOidToId = new Map<String,String>();
        Set<String> setOrderOid = new Set<String>();

        //Oracle Shipment Id 映射SF Id
        Map<String,String> mapShipmentOidToId = new Map<String,String>();
        Set<String> setShipmentOid = new Set<String>();

        //Oracle Invoice Id 映射SF Id
        Map<String,String> mapInvoiceOidToId = new Map<String,String>();
        Set<String> setInvoiceOid = new Set<String>();
        try{
            if(!resStr.startsWith('[')){
                resStr = '[' + resStr + ']';
            }
            resStr = resStr.replace('\'','\"');
            //解析数据
            reqObjList = parse(resStr);
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObjList);
            //
            //遍历请求体中的数据 映射字段
            if (reqObjList != null && reqObjList.size() > 0) {
                for (ReqestObj reqObj : reqObjList) {
                    setCustomerCode.add(reqObj.CUSTOMER_CODE);
                    setOrderOid.add(reqObj.ORDER_ORACLE_ID);
                    setShipmentOid.add(reqObj.SHIPMENT_ORACLE_ID);
                    setInvoiceOid.add(reqObj.INVOICE_ORACLE_ID);
                }
                mapCustomerCodeToId = getMapCustomerCodeToId(setCustomerCode);
                mapOrderOidToId = getMapOrderOidToSFId(setOrderOid);
                mapShipmentOidToId = getMapShipmentOidToSFId(setShipmentOid);
                mapInvoiceOidToId = getMapInvoiceOidToSFId(setInvoiceOid);
                //映射到SF字段
                for (ReqestObj reqObj : reqObjList) {
                    Invoice_Url__c objUrl = new Invoice_Url__c();
                    objUrl.File_Id__c = reqObj.FILE_ID;
                    objUrl.Invoice_Oracle_Id__c = reqObj.INVOICE_ORACLE_ID;
                    objUrl.Invoice_Oracle_Number__c = reqObj.INVOICE_ORACLE_NUMBER;
                    objUrl.Customer_Number__c = reqObj.CUSTOMER_CODE;
                    objUrl.Order_Oracle_Id__c = reqObj.ORDER_ORACLE_ID;
                    objUrl.Shipment_Oracle_Id__c = reqObj.SHIPMENT_ORACLE_ID;
                    objUrl.Type__c = reqObj.TYPE;
                    objUrl.Url__c = reqObj.URL;
                    objUrl.Created_Date_In_LDox__c = String.isNotBlank(reqObj.CREATEDDATE_IN_LDOX) ?  Datetime.valueOf(reqObj.CREATEDDATE_IN_LDOX) : null;
                    objUrl.Last_Modified_Date_In_LDox__c = String.isNotBlank(reqObj.LAST_MODIFIED_IN_LDOX) ?  Datetime.valueOf(reqObj.LAST_MODIFIED_IN_LDOX) : null;

                    //绑定lookup 字段
                    if(mapCustomerCodeToId.containsKey(reqObj.CUSTOMER_CODE)){
                        objUrl.Customer__c = mapCustomerCodeToId.get(reqObj.CUSTOMER_CODE);
                    }
                    if(mapOrderOidToId.containsKey(reqObj.ORDER_ORACLE_ID)){
                        objUrl.Order__c = mapOrderOidToId.get(reqObj.ORDER_ORACLE_ID);
                    }
                    if(mapShipmentOidToId.containsKey(reqObj.SHIPMENT_ORACLE_ID)){
                        objUrl.Shipment__c = mapShipmentOidToId.get(reqObj.SHIPMENT_ORACLE_ID);
                    }
                    if(mapInvoiceOidToId.containsKey(reqObj.INVOICE_ORACLE_ID)){
                        objUrl.Invoice__c = mapInvoiceOidToId.get(reqObj.INVOICE_ORACLE_ID);
                    }
                    lstUrlToUpsert.add(objUrl);
                }

                System.debug(LoggingLevel.INFO,'***lstUrlToUpsert : ' + lstUrlToUpsert);
                Database.UpsertResult[] resList = Database.upsert(lstUrlToUpsert,Invoice_Url__c.File_Id__c.getDescribe().getSObjectField(),false);
                for (Integer i = 0 ; i < resList.size() ; i++) {
                    if (!resList.get(i).isSuccess()) {
                        Database.Error[] err = resList.get(i).getErrors();
                        ReturnItem request = new ReturnItem();
                        request.External_Id = reqObjList.get(i).FILE_ID;
                        request.Error_Message = 'This Invoice Url was failed saving in Salesforce';
                        request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                        resObj.Process_Result.add(request);
                    }
                }
                //插入成功
                if (resObj.Process_Result.size() == 0) {
                    resObj.Process_Status = 'Success';
                    String logId = Util.logIntegration('Invoice Url Log','CCM_RestService_DealInvoiceUrl','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                }else {
                //插入失败
                    resObj.Process_Status = 'Fail';
                    String logId = Util.logIntegration('Product Exception','CCM_RestService_DealProductInfo','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                    Util.pushExceptionEmail('Accept Product Info',logId,getMailErrorMessage(resObj));
                }
            System.debug(LoggingLevel.INFO, '*** : ' + JSON.serialize(resObj));
            }else{
                resObj.Process_Status = 'Fail';
                ReturnItem empty = new ReturnItem();
                empty.Error_Message = 'Empty JSON';
                empty.Error_Detail = 'Empty JSON';
                resObj.Process_Result.add(empty);

                String logId = Util.logIntegration('Invoice Url Exception','CCM_RestService_DealInvoiceUrl','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                Util.pushExceptionEmail('Accept Invoice Url Info',logId,getMailErrorMessage(resObj));
            }
        }catch (Exception e) {
            resObj.Process_Status = 'Fail';
            ReturnItem request = new ReturnItem();
            request.Error_Message = 'This Invoice Url was failed saving in Salesforce';
            request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
            resObj.Process_Result.add(request);
            // System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration('Invoice Url Exception','CCM_RestService_DealInvoiceUrl','POST',
                                               JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept Invoice Url Info',logId,getMailErrorMessage(resObj));
            return resObj;
        }
        // System.debug(LoggingLevel.INFO, '*** resObj: ' + resObj);
        return resObj;
    }

    /**
     * 映射Customer Code 到 SF Id
     */
    public static Map<String,String> getMapCustomerCodeToId(Set<String> setCustomerCode){
        Map<String,String> mapCustomerCodeToId = new Map<String,String>();
        List<Account> lstCustomer = new List<Account>();
        lstCustomer = [SELECT Id,AccountNumber FROM Account WHERE AccountNumber != null And  AccountNumber in :setCustomerCode];
        if(lstCustomer.size() > 0){
            for(Account customer : lstCustomer){
                mapCustomerCodeToId.put(customer.AccountNumber, customer.Id);
            }
        }
        return mapCustomerCodeToId;
    }

    /**
     * 映射Order Oracle Id 到 SF Id
     */
    public static Map<String,String> getMapOrderOidToSFId(Set<String> setOrderOracleId){
        Map<String,String> mapOrderOidToSFId = new Map<String,String>();
        List<Order> lstOrder = new List<Order>();
        lstOrder = [SELECT Id, Order_OracleID__c FROM Order WHERE Order_OracleID__c in :setOrderOracleId];
        if(lstOrder.size() > 0){
            for(Order order : lstOrder){
                mapOrderOidToSFId.put(order.Order_OracleID__c, order.Id);
            }
        }
        return mapOrderOidToSFId;
    }
    /**
     * 映射Shipment Oracle Id 到 SF Id
     */
    public static Map<String,String> getMapShipmentOidToSFId(Set<String> setShipmentOracleId){
        Map<String,String> mapShipmentOidToSFId = new Map<String,String>();
        List<Shipment__c> lstShipment = new List<Shipment__c>();
        lstShipment = [SELECT Id, Ship_OracleID__c  FROM Shipment__c WHERE Ship_OracleID__c  in :setShipmentOracleId];
        if(lstShipment.size() > 0){
            for(Shipment__c shipment : lstShipment){
                mapShipmentOidToSFId.put(shipment.Ship_OracleID__c, shipment.Id);
            }
        }
        return mapShipmentOidToSFId;
    }
    /**
     * 映射Invoice Oracle Id 到 SF Id
     */
    public static Map<String,String> getMapInvoiceOidToSFId(Set<String> setInvoiceOracleId){
        Map<String,String> mapInvoiceOidToSFId = new Map<String,String>();
        List<Invoice__c> lstInvoice = new List<Invoice__c>();
        lstInvoice = [SELECT Id, Invoice_OracleID__c FROM Invoice__c WHERE Invoice_OracleID__c in :setInvoiceOracleId];
        if(lstInvoice.size() > 0){
            for(Invoice__c invoice : lstInvoice){
                mapInvoiceOidToSFId.put(invoice.Invoice_OracleID__c, invoice.Id);
            }
        }
        return mapInvoiceOidToSFId;
    }

    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
       	errContent += 'Process Status : Fail<br/><br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
            	errContent += 'External ID : ' + Item.External_Id + '<br/>';
            	errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
            	errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>';
            }
        }
        return errContent;
    }
 

    global class ReqestObj {
        global String FILE_ID;
        global String INVOICE_ORACLE_ID;
        global String INVOICE_ORACLE_NUMBER;
        global String CUSTOMER_CODE;
        global String ORDER_ORACLE_ID;
        global String SHIPMENT_ORACLE_ID;
        global String TYPE;
        global String URL;
        global String CREATEDDATE_IN_LDOX;
        global String LAST_MODIFIED_IN_LDOX;
    }

	global static List<ReqestObj> parse(String jsonStr) {
		return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }

    global class ResultObj {
        global String Process_Status;
        //global Integer IgnoreSize;
        global List<ReturnItem> Process_Result;
    }

    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
}