<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Dunning_Letter_Contact__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <externalId>false</externalId>
    <label>Dunning Letter – Customer</label>
    <lookupFilter>
        <active>true</active>
        <booleanFilter>(1 AND 4) OR (2 AND 3)</booleanFilter>
        <filterItems>
            <field>$Source.Customer__c</field>
            <operation>equals</operation>
            <valueField>Contact.Account.Id</valueField>
        </filterItems>
        <filterItems>
            <field>$Source.Prospect__c</field>
            <operation>equals</operation>
            <valueField>Contact.Prospect__c</valueField>
        </filterItems>
        <filterItems>
            <field>$Source.Customer__c</field>
            <operation>equals</operation>
            <value></value>
        </filterItems>
        <filterItems>
            <field>$Source.Customer__c</field>
            <operation>notEqual</operation>
            <value></value>
        </filterItems>
        <infoMessage>Contact and address should belong to same customer Or prospect.</infoMessage>
        <isOptional>false</isOptional>
    </lookupFilter>
    <referenceTo>Contact</referenceTo>
    <relationshipLabel>Address (Dunning Letter Contact)</relationshipLabel>
    <relationshipName>Dunning_Letter_Contacts</relationshipName>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Lookup</type>
</CustomField>
