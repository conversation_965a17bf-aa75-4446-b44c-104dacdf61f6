/**
* Author: Honey
* Date: 2023/06/10
* Description: 批量导入Purchase Order 信息。并查询Address Code
*/
public without sharing class CCM_PurchaseBatchUploadController {
    //根据CustomerId查询Customer可选的Product信息-->Model号
     @AuraEnabled
   public static List<String> QueryAvalibleProduct(String CustomerId){
        Date PricingDate = Date.today();
        Account objAccount = [
            SELECT OwnerId,Id,CurrencyIsoCode FROM Account WHERE Id = :CustomerId
        ];
        List<Product2> filteredProducts = new List<Product2>();
        //根据用户Id查询中间表-->校验是否包含All Item 
        List<MasterProductPrice__c> lstMasterProduct = [
            SELECT Id,Account__c,End_Date__c,Start_Date__c,Product__c,Has_AllItem__c
            FROM MasterProductPrice__c 
            
            WHERE Account__c = :CustomerId AND End_Date__c >= :PricingDate AND Start_Date__c <= :PricingDate
            AND Modifier_Entry__C != NULL
            AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
            AND  Is_Delete__c != TRUE ORDER By min_Final_Prority__c ASC
        ]; 
        
        //只要有一个包含ALL Item就直接取所有的Product-->获取该Customer下所有的Product
        Boolean hasAllItem = false;
        List<String> lstSearchProductIds = new List<String>();
        List<String> lstmasterProductIds = new List<String>();
        for(MasterProductPrice__c objMasterProduct : lstMasterProduct){
            if(objMasterProduct.Has_AllItem__c){
                //有一个为AllItem则为AllItem
                hasAllItem = true;
            }
            lstmasterProductIds.add(objMasterProduct.Product__c);
        }
        if(hasAllItem){
            //标识全部的PriceBook绑定的Customer信息
            List<String> lstProductIds = CCM_GetProductInfoUtil.getPriceBookEntry(CustomerId,PricingDate,'',objAccount.CurrencyIsoCode);
            lstSearchProductIds.addAll(lstProductIds);
        }else{
            //只有满足Modifire的才在里面
            lstSearchProductIds.addAll(lstmasterProductIds);
        }
        //
        List<String> lstProductModel = new List<String>();
        for(Product2 objProduct : [SELECT Id,Order_Model__c FROM Product2 WHERE Id IN :lstSearchProductIds ]){
            lstProductModel.add(objProduct.Order_Model__c);
        }
        
        system.debug('返回List信息--->'+lstProductModel);
        return lstProductModel;
        
    }
    @AuraEnabled
    public static String QueryAddressInfo(String CustomerName,Boolean IsProtal){
        //Map<String,String> mapCode2Address = new Map<String,String>();
        List<Account_Address__c> lstAccountAddress  = new List<Account_Address__c>();
        try {
            //查询Address地址信息到Code 的映射--->Protal端
            if(IsProtal){
                lstAccountAddress = [
                    SELECT Id,Name,Country__c,City__c,Street_1__c,RecordTypeId,RecordType_Name__c,Customer_Line_Oracle_ID__c,Postal_Code__c,Customer__c,Customer__r.Name ,Customer__r.AccountNumber
                    FROM Account_Address__c  Where Customer__c = :CustomerName AND Customer_Line_Oracle_ID__c <> NULL AND Status__c = true
                ];
            }else{
                lstAccountAddress = [
                    SELECT Id,Name,Country__c,City__c,Street_1__c,RecordTypeId,RecordType_Name__c,Customer_Line_Oracle_ID__c,Postal_Code__c,Customer__c,Customer__r.Name ,Customer__r.AccountNumber
                    FROM Account_Address__c  Where Customer__c = :CustomerName AND Customer_Line_Oracle_ID__c <> NULL AND Status__c = true
                ];
            }
            
            //遍历Address做拼接
             for(Account_Address__c objAccountAddress : lstAccountAddress){
                 objAccountAddress.Customer__r.AccountNumber =  
                     (objAccountAddress.Customer__r.AccountNumber == null ? '':objAccountAddress.Customer__r.AccountNumber) ;
                 objAccountAddress.Postal_Code__c = 
                     (objAccountAddress.Postal_Code__c == null ? '':objAccountAddress.Postal_Code__c );
                 objAccountAddress.Country__c = 
                     (objAccountAddress.Country__c == null ? '':objAccountAddress.Country__c );
                 objAccountAddress.City__c = 
                     (objAccountAddress.City__c == null ? '':objAccountAddress.City__c) ;
                 objAccountAddress.Street_1__c = 
                     (objAccountAddress.Street_1__c == null ? '':objAccountAddress.Street_1__c) ;
                 objAccountAddress.Customer_Line_Oracle_ID__c = 
                     (objAccountAddress.Customer_Line_Oracle_ID__c == null ? '':objAccountAddress.Customer_Line_Oracle_ID__c) ;
                 
              }
              system.debug('返回的Address信息--->'+lstAccountAddress);

              return JSON.serialize(lstAccountAddress);
            
        } catch (Exception e) {
            system.debug('报错行数--->'+e.getLineNumber()+'报错信息1--->'+e.getMessage());
            throw new AuraHandledException(e.getMessage());
            
        }
       
    }
    
    @AuraEnabled
    public static String createPurchaseOrder(String strpurchaseOrderItems, String pricingDate) {
        system.debug('strpurchaseOrderItems--->'+strpurchaseOrderItems);
        try{
            List<PurchaseOrderItemWrapper> purchaseOrderItems = (List<PurchaseOrderItemWrapper>)JSON.deserialize(strpurchaseOrderItems, List<PurchaseOrderItemWrapper>.class);
            //对数据trim
            Set<String> productModels = new Set<String>();
            for(PurchaseOrderItemWrapper objItem : purchaseOrderItems){
                objItem.billToAddressCode = objItem.billToAddressCode.trim();
                objItem.shipToAddressCode = objItem.shipToAddressCode.trim();
                objItem.dropshipAddressCode = objItem.dropshipAddressCode == null ? '': objItem.dropshipAddressCode.trim();
                objItem.orderGroupNumber = objItem.orderGroupNumber.trim();
                objItem.customerPO = objItem.customerPO.trim();
                objItem.insuranceFee = objItem.insuranceFee == null ? '0': objItem.insuranceFee.trim();
                objItem.otherFee = objItem.otherFee == null ? '0' :  objItem.otherFee.trim();
                objItem.orderType = objItem.orderType == null ? '' : objItem.orderType.trim();
                objItem.warehouse = objItem.warehouse == null ? '' :  objItem.warehouse.trim();
                objItem.isDropship = objItem.isDropship == null ? '': objItem.isDropship.trim();
                objItem.modelNumber = objItem.modelNumber == null ? '': objItem.modelNumber.trim();
                objItem.qty = objItem.qty == null ? '': objItem.qty.trim();
                objItem.requestDate = objItem.requestDate == null ? '': objItem.requestDate.trim();
                objItem.ScheduleShipDate = String.isBlank(objItem.ScheduleShipDate) ? '' : objItem.ScheduleShipDate.trim();
                objItem.remark = objItem.remark == null ? '': objItem.remark.trim();
                if(String.isNotBlank(objItem.modelNumber)) {
                    productModels.add(objItem.modelNumber);
                }
            }
            Set<String> inactiveProducts= new Set<String>();
            Map<String, Product2> productModelMap = getProductModelIds(productModels, inactiveProducts);
        
            Map<String, List<PurchaseOrderItemWrapper>> orderGroupedItems = new Map<String, List<PurchaseOrderItemWrapper>>();
            Set<String> AllcustomerNumbers = new Set<String>();
            Set<String> AllbillToAddressCodes = new Set<String>();
            Set<String> AllshipToAddressCodes = new Set<String>();
            Set<String> AlldropshipAddressCodes = new Set<String>();
            Set<String> AllAddresCode = new Set<String>();
            Map<String,String> mapAddressCode2Id = new Map<String,String>();
            //校验重复是根据不同的GroupNUmber有不同的Codes等
            Map<String,Set<String>> mapNumber2BillCode = new Map<String,Set<String>>();
            Map<String,Set<String>> mapNumber2ShipCode = new Map<String,Set<String>>();
            Map<String,Set<String>> mapNumber2DropCode = new Map<String,Set<String>>();
            Map<String,Set<String>> mapNumber2CustomerCode = new Map<String,Set<String>>();
        
            Set<String> lstCustomerIds = new Set<String>();
            // Group purchase order items by order group number
            system.debug('purchaseOrderItems--->'+purchaseOrderItems);
            for (PurchaseOrderItemWrapper item : purchaseOrderItems) {
                if (!orderGroupedItems.containsKey(item.orderGroupNumber)) {
                    orderGroupedItems.put(item.orderGroupNumber.trim(), new List<PurchaseOrderItemWrapper>());
                }
                lstCustomerIds.add(item.customerNumber);
                Set<String> billToAddressCodes = mapNumber2BillCode.containsKey(item.orderGroupNumber)? mapNumber2BillCode.get(item.orderGroupNumber) : new Set<String>();
                Set<String> shipToAddressCodes = mapNumber2ShipCode.containsKey(item.orderGroupNumber)? mapNumber2ShipCode.get(item.orderGroupNumber) : new Set<String>();
                Set<String> dropshipAddressCodes = mapNumber2DropCode.containsKey(item.orderGroupNumber)? mapNumber2DropCode.get(item.orderGroupNumber) : new Set<String>();
                Set<String> customerNumbers = mapNumber2CustomerCode.containsKey(item.orderGroupNumber)? mapNumber2CustomerCode.get(item.orderGroupNumber) : new Set<String>();
            
                billToAddressCodes.add(item.billToAddressCode.trim());
                shipToAddressCodes.add(item.shipToAddressCode.trim() );
                dropshipAddressCodes.add(item.dropshipAddressCode == null ? '': item.dropshipAddressCode.trim());
                mapNumber2BillCode.put(item.orderGroupNumber,billToAddressCodes);
                mapNumber2ShipCode.put(item.orderGroupNumber,shipToAddressCodes);
                mapNumber2DropCode.put(item.orderGroupNumber,dropshipAddressCodes);
                orderGroupedItems.get(item.orderGroupNumber).add(item);
                AllAddresCode.add(item.shipToAddressCode);
                AllAddresCode.add(item.billToAddressCode);
                system.debug(item.dropshipAddressCode);
                AllAddresCode.add(item.dropshipAddressCode);
                // Collect unique customer numbers, bill to address codes, ship to address codes and dropship address codes
                customerNumbers.add(item.customerNumber);                
                AllbillToAddressCodes.add(item.billToAddressCode);
                AllshipToAddressCodes.add(item.shipToAddressCode);
                AlldropshipAddressCodes.add(item.dropshipAddressCode);
                AllcustomerNumbers.add(item.customerNumber);                
            }
            
            Map<String,String> mapNumber2Ship = new Map<String,String>();
            Map<String,String> mapNumber2Bill = new Map<String,String>();
            Map<String,String> mapNumber2Drop = new Map<String,String>();
            Map<String,String> mapNumber2Customer = new Map<String,String>();
            system.debug('mapNumber2ShipCode--->'+mapNumber2ShipCode);
            for(String StrNumber : mapNumber2ShipCode.keySet()){
                if( mapNumber2ShipCode.get(StrNumber).size()>1){
                    return System.Label.CCM_ConflictShiptoCode;
                }
                for(String StrCode :  mapNumber2ShipCode.get(StrNumber)){
                    mapNumber2Ship.put(StrNumber,StrCode);
                }
            }
            system.debug('mapNumber2DropCode--->'+mapNumber2DropCode);
            for(String StrNumber : mapNumber2DropCode.keySet()){
                if(mapNumber2DropCode.get(StrNumber).size()>1){
                    return System.Label.CCM_ConflictDropShipCode;
            
                }
                for(String StrCode :  mapNumber2DropCode.get(StrNumber)){
                    mapNumber2Drop.put(StrNumber,StrCode);
                }
            }
            system.debug('mapNumber2BillCode--->'+mapNumber2BillCode);
            for(String StrNumber : mapNumber2BillCode.keySet()){
                if(mapNumber2BillCode.get(StrNumber).size()>1){
                    return System.Label.CCM_ConflictBilltoCode;
                    
                }
                for(String StrCode :  mapNumber2BillCode.get(StrNumber)){
                    mapNumber2Bill.put(StrNumber,StrCode);
                }
            }
            system.debug('mapNumber2CustomerCode---->'+mapNumber2CustomerCode);
            for(String StrNumber : mapNumber2CustomerCode.keySet()){
                if(mapNumber2CustomerCode.get(StrNumber).size()>1){
                    return System.Label.CCM_ConflictCustomerNumber;
                
                }
                system.debug('mapNumber2CustomerCode-->0+'+mapNumber2CustomerCode);
            
                for(String StrCode :  mapNumber2CustomerCode.get(StrNumber)){
                    mapNumber2Customer.put(StrNumber,StrCode);
                }
            }
        
            
            //通过customerId查询CustomerOwner信息
            List<Account> lstAccount = [
                SELECT Id,OwnerId,CurrencyIsoCode FROM Account WHERE Id = :lstCustomerIds
            ];
            Map<String,String> mapId2Code = new Map<String,String>();
            Map<String,String> mapAccountId2Owner = new Map<String,String>();
            for(Account objAccount : lstAccount){
                mapAccountId2Owner.put(objAccount.Id, objAccount.OwnerId);
                mapId2Code.put(objAccount.Id, objAccount.CurrencyIsoCode);
            }
            //
            system.debug('AllAddresCode--->'+AllAddresCode);
            List<Account_Address__c> lstaddresses = [
                                SELECT Id,Customer_Line_Oracle_ID__c,City__c
                                ,RecordType.DeveloperName FROM Account_Address__c 
                                WHERE Customer_Line_Oracle_ID__c IN :AllAddresCode 
                                AND Customer__c IN :AllcustomerNumbers
                                AND Status__c = true
                            ];

            if(lstaddresses == null || lstaddresses.size() == 0){
                return System.Label.CCM_AddressNotExistError;
    
            }
            //遍历AccountAddress
            //创建AddressId到City的映射
            Map<String,String> mapAddressId2City = new Map<String,String>();
            Map<String,String> mapBillAddressCode2Id = new Map<String,String>();
            Map<String,String> mapShipAddressCode2Id = new Map<String,String>();
            Map<String,String> mapDropShipAddressCode2Id = new Map<String,String>();
            for(Account_Address__c objAccountAddress : lstaddresses){
                system.debug('objAccountAddress.Customer_Line_Oracle_ID__c--->'+objAccountAddress.Customer_Line_Oracle_ID__c);
                if(objAccountAddress.RecordType.DeveloperName == 'Billing_Address'){
                    mapBillAddressCode2Id.put(objAccountAddress.Customer_Line_Oracle_ID__c,objAccountAddress.Id);
                }else if(objAccountAddress.RecordType.DeveloperName == 'Shipping_Address'){
                    mapShipAddressCode2Id.put(objAccountAddress.Customer_Line_Oracle_ID__c,objAccountAddress.Id);
                }else if(objAccountAddress.RecordType.DeveloperName == 'Dropship_Shipping_Address'){
                    mapDropShipAddressCode2Id.put(objAccountAddress.Customer_Line_Oracle_ID__c,objAccountAddress.Id);
                }
                mapAddressCode2Id.put(objAccountAddress.Customer_Line_Oracle_ID__c,objAccountAddress.Id);
                mapAddressId2City.put(objAccountAddress.Id, objAccountAddress.City__c);
            }
            system.debug('mapAddressCode2Id-->'+mapAddressCode2Id);
            
            
            // Create purchase order records and related purchase order item records
            List<Purchase_Order__c> purchaseOrders = new List<Purchase_Order__c>();
            List<Purchase_Order_Item__c> purchaseOrderItemsToInsert = new List<Purchase_Order_Item__c>();
            Set<String> productNotExitSet = new Set<String>();
            Map<String, Set<String>> customerFeeTypesMap = CCM_PurchaseOrder_FeeCheckUtil.checkFeeInPriceBook(AllcustomerNumbers, pricingDate);

            Map<String, String> recordTypeMap = new Map<String, String>{
                CCM_Constants.REGULAR_ORDER => Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.REGULAR_ORDER).getRecordTypeId(),
                CCM_Constants.PRE_SEASON_ORDER_DEV_NAME => Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PRE_SEASON_ORDER_DEV_NAME).getRecordTypeId()
            };
            String userType = CCM_PurchaseOrderDetailController.GetUserType();
            Date priceDate = Date.today();
            Map<String,String> mapFeild2Value = CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
            for (String orderGroupNumber : orderGroupedItems.keySet()) {
                List<PurchaseOrderItemWrapper> items = orderGroupedItems.get(orderGroupNumber);
                if(items == null || items.size() == 0){
                    continue;
                }
                // Use the first item's insurance fee and other fee for the entire group
                Decimal insuranceFee = String.isBlank(items[0].insuranceFee) ? 0 : Decimal.valueOf( items[0].insuranceFee);
                Decimal otherFee =String.isBlank(items[0].otherFee) ? 0 : Decimal.valueOf( items[0].otherFee);
                //计算recordType
                String RecordTypeName = null;
                system.debug('items[0].orderType---->'+items[0].orderType);
                if(items[0].orderType == 'Regular'){
                    RecordTypeName = CCM_Constants.REGULAR_ORDER;
                }else if(items[0].orderType == 'Pre-season'){
                    RecordTypeName = CCM_Constants.PRE_SEASON_ORDER_DEV_NAME;
                }else{
                    RecordTypeName = CCM_Constants.REGULAR_ORDER;
                }
                Boolean isDropshipOrder =  false;
                if(items[0].isDropship == 'YES'){
                    isDropshipOrder = true;
                }
                String wareHouse = '';
                if(items[0].warehouse == 'Germany(DSV)'){
                    wareHouse = CCM_Constants.GERMANY;
                }else if(items[0].warehouse == 'China(DI)'){
                    wareHouse = CCM_Constants.CHINA;
                }
                
            
                // Create purchase order record
                //先获取一下用户类型
                // String userType = CCM_PurchaseOrderDetailController.GetUserType();
                // Date priceDate = Date.today();
                if(String.isNotBlank(pricingDate)) {
                    priceDate = Date.valueOf(pricingDate);
                }
                Purchase_Order__c purchaseOrder;
                if(userType == 'InsideSales'){

                    List<String> errMsgList = checkFeeInPriceBook(items[0].customerNumber, customerFeeTypesMap, insuranceFee, otherFee);
                    if(!errMsgList.isEmpty()) {
                        return String.join(errMsgList, '\n');
                    }

                    purchaseOrder = new Purchase_Order__c(
                        WearHouse_In_EBS__c = 'EEG',
                        ORG_ID__c = 'EEG',
                        CurrencyIsoCode = mapId2Code.get(items[0].customerNumber),
                        Customer__c = items[0].customerNumber,
                        Customer_PO_Num__c = items[0].customerPO,
                        Insurance_Fee__c = insuranceFee,
                        Other_Fees__c = otherFee,
                        Pricing_Date__c = priceDate,
                        
                        //Order_Type__c = items[0].orderType,
                        // RecordTypeId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(RecordTypeName).getRecordTypeId(),
                        RecordTypeId = recordTypeMap.get(RecordTypeName),
                        Warehouse__c = wareHouse,
                        Salesperson__c = mapAccountId2Owner.get(items[0].customerNumber),
                        Is_DropShip__c = isDropshipOrder
                    );
                }else if(userType == 'SalesRep'){
                    purchaseOrder = new Purchase_Order__c(
                        WearHouse_In_EBS__c = 'EEG',
                        ORG_ID__c = 'EEG',
                        CurrencyIsoCode = mapId2Code.get(items[0].customerNumber),
                        Customer__c = items[0].customerNumber,
                        Customer_PO_Num__c = items[0].customerPO,
                        Insurance_Fee__c = 0,
                        Other_Fees__c = 0,
                        Pricing_Date__c = priceDate,
                    
                        //Order_Type__c = items[0].orderType,
                        // RecordTypeId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(RecordTypeName).getRecordTypeId(),
                        RecordTypeId = recordTypeMap.get(RecordTypeName),
                        Warehouse__c = CCM_Constants.GERMANY,
                        Salesperson__c = mapAccountId2Owner.get(items[0].customerNumber),
                        Is_DropShip__c = isDropshipOrder
                    );
                }else{
                    //获取当前用户对应customer
                    // Map<String,String> mapFeild2Value = CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
                    purchaseOrder = new Purchase_Order__c(
                        WearHouse_In_EBS__c = 'EEG',
                        ORG_ID__c = 'EEG',
                        CurrencyIsoCode = mapId2Code.get(items[0].customerNumber),
                        Customer__c = mapFeild2Value.get('CustomerId'),
                        Customer_PO_Num__c = items[0].customerPO,
                        Insurance_Fee__c = 0,
                        Other_Fees__c = 0,
                        Pricing_Date__c = priceDate,
                        // RecordTypeId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(RecordTypeName).getRecordTypeId(),
                        RecordTypeId = recordTypeMap.get(RecordTypeName),
                        Warehouse__c =  CCM_Constants.GERMANY,
                        Salesperson__c = mapAccountId2Owner.get(mapFeild2Value.get('CustomerId')),
                        Is_DropShip__c = isDropshipOrder
                        
                    );
                }
                
                
                // Assign billing address, ship to address and dropship address IDs if they exist
                if (mapAddressCode2Id != null) {
                    if(!mapBillAddressCode2Id.containsKey(items[0].billToAddressCode)){
                        return 'The Bill address'+ items[0].billToAddressCode+' information does not exist. Please confirm whether it is the address of this customer';
                    }
                    purchaseOrder.Billing_Address__c = mapBillAddressCode2Id.get(items[0].billToAddressCode);
                
                    if(items[0].isDropship == 'YES'){
                        //DropShip必填
                        if(!mapDropShipAddressCode2Id.containsKey(items[0].dropshipAddressCode)){
                            return 'The DropShip address'+ items[0].dropshipAddressCode+' information does not exist. Please confirm whether it is the address of this customer';
                        }
                    }else{
                        //表示不是DropShip .ShipAddress必填
                        if(!mapShipAddressCode2Id.containsKey(items[0].shipToAddressCode)){
                            return 'The Shiping address'+ items[0].shipToAddressCode+' information does not exist. Please confirm whether it is the address of this customer';
                        }
                    }
                    purchaseOrder.Shipping_Address__c = mapShipAddressCode2Id.get(items[0].shipToAddressCode);
                    system.debug('mapAddressCode2Id------->'+mapAddressCode2Id);
                    system.debug('items[0].dropshipAddressCode-->'+items[0].dropshipAddressCode);
                    purchaseOrder.Dropship_Address__c = mapShipAddressCode2Id.get(items[0].dropshipAddressCode);
                }
                insert purchaseOrder;
                Decimal EstOrderVolum = 0;
                Decimal EstWeight = 0;
                Date exceptDate = null;
                // Create purchase order item records
                // Set<String> productModels = new Set<String>();
                // for (PurchaseOrderItemWrapper item : items) {
                //     if(String.isNotBlank(item.modelNumber)) {
                //         productModels.add(item.modelNumber);
                //     }
                // }
                // Map<String, Product2> productModelMap = getProductModelIds(productModels, inactiveProducts);
                for (PurchaseOrderItemWrapper item : items) {
                    Product2 objProduct = productModelMap.get(item.modelNumber);
                    if(objProduct == null){
                        // return 'Product does not exist';
                        productNotExitSet.add(item.modelNumber);
                        continue;
                    }
                    if(exceptDate == null || exceptDate > Date.valueOf(item.requestDate)){
                        // excepted去request最早的一个
                        exceptDate = Date.valueOf(item.requestDate);
                    }

                    Purchase_Order_Item__c purchaseOrderItem = new Purchase_Order_Item__c(
                        Purchase_Order__c = purchaseOrder.Id,
                        Request_Date__c =  item.requestDate == null ? null :Date.valueOf(item.requestDate),
                        Schedule_Ship_Date__c = String.isBlank(item.ScheduleShipDate) ? null : Date.valueOf(item.ScheduleShipDate),
                        Submit_Date__c = Date.today(),
                        Quantity__c =  String.isBlank(item.qty) ? 0 : Decimal.valueOf(item.qty ),
                        UOM__c = 'EA',
                        Discount__c = 0,
                        Product__c = objProduct.Id,
                        Remarks__c = item.remark
                    );
                    if(objProduct.Est_Order_Volum__c != null){
                        EstOrderVolum += objProduct.Est_Order_Volum__c;
                    }
                    if(objProduct.Est_Order_Weight__c != null){
                        EstWeight += objProduct.Est_Order_Weight__c;
                    }
                    purchaseOrderItemsToInsert.add(purchaseOrderItem);
                }
                purchaseOrder.Expected_Delivery_Date__c = exceptDate;
                purchaseOrder.Est_Weight__c = EstWeight;
                purchaseOrder.Est_OrderVolum__c = EstOrderVolum;
                purchaseOrders.add(purchaseOrder);
            }

            if(!productNotExitSet.isEmpty() || !inactiveProducts.isEmpty()) {
                String errorMsg = '';
                if(!productNotExitSet.isEmpty()) {
                    List<String> productNotExistList = new List<String>();
                    productNotExistList.addAll(productNotExitSet);
                    errorMsg += String.join(productNotExistList, ', ') + ' ' + System.Label.CCM_OrderBatchUpload_ProductNotExist;
                }
                if(!inactiveProducts.isEmpty()) {
                    List<String> inactiveProductList = new List<String>();
                    inactiveProductList.addAll(inactiveProducts);
                    if(String.isNotBlank(errorMsg)) {
                        errorMsg += ' ';
                    }
                    errorMsg += String.join(inactiveProductList, ', ') + ' ' + System.Label.CCM_OrderBatchUpload_ProductNotAvailable;
                }
                return errorMsg;
            }

            // Insert purchase order records and related purchase order item records
            for (Purchase_Order__c purchaseOrder : purchaseOrders) {
                for (Purchase_Order_Item__c purchaseOrderItem : purchaseOrderItemsToInsert) {
                    if (purchaseOrderItem.Purchase_Order__c == purchaseOrder.Id) {
                        purchaseOrderItem.Purchase_Order__c = purchaseOrder.Id;
                    }
                }
            }
            insert purchaseOrderItemsToInsert;
            
            String str = AutoFillInfo(purchaseOrders, purchaseOrderItemsToInsert, mapAddressId2City, pricingDate);
            return str; 
        }catch(Exception e){
            system.debug('报错信息2----->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }

    private static List<String> checkFeeInPriceBook(String customerId, Map<String, Set<String>> customerFeeTypeMap, Decimal insuranceFee, Decimal otherFee) {
        List<String> errorMsgList = new List<String>();
        if(customerFeeTypeMap.containsKey(customerId)) {
            Set<String> feeTypes = customerFeeTypeMap.get(customerId);
            if(insuranceFee != null && insuranceFee != 0) {
                String feeType = 'insurance';
                Boolean insuranceFeeInPriceBook = customerFeeTypeMap.get(customerId).contains(feeType);
                if(!insuranceFeeInPriceBook) {
                    errorMsgList.add(Label.CCM_Insurance_Fee_Not_In_PriceBook);
                }
            }
            if(otherFee != null && otherFee != 0) {
                String feeType = 'otherfee';
                Boolean otherFeeInPriceBook = customerFeeTypeMap.get(customerId).contains(feeType);
                if(!otherFeeInPriceBook) {
                    errorMsgList.add(Label.CCM_Other_Fee_Not_In_PriceBook);
                }
            }
        }
        return errorMsgList;
    }

    public static Boolean IsNullOrWhiteSpace(String str){
        if(str==null||str.isWhitespace()){
            return true;
        }else{
            return false;
        }
    }
    public static List<String> CheckPurchaseOrderTableData(String userType, List<PurchaseOrderItemWrapper> purchaseOrderItems){
        List<String> ErrorMessageList = new List<String>();
        String requiredField = System.Label.CCM_RequiredField;
        system.debug('requiredField:'+requiredField);
        if(!(userType == 'InsideSales'||userType == 'SalesRep'||userType =='Dealer')){
            system.debug('用户类型不是 InsideSales、SalesRep、Dealer');
            return ErrorMessageList;             
        }        

        Integer lineNum = 0;
        for(PurchaseOrderItemWrapper item : purchaseOrderItems){
            List<String> WhiteSpaceFeildList=new List<String>();
            lineNum++;
            String noNum = Label.CCM_Line + ' ' + lineNum + ': ';
            if (IsNullOrWhiteSpace(item.orderGroupNumber)) {
                //ErrorMessageList.add(noNum+' OrderGroupNumber '+requiredField);
                WhiteSpaceFeildList.add(Label.CCM_OrderGroupNumber);
            }
            if (IsNullOrWhiteSpace(item.customerPO)) {
                //ErrorMessageList.add(noNum+' CustomerPO '+requiredField);
                WhiteSpaceFeildList.add(Label.CCM_CustomerPO);
            }
            if (IsNullOrWhiteSpace(item.billToAddressCode)) {
                //ErrorMessageList.add(noNum+' BillToAddressCode '+requiredField);
                WhiteSpaceFeildList.add(Label.CCM_BillToAddressCode);
            }
            if (IsNullOrWhiteSpace(item.shipToAddressCode)) {
                //ErrorMessageList.add(noNum+' ShipToAddressCode '+requiredField);
                WhiteSpaceFeildList.add(Label.CCM_ShipToAddressCode);
            }
            if (IsNullOrWhiteSpace(item.modelNumber)) {
                //ErrorMessageList.add(noNum+' Model '+requiredField);
                WhiteSpaceFeildList.add(Label.CCM_Model);
            }
            if (IsNullOrWhiteSpace(item.qty)) {
                //ErrorMessageList.add(noNum+' Qty '+requiredField);
                WhiteSpaceFeildList.add(Label.CCM_Qty);
            }
            if (IsNullOrWhiteSpace(item.requestDate)) {
                //ErrorMessageList.add(noNum+' RequestDate '+requiredField);
                WhiteSpaceFeildList.add(Label.CCM_RequestDate);
            }

            if(userType == 'InsideSales'){
                if (IsNullOrWhiteSpace(item.orderType)) {
                    //ErrorMessageList.add(noNum+' OrderType '+requiredField);
                    WhiteSpaceFeildList.add(Label.CCM_OrderType);
                }
                if (IsNullOrWhiteSpace(item.isDropship)) {
                    //ErrorMessageList.add(noNum+' IsDropship '+requiredField);
                    WhiteSpaceFeildList.add(Label.Order_IsDropship);
                }
                if (IsNullOrWhiteSpace(item.warehouse)) {
                    //ErrorMessageList.add(noNum+' Warehouse '+requiredField);
                    WhiteSpaceFeildList.add(Label.CCM_Warehouse);
                }
                if (IsNullOrWhiteSpace(item.ScheduleShipDate)) {
                    WhiteSpaceFeildList.add(Label.CCM_ScheduleShipDate);
                }
            }else if(userType == 'SalesRep'){
                if (IsNullOrWhiteSpace(item.orderType)) {
                    //ErrorMessageList.add(noNum+' OrderType '+requiredField);
                    WhiteSpaceFeildList.add(Label.CCM_OrderType);
                }
                if (IsNullOrWhiteSpace(item.isDropship)) {
                    //ErrorMessageList.add(noNum+' IsDropship '+requiredField);
                    WhiteSpaceFeildList.add(Label.Order_IsDropship);
                }
                if (IsNullOrWhiteSpace(item.ScheduleShipDate)) {
                    WhiteSpaceFeildList.add(Label.CCM_ScheduleShipDate);
                }
            }
            if(WhiteSpaceFeildList.size()>0){
                ErrorMessageList.add(noNum+WhiteSpaceFeildList.toString()+' '+requiredField);
            }
         }
		 system.debug('ErrorMessageList'+JSON.serialize(ErrorMessageList));
         return ErrorMessageList;

    } 
     @AuraEnabled
    public static String CheckBatchPurchaseOrderData(String strpurchaseOrderItems) {
        system.debug('strpurchaseOrderItems--->'+strpurchaseOrderItems);
        List<PurchaseOrderItemWrapper> purchaseOrderItems = (List<PurchaseOrderItemWrapper>)JSON.deserialize(strpurchaseOrderItems, List<PurchaseOrderItemWrapper>.class);
             system.debug('purchaseOrderItems--->'+purchaseOrderItems);
             //先获取一下用户类型
            String userType = CCM_PurchaseOrderDetailController.GetUserType();
            system.debug('userType--->'+userType);
            //检查必填字段是否填写
             List<String> ErrorMessageList =  CheckPurchaseOrderTableData(userType, purchaseOrderItems);
            // if(ErrorMessageList.size()>0){
            //     return JSON.serialize(ErrorMessageList);
            // } 
        return JSON.serialize(ErrorMessageList);
       
    } 
    @AuraEnabled
    public static Map<String,String> GetCurrentUserCustomer(){
        return CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
    }
    
    
    public Static String AutoFillInfo(List<Purchase_Order__c> lstPurchaseOrder, List<Purchase_Order_Item__c> lstPurchaseOrderItem, Map<String,String> mapAddressId2City, String priceDate){
        //根据CustomerId查询Auth Brand信息
        String CustomerId = '';
        String WareHouse = '';
        //根据DropShipId查询DropShip详细信息
        Set<String> setDropShipId = new Set<String>();
        //获取ProductId信息
        List<String> setProductId = new List<String>();
        //获取Pricing Date
        Date PricingDate;
        if(String.isNotBlank(priceDate)) {
            PricingDate = Date.valueOf(priceDate);
        }
        else {
            PricingDate = Date.today();
        }
        //创建Product到Qty的map
        Map<String,Double> mapProductId2Qty = new Map<String,Double>();
        //创建Product到RecordType的映射
        Map<String,String> mapProductId2RecordName = new Map<String,String>();
        Map<String,String> mapOrder2WareHouse = new Map<String,String>();  
        //创建Order到PurchaseOrderItem的映射
        Map<String,List<Purchase_Order_Item__c>> mapOrderId2PurchaseOrderItems = new Map<String,List<Purchase_Order_Item__c>>();
        List<String> lstContainsToolProductId = new List<String>();
        for(Purchase_Order_Item__c objPurchaseOrderItem : lstPurchaseOrderItem){         
            lstContainsToolProductId.add(objPurchaseOrderItem.Product__c);
            setProductId.add(objPurchaseOrderItem.Product__c);
            mapProductId2Qty.put(objPurchaseOrderItem.Product__c, objPurchaseOrderItem.Quantity__c);
            mapOrder2WareHouse.put(objPurchaseOrderItem.Product__c, objPurchaseOrderItem.wearhouse__c);
            mapProductId2RecordName.put(objPurchaseOrderItem.Product__c, objPurchaseOrderItem.Header_Record_Name__c);
            //  setPricingDate.add(objPurchaseOrderItem.Pricing_Date__c);
            List<Purchase_Order_Item__c> listItemsByOrder = mapOrderId2PurchaseOrderItems.ContainsKey(objPurchaseOrderItem.Purchase_Order__c)?
                mapOrderId2PurchaseOrderItems.get(objPurchaseOrderItem.Purchase_Order__c) : new List<Purchase_Order_Item__c>();
            listItemsByOrder.add(objPurchaseOrderItem);
            mapOrderId2PurchaseOrderItems.put(objPurchaseOrderItem.Purchase_Order__c,listItemsByOrder);
        }
        system.debug('mapOrder2WareHouse--->'+mapOrder2WareHouse);
        system.debug('mapProductId2RecordName--->'+mapProductId2RecordName);
        //查询产品
        List<Product2> lstProduct = [
            SELECT Id,Order_MOdel__c FROM Product2 WHERE Id IN :setProductId
        ];
        Map<String,String> mapProductId2Model = new Map<String,String>();
        for(Product2 objPro : lstProduct){
            mapProductId2Model.put(objPro.Id, objPro.Order_MOdel__c);
        }
        
        Map<String,Decimal> mapConatinsKitProductId2Qty = new Map<String,Decimal>();
        List<Kit_Item__c> matchingKitItems = [SELECT VK_Product__c,VK_Product__r.Item_Description_DE__c,VK_Product__r.Order_Model__c,
                                                  VK_Product__r.Item_Description_EN__c ,Kit__c,Quantity__c   FROM Kit_Item__c  WHERE Kit__c IN :setProductId
                                                  AND VK_Product__c != NULL];
        //创建map下的kit
        Map<String,Set<String>> mapProductId2ToolIds = new Map<String,Set<String>>();
        for(Kit_Item__c objKit : matchingKitItems){
            lstContainsToolProductId.add(objKit.VK_Product__c);
            
            if( mapProductId2Qty.get(objKit.Kit__c) == null ){
                mapProductId2Qty.put(objKit.Kit__c, 0);
            }
            if(objKit.Quantity__c == null ){
                objKit.Quantity__c = 0;
            }
            mapConatinsKitProductId2Qty.put(objKit.VK_Product__c, mapProductId2Qty.get(objKit.Kit__c) * objKit.Quantity__c);
            mapConatinsKitProductId2Qty.put(objKit.Kit__c, mapProductId2Qty.get(objKit.Kit__c));
            Set<String> setToolIds = mapProductId2ToolIds.containsKey(objKit.Kit__c) ? 
                mapProductId2ToolIds.get(objKit.Kit__c) : new Set<String>();
            setToolIds.add(objKit.VK_Product__c);
            mapProductId2ToolIds.put(objKit.Kit__c, setToolIds);
        }
        
        
        system.debug('mapProductId2ToolIds---->'+mapProductId2ToolIds);
        
        Map<String,Set<String>> MapCustomerId2PurchaseId = new Map<String,Set<String>>();
        for(Purchase_Order__c objPurchase : lstPurchaseOrder){
            system.debug('lstPurchaseOrder--->'+objPurchase.Customer__c);
            CustomerId = objPurchase.Customer__c;
            WareHouse  =objPurchase.Warehouse__c;
            Set<String> setPurchaseIds = MapCustomerId2PurchaseId.containsKey(objPurchase.Customer__c)?
                MapCustomerId2PurchaseId.get(objPurchase.Customer__c) : new Set<String>();
            setPurchaseIds.add(objPurchase.Id);
            MapCustomerId2PurchaseId.put(objPurchase.Customer__c, setPurchaseIds);
            
            SetOrderType(objPurchase);
            setDropShipId.add(objPurchase.Dropship_Address__c);
            
        }
        //获取Auth 信息
        Sales_Program__c objSalesProgram = GetAuthBrands(CustomerId);
        if(setDropShipId != null && setDropShipId.size()>0){
            setDropShipInfo(lstPurchaseOrder,setDropShipId);
        }
        
        //调用价格计算
        Map<String,Map<String,Object>> mapProductId2PriceInfo = CaulateProduct2PriceInfo(CustomerId,setProductId,PricingDate,mapProductId2Qty);
        
        //查询QTy红绿灯信息
        Map<String,String> mapProduct2Invotory = mapProductInfo2Invotory(CustomerId,lstContainsToolProductId,PricingDate,mapOrder2WareHouse,mapConatinsKitProductId2Qty,mapProductId2RecordName);
        system.debug('mapProduct2Invotory--->'+mapProduct2Invotory);
        //根据AddressId信息获取ABBA信息
        List<String> lstPurchaseIds = new List<String>();
        Map<String, String> purchaseOrderRecordTypeMap = new Map<String, String>();
        for(Purchase_Order__c objPurchase : lstPurchaseOrder){
            objPurchase.Authorized_Brand__c = objSalesProgram.Id;
            objPurchase.Inco_Term__c = objSalesProgram.Incoterm__c;
            if(objPurchase.Inco_Term__c == 'FCA'|| objPurchase.Inco_Term__c == 'EXW'){
                objPurchase.Shipping_Place__c = 'Möckmühl';
            }else{

                //校验是否为DropSHip 
                system.debug('IsDropShipOrder---->'+objPurchase.Is_DropShip__c);
                if(objPurchase.Is_DropShip__c == true){
                    //如果是DropShip就要取Dropship的City
                    system.debug('进入DropSHIp');
                    objPurchase.Shipping_Place__c =  mapAddressId2City.get(objPurchase.Dropship_Address__c);
                }else if(objPurchase.Is_DropShip__c == false){
                    objPurchase.Shipping_Place__c = mapAddressId2City.get(objPurchase.Shipping_Address__c);
                }  
            }
            objPurchase.Freight_Term__c = objSalesProgram.Freight_Term__c;
            objPurchase.Payment_Term__c = objSalesProgram.Payment_Term__c;
            lstPurchaseIds.add(objPurchase.Id);
            purchaseOrderRecordTypeMap.put(objPurchase.Id, objPurchase.RecordTypeId);
        }

        Map<String,List<Decimal>> mapPurchaseId2Prices = new Map<String,List<Decimal>>();
        Set<String> productNotHavePriceSet = new Set<String>();
        String PreSeasonOrderId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PRE_SEASON_ORDER_DEV_NAME).getRecordTypeId();
        for(Purchase_Order_Item__c objPurchaseItem : lstPurchaseOrderItem){
            // Boolean isPreSeasonOrder = PreSeasonOrderId == purchaseOrderRecordTypeMap.get(objPurchaseItem.Purchase_Order__c) ? true : false;
            if(mapProduct2Invotory.containsKey(objPurchaseItem.Product__c)){
                objPurchaseItem.Inventory__c = mapProduct2Invotory.get(objPurchaseItem.Product__c);
                // Schedule Ship Date has filled in template
                Boolean isScheduleShipDateFilledIn = objPurchaseItem.Schedule_Ship_Date__c != null ? true : false;
                if(!isScheduleShipDateFilledIn) {
                    if(mapProductId2ToolIds.containsKey(objPurchaseItem.Product__c)){
                        //表示 是Kit类型。需要对Kit下所有的Tools遍历
                        //默认为绿色。有一个为黄色则为黄色
                        String InventoryInfo = 'Green';
                        for(String ToolId : mapProductId2ToolIds.get(objPurchaseItem.Product__c)){
                            if(mapProduct2Invotory.get(ToolId) != 'Green'){
                                //有一个不为绿色则不对
                                InventoryInfo = 'Yellow';
                                break;
                            }
                        }
                        if(InventoryInfo == 'Green'){
                            objPurchaseItem.Schedule_Ship_Date__c = objPurchaseItem.Request_Date__c;
                        }
                    }else{
                        //表示不是kit类型。直接取
                        if(mapProduct2Invotory.get(objPurchaseItem.Product__c) == 'Green'){
                            objPurchaseItem.Schedule_Ship_Date__c = objPurchaseItem.Request_Date__c;
                        }
                    }
                }              
            }

            // if(isPreSeasonOrder) {
            //     objPurchaseItem.Schedule_Ship_Date__c = objPurchaseItem.Request_Date__c;
            // }

            if(mapProductId2PriceInfo.containsKey(objPurchaseItem.Product__c)){
                Map<String,Object> mapFeild2Price = mapProductId2PriceInfo.get(objPurchaseItem.Product__c);
                system.debug('mapFeild2Price--->'+mapFeild2Price);
                objPurchaseItem.List_Price__c = ((Decimal)mapFeild2Price.get(CCM_Constants.LIST_PRICE)).setScale(5);
                objPurchaseItem.Sales_Price__c = ((Decimal)mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(5);
                objPurchaseItem.Unit_Price__c = ((Decimal)mapFeild2Price.get(CCM_Constants.FINAL_PRICE)).setScale(5);
                //add by  vince 240422 初始化promotion折扣后价格：无折扣，默认与sales price一致
                objPurchaseItem.unit_Net_Price2__c  = objPurchaseItem.Sales_Price__c;//最终价
                objPurchaseItem.Promotion_Discount_Type__c  = 'None';
                objPurchaseItem.Discount__c  = 0;//行折扣

                objPurchaseItem.Stand_Discount_Value__c = CCM_Constants.STAND_DISCOUNT == null ? 0 : ((Decimal)mapFeild2Price.get(CCM_Constants.STAND_DISCOUNT)).setScale(5);
                objPurchaseItem.Application_Method__c = ((String)mapFeild2Price.get(CCM_Constants.APPLICATION_METHOD));
                objPurchaseItem.List_Price_Name__c = ((String)mapFeild2Price.get('ListPriceName'));
               
                List<Decimal> lstPrices = mapPurchaseId2Prices.containsKey(objPurchaseItem.Purchase_Order__c)?
                    mapPurchaseId2Prices.get(objPurchaseItem.Purchase_Order__c) : new List<Decimal>();
                lstPrices.add(objPurchaseItem.Unit_Price__c);
                mapPurchaseId2Prices.put(objPurchaseItem.Purchase_Order__c ,lstPrices);             
            }else{
                // return 'The product '+mapProductId2Model.get(objPurchaseItem.Product__c)+ ' is not on the available list';
                productNotHavePriceSet.add(mapProductId2Model.get(objPurchaseItem.Product__c));
            }
        }

        if(!productNotHavePriceSet.isEmpty()) {
            List<String> productNotHavePriceList = new List<String>();
            productNotHavePriceList.addAll(productNotHavePriceSet);
            String errorMsg = 'The products ' + String.join(productNotHavePriceList, ', ') + ' are not on the available list';
            return errorMsg;
        }

        update lstPurchaseOrderItem;
        update lstPurchaseOrder;
        //查询Amount
        List<Purchase_Order__c> lstPurchase = [
            SELECT Id,Total_Amount__c FROM Purchase_Order__c WHERE Id IN :lstPurchaseIds
        ];
        Map<String,Decimal> mapPoId2Amount = new Map<String,Decimal>();
        List<String> lstSyncPoId = new List<String>();
        for(Purchase_Order__c objOrder : lstPurchase){
            mapPoId2Amount.put(objOrder.Id, objOrder.Total_Amount__c);
        }
        //对同一个PurchaseOrder计算总价格
        for(Purchase_Order__c objPurchase : lstPurchaseOrder){
            objPurchase.Authorized_Brand__c = objSalesProgram.Id;
            objPurchase.Inco_Term__c = objSalesProgram.Incoterm__c;

            objPurchase.Freight_Term__c = objSalesProgram.Freight_Term__c;
            objPurchase.Payment_Term__c = objSalesProgram.Payment_Term__c;
            List<Decimal> lstPrices = mapPurchaseId2Prices.get(objPurchase.Id);
            Map<String,Decimal> mapFeild2AllTotalPrice = CCM_RequestPurchaseOrderController.CaulateAllListPrice(lstPrices,objPurchase.Customer__c);
            
            // objPurchase.Total_Amount__c = mapFeild2AllTotalPrice.get('TotalValue');
           Boolean isGreen = true;
            for(Purchase_Order_Item__c objItem : mapOrderId2PurchaseOrderItems.get(objPurchase.Id)){
                if(objItem.Inventory__c != 'Green'){
                    isGreen = false;
                    break;
                }
            }
            Decimal TotalDueAmount = mapPoId2Amount.get(objPurchase.Id);
            Integer CountLine = mapOrderId2PurchaseOrderItems.get(objPurchase.Id).size();
            //判断状态--》Is Portal Or Inside Sales
            //inside Sales根据Role判断
           
            String UserType = CCM_PurchaseOrderDetailController.GetUserType();
            //判断UserRole的名字是否为Inside Sales--》如果是Inside Sales 直接Sync。如果不是。则判断条件
            if(UserType == 'InsideSales'){
                system.debug('我s insideSales');
                //表示是Inside Sales直接同步至Oracle
                //---TODO-->调用Oracle的Call Out
                // objPurchase.Status__c = CCM_Constants.PENDING_REVIEW;
                // objPurchase.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
                // objPurchase.Submit_Date__c = Date.today();
                // lstSyncPoId.add(objPurchase.Id);
                // CCM_PurchesOrderCallOut.pushOrderInfo(objPurchase.Id);
                objPurchase.Status__c = CCM_Constants.SUBMITTED;
                objPurchase.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
            }else{
                // system.debug('不是inside Sales');
                // //不是Inside Sales -->校验条件
                // if(TotalDueAmount < 45000 && CountLine <40){
                //     //直接同步
                //     objPurchase.Status__c = CCM_Constants.PENDING_REVIEW;
                //     objPurchase.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
                //     objPurchase.Submit_Date__c = Date.today();
                //     lstSyncPoId.add(objPurchase.Id);
                //     // CCM_PurchesOrderCallOut.pushOrderInfo(objPurchase.Id);

                // }else{
                //     //状态改为Submit需要InsideSales手动同步
                //     objPurchase.Status__c = CCM_Constants.SUBMITTED;
                //     objPurchase.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
                //     //调用通知
                //     CCM_PurchaseOrderPreview.SendNotifyNocation(objPurchase.Id);
                // }
                objPurchase.Status__c = CCM_Constants.SUBMITTED;
                objPurchase.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
                // CCM_PurchaseOrderPreview.SendNotifyNocation(objPurchase.Id);
            }
            objPurchase.Submit_Date__c = objPurchase.Submit_Date__c == null ? Date.today() :objPurchase.Submit_Date__c ;
        }

        // CCM_PurchaseOrderCallOutBatch clsCallOutBatch = new CCM_PurchaseOrderCallOutBatch(lstSyncPoId);
        // Database.executeBatch(clsCallOutBatch,1);
       
        update lstPurchaseOrder;
        return null;
    }
    public static Void setDropShipInfo(List<Purchase_Order__c> lstPurchaseOrder,Set<String> DropShipIds ){
        // 批量查询Address信息
        Map<Id, Account_Address__c> addresses = new Map<Id, Account_Address__c>();
        for (Account_Address__c addr : [SELECT Id, X2nd_Tier_Dealer__c, Customer__c, Country__c, Province__c, Name, Postal_Code__c, Address2__c, Address1__c, City__c, DropShip_Phone__c
                                        FROM Account_Address__c WHERE Id IN :DropShipIds AND Status__c = true]) {
                                            addresses.put(addr.Id, addr);
                                        }
        // 更新Purchase Order
        
        for (Purchase_Order__c po : lstPurchaseOrder) {
            if(String.isNotBlank(po.Dropship_Address__c)){
                 Account_Address__c addr = addresses.get(po.Dropship_Address__c);
                if (addr != null) {
                po.Additional_Contact_Name__c = addr.Name;
                po.Additional_Shipping_Street__c = addr.Address1__c;
                po.Additional_Shipping_Street2__c = addr.Address2__c;
                po.Additional_Contact_Phone__c = addr.DropShip_Phone__c;
                po.Additional_Shipping_Country__c = addr.Country__c;
                po.Additional_Shipping_Postal_Code__c = addr.Postal_Code__c;
                po.Additional_Shipping_Province__c = addr.Province__c;
                po.Additional_Shipping_City__c = addr.City__c;
               
            }
            }
            
        }
    }
    public Static Void SetOrderType(Purchase_Order__c objPurchase){
        //根据Order的recordType判断OrderType
        String RegularOrderId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.REGULAR_ORDER).getRecordTypeId();
        String PreSeasonOrderId = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.PRE_SEASON_ORDER_DEV_NAME).getRecordTypeId();
        if(objPurchase.RecordTypeId == RegularOrderId){
            //regular类型
            //WareHouse为Germany (DSV)
            if(CCM_Constants.GERMANY.equals(objPurchase.Warehouse__c)){
                objPurchase.Order_Type__c = CCM_Constants.DSVR;
            }
            if(CCM_Constants.CHINA.equals(objPurchase.Warehouse__c)){
                objPurchase.Order_Type__c = CCM_Constants.DIR;
            }
        }
        if(PreSeasonOrderId == objPurchase.RecordTypeId){
            //Pre_season_Order
            //WareHouse为Germany (DSV)
            if(CCM_Constants.GERMANY.equals(objPurchase.Warehouse__c)){
                objPurchase.Order_Type__c = CCM_Constants.DSVP;
            }
            if(CCM_Constants.CHINA.equals(objPurchase.Warehouse__c)){
                objPurchase.Order_Type__c = CCM_Constants.DIP;
            }
        }
    }
    //根据CustomerId批量查询Auth信息
    public static Sales_Program__c GetAuthBrands(String customerId){
        Sales_Program__c objSalesProgram = new Sales_Program__c ();
        //根据CustomerId查询SalesChannel
        system.debug('customerId--->'+customerId);
        
            objSalesProgram = [
                SELECT Id,Brands__c,Freight_Term__c,Incoterm__c,Payment_Term__c,Customer__c,Customer__r.Sales_Channel__c
                FROM Sales_Program__c WHERE  Customer__c = :CustomerId AND Status__c = 'Active' LIMIT 1
            ];
            
        
        
        return  objSalesProgram;
    }
    //根据CustomerId、ProductId、PricingDate-->批量查询红绿灯信息
    public static Map<String,String> mapProductInfo2Invotory (String CustomerId,List<String> lstProductId, Date PricingDate,Map<String,String> mapOrder2WareHouse ,Map<String,Decimal> mapProduct2Qty,Map<String,String> mapProduct2RecordName){
        Map<String,String> mapProductId2Invotory = new Map<String,String>();
        try{
            system.debug('lstProductId--->'+lstProductId);
            
        //批量查询Invotory总库
        List<Inventory__c> lstInventory = [
            SELECT Id,Product__c,Available_QTY__c FROM Inventory__c WHERE Product__c IN :lstProductId 
                AND RecordType.Name = 'All Inventory' AND Sub_Inventory_Code__c = 'EGD01'
        ];
        system.debug('lstInventory-->'+lstInventory);
        Map<String,Decimal> mapProduct2Invotory = new Map<String,Decimal>();
        for(Inventory__c objInvotory : lstInventory){
            mapProduct2Invotory.put(objInvotory.Product__c,objInvotory.Available_QTY__c);
        }
        system.debug('mapProduct2Invotory-->'+mapProduct2Invotory);
        //通过CustomerID查询字库名字
        system.debug('CustomerId--->'+CustomerId);
        Account objAccount = [
            SELECT Id,Planning_Territory_No__c,Sales_Territory__r.Name FROM Account WHERE Id = :CustomerId
        ];
        //获取年月
        Integer Year = PricingDate.year();
        Integer Month = PricingDate.month();
        //根据仓库名字、产品、时间唯一确认一个planning Tool
        List<Planning_Tool__c> lstPlanningTool = [
            SELECT Id,Actual_QTY__c,Product__c,Current_Month__c,Supply_Year__c
            FROM Planning_Tool__c WHERE Product__c IN :lstProductId AND Current_Month__c = :Month AND Supply_Year__c = :Year AND Territory_NO__c = :objAccount.Planning_Territory_No__c
        ];
        system.debug('lstPlanningTool-->'+lstPlanningTool);
        Map<String,Decimal> mapProductId2Tools = new Map<String,Decimal>();
        for(Planning_Tool__c objPlanningTool : lstPlanningTool){
            mapProductId2Tools.put(objPlanningTool.Product__c,objPlanningTool.Actual_QTY__c);
        }
        system.debug('mapProductId2Tools-->'+mapProductId2Tools);
        //对于同一个产品
        for(String ProductId : lstProductId){
            system.debug('ProductId--->'+ProductId);
            String LightInfo = '';
            String WareHouse = mapOrder2WareHouse.get(ProductId);
            Decimal PlanningToolQty = mapProductId2Tools.get(ProductId) == null ? 0 : mapProductId2Tools.get(ProductId);
            Decimal AllProductQty = mapProduct2Invotory.get(ProductId)== null ? 0 : mapProduct2Invotory.get(ProductId);
            String RecordName = mapProduct2RecordName.get(ProductId);
            Decimal Qty = mapProduct2Qty.get(ProductId)== null ? 0 : mapProduct2Qty.get(ProductId);
            system.debug('WareHouse--->'+WareHouse);
            system.debug('RecordName--->'+RecordName);
            if(CCM_Constants.CHINA.equals(WareHouse) || CCM_Constants.PRE_SEASON_ORDER.equals(RecordName)){
                //中国仓库或者预订单都为黄灯
                system.debug('中国仓库');
                LightInfo =  'Yellow';
            }else{
                system.debug('PlanningToolQty-->'+PlanningToolQty);
                system.debug('AllProductQty-->'+AllProductQty);
                system.debug('Qty-->'+Qty);
                if(PlanningToolQty-Qty>0 && AllProductQty-Qty>0){
                    LightInfo =  'Green';
                }else if(PlanningToolQty-Qty <= 0 && AllProductQty -Qty <= 0){
                    system.debug('返回红灯');
                    LightInfo = 'Red';
                }else{
                    system.debug('返回黄灯');
                    LightInfo = 'Yellow';
                }
            }
            mapProductId2Invotory.put(ProductId,LightInfo);
            
        }
        
        system.debug('mapProductId2Invotory-->'+mapProductId2Invotory);
        system.debug('mapProductId2Invotory-->'+mapProductId2Invotory);
        return mapProductId2Invotory;

        }catch (Exception e){
            system.debug('报错信息3---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
        
        
    }
    //根据CustomerId、ProductId、PricingDate-->计算价格信息
    public static Map<String,Map<String,Object>> CaulateProduct2PriceInfo(String CustomerId,List<String> lstProductId, Date PricingDate, Map<String,Double> mapProductId2Qty){
        Map<String,Map<String,Object>> mapProductId2PriceInfo = new Map<String,Map<String,Object>>();
        try{
            Account objAccount = [
                SELECT OwnerId,Id,CurrencyIsoCode FROM Account WHERE Id = :CustomerId
            ];
            //根据CustomerId,ProductId,PricingDate查询价格信息
            //直接去中间表查询-->中间表没有再看是否包含AllItem包含则找中间子表下面的Entry计算
           
            List<MasterProductPrice__c> lstMasterProduct = [
                SELECT Id, Account__c, Product__c, Start_Date__c, End_Date__c, Final_Price__c,
                Modifier_Entry__r.Value__c, Modifier_Entry__r.Application_Method__c,
                List_Price__c, Has_AllItem__c, Modifier_Entry__c, Product__r.Order_Model__c FROM MasterProductPrice__c
                WHERE Account__c = :customerId  AND Start_Date__c <= :PricingDate AND End_Date__c >= :PricingDate 
                AND Product__c IN :lstProductId AND Modifier_Entry__C != NULL
                AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
                AND  Is_Delete__c != true ORDER By min_Final_Prority__c ASC
            ];

            Set<String> modifierEntrys = new Set<String>();
            for(MasterProductPrice__c masterProduct : lstMasterProduct) {
                if(String.isNotBlank(masterProduct.Modifier_Entry__c)) {
                    modifierEntrys.add(masterProduct.Modifier_Entry__c);
                }
            }

            Set<String> excludeDiscountList = CCM_ExculdeDiscountUtil.getproductExcludeDiscountList(modifierEntrys, lstProductId);
   
            List<String> lstCaulatedProduct = new List<String>();
            if (lstMasterProduct.size() == 0) {
                return mapProductId2PriceInfo;
            }
            List<Sales_Program__c> lstAuthInfo = [
                    SELECT Id,Customer__c,Modifier_1__c,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,
                    Modifier_2__c FROM Sales_Program__c WHERE Customer__c = :CustomerId
            ];
            Set<String> setPriceBookIds = new Set<String>();
            String PriceBook1st = '';
            for(Sales_Program__c objAuth : lstAuthInfo){
                if(String.isNotBlank(objAuth.List_Price_1__c)){
                    setPriceBookIds.add(objAuth.List_Price_1__c);
                }
                if(String.isNotBlank(objAuth.List_Price_2__c)){
                    setPriceBookIds.add(objAuth.List_Price_2__c);
                }
                if(String.isNotBlank(objAuth.List_Price_3__c)){
                    setPriceBookIds.add(objAuth.List_Price_3__c);
                }
                if(String.isNotBlank(objAuth.Price_Book__c)){
                    setPriceBookIds.add(objAuth.Price_Book__c);
                    PriceBook1st = objAuth.Price_Book__c;
                }
            }
         
            List<Pricebook_Entry__c>  lstPriceBookEntryPrice = [
                SELECT Id, PriceBook__c, PriceBook__r.Name, UnitPrice__c, End_Date__c, Start_Date__c, Product__c, Product__r.Order_Model__c from Pricebook_Entry__c WHERE PriceBook__c IN :setPriceBookIds
                and Product__c IN :lstProductId AND End_Date__c >= :PricingDate AND Start_Date__c <= :PricingDate
                AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
            ];
            Map<String, Decimal> mapProductId2ListPrice = new Map<String,Decimal>();
            Map<String, String> mapProductId2PriceName = new Map<String,String>();
            Map<String, String> productIdModelMap = new Map<String, String>();
            //先计算一级价格册的原价
            for(Pricebook_Entry__c objPriceBookEntry : lstPriceBookEntryPrice){
                if(String.isNotBlank(priceBook1st) && priceBook1st == objPriceBookEntry.PriceBook__c){
                    mapProductId2ListPrice.put(objPriceBookEntry.Product__c,objPriceBookEntry.UnitPrice__c);
                    mapProductId2PriceName.put(objPriceBookEntry.Product__c, objPriceBookEntry.PriceBook__r.Name);
                    productIdModelMap.put(objPriceBookEntry.Product__c, objPriceBookEntry.Product__r.Order_Model__c);
                }
            }
            //一级不存在再存放二级
            for(Pricebook_Entry__c objPriceBookEntry : lstPriceBookEntryPrice){
                if(!mapProductId2ListPrice.containsKey(objPriceBookEntry.Product__c)) {
                    mapProductId2ListPrice.put(objPriceBookEntry.Product__c, objPriceBookEntry.UnitPrice__c);
                    mapProductId2PriceName.put(objPriceBookEntry.Product__c, objPriceBookEntry.PriceBook__r.Name);
                    productIdModelMap.put(objPriceBookEntry.Product__c, objPriceBookEntry.Product__r.Order_Model__c);
                }
            }
            //如果能够查到数据则直接返回价格
            for(MasterProductPrice__c objMaterialPrice : lstMasterProduct){
                if(objMaterialPrice != null && objMaterialPrice.Has_AllItem__c != true){
                    // add product existing check to fix price incorrect issue(get the largest min priority)
                    if(!mapProductId2PriceInfo.containsKey(objMaterialPrice.Product__c)) {
                        String key = objMaterialPrice.Modifier_Entry__c + ' - ' + objMaterialPrice.Product__r.Order_Model__c;
                        Boolean needExcludeDiscount = CCM_ExculdeDiscountUtil.needExcludeDiscount(key, excludeDiscountList);
                        Map<String,Object> mapFeild2Price = new Map<String,Object>();
                        lstCaulatedProduct.add(objMaterialPrice.Product__c);
                        mapFeild2Price.put(CCM_Constants.FINAL_PRICE, objMaterialPrice.Final_Price__c);
                        mapFeild2Price.put(CCM_Constants.LIST_PRICE, objMaterialPrice.List_Price__c);
                        mapFeild2Price.put(CCM_Constants.START_DATE, objMaterialPrice.Start_Date__c);
                        mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, objMaterialPrice.Modifier_Entry__r.Value__c);
                        if(needExcludeDiscount) {
                            mapFeild2Price.put(CCM_Constants.FINAL_PRICE, objMaterialPrice.List_Price__c);
                            mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, 0);
                        }
                        mapFeild2Price.put(CCM_Constants.APPLICATION_METHOD, objMaterialPrice.Modifier_Entry__r.Application_Method__c);
                        mapFeild2Price.put(CCM_Constants.END_DATE, objMaterialPrice.End_Date__c);
                        //售出价格等于FinaPrice*数量
                        Decimal SalesPrice = (objMaterialPrice.Final_Price__c == null ? 0 : objMaterialPrice.Final_Price__c) * 
                            (mapProductId2Qty.get(objMaterialPrice.Product__c) == null ? 0 : mapProductId2Qty.get(objMaterialPrice.Product__c));
                        mapFeild2Price.put(CCM_Constants.SALES_PRICE, SalesPrice);
                        mapProductId2PriceInfo.put(objMaterialPrice.Product__c, mapFeild2Price);
                        mapFeild2Price.put('ListPriceName', mapProductId2PriceName.get(objMaterialPrice.Product__c));
                    }
                }
            }
            
            //去掉已经计算过的Id以后还存在的Id则为AllItem的数据
            for(String element : lstCaulatedProduct){
                if(lstProductId.IndexOf(element) >=0 ){
                    lstProductId.remove(lstProductId.IndexOf(element)); 
                }
                mapProductId2ListPrice.remove(element);
            }
            
            if(lstProductId == null && lstProductId.size() == 0 ){
                return mapProductId2PriceInfo;
            }

            if(lstProductId != null && lstProductId.size()>0){
                //查询这个Customer下没有Product的并且Has_AllItem__c = true的
                List<MasterProductPrice__c> lstMasterProductAllItem = [
                    SELECT Id, Account__c, Product__c, Start_Date__c, End_Date__c, Final_Price__c, Has_AllItem__c, Min_Final_Prority__c,
                    Modifier_Entry__c 
                    FROM MasterProductPrice__c
                    WHERE Account__c = :customerId AND Start_Date__c <= :PricingDate 
                    AND Modifier_Entry__C != NULL
                    AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
                    AND End_Date__c >= :PricingDate AND Has_AllItem__c = TRUE ORDER By min_Final_Prority__c ASC
                ];
              
                //获取到master数据。根据MasterId查询-->满足条件的只有一条。直接取第一条的Id查询
                if(lstMasterProductAllItem == null || lstMasterProductAllItem.size()==0){
                    return mapProductId2PriceInfo;
                }
                //得到minFinal最小的值。去子表中查询
                List<LinkProductAndPrice__c> lstLinkProduct = [
                    SELECT value__c, Final_Priority__c , Start_Date__c, End_Date__c,
                    Modifier_Entry__r.Value__c, Modifier_Entry__r.Application_Method__c,
                    Application_Method__c
                    FROM LinkProductAndPrice__c WHERE MasterProductPrice__c = :lstMasterProductAllItem[0].Id
                    AND Final_Priority__c = :lstMasterProductAllItem[0].Min_Final_Prority__c
                    AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
                    AND  Is_Delete__c != TRUE
                ];
                //遍历所有的product 计算价格
                for(String productId : mapProductId2ListPrice.keySet()){
                    Decimal ListPrice = mapProductId2ListPrice.get(productId);
                    String key = lstMasterProductAllItem[0].Modifier_Entry__c + ' - ' + productIdModelMap.get(productId);
                    Boolean needExcludeDiscount = CCM_ExculdeDiscountUtil.needExcludeDiscount(key, excludeDiscountList);
                    //同一个产品下同一优先级多个时
                    List<Decimal> lstFinalPrice = new List<Decimal>();
                    for(LinkProductAndPrice__c objLink : lstLinkProduct){
                        Decimal FinalPrice = 0;
                        if(objLink.Application_Method__c == 'Percent'){
                            //表示是百分比
                            Decimal FinalDiscountPrice = ListPrice*objLink.value__c/100;
                            FinalDiscountPrice = FinalDiscountPrice.setScale(2,RoundingMode.HALF_UP);
                            FinalPrice = ListPrice - FinalDiscountPrice ;
                            FinalPrice = FinalPrice.setScale(2,RoundingMode.HALF_UP);
                            if(needExcludeDiscount) {
                                FinalPrice = ListPrice;
                            }
                        }else if(objLink.Application_Method__c == 'New Price'){
                            FinalPrice = objLink.value__c;
                        }
                        lstFinalPrice.add(FinalPrice);
                    }
                    lstFinalPrice.sort();
                    Map<String,Object> mapFeild2Price = new Map<String,Object>();
                    mapFeild2Price.put(CCM_Constants.FINAL_PRICE, lstFinalPrice[0]);
                    mapFeild2Price.put(CCM_Constants.LIST_PRICE, ListPrice);
                    mapFeild2Price.put(CCM_Constants.START_DATE, lstLinkProduct[0].Start_Date__c);
                    mapFeild2Price.put(CCM_Constants.END_DATE, lstLinkProduct[0].End_Date__c);

                    mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, lstLinkProduct[0].Modifier_Entry__r.Value__c);
                    if(needExcludeDiscount) {
                        mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, 0);
                    }
                    mapFeild2Price.put(CCM_Constants.APPLICATION_METHOD, lstLinkProduct[0].Modifier_Entry__r.Application_Method__c);
                    mapFeild2Price.put('ListPriceName', mapProductId2PriceName.get(productId));
                    //售出价格等于FinaPrice*数量
                    Decimal SalesPrice = (lstFinalPrice[0]) * 
                        (mapProductId2Qty.get(productId) == null ? 0 : mapProductId2Qty.get(productId));
                    mapFeild2Price.put(CCM_Constants.SALES_PRICE, SalesPrice);
                    mapProductId2PriceInfo.put(productId, mapFeild2Price);
                }
            }
            return mapProductId2PriceInfo;
        }catch(Exception e){
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
        }
       
    }


     //根据CustomerId、ProductId、PricingDate-->计算价格信息
     public static Map<String,Map<String,Object>> CaulateProduct2PriceInfoByDownload(String CustomerId,List<String> lstProductId, Date PricingDate, Map<String,Double> mapProductId2Qty){
        Map<String,Map<String,Object>> mapProductId2PriceInfo = new Map<String,Map<String,Object>>();
        try{
            Account objAccount = [
                SELECT OwnerId,Id,CurrencyIsoCode FROM Account WHERE Id = :CustomerId
            ];
            
            //根据CustomerId,ProductId,PricingDate查询价格信息
            //直接去中间表查询-->中间表没有再看是否包含AllItem包含则找中间子表下面的Entry计算
            List<MasterProductPrice__c> lstMasterProduct = [
                SELECT Id,Account__c,Product__c,Start_Date__c,End_Date__c,Final_Price__c,List_Price__c,Has_AllItem__c ,
                Modifier_Entry__c,Authorized_Brand__c,Product__r.Product_Class__c,
                Modifier_Entry__r.Value__c,Modifier_Entry__r.Application_Method__c,Product__r.Order_Model__c,
                Product__r.Product_Series__c,Product__r.PPC_CODE__c,Product__r.PPC_Desp__c
                ,Modifier_Entry__r.Modifier_Type__c,Modifier_Entry__r.Modifier__c,Modifier_Entry__r.Modifier__r.Name,
                Order_Type__c,Authorized_Brand__r.Incoterm__c,Account__r.Classification1__c,Account__r.Country__c,
                Account__r.Country_Description__c,Account__r.Sales_Channel__c
                FROM MasterProductPrice__c
                WHERE Account__c = :customerId  AND Start_Date__c <= :PricingDate AND End_Date__c >= :PricingDate 
                AND Product__c IN :lstProductId
                AND Modifier_Entry__C != NULL
                AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
                AND  Is_Delete__c != true ORDER By min_Final_Prority__c ASC
            ];
            
            Set<String> modifierEntrys = new Set<String>();
            for(MasterProductPrice__c masterProduct : lstMasterProduct) {
                if(String.isNotBlank(masterProduct.Modifier_Entry__c)) {
                    modifierEntrys.add(masterProduct.Modifier_Entry__c);
                }
            }
            Set<String> excludeDiscountList = CCM_ExculdeDiscountUtil.getproductExcludeDiscountList(modifierEntrys, lstProductId);

            List<String> lstCaulatedProduct = new List<String>();
            if (lstMasterProduct.size() == 0) {
                return mapProductId2PriceInfo;
            }
            //如果能够查到数据则直接返回价格
            for(MasterProductPrice__c objMaterialPrice : lstMasterProduct){
                if(objMaterialPrice != null && objMaterialPrice.Has_AllItem__c != true){
                    Map<String,Object> mapFeild2Price = new Map<String,Object>();
                    IF(mapProductId2PriceInfo.containsKey(objMaterialPrice.product__c)){
                        continue;
                    }
                    String key = objMaterialPrice.Modifier_Entry__c + ' - ' + objMaterialPrice.Product__r.Order_Model__c;
                    Boolean needExcludeDiscount = CCM_ExculdeDiscountUtil.needExcludeDiscount(key, excludeDiscountList);
                    lstCaulatedProduct.add(objMaterialPrice.Product__c);
                    mapFeild2Price.put(CCM_Constants.FINAL_PRICE,objMaterialPrice.Final_Price__c);
                    mapFeild2Price.put(CCM_Constants.LIST_PRICE,objMaterialPrice.List_Price__c);
                    mapFeild2Price.put(CCM_Constants.START_DATE,objMaterialPrice.Start_Date__c);
                    mapFeild2Price.put(CCM_Constants.END_DATE,objMaterialPrice.End_Date__c);
                    mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT,objMaterialPrice.Modifier_Entry__r.Value__c);
                    if(needExcludeDiscount) {
                        mapFeild2Price.put(CCM_Constants.FINAL_PRICE, objMaterialPrice.List_Price__c);
                        mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, 0);
                    }
                    mapFeild2Price.put(CCM_Constants.APPLICATION_METHOD,objMaterialPrice.Modifier_Entry__r.Application_Method__c);
                    mapFeild2Price.put('country',objMaterialPrice.Account__r.Country__c);
                    mapFeild2Price.put('countryDescription',objMaterialPrice.Account__r.Country_Description__c);
                    mapFeild2Price.put('salesChannel',objMaterialPrice.Account__r.Sales_Channel__c);
                    mapFeild2Price.put('Classification1',objMaterialPrice.Account__r.Classification1__c);
                    mapFeild2Price.put('productClass',objMaterialPrice.Product__r.Product_Class__c);
                    mapFeild2Price.put('PPCCode',objMaterialPrice.Product__r.PPC_CODE__c);
                    mapFeild2Price.put('PPCDescription',objMaterialPrice.Product__r.PPC_Desp__c);
                    mapFeild2Price.put('ProductSeries',objMaterialPrice.Product__r.Product_Series__c);
                    mapFeild2Price.put('modiferType',objMaterialPrice.Modifier_Entry__r.Modifier_Type__c);
                    mapFeild2Price.put('modifer',objMaterialPrice.Modifier_Entry__r.Modifier__r.Name);
                    mapFeild2Price.put('orderType',objMaterialPrice.Order_Type__c);
                    mapFeild2Price.put('incoTerm',objMaterialPrice.Authorized_Brand__r.Incoterm__c);
                    //售出价格等于FinaPrice*数量
                    Decimal SalesPrice = (objMaterialPrice.Final_Price__c == null ? 0 : objMaterialPrice.Final_Price__c) * 
                        (mapProductId2Qty.get(objMaterialPrice.Product__c) == null ? 0 : mapProductId2Qty.get(objMaterialPrice.Product__c));
                    mapFeild2Price.put(CCM_Constants.SALES_PRICE,SalesPrice);
                    mapProductId2PriceInfo.put(objMaterialPrice.Product__c, mapFeild2Price);
                }
            }
            for(String element : lstCaulatedProduct){
                if(lstProductId.IndexOf(element) >=0 ){
                    lstProductId.remove(lstProductId.IndexOf(element)); 
                }
                
            }
            //去掉已经计算过的Id以后还存在的Id则为AllItem的数据
            if(lstProductId == null && lstProductId.size() == 0 ){
                return mapProductId2PriceInfo;
            }
            if(lstProductId != null && lstProductId.size()>0){
                //根据Customer查询原价信息
                List<Sales_Program__c> lstAuth = [
                    SELECT Id,Customer__c,Modifier_1__c,List_Price_1__c,List_Price_2__c,List_Price_3__c,Price_Book__c,
                    Modifier_2__c FROM Sales_Program__c WHERE Customer__c = :CustomerId
                ]; 
                Set<String> setPriceBookIds = new Set<String>();
                String PriceBook1st = '';
                for(Sales_Program__c objAuth : lstAuth){
                    if(String.isNotBlank(objAuth.List_Price_1__c)){
                        setPriceBookIds.add(objAuth.List_Price_1__c);
                    }
                    if(String.isNotBlank(objAuth.List_Price_2__c)){
                        setPriceBookIds.add(objAuth.List_Price_2__c);
                    }
                    if(String.isNotBlank(objAuth.List_Price_3__c)){
                        setPriceBookIds.add(objAuth.List_Price_3__c);
                    }
                    if(String.isNotBlank(objAuth.Price_Book__c)){
                        setPriceBookIds.add(objAuth.Price_Book__c);
                        PriceBook1st = objAuth.Price_Book__c;
                    }
                    
                }

                List<Pricebook_Entry__c>  lstPriceBookEntryPrice = [
                    SELECT Id,PriceBook__c,UnitPrice__c,End_Date__c,Start_Date__c,Product__c, Product__r.Order_Model__c from Pricebook_Entry__c where PriceBook__c IN :setPriceBookIds
                    and Product__c IN :lstProductId AND End_Date__c >= :PricingDate AND Start_Date__c <= :PricingDate
                    AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
                ];
                Map<String,Decimal> mapProductId2ListPrice = new Map<String,Decimal>();
                Map<String, String> productIdModelMap = new Map<String, String>();
                for(Pricebook_Entry__c objPriceBookEntry : lstPriceBookEntryPrice){
                    //先计算一级价格册的原价
                    if(String.isNotBlank(PriceBook1st) && PriceBook1st == objPriceBookEntry.PriceBook__c){
                        mapProductId2ListPrice.put(objPriceBookEntry.Product__c,objPriceBookEntry.UnitPrice__c);
                        productIdModelMap.put(objPriceBookEntry.Product__c, objPriceBookEntry.Product__r.Order_Model__c);
                    }
                }
                //一级不存在再存放二级
                for(Pricebook_Entry__c objPriceBookEntry : lstPriceBookEntryPrice){
                    if(!mapProductId2ListPrice.containsKey(objPriceBookEntry.Product__c)) {
                        mapProductId2ListPrice.put(objPriceBookEntry.Product__c, objPriceBookEntry.UnitPrice__c);
                        productIdModelMap.put(objPriceBookEntry.Product__c, objPriceBookEntry.Product__r.Order_Model__c);
                    }
                }
                //查询这个Customer下没有Product的并且Has_AllItem__c = true的
                List<MasterProductPrice__c> lstMasterProductAllItem = [
                    SELECT Id,Account__c,Product__c,Start_Date__c,End_Date__c,Final_Price__c,List_Price__c,Has_AllItem__c ,
                    Modifier_Entry__c,Authorized_Brand__c,Min_Final_Prority__c,
                    Modifier_Entry__r.Modifier_Type__c,Modifier_Entry__r.Modifier__c,Modifier_Entry__r.Modifier__r.Name,
                    Modifier_Entry__r.Value__c,Modifier_Entry__r.Application_Method__c,
                    Order_Type__c,Authorized_Brand__r.Incoterm__c,Account__r.Classification1__c,Account__r.Country__c,
                    Account__r.Country_Description__c,Account__r.Sales_Channel__c 
                    FROM MasterProductPrice__c
                    WHERE Account__c = :customerId AND Start_Date__c <= :PricingDate
                     AND End_Date__c >= :PricingDate AND Has_AllItem__c = TRUE
                     AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
                     AND Modifier_Entry__C != NULL
                    ORDER By min_Final_Prority__c ASC
                ];
                // system.debug('lstMasterProductAllItem-->'+lstMasterProductAllItem);
                //获取到master数据。根据MasterId查询-->满足条件的只有一条。直接取第一条的Id查询
                if(lstMasterProductAllItem == null || lstMasterProductAllItem.size()==0){
                    return mapProductId2PriceInfo;
                }
                //查询产品信息
                List<Product2> lstProduct = [
                    SELECT Id,Product_Class__c,
                    Product_Series__c,PPC_CODE__c,PPC_Desp__c
                    FROM Product2 WHERE Id IN :mapProductId2ListPrice.keySet()
                ];
                Map<String,Product2> mapId2Product = new Map<String,Product2>();
                for(Product2 objProduct : lstProduct){
                    mapId2Product.put(objProduct.Id, objProduct);
                }
                //得到minFinal最小的值。去子表中查询
                List<LinkProductAndPrice__c> lstLinkProduct = [
                    SELECT  value__c, Final_Priority__c , Application_Method__c,Modifier_Entry__c,End_Date__c,
                    Modifier_Entry__r.Value__c,Modifier_Entry__r.Application_Method__c,Start_Date__c
                    FROM LinkProductAndPrice__c WHERE MasterProductPrice__c = :lstMasterProductAllItem[0].Id
                    AND Final_Priority__c = :lstMasterProductAllItem[0].Min_Final_Prority__c
                    AND CurrencyIsoCode = :objAccount.CurrencyIsoCode
                    AND  Is_Delete__c != TRUE
                ];
                //遍历所有的product 计算价格
                for(String productId : mapProductId2ListPrice.keySet()){
                    Product2 objProductInfo = mapId2Product.get(productId);
                    Decimal ListPrice = mapProductId2ListPrice.get(productId);
                    //同一个产品下同一优先级多个时
                    //创建价格到Link的映射
                    String key = lstMasterProductAllItem[0].Modifier_Entry__c + ' - ' + productIdModelMap.get(productId);
                    Boolean needExcludeDiscount = CCM_ExculdeDiscountUtil.needExcludeDiscount(key, excludeDiscountList);
                    Map<Decimal,LinkProductAndPrice__c> mapPrice2Link = new Map<Decimal,LinkProductAndPrice__c>();
                    
                    List<Decimal> lstFinalPrice = new List<Decimal>();
                    for(LinkProductAndPrice__c objLink : lstLinkProduct){
                        Decimal FinalPrice = 0;
                        if(objLink.Application_Method__c == 'Percent'){
                            //表示是百分比
                            Decimal FinalDiscountPrice = ListPrice*objLink.value__c/100;
                            FinalDiscountPrice = FinalDiscountPrice.setScale(2,RoundingMode.HALF_UP);
                            FinalPrice = ListPrice - FinalDiscountPrice;
                            FinalPrice = FinalPrice.setScale(2,RoundingMode.HALF_UP);
                            if(needExcludeDiscount) {
                                FinalPrice = ListPrice;
                            }
                        }else if(objLink.Application_Method__c == 'New Price'){
                            FinalPrice = objLink.value__c;
                        }
                        lstFinalPrice.add(FinalPrice);
                        mapPrice2Link.put(FinalPrice,objLink);
                    }
                    // system.debug('排序前---->'+lstFinalPrice);
                    lstFinalPrice.sort();
                    // system.debug('排序后---->'+lstFinalPrice);
                    Map<String,Object> mapFeild2Price = new Map<String,Object>();
                    mapFeild2Price.put(CCM_Constants.FINAL_PRICE,lstFinalPrice[0]);
                    LinkProductAndPrice__c objFinalLink = mapPrice2Link.get(lstFinalPrice[0]);
                    mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT,objFinalLink.Modifier_Entry__r.Value__c);
                    if(needExcludeDiscount) {
                        mapFeild2Price.put(CCM_Constants.STAND_DISCOUNT, 0);
                    }
                    mapFeild2Price.put(CCM_Constants.APPLICATION_METHOD,objFinalLink.Modifier_Entry__r.Application_Method__c);
                    mapFeild2Price.put(CCM_Constants.LIST_PRICE,ListPrice);
                    mapFeild2Price.put(CCM_Constants.START_DATE,lstLinkProduct[0].Start_Date__c);
                    mapFeild2Price.put(CCM_Constants.END_DATE,lstLinkProduct[0].End_Date__c);
                    mapFeild2Price.put('country',lstMasterProductAllItem[0].Account__r.Country__c);
                    mapFeild2Price.put('countryDescription',lstMasterProductAllItem[0].Account__r.Country_Description__c);
                    mapFeild2Price.put('salesChannel',lstMasterProductAllItem[0].Account__r.Sales_Channel__c);
                    mapFeild2Price.put('Classification1',lstMasterProductAllItem[0].Account__r.Classification1__c);
                    mapFeild2Price.put('productClass',objProductInfo.Product_Class__c);
                    mapFeild2Price.put('PPCCode',objProductInfo.PPC_CODE__c);
                    mapFeild2Price.put('PPCDescription',objProductInfo.PPC_Desp__c);
                    mapFeild2Price.put('ProductSeries',objProductInfo.Product_Series__c);
                    mapFeild2Price.put('modiferType',lstMasterProductAllItem[0].Modifier_Entry__r.Modifier_Type__c);
                    mapFeild2Price.put('modifer',lstMasterProductAllItem[0].Modifier_Entry__r.Modifier__r.Name);
                    mapFeild2Price.put('orderType',lstMasterProductAllItem[0].Order_Type__c);
                    mapFeild2Price.put('incoTerm',lstMasterProductAllItem[0].Authorized_Brand__r.Incoterm__c);
                    //售出价格等于FinaPrice*数量
                    Decimal SalesPrice = (lstFinalPrice[0]) * 
                        (mapProductId2Qty.get(productId) == null ? 0 : mapProductId2Qty.get(productId));
                    mapFeild2Price.put(CCM_Constants.SALES_PRICE,SalesPrice);
                    mapProductId2PriceInfo.put(productId, mapFeild2Price);
                }
            }
            return mapProductId2PriceInfo;
        }catch(Exception e){
            system.debug('报错行数---》'+e.getLineNumber()+'报错信息4---->'+e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static string GetUserType(){
        try {
            return CCM_PurchaseOrderDetailController.GetUserType();
            
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    // Helper method to get product ID from Product2 object based on product model
    private static Product2 getProductModelId(String productModel) {
        List<Product2> products = [SELECT Id,Est_Order_Volum__c,Est_Order_Weight__c FROM Product2 WHERE Order_Model__c = :productModel LIMIT 1];
        if (!products.isEmpty()) {
            return products[0];
        }
        return null;
    }

    private static Map<String, Product2> getProductModelIds(Set<String> productModels, Set<String> inactiveProducts) {
        List<Product2> products = [SELECT Id, Est_Order_Volum__c, Est_Order_Weight__c, Order_Model__c, Product_Status__c
                                   FROM Product2 WHERE Order_Model__c IN :productModels];
        Map<String, Product2> productModelMap = new Map<String, Product2>();
        for (Product2 p : products) {
            if(p.Product_Status__c == 'INACT' || p.Product_Status__c == 'RTO') {
                inactiveProducts.add(p.Order_Model__c);
            }
            productModelMap.put(p.Order_Model__c, p);
        }
        return productModelMap;
    }
    
    // Wrapper class for purchase order item
    public class PurchaseOrderItemWrapper {
        @AuraEnabled public String orderGroupNumber;
        @AuraEnabled public String customerNumber;
        @AuraEnabled public String customerPO;
        @AuraEnabled public String billToAddressCode;
        @AuraEnabled public String shipToAddressCode;
        @AuraEnabled public String dropshipAddressCode;
        @AuraEnabled public String insuranceFee;
        @AuraEnabled public String otherFee;
        @AuraEnabled public String instructionToDSV;
        @AuraEnabled public String carrierInformation;
        @AuraEnabled public String orderType;
        @AuraEnabled public String warehouse;
        @AuraEnabled public String isDropship;
        @AuraEnabled public String modelNumber;
        @AuraEnabled public String requestDate;
        @AuraEnabled public String ScheduleShipDate;
        @AuraEnabled public String PricingDate;
        @AuraEnabled public String qty;
        @AuraEnabled public String remark;
    }

    public static void forCodeCoverage() {
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
}