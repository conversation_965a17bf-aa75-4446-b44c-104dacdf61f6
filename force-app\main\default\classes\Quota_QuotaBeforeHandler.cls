public without sharing class Quota_QuotaBeforeHandler implements Triggers.Handler {
    public static Boolean isRun = true;
    public void handle() {
        if(isRun){
            if(Trigger.isBefore && (Trigger.isInsert || Trigger.isUpdate)){

                QuotaFilter existQuotaFilter = new QuotaFilter();

                QuotaFilter previousQuotaFilter = new QuotaFilter();

                Map<String, Id> name2tIdMap = getName2territoryIdMapByType('Channel');

                for(Quota_Allocation__c quota : (List<Quota_Allocation__c>)Trigger.new){
                    if(Trigger.isInsert){
                        if(!checkStartDateAndEndDate(quota)){
                            quota.addError(Label.Quota_Check_Date);
                        }
                        if(quota.Start_Date__c >= quota.End_Date__c){
                            quota.addError(Label.Quota_DateError);
                        }
                        if(quota.Previous_Quota_Allocation__c != null){
                            previousQuotaFilter.needGetPrevious = true;
                            previousQuotaFilter.idSet.add(quota.Previous_Quota_Allocation__c);
                        }

                        if(canCheckDuplicate(quota)){
                            existQuotaFilter.needCheckDuplicate = true;

                            existQuotaFilter.productLineIdSet.add(quota.Product_Line__c);
                            existQuotaFilter.recordTypeIdSet.add(quota.RecordTypeId);

                            if(existQuotaFilter.startDate == null || existQuotaFilter.startDate > quota.Start_Date__c){
                                existQuotaFilter.startDate = quota.Start_Date__c;
                            }

                            if(existQuotaFilter.endDate == null || existQuotaFilter.endDate < quota.End_Date__c){
                                existQuotaFilter.endDate = quota.End_Date__c;
                            }
                        }
                    }else if(Trigger.isUpdate){
                        Quota_Allocation__c oldQuota = (Quota_Allocation__c)Trigger.oldMap.get(quota.Id);
                        if(
                            quota.Start_Date__c != oldQuota.Start_Date__c
                            || quota.End_Date__c != oldQuota.End_Date__c
                        ){
                            if(!checkStartDateAndEndDate(quota)){
                                quota.addError(Label.Quota_Check_Date);
                            }
                            if(quota.Start_Date__c >= quota.End_Date__c){
                                quota.addError(Label.Quota_DateError);
                            }

                        }

                        if(
                            quota.Previous_Quota_Allocation__c != null 
                            && quota.Previous_Quota_Allocation__c != oldQuota.Previous_Quota_Allocation__c
                        ){
                            previousQuotaFilter.needGetPrevious = true;
                            previousQuotaFilter.idSet.add(quota.Previous_Quota_Allocation__c);
                        }

                        if(
                            quota.Product_Line__c != oldQuota.Product_Line__c
                            || quota.Start_Date__c != oldQuota.Start_Date__c
                            || quota.End_Date__c != oldQuota.End_Date__c
                            || quota.RecordTypeId != oldQuota.RecordTypeId
                            || quota.Actived__c != oldQuota.Actived__c
                        ){
                            if(canCheckDuplicate(quota)){
                                existQuotaFilter.needCheckDuplicate = true;

                                existQuotaFilter.productLineIdSet.add(quota.Product_Line__c);
                                existQuotaFilter.recordTypeIdSet.add(quota.RecordTypeId);
                                existQuotaFilter.notInIdSet.add(quota.Id);

                                if(existQuotaFilter.startDate == null || existQuotaFilter.startDate > quota.Start_Date__c){
                                    existQuotaFilter.startDate = quota.Start_Date__c;
                                }

                                if(existQuotaFilter.endDate == null || existQuotaFilter.endDate < quota.End_Date__c){
                                    existQuotaFilter.endDate = quota.End_Date__c;
                                }
                            }
                        }

                    }
                }

                List<Quota_Allocation__c> existQuotaList = new List<Quota_Allocation__c>();
                if(existQuotaFilter.needCheckDuplicate){
                    existQuotaList = getQuotaList(existQuotaFilter);
                }

                Map<Id, Quota_Allocation__c> previousQuotaMap = new Map<Id, Quota_Allocation__c>();
                if(previousQuotaFilter.needGetPrevious){
                    previousQuotaMap = new Map<Id, Quota_Allocation__c>(getQuotaList(previousQuotaFilter));
                }

                for(Quota_Allocation__c quota : (List<Quota_Allocation__c>)Trigger.new){
                    //赋值逻辑放在最前面
                    Quota_Allocation__c previousQuota = previousQuotaMap.get(quota.Previous_Quota_Allocation__c);
                    if(previousQuota != null){
                        quota.Start_Date__c = previousQuota.Start_Date__c;
                        quota.End_Date__c = previousQuota.End_Date__c;
                        quota.Product_Line__c = previousQuota.Product_Line__c;
                        quota.Channel__c = previousQuota.Channel__c;
                        //新建没有赋值的时候
                        if(Trigger.isInsert && quota.National_Gross_Quota__c == 0){
                            quota.National_Gross_Quota__c = previousQuota.National_Gross_Quota__c;
                        }
                    }


                    for(Quota_Allocation__c existQuota : existQuotaList){
                        if(
                            quota.Product_Line__c != existQuota.Product_Line__c
                            || quota.RecordTypeId != existQuota.RecordTypeId
                        ){
                            continue;
                        }

                        if(quota.Start_Date__c <= existQuota.End_Date__c && quota.End_Date__c >= existQuota.Start_Date__c){
                            quota.addError(Label.Quota_Check_Duplicate);
                        }
                    }
                }
            }
            if (Trigger.isAfter && Trigger.isUpdate) {
                for(Quota_Allocation__c quota : (List<Quota_Allocation__c>)Trigger.new){
                    Quota_Allocation__c oldQuota = (Quota_Allocation__c)Trigger.oldMap.get(quota.Id);
                    if (quota.Status__c != oldQuota.Status__c && quota.Status__c == 'Approved') {
                        //Quota_TargetCustomerSummaryBatch targetSummaryBatch = new Quota_TargetCustomerSummaryBatch(new Set<Id>{quota.Id});
                        //Database.executeBatch(targetSummaryBatch, 1000);
                        CCM_SalesTarget_SetTargetFlagCtrl.setTargetFlag(quota.Id);
                    }
                }
            }
        }
    }

    private static Boolean checkStartDateAndEndDate(Quota_Allocation__c quota){
        Boolean flag = true;

        //开始日期不是月初
        if(quota.Start_Date__c != null && quota.Start_Date__c.Day() != 1){
            flag = false;
        }

        //结束日期不是月末
        if(quota.End_Date__c != null){
            Integer day = quota.End_Date__c.Day();
            Integer lastDay = Date.daysInMonth(quota.End_Date__c.Year(), quota.End_Date__c.Month());
            if(day != lastDay){
                flag = false;
            }
        }

        return flag;
    }

    private class QuotaFilter{
        private Set<Id> productLineIdSet;
        private Set<Id> notInIdSet;
        private Set<Id> idSet;
        private Set<Id> recordTypeIdSet;
        private Date startDate;
        private Date endDate;
        private Boolean needCheckDuplicate;
        private Boolean needGetPrevious;

        private QuotaFilter(){
            this.productLineIdSet = new Set<Id>();
            this.notInIdSet = new Set<Id>();
            this.idSet = new Set<Id>();
            this.recordTypeIdSet = new Set<Id>();
            this.needCheckDuplicate = false;
            this.needGetPrevious = false;
        }
    }

    private static Boolean canCheckDuplicate(Quota_Allocation__c quota){
        Boolean flag = false;

        if(
            quota.Product_Line__c != null
            && quota.Start_Date__c != null
            && quota.End_Date__c != null
            && quota.RecordTypeId != null
            && quota.Actived__c
        ){
            flag = true;
        }

        return flag;
    }

    private static List<Quota_Allocation__c> getQuotaList(QuotaFilter existQuotaFilter){
        String queryStr =
            ' SELECT Id, Start_Date__c, End_Date__c, ' +
            ' Product_Line__c, RecordTypeId, '+
            ' National_Gross_Quota__c, Channel__c ' +
            ' FROM Quota_Allocation__c ';

        String filterStr = '';
        if(!existQuotaFilter.idSet.isEmpty()){
            Set<Id> idSet = existQuotaFilter.idSet;
            filterStr += ' AND Id IN :idSet';
        }

        if(!existQuotaFilter.notInIdSet.isEmpty()){
            Set<Id> notInIdSet = existQuotaFilter.notInIdSet;
            filterStr += ' AND Id NOT IN :notInIdSet';
        }

        if(!existQuotaFilter.productLineIdSet.isEmpty()){
            Set<Id> productLineIdSet = existQuotaFilter.productLineIdSet;
            filterStr += ' AND Product_Line__c IN :productLineIdSet';
        }

        if(!existQuotaFilter.recordTypeIdSet.isEmpty()){
            Set<Id> recordTypeIdSet = existQuotaFilter.recordTypeIdSet;
            filterStr += ' AND RecordTypeId IN :recordTypeIdSet';
        }

        if(existQuotaFilter.startDate != null){
            Date startDate = existQuotaFilter.startDate;
            filterStr += ' AND Start_Date__c >= :startDate';
        }

        if(existQuotaFilter.endDate != null){
            Date endDate = existQuotaFilter.endDate;
            filterStr += ' AND End_Date__c <= :endDate';
        }

        filterStr += ' AND Actived__c = TRUE ';

        if(String.isNotBlank(filterStr)){
            //将第一个 'AND' 替换 'WHERE'
            queryStr += ' WHERE ' + filterStr.removeStart(' AND ');
        }
        System.debug(LoggingLevel.INFO, '*** queryStr: ' + queryStr);
        return Database.query(queryStr);
    }

    private static Map<String, Id> getName2territoryIdMapByType(String type){
        List<Territory__c> territoryList = new List<Territory__c>([
            SELECT Id, Developer_Name__c,Brand_Name__c
            FROM Territory__c
            WHERE Type__c = :type
            AND Actived__c = TRUE
        ]);

        Map<String, Id> name2tIdMap = new Map<String, Id>();

        for(Territory__c territory : territoryList){
            name2tIdMap.put(territory.Brand_Name__c, territory.Id);
        }

        return name2tIdMap;
    }

}