public without sharing class CCM_Community_InvoiceTabCtl {
    /**
     * 
     * invoiceType: INV / CREDIT
     * 查询和过滤 3 种类型的 record type
     * invRecordType:
     *  1、warranty
     *  2、training order
     *  3、sales order
     */
    @AuraEnabled
    public static String searchInvoice(
                                    Integer pageNumber
                                    , Integer pageSize
                                    , String invoiceNumber
                                    , String vatRegNo
                                    , String invoiceType
                                    , String dtStart
                                    , String dtEnd
                                    , String invRecordType
                                    ){
        // map， 返回信息封装
        Map<String,Object> result = new Map<String,Object>();
        // 最终过滤掉，满足条件的 invoice 集合
        List<InvoiceRow> lstReturnInvoiceRows = new List<InvoiceRow>();
        // 返回值 - table data
        List<InvoiceRow> tableData = new List<InvoiceRow>();
        try {
            // 查找 account 相关的信息
            User currentUser = [
                                SELECT Id
                                , ContactId
                                , Contact.AccountId
                                FROM User WHERE Id != NULL 
                                AND Id = :UserInfo.getUserId()
                            ];
            // 判断 currentUser 的类型
            String customerId = currentUser.Contact.AccountId;
            // 符合条件的 sales order 和 
            // 如果没有筛选条件，就说明是全部查询
            String soqlInvoiceQuery = ''
                                    + ' SELECT Id,Name'
                                    + ' ,Customer__c'
                                    + ' ,Order__c,Order__r.RecordType.Developername'
                                    + ' ,Invoice_Number__c'
                                    + ' ,Your_VAT_Re_No__c'
                                    + ' ,Invoice_Date__c'
                                    + ' ,Order__r.Order_Number__c'
                                    + ' ,Order__r.Order_Date__c'
                                    + ' ,Order_Number__c'
                                    + ' ,Total_Amount__c'
                                    + ' ,Credit_note_for_Invoice__c'
                                    + ' ,Scene_Type__c'
                                    + ' ,Training_Order__r.Order_Number__c'
                                    + ' ,Training_Order__r.Order_Date__c'
                                    + ' FROM Invoice__c WHERE Id != NULL'
                                    + ' AND Customer__c = :customerId';
            // 
            /**
             * 3 种类型的 invoice 
             * 1、sales order
             *      invoice__r.order__r.recordtype
             *          PreSeason_Order Regular_Order
             * 2、warranty -> 这种没有需求了
             *      invoice__r.order__r.recordtype
             *          Warranty_Claim  Warranty_Order
             * 3、training order
             *      invoice__r.Training_Order__c != null
             * 4、符合条件的 Invoice__c.Scene_Type__c - picklist values
             *      Sales Order; 
             *      Training Request;
             *      // 下面的 warranty 没有需求了
             *      Warranty Order; Warranty Claim; Warranty Inv;
             */
            // lookup 满足条件
            List<String> lstAvailableOrderRecordtype = new List<String>();
            lstAvailableOrderRecordtype.add('PreSeason_Order');
            lstAvailableOrderRecordtype.add('Regular_Order');
            // lstAvailableOrderRecordtype.add('Warranty_Claim');
            // lstAvailableOrderRecordtype.add('Warranty_Order');

            soqlInvoiceQuery += ' AND ('
                                + ' Order__r.RecordType.Developername IN :lstAvailableOrderRecordtype'
                                + ' OR Training_Order__c != null'
                                + ' )';
            // Scene_Type__c picklist 满足条件
            List<String> lstAvailableSceneType = new List<String>();
            lstAvailableSceneType.add('Sales Order');
            // lstAvailableSceneType.add('Warranty Order');
            // lstAvailableSceneType.add('Warranty Claim');
            // lstAvailableSceneType.add('Warranty Inv');
            lstAvailableSceneType.add('Training Request');
            // TBD -> 待刷新数据
            // soqlInvoiceQuery += ' AND Scene_Type__c IN :lstAvailableSceneType';

            if (String.isNotBlank(invRecordType)) {
                // sales order 筛选
                if (invRecordType == 'salesorder') {
                    soqlInvoiceQuery += ' AND Order__r.RecordType.Developername IN (\'PreSeason_Order\', \'Regular_Order\') ';
                    soqlInvoiceQuery += ' AND Scene_Type__c = \'Sales Order\'';
                
                // training order 筛选
                } else if (invRecordType == 'trainingorder') {
                    soqlInvoiceQuery += ' AND Training_Order__c != NULL';
                    soqlInvoiceQuery += ' AND Scene_Type__c = \'Training Request\'';

                // warranty 筛选
                } else if (invRecordType == 'warrantyorder') {
                    soqlInvoiceQuery += ' AND Order__r.RecordType.Developername IN (\'Warranty_Order\') ';
                    soqlInvoiceQuery += ' AND Scene_Type__c IN (\'Warranty Order\') ';
                }else if (invRecordType == 'warrantyclaim') {
                    soqlInvoiceQuery += ' AND Order__r.RecordType.Developername IN (\'Warranty_Claim\') ';
                    soqlInvoiceQuery += ' AND Scene_Type__c IN (\'Warranty Claim\', \'Warranty Inv\') ';
                }
            }

            if (String.isNotBlank(invoiceNumber)) {
                invoiceNumber = String.escapeSingleQuotes(invoiceNumber);
                invoiceNumber = '%' + invoiceNumber + '%';
                soqlInvoiceQuery += ' AND Invoice_Number__c LIKE :invoiceNumber';
            }
            if (String.isNotBlank(vatRegNo)) {
                vatRegNo = String.escapeSingleQuotes(vatRegNo);
                vatRegNo = '%' + vatRegNo + '%';
                soqlInvoiceQuery += ' AND Your_VAT_Re_No__c LIKE :vatRegNo';
            }
            // invoice type 的类型 INV / CREDIT
            if (String.isNotBlank(invoiceType)) {
                invoiceType = String.escapeSingleQuotes(invoiceType);
                soqlInvoiceQuery += ' AND Credit_note_for_Invoice__c = :invoiceType';
            }
            // 开始时间和结束时间的筛选条件
            if (String.isNotBlank(dtStart) && String.isNotBlank(dtEnd)) {
                Date dtStartFilter = Date.valueOf(dtStart);
                Date dtEndFilter = Date.valueOf(dtEnd);

                soqlInvoiceQuery += ' AND Invoice_Date__c >= :dtStartFilter';
                soqlInvoiceQuery += ' AND Invoice_Date__c <= :dtEndFilter';
            }

            soqlInvoiceQuery += ' ORDER BY Lastmodifieddate DESC LIMIT 49999';

            List<Invoice__c> lstInvoice = Database.query(soqlInvoiceQuery);
            System.debug('soqlInvoiceQuery:' + soqlInvoiceQuery);
            System.debug('lstInvoice:' + lstInvoice);

            Map<String,Decimal> invoiceAmountMap = New Map<String,Decimal>();
            invoiceAmountMap = getTotalAmountMap(lstInvoice);
            for (Invoice__c invoiceTmp : lstInvoice) {
                InvoiceRow invoiceRowItem = new InvoiceRow();
                invoiceRowItem.invoiceId = invoiceTmp.Id;
                invoiceRowItem.invoiceNumber = invoiceTmp.Invoice_Number__c;
                invoiceRowItem.vatRegNo = invoiceTmp.Your_VAT_Re_No__c;
                if(invRecordType == 'trainingorder'){
                    invoiceRowItem.orderNumber = invoiceTmp.Training_Order__r.Order_Number__c;
                    invoiceRowItem.orderDate = String.valueOf(invoiceTmp.Training_Order__r.Order_Date__c);
                }
                else {
                    invoiceRowItem.orderNumber = invoiceTmp.Order__r.Order_Number__c;
                    invoiceRowItem.orderDate = String.valueOf(invoiceTmp.Order__r.Order_Date__c);
                }
                invoiceRowItem.invoiceAmount = invoiceAmountMap.containsKey(invoiceTmp.id)? invoiceAmountMap.get(invoiceTmp.id) : 0;
                invoiceRowItem.invoiceType = invoiceTmp.Credit_note_for_Invoice__c;
                invoiceRowItem.invoiceDate = String.valueOf(invoiceTmp.invoice_Date__c);
                // TODO , 待接口刷新数据
                invoiceRowItem.sceneType = invoiceTmp.Id + '@' + invoiceTmp.Scene_Type__c;
                lstReturnInvoiceRows.add(invoiceRowItem);
            }

            Integer min = ((Integer)pageNumber - 1) * pageSize;
            Integer max = (Integer)pageNumber * pageSize -1;
            for (Integer i = min ; i <= max; i++ ) {
                if (i < lstReturnInvoiceRows.size()) {
                    tableData.add(lstReturnInvoiceRows.get(i));
                }
            }
            result.put('tableData', tableData);
            result.put('totalData', lstReturnInvoiceRows.size());
            // result.put('strSoql', soqlInvoiceQuery);

            return JSON.serialize(result);
        } catch (Exception e) {
            System.debug('stack:');
            throw new AuraHandledException(e.getStackTraceString());
            // throw new AuraHandledException(e.getMessage());
        }
    }

    public static Map<String,Decimal> getTotalAmountMap(List<Invoice__c> invoiceList){
        Map<String,Decimal> resultMap = New Map<String,Decimal>();
        Set<Id> invoiceIdSet = New Set<Id>();
        List<Invoice_Item__c> invoiceItemList = New List<Invoice_Item__c>(); 
        for(Invoice__c item: invoiceList){
            invoiceIdSet.add(item.id);
        }
        invoiceItemList = [Select id,Amount__c,Invoice__c From Invoice_Item__c Where Invoice__c In:invoiceIdSet];
        for(Invoice_Item__c invItem: invoiceItemList){
            if(!resultMap.containsKey(invItem.Invoice__c)){
                if(invItem.Amount__c != null){
                    resultMap.put(invItem.Invoice__c,invItem.Amount__c);
                }else{
                    resultMap.put(invItem.Invoice__c,0);
                }
            }else{
                if(invItem.Amount__c != null){
                    Decimal total = resultMap.get(invItem.Invoice__c);
                    total += invItem.Amount__c;
                    resultMap.put(invItem.Invoice__c,total);
                } 
            }

        }
        return resultMap;
    }

    @AuraEnabled
    public static string getCustomerCurrency(){
        User currentUser = [
                            SELECT Id
                            , ContactId
                            , Contact.Account.CurrencyIsoCode
                            FROM User WHERE Id != NULL 
                            AND Id = :UserInfo.getUserId()
                        ];
        String currencyCode = currentUser.Contact.Account.CurrencyIsoCode;
        if(String.isBlank(currencyCode)) {
            currencyCode = 'EUR';
        }                
        return currencyCode;
    }

    // invoice 每一行的详细信息 -》 table 展示内容
    public class InvoiceRow {
        public String invoiceId;
        public String invoiceNumber;
        public String vatRegNo;
        public String orderNumber;
        public String orderDate;
        public Decimal invoiceAmount;
        public String invoiceType;
        // Sales Order / Training Request
        public String sceneType;
        public String invoiceDate;

    }
}