/**************************************************************************************************
 * Name: WarrantyClaimBasicHandler
 * Object: Lead
 * Purpose:  Warranty Claim util class
 * Author:Aria W Zhong
 * Create Date: 2023-09-25
 * Modify History:
 **************************************************************************************************/
public with sharing class WarrantyClaimUtil{
    public static Boolean IsPortal(){
        //获取Profile用以以判断是否为portal端
        User user = [select id, Profile.Name, UserType
                     from User
                     where id = :UserInfo.getUserId()];
        if (user.UserType.contains('PowerPartner')){
            return true;
        } else{
            return false;
        }
    }
    /**非Portal端需要选择Customer */
    public static String queryCustomer(String name, Integer pageNumber, Integer allPageSize){
        name = '%' + name + '%';
        List<Account> accountList = [SELECT Id, RecordType.DeveloperName, name
                                     FROM Account 
                                     WHERE RecordType.DeveloperName in ('Channel', 'Association_Group') AND Status__c = 'Active' AND Name LIKE:name
                                     ORDER BY CreatedDate DESC
                                     LIMIT 5000];
        List<AccountEntity> accList = new List<AccountEntity>();
        if (accountList.size() > 0){
            Integer totalSize = accountList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                AccountEntity accountEntity = new AccountEntity();
                accountEntity.consumerId = accountList[i].Id;
                accountEntity.name = accountList[i].Name;
                accList.add(accountEntity);
            }
        }

        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('List', accList);
        returnMap.put('TotalSize', accountList.size());
        return JSON.serialize(returnMap);
    }
    /**选择存在的warranty claim */
    public static String queryWarrantyClaim(String name, Integer pageNumber, Integer allPageSize, String ConsumerId){
        name = '%' + name + '%';
        List<Warranty_Claim__c> warrantyList = new List<Warranty_Claim__c>();
        if (String.isNotBlank(ConsumerId)){
            warrantyList = [SELECT Id, name
                            FROM Warranty_Claim__c 
                            WHERE NAME LIKE:name and Dealer_Name__c = :ConsumerId
                            ORDER BY CreatedDate DESC
                            LIMIT 5000];
        } else{
            warrantyList = [SELECT Id, name
                            FROM Warranty_Claim__c 
                            WHERE NAME LIKE:name
                            ORDER BY CreatedDate DESC
                            LIMIT 5000];
        }

        List<WarrantyClaimEntity> wList = new List<WarrantyClaimEntity>();
        if (warrantyList.size() > 0){
            Integer totalSize = warrantyList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                WarrantyClaimEntity warrantyClaimEntity = new WarrantyClaimEntity();
                warrantyClaimEntity.warrantyClaimId = warrantyList[i].Id;
                warrantyClaimEntity.name = warrantyList[i].Name;
                wList.add(warrantyClaimEntity);
            }
        }

        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('List', wList);
        returnMap.put('TotalSize', warrantyList.size());
        return JSON.serialize(returnMap);
    }
    /**获取customer信息:判断是 dealer还是distributor,获取labor Price */
    public static String getCustomerInformation(String accId){
        Map<String, Object> returnMap = new Map<String, Object>();
        //portal 获取
        if (String.isBlank(accId)){
            User user = [select id, Contact.AccountId, Profile.Name
                         from user 
                         where Id = :UserInfo.getUserId()];
            accId = user.Contact.AccountId;
        }
        if (String.isNotBlank(accId)){
            Account acc = [select id, Sales_Channel__c, Classification1__c, Labor_Rate__c, CurrencyIsoCode, Country__c
                           from Account
                           WHERE Id = :accId];
            CCM_LaborUtil.LaborWrapper wrapper = CCM_LaborUtil.getLabor(acc.Id);
            String value = '';
            if (String.isNotBlank(acc.Sales_Channel__c)){
                if (acc.Sales_Channel__c.containsIgnoreCase('Distributor')){
                    value = 'Distributor';
                }
                if (acc.Sales_Channel__c.containsIgnoreCase('Dealer')){
                    value = 'Dealer';
                }
            }
            returnMap.put('DistributorOrDealer', value);
            returnMap.put('Country', acc.Country__c);
            if(wrapper != null) {
                returnMap.put('LaborRate', wrapper.laborRate);
                returnMap.put('LaborPriceBook', wrapper.priceBookName);
            }
            returnMap.put('CurrencyIsoCode', acc.CurrencyIsoCode);

            Boolean needCalculateTax = needCalculateTax(acc);
            returnMap.put('needCalculateTax', needCalculateTax);
        }

        returnMap.put('AccountId', accId);
        return JSON.serialize(returnMap);
    }

    public static Boolean needCalculateTax(Account acc){
        Boolean needCalculateTax = false;
        if(acc.Country__c == 'DE') {
            if(acc.Sales_Channel__c.containsIgnoreCase('Dealer')) {
                needCalculateTax = true;
            }
            if(acc.Sales_Channel__c.containsIgnoreCase('PbE')) {
                if(acc.Classification1__c == 'PbE Importdealer' || acc.Classification1__c == 'International Manufacturers' || acc.Classification1__c == 'National Manufacturer'){
                    needCalculateTax = true;
                }
            }
        }
        return needCalculateTax;
    }

    public static String getPicklistOption(String objectAPI, String fieldAPI, String fifterString){
        List<SelectOption> selectList = new List<SelectOption>();
        List<SelectOptionItem> selectListReturn = new List<SelectOptionItem>();
        if (String.isNotBlank(fifterString)){
            List<String> exceptValueList = New List<String>();
            exceptValueList.add(fifterString);
            selectList = CCM_Untils.getPicklistOption(objectAPI, fieldAPI, exceptValueList);
        } else{
            selectList = CCM_Untils.getPicklistOption(objectAPI, fieldAPI);

        }
        for (SelectOption item : selectList){
            SelectOptionItem option = new SelectOptionItem();
            option.label = item.getLabel();
            option.value = item.getValue();
            selectListReturn.add(option);
        }
        return JSON.serialize(selectListReturn);
    }
    public class AccountEntity{
        public String name;
        public String consumerId;
    }
    public class WarrantyClaimEntity{
        public String name;
        public String warrantyClaimId;
    }
    private class SelectOptionItem{
        public String label;
        public String value;
    }
}