({
    // 获取customer 相关信息
    getCustomerInfo : function(component, name) {
        let self = this;
        var action = component.get('c.QueryCustomer');
        action.setParams({
            CustomerId: component.get('v.customerId'),
        })
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            console.log(result, '获取customer 相关信息=========');
            if (state === 'SUCCESS' ) {
                component.set('v.customerInfo', result);
                component.set('v.currencySymbol', result.CurrencyIsoCode);
                console.log(component.get('v.currencySymbol'), 'currencySymbol------------');
                const url = '/' + component.get('v.customerId');
                component.set('v.customerUrl', url);
                if (component.get('v.userType') == 'InsideSales') {
                    self.queryAlertMessage(component, result.AccountNumber);
                }
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 创建Po信息
    createPo : function(component, name) {
        let self = this;
        var action = component.get('c.createPO');
        action.setParams({
            selectCustomer: component.get('v.customerId'),
            warehouse: component.get('v.warehouse'),
            isDropship: component.get('v.isDropShip') === 'Y' ? true : false,
            pricingDate: component.get('v.pricingDate'),
            CustomerPo: component.get('v.customerPO'),
            RecordTypeName: component.get('v.recordTypeName'),
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            status: component.get('v.orderStatus'),
            contactId: component.get('v.contactId'),
        })
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, '创建Po信息=========');
            if (state === 'SUCCESS' ) {
                var result = response.getReturnValue();
                console.log(response.getReturnValue(), '创建Po信息 result=========');
                if (component.get('v.actionType') !== 'edit') {
                    component.set('v.purchaseOrderId', result);
                }
                // 判断是否保存drop address
                let isCollectiveCustomer = component.get('v.isCollectiveCustomer');
                let isDropShip = component.get('v.isDropShip');
                if (isCollectiveCustomer && isDropShip == 'Y') {
                    self.updatePoInfo(component);
                } else {
                    component.set("v.currentStep", component.get("v.currentStep") + 1);
                }
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // customer提示信息
    queryAlertMessage : function(component, customerNumber) {
        var action = component.get('c.queryAlertMessage');
        action.setParams({
            CustomerNumber: customerNumber,
            AlertMode: 'Create',
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'customer提示信息=========');
            if (state === 'SUCCESS' ) {
                var result = response.getReturnValue();
                console.log(result, 'customer提示信息 result=========');
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                   "title": "Success",
                   "message": result,
                   "type": "success",
                   "duration": "pester"
                }).fire();
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    // 获取order数据
    getPODetailInfo : function(component, step) {
        let self = this;
        var action = component.get('c.QueryPirchaseAndItemInfo');
        action.setParams({
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            IsProtal: false,
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            var result = JSON.parse(response.getReturnValue());
            console.log(JSON.stringify(result), '获取order数据-----------------');
            if (state === 'SUCCESS') {
                component.set('v.PodetailInfo', result);
                // 回填参数
                component.set('v.warehouse', result.WareHouse);
                component.set('v.customerPO', result.CustomerPo);
                component.set('v.isDropShip', result.IsDropship ? 'Y' : 'N');
                component.set('v.pricingDate', result.PricingDate);
                component.set('v.recordTypeName', result.recordTypeName == 'Regular Order' ? 'Regular_Order' : 'Pre_season_Order'),
                component.set('v.orderStatus', result.OrderStatus);
                component.set('v.purchaseOrderId', component.get('v.purchaseOrderId')),
                // 获取customer detail
                component.set('v.customerId', result.CustomerId);
                component.set('v.customerName', result.CustomerName);
                // 获取coontact
                component.set('v.contactId', result.contactId);
                // 最大step
                if (component.get('v.actionType') === 'draft') {
                    component.set('v.maxStep', Number(result.MaxStep));
                }
                component.set('v.customerObj', {
                    Id: result.CustomerId,
                    Name: result.CustomerName,
                });
                component.set('v.contactObj', {
                    Id: result.contactId,
                    Name: result.contactName,
                });
                component.set('v.shipToAddressId', result.ShipToAddressId);
                component.set('v.billToAddressId', result.BillToAddressId);
                component.set('v.dropshipAddressId', result.DropshipAddressId);
                // dropshipAddress
                let dropshipAddress = {
                    Id: result.DropshipAddressId,
                    Name: result.DropshipAddress,
                    address: result.DropshipAddressInfo,
                }
                component.set('v.dropshipAddress', dropshipAddress);
                component.set('v.freightCost', result.FreightCost);
                component.set('v.insuranceFee', result.InsuranceFee);
                component.set('v.otherFee', result.OtherFee);
                component.set('v.dropshipType', result.DropshipType);
                component.set('v.DropShipName', result.DropShipName);
                component.set('v.DropShipAddress1', result.DropShipAddress1);
                component.set('v.DropShipPhone', result.DropShipPhone);
                component.set('v.DropShipCity', result.DropShipCity);
                component.set('v.DropShipZip', result.DropShipZip);
                component.set('v.DropShipCountry', {
                    Id: result.DropShipCountry,
                    Name: result.DropShipCountry,
                });
                // self.getDropAddressList(component);
                self.getCustomerInfo(component);
                // self.checkCollectiveCustomer(component, result.CustomerId);
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // customerPO 重复校验
    checkPO: function(component) {
        if (!component.get('v.customerPO')) {
            return;
        }
        let customerObj = component.get('v.customerObj');
        var action = component.get('c.checkDuplicatePO');
        action.setParams({
            CustomerPo: component.get('v.customerPO'),
            isProtal: false,
            CustomerId: customerObj.Id,
            purchaseOrderId: component.get('v.purchaseOrderId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'customerPO 重复校验=========');
            if (state === 'SUCCESS' ) {
                var result = response.getReturnValue();
                console.log(result, 'customer提示信息 result=========');
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Warning",
                    "message": result,
                    "type": "warning",
                    "duration": "dismissible"
                }).fire();
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            };
        });
        $A.enqueueAction(action);
    },
    // 判断是否Collective Customer
    checkCollectiveCustomer: function(component, custmerId) {
        var action = component.get('c.isCollectiveCustomer');
        action.setParams({
            accId: custmerId
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === 'SUCCESS' ) {
                var result = JSON.parse(response.getReturnValue());
                console.log(result, result.isCollectiveCustomer, '判断是否Collective Customer=========');
                component.set('v.isCollectiveCustomer', result.isCollectiveCustomer);
                // TODO: delete
                // component.set('v.isCollectiveCustomer', true);
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            };
        });
        $A.enqueueAction(action);
    },
    // 必填校验
    getValidation : function (component, type) {
        let self = this;
        if (type == 'detail') {
            let valid1 = self.getElementRequiredError(component, 'dropShipName');
            let valid2 = self.getElementRequiredError(component, 'dropshipStreetAndStreetNo');
            let valid3 = self.getElementRequiredError(component, 'dropShipZip');
            let valid4 = self.getElementRequiredError(component, 'dropShipCity');
            return (valid1 && valid2 && valid3 && valid4);
        } else {
            let valid1 = self.getElementRequiredError(component, 'dropshipType');
            return valid1;
        }
    },
    // 校验错误提示信息
    getElementRequiredError : function (component, ele) {
        var element = component.find(ele);
        var requiredText = component.find(ele + '-error-required');
        var val = element.get('v.value');
        var valid = !!val;
        console.log(valid, 'dropshipType valid-------------');
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }

        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;
    },
    // 修改po
    updatePoInfo: function (component) {
        var action = component.get("c.UpdatePoInfo");
        let billToAddressId = component.get("v.billToAddressId");
        let shipToAddressId = component.get("v.shipToAddressId");
        let dropshipAddressId = component.get("v.dropshipAddressId");
        let DropShipCountry = component.get("v.DropShipCountry");
        let paramsObj = {
            CustomerId: component.get('v.customerId'),
            BillAddressId: billToAddressId || null,
            ShipAddressId: shipToAddressId || null,
            DropShipType: component.get('v.dropshipType'),
            DropShipAddressId: dropshipAddressId || null,
            PurchaseOrderId: component.get('v.purchaseOrderId'),
            IsProtal: false,
            DropShipName: component.get('v.DropShipName'),
            DropShipAddress1: component.get('v.DropShipAddress1'),
            DropShipAddress2: component.get('v.DropShipAddress2'),
            DropShipPhone: component.get('v.DropShipPhone'),
            DropShipCountry: DropShipCountry ? DropShipCountry.Id : '',
            DropShipCity: component.get('v.DropShipCity'),
            DropShipZip: component.get('v.DropShipZip'),
            DropShipState: component.get('v.DropShipState'),
            FreightCost: component.get('v.freightCost') || 0,
            InsuranceFee: component.get('v.insuranceFee') || 0,
            OtherFee: component.get('v.otherFee') || 0,
            isFirst: true
        }
        action.setParams({
            StringRequestBody: JSON.stringify(paramsObj),
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, '下一步--------------');
            if (state === "SUCCESS") {
                    component.set("v.currentStep", component.get("v.currentStep") + 1);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action); 
    },
    getDropAddressList: function (component) {
        var action = component.get("c.QueryAddressByName");
        action.setParams({
            AddressName: '',
            customerId: component.get('v.customerId'),
            recordTypeName: 'Dropship Shipping Address',
            isPortal: false,
            contactId: component.get('v.contactId')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                let dropList = JSON.parse(result);
                if (dropList && dropList.length) {
                    console.log(JSON.stringify(dropList), 'dropList===============');
                    let primaryList = [];
                    dropList.forEach((item)=>{
                        if (item.address.Primary) {
                            primaryList.push(item)
                        }
                    })
                    console.log(JSON.stringify(primaryList), 'primaryList===============');
                    if (dropList.length == 1) {
                         // 1.当仅有一条数据时，直接自动带入，不用考虑primary
                        component.set('v.defaultDropAddressInfo', {
                            Id: dropList[0].Id,
                            Name: dropList[0].Name,
                            address: dropList[0].address,
                        })
                    } else {
                        // 2.当有多条数据，但仅有一条数据primary为true时，自动带入
                        if (primaryList.length == 1) {
                            component.set('v.defaultDropAddressInfo', {
                                Id: primaryList[0].Id,
                                Name: primaryList[0].Name,
                                address: primaryList[0].address,
                            })
                        } else {
                            component.set('v.defaultDropAddressInfo', {
                                Id: '',
                                Name: ''
                            })
                        }
                    }
                } else {
                    component.set('v.defaultDropAddressInfo', {
                        Id: '',
                        Name: ''
                    })
                }
                console.log(JSON.stringify(component.get('v.defaultDropAddressInfo')), 'defaultDropAddressInfo---------------------');
                // 判断是否立即带入
                let showDropAddress = component.get('v.showDropAddress');
                let dropshipType = component.get('v.dropshipType');
                if (showDropAddress && dropshipType != 'End Consumer') {
                    let dropshipAddress = component.get('v.defaultDropAddressInfo');
                    const dropshipAddressElement = component.find('dropshipAddress');
                    const dropshipAddressRequiredText = component.find('dropshipAddress-error-required');
                    if (dropshipAddress.Id) {
                        component.set('v.dropshipAddressId', dropshipAddress.Id);
                        $A.util.removeClass(dropshipAddressElement, 'field-error');
                        $A.util.addClass(dropshipAddressRequiredText, 'slds-hide');
                        component.set('v.dropshipAddress', dropshipAddress);
                        component.set('v.dropshipAddressId', dropshipAddress.Id);
                    }
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
});