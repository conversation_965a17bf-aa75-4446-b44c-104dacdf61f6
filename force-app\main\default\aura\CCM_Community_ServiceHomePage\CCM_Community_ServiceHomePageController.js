/**
 * Created by gluo006 on 9/24/2019.
 */
({
    init:function(component, event, helper){
        component.set('v.isDE', $A.get("$Label.c.CCM_Translate"));
        let profileName = $A.get("$SObjectType.CurrentUser.Profile");
        console.log(profileName, 'profileName--------------');

        var userInfo = $A.get("$SObjectType.CurrentUser");
        console.log(userInfo, 'userInfo========');
        console.log('window.location.href === ',window.location.href);
        if(userInfo.Id){
            var userId = userInfo.Id.substr(0,18);
            component.set('v.userId', userId);
        }
        // 获取warranty history表格数据
        const warrantyColumns= [
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '140px',
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:money",
                            alternativeText:"New Claim",
                            class: "${isShowClaimBtn}",
                            onclick: component.getReference("c.toNewClaimByWarranty")
                        }
                    }
                ]
            },
            {label: $A.get("$Label.c.CCM_UserType"), fieldName: 'userTypeLabel'},
            {label: $A.get("$Label.c.CCM_CustomerName"), fieldName:'company'},
            {label: $A.get("$Label.c.CCM_WarrantyNumber"), fieldName:'name'},
            // {label: $A.get("$Label.c.CCM_KitModel"), fieldName: 'masterProduct'},
            {label: $A.get("$Label.c.CCM_ModelNumber"), fieldName: 'modelNumber'},
            {label: $A.get("$Label.c.CCM_SerialNumber"), fieldName: 'serialNumber'},
            {label: $A.get("$Label.c.CCM_WarrantyStatus"), fieldName: 'warrantyStatusLabel'},
            {label: $A.get("$Label.c.CCM_ExpirationDate"), fieldName: 'expirationDate'},
            {label: $A.get("$Label.c.CCM_CreatedDate"), fieldName: 'createdDate'},
        ];
        component.set('v.warrantyColumns', warrantyColumns);
        // 获取user history表格数据
        const userColumns= [
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '80px',
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:preview",
                            alternativeText:"View",
                            class: "${isShowViewBtn}",
                            onclick: component.getReference('c.viewUserDetail')
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:edit",
                            alternativeText:"Edit",
                            class: "${isShowEditBtn}",
                            onclick: component.getReference("c.editUserDetail")
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:delete",
                            alternativeText:"Delete",
                            class: "${isShowDeleteBtn}",
                            onclick: component.getReference("c.deleteUserDetail")
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:product_warranty_term",
                            alternativeText:"Product Registration",
                            class: "${isShowProductBtn}",
                            onclick: component.getReference("c.toProductRegistration")
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:money",
                            alternativeText:"New Claim",
                            class: "${isShowClaimBtn}",
                            onclick: component.getReference("c.toNewClaim")
                        }
                    }
                ]
            },
            {label: $A.get("$Label.c.CCM_UserType"), fieldName: 'userTypeLabel'},
            {label: $A.get("$Label.c.CCM_CompanyName"), fieldName:'company'},
            {label: $A.get("$Label.c.CCM_Name"), fieldName:'name'},
            {label: $A.get("$Label.c.CCM_Email"), fieldName: 'email'},
            {label: $A.get("$Label.c.CCM_ApplyDate"), fieldName: 'applyDate'},
            {label: $A.get("$Label.c.CCM_ConfirmDate"), fieldName: 'confirmDate'},
            {label: $A.get("$Label.c.CCM_DueDate"), fieldName: 'dueDate'},
            {label: $A.get("$Label.c.CCM_Status"), fieldName: 'status'},
        ];
        component.set('v.userColumns',userColumns);
        // 获取Claim history表格数据
        const claimColumns= [
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '80px',
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:preview",
                            alternativeText:"View",
                            class: "${notDraft}",
                            onclick: component.getReference('c.viewClaimDetail')
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:edit",
                            alternativeText:"Edit",
                            class: "${isDraft}",
                            onclick: component.getReference("c.editClaimDetail")
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${id}",
                            variant:"bare",
                            iconName:"utility:delete",
                            alternativeText:"Delete",
                            class: "${isDraft}",
                            onclick: component.getReference("c.deleteClaimDetail")
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rowData}",
                            variant:"bare",
                            iconName:"utility:copy",
                            alternativeText:"Copy Claim",
                            class: "${notDraft}",
                            onclick: component.getReference("c.copyClaim")
                        }
                    }
                ]
            },
            {label: $A.get("$Label.c.CCM_CustomerName"), fieldName:'companyName'},
            {label: $A.get("$Label.c.CCM_ClaimNumber"), fieldName: 'name'},
            {label: $A.get("$Label.c.CCM_CustomerClaimReferenceNumber"), fieldName: 'claimReferenceNumber'},
            // {label: $A.get("$Label.c.CCM_Status"), fieldName: 'status'}
            {label: $A.get("$Label.c.CCM_Status"),
                children: [
                    {
                        type: "lightning:formattedText",
                        attributes:{
                            value: "${statusLabel}"
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${rejectComments}",
                            variant:"bare",
                            iconName:"utility:info",
                            alternativeText:"Reject Comments",
                            class: "${isReject}",
                            onclick: component.getReference("c.showRejectComments")
                        }
                    }
                ]
            },
            // {label: 'Payment Status', fieldName: 'paymentStatus'},
            {label: $A.get("$Label.c.CCM_ModelNumber"), fieldName: 'modelNumber'},
            {label: $A.get("$Label.c.CCM_SerialNumber"), fieldName: 'serialNumber'},
            {label: $A.get("$Label.c.CCM_LaborCost"), fieldName: 'laborCost'},
            {label: $A.get("$Label.c.CCM_MaterialCost"), fieldName: 'materialCost'},
            {label: $A.get("$Label.c.CCM_Total"), fieldName: 'total'},
            {
                label: $A.get("$Label.c.CCM_Document"),
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:button",
                        attributes:{
                            label: "${creditMemoOrWarrantyOrder}",
                            value: "${rowData}",
                            variant:"bare",
                            class: "company-btn",
                            onclick: component.getReference("c.toCreditMemoOrWarrantyOrder")
                        }
                    }
                ]
            }
            // {
            //     label: $A.get("$Label.c.CCM_CreditMemo"),
            //     tdStyle: 'text-align: center',
            //     children:[
            //         {
            //             type: "lightning:button",
            //             attributes:{
            //                 label: "${creditMemo}",
            //                 value: "${creditMemoId}",
            //                 variant:"bare",
            //                 class: "company-btn",
            //                 onclick: component.getReference("c.toCreditMemo")
            //             }
            //         }
            //     ]
            // },
            // {
            //     label: $A.get("$Label.c.CCM_WarrantyOrder"),
            //     tdStyle: 'text-align: center',
            //     children:[
            //         {
            //             type: "lightning:button",
            //             attributes:{
            //                 label: "${warrantyOrder}",
            //                 value: "${rowData}",
            //                 variant:"bare",
            //                 class: "company-btn",
            //                 onclick: component.getReference("c.toWarrantyOrder")
            //             }
            //         }
            //     ]
            // },
        ];
        component.set('v.claimColumns', claimColumns);
        helper.getUserTableData(component,event, helper);
        helper.getCustomerInformation(component);
    },
    doView: function(component, event){
        var id = event.getSource().get('v.value');
        var url = '/s/warrantyclaim?recordId=' + id + '&view=true';
        window.open(url);
    },
    doEdit: function(component, event){
        var id = event.getSource().get('v.value');
        var url = '/s/warrantyclaim?recordId=' + id + '&edit=true';
        window.open(url);
    },
    doDelete: function(component, event, helper) {
        var claimId = event.getSource().get('v.value');
        helper.doDeleteClaim(component, claimId);
    },
    // tab切换
    handleChange: function(component, event, helper){
        var selected = component.get("v.tabId");
        component.set('v.tabId', selected);
        // 重置分页
        component.set('v.pageNumber', 1);
        component.set('v.pageCount', 10);
        component.set('v.totalRecords', 0);
        switch (selected) {
            case 'User':
                helper.getUserTableData(component);
                break;
            case 'Warranty':
                helper.getWarrantyTableData(component);
                break;
            case 'Claim':
                helper.getClaimTableData(component);
                break;
            default:
                break;
        }
    },
    openDocument: function(component, event, helper){
        var recordId = event.currentTarget.getAttribute('data-fileId');
        $A.get('e.lightning:openFiles').fire({
            recordIds: [recordId]
        });
    },
    // 分页
    pageChange : function(component, event, helper) {
        var pageNumber = event.getParam("pageNumber");
        console.log('PageNumber================');
        if(component.get('v.tabId') == 'Claim'){
            component.set("v.pageNumber",pageNumber);
            helper.getClaimTableData(component);
        }
        if(component.get('v.tabId') == 'User'){
            component.set("v.pageNumber",pageNumber);
            helper.getUserTableData(component,event, helper);
        }
        if(component.get('v.tabId') == 'Warranty'){
            component.set("v.pageNumber",pageNumber);
            helper.getWarrantyTableData(component,event, helper);
        }
        event.stopPropagation();
    },
    // 分页
    pageCountChange : function(component, event, helper){
        var pageCount = event.getParam("pageCount");
        console.log('pageCount================');
        if(component.get('v.tabId') == 'Claim'){
            component.set("v.pageCount",pageCount);
            helper.getClaimTableData(component);
        }
        if(component.get('v.tabId') == 'User'){
            component.set("v.pageCount",pageCount);
            helper.getUserTableData(component,event, helper);
        }
        if(component.get('v.tabId') == 'Warranty'){
            component.set("v.pageCount",pageCount);
            helper.getWarrantyTableData(component,event, helper);
        }
        component.set("v.pageCount",pageCount);
        component.set("v.pageNumber", 1);
        // helper.getInit(component,event, helper);
        event.stopPropagation();
    },

    // 跳转到user history detail
    viewUserDetail : function(component, event){
        const rowData = event.getSource().get('v.value');
        console.log('rowData === ',rowData);
        const url = '/s/user-history-detail?recordId=' + rowData.id + '&userType=' + rowData.userType + '&actionType=view';
        console.log('url === ',url);
        window.open(url,'_self'); 
    },
    editUserDetail : function(component, event){
        const rowData = event.getSource().get('v.value');
        const url = '/s/user-history-detail?recordId=' + rowData.id + '&userType=' + rowData.userType + '&actionType=edit';
        window.open(url,'_self'); 
    },
    // 删除user history detail
    deleteUserDetail : function(component, event, helper){
        const rowData = event.getSource().get('v.value');
        console.log(rowData, 'deleteUserDetail======');
        helper.deleteUserDetail(component,rowData.id, helper);
    },
    // 跳转claim detail
    viewClaimDetail : function(component, event){
        let rowData = event.getSource().get('v.value');
        let url = window.location.origin + '/s/warranty-claim-create?0.claimId=' + rowData.claimId+ '&0.modelNumber=' + rowData.modelNumber + '&0.customerId=' + rowData.customerId;
        window.open(url, '_self');
    },
    // 编辑Claim
    editClaimDetail : function(component, event){
        let rowData = event.getSource().get('v.value');
        let url = window.location.origin + '/s/warranty-claim-create?0.claimId=' + rowData.claimId + '&0.type=create&0.modelNumber=' + rowData.modelNumber + '&0.customerId=' + rowData.customerId;
        window.open(url, '_self');
    },
    // 删除claim 数据
    deleteClaimDetail : function(component, event, helper){
        let id = event.getSource().get('v.value');
        console.log(id, '删除claim 数据-------------');
        helper.deleteClaim(component, id);
    },
    // direct to credit memo or warranty order
    toCreditMemoOrWarrantyOrder: function(component, event, helper) {
        let rowData = event.getSource().get('v.value');
        if(rowData.type === 'creditmemo') {
            let creditMemoId = rowData.creditMemoId;
            let isDE = component.get('v.isDE');
            let url = '';
            if (isDE == 'DE') {
                url = '/apex/WarrantyCreditMemoPDFViewDE?recordId=' + creditMemoId;
            } else {
                url = '/apex/WarrantyCreditMemoPDFView?recordId=' + creditMemoId;
            }
            window.open(url);
        }
        else if(rowData.type === 'warrantyorder') {
            let url = window.location.origin + '/s/warranty-order?0.recordId=' + rowData.warrantyOrderId + '&0.accId=' + rowData.customerId;
            window.open(url, '_self');
        }
        let url = window.location.origin + '/s/warranty-order?0.recordId=' + rowData.warrantyOrderId + '&0.accId=' + rowData.customerId;
        window.open(url, '_self');
    },
    // 跳转credit Memo
    // toCreditMemo: function(component, event, helper){
    //     var Id = event.getSource().get('v.value');
    //     let isDE = component.get('v.isDE');
    //     let url = '';
    //     if (isDE == 'DE') {
    //         url = '/apex/WarrantyCreditMemoPDFViewDE?recordId=' + Id;
    //     } else {
    //         url = '/apex/WarrantyCreditMemoPDFView?recordId=' + Id;
    //     }
    //     window.open(url);
    // },
    // 跳转
    // toWarrantyOrder : function(component, event){
    //     let rowData = event.getSource().get('v.value');
    //     let url = window.location.origin + '/s/warranty-order?0.recordId=' + rowData.warrantyOrderId + '&0.accId=' + rowData.customerId;
    //     window.open(url, '_self');
    // },
    // 带consumer信息直接跳转注册页面
    toProductRegistration : function(component, event){
        let rowData = event.getSource().get('v.value');
        console.log(JSON.stringify(rowData), 'consumer信息-------------');
            if (rowData.userType == 'Residential') {
            let url = window.location.origin + '/s/productregistration?registrationType=residentialUser&id=' + rowData.id;
        window.open(url, '_blank');
        } else {
            let url = window.location.origin + '/s/productregistration?registrationType=commercialUser&id=' + rowData.id + '&0.accountEmail=' + rowData.email;
            window.open(url, '_blank');
        }
    },
    // 带consumer信息直接跳转new claim
    toNewClaim : function(component, event){
        let rowData = event.getSource().get('v.value');
        let customerId = component.get('v.customerId');
        let url = window.location.origin + '/s/warranty-claim-create?0.customerId=' + customerId +'&0.type=create&0.userType=' + rowData.userType + '&0.email=' + rowData.email;
        window.open(url, '_blank');
    },
    // 带consumer信息直接跳转new claim
    toNewClaimByWarranty : function(component, event){
        let rowData = event.getSource().get('v.value');
        let customerId = component.get('v.customerId');
        let url = window.location.origin + '/s/warranty-claim-create?0.customerId=' + customerId +'&0.type=create&0.modelNumber=' + rowData.modelNumber + '&0.modelNumberId=' + rowData.modelNumberId + '&0.serialNumber=' + rowData.serialNumber + '&0.userType=' + rowData.userType + '&0.email=' + rowData.email;
        window.open(url, '_blank');
    },
    // copy claim
    copyClaim : function(component, event){
        let rowData = event.getSource().get('v.value');
        let customerId = component.get('v.customerId');
        let url = window.location.origin + '/s/warranty-claim-create?0.customerId=' + customerId + '&0.claimId=' + rowData.claimId + '&0.type=create&0.modelNumber=' + rowData.modelNumber + '&0.isCopy=true&0.serialNumber=' + rowData.serialNumber;
        window.open(url, '_blank');
    },
    // 列表查询
    handleSearch : function(component, event, helper){
        var selected = component.get("v.tabId");
        // 重置分页
        component.set('v.pageNumber', 1);
        component.set('v.pageCount', 10);
        component.set('v.totalRecords', 0);
        switch (selected) {
            case 'User':
                helper.getUserTableData(component);
                break;
            case 'Warranty':
                helper.getWarrantyTableData(component);
                break;
            case 'Claim':
                helper.getClaimTableData(component);
                break;
            default:
                break;
        }
    },
    // 列表重置
    doReset : function(component, event, helper){
        var selected = component.get("v.tabId");
        // 重置分页
        component.set('v.pageNumber', 1);
        component.set('v.pageCount', 10);
        component.set('v.totalRecords', 0);
        component.set('v.companyName', '');
        component.set('v.name', '');
        component.set('v.emailAddress', '');
        component.set('v.modelNumberInfo', {});
        component.set('v.serialNumber', '');
        switch (selected) {
            case 'User':
                helper.getUserTableData(component);
                break;
            case 'Warranty':
                helper.getWarrantyTableData(component);
                break;
            case 'Claim':
                helper.getClaimTableData(component);
                break;
            default:
                break;
        }
    },

    showRejectComments: function(component, event, helper) {
        let comments = event.getSource().get('v.value');
        component.set("v.isShowRejectComments", false);
        component.set("v.rejectedComments", comments);
        
        setTimeout(()=>{
            let box = event.target.getBoundingClientRect();
            let scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            let clientTop = document.documentElement.clientTop || document.body.clientTop;
            component.set("v.isShowRejectComments", true);
            let elementHeight = document.querySelector('#help').clientHeight;
            component.set("v.tooltipTop", box.top + scrollTop - clientTop - elementHeight/2 - 60);
            component.set("v.tooltipLeft", box.left - 50);
        }, 100);
    },
    closeTooltip: function(component, event) {
        component.set("v.isShowRejectComments", false);
    },
})