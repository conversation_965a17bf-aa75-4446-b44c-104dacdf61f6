<apex:page standardController="Warranty_Claim__c" extensions="CCM_WarrantyClaimDetailReceiptPageCtl">
    <table cellpadding="2" style="width:100%; margin-left:-20px; border-collapse: collapse; font-family: sans-serif; " >
        <tr>
            <apex:outputPanel layout="block">
                <td style="width:16%;">
                    <apex:outputLabel value="{!$ObjectType.Warranty_Claim__c.Fields.Receipt_Link__c.label}" /> 
                </td>
                <td>
                    <apex:form >
                        <a style = "word-break:break-all;color:rgb(1, 118, 211);" onclick="linkClick(); return false;" ><apex:OutPutText value="{!Warranty_Claim__c.Receipt_Link__c}" /></a>
                        <apex:actionfunction name="doPreview" action="{!awsReceiptPreview}" ></apex:actionFunction>
                    </apex:form>
                </td>
            </apex:outputPanel>
        </tr>
    </table>
    <script type='text/javascript'>
        function linkClick() {
            doPreview();
        }
    </script>
</apex:page>