/**
 * <AUTHOR>
 * @date 2025-04-27
 * @description Auto deactive and generate new account in info portal when email change
 */
public without sharing class CCM_UserEmailUpdateHandler implements Triggers.Handler {

    public static Boolean isRun = true;

    private List<User> newRecords = (List<User>)Trigger.new;
    private Map<Id, User> oldMap = (Map<Id, User>)Trigger.oldMap;

    public void handle() {
        if(!isRun) {
            return;
        }

        List<User> emailUpdateRecords = new List<User>();
        for(User newRecord : this.newRecords) {
            if(String.isNotBlank(newRecord.InfoPortal_UserId__c)) {
                if(newRecord.Email != oldMap.get(newRecord.Id).Email) {
                    emailUpdateRecords.add(newRecord);
                }
            }
        }

        if(!emailUpdateRecords.isEmpty()) {
            for(User u : emailUpdateRecords) {
                CCM_InfoPortalAutoLoginUtil.deleteUserInInfoPortalFuture(u.Id);
                CCM_InfoPortalAutoLoginUtil.createNewUserInInfoPortalFuture(u.Id);
            }
        }
    }
}