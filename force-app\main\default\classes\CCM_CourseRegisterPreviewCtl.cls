/**
 * 注册课程的详细信息
 * Honey
 * 20230920
 */
public without sharing class CCM_CourseRegisterPreviewCtl {
    public final static String strRichTextImageUrl = '/servlet/rtaImage';
    public final static String strDomain = Label.CCM_File_Storage_Url;
    @AuraEnabled
    public static Map<String,Object> queryRegisterInfo(String registerId){
        system.debug('前端请求参数---->'+registerId);
        try {
            Map<String,Object> mapFeild2Value = new Map<String,Object>();
            //通过registerId查询详细信息
            Course_Register__c objCourseRegister = [
                SELECT Id,Billing_Address__c,Course_Arrangement__c,Customer__c,To<PERSON><PERSON><PERSON>(Payment_Term__c),PCS__c,Price_Pcs__c,Salesperson__c,
                Status__c,Total_Amount__c,Training_Amount__c,Training_Course__c,VAT__c,Training_Course__r.Course_Name__c,
                Training_Course__r.Course_Name__r.Name,Training_Course__r.Course_Desc__c,Course_Arrangement__r.Start_Time__c,Course_Arrangement__r.Course_Date__c,
                Course_Arrangement__r.End_Time__c,Course_Arrangement__r.Training_Location__c,CreatedById,Course_Arrangement__r.Training_Location__r.Final_Address__C,
                Billing_Address__r.Name,Billing_Address__r.Final_Address__c,Billing_Address__r.Country__c,Training_Course__r.Course_Name__r.Order_Model__c,
                Billing_Address__r.City__c,Billing_Address__r.Street_1__c,Billing_Address__r.Postal_Code__c,CurrencyIsoCode,
                Billing_Address__r.Company_name_1__c,Customer__r.Name,CreatedDate,Customer__r.AccountNUmber,Trainee__c,
                Shiping_Address__c,Shiping_Address__r.Name,Shiping_Address__r.Final_Address__c,Shiping_Address__r.Country__c,
                Shiping_Address__r.City__c,Shiping_Address__r.Street_1__c,Shiping_Address__r.Postal_Code__c,
                Shiping_Address__r.Company_name_1__c,Name,JsonInfo__c,Training_Start_Time__c

                FROM Course_Register__c WHERE Id = : registerId
            ];
            //通过registerId查询Order判断是否return
            List<Training_Order__c> lstTrainingOrder = new List<Training_Order__c>();
            lstTrainingOrder = [
                SELECT Id,Course_Register__c FROM Training_Order__c WHERE Course_Register__c = :registerId
            ];
            if(lstTrainingOrder != null && lstTrainingOrder.size()>0){
                mapFeild2Value.put('afterSyncId',lstTrainingOrder[0].Id);
            }
            /*if(objCourseRegister.Trainee__c != null){
                String trainee = objCourseRegister.Trainee__c;
                trainee = trainee.substring(1,trainee.length()-1);
                mapFeild2Value.put('lstparticipants',trainee.split(','));

            }*/

            //String trainee = objCourseRegister.Trainee__c == null ? ' ': objCourseRegister.Trainee__c;
            mapFeild2Value.put('customerId', objCourseRegister.Customer__c);
            mapFeild2Value.put('isOrder', 'false');
            mapFeild2Value.put('requestNumber', objCourseRegister.Name);
            mapFeild2Value.put('jsonInfo', objCourseRegister.JsonInfo__c);
            mapFeild2Value.put('customerName', objCourseRegister.Customer__r.Name);
            mapFeild2Value.put('customerNumber', objCourseRegister.Customer__r.AccountNumber);
            mapFeild2Value.put('salesPerson', objCourseRegister.Salesperson__c);
            mapFeild2Value.put('trainingCourseName', objCourseRegister.Training_Course__r.Course_Name__r.Name);
            mapFeild2Value.put('trainingSettingId', objCourseRegister.Training_Course__c);
            mapFeild2Value.put('trainingCourseNumber', objCourseRegister.Training_Course__r.Course_Name__r.Order_Model__c);
            mapFeild2Value.put('trainingCourseId', objCourseRegister.Training_Course__r.Course_Name__c);
            mapFeild2Value.put('arrangementId', objCourseRegister.Course_Arrangement__c);



            mapFeild2Value.put('trainingStartTime', objCourseRegister.Course_Arrangement__r.Start_Time__c ==  null ? null : DateTime.newInstance(Date.today(), objCourseRegister.Course_Arrangement__r.Start_Time__c).format('HH:mm'));
            mapFeild2Value.put('trainingEndTime', objCourseRegister.Course_Arrangement__r.End_Time__c ==  null ? null :  DateTime.newInstance(Date.today(), objCourseRegister.Course_Arrangement__r.End_Time__c).format('HH:mm'));
            mapFeild2Value.put('trainingDate', objCourseRegister.Course_Arrangement__r.Course_Date__c);
            mapFeild2Value.put('trainingLocation', objCourseRegister.Course_Arrangement__r.Training_Location__r.Final_Address__C);
            mapFeild2Value.put('paymentTerm', objCourseRegister.Payment_Term__c);
            mapFeild2Value.put('price', objCourseRegister.Price_Pcs__c);
            mapFeild2Value.put('pcs', objCourseRegister.PCS__c);
            mapFeild2Value.put('addressId', objCourseRegister.Billing_Address__c);
            mapFeild2Value.put('addressName', objCourseRegister.Billing_Address__r.Final_Address__c);
            mapFeild2Value.put('addressCountry', objCourseRegister.Billing_Address__r.Country__c);
            mapFeild2Value.put('addressCity', objCourseRegister.Billing_Address__r.City__c);
            mapFeild2Value.put('addressAddress', objCourseRegister.Billing_Address__r.Street_1__c);
            mapFeild2Value.put('addressPostalCode', objCourseRegister.Billing_Address__r.Postal_Code__c);
            mapFeild2Value.put('addressCompanyName', objCourseRegister.Billing_Address__r.Company_name_1__c);

            mapFeild2Value.put('shipAddressId', objCourseRegister.Shiping_Address__c);
            mapFeild2Value.put('shipAddressName', objCourseRegister.Shiping_Address__r.Final_Address__c);
            mapFeild2Value.put('shipAddressCountry', objCourseRegister.Shiping_Address__r.Country__c);
            mapFeild2Value.put('shipAddressCity', objCourseRegister.Shiping_Address__r.City__c);
            mapFeild2Value.put('shipAddressAddress', objCourseRegister.Shiping_Address__r.Street_1__c);
            mapFeild2Value.put('shipAddressPostalCode', objCourseRegister.Shiping_Address__r.Postal_Code__c);
            mapFeild2Value.put('shipAddressCompanyName', objCourseRegister.Shiping_Address__r.Company_name_1__c);

            mapFeild2Value.put('currencyCode', objCourseRegister.CurrencyIsoCode);
            mapFeild2Value.put('orderDate', objCourseRegister.CreatedDate.Date());
            mapFeild2Value.put('status', objCourseRegister.Status__c);
            mapFeild2Value.put('vat', objCourseRegister.VAT__c);
            mapFeild2Value.put('totalAmount', objCourseRegister.Total_Amount__c);
            mapFeild2Value.put('trainingAmount', objCourseRegister.Training_Amount__c);
            //默认权限都为false
            mapFeild2Value.put('isApproval', false);
            //校验是否具有提交审批的权限
            mapFeild2Value.put('isSubmit', false);
            //校验是否具有修改权限
            mapFeild2Value.put('isEdit', false);
            //校验是否具有确认或者拒绝权限
            mapFeild2Value.put('isComfirm', false);
            //默认isCancel为false
            mapFeild2Value.put('isCancel', false);
            //开始日期前四天可以有取消按钮
            Datetime now =  Datetime.now();
            //获取开始日期的前四天
            Datetime startDate = objCourseRegister.Training_Start_Time__c.addDays(-4);
            //当前时间小于开始日期的前四天。有删除按钮

            if((now <=  startDate && ( objCourseRegister.status__c == 'Reserved')) ||objCourseRegister.status__c == 'Submitted'  ){
                mapFeild2Value.put('isCancel', true);
            }

            //根据当前user的Profile以及状态判断是否可以审核
            Set<String> setApprovalProfile = new Set<String>(Label.Service_Training_Approval_Profile.split(','));
            User objUser = [
                SELECT Id,Name,Profile.Name,UserType FROM User WHERE Id = :UserInfo.getUserId()
            ];
            String strDesc = objCourseRegister.Training_Course__r.Course_Desc__c;
            if(objUser.UserType.contains('Partner')){
                //表示是protal端
                if (String.isNotBlank(strDesc) && strDesc.contains(strRichTextImageUrl)) {
                    strDesc = strDesc.replace(strRichTextImageUrl, Label.CCM_File_Storage_Url + strRichTextImageUrl);
                    strDesc = strDesc.replace('amp;','');
                    strDesc = strDesc.replace('/;','');

                }
            }
            mapFeild2Value.put('trainingDescription', strDesc);
            system.debug('objUser.Profile.Name-->'+objUser.Profile.Name);
            if(setApprovalProfile.contains(objUser.Profile.Name) && objCourseRegister.status__c == 'Submitted'){
                //校验是否具有审批权限（可以审批或拒绝）
                mapFeild2Value.put('isApproval', true);
            }
            if(setApprovalProfile.contains(objUser.Profile.Name) && (now <=  startDate || objCourseRegister.status__c == 'Draft'  ||objCourseRegister.status__c == 'Submitted') ){
                //AfterSales和Inside Sales可以修改所有状态的register 数据
                mapFeild2Value.put('isEdit', true);
                if(objCourseRegister.status__c == 'Draft' || objCourseRegister.status__c == 'Cancelled'){
                    mapFeild2Value.put('isSubmit', true);
                }
            }
            //校验是否具有提交审批的权限
            if(UserInfo.getUserId() == objCourseRegister.CreatedById && objCourseRegister.status__c == 'Draft'){
                //本人并且为草稿状态才能提交审批
                mapFeild2Value.put('isSubmit', true);
                //本人并且状态为草该可以修改
                mapFeild2Value.put('isEdit', true);
            }

            if(objCourseRegister.status__c == 'Reserved' && Datetime.now()  >=  objCourseRegister.Course_Arrangement__r.Course_Date__c.addDays(-7) ){
                //本人，并且状态为审核通过。并且当前时间在开课前一周内 具有confirm权限
                mapFeild2Value.put('isComfirm', true);
            }
            //根据registerId查询行数据
            List<Course_Register_Item__c> lstCourseItem = new List<Course_Register_Item__c>();
            lstCourseItem = [
                SELECT Course_Arrangement__c,Course_Register__c,Course_Register__r.Training_Course__r.Course_Name__r.Name,Trainee__c,
                Remark__c,tele_SMS__c,email__c,
                Course_Register__r.Training_Course__r.Course_Desc__c,Course_Register__r.Training_Course__r.Course_Name__r.Order_Model__c,Order_Date__c,Course_Arrangement__r.Price__c
                FROM Course_Register_Item__c WHERE Course_Register__c = :registerId
            ];
            List<Map<String,Object>> lstmapItemInfo = new List<Map<String,Object>>();
            List<Map<String,String>> lstMapParti = new List<Map<String,String>>();
            for(Course_Register_Item__c  objItem : lstCourseItem){
                Map<String,Object> mapItemInfo = new Map<String,Object>();
                Map<String,String> mappartinfo = new Map<String,String>();
                mapItemInfo.put('productName', objItem.Course_Register__r.Training_Course__r.Course_Name__r.Name);
                mapItemInfo.put('trainingOrderItemid', objItem.Id);
                mapItemInfo.put('productDescription', objItem.Course_Register__r.Training_Course__r.Course_Desc__c);
                mapItemInfo.put('productModel', objItem.Course_Register__r.Training_Course__r.Course_Name__r.Order_Model__c);
                mapItemInfo.put('uom', 'EA');
                mapItemInfo.put('orderDate', objItem.Order_Date__c);
                mapItemInfo.put('orderQty', 1);

                // Get customer-specific price if available, otherwise use default arrangement price
                Decimal customerPrice = CCM_CourseRegisterCtl.getCustomerSpecificPrice(objItem.Course_Arrangement__c, objCourseRegister.Customer__c);
                Decimal finalPrice = customerPrice != null ? customerPrice : objItem.Course_Arrangement__r.Price__c;
                mapItemInfo.put('listPrice', finalPrice);

                mapItemInfo.put('trainee', objItem.Trainee__c);
                mapItemInfo.put('remark', objItem.Remark__c);
                mapItemInfo.put('sms', objItem.tele_SMS__c);
                mapItemInfo.put('email', objItem.email__c);

                mappartinfo.put('trainee', objItem.Trainee__c == null ? '' : objItem.Trainee__c);
                mappartinfo.put('remark', objItem.Remark__c == null ? '' : objItem.Remark__c);
                mappartinfo.put('sms', objItem.tele_SMS__c == null ? '' : objItem.tele_SMS__c);
                mappartinfo.put('email', objItem.email__c == null ? '' : objItem.email__c);
                lstmapItemInfo.add(mapItemInfo);
                lstMapParti.add(mappartinfo);

            }
            mapFeild2Value.put('mapItemInfo', lstmapItemInfo);
            mapFeild2Value.put('lstparticipants', lstMapParti);
            //查询审批历史
            List<ProcessInstanceStep> lstProcessInstance = new List<ProcessInstanceStep>();
            lstProcessInstance = [
                SELECT Actor.Name,Actor.FirstName,Actor.LastName, CreatedDate, p.ProcessInstanceId, p.ProcessInstance.TargetObjectId, p.StepStatus
                FROM ProcessInstanceStep p WHERE ProcessInstance.TargetObjectId = :registerId order by CreatedDate desc
            ];
            //List<Map<String,Object>> lstMapApprovalInfo = new List<Map<String,Object>>();
            List<Map<String,Object>> lstMapApprovalInfo = new List<Map<String,Object>>();
            if(lstProcessInstance != null && lstProcessInstance.size()>0){
                for(ProcessInstanceStep objProcessInstance : lstProcessInstance){
                    Map<String,Object> mapApprovalInfo = new Map<String,Object>();
                    if(objProcessInstance.StepStatus == 'Started'){
                        mapApprovalInfo.put('submitUserName', (objProcessInstance.Actor.FirstName == null ? '' : objProcessInstance.Actor.FirstName) + ' ' +
                        ( objProcessInstance.Actor.LastName == null  ? '' : objProcessInstance.Actor.LastName) );
                        mapApprovalInfo.put('submitDate',String.valueOf(objProcessInstance.CreatedDate) );

                    }else {
                        mapApprovalInfo.put('Approver',  (objProcessInstance.Actor.FirstName == null ? '' : objProcessInstance.Actor.FirstName) + ' ' +
                        ( objProcessInstance.Actor.LastName == null  ? '' : objProcessInstance.Actor.LastName) );
                        mapApprovalInfo.put('ApprovalDate', String.valueOf(objProcessInstance.CreatedDate));
                        mapApprovalInfo.put('ApprovalStatus', objProcessInstance.StepStatus);

                    }
                    if(!mapApprovalInfo.containsKey('ApprovalStatus')){
                        mapApprovalInfo.put('ApprovalStatus', 'Submitted');
                    }
                    lstMapApprovalInfo.add(mapApprovalInfo);

                }
            }
            mapFeild2Value.put('approvalInfo', lstMapApprovalInfo);

            //通过purchaseId查询Attachment得List
            List<Purchase_Order_Attachment__c> lstPurchaseAttachments = [
                SELECT Id,File_Id__c,File_Name__c,File_Type__c,Purchase_Order__c,File_Date__c FROM Purchase_Order_Attachment__c
                WHERE Course_Register__c = :registerId
            ];
            List<Map<String,String>> lstmapAccachmentInfo = new List<Map<String,String>>();
            for(Purchase_Order_Attachment__c objPurchase : lstPurchaseAttachments){
                Map<String,String> mapAttachmentInfo = new Map<String,String>();
                mapAttachmentInfo.put('fileId', objPurchase.File_Id__c);
                mapAttachmentInfo.put('fileName', objPurchase.File_Name__c);
                mapAttachmentInfo.put('fileType', objPurchase.File_Type__c);
                mapAttachmentInfo.put('fileDate', objPurchase.File_Date__c == null ? '' : String.valueOf(objPurchase.File_Date__c));
                lstmapAccachmentInfo.add(mapAttachmentInfo);
            }

            mapFeild2Value.put('lstmapAccachmentInfo', lstmapAccachmentInfo);




            return mapFeild2Value;



        } catch (Exception e) {
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }

    /**
     * Author : Honey
     * Date 2023/07/31
     * Description: 临时保存文件。供前端预览文件
     */
    @AuraEnabled
    public static string uploadFileMidel( String content,String uploadFileName,String fileName){
        Map<String, String> result = new Map<String, String>();

        try {
            ContentVersion conVer = new ContentVersion();
            conVer.ContentLocation = 'S'; // S specify this document is in SF, use E for external files
            conVer.PathOnClient = uploadFileName; // The files name, extension is very important here which will help the file in preview.
            conVer.Title = FileName + String.valueOf(Datetime.now()); // Display name of the files
            conVer.VersionData = EncodingUtil.base64Decode(content); // converting your binary string to Blog
            insert conVer;
            ContentVersion objContentVersion = [
                SELECT Id,ContentDocumentId FROM ContentVersion WHERE Id = :conVer.Id
            ];
            system.debug('objContentVersion--->'+objContentVersion.ContentDocumentId);
            result.put('Status', 'Success');
            result.put('Message', '');
            result.put('ContentId', objContentVersion.ContentDocumentId);
            return JSON.serialize(result);

        } catch (Exception e) {
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            result.put('Status', 'Error');
            result.put('Message', e.getMessage());
            result.put('ContentId', '');
            return JSON.serialize(result);
        }
    }

    @AuraEnabled
    public static void uploadFile(String recordId,List<CCM_PurchaseOrderPreview.uploadFileInfo> lstuploadFileInfo){
        system.debug('lstuploadFileInfo--->'+lstuploadFileInfo);
        List<Purchase_Order_Attachment__c> lstdeleteAttachment = [
            SELECT Id,Course_Register__c,File_Id__c FROM Purchase_Order_Attachment__c WHERE Course_Register__c = :recordId
        ];
        Set<String> lstContentDocumentId = new Set<String>();
        Set<String> setInsertDocument = new Set<String>();


        delete lstdeleteAttachment;
        List<Purchase_Order_Attachment__c> lstinsertPurchaseOrder = new List<Purchase_Order_Attachment__c>();
        for(CCM_PurchaseOrderPreview.uploadFileInfo objFileInfo : lstuploadFileInfo){
            Purchase_Order_Attachment__c objPurchaseAttachment = new Purchase_Order_Attachment__c();
            objPurchaseAttachment.Course_Register__c = recordId;
            objPurchaseAttachment.File_Date__c = objFileInfo.fileDate;
            setInsertDocument.add(objFileInfo.contentId);
            objPurchaseAttachment.File_Id__c = objFileInfo.contentId;
            objPurchaseAttachment.File_Name__c = objFileInfo.fileName;
            objPurchaseAttachment.File_Type__c = objFileInfo.fileType;
            lstinsertPurchaseOrder.add(objPurchaseAttachment);

        }
        for(Purchase_Order_Attachment__c objAttachment : lstdeleteAttachment){
            if(!setInsertDocument.contains(objAttachment.File_Id__c)){
                lstContentDocumentId.add(objAttachment.File_Id__c);
            }
        }
        //根据AttachmentId删除文件
        List<ContentDocument> lstContent = [SELECT  Id FROM ContentDocument  WHERE Id IN :lstContentDocumentId];
        delete lstContent;

        insert lstinsertPurchaseOrder;

    }
    @AuraEnabled
    public static List<Map<String,Object>> queryRegisterList(String fifter){
        String queryStr = 'SELECT Id,Customer__c,Customer__r.Name,Customer__r.AccountNumber,Name,Order_Type__c,Status__c,Total_Amount__c,'+
        ' CreatedById,CreatedBy.Name,CreatedBy.FirstName,CreatedBy.LastName,CurrencyIsoCode,Training_Course__c,Training_Course__r.Course_Name__r.Order_Model__c,'+
        ' Training_End_Time__c, Training_Start_Time__c , Training_Course__r.Course_Desc__c,Training_Course__r.Course_Name__r.Name,CreatedDate '+
        '  FROM Course_Register__c  WHERE Id != NULL ';
        try {
            //查询registerList
            //根据当前用户的profile判断是不是Dealer
            Set<String> setApprovalProfile = new Set<String>(Label.Service_Training_Approval_Profile.split(','));
            User objUser = [
                SELECT Id,Name,Profile.Name FROM User WHERE Id = :UserInfo.getUserId()
            ];
            String userId = objUser.Id;
            SYSTEM.debug('userId-->'+userId);

            List<Course_Register__c> lstCourseRegister = new List<Course_Register__c>();
            if(!setApprovalProfile.contains(objUser.Profile.Name) ){
                queryStr += ' AND ( CreatedById = :userId';
                //代注册的情况。-->获取当前用户信息
                Map<String,String> mapFeild2Value = CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
                String customerId = mapFeild2Value.get('CustomerId');
                SYSTEM.debug('CustomerId-->'+customerId);
                queryStr += ' OR  Customer__c = :customerId) ';
            }
            if(String.isNotBlank(fifter) && fifter != null){
                String filterCondition2 = getFilterCondition(fifter);
                queryStr += filterCondition2;

            }
            queryStr += ' ORDER BY CreatedDate Desc LIMIT 2000 ';
            List<Map<String,Object>> lstCourseRegisterInfo  = new List<Map<String,Object>>();
            lstCourseRegister = Database.query(queryStr);
            system.debug('lstCourseRegister--->'+lstCourseRegister.size());
            system.debug('queryStr--->'+queryStr);

            if(lstCourseRegister != null && lstCourseRegister.size()>0){
                for(Course_Register__c objCourse : lstCourseRegister){
                    Map<String,Object> mapRegisterInfo = new Map<String,Object>();
                    mapRegisterInfo.put('registerId', objCourse.Id);
                    mapRegisterInfo.put('period',
                    ( objCourse.Training_Start_Time__c == null  || String.valueOf(objCourse.Training_End_Time__c) == null )?
                    ' ' : String.valueOf(objCourse.Training_Start_Time__c) + '-'+ String.valueOf(objCourse.Training_End_Time__c));
                    mapRegisterInfo.put('courseName', objCourse.Training_Course__r.Course_Name__r.Name);
                    String description =  objCourse.Training_Course__r.Course_Desc__c == null ? '': objCourse.Training_Course__r.Course_Desc__c;
                    if(String.isNotBlank(description)){
                        description = description.substring(0, description.indexOf('</'));
                        description = description.substring(description.lastIndexOf('>')+1, description.length());
                    }

                    mapRegisterInfo.put('description', description);
                    mapRegisterInfo.put('customerName', objCourse.Customer__r.Name);
                    mapRegisterInfo.put('creationDate', String.valueOf(objCourse.CreatedDate));
                    mapRegisterInfo.put('customerNumber', objCourse.Customer__r.AccountNumber);
                    mapRegisterInfo.put('registerNo', objCourse.Name);
                    mapRegisterInfo.put('orderType', objCourse.Order_Type__c);
                    mapRegisterInfo.put('totalDueAmount', objCourse.Total_Amount__c);
                    mapRegisterInfo.put('createdBy', (objCourse.CreatedBy.FirstName == null ? '' : objCourse.CreatedBy.FirstName) + ' ' +
                     (objCourse.CreatedBy.LastName == null ? '' : objCourse.CreatedBy.LastName));
                    mapRegisterInfo.put('currencyCode', objCourse.CurrencyIsoCode);
                    mapRegisterInfo.put('status', objCourse.Status__c);
                    mapRegisterInfo.put('viewStyleCss', 'showbtn');
                    mapRegisterInfo.put('editStyleCss', 'hidebtn');
                    //校验当前用户以及状态
                    if(objCourse.Status__c == 'Draft'){
                        //非系统管理员只能草稿状态下编辑
                        mapRegisterInfo.put('editStyleCss', 'showbtn');
                    }
                    lstCourseRegisterInfo.add(mapRegisterInfo);

                }

            }
            system.debug('lstCourseRegisterInfo---->'+lstCourseRegisterInfo.size());
            return lstCourseRegisterInfo;




        } catch (Exception e) {
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static String  RegisterSubmit(String JsonApprovalInfoString){
        system.debug('进入submit--->');

        Map<String,String> objApprovalInfo = (Map<String,String>)JSON.deserialize(JsonApprovalInfoString, Map<String,String>.class);
        String recordId = objApprovalInfo.get('recordId');
        String comments = objApprovalInfo.get('comments');
        String isNotSendEmail = objApprovalInfo.get('isNotSendEmail');
        try {
            Approval.ProcessSubmitRequest req = new Approval.ProcessSubmitRequest();
            system.debug('recordId-->'+recordId);
            system.debug('comments-->'+comments);
            system.debug('isNotSendEmail-->'+isNotSendEmail);
            req.setObjectId(recordId);
            req.setSubmitterId(UserInfo.getUserId());
            req.setProcessDefinitionNameOrId('Register_Approval');
            Course_Register__c objUpdateCourse = [SELECT isNotSendEmail__c,PCS__c,Course_Arrangement__c,Course_Arrangement__r.Avaliable_Slot__c
             FROM Course_Register__c where Id = : recordId];
             //判断座位数
             if(objUpdateCourse.Course_Arrangement__r.Avaliable_Slot__c < objUpdateCourse.PCS__c){
                //可用座位数小于申请的座位数 。需要提示错误信息
                return  Label.No_Seats;
             }
            if(isNotSendEmail == 'true'){
                //查询register

                objUpdateCourse.isNotSendEmail__c = true;

            }else{

                sendSubmitEmailToUser(recordId);
                objUpdateCourse.isNotSendEmail__c = false;
            }
            update objUpdateCourse;


            req.setComments(comments);
            system.debug('req-->'+req);
            // 提交审批请求
            Approval.ProcessResult result = Approval.process(req);
            //发送小铃铛通知
            SendNotifyNocation(recordId, Label.After_Sales_Queue);
            return CCM_Constants.SUCCESS;

        } catch (Exception e) {
            system.debug('报错行数---->'+e.getLineNumber()+'报错信息---->'+e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }
    public static void sendSubmitEmailToUser(String  RecordId){
        try {
             //审核通过发送邮件到创建人
             List<EmailTemplate> lstEmailTemplate = new List<EmailTemplate>();
             //不同语言不同邮件内容
             //判断当前语言
            String userLanguage = UserInfo.getLanguage();
            String EmailName = '';
            if(userLanguage == CCM_Constants.DE){
                EmailName = 'Training Submit Emial DE';

            }else{
                EmailName = 'Training Submit Emial';
            }
            lstEmailTemplate = [
                 Select e.Id, e.Name,Subject,Body,HtmlValue from EmailTemplate e where Name  = :EmailName
             ];
             if(lstEmailTemplate != null && lstEmailTemplate.size()>0){
                 String templateId = lstEmailTemplate[0].Id;
                 //通过recordId查询Register的创建人
                 List<Course_Register__c> lstCourse = new List<Course_Register__c>();
                 lstCourse = [
                    SELECT Id,CreatedById,CreatedBy.Email,CreatedBy.IsActive,Status__c,Customer__c,
                    Customer__r.Name,Customer__r.AccountNumber,Billing_Address__c,Billing_Address__r.Final_Address__c,
                    Course_Arrangement__c,Training_Course__r.Course_Name__c,Training_Course_Name__c,
                    Shiping_Address__c,
                    Billing_Address__r.Acknowledgement_Customer__c,
                    Billing_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Billing_Address__r.Acknowledgement_Customer__r.Salutation),
                    Shiping_Address__r.Acknowledgement_Customer__c,
                    Shiping_Address__r.Acknowledgement_Customer__r.Email,
                    ToLabel(Shiping_Address__r.Acknowledgement_Customer__r.Salutation),
                    Training_Course__c,Course_Arrangement__r.Course_Date__c,Course_Arrangement__r.End_Time__c,
                    Course_Arrangement__r.Start_Time__c,Course_Arrangement__r.Training_Location__r.Final_Address__c,
                    Trainee__c
                    FROM Course_Register__c
                    WHERE CreatedBy.IsActive = TRUE AND Id = :RecordId
                ];

                List<String> lstReceiptEmail = new List<String>();

                List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
                List<Messaging.SingleEmailMessage> lstemails = new List<Messaging.SingleEmailMessage>();
                for(Course_Register__c objCourse : lstCourse){
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    // if(String.isNotBlank(objCourse.Billing_Address__r.Acknowledgement_Customer__c)){
                    //     //不为空。表示取billAddress上的email
                    //     lstReceiptEmail.add(objCourse.Billing_Address__r.Acknowledgement_Customer__r.Email);
                    // }else if(String.isNotBlank(objCourse.Shiping_Address__r.Acknowledgement_Customer__c)){
                    //      //不为空。表示取ShipAddress上的email
                    //      lstReceiptEmail.add(objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Email);
                    // }

                    for(Course_Register_Item__c registerItem : [SELECT email__c FROM Course_Register_Item__c WHERE Course_Register__c = :objCourse.Id]) {
                        lstReceiptEmail.add(registerItem.email__c);
                    }

                    //邮件内容设定
                    email.setSubject(lstEmailTemplate[0].Subject);
                    String strBody = lstEmailTemplate[0].HtmlValue;
                    strBody = strBody.replace('{!Course_Register__c.Customer_Name__c}', objCourse.Customer__r.Name);
                    // String gender = objCourse.Billing_Address__r.Acknowledgement_Customer__c == null ?
                    // objCourse.Shiping_Address__r.Acknowledgement_Customer__r.Salutation :
                    // objCourse.Billing_Address__r.Acknowledgement_Customer__r.Salutation;
                    // strBody = strBody.replace('{Family Name}', gender == null ? '': gender);
                    strBody = strBody.replace('{Family Name}', UserInfo.getName());
                    strBody = strBody.replace('{!Course_Register__c.Customer_Number__c}', objCourse.Customer__r.AccountNumber);
                    strBody = strBody.replace('{!Course_Register__c.Bill_Address_Info__c}', objCourse.Billing_Address__r.Final_Address__c);
                    strBody = strBody.replace('{!Course_Register__c.Training_Course_Name__c}', objCourse.Training_Course_Name__c);
                    strBody = strBody.replace('{!Course_Register__c.Course_Location_Info__c}', objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c == null ? '' : objCourse.Course_Arrangement__r.Training_Location__r.Final_Address__c);
                    String courseTime = String.valueOf(objCourse.Course_Arrangement__r.Course_Date__c) +' '
                    + String.valueOf( objCourse.Course_Arrangement__r.Start_Time__c).subString(0,8) +'-'+
                     String.valueOf(objCourse.Course_Arrangement__r.End_Time__c).subString(0,8);
                    strBody = strBody.replace('{!Course_Register__c.Course_Time__c}', courseTime);
                    strBody = strBody.replace('{!Course_Register__c.Trainee__c}', objCourse.Trainee__c);

                    email.setTargetObjectId(UserInfo.getUserId());
                    email.setHtmlBody(strBody);

                    if(lstReceiptEmail == null || lstReceiptEmail.size() == 0){
                        break ;
                    }
                    system.debug('send Email--->'+lstReceiptEmail);
                    email.setToAddresses(lstReceiptEmail);
                    // email.setCcAddresses(new List<String>{'<EMAIL>'});
                    //设置发件人

                    if(listAddresses != null  && listAddresses.size() > 0){
                        email.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }
                    email.setSaveAsActivity(false);
                    lstemails.add(email);

                }
                Messaging.SendEmailResult[] results = Messaging.sendEmail(lstemails);
             }
        } catch (Exception e) {
            system.debug('报错信息－－－＞'+e.getMessage()+'报错行数－－－－'+e.getLineNumber());
        }
    }

    @AuraEnabled
    public static String  approvalOrRejectRegister(String JsonApprovalInfoString){
        try {
            Map<String,String> objApprovalInfo = (Map<String,String>)JSON.deserialize(JsonApprovalInfoString, Map<String,String>.class);

            String recordId = objApprovalInfo.get('recordId');
            String comments = objApprovalInfo.get('comments');
            String action = objApprovalInfo.get('action');
            if(action == 'Recall'){
                action = 'Removed';
            }
            List<ProcessInstanceWorkitem> workItems =  new List<ProcessInstanceWorkitem>();
                workItems = [
                    SELECT Id, ActorId,ProcessInstanceId, ProcessInstance.TargetObjectId  FROM ProcessInstanceWorkitem WHERE
                    ProcessInstance.TargetObjectId = :recordId
                ];
                if(workItems.size()>0){

                    // 记录为不为 null，返回成功状态
                    // 执行审批操作
                    Approval.ProcessWorkitemRequest req = new Approval.ProcessWorkitemRequest();
                    req.setWorkitemId(workItems[0].Id);
                    req.setAction(action);
                    req.setComments(comments);
                    Approval.ProcessResult result = Approval.process(req);
                }
            //审核通过发送邮件到创建人
            /*List<EmailTemplate> lstEmailTemplate = new List<EmailTemplate>();
            lstEmailTemplate = [
                Select e.Id, e.Name from EmailTemplate e where Name  = 'Training Approval  Emial'
            ];
            if(lstEmailTemplate != null && lstEmailTemplate.size()>0){
                String templateId = lstEmailTemplate[0].Id;
                //通过recordId查询Register的创建人
                List<Course_Register__c> lstCourse = new List<Course_Register__c>();
                lstCourse = [
                    SELECT Id,CreatedById,CreatedBy.Email,CreatedBy.IsActive  FROM Course_Register__c
                    WHERE CreatedBy.IsActive = TRUE AND Id = :recordId
                ];
                if(lstCourse != null && lstCourse.size()>0){
                    List<String> lstReceiptEmail = new List<String>();
                    for(Course_Register__c objCourse : lstCourse){
                        lstReceiptEmail.add(objCourse.CreatedBy.Email);
                    }
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    email.setTemplateId(templateId);
                    email.setTargetObjectId(UserInfo.getUserId());

                    email.setToAddresses(lstReceiptEmail);
                    //设置发件人
                    List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
                    if(listAddresses != null  && listAddresses.size() > 0){
                        email.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }
                    email.setSaveAsActivity(false);
                    try{
                        Messaging.SendEmailResult[] results = Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{email});

                    }catch(Exception e){
                        system.debug('发送邮件错误');
                    }

                }
            }*/
            return CCM_Constants.SUCCESS;

        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    //protal用户接收课程
    @AuraEnabled
    public static void ReceiptCourse(String courseRegisterId){
        try {
            Course_Register__c objRegister = [
                SELECT Id,Customer__c,Customer__r.Name,Customer__r.AccountNumber,Name,Order_Type__c,Status__c,Total_Amount__c,
                CreatedById,CreatedBy.Name, Course_Arrangement__c, Course_Arrangement__r.Free_Training__c
                FROM Course_Register__c  WHERE Id = :courseRegisterId
            ];
            objRegister.Status__c = 'Confirmed';

            //todo 调用接口
            update objRegister;

            // Only sync to ERP if it's not a free training
            if(objRegister.Course_Arrangement__r.Free_Training__c != true){
                CCM_TraningOrderCallout.pushRequestInfo( objRegister.Id);
            }


        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static void CancelCourse(String courseRegisterId,String reason){
        try {
            Course_Register__c objRegister = [
                SELECT Id,Customer__c,Customer__r.Name,Customer__r.AccountNumber,Name,Order_Type__c,Status__c,Total_Amount__c,Reason__c,
                CreatedById,CreatedBy.Name  FROM Course_Register__c  WHERE Id = :courseRegisterId
            ];
            objRegister.Status__c = 'Cancelled';
            objRegister.Reason__c = reason;
            update objRegister;

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    public CCM_CourseRegisterPreviewCtl() {

    }
    public static void SendNotifyNocation(String objectId,String QueueName){
        Messaging.CustomNotification notification = new Messaging.CustomNotification();
        notification.setTitle(UserInfo.getName()+' is requesting approval for course register');
        Course_Register__c objCourse = [
            SELECT Name from Course_Register__c where Id = :objectId
        ];
        notification.setBody(objCourse.Name);
        CustomNotificationType objNotificationType = [SELECT Id FROM CustomNotificationType WHERE DeveloperName = 'Po_Approval_Notify'];
        notification.setNotificationTypeId(objNotificationType.Id);
        notification.setTargetId(objectId);
        List<GroupMember> lstMenber  = [
            Select g.GroupId, g.Group.Name, g.UserOrGroupId from GroupMember g  where Group.Name = : QueueName
        ];
        system.debug('lstMenber--->'+lstMenber);
        List<String> lstGroupId = new List<String>();
        List<String> lstRoleId = new List<String>();
        for(GroupMember objMenber : lstMenber){
            lstGroupId.add(objMenber.UserOrGroupId);
        }
        //通过GroupId查找Group
        List<Group> lstGroupInfo = [
            SELECT iD,RelatedId FROM Group WHERE Id IN:lstGroupId
        ];
        for(Group objGroup : lstGroupInfo){
            lstRoleId.add(objGroup.RelatedId);
        }
        List<User> lstUser = [
            SELECT Id , IsActive,Email,UserRoleId FROM User WHERE UserRoleId IN : lstRoleId AND IsActive = TRUE
        ];
        system.debug('lstUser--->'+lstUser);
        List<String> lstEmailList = new List<String>();
        Set<String> setSendIds = new Set<String>();
        for(User objUser : lstUser){
            setSendIds.add(objUser.Id);
            setSendIds.add(UserInfo.getUserId());
            lstEmailList.add(objUser.Email);
        }
        system.debug('需要发送的人----->'+setSendIds);
        notification.send(setSendIds);


    }
    public static String getFilterCondition(String filterString){
        String sqlString = '';
        FilterWrapper filters = (FilterWrapper)JSON.deserialize(filterString, FilterWrapper.class);
        if (String.isNotBlank(filters.OrderType)){
            SYSTEM.debug('filters.OrderType-->'+filters.OrderType);
            sqlString += ' AND Order_Type__c =\'' + (String) filters.OrderType + '\'';
        }
        if (String.isNotBlank(filters.CreatedBy)){
            sqlString += ' AND   CreatedById = \'' + (String) filters.CreatedBy + '\'';

        }
        system.debug('filters.Customer---->'+filters.Customer);
        if (String.isNotBlank(filters.Customer)){
            sqlString += ' AND Customer__c = \'' + (String) filters.Customer + '\'';

        }
        if (String.isNotBlank(filters.CustomerNumber)){
            sqlString += ' AND Customer__r.AccountNumber like \'%' + (String) filters.CustomerNumber + '%\'';
        }
        if (String.isNotBlank(filters.Model)){
            sqlString += ' AND Training_Course__r.Course_Name__r.Order_Model__c LIKE \'%' + (String) filters.Model + '%\'';
        }

        if (String.isNotBlank(filters.requestNo)){
            sqlString += ' AND Name LIKE \'%' + (String) filters.requestNo + '%\'';
        }
        if (String.isNotBlank(filters.OrderNumber)){
            sqlString += ' AND Name LIKE \'%' + (String) filters.OrderNumber + '%\'';
        }
        if (String.isNotBlank(filters.OrderDateFrom)){

            sqlString += ' AND Course_Arrangement__r.Course_Date__c >=' + String.ValueOf(filters.OrderDateFrom);
        }
        if (String.isNotBlank(filters.OrderDateTo)){
            sqlString += ' AND Course_Arrangement__r.Course_Date__c  <=' + String.ValueOf(filters.OrderDateTo);
        }
        if (String.isNotBlank(filters.OrderStatus)){
            String statusCondition = '';
            String[] OrderStatusList = filters.OrderStatus.split(',');
            for (String st : OrderStatusList){
                statusCondition += '\'' + st + '\',';
            }
            sqlString += ' AND Status__c in(' + statusCondition.removeEnd(',') + ')';
        }

        if (String.isNotBlank(filters.SalesPerson)){
            sqlString += ' AND Customer__r.OwnerId  =\'' + filters.SalesPerson + '\'';
        }

        if (String.isNotBlank(filters.trainingTitle)){
            sqlString += ' AND Training_Course__r.Training_Title__c LIKE \'%' + (String) filters.trainingTitle + '%\'';
        }
        if (String.isNotBlank(filters.trainingLocation)){
            sqlString += ' AND Course_Arrangement__r.Training_Location__r.Final_Address__c LIKE \'%' + (String) filters.trainingLocation + '%\'';
        }
        if (String.isNotBlank(filters.participants)){
            sqlString += ' AND Trainee__c LIKE \'%' + (String) filters.participants + '%\'';
        }
        if (String.isNotBlank(filters.trainingDateTimeFrom)){
            //DateTime searchDate = DateTime.valueOf(filters.trainingDateTimeFrom);

            //sssDatetime myDatetime = Datetime.newInstance(searchDate.year(), searchDate.month(), searchDate.day(), searchDate.hour(), searchDate.minute(), searchDate.second());
            system.debug('filters.trainingDateTimeFrom---->'+filters.trainingDateTimeFrom);
            sqlString += ' AND Training_Start_Time__c >= '+filters.trainingDateTimeFrom;
        }
        if (String.isNotBlank(filters.trainingDateTimeTo)){
            //DateTime searchDate = DateTime.valueOf(filters.trainingDateTimeFrom);
            system.debug('filters.trainingDateTimeTo---->'+filters.trainingDateTimeTo);

            //Datetime myDatetime = Datetime.newInstance(searchDate.year(), searchDate.month(), searchDate.day(), searchDate.hour(), searchDate.minute(), searchDate.second());
             sqlString += ' AND Training_End_Time__c  <=' +filters.trainingDateTimeTo ;
        }
        return sqlString;
    }
    public class FilterWrapper{
        public String OrderNumber;

        public String OrderType;

        public String CreatedBy;

        public String Customer;
        public String CustomerNumber;

        public String SalesPerson;

        public String requestNo;

        public String OrderDateFrom;

        public String OrderDateTo;

        public String Model;

        public String OrderStatus;
        public String trainingTitle;
        public String trainingDateTimeFrom;
        public String trainingDateTimeTo;
        public String trainingLocation;
        public String participants;

    }
}