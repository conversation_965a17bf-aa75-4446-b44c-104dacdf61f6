/**
 * <AUTHOR>
 * @date 2025-06-30
 * @description Test class for CCM_WarrantyClaim_VATGAPCalculateHandler
 */
@isTest
public class CCM_WarrantyVATGAPHandler_Test {
    
    @TestSetup
    static void makeData() {
        // Create test Accounts with different configurations
        List<Account> accounts = new List<Account>();
        
        // Dealer account
        Account dealer = new Account();
        dealer.Name = 'Test Dealer';
        dealer.Country_All__c = 'DE-Germany';
        dealer.Country__c = 'DE';
        dealer.Sales_Channel__c = 'OPE Dealer';
        dealer.Classification1__c = 'Dealer';
        dealer.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        accounts.add(dealer);
        
        // PbE account
        Account pbeAccount = new Account();
        pbeAccount.Name = 'Test PbE';
        pbeAccount.Country_All__c = 'DE-Germany';
        pbeAccount.Country__c = 'DE';
        pbeAccount.Sales_Channel__c = 'PbE';
        pbeAccount.Classification1__c = 'PbE Importdealer';
        pbeAccount.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        accounts.add(pbeAccount);
        
        // Distributor account in EU
        Account distributorEU = new Account();
        distributorEU.Name = 'Test Distributor EU';
        distributorEU.Country_All__c = 'FR-France';
        distributorEU.Country__c = 'FR';
        distributorEU.Sales_Channel__c = 'Distributor';
        distributorEU.Classification1__c = 'Distributor';
        distributorEU.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        accounts.add(distributorEU);
        
        // Distributor account non-EU
        Account distributorNonEU = new Account();
        distributorNonEU.Name = 'Test Distributor Non-EU';
        distributorNonEU.Country_All__c = 'US-United States';
        distributorNonEU.Country__c = 'US';
        distributorNonEU.Sales_Channel__c = 'Distributor';
        distributorNonEU.Classification1__c = 'Distributor';
        distributorNonEU.RecordTypeId = CCM_Constants.CHANNEL_RECORDTYPEID;
        accounts.add(distributorNonEU);
        
        insert accounts;

        // Create test Consumer Account
        Account consumer = new Account();
        consumer.LastName = 'Test Consumer';
        consumer.Country_All__c = 'DE-Germany';
        consumer.Postal_Code__c = '12345';
        consumer.RecordTypeId = CCM_Constants.PERSONACCOUNT_RECORDTYPEID;
        insert consumer;

        // Create test Product
        Product2 product = new Product2();
        product.Name = 'Test Product';
        product.Order_Model__c = 'TEST001';
        product.Master_Product__c = 'TEST001';
        product.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        insert product;
        
        // Create test Warranty Claims
        List<Warranty_Claim__c> claims = new List<Warranty_Claim__c>();

        for (Account acc : accounts) {
            Warranty_Claim__c claim = new Warranty_Claim__c();
            claim.Serial_Number__c = 'TEST' + acc.Name.replace(' ', '') + 'X';
            claim.Dealer_Name__c = acc.Id;
            claim.Service_Option__c = 'Repair';
            claim.Claim_Status__c = 'Draft';
            claim.Labor_Input_Time__c = 2.5;
            claim.Actual_Labor_Rate__c = 10.0; // Labor_Input_Cost = 25.00
            claim.Diagnostic_Fee__c = 5.00;
            claim.Bench_Mark__c = 100.00;
            claim.Historical_Warranty_Efforts__c = 50.00;
            claim.Bench_Mark_Calculated__c = false;
            claims.add(claim);
        }

        insert claims;

        // Create Warranty Claim Items to populate Material_Actual_Cost__c
        List<Warranty_Claim_Item__c> claimItems = new List<Warranty_Claim_Item__c>();

        for (Warranty_Claim__c claim : claims) {
            Warranty_Claim_Item__c item = new Warranty_Claim_Item__c();
            item.Warranty_Claim__c = claim.Id;
            item.Actual_Price__c = 40.00;
            item.Quantity__c = '1';
            claimItems.add(item);
        }

        insert claimItems;
    }
    
    @isTest
    static void testHandle_BenchMarkCalculatedUpdate() {
        // Get test data
        List<Warranty_Claim__c> claims = [SELECT Id, Bench_Mark_Calculated__c, Dealer_Name__c FROM Warranty_Claim__c];
        
        // Update claims to trigger the handler
        for (Warranty_Claim__c claim : claims) {
            claim.Bench_Mark_Calculated__c = true;
        }
        
        Test.startTest();
        update claims;
        Test.stopTest();
        
        // Verify VAT Gap and Cost Center were calculated
        List<Warranty_Claim__c> updatedClaims = [
            SELECT Id, VAT_Gap_for_VAT_Correction__c, VAT_Cost_Center__c, All_Cost_Calculated__c, 
                   Dealer_Name__r.Name, Dealer_Name__r.Sales_Channel__c, Dealer_Name__r.Classification1__c, Dealer_Name__r.Country__c
            FROM Warranty_Claim__c
        ];
        
        for (Warranty_Claim__c claim : updatedClaims) {
            System.assertEquals(true, claim.All_Cost_Calculated__c, 'All Cost Calculated should be true');
            System.assertNotEquals(null, claim.VAT_Gap_for_VAT_Correction__c, 'VAT Gap should be calculated');
            System.assertNotEquals(null, claim.VAT_Cost_Center__c, 'VAT Cost Center should be set');
            
            // Verify cost center logic based on account type
            if (claim.Dealer_Name__r.Sales_Channel__c.containsIgnoreCase('Dealer')) {
                System.assertEquals('EEG', claim.VAT_Cost_Center__c, 'Dealer should have EEG cost center');
            } else if (claim.Dealer_Name__r.Sales_Channel__c.containsIgnoreCase('PbE') &&
                      claim.Dealer_Name__r.Classification1__c == 'PbE Importdealer') {
                System.assertEquals('EEG', claim.VAT_Cost_Center__c, 'PbE Importdealer should have EEG cost center');
            } else if (claim.Dealer_Name__r.Sales_Channel__c.containsIgnoreCase('Distributor')) {
                // Check if it's EU or non-EU distributor
                if (claim.Dealer_Name__r.Country__c == 'FR') {
                    System.assertEquals('type2', claim.VAT_Cost_Center__c, 'EU Distributor should have type2 cost center');
                } else {
                    System.assertEquals('default', claim.VAT_Cost_Center__c, 'Non-EU Distributor should have default cost center');
                }
            }
        }
    }
    
    @isTest
    static void testHandle_NoUpdate() {
        // Get test data
        List<Warranty_Claim__c> claims = [SELECT Id, Bench_Mark_Calculated__c FROM Warranty_Claim__c LIMIT 1];
        
        // Update without changing Bench_Mark_Calculated__c
        claims[0].Serial_Number__c = 'UPDATED_SERIAL';
        
        Test.startTest();
        update claims;
        Test.stopTest();
        
        // Verify no VAT calculations were performed
        Warranty_Claim__c updatedClaim = [
            SELECT VAT_Gap_for_VAT_Correction__c, VAT_Cost_Center__c, All_Cost_Calculated__c 
            FROM Warranty_Claim__c 
            WHERE Id = :claims[0].Id
        ];
        
        System.assertEquals(null, updatedClaim.VAT_Gap_for_VAT_Correction__c, 'VAT Gap should not be calculated');
        System.assertEquals(null, updatedClaim.VAT_Cost_Center__c, 'VAT Cost Center should not be set');
        System.assertEquals(false, updatedClaim.All_Cost_Calculated__c, 'All Cost Calculated should remain false');
    }
    
    @isTest
    static void testHandle_IsRunFalse() {
        // Disable the handler
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = false;
        
        // Get test data
        List<Warranty_Claim__c> claims = [SELECT Id, Bench_Mark_Calculated__c FROM Warranty_Claim__c LIMIT 1];
        
        // Update to trigger the handler
        claims[0].Bench_Mark_Calculated__c = true;
        
        Test.startTest();
        update claims;
        Test.stopTest();
        
        // Verify no processing occurred
        Warranty_Claim__c updatedClaim = [
            SELECT VAT_Gap_for_VAT_Correction__c, VAT_Cost_Center__c, All_Cost_Calculated__c 
            FROM Warranty_Claim__c 
            WHERE Id = :claims[0].Id
        ];
        
        System.assertEquals(null, updatedClaim.VAT_Gap_for_VAT_Correction__c, 'VAT Gap should not be calculated when handler is disabled');
        System.assertEquals(null, updatedClaim.VAT_Cost_Center__c, 'VAT Cost Center should not be set when handler is disabled');
        System.assertEquals(false, updatedClaim.All_Cost_Calculated__c, 'All Cost Calculated should remain false when handler is disabled');
        
        // Re-enable for other tests
        CCM_WarrantyClaim_VATGAPCalculateHandler.isRun = true;
    }
    
    @isTest
    static void testSetCostCenter_DealerAccount() {
        // Get dealer account
        Account dealer = [SELECT Id, Sales_Channel__c, Classification1__c, Country__c FROM Account WHERE Name = 'Test Dealer' LIMIT 1];
        
        // Test through the main handler logic by updating a claim
        Warranty_Claim__c claim = [SELECT Id FROM Warranty_Claim__c WHERE Dealer_Name__c = :dealer.Id LIMIT 1];
        claim.Bench_Mark_Calculated__c = true;
        
        Test.startTest();
        update claim;
        Test.stopTest();
        
        // Verify cost center was set correctly
        Warranty_Claim__c updatedClaim = [SELECT VAT_Cost_Center__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals('EEG', updatedClaim.VAT_Cost_Center__c, 'Dealer should have EEG cost center');
    }
    
    @isTest
    static void testSetCostCenter_DistributorEU() {
        // Get EU distributor account
        Account distributor = [SELECT Id FROM Account WHERE Name = 'Test Distributor EU' LIMIT 1];
        
        // Test through the main handler logic
        Warranty_Claim__c claim = [SELECT Id FROM Warranty_Claim__c WHERE Dealer_Name__c = :distributor.Id LIMIT 1];
        claim.Bench_Mark_Calculated__c = true;
        
        Test.startTest();
        update claim;
        Test.stopTest();
        
        // Verify cost center was set correctly
        Warranty_Claim__c updatedClaim = [SELECT VAT_Cost_Center__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals('type2', updatedClaim.VAT_Cost_Center__c, 'EU Distributor should have type2 cost center');
    }
    
    @isTest
    static void testSetCostCenter_DistributorNonEU() {
        // Get non-EU distributor account
        Account distributor = [SELECT Id FROM Account WHERE Name = 'Test Distributor Non-EU' LIMIT 1];
        
        // Test through the main handler logic
        Warranty_Claim__c claim = [SELECT Id FROM Warranty_Claim__c WHERE Dealer_Name__c = :distributor.Id LIMIT 1];
        claim.Bench_Mark_Calculated__c = true;
        
        Test.startTest();
        update claim;
        Test.stopTest();
        
        // Verify cost center was set correctly
        Warranty_Claim__c updatedClaim = [SELECT VAT_Cost_Center__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals('default', updatedClaim.VAT_Cost_Center__c, 'Non-EU Distributor should have default cost center');
    }
    
    @isTest
    static void testSetCostCenter_PbEAccount() {
        // Get PbE account
        Account pbeAccount = [SELECT Id FROM Account WHERE Name = 'Test PbE' LIMIT 1];
        
        // Test through the main handler logic
        Warranty_Claim__c claim = [SELECT Id FROM Warranty_Claim__c WHERE Dealer_Name__c = :pbeAccount.Id LIMIT 1];
        claim.Bench_Mark_Calculated__c = true;
        
        Test.startTest();
        update claim;
        Test.stopTest();
        
        // Verify cost center was set correctly
        Warranty_Claim__c updatedClaim = [SELECT VAT_Cost_Center__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals('EEG', updatedClaim.VAT_Cost_Center__c, 'PbE Importdealer should have EEG cost center');
    }
    
    @isTest
    static void testSetCostCenter_AccountNotFound() {
        // Create a claim with non-existing dealer
        Warranty_Claim__c claim = new Warranty_Claim__c();
        claim.Serial_Number__c = 'TESTNONEXISTINGX';
        claim.Dealer_Name__c = null; // No dealer
        claim.Service_Option__c = 'Repair';
        claim.Claim_Status__c = 'Draft';
        claim.Labor_Input_Time__c = 2.0;
        claim.Actual_Labor_Rate__c = 10.0;
        claim.Diagnostic_Fee__c = 5.00;
        claim.Bench_Mark__c = 100.00;
        claim.Historical_Warranty_Efforts__c = 50.00;
        claim.Bench_Mark_Calculated__c = false;
        insert claim;
        
        // Update to trigger handler
        claim.Bench_Mark_Calculated__c = true;
        
        Test.startTest();
        update claim;
        Test.stopTest();
        
        // Verify default cost center was set
        Warranty_Claim__c updatedClaim = [SELECT VAT_Cost_Center__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        System.assertEquals('default', updatedClaim.VAT_Cost_Center__c, 'Should have default cost center when dealer not found');
    }
    
    @isTest
    static void testVATGapCalculation() {
        // Get test claim
        Warranty_Claim__c claim = [SELECT Id, Bench_Mark__c, Historical_Warranty_Efforts__c, Total_Actual__c FROM Warranty_Claim__c LIMIT 1];
        
        // Update to trigger calculation
        claim.Bench_Mark_Calculated__c = true;
        
        Test.startTest();
        update claim;
        Test.stopTest();
        
        // Verify VAT gap was calculated
        Warranty_Claim__c updatedClaim = [SELECT VAT_Gap_for_VAT_Correction__c FROM Warranty_Claim__c WHERE Id = :claim.Id];
        
        // Expected calculation: totalWarrantyEfforts (50 + 70) = 120 > benchMark (100)
        // currentVATGAP = Total_Actual__c = 70
        // previousVATGAP = Historical_Warranty_Efforts__c - benchMark = 50 - 100 = -50 (negative)
        // Final VAT GAP = previousVATGAP + Total_Actual__c = -50 + 70 = 20
        System.assertEquals(20.00, updatedClaim.VAT_Gap_for_VAT_Correction__c, 'VAT Gap should be calculated correctly');
    }
}
