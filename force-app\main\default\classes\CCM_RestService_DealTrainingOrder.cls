global without sharing class CCM_RestService_DealTrainingOrder {
    global static CCM_UpsertOrder.ResultObj dealTrainingOrderInfo(CCM_UpsertOrder.ResultObj resObj,List<CCM_UpsertOrder.ReqestObj> lstTrainingReqObj){
        // ResultObj resObj = new ResultObj();
        // resObj.Process_Result = new List<ReturnItem>();
        //接收当前批次的training order
        List<Training_Order__c> lstTrainingOrder = new List<Training_Order__c>();
        Map<String,String> mapOIdToSFId = new Map<String,String>();
        //获取Account info
        List<Account> lstAccount = new List<Account>();
        Set<String> setAccNo = new Set<String>();
        Map<String,String> mapNoToId = new Map<String,String>();
        //Address 信息
        Set<String> setAddressOId = new Set<String>();
        List<Account_Address__c> lstAddress = new List<Account_Address__c>();
        Map<String,String> mapAddressNoToId = new Map<String,String>();
        //获取user信息
        Set<String> setEmail = new Set<String>();
        Map<String,String> mapEmailToId = new Map<String,String>();
        List<User> lstUser = new List<User>();
        //接收当前批次training order item
        List<Training_Order_Item__c> lstTrainingOrderItem = new List<Training_Order_Item__c>();
        //获取training product
        List<Course_Product__c> lstTrainingProduct = new List<Course_Product__c>();
        Map<String,String> mapCourseModelToId = new Map<String,String>();
        Set<String> courseRegisterNames = new Set<String>();
        for(CCM_UpsertOrder.ReqestObj objOrder : lstTrainingReqObj){
            if(String.isNotBlank(objOrder.ORDER_NUMBER_CRM)) {
                courseRegisterNames.add(objOrder.ORDER_NUMBER_CRM);
            }
        }
        Map<String, String> courseRegisterMap = new Map<String, String>();
        for(Course_Register__c courseRegister : [SELECT Name FROM Course_Register__c WHERE Name IN :courseRegisterNames]) {
            courseRegisterMap.put(courseRegister.Name, courseRegister.Id);
        }
        for(CCM_UpsertOrder.ReqestObj objOrder : lstTrainingReqObj){
            Training_Order__c data = new Training_Order__c();
            setOrderValue(objOrder,data);
            // data.Course_Register__c = objOrder.ORDER_NUMBER_CRM;
            if(courseRegisterMap.containsKey(objOrder.ORDER_NUMBER_CRM)) {
                data.Course_Register__c = courseRegisterMap.get(objOrder.ORDER_NUMBER_CRM);
            }
            lstTrainingOrder.add(data);
            setAccNo.add(objOrder.CUSTOMER);
            setAddressOId.add(objOrder.BILLTO);
            setAddressOId.add(objOrder.SHIPTO);
            setEmail.add(objOrder.SALES_REP_EMAIL);
        }
        System.debug('lstTrainingOrder的数量'+lstTrainingOrder.size());
        if(lstTrainingOrder.size() > 0 ){
            //获取Customer 信息
            lstAccount = [SELECT Id, AccountNumber FROM Account WHERE AccountNumber in :setAccNo];
            if(lstAccount.size() > 0){
                for(Account acc : lstAccount){
                    mapNoToId.put(acc.AccountNumber, acc.Id);
                }   
            }
            //获取Address 信息
            lstAddress = [SELECT Id,Customer_Line_Oracle_ID__c FROM Account_Address__c WHERE Customer_Line_Oracle_ID__c in :setAddressOId];
            if(lstAddress.size() > 0){
                for(Account_Address__c address : lstAddress){
                    mapAddressNoToId.put(address.Customer_Line_Oracle_ID__c,address.Id);
                }
            }
            //获取user 信息
            lstUser = [SELECT Id,Email FROM User WHERE Email in :setEmail];
            if(lstUser.size() > 0){
                for(User u : lstUser){
                    mapEmailToId.put(u.Email, u.Id);
                }
            }
            //绑定lookup 字段
            for(Training_Order__c to : lstTrainingOrder){
                //绑定customer
                if(mapNoToId.containsKey(to.CustomerNumber__c)){
                    to.Customer__c = mapNoToId.get(to.CustomerNumber__c);
                }
                //绑定地址信息
                if(mapAddressNoToId.containsKey(to.Bill_To__c)){
                    to.Billing_Address__c = mapAddressNoToId.get(to.Bill_To__c);
                }
                if(mapAddressNoToId.containsKey(to.Ship_To__c)){
                    to.Ship_To_Address__c = mapAddressNoToId.get(to.Ship_To__c);
                }
                //绑定sales rep user信息
                if(mapEmailToId.containsKey(to.Sales_Rep_Email__c)){
                    to.Sales_Rep_Info__c = mapEmailToId.get(to.Sales_Rep_Email__c);
                }
            }
            // 更新或插入订单头数据
            Database.UpsertResult[] resList = Database.Upsert(lstTrainingOrder,Training_Order__c.Order_Oralce_Id__c.getDescribe().getSObjectField(),false);
            for (Integer i = 0 ; i < resList.size() ; i++) {
                if (!resList.get(i).isSuccess()) {
                    Database.Error[] err = resList.get(i).getErrors();
                    CCM_UpsertOrder.ReturnItem request = new CCM_UpsertOrder.ReturnItem();
                    request.External_Id = lstTrainingReqObj.get(i).ORDER_ORACLE_ID;
                    request.Error_Message = 'This Training Order was failed saving in Salesforce';
                    request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                    resObj.Process_Result.add(request);
                }
            }
        }
        //获取订单头SF ID
        for(Training_Order__c headData : lstTrainingOrder){
            mapOIdToSFId.put(headData.Order_Oralce_Id__c,String.valueOf(headData.Id));
        }
        
        //获取Training Product Model To Id
        lstTrainingProduct = [SELECT Id, Order_Model__c FROM Course_Product__c limit 9999];
        if(lstTrainingProduct.size() > 0){
            for(Course_Product__c cp : lstTrainingProduct){
                mapCourseModelToId.put(cp.Order_Model__c,cp.Id);
            }
        }

        for(CCM_UpsertOrder.ReqestObj objOrder : lstTrainingReqObj){
            List<CCM_UpsertOrder.OrderItem> lstReqItem = new List<CCM_UpsertOrder.OrderItem>();
            lstReqItem = objOrder.OrderLine;
            if(lstReqItem.size() > 0){
                for(CCM_UpsertOrder.OrderItem item : lstReqItem){
                    Training_Order_Item__c lineData = new Training_Order_Item__c();
                    if(mapOIdToSFId.containsKey(objOrder.ORDER_ORACLE_ID)){
                        //绑定order sf id
                        lineData.Training_Order__c = mapOIdToSFId.get(objOrder.ORDER_ORACLE_ID);
                    }
                    setOrderLineValue(item,lineData,objOrder);
                    if(mapCourseModelToId.containsKey(item.PRODUCT_MODEL)){
                        lineData.Course_Product__c = mapCourseModelToId.get(item.PRODUCT_MODEL);
                    }
                    lstTrainingOrderItem.add(lineData);
                }
            }
        }
        System.debug('lstTrainingOrderItem'+lstTrainingOrderItem.size());
        if(lstTrainingOrderItem.size() > 0){
            Database.UpsertResult[] resList = Database.Upsert(lstTrainingOrderItem,Training_Order_Item__c.Order_Line_OracleId__c.getDescribe().getSObjectField(),false);
            for (Integer i = 0 ; i < resList.size() ; i++) {
                if (!resList.get(i).isSuccess()) {
                    Database.Error[] err = resList.get(i).getErrors();
                    CCM_UpsertOrder.ReturnItem request = new CCM_UpsertOrder.ReturnItem();
                    request.External_Id = lstTrainingOrderItem.get(i).Order_Line_OracleId__c;
                    request.Error_Message = 'This Training OrderLine was failed saving in Salesforce';
                    request.Error_Detail = '***The following error has occurred***' + err.get(0).getStatusCode() + ' : ' +err.get(0).getMessage();
                    resObj.Process_Result.add(request);
                }
            }
        }

        if (resObj.Process_Result.size() == 0) {
            resObj.Process_Status = 'Success';
            String logId = Util.logIntegration('Training Order Log','CCM_RestService_DealTrainingOrder','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(lstTrainingReqObj), JSON.serialize(resObj));
        } else {
            resObj.Process_Status = 'Fail';
            String logId = Util.logIntegration('Training Order Exception','CCM_RestService_DealTrainingOrder','POST',JSON.serialize(resObj.Process_Result),JSON.serialize(lstTrainingReqObj), JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept Training Order Info',logId,CCM_UpsertOrder.getMailErrorMessage(resObj));
        }

        return resObj;

    }
    /**
     * 接入订单行信息
     */
    global static void setOrderLineValue(CCM_UpsertOrder.OrderItem item,Training_Order_Item__c lineData,CCM_UpsertOrder.ReqestObj objOrder) {
        lineData.CurrencyIsoCode = objOrder.CURRENCY_CODE;
        lineData.Order_OracleId__c = objOrder.ORDER_ORACLE_ID;
        lineData.Order_Line_No__c = item.ORDERLINE_NUMBER;
        lineData.Order_Line_OracleId__c = item.ORDERLINE_ORACLE_ID;
        lineData.OrderLine_CRM_Id__c = item.ORDERLINE_CRM_ID;
        lineData.Order_Model__c = item.PRODUCT_MODEL;
        lineData.Order_Qty__c = String.isEmpty(item.ORDER_QUANTITY) ? null : Decimal.valueOf(item.ORDER_QUANTITY);
        lineData.List_Price__c =  String.isEmpty(item.LIST_PRICE) ? null : Decimal.valueOf(item.LIST_PRICE);
        lineData.Sales_Price__c = String.isEmpty(item.SALES_PRICE) ? null : Decimal.valueOf(item.SALES_PRICE);
        lineData.Unit_Selling_Price__c = String.isEmpty(item.UNIT_SELLING_PRICE) ? null : Decimal.valueOf(item.UNIT_SELLING_PRICE);
        lineData.Request_Date__c = String.isEmpty(item.REQUEST_DATE) ? null : Date.valueOf(item.REQUEST_DATE);
        lineData.Product_Description__c = item.PRODUCT_DESCRIPTION;
        lineData.UOM__c = item.UOM;
        lineData.Remark__c = item.REMARK;
        lineData.Order_Line_Status__c = item.ORDER_LINE_STATUS;
    }

    /**
     * 接入订单头信息
     */
    global static void setOrderValue(CCM_UpsertOrder.ReqestObj objOrder,Training_Order__c objUpsertOrder) {
        //Booked->Order Processing    Closed-->Shipment Complete   Cancelled -->Cancelled
        if(CCM_Constants.BOOKED.Equals(objOrder.ORDER_STATUS)){
            objUpsertOrder.Order_Status__c = 'Order Processing';
        }else if(CCM_Constants.CLOSED.Equals(objOrder.ORDER_STATUS)){
            objUpsertOrder.Order_Status__c = 'Order Completed';
        }else if(CCM_Constants.CANCELLED_EBS.Equals(objOrder.ORDER_STATUS)){
            objUpsertOrder.Order_Status__c = 'Cancelled';
        }
        objUpsertOrder.CurrencyIsoCode = objOrder.CURRENCY_CODE;
        objUpsertOrder.Order_Oralce_Id__c = objOrder.ORDER_ORACLE_ID;
        objUpsertOrder.Order_Number__c = objOrder.ORDER_NUMBER_ORACLE;
        objUpsertOrder.Order_Number_CRM__c = objOrder.ORDER_NUMBER_CRM;
        objUpsertOrder.CustomerNumber__c = objOrder.CUSTOMER;
        objUpsertOrder.Course_Detail__c = objOrder.PO_NUMBER;
        objUpsertOrder.Ship_To__c = objOrder.SHIPTO;
        objUpsertOrder.Bill_To__c = objOrder.BILLTO;
        objUpsertOrder.Scene_Type__c = objOrder.SCENE_TYPE;
        objUpsertOrder.Order_Type__c = 'Training Order';
        objUpsertOrder.Price_List__c = objOrder.PRICE_LIST;
        objUpsertOrder.Price_Date__c = String.isEmpty(objOrder.PRICE_DATE) ? null : Date.valueOf(objOrder.PRICE_DATE);
        objUpsertOrder.Sales_Rep_Code__c = objOrder.SALES_REP_CODE;
        objUpsertOrder.Sales_Rep_Email__c = objOrder.SALES_REP_EMAIL;
        objUpsertOrder.Order_Date__c = String.isEmpty(objOrder.DATE_ORDER) ? null : Date.valueOf(objOrder.DATE_ORDER);
        objUpsertOrder.Payment_Term__c = objOrder.PAYMENT_TERMS;
        objUpsertOrder.Inco_Term__c = objOrder.INCO_TERM;
        objUpsertOrder.Shipping_Place__c = objOrder.SHPPING_PLACE;
        objUpsertOrder.Freight_Fee__c = String.isEmpty(objOrder.FERIGHT_FEE) ? null : Decimal.valueOf(objOrder.FERIGHT_FEE);
        // objUpsertOrder.Order_Status__c = objOrder.ORDER_STATUS;
        objUpsertOrder.Total_Value__c = String.isEmpty(objOrder.TOTAL_VALUE) ? null : Decimal.valueOf(objOrder.TOTAL_VALUE);
        objUpsertOrder.VAT__c = String.isEmpty(objOrder.VAT) ? null : Decimal.valueOf(objOrder.VAT);
        objUpsertOrder.Total_Value_Net__c = String.isEmpty(objOrder.TOTAL_VALUE_NET) ? null : Decimal.valueOf(objOrder.TOTAL_VALUE_NET);
        objUpsertOrder.Total_Amount__c = String.isEmpty(objOrder.TOTAL_AMOUNT) ? null : Decimal.valueOf(objOrder.TOTAL_AMOUNT);
    }

    // global class ResultObj {
    //     global String Process_Status;
    //     global List<ReturnItem> Process_Result;
    // }
    // global static String getMailErrorMessage(CCM_UpsertOrder.ResultObj res){
    //     String errContent = '';
    //     errContent += 'Process Status : Fail<br/><br/>';
    //     if(res.Process_Result.size() > 0){
    //         for(ReturnItem Item : res.Process_Result){
    //             errContent += 'External ID : ' + Item.External_Id + '<br/>';
    //             errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
    //             errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>'; 
    //         }
    //     }
    //     return errContent;
    // }

    // global class ReturnItem {
    //     global String External_Id;
    //     global String Error_Message;
    //     global String Error_Detail;
    // }
}