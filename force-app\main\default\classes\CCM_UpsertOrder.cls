/**
 * url : /services/apexrest/CCM_RestService_DealOrderInfoEU
 * [
  {
    "HEADERID": "1",
    "ORDER_ORACLEID": "2",
    "ORDER_NUMBER_ORACLE": "3",
    "ORDER_NUMBER_CRM": "4",
    "ORDER_HEADERID": "5",
    "CUSTOMER": "E02140",
    "PO_NUMBER": "7",
    "SHIPTO": "8",
    "BILLTO": "9",
    "ORDER_TYPE": "ORDER_TYPE",
    "PRICE_LIST": "1",
    "SALES_REP_EMAIL": "<EMAIL>",
    "DATE_ORDER": "2019-10-24 00:00:00",
    "PAYMENT_TERM": "NA021",
    "FREIGHT_TERM": "Collect",
    "INCO_TERM": "aa",
    "SHPPING_PLACE": "11",
    "EXPECTED_DELIVERY_DATE": "2023-01-11",
    "FERIGHT_FEE": "12",
    "ORDER_STATUS": "Booked",
    "DROPSHIP_NAME": "13",
    "DROPSHIP_ADDRESS1": "14",
    "DROPSHIP_ADDRESS2": "15",
    "DROPSHIP_COUNTRY": "16",
    "DROPSHIP_ZIP": "17",
    "DROPSHIP_STATE": "18",
    "DROPSHIP_CITY": "19",
    "INSTRUCTION_TO_DSV": "20",
    "CARRIER_INFORMATION": "ss",
    "WAREHOUSE": "22",
    "TOTAL_VALUE": "23",
    "HEADER_DISCOUNT": "24",
    "HEADER_DISCOUNT_AMOUNT": "25",
    "TOTAL_VALUE_NET": "26",
    "TOTAL_AMOUNT": "27",
    "IS_DROPSHIP": "YES",
    "ATTRIBUTE1": "",
    "ATTRIBUTE2": "",
    "ATTRIBUTE3": "",
    "ATTRIBUTE4": "",
    "ATTRIBUTE5": "",
    "ATTRIBUTE6": "",
    "ATTRIBUTE7": "",
    "ATTRIBUTE8": "",
    "ATTRIBUTE9": "",
    "ATTRIBUTE10": "",
    "ATTRIBUTE11": "",
    "ATTRIBUTE12": "",
    "ATTRIBUTE13": "",
    "ATTRIBUTE14": "",
    "ATTRIBUTE15": "",
    "OrderLine": [
      {
        "LINEID": "123",
        "HEADERID": "123",
        "ORDERLINE_NUMBER": "123",
        "ORDERLINE_ORACLE_ID": "123",
        "ORDERLINE_CRM_ID": "123",
        "PRODUCT_MODEL": " SPT2008-05 ",
        "ORDER_QUANTITY": " 1 ",
        "RESOURCE_ORDERNUMBER": "",
        "RESOURCE_HEADERID": "",
        "RESOURCE_LINENUMBER": "",
        "RESOURCE_LINEID": "",
        "CANCELLED_QTY": "1",
        "LIST_PRICE": "",
        "UNIT_SELLING_PRICE": "100",
        "REQUEST_DATE": "2019-12-31  00:00:00",
        "BACK_ORDER_QTY": "1",
        "RELEASE_TO_WAREHOUSE_QTY": "1",
        "PICK_AND_PACKED_QTY": "1",
        "SHIPPED_QTY": "1",
        "INVOICED_QTY": "1",
        "EST_REPLENISH_DATE": "2019-12-31  00:00:00",
        "PRODUCT_DESCRIPTION": "123",
        "UOM": "12",
        "TOTAL_NET_PRICE": "123",
        "REMARK": "123",
        "ATTRIBUTE1": "",
        "ATTRIBUTE2": "",
        "ATTRIBUTE3": "",
        "ATTRIBUTE4": "",
        "ATTRIBUTE5": "",
        "ATTRIBUTE6": "",
        "ATTRIBUTE7": "",
        "ATTRIBUTE8": "",
        "ATTRIBUTE9": "",
        "ATTRIBUTE10": "",
        "ATTRIBUTE11": "",
        "ATTRIBUTE12": "",
        "ATTRIBUTE13": "",
        "ATTRIBUTE14": "",
        "ATTRIBUTE15": "",
        "OrderLineAdjustment": [
          {
            "ADJUSTMENTID": "123",
            "LINEID": "123",
            "ORDERLINE_ORACLE_ID": "123",
            "ORDERHEADER_ORACLE_ID": "123",
            "TYPE": "Discount",
            "NAME": "Discount",
            "LIST_LINE_NO": "",
            "RATE": "0.1",
            "AMOUNT": "10",
            "ATTRIBUTE1": "",
            "ATTRIBUTE2": "",
            "ATTRIBUTE3": "",
            "ATTRIBUTE4": "",
            "ATTRIBUTE5": "",
            "ATTRIBUTE6": "",
            "ATTRIBUTE7": "",
            "ATTRIBUTE8": "",
            "ATTRIBUTE9": "",
            "ATTRIBUTE10": "",
            "ATTRIBUTE11": "",
            "ATTRIBUTE12": "",
            "ATTRIBUTE13": "",
            "ATTRIBUTE14": "",
            "ATTRIBUTE15": ""
          }
        ]
      }
    ]
  }
]
 * Author : Honey
 * Date : 2023/0602
 * Description: 从外部接口获取Order信息并存入数据库
*/
@RestResource(urlMapping='/CCM_RestService_DealOrderInfoEU')
global without sharing class CCM_UpsertOrder {
    global static final String SCENE_TYPE_SALES_ORDER = 'Sales Order';
    global static final String SCENE_TYPE_TRAINING_ORDER = 'Training Request';
    global static final String SCENE_TYPE_WARRANTY_ORDER = 'Warranty Order';
    global static final String SCENE_TYPE_WARRANTY_CLAIM = 'Warranty Claim';
    global static final String SCENE_TYPE_INVOICE_DAMAGE = 'Invoice Damage';
    global static final String SCENE_TYPE_RETURN_DISPOSAL = 'Return Disposal';
    global static final String SCENE_TYPE_RESALES_REQUEST = 'Resales Request';
    global static final String INTEGRATOIN_USERNAME = 'Integration';
    global static final String ERP_USERNAME_ANONYMOUS = 'Users Who Do Not Log';
    global static final String ERP_USERNAME_REQUEST_USER = 'Request To Run User';
    global static final String ERP_USERNAME_SYSADMIN = 'System Administrator';
    //recordType
    public static final Id RT_INVOICE_DAMAGE = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Invoice_Damage').getRecordTypeId();
    public static final Id RT_RETURN_DISPOSAL = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Retrun_Disposal').getRecordTypeId();
    public static final Id RT_RESALES_REQUEST = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Resales_Request').getRecordTypeId();
    //add start vince 2023-10-31
    public static final Id RT_WARRANTY_CLAIM = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Warranty_Claim').getRecordTypeId();
    public static final Id RT_WARRANTY_ORDER = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Warranty_Order').getRecordTypeId();
    //add end vince 2023-10-31
    @HttpPost
    global static ResultObj doPost() {
        RestRequest req = RestContext.request;
        List<ReqestObj> reqObjList = new List<ReqestObj>();
        ResultObj resObj = new ResultObj();
        resObj.Process_Result = new List<ReturnItem>();
        String resStr = null;
        try {
            resStr = req.requestBody.toString();
            resStr = resStr.replace('\'', '\\\'');
            // System.debug(LoggingLevel.INFO, '*** req.requestBody.toString(): ' + req.requestBody.toString());
            if(!resStr.startsWith('[')){
                resStr = '[' + resStr + ']';
            }
            resStr = resStr.replace('\'','\"');
            reqObjList = parse(resStr);
            System.debug(LoggingLevel.INFO, '*** req.requestBody(): ' + reqObjList);
            //遍历reqObj用于创建Order\OrderLine\OderLineAdjustment
            List<Order> lstUpsertOrder = new List<Order>();
            List<Order_Item__c> lstUpsertOrderItem = new List<Order_Item__c>();
            List<Order_Line_Adjustment__c> lstUpsertAdjustmentLine = new List<Order_Line_Adjustment__c>();
            //创建唯一标识到具体对象的映射
            Map<String,Order> mapId2Order = new Map<String,Order>();
            Map<String,Order_Item__c> mapId2OrderLine = new Map<String,Order_Item__c>();
            Map<String,Order_Line_Adjustment__c> mapId2OrderAdjustment = new Map<String,Order_Line_Adjustment__c>();
            //校验是否需要插入或更新通过各种Id
            Set<String> setOrderId = new Set<String>();
            Set<String> setOrderLineId = new Set<String>();
            Set<String> setAdjustmentId = new Set<String>();
            Set<String> setCustomerNumber = new Set<String>();
            //设置Product 的Model获取产品信息
            Set<String> setProductModel = new Set<String>();
            //设置PriceBook的Name
            Set<String> setPriceBookName = new Set<String>();
            //设置ShipTo和Bill To
            Set<String> setShipToOracleId = new Set<String>();
            Set<String> setBillToOracleId = new Set<String>();
            Set<String> setOracleId = new Set<String>();
            //查询Address
            Set<String> setAddressName = new Set<String>();
            //Sales Rep Email
            Set<String> setRepEmail = new Set<String>();
            //根据PO Number找到PurchaseOrder信息
            Set<String> SetPoNumber = new Set<String>();
            //设置DamageItem、ResaleItem、DisposalItem的Id
            Set<String> setDamageItemId = new Set<String>();
            Set<String> setResaleItemId = new Set<String>();
            Set<String> setDisposalItemId = new Set<String>();
            //设置StockItemId
            Set<String> setStockItemId = new Set<String>();
            //保存training order 报文
            List<ReqestObj> lstTrainingReqObj = new List<ReqestObj>();
            //PO的报文
            List<ReqestObj> errorReqest = new List<ReqestObj>();
            //PO的name集合
            Set<String> errorPoNames = new Set<String>();
            //PO Item的name集合
            Set<String> PoItemNames = new Set<String>();
            Datetime today = Datetime.now();
            
            Map<String, OrderType_Mapping__c> orderTypeMap = new Map<String, OrderType_Mapping__c>();
            for(OrderType_Mapping__c mapping : [SELECT Id, Object_Type__c, Oracle_Order_type__c, Order_Type__c, Ship_to_Country__c, Warehouse__c, Order_Type_In_EBS__c
                                                FROM OrderType_Mapping__c 
                                                WHERE Object_Type__c = 'Order' order by Oracle_Order_type__c, Order_Type__c]) {
                orderTypeMap.put(mapping.Oracle_Order_type__c, mapping);
            }

            Map<String,List<Order_Item__c>> mapCode2lstOrderItem = new Map<String,List<Order_Item__c>>();
            for(ReqestObj objOrder : reqObjList){
                if(objOrder.ATTRIBUTE4 == 'PO'){
                    // 收集所有的无效PO的报文
                    errorReqest.add(objOrder);
                    errorPoNames.add(objOrder.ORDER_NUMBER_CRM);
                    for(OrderItem objItem : objOrder.OrderLine){
                        PoItemNames.add(objItem.ORDERLINE_CRM_ID);
                    }
                    continue;
                }else if(objOrder.ATTRIBUTE4 == 'ORDER'){
                    if(objOrder.SCENE_TYPE.equals(SCENE_TYPE_TRAINING_ORDER)){
                        System.debug('进入training order 处理分支');
                        lstTrainingReqObj.add(objOrder);
                        continue;
                    }
                    SetPoNumber.add(objOrder.ORDER_NUMBER_CRM);
                    setRepEmail.add(objOrder.SALES_REP_EMAIL);
                    setAddressName.add(objOrder.DROPSHIP_NAME);
                    setShipToOracleId.add(objOrder.SHIPTO);
                    setBillToOracleId.add(objOrder.BILLTO);
                    setOracleId.add(objOrder.SHIPTO);
                    setOracleId.add(objOrder.BILLTO);
                    setPriceBookName.add(objOrder.PRICE_LIST);
                    setOrderId.add(objOrder.ORDER_ORACLE_ID);
                    setCustomerNumber.add(objOrder.CUSTOMER);
                    Order objUpsertOrder = new Order();
                    system.debug('new Order');
                    SetOrderValue(objOrder, objUpsertOrder, orderTypeMap);
                    mapId2Order.put(objOrder.ORDER_ORACLE_ID,objUpsertOrder);
                    system.debug('Order-->'+ objOrder);
                    if(objOrder.OrderLine == null){
                        continue;
                    }
                    // head status needs to be booked
                    Boolean isAllLineStatusClosed = true;
                    if(!CCM_Constants.BOOKED.Equals(objOrder.ORDER_STATUS)){ 
                        isAllLineStatusClosed = false;
                    }
                    //设置完Oder后遍历Item
                    for(OrderItem objItem : objOrder.OrderLine){

                        if(SCENE_TYPE_INVOICE_DAMAGE == objOrder.SCENE_TYPE){
                            //判断场景--Invoice Damage,记录行id
                            setDamageItemId.add(objItem.ORDERLINE_CRM_ID);
                        }
                        if(SCENE_TYPE_RETURN_DISPOSAL == objOrder.SCENE_TYPE){
                            //判断场景--Return Disposal,记录行id
                            setDisposalItemId.add(objItem.ORDERLINE_CRM_ID);
                        }
                        if(SCENE_TYPE_RESALES_REQUEST == objOrder.SCENE_TYPE){
                            //判断场景--Resales,记录行id
                            setResaleItemId.add(objItem.ORDERLINE_CRM_ID);
                        }
                        setProductModel.add(objItem.PRODUCT_MODEL);
                        setOrderLineId.add(objItem.ORDERLINE_ORACLE_ID);
                        Order_Item__c objOrderItem = new Order_Item__c();
                        SetOrderLineValue(objItem, objOrderItem, objOrder);
                        if(!CCM_Constants.CLOSED.equals(objItem.ORDER_LINE_STATUS)) {
                            isAllLineStatusClosed = false;
                        }
                        List<Order_Item__c> lstOrderItemByCode = mapCode2lstOrderItem.containsKey(objOrder.ORDER_ORACLE_ID)?
                            mapCode2lstOrderItem.get(objOrder.ORDER_ORACLE_ID) : new List<Order_Item__c>();
                        lstOrderItemByCode.add(objOrderItem);
                        mapCode2lstOrderItem.put(objOrder.ORDER_ORACLE_ID,lstOrderItemByCode);
                        system.debug('objOrderItem-->'+objOrderItem);
                        system.debug('OrderLineAdjustment-->'+objItem.OrderLineAdjustment);
                        mapId2OrderLine.put(objItem.ORDERLINE_ORACLE_ID,objOrderItem);
                        if(objItem.OrderLineAdjustment == null){
                            continue;
                        }
                        //遍历Adjustment
                        if(objItem.OrderLineAdjustment != null){
                            for(OrderLineAdjustment objAdjustment  : objItem.OrderLineAdjustment){
                                setAdjustmentId.add(objAdjustment.ADJUSTMENTID);
                                Order_Line_Adjustment__c objUpsertAdjustment = new Order_Line_Adjustment__c();
                                SetOrderAdjustment(objAdjustment,objUpsertAdjustment,objOrder);
                                mapId2OrderAdjustment.put(objAdjustment.ADJUSTMENTID,objUpsertAdjustment);
                                
                            }
                        }
                    }
                    if(isAllLineStatusClosed) {
                        objUpsertOrder.Order_Status__c = CCM_Constants.SHIPMENT_COMPLETE;
                    }
                }
            }

            // 处理PO
            if(errorReqest.size() > 0){
                dealPurchaseOrder(errorReqest, errorPoNames, PoItemNames, today);
            }else{
                //如果当前批次有training order,走training的处理逻辑
                if(lstTrainingReqObj.size() > 0){
                    System.debug('调用deal training api');
                    resObj = CCM_RestService_DealTrainingOrder.dealTrainingOrderInfo(resObj,lstTrainingReqObj);
                }else{
                    System.debug('处理普通订单-----》');
                    //根据PoNumber查询Po 
                    List<Purchase_Order__c> lstPurchase  = [
                        SELECT Status__c,Name FROM Purchase_Order__c WHERE Name IN : SetPoNumber
                    ];
                    Map<String,String> mapNum2Id = new Map<String,String>();
                    for(Purchase_Order__c objPurchaseOrder : lstPurchase){
                        objPurchaseOrder.Status__c = 'Booked';
                        mapNum2Id.put(objPurchaseOrder.Name, objPurchaseOrder.Id);
                    }
                    Database.update(lstPurchase,false);
                    //根据Po NUmber查询Po Item的信息
                    List<Purchase_Order_Item__c> lstPutchaseOrderItem = [
                        SELECT Id,Schedule_Ship_Date__c,Est_Replenish_Date__c,Sales_Agreement_No__c,Product__c,Purchase_Order__r.Expected_Delivery_Date__c,
                        Request_Date__c,Purchase_Order__c,Purchase_Order__r.Customer_PO_Num__c,Purchase_Order__r.Name,
                        Purchase_Order__r.DropShip_Type__c
                        FROM Purchase_Order_Item__c WHERE Purchase_Order__r.Name IN :SetPoNumber 
                    ];
                    Map<String,Purchase_Order_Item__c> mapProduct2OrderItem = new Map<String,Purchase_Order_Item__c>();
                    Map<String,String> mapCustomePo2Number = new Map<String,String>();
                    Map<String,Date> mapCustomer2Date = new Map<String,Date>();
                    Map<String,String> mapCustomer2Type = new Map<String,String>();
                    for(Purchase_Order_Item__c objPurchaseItem : lstPutchaseOrderItem ){
                        mapProduct2OrderItem.put(objPurchaseItem.Product__c+''+objPurchaseItem.Purchase_Order__r.Name,objPurchaseItem);
                    
                        mapCustomer2Date.put(objPurchaseItem.Purchase_Order__r.Name, objPurchaseItem.Purchase_Order__r.Expected_Delivery_Date__c);
                        mapCustomer2Type.put(objPurchaseItem.Purchase_Order__r.Name, objPurchaseItem.Purchase_Order__r.DropShip_Type__c);
                    }
                    //通过Email查询Id
                    List<User> lstUser = [
                        SELECT Id,Email FROM User Where Email IN : setRepEmail
                    ];
                    Map<String,String> mapEmail2Id = new Map<String,String>();
                    for(User objUser : lstUser){
                        String lowercaseKey = objUser.Email == null ? '' :  objUser.Email.toLowerCase();
                        mapEmail2Id.put(lowercaseKey,objUser.Id);
                    }
                    //通过setAddressName和Customer绑定DropShip
                    List<Account_Address__c> lstAccountAddress = [
                        SELECT Id,Name , Customer__c,Customer__r.AccountNumber FROM Account_Address__c WHERE Name IN : setAddressName AND Customer__r.AccountNumber IN :setCustomerNumber
                    ];
                    Map<String,String> mapCustomerName2Id = new Map<String,String>();
                    for(Account_Address__c objAccountAddress : lstAccountAddress){
                        mapCustomerName2Id.put(objAccountAddress.Customer__r.AccountNumber + objAccountAddress.Name, objAccountAddress.Id);
                    }
                    //通过OracleId查询 Address信息
                    List<Address_With_Program__c> lstAddress = [
                        SELECT Id,Customer_Line_Oracle_ID__c,Account_Address__c FROM Address_With_Program__c WHERE Customer_Line_Oracle_ID__c IN :setOracleId
                    ];
                    Map<String,Address_With_Program__c> mapShip2Id = new Map<String,Address_With_Program__c>();
                    Map<String,Address_With_Program__c> mapBill2Id = new Map<String,Address_With_Program__c>();
                    for(Address_With_Program__c objAddress : lstAddress){
                        if(setShipToOracleId.contains(objAddress.Customer_Line_Oracle_ID__c)){
                            mapShip2Id.put(objAddress.Customer_Line_Oracle_ID__c,objAddress);
                        }
                        
                        if(setBillToOracleId.contains(objAddress.Customer_Line_Oracle_ID__c)){
                            mapBill2Id.put(objAddress.Customer_Line_Oracle_ID__c,objAddress);
                        }
                    }
                    system.debug('mapBill2Id-->'+mapBill2Id);
                    //通过setPriceBookName查询Price Book的id
                    List<Pricebook2> lstPriceBook = [
                        SELECT Id,Name FROM Pricebook2 WHERE Name IN : setPriceBookName
                    ];
                    Map<String,String> mapBookName2Id = new Map<String,String>();
                    for(Pricebook2 objPriceBook : lstPriceBook){
                        mapBookName2Id.put(objPriceBook.Name,objPriceBook.Id);
                    }
                    //通过CustomerNumber去Customer表中查询Customer Id
                    List<Account> lstAccount = [
                        SELECT Id ,AccountNumber FROM Account WHERE AccountNumber IN : setCustomerNumber
                    ];
                    //通过setProductModel 去Product表中查询Product Id
                    List<Product2> lstProduct = [
                        SELECT Id ,Order_Model__c FROM Product2 WHERE Order_Model__c IN : setProductModel
                    ];
                    Map<String,String> mapModel2ProductId = new Map<String,String>();
                    for(Product2 objProduct : lstProduct){
                        mapModel2ProductId.put(objProduct.Order_Model__c,objProduct.Id);
                    }
                    Map<String,String> mapAccountNumber2Id = new Map<String,String>();
                    for(Account objAccount : lstAccount){
                        mapAccountNumber2Id.put(objAccount.AccountNumber,objAccount.Id);
                    }
                    //通过OrderId查询Order信息--->用于校验是否重复
                    List<Order> lstExitOrder = [
                        SELECT Id ,Order_OracleID__c  FROM Order WHERE  Order_OracleID__c IN : setOrderId
                    ];
                    List<Order_Item__c> lstExitOrderLine = [
                        SELECT Id ,OrderLine_OracleID__c  FROM Order_Item__c WHERE  OrderLine_OracleID__c IN : setOrderLineId
                    ];
                    system.debug('lstExitOrderLine-->'+lstExitOrderLine);
                    List<Order_Line_Adjustment__c> lstExitOrderAdjustment = [
                        SELECT Id ,Adjustment_Id__c,Order_Item__r.Order__c,Order_Item__r.Order_OracleID__c   FROM Order_Line_Adjustment__c WHERE  Order_Item__r.Order_OracleID__c IN : setOrderId
                    ];
                    //先删除再插入
                    delete lstExitOrderAdjustment;
                    system.debug('需要插入的数据Order1-->'+lstUpsertOrder.size());
                    CheckUpdateOrInsertOrder(lstExitOrder,mapId2Order,lstUpsertOrder,mapBookName2Id);
                    //对需要插入的数据计算Vat
                    for(Order objOrder : lstUpsertOrder){
                        List<Order_Item__c> lstOrderItem = mapCode2lstOrderItem.get(objOrder.Order_OracleId__c);
                        Double FerightFee = 0;
                        Double Insurancefee = 0;
                        Double Otherfee = 0;
                        if(lstOrderItem != null && lstOrderItem.size()>0){
                            for(Order_Item__c objItem : lstOrderItem){
                                if(CCM_Constants.INSURANCE_FEE.equals(objItem.Product_Text__c)){
                                                            
                                    Insurancefee += objItem.Total_Net_Price__c;
                                }else if(CCM_Constants.OTHER_FEE.equals(objItem.Product_Text__c)){
                                    Otherfee += objItem.Total_Net_Price__c;
                                
                                }else if(CCM_Constants.FREIGHT_FEE.equals(objItem.Product_Text__c)){
                                    FerightFee += (objItem.Unit_Selling_Price__c == null ? 0: objItem.Unit_Selling_Price__c);
                                
                                }
                            }
                        }
                        system.debug('FerightFee-->'+FerightFee);
                        system.debug('objOrder.Total_Amount__c-->'+objOrder.Total_Amount__c);
                        system.debug('objOrder.Total_value_Net__c-->'+objOrder.Total_value_Net__c);
                        system.debug('Total_Tax__c运费前-->'+objOrder.Total_Tax__c);
                        objOrder.Total_Tax__c = (objOrder.Total_Tax__c == null ? 0 : objOrder.Total_Tax__c ) - FerightFee - Insurancefee - Otherfee;
                        objOrder.Total_Tax__c = objOrder.Total_Tax__c <0 ? 0 : objOrder.Total_Tax__c;
                        objOrder.Feright_Fee__c = FerightFee;
                        objOrder.Feright_Fee__c = FerightFee == 0 ? null : FerightFee ;
                        objOrder.Insurance_fee__c = Insurancefee == 0 ? null : Insurancefee;
                        objOrder.Other_fee__c = Otherfee == 0 ? null : Otherfee ;
                        objOrder.Purchase_Order__c = mapNum2Id.get(objOrder.Order_Number_CRM__c);
                        
                        system.debug('Order_Number_CRM__c--->'+objOrder.Order_Number_CRM__c);
                        system.debug('mapCustomer2Date--->'+mapCustomer2Date);
                        system.debug('mapCustomer2Type--->'+mapCustomer2Type);
                        objOrder.Expected_Delivery_Date__c = mapCustomer2Date.get(objOrder.Order_Number_CRM__c);
                        objOrder.Dropship_Type__c = mapCustomer2Type.get(objOrder.Order_Number_CRM__c);
                        system.debug('Total_Tax__c-->'+objOrder.Total_Tax__c);
                    }            
                
                    //设置AccountId
                    for(Order objOrder : lstUpsertOrder){
                        if(mapEmail2Id.containsKey(objOrder.Sales_Rep_Email__c)){
                            String lowercaseKey = objOrder.Sales_Rep_Email__c == null ? '' :  objOrder.Sales_Rep_Email__c.toLowerCase();
                            objOrder.Sales_Rep__c = mapEmail2Id.get(lowercaseKey);
                        }
                        system.debug('mapAccountNumber2Id-->'+mapAccountNumber2Id);
                        if(mapCustomerName2Id.containsKey(objOrder.Account_Text__c + objOrder.Dropship_Name__c)){
                            //包含则设置Account Id
                            objOrder.Dropship_Address__c = mapCustomerName2Id.get(objOrder.Account_Text__c + objOrder.Dropship_Name__c);
                            
                        }
                        if(mapAccountNumber2Id.containsKey(objOrder.Account_Text__c)){
                            //包含则设置Account Id
                            objOrder.AccountId = mapAccountNumber2Id.get(objOrder.Account_Text__c);
                            objOrder.Customer_EU__c = mapAccountNumber2Id.get(objOrder.Account_Text__c);
                        }
                        Address_With_Program__c objAddressShip = mapShip2Id.get(objOrder.ShipTo_Text__c);
                        if(objAddressShip != null){
                            //包含则设置Account Id
                            objOrder.ShipTo__c = objAddressShip.Id;
                            objOrder.Ship_To_Address__c = objAddressShip.Account_Address__c;
                        }
                        Address_With_Program__c objAddressBill = mapBill2Id.get(objOrder.BillTo_Text__c);
                        system.debug('objAddressBill');
                        if(objAddressBill != null){
                            //包含则设置Account Id
                            objOrder.BillTo__c = objAddressBill.Id;
                            objOrder.Bill_To_Address__c = objAddressBill.Account_Address__c;
                        }
                    }
                    
                
                    system.debug('需要插入的数据Order-->'+lstUpsertOrder.size());
                    
                    List<String> lstOrderItemIds = new List<String>();
                    Map<String,String> mapOrderId2Id = new Map<String,String>();
                    if(lstUpsertOrder.size()>0){
                        Database.UpsertResult[] resOrderList= Database.upsert(lstUpsertOrder,false);
                        for (Integer i = 0 ; i < resOrderList.size() ; i++) {
                            if (!resOrderList.get(i).isSuccess()) {
                                Database.Error[] err = resOrderList.get(i).getErrors();
                                ReturnItem request = new ReturnItem();
                                system.debug('String.valueOf(lstUpsertOrder.get(i).Order_OracleID__c)--->'+String.valueOf(lstUpsertOrder.get(i).Order_OracleID__c));
                                request.External_Id = String.valueOf(lstUpsertOrder.get(i).Order_OracleID__c);
                                request.Error_Message = 'This Order was failed saving in Salesforce';
                                request.Error_Detail = '***The following error has occurred***' + err;
                                resObj.Process_Result.add(request);
                            }else{
                                //表示更新或插入成功
                                mapOrderId2Id.put(String.valueOf(lstUpsertOrder.get(i).Order_OracleID__c),lstUpsertOrder.get(i).Id);
                                
                            }
                        }
                    }
                    system.debug('mapOrderId2Id-->'+mapOrderId2Id);
                    // 插入Order后获取Order的Id ,用于关联Order Item
                    CheckUpdateOrInsertOrderLine(lstExitOrderLine,mapId2OrderLine,lstUpsertOrderItem,mapOrderId2Id, mapModel2ProductId);
                
                    
                    //根据ProductId和PurchaseOrder匹配信息--》为OrderItem联动
                    List<Purchase_Order_Item__c> lstUpdatePurchaseOrderItem = new  List<Purchase_Order_Item__c>();
                    for(Order_Item__c objOrderItem : lstUpsertOrderItem){
                        Purchase_Order_Item__c objPurchasePrderItem = mapProduct2OrderItem.get(objOrderItem.Product__c+objOrderItem.Po_Number__c);
                        if(objPurchasePrderItem != null){
                            objPurchasePrderItem.Est_Replenish_Date__c = objOrderItem.Est_Replenish_Date__c;
                            objPurchasePrderItem.Request_Date__c = objOrderItem.Request_Date__c;
                            objPurchasePrderItem.Schedule_Ship_Date__c = objOrderItem.Schedule_Ship_Date__c;
                            objPurchasePrderItem.Sales_Agreement_No__c = objOrderItem.Sales_Agreement_No__c; 
                            lstUpdatePurchaseOrderItem.add(objPurchasePrderItem);
                        }
                    
                        
                    }
                    if(lstUpdatePurchaseOrderItem.size()>0){
                        update lstUpdatePurchaseOrderItem; 
                    }
                    
                    system.debug('需要插入的数据OrderLine-->'+lstUpsertOrderItem.size());
                    Map<String,String> mapOrderLineId2Id = new Map<String,String>();
                    if(lstUpsertOrderItem.size()>0){
                        Database.UpsertResult[] resOrderLineList= Database.upsert(lstUpsertOrderItem,false);
                        for (Integer i = 0 ; i < resOrderLineList.size() ; i++) {
                            if (!resOrderLineList.get(i).isSuccess()) {
                                Database.Error[] err = resOrderLineList.get(i).getErrors();
                                ReturnItem request = new ReturnItem();
                                request.External_Id = String.valueOf(lstUpsertOrderItem.get(i).OrderLine_OracleID__c);
                                request.Error_Message = 'This Order Item was failed saving in Salesforce';
                                request.Error_Detail = '***The following error has occurred***' +err ;
                                resObj.Process_Result.add(request);
                            }else{
                                //表示插入成功获取Oracle到Id的映射信息
                                mapOrderLineId2Id.put(lstUpsertOrderItem.get(i).OrderLine_OracleID__c,lstUpsertOrderItem.get(i).Id);
                                lstOrderItemIds.add(lstUpsertOrderItem.get(i).Id);
                            }
                        }
                    }

                    //---began-- 0927 add vince 更新stock 库存
                    List<Invoice_Damage_Item__c> lstDamageLine= new List<Invoice_Damage_Item__c>();
                    List<Sales_RepStock_ReSale_SN__c> lstResaleLine= new List<Sales_RepStock_ReSale_SN__c>();
                    List<Sales_Rep_Stock_Return_Item__c> lstDisposalLine= new List<Sales_Rep_Stock_Return_Item__c>();
                    if(setDamageItemId.size() > 0){
                        lstDamageLine = [SELECT Id, Sales_Rep_Stock__c FROM Invoice_Damage_Item__c WHERE Id in :setDamageItemId];
                        if(lstDamageLine.size() > 0){
                            for(Invoice_Damage_Item__c idi : lstDamageLine){
                                if(idi.Sales_Rep_Stock__c != null){
                                    setStockItemId.add(idi.Sales_Rep_Stock__c);
                                }
                            }
                        }
                    }
                    if(setResaleItemId.size() > 0){
                        lstResaleLine = [SELECT Id, Sales_Rep_Stock__c FROM Sales_RepStock_ReSale_SN__c WHERE Id in :setResaleItemId];
                        if(lstResaleLine.size() > 0){
                            for(Sales_RepStock_ReSale_SN__c srrs : lstResaleLine){
                                if(srrs.Sales_Rep_Stock__c != null){
                                    setStockItemId.add(srrs.Sales_Rep_Stock__c);
                                }
                            }
                        }
                    }

                    if(setDisposalItemId.size() > 0){
                        lstDisposalLine = [SELECT Id, Sales_Rep_Stock__c FROM Sales_Rep_Stock_Return_Item__c WHERE Id in :setDisposalItemId];
                        if(lstDisposalLine.size() > 0){
                            for(Sales_Rep_Stock_Return_Item__c srsri : lstDisposalLine){
                                if(srsri.Sales_Rep_Stock__c != null){
                                    setStockItemId.add(srsri.Sales_Rep_Stock__c);
                                }
                            }
                        }
                    }
                    List<Sales_Rep_Stock__c> lstStockLine = new List<Sales_Rep_Stock__c>();
                    if(setStockItemId.size() > 0){
                        lstStockLine = [SELECT id,isDelete__c FROM Sales_Rep_Stock__c WHERE Id in :setStockItemId and isDelete__c = false];
                        if(lstStockLine.size() > 0){
                            for(Sales_Rep_Stock__c srs : lstStockLine){
                                srs.isDelete__c = true;
                            }
                            update lstStockLine;
                        }
                    }
                    //---end-- 0927 add vince 更新stock 库存
                    
                    CheckUpdateOrInsertOrderAdjustment(null,mapId2OrderAdjustment,lstUpsertAdjustmentLine,mapOrderLineId2Id);
                    system.debug('需要插入的数据OrderAdjustment-->'+lstUpsertAdjustmentLine);
                
                    if(lstUpsertAdjustmentLine.size()>0){
                        Database.UpsertResult[] resOrderAdjustmentList= Database.upsert(lstUpsertAdjustmentLine,false);
                        for (Integer i = 0 ; i < resOrderAdjustmentList.size() ; i++) {
                            if (!resOrderAdjustmentList.get(i).isSuccess()) {
                                Database.Error[] err = lstUpsertAdjustmentLine.get(i).getErrors();
                                ReturnItem request = new ReturnItem();
                                request.External_Id = String.valueOf(lstUpsertAdjustmentLine.get(i).Adjustment_Id__c);
                                request.Error_Message = 'This Order Adjustment was failed saving in Salesforce';
                                request.Error_Detail = '***The following error has occurred***' + err;
                                resObj.Process_Result.add(request);
                            }
                        }
                    }
                    if (resObj.Process_Result.size() == 0) {
                        resObj.Process_Status = 'Success';
                        String logId = Util.logIntegration('OrderService Log','CCM_UpsertOrder','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                    }else {
                        resObj.Process_Status = 'Fail';
                        String logId = Util.logIntegration('OrderService Exception','CCM_UpsertOrder','POST',JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
                        Util.pushExceptionEmail('Accept Order Info',logId,getMailErrorMessage(resObj));
                    }
                    //执行完毕以后计算数据---Honey Added ---.2023/07/11
                    Database.executeBatch(new CCM_OrderItemUpsertBatch(lstOrderItemIds));
                }
            }        
        } catch (Exception e) {
           
            resObj.Process_Status = 'Fail';
            ReturnItem request = new ReturnItem();
            request.Error_Message = 'This Order was failed saving in Salesforce';
            request.Error_Detail = '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage();
            resObj.Process_Result.add(request);
            System.debug(LoggingLevel.INFO, '*** e.getMessage(): '+ e.getLineNumber() +' line e.getMessage(): ' + e.getMessage());
            String logId = Util.logIntegration('OrderService Exception','CCM_UpsertOrder','POST',
                                               JSON.serialize(resObj.Process_Result),resStr, JSON.serialize(resObj));
            Util.pushExceptionEmail('Accept Order Info',logId,getMailErrorMessage(resObj));
            system.debug('错误的resObj--->'+resObj);
            return resObj;
        }
        system.debug('resObj--->'+resObj);
        return resObj;
    }
   //  @future 
    //public static void caculateInfo(List<String> lstOrderId){
      //  system.debug('lstOrderId-->'+lstOrderId);
        //Database.executeBatch(new CCM_OrderItemUpsertBatch(lstOrderId));
    //}
    
  
    global static void CheckUpdateOrInsertOrder(List<Order> lstExitOrder , Map<String,Order> mapId2Order, List<Order> lstUpsertOrder,Map<String,String> mapBookName2Id) {
        for(Order objExitOrder : lstExitOrder){
            if(mapId2Order.containsKey(objExitOrder.Order_OracleID__c)){
                //如果存在的列表中的OrderId包含传入的则是修改。将Id赋值
                Order objOrder = mapId2Order.get(objExitOrder.Order_OracleID__c);
                objOrder.Id = objExitOrder.Id;
                mapId2Order.put(objExitOrder.Order_OracleID__c,objOrder);
            }
        }
        for(String key : mapId2Order.keySet()){
            Order objOrder = mapId2Order.get(key);
            objOrder.Pricebook2Id = mapBookName2Id.get(objOrder.Price_Book_Text__c);
            lstUpsertOrder.add(objOrder);
        }
    }
    global static void CheckUpdateOrInsertOrderLine(List<Order_Item__c> lstExitOrderLine, Map<String,Order_Item__c> mapId2OrderLine, List<Order_Item__c> lstUpsertOrderItem, Map<String,String> mapOrderId2Id, Map<String,String> mapModel2ProductId) {
        for(Order_Item__c objExitOrderLine : lstExitOrderLine){
            if(mapId2OrderLine.containsKey(objExitOrderLine.OrderLine_OracleID__c)){
                //如果存在的列表中的OrderId包含传入的则是修改。将Id赋值
                Order_Item__c objOrderLine = mapId2OrderLine.get(objExitOrderLine.OrderLine_OracleID__c);
                objOrderLine.Id = objExitOrderLine.Id;
                mapId2OrderLine.put(objExitOrderLine.OrderLine_OracleID__c,objOrderLine);
            }
        }
        for(String key : mapId2OrderLine.keySet()){
            Order_Item__c objOrderItem = mapId2OrderLine.get(key);
            objOrderItem.Order__c = mapOrderId2Id.get(objOrderItem.Order_OracleId__c);
            system.debug('Order__c--->'+mapOrderId2Id.get(objOrderItem.Order_OracleId__c));
            objOrderItem.Product__c = mapModel2ProductId.get(objOrderItem.Product_Text__c);
 
            lstUpsertOrderItem.add(objOrderItem);
        }
    }
    global static void CheckUpdateOrInsertOrderAdjustment(List<Order_Line_Adjustment__c> lstExitOrderAdjustment ,   Map<String,Order_Line_Adjustment__c> mapId2OrderAdjustment ,   List<Order_Line_Adjustment__c> lstUpsertAdjustmentLine,Map<String,String> mapOrderLineId2Id ) {
       
        for(String key : mapId2OrderAdjustment.keySet()){
            Order_Line_Adjustment__c objOrderLineAdjustment = mapId2OrderAdjustment.get(key);
            objOrderLineAdjustment.Order_Item__c = mapOrderLineId2Id.get(objOrderLineAdjustment.OrderLine_Oracle_Id__c);
            lstUpsertAdjustmentLine.add(objOrderLineAdjustment);
        }
    }
    global static List<ReqestObj> parse(String jsonStr) {
        return (List<ReqestObj>) JSON.deserialize(jsonStr, List<ReqestObj>.class);
    }
    global static void SetOrderValue(ReqestObj objOrder, Order objUpsertOrder, Map<String, OrderType_Mapping__c> orderTypeMap) {
        objUpsertOrder.CurrencyIsoCode = objOrder.CURRENCY_CODE;
        
        objUpsertOrder.Header_Id__c =  objOrder.HEADERID;
        objUpsertOrder.remark__c =  objOrder.COMMENT_TO_CUSTOMER;
        objUpsertOrder.Order_OracleID__c  =  objOrder.ORDER_ORACLE_ID;
        objUpsertOrder.Order_Number__c   =  objOrder.ORDER_NUMBER_ORACLE;
        objUpsertOrder.Order_Number_CRM__c =  objOrder.ORDER_NUMBER_CRM;
        objUpsertOrder.Order_Header_Id__c =  objOrder.ORDER_HEADERID;
        objUpsertOrder.Account_Text__c =  objOrder.CUSTOMER;
        
        objUpsertOrder.ShipTo_Text__c =  objOrder.SHIPTO;
        objUpsertOrder.BillTo_Text__c =  objOrder.BILLTO;
       
        objUpsertOrder.Price_Book_Text__c =  objOrder.PRICE_LIST;
        objUpsertOrder.Sales_Rep_Email__c =  objOrder.SALES_REP_EMAIL;
        objUpsertOrder.Date_Order__c =  String.isBlank(objOrder.DATE_ORDER) ? null : Date.valueOf(objOrder.DATE_ORDER);
        //Order上面默认值等于Order Created In EBs
        objUpsertOrder.Pricing_Date__c = objUpsertOrder.Date_Order__c ;
        objUpsertOrder.EffectiveDate =  String.isBlank(objOrder.DATE_ORDER) ? null : Date.valueOf(objOrder.DATE_ORDER);
        objUpsertOrder.Payment_Term__c =  objOrder.PAYMENT_TERMS;
        objUpsertOrder.Freight_Term__c =  objOrder.FREIGHT_TERM;
        objUpsertOrder.Inco_Term__c=  objOrder.INCO_TERM;
        objUpsertOrder.Notes__c =  objOrder.SHPPING_PLACE;

        objUpsertOrder.Expected_Delivery_Date__c = String.isBlank(objOrder.EXPECTED_DELIVERY_DATE)  ? null : Date.valueOf(objOrder.EXPECTED_DELIVERY_DATE);
        objUpsertOrder.Feright_Fee__c =  String.isBlank(objOrder.FERIGHT_FEE)  ? null : Double.valueOf(objOrder.FERIGHT_FEE);
        //接口传过来的状态
        objUpsertOrder.Order_Status_in_EBS__c =  objOrder.ORDER_STATUS;
        //存在Mapping关系。参照PPt 
        //Booked->Order Processing    Closed-->Shipment Complete   Cancelled -->Cancelled
        if(CCM_Constants.BOOKED.Equals(objOrder.ORDER_STATUS)){
            objUpsertOrder.Order_Status__c = CCM_Constants.ORDER_PROCESS;
        }else if(CCM_Constants.CLOSED.Equals(objOrder.ORDER_STATUS)){
            objUpsertOrder.Order_Status__c = CCM_Constants.SHIPMENT_COMPLETE;
        }else if(CCM_Constants.CANCELLED_EBS.Equals(objOrder.ORDER_STATUS)){
            objUpsertOrder.Order_Status__c = CCM_Constants.CANCELLED;
        }

        objUpsertOrder.Order_Type_In_Oracle__c = objOrder.ORDER_TYPE;
        //2023/06/10 --Honey add-->添加record Type与WareHouse的映射---Start
        // if(CCM_Constants.EEG_SALES_DOMESTIC.equals(objOrder.ORDER_TYPE)){
        //     objUpsertOrder.Order_Type__c =  CCM_Constants.DSVR;
        //     objUpsertOrder.warehouse__c =  CCM_Constants.GERMANY_DSV;
        //     objUpsertOrder.recordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Regular_Order').getRecordTypeId();
        // }else if(CCM_Constants.EEG_PRESEASON_DOMESTIC.equals(objOrder.ORDER_TYPE)){
        //     objUpsertOrder.Order_Type__c =  CCM_Constants.DSVP;
        //     objUpsertOrder.warehouse__c =  CCM_Constants.GERMANY_DSV;
        //     objUpsertOrder.recordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('PreSeason_Order').getRecordTypeId();
        // }else if(CCM_Constants.EEG_DROPSHIPMENT_DOMESTIC.equals(objOrder.ORDER_TYPE)){
        //     objUpsertOrder.Order_Type__c =  CCM_Constants.DIP;
        //     objUpsertOrder.warehouse__c =  CCM_Constants.CHINA_DI;
        //     objUpsertOrder.recordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('PreSeason_Order').getRecordTypeId();
        // }
        if(orderTypeMap.containsKey(objOrder.ORDER_TYPE)) {
            OrderType_Mapping__c mapping = orderTypeMap.get(objOrder.ORDER_TYPE);
            objUpsertOrder.Order_Type__c = mapping.Order_Type_In_EBS__c;
            objUpsertOrder.Warehouse__c = mapping.Warehouse__c;
            if(mapping.Order_Type__c == 'Pre_season_Order') {
                objUpsertOrder.recordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('PreSeason_Order').getRecordTypeId();
            }
            else if(mapping.Order_Type__c == 'Regular_Order') {
                objUpsertOrder.recordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Regular_Order').getRecordTypeId();
            }
            else if(mapping.Order_Type__c == CCM_Constants.EEG_Influencer_EU_ORDER) {
                objUpsertOrder.recordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(CCM_Constants.EEG_Influencer_EU_ORDER).getRecordTypeId();
            }
        }

        if(SCENE_TYPE_INVOICE_DAMAGE == objOrder.SCENE_TYPE){
            //判断场景，绑定lookup关系--Invoice Damage
            objUpsertOrder.recordTypeId = RT_INVOICE_DAMAGE;
            objUpsertOrder.Invoice_Damage__c = objOrder.ORDER_NUMBER_CRM;
        }
        if(SCENE_TYPE_RETURN_DISPOSAL == objOrder.SCENE_TYPE){
            //判断场景，绑定lookup关系--Return Disposal
            objUpsertOrder.recordTypeId = RT_RETURN_DISPOSAL;
            objUpsertOrder.Return_Disposal__c = objOrder.ORDER_NUMBER_CRM;
        }
        if(SCENE_TYPE_RESALES_REQUEST == objOrder.SCENE_TYPE){
            //判断场景，绑定lookup关系--Resales
            objUpsertOrder.recordTypeId = RT_RESALES_REQUEST;
            objUpsertOrder.Resales_Request__c = objOrder.ORDER_NUMBER_CRM;
        }
        //add start vince 2032-10-31
        if(SCENE_TYPE_WARRANTY_CLAIM == objOrder.SCENE_TYPE){
            //判断场景，绑定lookup关系--warranty claim
            System.debug('warranty claim 场景处理------》');
            objUpsertOrder.recordTypeId = RT_WARRANTY_CLAIM;
            List<Warranty_Claim__c> claims = [SELECT Id FROM Warranty_Claim__c WHERE Name = :objOrder.ORDER_NUMBER_CRM];
            if(!claims.isEmpty()) {
                objUpsertOrder.Warranty_Claim__c = claims[0].Id;
            }
        }
        if(SCENE_TYPE_WARRANTY_ORDER == objOrder.SCENE_TYPE){
            //判断场景，绑定lookup关系--warranty order
            System.debug('warranty order 场景处理------》');
            objUpsertOrder.recordTypeId = RT_WARRANTY_ORDER;
            List<Warranty_Purchase_Order__c> wpos = [SELECT Id FROM Warranty_Purchase_Order__c WHERE Name = :objOrder.ORDER_NUMBER_CRM];
            if(!wpos.isEmpty()) {
                objUpsertOrder.Warranty_Purchase_Order__c = wpos[0].Id;
            }
        }
        //add end vince 2032-10-31
        objUpsertOrder.Pricing_Date__c = String.isNotBlank(objOrder.PRICE_DATE) ? Date.valueOf(objOrder.PRICE_DATE) : null;
        objUpsertOrder.Sales_Rep_Code__c = objOrder.SALES_REP_CODE;
        objUpsertOrder.Vat__c = String.isNotBlank(objOrder.VAT) ? Decimal.valueOf(objOrder.VAT) : null;
        /*else{
            objUpsertOrder.warehouse__c =  CCM_Constants.CHINA_DI;
            objUpsertOrder.Order_Type__c =  CCM_Constants.DIR;
            objUpsertOrder.recordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(CCM_Constants.REGULAR_ORDER).getRecordTypeId();
        }*/
        //----->end
        
        objUpsertOrder.Status = CCM_Constants.DRAFT;
        objUpsertOrder.Dropship_Name__c =  objOrder.DROPSHIP_NAME;
        objUpsertOrder.Dropship_Address1__c =  objOrder.DROPSHIP_ADDRESS1;
        objUpsertOrder.Dropship_Address2__c =  objOrder.DROPSHIP_ADDRESS2;
        objUpsertOrder.Dropship_Country__c =  objOrder.DROPSHIP_COUNTRY;
        objUpsertOrder.Dropship_ZIP__c =  objOrder.DROPSHIP_ZIP;
        objUpsertOrder.Dropship_State__c =  objOrder.DROPSHIP_STATE;
        objUpsertOrder.Dropship_City__c =  objOrder.DROPSHIP_CITY;
        objUpsertOrder.Instruction_to_DSV__c =  objOrder.INSTRUCTION_TO_DSV;
        objUpsertOrder.Carrier_Information__c =  objOrder.CARRIER_INFORMATION;
        
        objUpsertOrder.Total_Value__c =   String.isBlank(objOrder.TOTAL_VALUE)  ? null : Double.valueOf(objOrder.TOTAL_VALUE);
        objUpsertOrder.header_discount__c =   String.isBlank(objOrder.HEADER_DISCOUNT)  ? null : Double.valueOf(objOrder.HEADER_DISCOUNT);
        objUpsertOrder.header_discount_amount__c =   String.isBlank(objOrder.HEADER_DISCOUNT_AMOUNT)  ? null : Double.valueOf(objOrder.HEADER_DISCOUNT_AMOUNT);
        objUpsertOrder.Total_Value_Net__c =   String.isBlank(objOrder.TOTAL_VALUE_NET)  ? null : Double.valueOf(objOrder.TOTAL_VALUE_NET);
        objUpsertOrder.Total_Amount__c =   String.isBlank(objOrder.TOTAL_AMOUNT)  ? null : Double.valueOf(objOrder.TOTAL_AMOUNT);
        objUpsertOrder.Is_Dropship__c =  objOrder.IS_DROPSHIP;
        objUpsertOrder.Total_Tax__c = (objUpsertOrder.Total_Amount__c == null ? 0 : objUpsertOrder.Total_Amount__c) -  
            (objUpsertOrder.Total_Value_Net__c == null ? 0 : objUpsertOrder.Total_Value_Net__c);
        objUpsertOrder.PO_Number__c = objOrder.PO_NUMBER;
        //记录回传订单最后修改人
        if(objOrder.Attribute1.equals(ERP_USERNAME_ANONYMOUS) || objOrder.Attribute1.equals(ERP_USERNAME_REQUEST_USER) || objOrder.Attribute1.equals(ERP_USERNAME_SYSADMIN)){
            objUpsertOrder.Last_Update_By_In_EBS__c = INTEGRATOIN_USERNAME;
        }else{
            objUpsertOrder.Last_Update_By_In_EBS__c = objOrder.Attribute1;
        }
        objUpsertOrder.Comments__c = objOrder.Attribute2;
    }
    global static void SetOrderLineValue(OrderItem objOrderItem , Order_Item__c objUpsertOrderItem, ReqestObj objOrder) {
       	objUpsertOrderItem.CurrencyIsoCode = objOrder.CURRENCY_CODE;
        objUpsertOrderItem.Po_Number__c = objOrder.PO_NUMBER;
        objUpsertOrderItem.Pricing_Date__c = String.isBlank(objOrderItem.Attribute2) ? null : Date.valueOf(objOrderItem.Attribute2);
        //发货时间。需要接口传过来
        objUpsertOrderItem.Schedule_Ship_Date__c = String.isBlank(objOrderItem.SCHEDULE_SHIP_DATE) ? null : Date.valueOf(objOrderItem.SCHEDULE_SHIP_DATE);
        //需要接口传过来
        objUpsertOrderItem.Sales_Agreement_No__c = objOrderItem.SALES_AGREEMENT_NO;
        objUpsertOrderItem.Order_OracleId__c = objOrder.ORDER_ORACLE_ID;
        objUpsertOrderItem.Line_Id__c = objOrderItem.LINEID;
        objUpsertOrderItem.Header_Id__c = objOrderItem.HEADERID;
        objUpsertOrderItem.OrderLine_Number__c = objOrderItem.ORDERLINE_NUMBER;
        objUpsertOrderItem.OrderLine_OracleID__c = objOrderItem.ORDERLINE_ORACLE_ID;
        objUpsertOrderItem.OrderLine_CRM_Id__c = objOrderItem.ORDERLINE_CRM_ID;
        objUpsertOrderItem.Product_Text__c = objOrderItem.PRODUCT_MODEL;
        objUpsertOrderItem.Order_Quantity__c = String.isBlank(objOrderItem.ORDER_QUANTITY) ? null : Double.valueOf(objOrderItem.ORDER_QUANTITY);
        objUpsertOrderItem.Resource_Order_Number__c = objOrderItem.RESOURCE_ORDERNUMBER;
        objUpsertOrderItem.Resource_Header_Id__c = objOrderItem.RESOURCE_HEADERID;
        objUpsertOrderItem.Resource_Line_Number__c = objOrderItem.RESOURCE_LINENUMBER;
        objUpsertOrderItem.Resource_Line_Id__c = objOrderItem.RESOURCE_LINEID;
        objUpsertOrderItem.cancelled_qty__c = String.isBlank(objOrderItem.CANCELLED_QTY) ? null : Double.valueOf(objOrderItem.CANCELLED_QTY);
        objUpsertOrderItem.List_Price__c = String.isBlank(objOrderItem.LIST_PRICE) ? null : Double.valueOf(objOrderItem.LIST_PRICE);
        objUpsertOrderItem.Unit_Selling_Price__c = String.isBlank(objOrderItem.UNIT_SELLING_PRICE) ? null : Double.valueOf(objOrderItem.UNIT_SELLING_PRICE);
        objUpsertOrderItem.Unit_Net_Price__c = String.isBlank(objOrderItem.UNIT_SELLING_PRICE) ? null : Double.valueOf(objOrderItem.UNIT_SELLING_PRICE);

       // Decimal ListPrice = objUpsertOrderItem.List_Price__c == null ? 0 : objUpsertOrderItem.List_Price__c;
       // Decimal unitPrice = objUpsertOrderItem.Unit_Selling_Price__c == null ? 0 : objUpsertOrderItem.Unit_Selling_Price__c;
        
       // objUpsertOrderItem.Discount__c = ListPrice == 0 ? 0 : (ListPrice-unitPrice)/ListPrice;

        objUpsertOrderItem.Request_Date__c = String.isBlank(objOrderItem.REQUEST_DATE) ? null : Date.valueOf(objOrderItem.REQUEST_DATE);
        objUpsertOrderItem.Back_Order_Qty__c = String.isBlank(objOrderItem.BACK_ORDER_QTY) ? null : Double.valueOf(objOrderItem.BACK_ORDER_QTY);
        objUpsertOrderItem.Release_to_warehouse_Qty__c = String.isBlank(objOrderItem.RELEASE_TO_WAREHOUSE_QTY) ? null : Double.valueOf(objOrderItem.RELEASE_TO_WAREHOUSE_QTY);
        objUpsertOrderItem.Pick_and_Packed_Qty__c = String.isBlank(objOrderItem.PICK_AND_PACKED_QTY) ? null : Double.valueOf(objOrderItem.PICK_AND_PACKED_QTY);
        objUpsertOrderItem.Shipped_Qty__c = String.isBlank(objOrderItem.SHIPPED_QTY) ? null : Double.valueOf(objOrderItem.SHIPPED_QTY);
        objUpsertOrderItem.Invoiced_Qty__c = String.isBlank(objOrderItem.INVOICED_QTY) ? null : Double.valueOf(objOrderItem.INVOICED_QTY);
        objUpsertOrderItem.Est_Replenish_Date__c = String.isBlank(objOrderItem.EST_REPLENISH_DATE) ? null : Date.valueOf(objOrderItem.EST_REPLENISH_DATE);
        objUpsertOrderItem.Description__c = objOrderItem.PRODUCT_DESCRIPTION;
        objUpsertOrderItem.UOM__c = objOrderItem.UOM;
        objUpsertOrderItem.Order_Line_Status__c = objOrderItem.ORDER_LINE_STATUS;
        objUpsertOrderItem.Total_Net_Price__c = String.isBlank(objOrderItem.TOTAL_NET_PRICE) ? null : Double.valueOf(objOrderItem.TOTAL_NET_PRICE);
        objUpsertOrderItem.Remark__c= objOrderItem.REMARK;

        //vince add for sales rep stock 0810
        objUpsertOrderItem.Serial_Number__c = objOrderItem.SERIAL_NUMBER;
        objUpsertOrderItem.Sub_Inventory_Code__c = objOrderItem.SUB_INVENTORY_CODE;
        objUpsertOrderItem.Sales_Price__c = objOrderItem.SALES_PRICE;
        objUpsertOrderItem.Product_Status_Rating__c = objOrderItem.PRODUCT_RATING_STATUS;   


        //add vince 0925
        if(SCENE_TYPE_INVOICE_DAMAGE == objOrder.SCENE_TYPE){
            //判断场景，绑定lookup关系--Invoice Damage item
            objUpsertOrderItem.Invoice_Damage_Item__c = objOrderItem.ORDERLINE_CRM_ID;
        }
        if(SCENE_TYPE_RETURN_DISPOSAL == objOrder.SCENE_TYPE){
            //判断场景，绑定lookup关系--Return Disposal item
            objUpsertOrderItem.Return_Disposal_Item__c = objOrderItem.ORDERLINE_CRM_ID;
        }
    }
    global static void SetOrderAdjustment(OrderLineAdjustment objOrderAdjustment , Order_Line_Adjustment__c objUpsertAdjustment ,ReqestObj objOrder){
        objUpsertAdjustment.CurrencyIsoCode = objOrder.CURRENCY_CODE;
        objUpsertAdjustment.Adjustment_Id__c = objOrderAdjustment.ADJUSTMENTID;
        objUpsertAdjustment.Line_Id__c =  objOrderAdjustment.LINEID;
        objUpsertAdjustment.OrderLine_Oracle_Id__c = objOrderAdjustment.ORDERLINE_ORACLE_ID;
        objUpsertAdjustment.Order_Header_Oracle_Id__c = objOrderAdjustment.ORDERHEADER_ORACLE_ID;
        objUpsertAdjustment.Type__c= objOrderAdjustment.TYPE;
        objUpsertAdjustment.Name__c  = objOrderAdjustment.MODIFIER_NUMBER;
        objUpsertAdjustment.List_Line_No__c= objOrderAdjustment.LIST_LINE_NO;
        objUpsertAdjustment.Rate__c = String.isBlank(objOrderAdjustment.RATE) ? null : Double.valueOf(objOrderAdjustment.RATE);
        objUpsertAdjustment.Amount__c = String.isBlank(objOrderAdjustment.AMOUNT) ? null : Double.valueOf(objOrderAdjustment.AMOUNT);
    }
    // 处理PO
    global static void dealPurchaseOrder(List<ReqestObj> errorReqest, Set<String> errorPoNames, Set<String> PoItemNames, Datetime today){

        // 映射Reqest和PO
        Map<String, Purchase_Order__c> mapName2PO = new Map<String, Purchase_Order__c>();
        // 映射OrderLine和POItem
        Map<String, Purchase_Order_Item__c> mapName2POItem = new Map<String, Purchase_Order_Item__c>();
        // 待更新的PO
        Purchase_Order__c updatePo = new Purchase_Order__c();
        // 待更新的PO列表
        List<Purchase_Order__c> updatePos = new List<Purchase_Order__c>();
        // 待更新的PO Item
        Purchase_Order_Item__c updatePoItem = new Purchase_Order_Item__c();
        // 待更新的PO Item列表
        List<Purchase_Order_Item__c> updatePoItems = new List<Purchase_Order_Item__c>();

        for(Purchase_Order__c temp:[SELECT Id, Name FROM Purchase_Order__c WHERE Name IN : errorPoNames]){
            mapName2PO.put(temp.Name, temp);
        }
        for(Purchase_Order_Item__c temp:[SELECT Id, Name FROM Purchase_Order_Item__c WHERE Name IN : PoItemNames]){
            mapName2POItem.put(temp.Name, temp);
        }

        for(ReqestObj objOrder : errorReqest){
            if(mapName2PO.containsKey(objOrder.ORDER_NUMBER_CRM) == false){
                continue;
            }
            updatePo = mapName2PO.get(objOrder.ORDER_NUMBER_CRM);
            if(objOrder.ORDER_STATUS == 'S'){
                updatePo.Sync_Status__c = 'Success';
            }else if(objOrder.ORDER_STATUS == 'E'){
                updatePo.Sync_Status__c = 'Failed';
            }
            updatePo.Sync_Message__c = objOrder.Attribute3;
            updatePo.Sync_Date__c = today;
            updatePos.add(updatePo);
            for(OrderItem objItem : objOrder.OrderLine){
                if(mapName2POItem.containsKey(objItem.ORDERLINE_CRM_ID) == false){
                    continue;
                }
                updatePoItem = mapName2POItem.get(objItem.ORDERLINE_CRM_ID);
                updatePoItem.Sync_Status__c = objItem.ORDER_LINE_STATUS;
                updatePoItem.Sync_Message__c = objItem.Attribute3;
                updatePoItem.Sync_Date__c = today;
                updatePoItems.add(updatePoItem);
            }
        }
        update updatePos;
        update updatePoItems;
    }
    global class ResultObj {
        global String Process_Status;
        global List<ReturnItem> Process_Result;
    }
    public static String getMailErrorMessage(ResultObj res){
        String errContent = '';
        errContent += 'Process Status : Fail<br/><br/>';
        if(res.Process_Result.size() > 0){
            for(ReturnItem Item : res.Process_Result){
                errContent += 'External ID : ' + Item.External_Id + '<br/>';
                errContent += 'Error Message : ' + Item.Error_Message +'<br/>';
                errContent += 'Error Detail : '+ Item.Error_Detail +'<br/><br/>'; 
            }
        }
        return errContent;
    }
    global class ReqestObj {
        global String CURRENCY_CODE;
        global String HEADERID;
       
        global String ORDER_ORACLE_ID;
        global String ORDER_NUMBER_ORACLE;
        global String ORDER_NUMBER_CRM;
        global String ORDER_HEADERID;
        global String COMMENT_TO_CUSTOMER;
        global String CUSTOMER;
        global String PO_NUMBER;
        global String SHIPTO;
        global String BILLTO;
        global String ORDER_TYPE;
        global String PRICE_LIST;
        global String SALES_REP_EMAIL;
        global String DATE_ORDER;
        global String PAYMENT_TERMS;
        global String FREIGHT_TERM;
        global String INCO_TERM;
        global String SHPPING_PLACE;
        global String EXPECTED_DELIVERY_DATE;
        global String FERIGHT_FEE;
        global String ORDER_STATUS;
        global String DROPSHIP_NAME;
        global String DROPSHIP_ADDRESS1;
        global String DROPSHIP_ADDRESS2;
        global String DROPSHIP_COUNTRY;
        global String DROPSHIP_ZIP;
        global String DROPSHIP_STATE;
        global String DROPSHIP_CITY;
        global String INSTRUCTION_TO_DSV;
        global String CARRIER_INFORMATION;
        global String WAREHOUSE;
        global String TOTAL_VALUE;
        global String HEADER_DISCOUNT;
        global String HEADER_DISCOUNT_AMOUNT;
        global String TOTAL_VALUE_NET;
        global String TOTAL_AMOUNT;
        global String IS_DROPSHIP;
        //vince 0810 add for sales rep stock
        global String SCENE_TYPE;
        global String PRICE_DATE;
        global String SALES_REP_CODE;
        global String VAT;

        global String Attribute1;
        global String Attribute2;
        global String Attribute3;
        global String Attribute4;
        
        global List<OrderItem> OrderLine;
    }
    global class ReturnItem {
        global String External_Id;
        global String Error_Message;
        global String Error_Detail;
    }
    global class OrderItem {
        
        global String LINEID;
        global String HEADERID;
        global String ORDERLINE_NUMBER;
        global String ORDERLINE_ORACLE_ID;
        global String ORDERLINE_CRM_ID;
        global String PRODUCT_MODEL;
        global String ORDER_QUANTITY;
        global String RESOURCE_ORDERNUMBER;
        global String RESOURCE_HEADERID;
        global String RESOURCE_LINENUMBER;
        global String RESOURCE_LINEID;
        global String CANCELLED_QTY;
        global String LIST_PRICE;
        global String UNIT_SELLING_PRICE;
        global String REQUEST_DATE;
        global String BACK_ORDER_QTY;
        global String RELEASE_TO_WAREHOUSE_QTY;
        global String PICK_AND_PACKED_QTY;
        global String SHIPPED_QTY;
        global String INVOICED_QTY;
        global String EST_REPLENISH_DATE;
        global String PRODUCT_DESCRIPTION;
        global String UOM;
        global String TOTAL_NET_PRICE;
        global String REMARK;
        global String SCHEDULE_SHIP_DATE;
        global String SALES_AGREEMENT_NO;
        global String ORDER_LINE_STATUS;

        //vince 0810 add for sales rep stock
        global String SERIAL_NUMBER;
        global String SUB_INVENTORY_CODE;
        global String SALES_PRICE;
        global String PRODUCT_RATING_STATUS;
        
        // store pricing date
        global String Attribute2;
        global String Attribute3;

        global List<OrderLineAdjustment> OrderLineAdjustment;
    }
    global class OrderLineAdjustment {
        global String ADJUSTMENTID;
        global String LINEID;
        global String ORDERLINE_ORACLE_ID;
        global String ORDERHEADER_ORACLE_ID;
        global String LIST_LINE_NO;
        global String TYPE;
        global String MODIFIER_NUMBER;
        global String RATE;
        global String AMOUNT;
        
    }
}