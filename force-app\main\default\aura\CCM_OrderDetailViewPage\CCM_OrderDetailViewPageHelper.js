({
    getUrlParameter : function(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    },
    showToast : function(title, message, type){
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": title,
            "message": message,
            "type" : type
        });
        toastEvent.fire();
    },
    // customer提示信息
    queryAlertMessage : function(component, customerNumber) {
        var action = component.get('c.queryAlertMessage');
        action.setParams({
            CustomerNumber: customerNumber,
            AlertMode: 'Delivery',
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log(state, 'customer提示信息=========');
            if (state === 'SUCCESS' ) {
                var result = response.getReturnValue();
                console.log(result, 'customer提示信息 result=========');
                var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Success",
                   "message": result,
                   "type": "success",
                   "duration": "pester"
               }).fire();
            }else{
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].message,
                   "type": "error"
               }).fire();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    // 刷新红绿灯
    refreshLight: function(component, needNotification) {
        let self = this;
        console.log('刷新红绿灯=========');
        var action = component.get("c.refreshInventory");
        action.setParams({
            OrderId: component.get('v.recordId'),
        });
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                if(needNotification) {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Success",
                        "message": 'Refresh Traffic Light succeeded!',
                        "type": "success"
                    }).fire();
                }
                self.getBaseInfo(component);
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    getBaseInfo: function(component) {
        var action = component.get("c.getData");
        action.setParam('recordId', component.get('v.recordId'));
        component.set('v.isBusy', true);
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if (results.OrderItemData.length) {
                    results.OrderItemData.forEach((item)=>{
                        item.Discount = Number(item.Discount*100).toFixed(1) + '%';
                    })
                }
                component.set('v.orderItemList', results.OrderItemData);
            } else{
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
             };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },

    sortData: function(component, fieldName, direction) {
        let data = component.get("v.orderItemList");
        let cloneData = JSON.parse(JSON.stringify(data));
        
        cloneData.sort((a, b) => {
            let aValue = a[fieldName] || '';
            let bValue = b[fieldName] || '';
            
            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            if (aValue < bValue) {
                return direction === 'asc' ? -1 : 1;
            } else if (aValue > bValue) {
                return direction === 'asc' ? 1 : -1;
            }
            return 0;
        });
        
        component.set("v.orderItemList", cloneData);
    }
})