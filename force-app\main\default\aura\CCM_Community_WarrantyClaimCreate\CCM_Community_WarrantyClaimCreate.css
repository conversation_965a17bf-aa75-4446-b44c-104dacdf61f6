.THIS .slds-form-element__label{
   font-weight: bold;
   position: relative;
}
.THIS .slds-table thead th{
  background-color: #fafaf9;
}
.THIS .overtime{
   position: relative;
}
.THIS .overtime .overtimeInput{
   width: 80%;
   display: inline-block;
}
.THIS .overtime span.hour{
   position: absolute;
   top: 32px;
   left: 45%;
   border-left: 1px solid rgb(217, 219, 221);
   padding: 0 .25rem;
   border-radius: 0 .25rem .25rem 0;
}
.THIS .description{
   margin-left: -2.5rem;
}
.THIS .footer{
   text-align: right;
   color: black;
}
.THIS:after{
   content: '';
   display: block;
   clear: both;
}
.THIS .slds-form-element__label:empty{
   display: none;
}
.THIS .slds-grid_vertical-align-center{
   justify-content: center;
}
.THIS table .slds-truncate{
   text-align: center;
   overflow: visible;
}
.THIS .quantity .slds-input{
   width: 30%;
   text-align: center;
}
.THIS .slds-truncate{
   white-space: normal;
   word-break: break-all;
}
.THIS .slds-form-element__control.wordBreak{
   word-break: break-all;
}
.THIS .auditTrail li{
   color: #484848;
   padding: 0 .75rem;
}
@media screen and (min-width: 1500px){
 .THIS .auditTrail a{
     font-size: .9rem;
 }
}
.THIS .slds-theme--shade {
   background-color: #d7d7d7;
   color: #484848;
}
/* .THIS .greenBulb .slds-icon{
   fill: #90c41f;
} */
.THIS .yellowBulb .slds-icon{
   fill: #ebd834;
}
.THIS .redBulb .slds-icon{
   fill: #f00;
}
/* .THIS .slds-button.slds-button_brand{
   background: #90c41f;
   border-color: #90c41f;
} */
.THIS .slds-button.slds-button_neutral{
   color: #000;
}
.THIS table thead th:last-child{
   width: 9%;
}
.THIS .deleteBtn{
   cursor: pointer;
}
.THIS .slds-button.slds-button_brand.disabled{
   background: #ccc;
   border-color: #ccc;
}
.THIS .slds-checkbox .slds-checkbox__label .slds-form-element__label{
   font-size: .75rem;
}
.THIS .cCCM_Community_LookUp > .slds-form-element__control{
   display: block;
}
.THIS .cCCM_Community_LookUp .slds-form-element__control.slds-input-has-icon.slds-input-has-icon_right{
   width: 100%;
}
.THIS .lineHeight{
   line-height: 1.875rem;
}
.THIS .replacement{
   margin-top: 1.8rem;
}
.THIS .totalCon p{
   display: inline-block;
}
.THIS .slds-accordion__summary-heading{
   font-size: 1rem;
   line-height: 1.875;
   background: #d7d7d7;
   color: #484848;
   font-weight: bold;
   padding: 0 .75rem;
}
.THIS .slds-accordion__content{
   padding: 0 .75rem 1rem;
}
.THIS .slds-accordion__section{
   padding: 0;
}
.THIS .slds-accordion__summary-heading .slds-truncate{
   color: #484848;
}
.THIS .accordionCon lightning-primitive-icon{
   position: absolute;
   right: 0;
}
.THIS .accordionCon lightning-primitive-icon svg, .THIS .accordionCon .slds-button:hover .slds-button__icon{
   fill: #fff;
}
.THIS .slds-modal__container{
   max-width: initial;
   width: 80%;
}
.THIS .slds-modal__container.replacementModal{
   max-width: initial;
   width: 50%;
}
.THIS .slds-button.slds-button_reset.slds-accordion__summary-action{
   line-height: 2rem;
}
.THIS .laborHoursInput lightning-input{
   display: inline-block;
   width: 30%;
}
.THIS .explode a{
   text-decoration: underline;
   cursor: pointer;
}
.THIS .redColor{
   color: #f00;
}
.THIS .spinner{
   position: fixed;
   width: 100%;
   height: 100%;
   top: 0;
   bottom: 0;
   background: none;
}
.THIS .slds-modal__header{
   padding: 0;
   border: 0;
}
.THIS .modalSpinner{
   margin: 3rem 0;
}
/* .THIS tr.highLight{
   background-color: #90c41f;
} */
.THIS .backToTop{
   display: inline-block;
   line-height: 2.5;
   text-decoration: underline;
}
.THIS .forceContentFileCard{
   width: 100%!important;
}
.THIS .search-wrap {
   padding-right: 20px;
}
.THIS .baseInfo-wrap {
   display: flex;
   align-items: center;
   justify-content: space-between;
   width: 100%;
   height: auto;
   padding: 10px;
}
.THIS .receipt-info{
   border: 1px solid rgb(217, 219, 221);
   padding: 12px;
   display: flex;
   justify-content: center;
   border-radius: 5px;
}
.THIS .info-title {
   margin-right: 5px;
}
.THIS .info-title p {
   height: 18px;
   line-height: 18px;
   text-align: end;
}
.THIS .info-value {
   margin-left: 5px;
}
.THIS .info-value p{
   height: 18px;
   line-height: 18px;
}
.THIS .customerInfo {
   width: 75%;
}
.THIS .purchaseInfo {
   width: 25%;
}
.THIS .field-required {
   margin-left:0px;
}
.THIS .field-required .slds-form-element__control::before{
   content:"*";
   display: inline-block;
   transform: scale(1.5);
   position:relative;
   color:rgb(194, 57, 52);
   position: absolute;
   left: -.5rem;
   top: 50%;
   transform: translateY(-50%);
}
.THIS .field-error.input{
   border: 2px solid rgb(194, 57, 52);
}
.THIS .field-error .slds-textarea{
   border: 2px solid rgb(194, 57, 52);
}
.THIS .field-error input, .THIS .field-error .slds-dueling-list__options, .THIS .field-error .slds-input_faux{
   border:2px solid rgb(194, 57, 52) !important;
}
.THIS .required-wrap {
   position: relative;
}
.THIS .error-text{
   color: rgb(194, 57, 52);
   display: flex;
   justify-content: end;
   position: absolute;
   bottom: -16px;
   right: 0px;
}
.THIS .slds-combobox__form-element {
   margin-top: 0 !important;
}
.THIS .margin-size {
   margin-bottom: 8px;
}
.THIS .portal-wrap {
   width: 100%;
   padding: 0 4.4%;
}
.THIS .crm-wrap{
   width: 100%;
   background-color: #fff;
   padding: 24px;
}
.THIS .receipt-link {
   text-decoration: underline;
   cursor: pointer;
   color: #0176D3;
}
.THIS .no-permission-warranty {
   padding: 20px;
   color: #0176D3;
   text-align: center;
}
.THIS .no-warranty {
   padding: 20px;
   text-align: center;
   color: red;
}
.THIS .isExpired {
   color: red;
}
.THIS .email-check {
   padding: 20px;
   color: #0176D3;
   display: flex;
   align-items: center;
   flex-direction: column;
}
.THIS .upload-receipt-wrap {
   display: flex;
   height: auto;
}
.THIS .fileName-wrap{
   display: flex;
   flex-direction: column;
   margin-top: 0px;
}
.THIS .uploadFinished{
   display: flex;
   justify-content: flex-start;
}
.THIS .portal-color {
   color: #90c41f;
}
.THIS .crm-color {
   color: #0176D3;
}
.THIS .fileName {
   margin-right: 110px;
}
.THIS .delete{
   color: #777;
   cursor: pointer;
   float: right;
   text-decoration: underline;
}
.THIS .position-wrap {
   position: relative;
}
.THIS .scenario2-wrap {
   display: flex;
   position: absolute;
   bottom: 58px;
   left: 26%;
   width: 75%;
}
.THIS .scenario2-wrap-portal {
   display: flex;
   position: absolute;
   bottom: 64px;
   left: 26%;
   width: 75%;
}
.THIS .scenario34-wrap {
   display: flex;
   position: absolute;
   bottom: 0px;
   left: 26%;
   width: 75%;
}
.THIS .projectcode {
   display: flex;
   position: absolute;
   left: 26%;
   width: 75%;
   top:0px;
}
.THIS .projectbutton {
   margin-top:var(--lwc-spacingMedium,1rem);
   color:#000;
   font-size:20px;
}
.THIS .partsNumber .slds-form-element__label {
   display: none;
}
.THIS .btn-wrap {
   width: 100%;
   margin-top: 185px;
   display: flex;
   justify-content: center;
}
.THIS .btn-margin {
   margin-left: 12px;
}
.THIS .dialog-content-wrap {
   padding: 20px 0;
   overflow: initial;
}
.THIS .dialog-wrap .slds-modal__header {
   padding: 16px;
}
.THIS .dialog-wrap .new-sn-btn div {
   display: flex;
}

.THIS .upload-wrap-required::before {
   content: '*';
   display: inline-block;
   color: rgb(194, 57, 52);
   position: absolute;
   left: -.5rem;
   top: 70%;
   transform: translateY(-50%);
}