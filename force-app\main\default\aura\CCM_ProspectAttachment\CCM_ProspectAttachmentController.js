({
    // 初始化
    doInit: function(component, event, helper) {
        // 判断url参数
        // const params = new URLSearchParams(window.location.search);
        // const recordId =  params.get('0.recordId');
        let urlStr = window.location.pathname;
        let recordId = '';
        let index = urlStr.indexOf('Lead/');
        if(index !== -1) {
            recordId =  urlStr.slice(index + 5, index + 5 + 18);
        }
        else {
            let index = urlStr.indexOf('Account/');
            recordId =  urlStr.slice(index + 8, index + 8 + 18);
        }
        console.log(recordId, 'recordId=========');
        component.set('v.recordId', recordId);
        helper.searchFileList(component);
    },
    // 附件上传
    handleUploadFinished : function(component, event, helper) {
        const uploadedFiles = event.getParam("files");
        console.log(uploadedFiles.FileList, 'files============');
        let arr = [];
        arr = [...uploadedFiles];
        arr.forEach((item)=>{
            console.log(item, 'item==========');
            const reader = new FileReader();
            // 传入一个参数对象即可得到基于该参数对象的文本内容
            reader.readAsDataURL(item);
            reader.onload = function (e) {
                // target.result 该属性表示目标对象的DataURL
                let fileStr = e.target.result;
                let index = fileStr.indexOf(',') + 1;
                let fileValue = fileStr.substring(index);
                helper.uploadFile(component, fileValue, item.name);
            };
        })
    },

    handleUploadFinished2: function(component, event, helper) {
        const uploadedFiles = event.getParam("files");
        debugger;
        helper.linkFile(component, uploadedFiles);
    },

    // 附件预览
    handleViewFile : function(component, event, helper) {
        console.log(event.target.getAttribute('id'), '附件预览==========');
        // 获取ContentDocumentId
        var strContentDocumentId = event.target.getAttribute('id');
        component.set('v.contentDocumentId', strContentDocumentId);

        // 打开预览模式
        var openPreview = $A.get('e.lightning:openFiles');
        openPreview.fire({
            recordIds: [strContentDocumentId]
        });
    },
    // 删除附件
    handleDelete : function(component, event, helper) {
        helper.deleteFile(component, event.getSource().get('v.id'));
    }
})